<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车间执行看板 - MES系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .dashboard {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .number {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-card .label {
            font-size: 1.1em;
            color: #7f8c8d;
        }
        
        .planned { color: #3498db; }
        .assigned { color: #f39c12; }
        .in-progress { color: #e74c3c; }
        .completed { color: #27ae60; }
        
        .workstation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .workstation-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .workstation-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }
        
        .workstation-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-in-progress {
            background: #e74c3c;
            color: white;
        }
        
        .status-assigned {
            background: #f39c12;
            color: white;
        }
        
        .status-planned {
            background: #3498db;
            color: white;
        }
        
        .workstation-details {
            margin-top: 15px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .detail-label {
            color: #7f8c8d;
            font-weight: 500;
        }
        
        .detail-value {
            color: #2c3e50;
            font-weight: bold;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            transition: width 0.3s ease;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #2980b9;
            transform: scale(1.05);
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: white;
            font-size: 1.2em;
        }
        
        .error {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏭 车间执行看板</h1>
        <div class="subtitle">实时监控生产进度 · MES制造执行系统</div>
    </div>
    
    <div class="dashboard">
        <div id="loading" class="loading">
            正在加载车间数据...
        </div>
        
        <div id="error" class="error" style="display: none;">
            数据加载失败，请检查网络连接或稍后重试
        </div>
        
        <div id="content" style="display: none;">
            <div class="stats-grid" id="statsGrid">
                <!-- 统计卡片将通过JavaScript动态生成 -->
            </div>
            
            <div class="workstation-grid" id="workstationGrid">
                <!-- 工作站卡片将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
    
    <button class="refresh-btn" onclick="loadDashboardData()">
        🔄 刷新数据
    </button>
    
    <script>
        const API_BASE = 'http://localhost:3000/api';
        let authToken = '';
        
        // 模拟登录获取token（实际应用中应该有登录界面）
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                if (data.token) {
                    authToken = data.token;
                    return true;
                }
            } catch (error) {
                console.error('登录失败:', error);
            }
            return false;
        }
        
        async function loadDashboardData() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const content = document.getElementById('content');
            
            loading.style.display = 'block';
            error.style.display = 'none';
            content.style.display = 'none';
            
            try {
                if (!authToken && !(await login())) {
                    throw new Error('认证失败');
                }
                
                // 并行加载数据
                const [summaryResponse, workstationResponse] = await Promise.all([
                    fetch(`${API_BASE}/execution/summary`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    fetch(`${API_BASE}/execution/workstation-status`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    })
                ]);
                
                const summaryData = await summaryResponse.json();
                const workstationData = await workstationResponse.json();
                
                renderStats(summaryData.execution_summary);
                renderWorkstations(workstationData);
                
                loading.style.display = 'none';
                content.style.display = 'block';
                
            } catch (err) {
                console.error('加载数据失败:', err);
                loading.style.display = 'none';
                error.style.display = 'block';
            }
        }
        
        function renderStats(stats) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="number planned">${stats.planned_tasks}</div>
                    <div class="label">计划任务</div>
                </div>
                <div class="stat-card">
                    <div class="number assigned">${stats.assigned_tasks}</div>
                    <div class="label">已分配</div>
                </div>
                <div class="stat-card">
                    <div class="number in-progress">${stats.in_progress_tasks}</div>
                    <div class="label">进行中</div>
                </div>
                <div class="stat-card">
                    <div class="number completed">${stats.completed_tasks}</div>
                    <div class="label">已完成</div>
                </div>
            `;
        }
        
        function renderWorkstations(workstations) {
            const workstationGrid = document.getElementById('workstationGrid');
            
            if (workstations.length === 0) {
                workstationGrid.innerHTML = '<div class="loading">暂无活动工作站</div>';
                return;
            }
            
            workstationGrid.innerHTML = workstations.map(ws => `
                <div class="workstation-card">
                    <div class="workstation-header">
                        <div class="workstation-title">
                            ${ws.part_number} - ${ws.process_name}
                        </div>
                        <div class="status-badge status-${ws.status.toLowerCase().replace('_', '-')}">
                            ${getStatusText(ws.status)}
                        </div>
                    </div>
                    <div class="workstation-details">
                        <div class="detail-row">
                            <span class="detail-label">项目:</span>
                            <span class="detail-value">${ws.project_name}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">零件:</span>
                            <span class="detail-value">${ws.part_name || ws.part_number}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">工序:</span>
                            <span class="detail-value">步骤${ws.step_number} - ${ws.process_name}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">数量:</span>
                            <span class="detail-value">${ws.work_order_quantity}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">操作员:</span>
                            <span class="detail-value">${ws.current_operator || '未分配'}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">设备:</span>
                            <span class="detail-value">${ws.assigned_machine || '未分配'}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${ws.progress_percentage}%"></div>
                        </div>
                        <div style="text-align: center; margin-top: 5px; font-size: 0.9em; color: #7f8c8d;">
                            进度: ${ws.progress_percentage.toFixed(1)}%
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        function getStatusText(status) {
            const statusMap = {
                'PLANNED': '已计划',
                'ASSIGNED': '已分配',
                'IN_PROGRESS': '进行中',
                'COMPLETED': '已完成',
                'CANCELLED': '已取消'
            };
            return statusMap[status] || status;
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', loadDashboardData);
        
        // 每30秒自动刷新数据
        setInterval(loadDashboardData, 30000);
    </script>
</body>
</html>
