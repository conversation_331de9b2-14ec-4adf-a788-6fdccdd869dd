#!/bin/bash

# MES系统阶段3调度功能API测试脚本

BASE_URL="http://localhost:3000"
TOKEN=""

echo "=== MES系统阶段3调度功能测试 ==="

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/health" | jq .

# 2. 用户登录
echo -e "\n2. 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo $LOGIN_RESPONSE | jq .

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token // empty')

if [ -z "$TOKEN" ]; then
  echo "登录失败，无法获取token"
  exit 1
fi

echo "Token获取成功: ${TOKEN:0:20}..."

# 3. 获取工单列表
echo -e "\n3. 获取工单列表..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/work-orders" | jq .

# 4. 获取计划任务列表
echo -e "\n4. 获取计划任务列表..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/plan-tasks" | jq .

# 5. 获取甘特图数据
echo -e "\n5. 获取甘特图数据..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/plan-tasks/gantt" | jq .

# 6. 获取资源利用率
echo -e "\n6. 获取资源利用率..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/plan-tasks/resource-utilization" | jq .

# 7. 创建新工单
echo -e "\n7. 创建新工单..."
NEW_WORK_ORDER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/work-orders" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "project_bom_id": 1,
    "quantity": 2,
    "due_date": "2025-08-15"
  }')

echo $NEW_WORK_ORDER_RESPONSE | jq .

# 提取新工单ID
NEW_WORK_ORDER_ID=$(echo $NEW_WORK_ORDER_RESPONSE | jq -r '.id // empty')

if [ ! -z "$NEW_WORK_ORDER_ID" ]; then
  echo "新工单创建成功，ID: $NEW_WORK_ORDER_ID"
  
  # 8. 获取工单详情
  echo -e "\n8. 获取工单详情..."
  curl -s -H "Authorization: Bearer $TOKEN" \
    "$BASE_URL/api/work-orders/$NEW_WORK_ORDER_ID" | jq .
    
  # 9. 执行自动调度
  echo -e "\n9. 执行自动调度..."
  curl -s -X POST "$BASE_URL/api/plan-tasks/schedule" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"work_order_ids\": [$NEW_WORK_ORDER_ID],
      \"start_date\": \"2025-07-01\",
      \"priority_mode\": \"due_date\"
    }" | jq .
    
  # 10. 再次获取甘特图数据查看调度结果
  echo -e "\n10. 调度后的甘特图数据..."
  curl -s -H "Authorization: Bearer $TOKEN" \
    "$BASE_URL/api/plan-tasks/gantt" | jq .
fi

# 11. 创建手动计划任务
echo -e "\n11. 创建手动计划任务..."
MANUAL_TASK_RESPONSE=$(curl -s -X POST "$BASE_URL/api/plan-tasks" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "work_order_id": 1,
    "routing_step_id": 1,
    "skill_group_id": 1,
    "planned_start": "2025-07-20T08:00:00Z",
    "planned_end": "2025-07-20T16:00:00Z"
  }')

echo $MANUAL_TASK_RESPONSE | jq .

# 提取任务ID
TASK_ID=$(echo $MANUAL_TASK_RESPONSE | jq -r '.id // empty')

if [ ! -z "$TASK_ID" ]; then
  echo "手动计划任务创建成功，ID: $TASK_ID"
  
  # 12. 更新计划任务
  echo -e "\n12. 更新计划任务状态..."
  curl -s -X PUT "$BASE_URL/api/plan-tasks/$TASK_ID" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d '{
      "status": "ASSIGNED",
      "planned_end": "2025-07-20T18:00:00Z"
    }' | jq .
fi

# 13. 测试工单更新
echo -e "\n13. 更新工单状态..."
curl -s -X PUT "$BASE_URL/api/work-orders/1" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "IN_PROGRESS",
    "quantity": 2
  }' | jq .

# 14. 最终资源利用率检查
echo -e "\n14. 最终资源利用率..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/plan-tasks/resource-utilization" | jq .

echo -e "\n=== 阶段3调度功能测试完成 ==="
