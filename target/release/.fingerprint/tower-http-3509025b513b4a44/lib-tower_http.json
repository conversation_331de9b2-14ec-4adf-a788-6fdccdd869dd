{"rustc": 15597765236515928571, "features": "[\"cors\", \"default\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 14850331575045365232, "profile": 2040997289075261528, "path": 6468369650802004985, "deps": [[784494742817713399, "tower_service", false, 1186883134076061872], [1906322745568073236, "pin_project_lite", false, 8007073851363725439], [7712452662827335977, "tower_layer", false, 280170291908534151], [7896293946984509699, "bitflags", false, 13161158119303731458], [9010263965687315507, "http", false, 7624756925077580573], [14084095096285906100, "http_body", false, 8644577521426026053], [16066129441945555748, "bytes", false, 2205694668617458495], [16900715236047033623, "http_body_util", false, 2393105751925182053]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tower-http-3509025b513b4a44/dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}