{"rustc": 15597765236515928571, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 18348216721672176038, "path": 3356228515428702723, "deps": [[1615478164327904835, "pin_utils", false, 8897686436797314585], [1906322745568073236, "pin_project_lite", false, 8007073851363725439], [7620660491849607393, "futures_core", false, 3560167509610380493], [16240732885093539806, "futures_task", false, 15281185754139933982]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-util-aca32ea7905808cb/dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}