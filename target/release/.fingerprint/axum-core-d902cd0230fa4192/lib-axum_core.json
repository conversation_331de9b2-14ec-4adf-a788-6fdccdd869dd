{"rustc": 15597765236515928571, "features": "[\"tracing\"]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 2040997289075261528, "path": 5395799406021694165, "deps": [[784494742817713399, "tower_service", false, 1186883134076061872], [1906322745568073236, "pin_project_lite", false, 8007073851363725439], [2517136641825875337, "sync_wrapper", false, 1841457562584396271], [7712452662827335977, "tower_layer", false, 280170291908534151], [7858942147296547339, "rustversion", false, 2963522994415578941], [8606274917505247608, "tracing", false, 14642469008454443714], [9010263965687315507, "http", false, 7624756925077580573], [10229185211513642314, "mime", false, 1573940813529158686], [10629569228670356391, "futures_util", false, 6900119977374006828], [11946729385090170470, "async_trait", false, 4627006503581027004], [14084095096285906100, "http_body", false, 8644577521426026053], [16066129441945555748, "bytes", false, 2205694668617458495], [16900715236047033623, "http_body_util", false, 2393105751925182053]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-core-d902cd0230fa4192/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}