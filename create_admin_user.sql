-- 创建默认管理员用户
-- 密码: admin123 (已哈希)

INSERT INTO users (username, password_hash, full_name, is_active) 
VALUES ('admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlIW', '系统管理员', true)
ON CONFLICT (username) DO NOTHING;

-- 获取管理员用户ID并分配管理员角色
DO $$
DECLARE
    admin_user_id INTEGER;
    admin_role_id INTEGER;
BEGIN
    -- 获取管理员用户ID
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin';
    
    -- 获取管理员角色ID
    SELECT id INTO admin_role_id FROM roles WHERE role_name = 'Admin';
    
    -- 分配管理员角色
    INSERT INTO user_roles (user_id, role_id) 
    VALUES (admin_user_id, admin_role_id)
    ON CONFLICT (user_id, role_id) DO NOTHING;
    
    -- 分配所有技能组
    INSERT INTO user_skills (user_id, skill_group_id)
    SELECT admin_user_id, id FROM skill_groups
    ON CONFLICT (user_id, skill_group_id) DO NOTHING;
END $$;
