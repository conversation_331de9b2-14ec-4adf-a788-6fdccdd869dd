-- 插入阶段4车间执行相关的示例数据

-- 创建一些操作员用户
DO $$
DECLARE
    operator_role_id INTEGER;
    user_id INTEGER;
BEGIN
    -- 获取操作员角色ID
    SELECT id INTO operator_role_id FROM roles WHERE role_name = 'Operator';
    
    -- 创建操作员用户
    INSERT INTO users (username, password_hash, full_name, is_active) VALUES
    ('operator1', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlIW', '张师傅', true),
    ('operator2', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlIW', '李师傅', true),
    ('operator3', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.PmvlIW', '王师傅', true)
    ON CONFLICT (username) DO NOTHING;
    
    -- 为操作员分配角色
    FOR user_id IN
        SELECT id FROM users WHERE username IN ('operator1', 'operator2', 'operator3')
    LOOP
        INSERT INTO user_roles (user_id, role_id)
        VALUES (user_id, operator_role_id)
        ON CONFLICT DO NOTHING;
    END LOOP;
    
    -- 为操作员分配技能
    -- 张师傅：CNC加工
    SELECT id INTO user_id FROM users WHERE username = 'operator1';
    IF user_id IS NOT NULL THEN
        INSERT INTO user_skills (user_id, skill_group_id)
        SELECT user_id, id FROM skill_groups WHERE group_name = 'CNC Machining'
        ON CONFLICT DO NOTHING;
    END IF;
    
    -- 李师傅：手工加工
    SELECT id INTO user_id FROM users WHERE username = 'operator2';
    IF user_id IS NOT NULL THEN
        INSERT INTO user_skills (user_id, skill_group_id)
        SELECT user_id, id FROM skill_groups WHERE group_name = 'Manual Machining'
        ON CONFLICT DO NOTHING;
    END IF;
    
    -- 王师傅：质量检验
    SELECT id INTO user_id FROM users WHERE username = 'operator3';
    IF user_id IS NOT NULL THEN
        INSERT INTO user_skills (user_id, skill_group_id)
        SELECT user_id, id FROM skill_groups WHERE group_name = 'Quality Control'
        ON CONFLICT DO NOTHING;
    END IF;
END $$;

-- 插入更多执行记录
DO $$
DECLARE
    task_record RECORD;
    operator_id INTEGER;
    machine_id INTEGER;
BEGIN
    -- 获取操作员ID
    SELECT id INTO operator_id FROM users WHERE username = 'operator1';
    
    -- 获取机器ID
    SELECT id INTO machine_id FROM machines WHERE machine_name = '立式加工中心VMC-850';
    
    -- 为一些计划任务添加执行记录
    FOR task_record IN 
        SELECT pt.id, pt.status, r.process_name
        FROM plan_tasks pt
        JOIN routings r ON pt.routing_step_id = r.id
        WHERE pt.status IN ('PLANNED', 'IN_PROGRESS')
        ORDER BY pt.planned_start
        LIMIT 5
    LOOP
        -- 如果是CNC相关工序，添加开始记录
        IF task_record.process_name ILIKE '%加工%' AND operator_id IS NOT NULL THEN
            INSERT INTO execution_logs (plan_task_id, machine_id, user_id, event_type, event_time, notes)
            VALUES (
                task_record.id,
                machine_id,
                operator_id,
                'START',
                CURRENT_TIMESTAMP - INTERVAL '2 hours' + (task_record.id * INTERVAL '30 minutes'),
                '开始执行' || task_record.process_name
            );
            
            -- 更新任务状态为进行中
            UPDATE plan_tasks SET status = 'IN_PROGRESS' WHERE id = task_record.id;
            
            -- 如果是第一个任务，添加完成记录
            IF task_record.id = (SELECT MIN(id) FROM plan_tasks WHERE status = 'IN_PROGRESS') THEN
                INSERT INTO execution_logs (plan_task_id, machine_id, user_id, event_type, event_time, notes)
                VALUES (
                    task_record.id,
                    machine_id,
                    operator_id,
                    'COMPLETE',
                    CURRENT_TIMESTAMP - INTERVAL '30 minutes',
                    task_record.process_name || '工序完成，质量良好'
                );
                
                -- 更新任务状态为完成
                UPDATE plan_tasks SET status = 'COMPLETED' WHERE id = task_record.id;
            END IF;
        END IF;
    END LOOP;
END $$;

-- 插入质量检验记录
DO $$
DECLARE
    task_id INTEGER;
    inspector_id INTEGER;
BEGIN
    -- 获取质检员ID
    SELECT id INTO inspector_id FROM users WHERE username = 'operator3';
    
    -- 获取一个检验工序的任务
    SELECT pt.id INTO task_id
    FROM plan_tasks pt
    JOIN routings r ON pt.routing_step_id = r.id
    WHERE r.process_name ILIKE '%检验%'
    AND pt.status = 'PLANNED'
    LIMIT 1;
    
    IF task_id IS NOT NULL AND inspector_id IS NOT NULL THEN
        -- 开始检验
        INSERT INTO execution_logs (plan_task_id, user_id, event_type, event_time, notes)
        VALUES (
            task_id,
            inspector_id,
            'START',
            CURRENT_TIMESTAMP - INTERVAL '1 hour',
            '开始三坐标测量检验'
        );
        
        -- 质量检查记录
        INSERT INTO execution_logs (plan_task_id, user_id, event_type, event_time, notes)
        VALUES (
            task_id,
            inspector_id,
            'QUALITY_CHECK',
            CURRENT_TIMESTAMP - INTERVAL '30 minutes',
            '尺寸精度符合图纸要求，表面粗糙度Ra1.6，合格'
        );
        
        -- 完成检验
        INSERT INTO execution_logs (plan_task_id, user_id, event_type, event_time, notes)
        VALUES (
            task_id,
            inspector_id,
            'COMPLETE',
            CURRENT_TIMESTAMP - INTERVAL '15 minutes',
            '检验完成，产品合格'
        );
        
        -- 更新任务状态
        UPDATE plan_tasks SET status = 'COMPLETED' WHERE id = task_id;
    END IF;
END $$;

-- 插入一些暂停和恢复记录
DO $$
DECLARE
    task_id INTEGER;
    operator_id INTEGER;
BEGIN
    -- 获取一个进行中的任务
    SELECT id INTO task_id FROM plan_tasks WHERE status = 'IN_PROGRESS' LIMIT 1;
    SELECT id INTO operator_id FROM users WHERE username = 'operator1';
    
    IF task_id IS NOT NULL AND operator_id IS NOT NULL THEN
        -- 暂停记录
        INSERT INTO execution_logs (plan_task_id, user_id, event_type, event_time, notes)
        VALUES (
            task_id,
            operator_id,
            'PAUSE',
            CURRENT_TIMESTAMP - INTERVAL '45 minutes',
            '设备故障，暂停加工'
        );
        
        -- 恢复记录
        INSERT INTO execution_logs (plan_task_id, user_id, event_type, event_time, notes)
        VALUES (
            task_id,
            operator_id,
            'RESUME',
            CURRENT_TIMESTAMP - INTERVAL '30 minutes',
            '设备修复完成，恢复加工'
        );
    END IF;
END $$;
