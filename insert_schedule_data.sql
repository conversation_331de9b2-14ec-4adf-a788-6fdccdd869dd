-- 插入阶段3调度相关的示例数据

-- 插入示例工单
DO $$
DECLARE
    bom_id INTEGER;
    wo_id INTEGER;
BEGIN
    -- 为手机外壳模具项目的上模板创建工单
    SELECT pb.id INTO bom_id FROM project_boms pb
    JOIN parts p ON pb.part_id = p.id
    WHERE p.part_number = 'P001' AND p.version = 'V1.0'
    LIMIT 1;
    
    IF bom_id IS NOT NULL THEN
        INSERT INTO work_orders (project_bom_id, quantity, status, due_date)
        VALUES (bom_id, 1, 'PENDING', CURRENT_DATE + INTERVAL '30 days')
        RETURNING id INTO wo_id;
        
        -- 为这个工单创建计划任务
        INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status)
        SELECT 
            wo_id,
            r.id,
            CASE 
                WHEN r.process_name ILIKE '%粗加工%' OR r.process_name ILIKE '%半精加工%' OR r.process_name ILIKE '%精加工%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'CNC Machining')
                WHEN r.process_name ILIKE '%热处理%' OR r.process_name ILIKE '%钻孔%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'Manual Machining')
                WHEN r.process_name ILIKE '%检验%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'Quality Control')
                ELSE 
                    (SELECT id FROM skill_groups WHERE group_name = 'Manual Machining')
            END,
            CURRENT_TIMESTAMP + (r.step_number - 1) * INTERVAL '1 day',
            CURRENT_TIMESTAMP + (r.step_number - 1) * INTERVAL '1 day' + 
                COALESCE(r.standard_hours, 8) * INTERVAL '1 hour',
            'PLANNED'
        FROM routings r
        WHERE r.part_id = (SELECT part_id FROM project_boms WHERE id = bom_id)
        ORDER BY r.step_number;
    END IF;
    
    -- 为手机外壳模具项目的下模板创建工单
    SELECT pb.id INTO bom_id FROM project_boms pb
    JOIN parts p ON pb.part_id = p.id
    WHERE p.part_number = 'P002' AND p.version = 'V1.0'
    LIMIT 1;
    
    IF bom_id IS NOT NULL THEN
        INSERT INTO work_orders (project_bom_id, quantity, status, due_date)
        VALUES (bom_id, 1, 'PENDING', CURRENT_DATE + INTERVAL '35 days')
        RETURNING id INTO wo_id;
        
        -- 为这个工单创建计划任务
        INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status)
        SELECT 
            wo_id,
            r.id,
            CASE 
                WHEN r.process_name ILIKE '%粗加工%' OR r.process_name ILIKE '%半精加工%' OR r.process_name ILIKE '%精加工%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'CNC Machining')
                WHEN r.process_name ILIKE '%热处理%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'Manual Machining')
                WHEN r.process_name ILIKE '%检验%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'Quality Control')
                ELSE 
                    (SELECT id FROM skill_groups WHERE group_name = 'Manual Machining')
            END,
            CURRENT_TIMESTAMP + INTERVAL '7 days' + (r.step_number - 1) * INTERVAL '1 day',
            CURRENT_TIMESTAMP + INTERVAL '7 days' + (r.step_number - 1) * INTERVAL '1 day' + 
                COALESCE(r.standard_hours, 8) * INTERVAL '1 hour',
            'PLANNED'
        FROM routings r
        WHERE r.part_id = (SELECT part_id FROM project_boms WHERE id = bom_id)
        ORDER BY r.step_number;
    END IF;
    
    -- 为导柱创建工单
    SELECT pb.id INTO bom_id FROM project_boms pb
    JOIN parts p ON pb.part_id = p.id
    WHERE p.part_number = 'P003' AND p.version = 'V1.0'
    LIMIT 1;
    
    IF bom_id IS NOT NULL THEN
        INSERT INTO work_orders (project_bom_id, quantity, status, due_date)
        VALUES (bom_id, 4, 'PENDING', CURRENT_DATE + INTERVAL '20 days')
        RETURNING id INTO wo_id;
        
        -- 为这个工单创建计划任务
        INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status)
        SELECT 
            wo_id,
            r.id,
            CASE 
                WHEN r.process_name ILIKE '%车削%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'CNC Machining')
                WHEN r.process_name ILIKE '%热处理%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'Manual Machining')
                WHEN r.process_name ILIKE '%磨削%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'Manual Machining')
                WHEN r.process_name ILIKE '%检验%' THEN 
                    (SELECT id FROM skill_groups WHERE group_name = 'Quality Control')
                ELSE 
                    (SELECT id FROM skill_groups WHERE group_name = 'Manual Machining')
            END,
            CURRENT_TIMESTAMP + INTERVAL '14 days' + (r.step_number - 1) * INTERVAL '12 hours',
            CURRENT_TIMESTAMP + INTERVAL '14 days' + (r.step_number - 1) * INTERVAL '12 hours' + 
                COALESCE(r.standard_hours, 4) * INTERVAL '1 hour',
            'PLANNED'
        FROM routings r
        WHERE r.part_id = (SELECT part_id FROM project_boms WHERE id = bom_id)
        ORDER BY r.step_number;
    END IF;
END $$;

-- 更新一些工单状态为已计划
UPDATE work_orders SET status = 'PLANNED' WHERE status = 'PENDING';

-- 插入一些执行记录示例
DO $$
DECLARE
    task_id INTEGER;
    admin_user_id INTEGER;
BEGIN
    -- 获取管理员用户ID
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin';
    
    -- 获取第一个计划任务ID
    SELECT id INTO task_id FROM plan_tasks ORDER BY id LIMIT 1;
    
    IF task_id IS NOT NULL AND admin_user_id IS NOT NULL THEN
        -- 插入开始执行记录
        INSERT INTO execution_logs (plan_task_id, user_id, event_type, event_time, notes)
        VALUES (task_id, admin_user_id, 'START', CURRENT_TIMESTAMP - INTERVAL '2 hours', '开始执行粗加工工序');
        
        -- 更新任务状态为进行中
        UPDATE plan_tasks SET status = 'IN_PROGRESS' WHERE id = task_id;
    END IF;
END $$;
