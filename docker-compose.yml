version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: mes_postgres
    environment:
      POSTGRES_DB: mes_db
      POSTGRES_USER: mes_user
      POSTGRES_PASSWORD: mes_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: mes_redis
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  postgres_data:
