模具车间制造执行系统 (MES) - 建议开发计划
此计划为个人开发者设计，侧重于分阶段交付核心功能，逐步构建完整系统，并确保每个阶段都有可验证的成果。

阶段〇：环境准备 (预计: 1-2天)

目标： 搭建稳定可靠的开发与部署环境。

任务：

安装Rust, PostgreSQL, Node.js/pnpm等基础环境。

初始化Git仓库。

编写docker-compose.yml文件，通过容器运行PostgreSQL数据库。

创建后端Rust项目和前端Vue/Quasar项目的基础脚手架。

阶段一：核心骨架与权限管理 (预计: 1-2周)

目标： 建立用户系统和权限控制基础，完成系统的“管理后台”。

任务：

实现用户注册、登录、JWT令牌认证的API。

构建users, roles, skill_groups, machines的管理界面，支持管理员进行增删改查。

实现用户与角色、技能的关联。

编写后端中间件，用于检查API的访问权限。

阶段二：工艺与项目定义 (预计: 2-3周)

目标： 实现工艺员的核心功能，让生产任务可以被完整定义。

任务：

开发Projects (项目)、Parts (零件)、BOM (物料清单) 的管理API和前端界面。

开发Routings (工艺路线) 的管理功能，重点是能为每个工序步骤添加work_instructions (作业指导)。

实现工单(work_orders)的自动或手动生成。

阶段三：计划与调度 (预计: 2-4周)

目标： 实现计划员的核心功能，能将工单任务排程。

任务：

调研并集成一个前端甘特图组件。

开发API，用于读取待排产任务和设备组资源。

实现拖拽甘特图任务来创建和更新plan_tasks (生产计划) 的核心逻辑。

这是前端最复杂的部分，需要投入较多时间。

阶段四：车间执行与追溯 (预计: 2周)

目标： 打通最后一公里，让系统在车间实际运行起来。

任务：

开发简洁的移动端/PC端工人操作界面。

界面功能：显示个人任务列表、查看作业指导、扫码报工 (开始/暂停/完成)。

开发execution_logs的API，用于接收报工数据并更新相关任务状态。

开发一个简单的追溯查询页面。

阶段五：看板、报表与优化 (持续进行)

目标： 让数据产生价值，并根据实际使用反馈进行优化。

任务：

开发第一个数据看板，展示设备状态和工单进度。

根据管理需求，逐步增加OEE、合格率等报表。

收集用户反馈，对UI/UX和系统性能进行持续优化。

完善自动化备份、日志监控等运