#!/bin/bash

# MES系统阶段4车间执行功能API测试脚本

BASE_URL="http://localhost:3000"
ADMIN_TOKEN=""
OPERATOR_TOKEN=""

echo "=== MES系统阶段4车间执行功能测试 ==="

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/health" | jq .

# 2. 管理员登录
echo -e "\n2. 管理员登录..."
ADMIN_LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo $ADMIN_LOGIN_RESPONSE | jq .
ADMIN_TOKEN=$(echo $ADMIN_LOGIN_RESPONSE | jq -r '.token // empty')

# 3. 操作员登录
echo -e "\n3. 操作员登录..."
OPERATOR_LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "operator1",
    "password": "admin123"
  }')

echo $OPERATOR_LOGIN_RESPONSE | jq .
OPERATOR_TOKEN=$(echo $OPERATOR_LOGIN_RESPONSE | jq -r '.token // empty')

if [ -z "$ADMIN_TOKEN" ] || [ -z "$OPERATOR_TOKEN" ]; then
  echo "登录失败，无法获取token"
  exit 1
fi

echo "管理员Token: ${ADMIN_TOKEN:0:20}..."
echo "操作员Token: ${OPERATOR_TOKEN:0:20}..."

# 4. 获取工作站状态
echo -e "\n4. 获取工作站状态..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution/workstation-status" | jq .

# 5. 操作员查看自己的任务
echo -e "\n5. 操作员查看自己的任务..."
curl -s -H "Authorization: Bearer $OPERATOR_TOKEN" \
  "$BASE_URL/api/execution/my-tasks" | jq .

# 6. 获取执行日志列表
echo -e "\n6. 获取执行日志列表..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution-logs" | jq .

# 7. 生成二维码
echo -e "\n7. 为任务生成二维码..."
QR_RESPONSE=$(curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution/qr-code/1")

echo $QR_RESPONSE | jq .
QR_CODE=$(echo $QR_RESPONSE | jq -r '.qr_code // empty')

# 8. 扫码报工
if [ ! -z "$QR_CODE" ]; then
  echo -e "\n8. 扫码开始工作..."
  curl -s -X POST "$BASE_URL/api/execution/scan-report" \
    -H "Authorization: Bearer $OPERATOR_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"qr_code\": \"$QR_CODE\",
      \"event_type\": \"START\",
      \"machine_id\": 1,
      \"notes\": \"扫码开始执行任务\"
    }" | jq .
    
  echo -e "\n9. 扫码暂停工作..."
  curl -s -X POST "$BASE_URL/api/execution/scan-report" \
    -H "Authorization: Bearer $OPERATOR_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"qr_code\": \"$QR_CODE\",
      \"event_type\": \"PAUSE\",
      \"notes\": \"设备需要维护，暂停工作\"
    }" | jq .
    
  echo -e "\n10. 扫码恢复工作..."
  curl -s -X POST "$BASE_URL/api/execution/scan-report" \
    -H "Authorization: Bearer $OPERATOR_TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"qr_code\": \"$QR_CODE\",
      \"event_type\": \"RESUME\",
      \"notes\": \"设备维护完成，恢复工作\"
    }" | jq .
fi

# 11. 手动创建执行记录
echo -e "\n11. 手动创建执行记录..."
curl -s -X POST "$BASE_URL/api/execution-logs" \
  -H "Authorization: Bearer $OPERATOR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "plan_task_id": 2,
    "machine_id": 2,
    "event_type": "QUALITY_CHECK",
    "notes": "质量检查：尺寸符合要求"
  }' | jq .

# 12. 获取执行摘要
echo -e "\n12. 获取执行摘要..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution/summary" | jq .

# 13. 追溯查询 - 按零件号
echo -e "\n13. 追溯查询 - 按零件号..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution/traceability?part_number=P001" | jq .

# 14. 追溯查询 - 按工单ID
echo -e "\n14. 追溯查询 - 按工单ID..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution/traceability?work_order_id=1" | jq .

# 15. 再次查看工作站状态（查看变化）
echo -e "\n15. 再次查看工作站状态..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution/workstation-status" | jq .

# 16. 查看更新后的执行日志
echo -e "\n16. 查看更新后的执行日志..."
curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
  "$BASE_URL/api/execution-logs?limit=10" | jq .

echo -e "\n=== 阶段4车间执行功能测试完成 ==="
