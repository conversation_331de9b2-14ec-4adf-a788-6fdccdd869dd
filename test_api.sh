#!/bin/bash

# MES系统API测试脚本
# 测试阶段2的项目管理、零件管理和工艺路线功能

BASE_URL="http://localhost:3000"
TOKEN=""

echo "=== MES系统API测试 ==="

# 1. 健康检查
echo "1. 健康检查..."
curl -s "$BASE_URL/health" | jq .

# 2. 用户登录
echo -e "\n2. 用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo $LOGIN_RESPONSE | jq .

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.token // empty')

if [ -z "$TOKEN" ]; then
  echo "登录失败，无法获取token"
  exit 1
fi

echo "Token获取成功: ${TOKEN:0:20}..."

# 3. 获取项目列表
echo -e "\n3. 获取项目列表..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/projects" | jq .

# 4. 获取零件列表
echo -e "\n4. 获取零件列表..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/parts" | jq .

# 5. 获取工艺路线列表
echo -e "\n5. 获取工艺路线列表..."
curl -s -H "Authorization: Bearer $TOKEN" \
  "$BASE_URL/api/routings" | jq .

# 6. 创建新零件
echo -e "\n6. 创建新零件..."
NEW_PART_RESPONSE=$(curl -s -X POST "$BASE_URL/api/parts" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "part_number": "P999",
    "part_name": "测试零件",
    "version": "V1.0",
    "specifications": "这是一个测试零件"
  }')

echo $NEW_PART_RESPONSE | jq .

# 提取新零件ID
NEW_PART_ID=$(echo $NEW_PART_RESPONSE | jq -r '.id // empty')

if [ ! -z "$NEW_PART_ID" ]; then
  echo "新零件创建成功，ID: $NEW_PART_ID"
  
  # 7. 为新零件创建工艺路线
  echo -e "\n7. 为新零件创建工艺路线..."
  curl -s -X POST "$BASE_URL/api/routings" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"part_id\": $NEW_PART_ID,
      \"step_number\": 10,
      \"process_name\": \"测试工序\",
      \"work_instructions\": \"这是测试工序的作业指导书\",
      \"standard_hours\": 2.5
    }" | jq .
    
  # 8. 获取零件详情（包含工艺路线）
  echo -e "\n8. 获取零件详情..."
  curl -s -H "Authorization: Bearer $TOKEN" \
    "$BASE_URL/api/parts/$NEW_PART_ID" | jq .
fi

# 9. 创建新项目
echo -e "\n9. 创建新项目..."
NEW_PROJECT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/projects" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "project_name": "测试项目",
    "customer_name": "测试客户"
  }')

echo $NEW_PROJECT_RESPONSE | jq .

# 提取新项目ID
NEW_PROJECT_ID=$(echo $NEW_PROJECT_RESPONSE | jq -r '.id // empty')

if [ ! -z "$NEW_PROJECT_ID" ] && [ ! -z "$NEW_PART_ID" ]; then
  echo "新项目创建成功，ID: $NEW_PROJECT_ID"
  
  # 10. 为项目添加BOM项
  echo -e "\n10. 为项目添加BOM项..."
  curl -s -X POST "$BASE_URL/api/projects/$NEW_PROJECT_ID/bom" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    -d "{
      \"part_id\": $NEW_PART_ID,
      \"quantity\": 5
    }" | jq .
    
  # 11. 获取项目详情（包含BOM）
  echo -e "\n11. 获取项目详情..."
  curl -s -H "Authorization: Bearer $TOKEN" \
    "$BASE_URL/api/projects/$NEW_PROJECT_ID" | jq .
fi

echo -e "\n=== API测试完成 ==="
