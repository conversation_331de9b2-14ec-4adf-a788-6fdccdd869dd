import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { message, notification } from 'antd';
import { ApiResponse } from '../types';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://192.168.2.11:3000/api',
  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求重试配置
const MAX_RETRY_COUNT = 3;
const RETRY_DELAY = 1000;

// 重试函数
const retryRequest = async (error: AxiosError, retryCount = 0): Promise<any> => {
  if (retryCount >= MAX_RETRY_COUNT) {
    throw error;
  }

  // 只对网络错误或5xx错误进行重试
  if (!error.response || (error.response.status >= 500 && error.response.status < 600)) {
    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retryCount + 1)));
    return api.request(error.config!);
  }

  throw error;
};

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 检查业务状态码
    const { data } = response;
    if (data && typeof data === 'object' && 'success' in data) {
      if (!data.success && data.message) {
        message.error(data.message);
        return Promise.reject(new Error(data.message));
      }
    }
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config;

    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          message.error('登录已过期，请重新登录');
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          // 避免在登录页面重复跳转
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
          break;
        case 403:
          message.error('没有权限访问此资源');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 422:
          // 表单验证错误
          const errorMsg = (data as any)?.message || '数据验证失败';
          message.error(errorMsg);
          break;
        case 429:
          message.error('请求过于频繁，请稍后再试');
          break;
        case 500:
        case 502:
        case 503:
        case 504:
          // 服务器错误，尝试重试
          try {
            return await retryRequest(error);
          } catch (retryError) {
            notification.error({
              message: '服务器错误',
              description: '服务器暂时不可用，请稍后重试',
              duration: 5,
            });
          }
          break;
        default:
          const errorMessage = (data as any)?.message || `请求失败 (${status})`;
          message.error(errorMessage);
      }
    } else if (error.request) {
      // 网络错误，尝试重试
      try {
        return await retryRequest(error);
      } catch (retryError) {
        notification.error({
          message: '网络连接失败',
          description: '请检查网络连接或稍后重试',
          duration: 5,
        });
      }
    } else {
      message.error('请求配置错误');
    }

    return Promise.reject(error);
  }
);

// 通用API方法
export const apiRequest = {
  get: <T>(url: string, params?: any): Promise<ApiResponse<T>> =>
    api.get(url, { params }).then(res => res.data),
    
  post: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>
    api.post(url, data).then(res => res.data),
    
  put: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>
    api.put(url, data).then(res => res.data),
    
  delete: <T>(url: string): Promise<ApiResponse<T>> =>
    api.delete(url).then(res => res.data),
    
  patch: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>
    api.patch(url, data).then(res => res.data),
};

// 健康检查
export const healthCheck = () => apiRequest.get('/health');

export default api;
