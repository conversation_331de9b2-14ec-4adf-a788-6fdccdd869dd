import { apiRequest } from './api';
import { LoginRequest, LoginResponse, User } from '../types';

export const authService = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await apiRequest.post<LoginResponse>('/auth/login', credentials);
    if (response.success && response.data) {
      // 保存token和用户信息到localStorage
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      return response.data;
    }
    throw new Error(response.message || '登录失败');
  },

  // 用户登出
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    window.location.href = '/login';
  },

  // 获取当前用户信息
  getCurrentUser: (): User | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        return null;
      }
    }
    return null;
  },

  // 获取token
  getToken: (): string | null => {
    return localStorage.getItem('token');
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },

  // 检查用户权限
  hasRole: (role: string): boolean => {
    const user = authService.getCurrentUser();
    return user?.roles?.includes(role) || false;
  },

  // 检查多个权限（任一满足）
  hasAnyRole: (roles: string[]): boolean => {
    const user = authService.getCurrentUser();
    if (!user?.roles) return false;
    return roles.some(role => user.roles.includes(role));
  },

  // 检查多个权限（全部满足）
  hasAllRoles: (roles: string[]): boolean => {
    const user = authService.getCurrentUser();
    if (!user?.roles) return false;
    return roles.every(role => user.roles.includes(role));
  },

  // 修改密码
  changePassword: async (oldPassword: string, newPassword: string): Promise<void> => {
    const response = await apiRequest.post('/auth/change-password', {
      old_password: oldPassword,
      new_password: newPassword,
    });
    if (!response.success) {
      throw new Error(response.message || '修改密码失败');
    }
  },

  // 刷新用户信息
  refreshUserInfo: async (): Promise<User> => {
    const response = await apiRequest.get<User>('/auth/me');
    if (response.success && response.data) {
      localStorage.setItem('user', JSON.stringify(response.data));
      return response.data;
    }
    throw new Error(response.message || '获取用户信息失败');
  },
};
