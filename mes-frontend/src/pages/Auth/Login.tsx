import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Input, Button, message, Space, Typography } from 'antd';
import { UserOutlined, LockOutlined, LoginOutlined } from '@ant-design/icons';
import { authService } from '../../services/auth';
import { LoginRequest } from '../../types';

const { Text } = Typography;

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const onFinish = async (values: LoginRequest) => {
    setLoading(true);
    try {
      await authService.login(values);
      message.success('登录成功！');
      navigate('/dashboard');
    } catch (error: any) {
      message.error(error.message || '登录失败，请检查用户名和密码');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Form
        name="login"
        onFinish={onFinish}
        autoComplete="off"
        size="large"
      >
        <Form.Item
          name="username"
          rules={[
            { required: true, message: '请输入用户名!' },
            { min: 3, message: '用户名至少3个字符!' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="用户名"
            autoComplete="username"
          />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[
            { required: true, message: '请输入密码!' },
            { min: 6, message: '密码至少6个字符!' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="密码"
            autoComplete="current-password"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            icon={<LoginOutlined />}
            style={{ height: '48px', fontSize: '16px' }}
          >
            登录
          </Button>
        </Form.Item>
      </Form>

      <div style={{ 
        marginTop: '24px', 
        padding: '16px', 
        background: '#f5f5f5', 
        borderRadius: '8px' 
      }}>
        <Text strong style={{ display: 'block', marginBottom: '8px' }}>
          演示账号：
        </Text>
        <Space direction="vertical" size="small">
          <Text>
            <Text code>admin</Text> / <Text code>admin123</Text> (管理员)
          </Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            * 这是演示环境，请使用上述账号登录
          </Text>
        </Space>
      </div>
    </div>
  );
};

export default Login;
