import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Typography,
  Card,
  Row,
  Col,
  Descriptions,
  Tag,
  Button,
  Space,
  Tabs,
  Table,
  Progress,
  Statistic,
  Timeline,
  Steps,
  Modal,
  Form,
  Input,
  Select,
  message,
  Badge,
  Tooltip,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ToolOutlined,
  QrcodeOutlined,
  FileTextOutlined,
  AlertOutlined,
} from '@ant-design/icons';
import { workOrderService, planTaskService, executionService } from '../../services/business';
import { WorkOrder, PlanTask, ExecutionLog } from '../../types';
import { useApi } from '../../hooks/useApi';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Step } = Steps;
const { TextArea } = Input;

const WorkOrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [actionModalVisible, setActionModalVisible] = useState(false);
  const [actionType, setActionType] = useState<'start' | 'pause' | 'complete' | 'issue'>('start');
  const [form] = Form.useForm();

  // 获取工单详情
  const {
    data: workOrder,
    loading: workOrderLoading,
    refresh: refreshWorkOrder,
  } = useApi(
    () => workOrderService.getWorkOrder(Number(id)),
    { immediate: true, deps: [id] }
  );

  // 获取计划任务列表
  const {
    data: tasksData,
    loading: tasksLoading,
    refresh: refreshTasks,
  } = useApi(
    () => planTaskService.getTasks({ work_order_id: Number(id) }),
    { immediate: true, deps: [id] }
  );

  const tasks = tasksData?.data || [];

  // 模拟工单数据
  const mockWorkOrder: WorkOrder = {
    id: 1,
    project_id: 1,
    part_id: 1,
    quantity: 100,
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    planned_start: '2024-01-01',
    planned_end: '2024-01-15',
    actual_start: '2024-01-01',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    project_name: '汽车零部件生产项目',
    part_number: 'PART-001',
    part_name: '活塞组件',
  };

  // 模拟任务数据
  const mockTasks: PlanTask[] = [
    {
      id: 1,
      work_order_id: 1,
      routing_id: 1,
      step_number: 1,
      process_name: '原料准备',
      assigned_user_id: 1,
      machine_id: 1,
      status: 'COMPLETED',
      planned_start: '2024-01-01T08:00:00Z',
      planned_end: '2024-01-01T10:00:00Z',
      actual_start: '2024-01-01T08:00:00Z',
      actual_end: '2024-01-01T09:30:00Z',
      standard_hours: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T09:30:00Z',
    },
    {
      id: 2,
      work_order_id: 1,
      routing_id: 1,
      step_number: 2,
      process_name: 'CNC加工',
      assigned_user_id: 2,
      machine_id: 2,
      status: 'IN_PROGRESS',
      planned_start: '2024-01-01T10:00:00Z',
      planned_end: '2024-01-01T14:00:00Z',
      actual_start: '2024-01-01T10:15:00Z',
      standard_hours: 4,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T10:15:00Z',
    },
    {
      id: 3,
      work_order_id: 1,
      routing_id: 1,
      step_number: 3,
      process_name: '质量检测',
      assigned_user_id: 3,
      status: 'PLANNED',
      planned_start: '2024-01-01T14:00:00Z',
      planned_end: '2024-01-01T15:00:00Z',
      standard_hours: 1,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
  ];

  const displayWorkOrder = workOrder || mockWorkOrder;
  const displayTasks = tasks.length > 0 ? tasks : mockTasks;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'blue';
      case 'IN_PROGRESS':
        return 'green';
      case 'COMPLETED':
        return 'default';
      case 'CANCELLED':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return '计划中';
      case 'IN_PROGRESS':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'red';
      case 'HIGH':
        return 'orange';
      case 'MEDIUM':
        return 'blue';
      case 'LOW':
        return 'default';
      default:
        return 'default';
    }
  };

  const handleAction = (type: typeof actionType) => {
    setActionType(type);
    form.resetFields();
    setActionModalVisible(true);
  };

  const handleActionSubmit = async (values: any) => {
    try {
      const { notes, quality_data } = values;

      switch (actionType) {
        case 'start':
          await workOrderService.startWorkOrder(Number(id));
          message.success('工单已启动');
          break;
        case 'pause':
          // 这里应该调用暂停API
          message.success('工单已暂停');
          break;
        case 'complete':
          await workOrderService.completeWorkOrder(Number(id));
          message.success('工单已完成');
          break;
        case 'issue':
          // 这里应该调用报告问题API
          message.success('问题已报告');
          break;
      }

      setActionModalVisible(false);
      refreshWorkOrder();
      refreshTasks();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const calculateProgress = () => {
    if (displayTasks.length === 0) return 0;
    const completedTasks = displayTasks.filter(task => task.status === 'COMPLETED').length;
    return Math.round((completedTasks / displayTasks.length) * 100);
  };

  const getCurrentStep = () => {
    const inProgressTask = displayTasks.find(task => task.status === 'IN_PROGRESS');
    if (inProgressTask) {
      return inProgressTask.step_number - 1;
    }
    const completedTasks = displayTasks.filter(task => task.status === 'COMPLETED');
    return completedTasks.length;
  };

  const taskColumns = [
    {
      title: '步骤',
      dataIndex: 'step_number',
      key: 'step_number',
      width: 80,
      render: (step: number) => (
        <Badge count={step} style={{ backgroundColor: '#1890ff' }} />
      ),
    },
    {
      title: '工序名称',
      dataIndex: 'process_name',
      key: 'process_name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '分配人员',
      dataIndex: 'assigned_user_id',
      key: 'assigned_user_id',
      render: (userId: number) => (
        <Space>
          <UserOutlined />
          {userId ? `操作员${userId}` : '未分配'}
        </Space>
      ),
    },
    {
      title: '设备',
      dataIndex: 'machine_id',
      key: 'machine_id',
      render: (machineId: number) => (
        <Space>
          <ToolOutlined />
          {machineId ? `设备${machineId}` : '无'}
        </Space>
      ),
    },
    {
      title: '计划时间',
      key: 'planned_time',
      render: (_: any, record: PlanTask) => (
        <div>
          <div>{dayjs(record.planned_start).format('MM-DD HH:mm')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            至 {dayjs(record.planned_end).format('MM-DD HH:mm')}
          </div>
        </div>
      ),
    },
    {
      title: '实际时间',
      key: 'actual_time',
      render: (_: any, record: PlanTask) => (
        <div>
          {record.actual_start ? (
            <>
              <div>{dayjs(record.actual_start).format('MM-DD HH:mm')}</div>
              {record.actual_end && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  至 {dayjs(record.actual_end).format('MM-DD HH:mm')}
                </div>
              )}
            </>
          ) : (
            <Text type="secondary">未开始</Text>
          )}
        </div>
      ),
    },
    {
      title: '标准工时',
      dataIndex: 'standard_hours',
      key: 'standard_hours',
      render: (hours: number) => `${hours}h`,
    },
  ];

  const progress = calculateProgress();
  const currentStep = getCurrentStep();

  return (
    <div>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/work-orders')}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            工单 WO-{displayWorkOrder.id.toString().padStart(4, '0')}
          </Title>
          <Tag color={getStatusColor(displayWorkOrder.status)}>
            {getStatusText(displayWorkOrder.status)}
          </Tag>
          <Tag color={getPriorityColor(displayWorkOrder.priority)}>
            {displayWorkOrder.priority}
          </Tag>
        </Space>
        <Space>
          {displayWorkOrder.status === 'PLANNED' && (
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => handleAction('start')}
            >
              启动工单
            </Button>
          )}
          {displayWorkOrder.status === 'IN_PROGRESS' && (
            <>
              <Button
                icon={<PauseCircleOutlined />}
                onClick={() => handleAction('pause')}
              >
                暂停工单
              </Button>
              <Button
                type="primary"
                icon={<CheckCircleOutlined />}
                onClick={() => handleAction('complete')}
              >
                完成工单
              </Button>
            </>
          )}
          <Button
            danger
            icon={<AlertOutlined />}
            onClick={() => handleAction('issue')}
          >
            报告问题
          </Button>
          <Button
            icon={<EditOutlined />}
          >
            编辑工单
          </Button>
        </Space>
      </div>

      {/* 工单概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="完成进度"
              value={progress}
              suffix="%"
              prefix={<CheckCircleOutlined />}
            />
            <Progress percent={progress} size="small" style={{ marginTop: '8px' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="生产数量"
              value={displayWorkOrder.quantity}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成任务"
              value={displayTasks.filter(t => t.status === 'COMPLETED').length}
              suffix={`/ ${displayTasks.length}`}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="计划工期"
              value={displayWorkOrder.planned_start && displayWorkOrder.planned_end
                ? dayjs(displayWorkOrder.planned_end).diff(dayjs(displayWorkOrder.planned_start), 'day')
                : 0
              }
              suffix="天"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 工单详细信息 */}
      <Card style={{ marginBottom: '24px' }}>
        <Descriptions title="工单信息" bordered column={2}>
          <Descriptions.Item label="工单编号">
            WO-{displayWorkOrder.id.toString().padStart(4, '0')}
          </Descriptions.Item>
          <Descriptions.Item label="项目名称">
            {displayWorkOrder.project_name}
          </Descriptions.Item>
          <Descriptions.Item label="零件编号">
            {displayWorkOrder.part_number}
          </Descriptions.Item>
          <Descriptions.Item label="零件名称">
            {displayWorkOrder.part_name}
          </Descriptions.Item>
          <Descriptions.Item label="生产数量">
            {displayWorkOrder.quantity}
          </Descriptions.Item>
          <Descriptions.Item label="优先级">
            <Tag color={getPriorityColor(displayWorkOrder.priority)}>
              {displayWorkOrder.priority}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="计划开始">
            {displayWorkOrder.planned_start ? dayjs(displayWorkOrder.planned_start).format('YYYY-MM-DD HH:mm') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="计划结束">
            {displayWorkOrder.planned_end ? dayjs(displayWorkOrder.planned_end).format('YYYY-MM-DD HH:mm') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="实际开始">
            {displayWorkOrder.actual_start ? dayjs(displayWorkOrder.actual_start).format('YYYY-MM-DD HH:mm') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="实际结束">
            {displayWorkOrder.actual_end ? dayjs(displayWorkOrder.actual_end).format('YYYY-MM-DD HH:mm') : '-'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 工序进度和任务详情 */}
      <Card>
        <Tabs
          defaultActiveKey="progress"
          items={[
            {
              key: 'progress',
              label: (
                <span>
                  <CheckCircleOutlined />
                  工序进度
                </span>
              ),
              children: (
                <div>
                  <Steps
                    current={currentStep}
                    style={{ marginBottom: '24px' }}
                  >
                    {displayTasks.map((task) => (
                      <Step
                        key={task.id}
                        title={task.process_name}
                        description={`${task.standard_hours}h`}
                        status={
                          task.status === 'COMPLETED' ? 'finish' :
                          task.status === 'IN_PROGRESS' ? 'process' :
                          task.status === 'CANCELLED' ? 'error' : 'wait'
                        }
                      />
                    ))}
                  </Steps>

                  <Table
                    columns={taskColumns}
                    dataSource={displayTasks}
                    rowKey="id"
                    pagination={false}
                    size="small"
                  />
                </div>
              ),
            },
            {
              key: 'timeline',
              label: (
                <span>
                  <ClockCircleOutlined />
                  执行时间线
                </span>
              ),
              children: (
                <Timeline
                  items={displayTasks.map((task) => ({
                    color: task.status === 'COMPLETED' ? 'green' :
                           task.status === 'IN_PROGRESS' ? 'blue' : 'gray',
                    children: (
                      <div>
                        <div style={{ fontWeight: 'bold' }}>{task.process_name}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          计划: {dayjs(task.planned_start).format('MM-DD HH:mm')} - {dayjs(task.planned_end).format('MM-DD HH:mm')}
                        </div>
                        {task.actual_start && (
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            实际: {dayjs(task.actual_start).format('MM-DD HH:mm')}
                            {task.actual_end && ` - ${dayjs(task.actual_end).format('MM-DD HH:mm')}`}
                          </div>
                        )}
                        <Tag color={getStatusColor(task.status)} size="small">
                          {getStatusText(task.status)}
                        </Tag>
                      </div>
                    ),
                  }))}
                />
              ),
            },
            {
              key: 'qr',
              label: (
                <span>
                  <QrcodeOutlined />
                  二维码
                </span>
              ),
              children: (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                  <div style={{
                    width: '200px',
                    height: '200px',
                    border: '2px dashed #d9d9d9',
                    margin: '0 auto',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '16px',
                    color: '#999'
                  }}>
                    工单二维码
                  </div>
                  <div style={{ marginTop: '16px' }}>
                    <Text>扫描二维码快速访问工单</Text>
                  </div>
                </div>
              ),
            },
          ]}
        />
      </Card>

      {/* 操作模态框 */}
      <Modal
        title={
          actionType === 'start' ? '启动工单' :
          actionType === 'pause' ? '暂停工单' :
          actionType === 'complete' ? '完成工单' : '报告问题'
        }
        open={actionModalVisible}
        onCancel={() => setActionModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleActionSubmit}
        >
          <Form.Item
            name="notes"
            label="备注说明"
            rules={actionType === 'issue' ? [{ required: true, message: '请输入问题描述' }] : []}
          >
            <TextArea
              rows={4}
              placeholder={
                actionType === 'start' ? '请输入启动备注...' :
                actionType === 'pause' ? '请输入暂停原因...' :
                actionType === 'complete' ? '请输入完成说明...' : '请详细描述遇到的问题...'
              }
            />
          </Form.Item>

          {actionType === 'complete' && (
            <Form.Item
              name="quality_data"
              label="质量数据"
            >
              <TextArea
                rows={3}
                placeholder="请输入质量检测数据..."
              />
            </Form.Item>
          )}

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确认
              </Button>
              <Button onClick={() => setActionModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WorkOrderDetail;
