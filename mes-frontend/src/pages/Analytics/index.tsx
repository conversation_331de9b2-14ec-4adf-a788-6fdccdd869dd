import React, { useState } from 'react';
import {
  Typography,
  Card,
  Row,
  Col,
  Tabs,
  DatePicker,
  Select,
  Button,
  Space,
  Statistic,
  Table,
  Tag,
  Progress,
  Divider,
} from 'antd';
import {
  BarChartOutlined,
  LineChartOutlined,
  Pie<PERSON>hartOutlined,
  DashboardOutlined,
  DownloadOutlined,
  ReloadOutlined,
  TrophyOutlined,
  ToolOutlined,
  DollarOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import {
  EfficiencyTrendChart,
  OEEAnalysisChart,
  QualityTrendChart,
  CapacityGaugeChart,
  CostAnalysisChart,
} from '../../components/Charts/AdvancedCharts';
import ProductionChart from '../../components/Charts/ProductionChart';
import QualityChart from '../../components/Charts/QualityChart';
import EquipmentChart from '../../components/Charts/EquipmentChart';
import { dashboardService } from '../../services/business';
import { useApi } from '../../hooks/useApi';
import RealTimeData from '../../components/Common/RealTimeData';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const Analytics: React.FC = () => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, 'day'),
    dayjs(),
  ]);
  const [selectedMetric, setSelectedMetric] = useState('efficiency');

  // 获取分析数据
  const {
    data: analyticsData,
    loading,
    refresh: refreshData,
  } = useApi(
    () => dashboardService.getEfficiencyData(
      dateRange[0].format('YYYY-MM-DD'),
      dateRange[1].format('YYYY-MM-DD')
    ),
    { immediate: true, deps: [dateRange] }
  );

  // 模拟KPI数据
  const kpiData = {
    overall_efficiency: 87.5,
    quality_rate: 98.2,
    on_time_delivery: 94.8,
    cost_variance: -2.3,
    equipment_utilization: 85.6,
    productivity_index: 112.4,
  };

  // 模拟设备性能数据
  const equipmentPerformance = [
    { equipment: 'CNC-001', efficiency: 92, utilization: 88, quality: 97, status: 'excellent' },
    { equipment: 'CNC-002', efficiency: 89, utilization: 91, quality: 95, status: 'good' },
    { equipment: 'CNC-003', efficiency: 76, utilization: 82, quality: 98, status: 'warning' },
    { equipment: 'CNC-004', efficiency: 94, utilization: 89, quality: 96, status: 'excellent' },
    { equipment: 'CNC-005', efficiency: 81, utilization: 85, quality: 94, status: 'good' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent':
        return 'green';
      case 'good':
        return 'blue';
      case 'warning':
        return 'orange';
      case 'critical':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'excellent':
        return '优秀';
      case 'good':
        return '良好';
      case 'warning':
        return '警告';
      case 'critical':
        return '严重';
      default:
        return '未知';
    }
  };

  const equipmentColumns = [
    {
      title: '设备编号',
      dataIndex: 'equipment',
      key: 'equipment',
    },
    {
      title: '效率',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (value: number) => (
        <div style={{ width: '100px' }}>
          <Progress
            percent={value}
            size="small"
            status={value >= 90 ? 'success' : value >= 80 ? 'active' : 'exception'}
          />
        </div>
      ),
    },
    {
      title: '利用率',
      dataIndex: 'utilization',
      key: 'utilization',
      render: (value: number) => `${value}%`,
    },
    {
      title: '质量',
      dataIndex: 'quality',
      key: 'quality',
      render: (value: number) => `${value}%`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
  ];

  return (
    <RealTimeData
      refreshInterval={60000} // 1分钟刷新一次
      onRefresh={refreshData}
      showIndicator={true}
    >
      <div>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '24px'
        }}>
          <Title level={2} style={{ margin: 0 }}>
            数据分析中心
          </Title>
          <Space>
            <RangePicker
              value={dateRange}
              onChange={(dates) => dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])}
              format="YYYY-MM-DD"
            />
            <Select
              value={selectedMetric}
              onChange={setSelectedMetric}
              style={{ width: 120 }}
            >
              <Option value="efficiency">效率</Option>
              <Option value="quality">质量</Option>
              <Option value="cost">成本</Option>
              <Option value="oee">OEE</Option>
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={refreshData}
              loading={loading}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
            >
              导出报告
            </Button>
          </Space>
        </div>

        {/* KPI指标卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={4}>
            <Card>
              <Statistic
                title="整体效率"
                value={kpiData.overall_efficiency}
                suffix="%"
                prefix={<BarChartOutlined />}
                valueStyle={{ color: kpiData.overall_efficiency >= 85 ? '#52c41a' : '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="质量合格率"
                value={kpiData.quality_rate}
                suffix="%"
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="准时交付率"
                value={kpiData.on_time_delivery}
                suffix="%"
                prefix={<ClockCircleOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="成本差异"
                value={kpiData.cost_variance}
                suffix="%"
                prefix={<DollarOutlined />}
                valueStyle={{ color: kpiData.cost_variance < 0 ? '#52c41a' : '#ff4d4f' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="设备利用率"
                value={kpiData.equipment_utilization}
                suffix="%"
                prefix={<ToolOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="生产力指数"
                value={kpiData.productivity_index}
                prefix={<DashboardOutlined />}
                valueStyle={{ color: '#fa8c16' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 分析图表 */}
        <Tabs
          defaultActiveKey="overview"
          items={[
            {
              key: 'overview',
              label: (
                <span>
                  <DashboardOutlined />
                  综合概览
                </span>
              ),
              children: (
                <Row gutter={[16, 16]}>
                  <Col xs={24} lg={12}>
                    <EfficiencyTrendChart height={250} />
                  </Col>
                  <Col xs={24} lg={12}>
                    <QualityTrendChart height={250} />
                  </Col>
                  <Col xs={24} lg={16}>
                    <OEEAnalysisChart height={300} />
                  </Col>
                  <Col xs={24} lg={8}>
                    <CapacityGaugeChart value={85} height={300} />
                  </Col>
                </Row>
              ),
            },
            {
              key: 'production',
              label: (
                <span>
                  <BarChartOutlined />
                  生产分析
                </span>
              ),
              children: (
                <Row gutter={[16, 16]}>
                  <Col xs={24} lg={12}>
                    <ProductionChart height={300} />
                  </Col>
                  <Col xs={24} lg={12}>
                    <EquipmentChart height={300} />
                  </Col>
                  <Col xs={24}>
                    <Card title="设备性能详情" size="small">
                      <Table
                        columns={equipmentColumns}
                        dataSource={equipmentPerformance}
                        rowKey="equipment"
                        pagination={false}
                        size="small"
                      />
                    </Card>
                  </Col>
                </Row>
              ),
            },
            {
              key: 'quality',
              label: (
                <span>
                  <TrophyOutlined />
                  质量分析
                </span>
              ),
              children: (
                <Row gutter={[16, 16]}>
                  <Col xs={24} lg={12}>
                    <QualityChart height={300} />
                  </Col>
                  <Col xs={24} lg={12}>
                    <QualityTrendChart height={300} />
                  </Col>
                  <Col xs={24}>
                    <Card title="质量指标趋势" size="small">
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">质量详细分析图表开发中...</Text>
                      </div>
                    </Card>
                  </Col>
                </Row>
              ),
            },
            {
              key: 'cost',
              label: (
                <span>
                  <DollarOutlined />
                  成本分析
                </span>
              ),
              children: (
                <Row gutter={[16, 16]}>
                  <Col xs={24} lg={12}>
                    <CostAnalysisChart height={350} />
                  </Col>
                  <Col xs={24} lg={12}>
                    <Card title="成本趋势" size="small">
                      <div style={{ padding: '50px', textAlign: 'center' }}>
                        <Text type="secondary">成本趋势图表开发中...</Text>
                      </div>
                    </Card>
                  </Col>
                  <Col xs={24}>
                    <Card title="成本明细分析" size="small">
                      <div style={{ padding: '20px', textAlign: 'center' }}>
                        <Text type="secondary">成本明细分析表格开发中...</Text>
                      </div>
                    </Card>
                  </Col>
                </Row>
              ),
            },
          ]}
        />
      </div>
    </RealTimeData>
  );
};

export default Analytics;
