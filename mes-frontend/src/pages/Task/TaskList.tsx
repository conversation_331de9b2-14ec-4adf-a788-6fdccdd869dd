import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Select,
  Input,
  Row,
  Col,
  Statistic,
  Progress,
  Tooltip,
  Modal,
  Form,
  DatePicker,
  message,
  Badge,
  Avatar,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ToolOutlined,
  ReloadOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { planTaskService } from '../../services/business';
import { PlanTask } from '../../types';
import { usePagination } from '../../hooks/useApi';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;

const TaskList: React.FC = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState({
    status: '',
    assigned_user: '',
    search: '',
  });
  const [scheduleModalVisible, setScheduleModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 使用分页Hook管理任务数据
  const {
    list: tasks,
    total,
    loading,
    params,
    changePage,
    changeParams,
    refresh,
  } = usePagination(planTaskService.getTasks, {
    page: 1,
    limit: 20,
  });

  // 模拟任务数据
  const mockTasks: PlanTask[] = [
    {
      id: 1,
      work_order_id: 1,
      routing_id: 1,
      step_number: 1,
      process_name: '原料准备',
      assigned_user_id: 1,
      machine_id: 1,
      status: 'IN_PROGRESS',
      planned_start: '2024-01-01T08:00:00Z',
      planned_end: '2024-01-01T10:00:00Z',
      actual_start: '2024-01-01T08:15:00Z',
      standard_hours: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T08:15:00Z',
    },
    {
      id: 2,
      work_order_id: 2,
      routing_id: 2,
      step_number: 2,
      process_name: 'CNC加工',
      assigned_user_id: 2,
      machine_id: 2,
      status: 'PLANNED',
      planned_start: '2024-01-01T10:00:00Z',
      planned_end: '2024-01-01T14:00:00Z',
      standard_hours: 4,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 3,
      work_order_id: 3,
      routing_id: 3,
      step_number: 1,
      process_name: '质量检测',
      assigned_user_id: 3,
      status: 'COMPLETED',
      planned_start: '2024-01-01T06:00:00Z',
      planned_end: '2024-01-01T07:00:00Z',
      actual_start: '2024-01-01T06:00:00Z',
      actual_end: '2024-01-01T06:45:00Z',
      standard_hours: 1,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T06:45:00Z',
    },
    {
      id: 4,
      work_order_id: 4,
      routing_id: 4,
      step_number: 3,
      process_name: '装配',
      assigned_user_id: 1,
      machine_id: 3,
      status: 'PLANNED',
      planned_start: '2024-01-01T14:00:00Z',
      planned_end: '2024-01-01T16:00:00Z',
      standard_hours: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
  ];

  const displayTasks = tasks.length > 0 ? tasks : mockTasks;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return 'blue';
      case 'IN_PROGRESS':
        return 'green';
      case 'COMPLETED':
        return 'default';
      case 'CANCELLED':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNED':
        return '计划中';
      case 'IN_PROGRESS':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      default:
        return '未知';
    }
  };

  const calculateProgress = (task: PlanTask) => {
    if (task.status === 'COMPLETED') return 100;
    if (task.status === 'PLANNED') return 0;
    if (task.status === 'IN_PROGRESS' && task.actual_start) {
      const start = dayjs(task.actual_start);
      const end = dayjs(task.planned_end);
      const now = dayjs();
      const total = end.diff(start, 'minute');
      const elapsed = now.diff(start, 'minute');
      return Math.min(Math.max(Math.round((elapsed / total) * 100), 0), 100);
    }
    return 0;
  };

  const handleAutoSchedule = async () => {
    try {
      // 这里应该调用自动调度API
      message.success('自动调度完成');
      refresh();
    } catch (error) {
      message.error('自动调度失败');
    }
  };

  const handleScheduleSubmit = async (values: any) => {
    try {
      // 这里应该调用手动调度API
      message.success('任务调度成功');
      setScheduleModalVisible(false);
      refresh();
    } catch (error) {
      message.error('任务调度失败');
    }
  };

  // 过滤任务
  const filteredTasks = displayTasks.filter(task => {
    const matchesStatus = !filters.status || task.status === filters.status;
    const matchesUser = !filters.assigned_user || task.assigned_user_id?.toString() === filters.assigned_user;
    const matchesSearch = !filters.search ||
      task.process_name.toLowerCase().includes(filters.search.toLowerCase()) ||
      task.work_order_id.toString().includes(filters.search);

    return matchesStatus && matchesUser && matchesSearch;
  });

  // 统计数据
  const stats = {
    total: displayTasks.length,
    planned: displayTasks.filter(t => t.status === 'PLANNED').length,
    inProgress: displayTasks.filter(t => t.status === 'IN_PROGRESS').length,
    completed: displayTasks.filter(t => t.status === 'COMPLETED').length,
  };

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (id: number) => (
        <Button
          type="link"
          onClick={() => navigate(`/tasks/${id}`)}
          style={{ padding: 0, height: 'auto' }}
        >
          T-{id.toString().padStart(3, '0')}
        </Button>
      ),
    },
    {
      title: '工单',
      dataIndex: 'work_order_id',
      key: 'work_order_id',
      render: (id: number) => `WO-${id.toString().padStart(4, '0')}`,
    },
    {
      title: '工序',
      key: 'process',
      render: (_: any, record: PlanTask) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.process_name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            步骤 {record.step_number}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '进度',
      key: 'progress',
      render: (_: any, record: PlanTask) => {
        const progress = calculateProgress(record);
        return (
          <div style={{ width: '100px' }}>
            <Progress
              percent={progress}
              size="small"
              status={record.status === 'COMPLETED' ? 'success' : 'active'}
            />
          </div>
        );
      },
    },
    {
      title: '分配人员',
      dataIndex: 'assigned_user_id',
      key: 'assigned_user_id',
      render: (userId: number) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          {userId ? `操作员${userId}` : '未分配'}
        </Space>
      ),
    },
    {
      title: '设备',
      dataIndex: 'machine_id',
      key: 'machine_id',
      render: (machineId: number) => (
        <Space>
          <ToolOutlined />
          {machineId ? `设备${machineId}` : '无'}
        </Space>
      ),
    },
    {
      title: '计划时间',
      key: 'planned_time',
      render: (_: any, record: PlanTask) => (
        <div>
          <div>{dayjs(record.planned_start).format('MM-DD HH:mm')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dayjs(record.planned_end).format('MM-DD HH:mm')}
          </div>
        </div>
      ),
    },
    {
      title: '标准工时',
      dataIndex: 'standard_hours',
      key: 'standard_hours',
      render: (hours: number) => `${hours}h`,
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: PlanTask) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => navigate(`/tasks/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑任务">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <Title level={2} style={{ margin: 0 }}>
          任务管理
        </Title>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={refresh}
            loading={loading}
          >
            刷新
          </Button>
          <Button
            icon={<CalendarOutlined />}
            onClick={() => setScheduleModalVisible(true)}
          >
            手动调度
          </Button>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAutoSchedule}
          >
            自动调度
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={stats.total}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="计划中"
              value={stats.planned}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中"
              value={stats.inProgress}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选器 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="搜索任务或工单"
              allowClear
              onSearch={(value) => setFilters(prev => ({ ...prev, search: value }))}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value || '' }))}
            >
              <Option value="PLANNED">计划中</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="CANCELLED">已取消</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="操作员筛选"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => setFilters(prev => ({ ...prev, assigned_user: value || '' }))}
            >
              <Option value="1">操作员1</Option>
              <Option value="2">操作员2</Option>
              <Option value="3">操作员3</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Space>
              <Badge count={filteredTasks.length} showZero>
                <Button icon={<SearchOutlined />}>
                  筛选结果
                </Button>
              </Badge>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 任务列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredTasks}
          rowKey="id"
          loading={loading}
          pagination={{
            current: params.page,
            pageSize: params.limit,
            total: total || filteredTasks.length,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: changePage,
            onShowSizeChange: changePage,
          }}
          rowClassName={(record) => {
            if (record.status === 'IN_PROGRESS') return 'row-in-progress';
            if (record.status === 'COMPLETED') return 'row-completed';
            return '';
          }}
        />
      </Card>

      {/* 调度模态框 */}
      <Modal
        title="手动调度任务"
        open={scheduleModalVisible}
        onCancel={() => setScheduleModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleScheduleSubmit}
        >
          <Form.Item
            name="work_order_id"
            label="工单"
            rules={[{ required: true, message: '请选择工单' }]}
          >
            <Select placeholder="请选择工单">
              <Option value={1}>WO-0001 - 汽车零部件生产</Option>
              <Option value={2}>WO-0002 - 电子产品外壳</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="assigned_user_id"
            label="分配操作员"
            rules={[{ required: true, message: '请选择操作员' }]}
          >
            <Select placeholder="请选择操作员">
              <Option value={1}>操作员1</Option>
              <Option value={2}>操作员2</Option>
              <Option value={3}>操作员3</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="machine_id"
            label="分配设备"
          >
            <Select placeholder="请选择设备">
              <Option value={1}>CNC-001</Option>
              <Option value={2}>CNC-002</Option>
              <Option value={3}>CNC-003</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="planned_time"
            label="计划时间"
            rules={[{ required: true, message: '请选择计划时间' }]}
          >
            <DatePicker.RangePicker
              showTime
              style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确认调度
              </Button>
              <Button onClick={() => setScheduleModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TaskList;
