import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Typography,
  Card,
  Row,
  Col,
  Descriptions,
  Tag,
  Button,
  Space,
  Tabs,
  Table,
  Progress,
  Statistic,
  Timeline,
  Avatar,
  List,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  FileTextOutlined,
  ScheduleOutlined,
  TeamOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import { projectService, workOrderService } from '../../services/business';
import { Project, WorkOrder } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<Project | null>(null);
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [loading, setLoading] = useState(false);

  // 模拟项目详情数据
  const mockProject: Project = {
    id: 1,
    project_name: '汽车零部件生产项目',
    customer_name: '某汽车制造公司',
    description: '生产汽车发动机相关零部件，包括活塞、连杆、曲轴等关键组件。项目要求高精度加工，严格的质量控制标准。',
    status: 'IN_PROGRESS',
    start_date: '2024-01-01',
    end_date: '2024-06-30',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  };

  // 模拟工单数据
  const mockWorkOrders: WorkOrder[] = [
    {
      id: 1,
      project_id: 1,
      part_id: 1,
      quantity: 100,
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      planned_start: '2024-01-01',
      planned_end: '2024-01-15',
      actual_start: '2024-01-01',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      part_number: 'PART-001',
      part_name: '活塞组件',
    },
    {
      id: 2,
      project_id: 1,
      part_id: 2,
      quantity: 50,
      status: 'PLANNED',
      priority: 'MEDIUM',
      planned_start: '2024-01-16',
      planned_end: '2024-02-01',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      part_number: 'PART-002',
      part_name: '连杆组件',
    },
  ];

  useEffect(() => {
    if (id) {
      fetchProjectDetail();
      fetchWorkOrders();
    }
  }, [id]);

  const fetchProjectDetail = async () => {
    setLoading(true);
    try {
      const response = await projectService.getProject(Number(id));
      if (response.success && response.data) {
        setProject(response.data);
      } else {
        throw new Error('API调用失败');
      }
    } catch (error) {
      console.log('使用模拟数据:', error);
      setProject(mockProject);
    } finally {
      setLoading(false);
    }
  };

  const fetchWorkOrders = async () => {
    try {
      const response = await workOrderService.getWorkOrders({ project_id: Number(id) });
      if (response.success && response.data) {
        setWorkOrders(response.data.data);
      } else {
        throw new Error('API调用失败');
      }
    } catch (error) {
      console.log('使用模拟数据:', error);
      setWorkOrders(mockWorkOrders);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return 'blue';
      case 'IN_PROGRESS':
        return 'green';
      case 'COMPLETED':
        return 'default';
      case 'CANCELLED':
        return 'red';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PLANNING':
        return '计划中';
      case 'IN_PROGRESS':
        return '进行中';
      case 'COMPLETED':
        return '已完成';
      case 'CANCELLED':
        return '已取消';
      default:
        return '未知';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'red';
      case 'HIGH':
        return 'orange';
      case 'MEDIUM':
        return 'blue';
      case 'LOW':
        return 'default';
      default:
        return 'default';
    }
  };

  const workOrderColumns = [
    {
      title: '工单编号',
      dataIndex: 'id',
      key: 'id',
      render: (id: number) => `WO-${id.toString().padStart(4, '0')}`,
    },
    {
      title: '零件信息',
      key: 'part',
      render: (_, record: WorkOrder) => (
        <div>
          <div><Text strong>{record.part_number}</Text></div>
          <div><Text type="secondary">{record.part_name}</Text></div>
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {priority}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '计划开始',
      dataIndex: 'planned_start',
      key: 'planned_start',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: '计划结束',
      dataIndex: 'planned_end',
      key: 'planned_end',
      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',
    },
  ];

  if (!project) {
    return <div>加载中...</div>;
  }

  // 计算项目进度
  const totalWorkOrders = workOrders.length;
  const completedWorkOrders = workOrders.filter(wo => wo.status === 'COMPLETED').length;
  const progress = totalWorkOrders > 0 ? Math.round((completedWorkOrders / totalWorkOrders) * 100) : 0;

  return (
    <div>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/projects')}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            {project.project_name}
          </Title>
          <Tag color={getStatusColor(project.status)}>
            {getStatusText(project.status)}
          </Tag>
        </Space>
        <Button
          type="primary"
          icon={<EditOutlined />}
        >
          编辑项目
        </Button>
      </div>

      {/* 项目概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="项目进度"
              value={progress}
              suffix="%"
              prefix={<ScheduleOutlined />}
            />
            <Progress percent={progress} size="small" style={{ marginTop: '8px' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="工单总数"
              value={totalWorkOrders}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成工单"
              value={completedWorkOrders}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="项目天数"
              value={project.start_date && project.end_date
                ? dayjs(project.end_date).diff(dayjs(project.start_date), 'day')
                : 0
              }
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细信息 */}
      <Card style={{ marginBottom: '24px' }}>
        <Descriptions title="项目信息" bordered column={2}>
          <Descriptions.Item label="项目名称">
            {project.project_name}
          </Descriptions.Item>
          <Descriptions.Item label="客户名称">
            {project.customer_name}
          </Descriptions.Item>
          <Descriptions.Item label="项目状态">
            <Tag color={getStatusColor(project.status)}>
              {getStatusText(project.status)}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {dayjs(project.created_at).format('YYYY-MM-DD HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="开始日期">
            {project.start_date ? dayjs(project.start_date).format('YYYY-MM-DD') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="结束日期">
            {project.end_date ? dayjs(project.end_date).format('YYYY-MM-DD') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="项目描述" span={2}>
            {project.description || '暂无描述'}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 标签页内容 */}
      <Card>
        <Tabs
          defaultActiveKey="workorders"
          items={[
            {
              key: 'workorders',
              label: (
                <span>
                  <FileTextOutlined />
                  工单列表
                </span>
              ),
              children: (
                <Table
                  columns={workOrderColumns}
                  dataSource={workOrders}
                  rowKey="id"
                  pagination={false}
                />
              ),
            },
            {
              key: 'timeline',
              label: (
                <span>
                  <ScheduleOutlined />
                  项目时间线
                </span>
              ),
              children: (
                <Timeline
                  items={[
                    {
                      children: '项目创建',
                      color: 'blue',
                    },
                    {
                      children: '项目启动',
                      color: 'green',
                    },
                    {
                      children: '第一批工单完成',
                      color: 'green',
                    },
                    {
                      children: '项目进行中...',
                      color: 'blue',
                    },
                  ]}
                />
              ),
            },
            {
              key: 'team',
              label: (
                <span>
                  <TeamOutlined />
                  项目团队
                </span>
              ),
              children: (
                <List
                  itemLayout="horizontal"
                  dataSource={[
                    { name: '张工程师', role: '项目经理', avatar: 'Z' },
                    { name: '李师傅', role: '生产主管', avatar: 'L' },
                    { name: '王技师', role: '质量检验', avatar: 'W' },
                  ]}
                  renderItem={(item) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<Avatar>{item.avatar}</Avatar>}
                        title={item.name}
                        description={item.role}
                      />
                    </List.Item>
                  )}
                />
              ),
            },
          ]}
        />
      </Card>
    </div>
  );
};

export default ProjectDetail;
