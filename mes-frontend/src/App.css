.App {
  text-align: left;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 执行看板样式 */
.row-in-progress {
  background-color: #f6ffed;
  border-left: 3px solid #52c41a;
}

.row-completed {
  background-color: #f0f0f0;
  opacity: 0.8;
}

.row-in-progress:hover {
  background-color: #f0f9ff;
}

/* 任务卡片样式 */
.task-card {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.task-card-in-progress {
  border-left: 4px solid #52c41a;
}

.task-card-planned {
  border-left: 4px solid #1890ff;
}

.task-card-completed {
  border-left: 4px solid #faad14;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.planned {
  background-color: #1890ff;
}

.status-indicator.in-progress {
  background-color: #52c41a;
  animation: pulse 2s infinite;
}

.status-indicator.completed {
  background-color: #faad14;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 实时数据指示器 */
.real-time-indicator {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1000;
  background: white;
  border-radius: 20px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #d9d9d9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-card {
    margin-bottom: 8px;
  }

  .real-time-indicator {
    top: 60px;
    right: 10px;
    padding: 6px 10px;
    font-size: 12px;
  }
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
