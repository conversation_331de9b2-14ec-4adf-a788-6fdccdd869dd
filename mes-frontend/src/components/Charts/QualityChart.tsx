import React from 'react';
import { Pie } from '@ant-design/charts';
import { Card } from 'antd';

interface QualityData {
  type: string;
  value: number;
}

interface QualityChartProps {
  data?: QualityData[];
  title?: string;
  height?: number;
}

const QualityChart: React.FC<QualityChartProps> = ({ 
  data = [], 
  title = "质量分析", 
  height = 300 
}) => {
  // 模拟数据
  const mockData: QualityData[] = [
    { type: '合格', value: 95.5 },
    { type: '返工', value: 3.2 },
    { type: '报废', value: 1.3 },
  ];

  const chartData = data.length > 0 ? data : mockData;

  const config = {
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    innerRadius: 0.5,
    color: ['#52c41a', '#faad14', '#ff4d4f'],
    label: {
      type: 'outer' as const,
      content: '{name}: {percentage}',
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: `${datum.value}%`,
        };
      },
    },
    legend: {
      position: 'bottom' as const,
    },
    statistic: {
      title: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: '质量',
      },
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: '合格率',
      },
    },
    interactions: [
      {
        type: 'pie-statistic-active',
      },
    ],
  };

  return (
    <Card title={title} size="small">
      <Pie {...config} height={height} />
    </Card>
  );
};

export default QualityChart;
