import React from 'react';
import { Line, Column, Area, Pie, Gauge } from '@ant-design/charts';
import { Card, Row, Col, Statistic, Typography } from 'antd';

const { Title } = Typography;

// 生产效率趋势图
export const EfficiencyTrendChart: React.FC<{ data?: any[]; height?: number }> = ({ 
  data = [], 
  height = 300 
}) => {
  // 模拟数据
  const mockData = [
    { date: '2024-01-01', efficiency: 85, target: 90 },
    { date: '2024-01-02', efficiency: 88, target: 90 },
    { date: '2024-01-03', efficiency: 92, target: 90 },
    { date: '2024-01-04', efficiency: 87, target: 90 },
    { date: '2024-01-05', efficiency: 94, target: 90 },
    { date: '2024-01-06', efficiency: 89, target: 90 },
    { date: '2024-01-07', efficiency: 91, target: 90 },
  ];

  const chartData = data.length > 0 ? data : mockData;

  const config = {
    data: chartData,
    xField: 'date',
    yField: 'efficiency',
    smooth: true,
    color: '#1890ff',
    point: {
      size: 4,
      shape: 'circle',
    },
    annotations: [
      {
        type: 'line',
        start: ['min', 90],
        end: ['max', 90],
        style: {
          stroke: '#ff4d4f',
          lineDash: [4, 4],
        },
        text: {
          content: '目标效率 90%',
          position: 'end',
          style: {
            textAlign: 'end',
          },
        },
      },
    ],
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: '生产效率',
          value: `${datum.efficiency}%`,
        };
      },
    },
  };

  return (
    <Card title="生产效率趋势" size="small">
      <Line {...config} height={height} />
    </Card>
  );
};

// 设备OEE分析图
export const OEEAnalysisChart: React.FC<{ data?: any[]; height?: number }> = ({ 
  data = [], 
  height = 300 
}) => {
  const mockData = [
    { equipment: 'CNC-001', availability: 95, performance: 88, quality: 97, oee: 81 },
    { equipment: 'CNC-002', availability: 92, performance: 91, quality: 95, oee: 80 },
    { equipment: 'CNC-003', availability: 88, performance: 85, quality: 98, oee: 73 },
    { equipment: 'CNC-004', availability: 96, performance: 89, quality: 96, oee: 82 },
    { equipment: 'CNC-005', availability: 90, performance: 87, quality: 94, oee: 74 },
  ];

  const chartData = data.length > 0 ? data : mockData;

  const config = {
    data: chartData,
    xField: 'equipment',
    yField: 'oee',
    color: ({ oee }: any) => {
      if (oee >= 85) return '#52c41a';
      if (oee >= 75) return '#faad14';
      return '#ff4d4f';
    },
    columnWidthRatio: 0.6,
    label: {
      position: 'top' as const,
      style: {
        fill: '#000',
        opacity: 0.6,
      },
      formatter: (datum: any) => `${datum.oee}%`,
    },
    tooltip: {
      formatter: (datum: any) => {
        return [
          { name: 'OEE', value: `${datum.oee}%` },
          { name: '可用性', value: `${datum.availability}%` },
          { name: '性能', value: `${datum.performance}%` },
          { name: '质量', value: `${datum.quality}%` },
        ];
      },
    },
  };

  return (
    <Card title="设备OEE分析" size="small">
      <Column {...config} height={height} />
    </Card>
  );
};

// 质量趋势分析图
export const QualityTrendChart: React.FC<{ data?: any[]; height?: number }> = ({ 
  data = [], 
  height = 300 
}) => {
  const mockData = [
    { date: '2024-01-01', qualified: 95, rework: 3, scrap: 2 },
    { date: '2024-01-02', qualified: 97, rework: 2, scrap: 1 },
    { date: '2024-01-03', qualified: 94, rework: 4, scrap: 2 },
    { date: '2024-01-04', qualified: 98, rework: 1, scrap: 1 },
    { date: '2024-01-05', qualified: 96, rework: 3, scrap: 1 },
    { date: '2024-01-06', qualified: 99, rework: 1, scrap: 0 },
    { date: '2024-01-07', qualified: 95, rework: 3, scrap: 2 },
  ];

  const chartData = data.length > 0 ? data : mockData;

  const config = {
    data: chartData,
    xField: 'date',
    yField: 'qualified',
    smooth: true,
    color: '#52c41a',
    area: {
      style: {
        fill: 'l(270) 0:#ffffff 0.5:#7ec2f3 1:#1890ff',
        fillOpacity: 0.3,
      },
    },
    point: {
      size: 4,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => {
        return [
          { name: '合格率', value: `${datum.qualified}%` },
          { name: '返工率', value: `${datum.rework}%` },
          { name: '报废率', value: `${datum.scrap}%` },
        ];
      },
    },
  };

  return (
    <Card title="质量趋势分析" size="small">
      <Area {...config} height={height} />
    </Card>
  );
};

// 产能利用率仪表盘
export const CapacityGaugeChart: React.FC<{ value?: number; height?: number }> = ({ 
  value = 75, 
  height = 200 
}) => {
  const config = {
    percent: value / 100,
    range: {
      color: 'l(0) 0:#ff4d4f 0.5:#faad14 1:#52c41a',
    },
    indicator: {
      pointer: {
        style: {
          stroke: '#D0D0D0',
        },
      },
      pin: {
        style: {
          stroke: '#D0D0D0',
        },
      },
    },
    statistic: {
      content: {
        style: {
          fontSize: '36px',
          lineHeight: '36px',
        },
        formatter: () => `${value}%`,
      },
    },
  };

  return (
    <Card title="产能利用率" size="small">
      <div style={{ textAlign: 'center' }}>
        <Gauge {...config} height={height} />
      </div>
    </Card>
  );
};

// 成本分析饼图
export const CostAnalysisChart: React.FC<{ data?: any[]; height?: number }> = ({ 
  data = [], 
  height = 300 
}) => {
  const mockData = [
    { type: '原材料', value: 45, cost: 450000 },
    { type: '人工成本', value: 25, cost: 250000 },
    { type: '设备折旧', value: 15, cost: 150000 },
    { type: '能源消耗', value: 10, cost: 100000 },
    { type: '其他费用', value: 5, cost: 50000 },
  ];

  const chartData = data.length > 0 ? data : mockData;

  const config = {
    data: chartData,
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    innerRadius: 0.5,
    label: {
      type: 'outer' as const,
      content: '{name}: {percentage}',
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: `${datum.value}% (¥${datum.cost.toLocaleString()})`,
        };
      },
    },
    legend: {
      position: 'bottom' as const,
    },
    statistic: {
      title: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: '总成本',
      },
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        },
        content: '¥1,000,000',
      },
    },
  };

  return (
    <Card title="成本结构分析" size="small">
      <Pie {...config} height={height} />
    </Card>
  );
};
