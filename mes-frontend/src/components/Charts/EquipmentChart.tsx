import React from 'react';
import { Column } from '@ant-design/charts';
import { Card } from 'antd';

interface EquipmentData {
  equipment: string;
  utilization: number;
  status: string;
}

interface EquipmentChartProps {
  data?: EquipmentData[];
  title?: string;
  height?: number;
}

const EquipmentChart: React.FC<EquipmentChartProps> = ({ 
  data = [], 
  title = "设备利用率", 
  height = 300 
}) => {
  // 模拟数据
  const mockData: EquipmentData[] = [
    { equipment: 'CNC-001', utilization: 85, status: '运行中' },
    { equipment: 'CNC-002', utilization: 92, status: '运行中' },
    { equipment: 'CNC-003', utilization: 78, status: '运行中' },
    { equipment: 'CNC-004', utilization: 95, status: '运行中' },
    { equipment: 'CNC-005', utilization: 68, status: '维护中' },
    { equipment: 'CNC-006', utilization: 88, status: '运行中' },
  ];

  const chartData = data.length > 0 ? data : mockData;

  const config = {
    data: chartData,
    xField: 'equipment',
    yField: 'utilization',
    seriesField: 'status',
    color: ({ status }: any) => {
      return status === '运行中' ? '#52c41a' : '#faad14';
    },
    columnWidthRatio: 0.6,
    label: {
      position: 'top' as const,
      style: {
        fill: '#000',
        opacity: 0.6,
      },
      formatter: (datum: any) => `${datum.utilization}%`,
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: '利用率',
          value: `${datum.utilization}%`,
        };
      },
    },
    yAxis: {
      max: 100,
      label: {
        formatter: (v: string) => `${v}%`,
      },
    },
    legend: {
      position: 'top' as const,
    },
    meta: {
      utilization: {
        alias: '利用率(%)',
      },
    },
  };

  return (
    <Card title={title} size="small">
      <Column {...config} height={height} />
    </Card>
  );
};

export default EquipmentChart;
