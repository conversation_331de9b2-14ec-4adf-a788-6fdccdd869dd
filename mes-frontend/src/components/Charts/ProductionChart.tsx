import React from 'react';
import { Line } from '@ant-design/charts';
import { Card } from 'antd';

interface ProductionData {
  date: string;
  value: number;
  category: string;
}

interface ProductionChartProps {
  data?: ProductionData[];
  title?: string;
  height?: number;
}

const ProductionChart: React.FC<ProductionChartProps> = ({ 
  data = [], 
  title = "生产趋势", 
  height = 300 
}) => {
  // 模拟数据
  const mockData: ProductionData[] = [
    { date: '2024-01-01', value: 120, category: '计划产量' },
    { date: '2024-01-02', value: 132, category: '计划产量' },
    { date: '2024-01-03', value: 101, category: '计划产量' },
    { date: '2024-01-04', value: 134, category: '计划产量' },
    { date: '2024-01-05', value: 90, category: '计划产量' },
    { date: '2024-01-06', value: 230, category: '计划产量' },
    { date: '2024-01-07', value: 210, category: '计划产量' },
    { date: '2024-01-01', value: 110, category: '实际产量' },
    { date: '2024-01-02', value: 125, category: '实际产量' },
    { date: '2024-01-03', value: 95, category: '实际产量' },
    { date: '2024-01-04', value: 128, category: '实际产量' },
    { date: '2024-01-05', value: 85, category: '实际产量' },
    { date: '2024-01-06', value: 220, category: '实际产量' },
    { date: '2024-01-07', value: 200, category: '实际产量' },
  ];

  const chartData = data.length > 0 ? data : mockData;

  const config = {
    data: chartData,
    xField: 'date',
    yField: 'value',
    seriesField: 'category',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    color: ['#1890ff', '#52c41a'],
    point: {
      size: 4,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.category,
          value: `${datum.value} 件`,
        };
      },
    },
    legend: {
      position: 'top' as const,
    },
    yAxis: {
      label: {
        formatter: (v: string) => `${v} 件`,
      },
    },
  };

  return (
    <Card title={title} size="small">
      <Line {...config} height={height} />
    </Card>
  );
};

export default ProductionChart;
