import React from 'react';
import { Layout, Typography, Space } from 'antd';
import { ToolOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Content
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '50px',
        }}
      >
        <div
          style={{
            background: 'rgba(255, 255, 255, 0.95)',
            padding: '48px',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            backdropFilter: 'blur(10px)',
            width: '100%',
            maxWidth: '400px',
          }}
        >
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <Space direction="vertical" size="small">
              <ToolOutlined
                style={{ 
                  fontSize: '48px', 
                  color: '#1890ff',
                  marginBottom: '16px' 
                }} 
              />
              <Title level={2} style={{ margin: 0, color: '#001529' }}>
                MES制造执行系统
              </Title>
              <Text type="secondary">
                Manufacturing Execution System
              </Text>
            </Space>
          </div>
          
          {children}
          
          <div style={{ textAlign: 'center', marginTop: '24px' }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              © 2024 MES System. All rights reserved.
            </Text>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default AuthLayout;
