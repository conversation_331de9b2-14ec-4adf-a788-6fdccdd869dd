import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Badge, List, Avatar, Typography } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StopOutlined,
  UserOutlined,
  ToolOutlined,
  AlertOutlined 
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text } = Typography;

interface TaskStatus {
  id: number;
  taskName: string;
  operator: string;
  status: 'running' | 'paused' | 'completed';
  progress: number;
  startTime: string;
}

interface Alert {
  id: number;
  type: 'warning' | 'error' | 'info';
  message: string;
  time: string;
}

const RealTimeMonitor: React.FC = () => {
  const [currentTime, setCurrentTime] = useState(dayjs());
  const [activeTasks, setActiveTasks] = useState<TaskStatus[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);

  // 模拟实时数据更新
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(dayjs());
      
      // 模拟任务状态数据
      setActiveTasks([
        {
          id: 1,
          taskName: '零件A加工',
          operator: '张师傅',
          status: 'running',
          progress: 75,
          startTime: '09:30',
        },
        {
          id: 2,
          taskName: '零件B装配',
          operator: '李师傅',
          status: 'paused',
          progress: 45,
          startTime: '10:15',
        },
        {
          id: 3,
          taskName: '零件C检测',
          operator: '王师傅',
          status: 'running',
          progress: 90,
          startTime: '08:45',
        },
      ]);

      // 模拟告警数据
      setAlerts([
        {
          id: 1,
          type: 'warning',
          message: 'CNC-002设备温度偏高',
          time: dayjs().subtract(5, 'minute').format('HH:mm'),
        },
        {
          id: 2,
          type: 'info',
          message: '零件A加工完成',
          time: dayjs().subtract(10, 'minute').format('HH:mm'),
        },
        {
          id: 3,
          type: 'error',
          message: '质量检测发现不合格品',
          time: dayjs().subtract(15, 'minute').format('HH:mm'),
        },
      ]);
    }, 5000); // 每5秒更新一次

    return () => clearInterval(timer);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <PlayCircleOutlined style={{ color: '#52c41a' }} />;
      case 'paused':
        return <PauseCircleOutlined style={{ color: '#faad14' }} />;
      case 'completed':
        return <StopOutlined style={{ color: '#1890ff' }} />;
      default:
        return <StopOutlined />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running':
        return '进行中';
      case 'paused':
        return '暂停';
      case 'completed':
        return '已完成';
      default:
        return '未知';
    }
  };

  const getAlertColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'red';
      case 'warning':
        return 'orange';
      case 'info':
        return 'blue';
      default:
        return 'default';
    }
  };

  return (
    <Row gutter={[16, 16]}>
      {/* 实时时间 */}
      <Col span={24}>
        <Card size="small">
          <div style={{ textAlign: 'center' }}>
            <Statistic
              title="当前时间"
              value={currentTime.format('YYYY-MM-DD HH:mm:ss')}
              valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
            />
          </div>
        </Card>
      </Col>

      {/* 活跃任务 */}
      <Col xs={24} lg={12}>
        <Card 
          title="活跃任务" 
          size="small"
          extra={<Badge count={activeTasks.length} />}
        >
          <List
            itemLayout="horizontal"
            dataSource={activeTasks}
            renderItem={(item) => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <Avatar icon={getStatusIcon(item.status)} />
                  }
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text strong>{item.taskName}</Text>
                      <Badge 
                        status={item.status === 'running' ? 'processing' : 'default'} 
                        text={getStatusText(item.status)} 
                      />
                    </div>
                  }
                  description={
                    <div>
                      <div style={{ marginBottom: '4px' }}>
                        <UserOutlined /> {item.operator} | 开始时间: {item.startTime}
                      </div>
                      <div>
                        进度: {item.progress}%
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      </Col>

      {/* 实时告警 */}
      <Col xs={24} lg={12}>
        <Card 
          title="实时告警" 
          size="small"
          extra={<AlertOutlined />}
        >
          <List
            itemLayout="horizontal"
            dataSource={alerts}
            renderItem={(item) => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <Badge 
                      color={getAlertColor(item.type)} 
                      dot 
                    />
                  }
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Text>{item.message}</Text>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {item.time}
                      </Text>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default RealTimeMonitor;
