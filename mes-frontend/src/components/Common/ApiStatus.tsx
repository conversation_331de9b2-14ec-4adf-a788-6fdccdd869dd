import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Toolt<PERSON>, But<PERSON> } from 'antd';
import { WifiOutlined, DisconnectOutlined, ReloadOutlined } from '@ant-design/icons';
import { healthCheck } from '../../services/api';

interface ApiStatusProps {
  showText?: boolean;
  size?: 'small' | 'default';
}

const ApiStatus: React.FC<ApiStatusProps> = ({ showText = false, size = 'default' }) => {
  const [status, setStatus] = useState<'online' | 'offline' | 'checking'>('checking');
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const checkApiStatus = async () => {
    setStatus('checking');
    try {
      await healthCheck();
      setStatus('online');
      setLastCheck(new Date());
    } catch (error) {
      setStatus('offline');
      setLastCheck(new Date());
    }
  };

  useEffect(() => {
    checkApiStatus();
    
    // 每30秒检查一次API状态
    const interval = setInterval(checkApiStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusConfig = () => {
    switch (status) {
      case 'online':
        return {
          status: 'success' as const,
          text: '在线',
          icon: <WifiOutlined />,
          color: '#52c41a',
        };
      case 'offline':
        return {
          status: 'error' as const,
          text: '离线',
          icon: <DisconnectOutlined />,
          color: '#ff4d4f',
        };
      case 'checking':
        return {
          status: 'processing' as const,
          text: '检查中',
          icon: <ReloadOutlined spin />,
          color: '#1890ff',
        };
      default:
        return {
          status: 'default' as const,
          text: '未知',
          icon: <DisconnectOutlined />,
          color: '#d9d9d9',
        };
    }
  };

  const config = getStatusConfig();
  
  const tooltipTitle = (
    <div>
      <div>API状态: {config.text}</div>
      {lastCheck && (
        <div>最后检查: {lastCheck.toLocaleTimeString()}</div>
      )}
      <div style={{ marginTop: '4px' }}>
        <Button 
          size="small" 
          type="text" 
          icon={<ReloadOutlined />}
          onClick={checkApiStatus}
          style={{ color: 'white', padding: '0 4px' }}
        >
          重新检查
        </Button>
      </div>
    </div>
  );

  if (showText) {
    return (
      <Tooltip title={tooltipTitle}>
        <Badge 
          status={config.status} 
          text={
            <span style={{ color: config.color, fontSize: size === 'small' ? '12px' : '14px' }}>
              {config.icon} {config.text}
            </span>
          } 
        />
      </Tooltip>
    );
  }

  return (
    <Tooltip title={tooltipTitle}>
      <Badge 
        status={config.status}
        style={{ cursor: 'pointer' }}
        onClick={checkApiStatus}
      />
    </Tooltip>
  );
};

export default ApiStatus;
