// 通用API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
}

// 用户相关类型
export interface User {
  id: number;
  username: string;
  full_name: string;
  email?: string;
  roles: string[];
  skills?: string[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}

// 项目相关类型
export interface Project {
  id: number;
  project_name: string;
  customer_name: string;
  description?: string;
  status: 'PLANNING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  start_date?: string;
  end_date?: string;
  created_at: string;
  updated_at: string;
}

// 零件相关类型
export interface Part {
  id: number;
  part_number: string;
  part_name: string;
  description?: string;
  unit: string;
  category?: string;
  created_at: string;
  updated_at: string;
}

// 工单相关类型
export interface WorkOrder {
  id: number;
  project_id: number;
  part_id: number;
  quantity: number;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
  planned_start?: string;
  planned_end?: string;
  actual_start?: string;
  actual_end?: string;
  created_at: string;
  updated_at: string;
  // 关联数据
  project_name?: string;
  part_number?: string;
  part_name?: string;
}

// 计划任务类型
export interface PlanTask {
  id: number;
  work_order_id: number;
  routing_id: number;
  step_number: number;
  process_name: string;
  assigned_user_id?: number;
  machine_id?: number;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  planned_start: string;
  planned_end: string;
  actual_start?: string;
  actual_end?: string;
  standard_hours?: number;
  created_at: string;
  updated_at: string;
}

// 执行日志类型
export interface ExecutionLog {
  id: number;
  plan_task_id: number;
  user_id: number;
  event_type: 'START' | 'PAUSE' | 'RESUME' | 'COMPLETE' | 'QUALITY_CHECK' | 'ISSUE';
  event_time: string;
  notes?: string;
  quality_data?: any;
  attachments?: string[];
}

// 分页查询参数
export interface PaginationParams {
  page?: number;
  limit?: number;
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// 统计数据类型
export interface DashboardStats {
  total_projects: number;
  active_projects: number;
  total_work_orders: number;
  active_work_orders: number;
  completed_tasks_today: number;
  active_operators: number;
  equipment_utilization: number;
  quality_rate: number;
}

// 图表数据类型
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

// 路由类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType<any>;
  exact?: boolean;
  title: string;
  icon?: string;
  children?: RouteConfig[];
  roles?: string[];
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
  path?: string;
  roles?: string[];
}

// 表格列配置
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  align?: 'left' | 'center' | 'right';
  render?: (value: any, record: any, index: number) => React.ReactNode;
  sorter?: boolean | ((a: any, b: any) => number);
  filters?: { text: string; value: any }[];
  onFilter?: (value: any, record: any) => boolean;
}

// 表单字段配置
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'date' | 'number' | 'switch' | 'upload';
  required?: boolean;
  options?: { label: string; value: any }[];
  placeholder?: string;
  rules?: any[];
}

// 主题配置
export interface ThemeConfig {
  primaryColor: string;
  borderRadius: number;
  colorBgContainer: string;
}
