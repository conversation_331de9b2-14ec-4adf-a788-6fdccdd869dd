{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { isCircular } from '../../../utils/coordinate';\nimport { inferOutsideCircularStyle, radiusOf, angleOf } from './outside';\nimport { hideAndDodgeY } from './utils';\nconst styleByPoints = new WeakMap();\nfunction compute(points, value, coordinate) {\n  const {\n    connectorLength,\n    connectorLength2,\n    connectorDistance\n  } = value;\n  const style = __rest(inferOutsideCircularStyle('outside', points, value, coordinate), []);\n  const center = coordinate.getCenter();\n  const radius = radiusOf(points, value, coordinate);\n  const angle = angleOf(points, value, coordinate);\n  const radius1 = radius + connectorLength + connectorLength2;\n  const sign = Math.sin(angle) > 0 ? 1 : -1;\n  const newX = center[0] + (radius1 + +connectorDistance) * sign;\n  const {\n    x: originX\n  } = style;\n  const dx = newX - originX;\n  style.x += dx;\n  style.connectorPoints[0][0] -= dx;\n  return style;\n}\n/**\n * Spider label transform only suitable for the labels in polar coordinate,\n * labels should distinguish coordinate type.\n */\nexport function spider(position, points, value, coordinate, options, labels) {\n  if (!isCircular(coordinate)) return {};\n  if (styleByPoints.has(points)) return styleByPoints.get(points);\n  const computed = labels.map(points => compute(points, value, coordinate));\n  const {\n    width,\n    height\n  } = coordinate.getOptions();\n  const left = computed.filter(d => d.x < width / 2);\n  const right = computed.filter(d => d.x >= width / 2);\n  const extendedOptions = Object.assign(Object.assign({}, options), {\n    height\n  });\n  hideAndDodgeY(left, extendedOptions);\n  hideAndDodgeY(right, extendedOptions);\n  computed.forEach((style, i) => styleByPoints.set(labels[i], style));\n  return styleByPoints.get(points);\n}", "map": {"version": 3, "names": ["isCircular", "inferOutsideCircularStyle", "radiusOf", "angleOf", "hideAndDodgeY", "styleByPoints", "WeakMap", "compute", "points", "value", "coordinate", "connectorLength", "connectorLength2", "connectorDistance", "style", "__rest", "center", "getCenter", "radius", "angle", "radius1", "sign", "Math", "sin", "newX", "x", "originX", "dx", "connectorPoints", "spider", "position", "options", "labels", "has", "get", "computed", "map", "width", "height", "getOptions", "left", "filter", "d", "right", "extendedOptions", "Object", "assign", "for<PERSON>ach", "i", "set"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/spider.ts"], "sourcesContent": ["import { Coordinate } from '@antv/coord';\nimport { Vector2 } from '../../../runtime';\nimport { isCircular } from '../../../utils/coordinate';\nimport { LabelPosition } from './default';\nimport { inferOutsideCircularStyle, radiusOf, angleOf } from './outside';\nimport { hideAndDodgeY } from './utils';\n\nconst styleByPoints = new WeakMap();\n\nfunction compute(\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n) {\n  const { connectorLength, connectorLength2, connectorDistance } = value;\n  const { ...style }: any = inferOutsideCircularStyle(\n    'outside',\n    points,\n    value,\n    coordinate,\n  );\n  const center = coordinate.getCenter();\n  const radius = radiusOf(points, value, coordinate);\n  const angle = angleOf(points, value, coordinate);\n  const radius1 = radius + connectorLength + connectorLength2;\n  const sign = Math.sin(angle) > 0 ? 1 : -1;\n  const newX = center[0] + (radius1 + +connectorDistance) * sign;\n  const { x: originX } = style;\n  const dx = newX - originX;\n  style.x += dx;\n  style.connectorPoints[0][0] -= dx;\n  return style;\n}\n\n/**\n * Spider label transform only suitable for the labels in polar coordinate,\n * labels should distinguish coordinate type.\n */\nexport function spider(\n  position: LabelPosition,\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n  options: Record<string, any>,\n  labels: Vector2[][],\n) {\n  if (!isCircular(coordinate)) return {};\n  if (styleByPoints.has(points)) return styleByPoints.get(points);\n  const computed = labels.map((points) => compute(points, value, coordinate));\n  const { width, height } = coordinate.getOptions();\n  const left = computed.filter((d) => d.x < width / 2);\n  const right = computed.filter((d) => d.x >= width / 2);\n  const extendedOptions = { ...options, height };\n  hideAndDodgeY(left, extendedOptions);\n  hideAndDodgeY(right, extendedOptions);\n  computed.forEach((style, i) => styleByPoints.set(labels[i], style));\n  return styleByPoints.get(points);\n}\n"], "mappings": ";;;;;;;;AAEA,SAASA,UAAU,QAAQ,2BAA2B;AAEtD,SAASC,yBAAyB,EAAEC,QAAQ,EAAEC,OAAO,QAAQ,WAAW;AACxE,SAASC,aAAa,QAAQ,SAAS;AAEvC,MAAMC,aAAa,GAAG,IAAIC,OAAO,EAAE;AAEnC,SAASC,OAAOA,CACdC,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB;EAEtB,MAAM;IAAEC,eAAe;IAAEC,gBAAgB;IAAEC;EAAiB,CAAE,GAAGJ,KAAK;EACtE,MAAWK,KAAK,GAAAC,MAAA,CAAUd,yBAAyB,CACjD,SAAS,EACTO,MAAM,EACNC,KAAK,EACLC,UAAU,CACX,EALK,EAAY,CAKjB;EACD,MAAMM,MAAM,GAAGN,UAAU,CAACO,SAAS,EAAE;EACrC,MAAMC,MAAM,GAAGhB,QAAQ,CAACM,MAAM,EAAEC,KAAK,EAAEC,UAAU,CAAC;EAClD,MAAMS,KAAK,GAAGhB,OAAO,CAACK,MAAM,EAAEC,KAAK,EAAEC,UAAU,CAAC;EAChD,MAAMU,OAAO,GAAGF,MAAM,GAAGP,eAAe,GAAGC,gBAAgB;EAC3D,MAAMS,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACzC,MAAMK,IAAI,GAAGR,MAAM,CAAC,CAAC,CAAC,GAAG,CAACI,OAAO,GAAG,CAACP,iBAAiB,IAAIQ,IAAI;EAC9D,MAAM;IAAEI,CAAC,EAAEC;EAAO,CAAE,GAAGZ,KAAK;EAC5B,MAAMa,EAAE,GAAGH,IAAI,GAAGE,OAAO;EACzBZ,KAAK,CAACW,CAAC,IAAIE,EAAE;EACbb,KAAK,CAACc,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAID,EAAE;EACjC,OAAOb,KAAK;AACd;AAEA;;;;AAIA,OAAM,SAAUe,MAAMA,CACpBC,QAAuB,EACvBtB,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB,EACtBqB,OAA4B,EAC5BC,MAAmB;EAEnB,IAAI,CAAChC,UAAU,CAACU,UAAU,CAAC,EAAE,OAAO,EAAE;EACtC,IAAIL,aAAa,CAAC4B,GAAG,CAACzB,MAAM,CAAC,EAAE,OAAOH,aAAa,CAAC6B,GAAG,CAAC1B,MAAM,CAAC;EAC/D,MAAM2B,QAAQ,GAAGH,MAAM,CAACI,GAAG,CAAE5B,MAAM,IAAKD,OAAO,CAACC,MAAM,EAAEC,KAAK,EAAEC,UAAU,CAAC,CAAC;EAC3E,MAAM;IAAE2B,KAAK;IAAEC;EAAM,CAAE,GAAG5B,UAAU,CAAC6B,UAAU,EAAE;EACjD,MAAMC,IAAI,GAAGL,QAAQ,CAACM,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjB,CAAC,GAAGY,KAAK,GAAG,CAAC,CAAC;EACpD,MAAMM,KAAK,GAAGR,QAAQ,CAACM,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjB,CAAC,IAAIY,KAAK,GAAG,CAAC,CAAC;EACtD,MAAMO,eAAe,GAAAC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQf,OAAO;IAAEO;EAAM,EAAE;EAC9ClC,aAAa,CAACoC,IAAI,EAAEI,eAAe,CAAC;EACpCxC,aAAa,CAACuC,KAAK,EAAEC,eAAe,CAAC;EACrCT,QAAQ,CAACY,OAAO,CAAC,CAACjC,KAAK,EAAEkC,CAAC,KAAK3C,aAAa,CAAC4C,GAAG,CAACjB,MAAM,CAACgB,CAAC,CAAC,EAAElC,KAAK,CAAC,CAAC;EACnE,OAAOT,aAAa,CAAC6B,GAAG,CAAC1B,MAAM,CAAC;AAClC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}