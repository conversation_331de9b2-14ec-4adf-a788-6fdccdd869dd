{"ast": null, "code": "var _ = require(\"./lodash\");\nvar util = require(\"./util\");\nmodule.exports = {\n  run: run,\n  cleanup: cleanup\n};\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, \"root\", {}, \"_root\");\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function (e) {\n    g.edge(e).minlen *= nodeSep;\n  });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, {\n        weight: 0,\n        minlen: nodeSep\n      });\n    }\n    return;\n  }\n  var top = util.addBorderNode(g, \"_bt\");\n  var bottom = util.addBorderNode(g, \"_bb\");\n  var label = g.node(v);\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n  _.forEach(children, function (child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n  });\n  if (!g.parent(v)) {\n    g.setEdge(root, top, {\n      weight: 0,\n      minlen: height + depths[v]\n    });\n  }\n}\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function (child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function (v) {\n    dfs(v, 1);\n  });\n  return depths;\n}\nfunction sumWeights(g) {\n  return _.reduce(g.edges(), function (acc, e) {\n    return acc + g.edge(e).weight;\n  }, 0);\n}\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}", "map": {"version": 3, "names": ["_", "require", "util", "module", "exports", "run", "cleanup", "g", "root", "addDummyNode", "depths", "treeDepths", "height", "max", "values", "nodeSep", "graph", "nestingRoot", "for<PERSON>ach", "edges", "e", "edge", "minlen", "weight", "sumWeights", "children", "child", "dfs", "nodeRankFactor", "v", "length", "setEdge", "top", "addBorderNode", "bottom", "label", "node", "setParent", "borderTop", "borderBottom", "childNode", "childTop", "childBottom", "thisWeight", "nestingEdge", "parent", "depth", "reduce", "acc", "graphLabel", "removeNode", "removeEdge"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/nesting-graph.js"], "sourcesContent": ["var _ = require(\"./lodash\");\nvar util = require(\"./util\");\n\nmodule.exports = {\n  run: run,\n  cleanup: cleanup\n};\n\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nfunction run(g) {\n  var root = util.addDummyNode(g, \"root\", {}, \"_root\");\n  var depths = treeDepths(g);\n  var height = _.max(_.values(depths)) - 1; // Note: depths is an Object not an array\n  var nodeSep = 2 * height + 1;\n\n  g.graph().nestingRoot = root;\n\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  _.forEach(g.edges(), function(e) { g.edge(e).minlen *= nodeSep; });\n\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  var weight = sumWeights(g) + 1;\n\n  // Create border nodes and link them up\n  _.forEach(g.children(), function(child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n  });\n\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  g.graph().nodeRankFactor = nodeSep;\n}\n\nfunction dfs(g, root, nodeSep, weight, height, depths, v) {\n  var children = g.children(v);\n  if (!children.length) {\n    if (v !== root) {\n      g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n    }\n    return;\n  }\n\n  var top = util.addBorderNode(g, \"_bt\");\n  var bottom = util.addBorderNode(g, \"_bb\");\n  var label = g.node(v);\n\n  g.setParent(top, v);\n  label.borderTop = top;\n  g.setParent(bottom, v);\n  label.borderBottom = bottom;\n\n  _.forEach(children, function(child) {\n    dfs(g, root, nodeSep, weight, height, depths, child);\n\n    var childNode = g.node(child);\n    var childTop = childNode.borderTop ? childNode.borderTop : child;\n    var childBottom = childNode.borderBottom ? childNode.borderBottom : child;\n    var thisWeight = childNode.borderTop ? weight : 2 * weight;\n    var minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n\n    g.setEdge(top, childTop, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n\n    g.setEdge(childBottom, bottom, {\n      weight: thisWeight,\n      minlen: minlen,\n      nestingEdge: true\n    });\n  });\n\n  if (!g.parent(v)) {\n    g.setEdge(root, top, { weight: 0, minlen: height + depths[v] });\n  }\n}\n\nfunction treeDepths(g) {\n  var depths = {};\n  function dfs(v, depth) {\n    var children = g.children(v);\n    if (children && children.length) {\n      _.forEach(children, function(child) {\n        dfs(child, depth + 1);\n      });\n    }\n    depths[v] = depth;\n  }\n  _.forEach(g.children(), function(v) { dfs(v, 1); });\n  return depths;\n}\n\nfunction sumWeights(g) {\n  return _.reduce(g.edges(), function(acc, e) {\n    return acc + g.edge(e).weight;\n  }, 0);\n}\n\nfunction cleanup(g) {\n  var graphLabel = g.graph();\n  g.removeNode(graphLabel.nestingRoot);\n  delete graphLabel.nestingRoot;\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (edge.nestingEdge) {\n      g.removeEdge(e);\n    }\n  });\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAE5BE,MAAM,CAACC,OAAO,GAAG;EACfC,GAAG,EAAEA,GAAG;EACRC,OAAO,EAAEA;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,GAAGA,CAACE,CAAC,EAAE;EACd,IAAIC,IAAI,GAAGN,IAAI,CAACO,YAAY,CAACF,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,OAAO,CAAC;EACpD,IAAIG,MAAM,GAAGC,UAAU,CAACJ,CAAC,CAAC;EAC1B,IAAIK,MAAM,GAAGZ,CAAC,CAACa,GAAG,CAACb,CAAC,CAACc,MAAM,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1C,IAAIK,OAAO,GAAG,CAAC,GAAGH,MAAM,GAAG,CAAC;EAE5BL,CAAC,CAACS,KAAK,CAAC,CAAC,CAACC,WAAW,GAAGT,IAAI;;EAE5B;EACAR,CAAC,CAACkB,OAAO,CAACX,CAAC,CAACY,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAAEb,CAAC,CAACc,IAAI,CAACD,CAAC,CAAC,CAACE,MAAM,IAAIP,OAAO;EAAE,CAAC,CAAC;;EAElE;EACA,IAAIQ,MAAM,GAAGC,UAAU,CAACjB,CAAC,CAAC,GAAG,CAAC;;EAE9B;EACAP,CAAC,CAACkB,OAAO,CAACX,CAAC,CAACkB,QAAQ,CAAC,CAAC,EAAE,UAASC,KAAK,EAAE;IACtCC,GAAG,CAACpB,CAAC,EAAEC,IAAI,EAAEO,OAAO,EAAEQ,MAAM,EAAEX,MAAM,EAAEF,MAAM,EAAEgB,KAAK,CAAC;EACtD,CAAC,CAAC;;EAEF;EACA;EACAnB,CAAC,CAACS,KAAK,CAAC,CAAC,CAACY,cAAc,GAAGb,OAAO;AACpC;AAEA,SAASY,GAAGA,CAACpB,CAAC,EAAEC,IAAI,EAAEO,OAAO,EAAEQ,MAAM,EAAEX,MAAM,EAAEF,MAAM,EAAEmB,CAAC,EAAE;EACxD,IAAIJ,QAAQ,GAAGlB,CAAC,CAACkB,QAAQ,CAACI,CAAC,CAAC;EAC5B,IAAI,CAACJ,QAAQ,CAACK,MAAM,EAAE;IACpB,IAAID,CAAC,KAAKrB,IAAI,EAAE;MACdD,CAAC,CAACwB,OAAO,CAACvB,IAAI,EAAEqB,CAAC,EAAE;QAAEN,MAAM,EAAE,CAAC;QAAED,MAAM,EAAEP;MAAQ,CAAC,CAAC;IACpD;IACA;EACF;EAEA,IAAIiB,GAAG,GAAG9B,IAAI,CAAC+B,aAAa,CAAC1B,CAAC,EAAE,KAAK,CAAC;EACtC,IAAI2B,MAAM,GAAGhC,IAAI,CAAC+B,aAAa,CAAC1B,CAAC,EAAE,KAAK,CAAC;EACzC,IAAI4B,KAAK,GAAG5B,CAAC,CAAC6B,IAAI,CAACP,CAAC,CAAC;EAErBtB,CAAC,CAAC8B,SAAS,CAACL,GAAG,EAAEH,CAAC,CAAC;EACnBM,KAAK,CAACG,SAAS,GAAGN,GAAG;EACrBzB,CAAC,CAAC8B,SAAS,CAACH,MAAM,EAAEL,CAAC,CAAC;EACtBM,KAAK,CAACI,YAAY,GAAGL,MAAM;EAE3BlC,CAAC,CAACkB,OAAO,CAACO,QAAQ,EAAE,UAASC,KAAK,EAAE;IAClCC,GAAG,CAACpB,CAAC,EAAEC,IAAI,EAAEO,OAAO,EAAEQ,MAAM,EAAEX,MAAM,EAAEF,MAAM,EAAEgB,KAAK,CAAC;IAEpD,IAAIc,SAAS,GAAGjC,CAAC,CAAC6B,IAAI,CAACV,KAAK,CAAC;IAC7B,IAAIe,QAAQ,GAAGD,SAAS,CAACF,SAAS,GAAGE,SAAS,CAACF,SAAS,GAAGZ,KAAK;IAChE,IAAIgB,WAAW,GAAGF,SAAS,CAACD,YAAY,GAAGC,SAAS,CAACD,YAAY,GAAGb,KAAK;IACzE,IAAIiB,UAAU,GAAGH,SAAS,CAACF,SAAS,GAAGf,MAAM,GAAG,CAAC,GAAGA,MAAM;IAC1D,IAAID,MAAM,GAAGmB,QAAQ,KAAKC,WAAW,GAAG,CAAC,GAAG9B,MAAM,GAAGF,MAAM,CAACmB,CAAC,CAAC,GAAG,CAAC;IAElEtB,CAAC,CAACwB,OAAO,CAACC,GAAG,EAAES,QAAQ,EAAE;MACvBlB,MAAM,EAAEoB,UAAU;MAClBrB,MAAM,EAAEA,MAAM;MACdsB,WAAW,EAAE;IACf,CAAC,CAAC;IAEFrC,CAAC,CAACwB,OAAO,CAACW,WAAW,EAAER,MAAM,EAAE;MAC7BX,MAAM,EAAEoB,UAAU;MAClBrB,MAAM,EAAEA,MAAM;MACdsB,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,IAAI,CAACrC,CAAC,CAACsC,MAAM,CAAChB,CAAC,CAAC,EAAE;IAChBtB,CAAC,CAACwB,OAAO,CAACvB,IAAI,EAAEwB,GAAG,EAAE;MAAET,MAAM,EAAE,CAAC;MAAED,MAAM,EAAEV,MAAM,GAAGF,MAAM,CAACmB,CAAC;IAAE,CAAC,CAAC;EACjE;AACF;AAEA,SAASlB,UAAUA,CAACJ,CAAC,EAAE;EACrB,IAAIG,MAAM,GAAG,CAAC,CAAC;EACf,SAASiB,GAAGA,CAACE,CAAC,EAAEiB,KAAK,EAAE;IACrB,IAAIrB,QAAQ,GAAGlB,CAAC,CAACkB,QAAQ,CAACI,CAAC,CAAC;IAC5B,IAAIJ,QAAQ,IAAIA,QAAQ,CAACK,MAAM,EAAE;MAC/B9B,CAAC,CAACkB,OAAO,CAACO,QAAQ,EAAE,UAASC,KAAK,EAAE;QAClCC,GAAG,CAACD,KAAK,EAAEoB,KAAK,GAAG,CAAC,CAAC;MACvB,CAAC,CAAC;IACJ;IACApC,MAAM,CAACmB,CAAC,CAAC,GAAGiB,KAAK;EACnB;EACA9C,CAAC,CAACkB,OAAO,CAACX,CAAC,CAACkB,QAAQ,CAAC,CAAC,EAAE,UAASI,CAAC,EAAE;IAAEF,GAAG,CAACE,CAAC,EAAE,CAAC,CAAC;EAAE,CAAC,CAAC;EACnD,OAAOnB,MAAM;AACf;AAEA,SAASc,UAAUA,CAACjB,CAAC,EAAE;EACrB,OAAOP,CAAC,CAAC+C,MAAM,CAACxC,CAAC,CAACY,KAAK,CAAC,CAAC,EAAE,UAAS6B,GAAG,EAAE5B,CAAC,EAAE;IAC1C,OAAO4B,GAAG,GAAGzC,CAAC,CAACc,IAAI,CAACD,CAAC,CAAC,CAACG,MAAM;EAC/B,CAAC,EAAE,CAAC,CAAC;AACP;AAEA,SAASjB,OAAOA,CAACC,CAAC,EAAE;EAClB,IAAI0C,UAAU,GAAG1C,CAAC,CAACS,KAAK,CAAC,CAAC;EAC1BT,CAAC,CAAC2C,UAAU,CAACD,UAAU,CAAChC,WAAW,CAAC;EACpC,OAAOgC,UAAU,CAAChC,WAAW;EAC7BjB,CAAC,CAACkB,OAAO,CAACX,CAAC,CAACY,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGd,CAAC,CAACc,IAAI,CAACD,CAAC,CAAC;IACpB,IAAIC,IAAI,CAACuB,WAAW,EAAE;MACpBrC,CAAC,CAAC4C,UAAU,CAAC/B,CAAC,CAAC;IACjB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}