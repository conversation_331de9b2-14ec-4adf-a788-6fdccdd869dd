{"ast": null, "code": "import { asNonCompoundGraph, buildLayerMatrix } from '../util';\nimport { alignCoordinates, balance, findSmallestWidthAlignment, findType1Conflicts, findType2Conflicts, horizontalCompaction, verticalAlignment } from './bk';\nconst positionY = (g, options) => {\n  const {\n    ranksep = 0\n  } = options || {};\n  const layering = buildLayerMatrix(g);\n  let prevY = 0;\n  layering === null || layering === void 0 ? void 0 : layering.forEach(layer => {\n    const heights = layer.map(v => g.getNode(v).data.height);\n    const maxHeight = Math.max(...heights, 0);\n    layer === null || layer === void 0 ? void 0 : layer.forEach(v => {\n      g.getNode(v).data.y = prevY + maxHeight / 2;\n    });\n    prevY += maxHeight + ranksep;\n  });\n};\nconst positionX = (g, options) => {\n  const {\n    align: graphAlign,\n    nodesep = 0,\n    edgesep = 0\n  } = options || {};\n  const layering = buildLayerMatrix(g);\n  const conflicts = Object.assign(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n  const xss = {};\n  let adjustedLayering = [];\n  ['u', 'd'].forEach(vert => {\n    adjustedLayering = vert === 'u' ? layering : Object.values(layering).reverse();\n    ['l', 'r'].forEach(horiz => {\n      if (horiz === 'r') {\n        adjustedLayering = adjustedLayering.map(inner => Object.values(inner).reverse());\n      }\n      const neighborFn = (vert === 'u' ? g.getPredecessors : g.getSuccessors).bind(g);\n      const align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      const xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, nodesep, edgesep, horiz === 'r');\n      if (horiz === 'r') {\n        Object.keys(xs).forEach(xsKey => xs[xsKey] = -xs[xsKey]);\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n  const smallestWidth = findSmallestWidthAlignment(g, xss);\n  smallestWidth && alignCoordinates(xss, smallestWidth);\n  return balance(xss, graphAlign);\n};\nexport const position = (g, options) => {\n  var _a;\n  const ng = asNonCompoundGraph(g);\n  positionY(ng, options);\n  const xs = positionX(ng, options);\n  (_a = Object.keys(xs)) === null || _a === void 0 ? void 0 : _a.forEach(key => {\n    ng.getNode(key).data.x = xs[key];\n  });\n};", "map": {"version": 3, "names": ["asNonCompoundGraph", "buildLayerMatrix", "alignCoordinates", "balance", "findSmallestWidthAlignment", "findType1Conflicts", "findType2Conflicts", "horizontalCompaction", "verticalAlignment", "positionY", "g", "options", "ranksep", "layering", "prevY", "for<PERSON>ach", "layer", "heights", "map", "v", "getNode", "data", "height", "maxHeight", "Math", "max", "y", "positionX", "align", "graphAlign", "nodesep", "edgesep", "conflicts", "Object", "assign", "xss", "adjustedLayering", "vert", "values", "reverse", "horiz", "inner", "neighborFn", "getPredecessors", "getSuccessors", "bind", "xs", "root", "keys", "xsKey", "smallestWidth", "position", "ng", "_a", "key", "x"], "sources": ["../../../src/antv-dagre/position/index.ts"], "sourcesContent": [null], "mappings": "AAGA,SAASA,kBAAkB,EAAEC,gBAAgB,QAAQ,SAAS;AAC9D,SACEC,gBAAgB,EAChBC,OAAO,EACPC,0BAA0B,EAC1BC,kBAAkB,EAClBC,kBAAkB,EAClBC,oBAAoB,EACpBC,iBAAiB,QACZ,MAAM;AAEb,MAAMC,SAAS,GAAGA,CAChBC,CAAS,EACTC,OAEE,KACA;EACF,MAAM;IAAEC,OAAO,GAAG;EAAC,CAAE,GAAGD,OAAO,IAAI,EAAE;EACrC,MAAME,QAAQ,GAAGZ,gBAAgB,CAACS,CAAC,CAAC;EAEpC,IAAII,KAAK,GAAG,CAAC;EACbD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,OAAO,CAAEC,KAAK,IAAI;IAC1B,MAAMC,OAAO,GAAGD,KAAK,CAACE,GAAG,CAAEC,CAAC,IAAKT,CAAC,CAACU,OAAO,CAACD,CAAC,CAAC,CAACE,IAAI,CAACC,MAAO,CAAC;IAC3D,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGR,OAAO,EAAE,CAAC,CAAC;IACzCD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAED,OAAO,CAAEI,CAAS,IAAI;MAC3BT,CAAC,CAACU,OAAO,CAACD,CAAC,CAAC,CAACE,IAAI,CAACK,CAAC,GAAGZ,KAAK,GAAGS,SAAS,GAAG,CAAC;IAC7C,CAAC,CAAC;IACFT,KAAK,IAAIS,SAAS,GAAGX,OAAO;EAC9B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMe,SAAS,GAAGA,CAChBjB,CAAS,EACTC,OAIE,KACoB;EACtB,MAAM;IAAEiB,KAAK,EAAEC,UAAU;IAAEC,OAAO,GAAG,CAAC;IAAEC,OAAO,GAAG;EAAC,CAAE,GAAGpB,OAAO,IAAI,EAAE;EAErE,MAAME,QAAQ,GAAGZ,gBAAgB,CAACS,CAAC,CAAC;EACpC,MAAMsB,SAAS,GAAGC,MAAM,CAACC,MAAM,CAC7B7B,kBAAkB,CAACK,CAAC,EAAEG,QAAQ,CAAC,EAC/BP,kBAAkB,CAACI,CAAC,EAAEG,QAAQ,CAAC,CAChC;EAED,MAAMsB,GAAG,GAA2C,EAAE;EACtD,IAAIC,gBAAgB,GAAW,EAAE;EACjC,CAAC,GAAG,EAAE,GAAG,CAAC,CAACrB,OAAO,CAAEsB,IAAI,IAAI;IAC1BD,gBAAgB,GACdC,IAAI,KAAK,GAAG,GAAGxB,QAAQ,GAAGoB,MAAM,CAACK,MAAM,CAACzB,QAAQ,CAAC,CAAC0B,OAAO,EAAE;IAC7D,CAAC,GAAG,EAAE,GAAG,CAAC,CAACxB,OAAO,CAAEyB,KAAK,IAAI;MAC3B,IAAIA,KAAK,KAAK,GAAG,EAAE;QACjBJ,gBAAgB,GAAGA,gBAAgB,CAAClB,GAAG,CAAEuB,KAAK,IAC5CR,MAAM,CAACK,MAAM,CAACG,KAAK,CAAC,CAACF,OAAO,EAAE,CAC/B;;MAGH,MAAMG,UAAU,GAAG,CACjBL,IAAI,KAAK,GAAG,GAAG3B,CAAC,CAACiC,eAAe,GAAGjC,CAAC,CAACkC,aAAa,EAClDC,IAAI,CAACnC,CAAC,CAAC;MACT,MAAMkB,KAAK,GAAGpB,iBAAiB,CAC7BE,CAAC,EACD0B,gBAAgB,EAChBJ,SAAS,EACTU,UAAU,CACX;MACD,MAAMI,EAAE,GAAGvC,oBAAoB,CAC7BG,CAAC,EACD0B,gBAAgB,EAChBR,KAAK,CAACmB,IAAI,EACVnB,KAAK,CAACA,KAAK,EACXE,OAAO,EACPC,OAAO,EACPS,KAAK,KAAK,GAAG,CACd;MACD,IAAIA,KAAK,KAAK,GAAG,EAAE;QACjBP,MAAM,CAACe,IAAI,CAACF,EAAE,CAAC,CAAC/B,OAAO,CAAEkC,KAAK,IAAMH,EAAE,CAACG,KAAK,CAAC,GAAG,CAACH,EAAE,CAACG,KAAK,CAAE,CAAC;;MAE9Dd,GAAG,CAACE,IAAI,GAAGG,KAAK,CAAC,GAAGM,EAAE;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAMI,aAAa,GAAG9C,0BAA0B,CAACM,CAAC,EAAEyB,GAAG,CAAC;EACxDe,aAAa,IAAIhD,gBAAgB,CAACiC,GAAG,EAAEe,aAAa,CAAC;EACrD,OAAO/C,OAAO,CAACgC,GAAG,EAAEN,UAAU,CAAC;AACjC,CAAC;AAED,OAAO,MAAMsB,QAAQ,GAAGA,CACtBzC,CAAS,EACTC,OAKE,KACA;;EACF,MAAMyC,EAAE,GAAGpD,kBAAkB,CAACU,CAAC,CAAC;EAEhCD,SAAS,CAAC2C,EAAE,EAAEzC,OAAO,CAAC;EACtB,MAAMmC,EAAE,GAAGnB,SAAS,CAACyB,EAAE,EAAEzC,OAAO,CAAC;EACjC,CAAA0C,EAAA,GAAApB,MAAM,CAACe,IAAI,CAACF,EAAE,CAAC,cAAAO,EAAA,uBAAAA,EAAA,CAAEtC,OAAO,CAAEuC,GAAO,IAAI;IACnCF,EAAE,CAAChC,OAAO,CAACkC,GAAG,CAAC,CAACjC,IAAI,CAACkC,CAAC,GAAGT,EAAE,CAACQ,GAAG,CAAC;EAClC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}