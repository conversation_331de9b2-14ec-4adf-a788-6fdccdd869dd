{"ast": null, "code": "/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n\n\"use strict\";\n\nfunction peg$subclass(child, parent) {\n  function ctor() {\n    this.constructor = child;\n  }\n  ctor.prototype = parent.prototype;\n  child.prototype = new ctor();\n}\nfunction peg$SyntaxError(message, expected, found, location) {\n  this.message = message;\n  this.expected = expected;\n  this.found = found;\n  this.location = location;\n  this.name = \"SyntaxError\";\n  if (typeof Error.captureStackTrace === \"function\") {\n    Error.captureStackTrace(this, peg$SyntaxError);\n  }\n}\npeg$subclass(peg$SyntaxError, Error);\npeg$SyntaxError.buildMessage = function (expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n    literal: function (expectation) {\n      return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n    },\n    \"class\": function (expectation) {\n      var escapedParts = \"\",\n        i;\n      for (i = 0; i < expectation.parts.length; i++) {\n        escapedParts += expectation.parts[i] instanceof Array ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1]) : classEscape(expectation.parts[i]);\n      }\n      return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n    },\n    any: function (expectation) {\n      return \"any character\";\n    },\n    end: function (expectation) {\n      return \"end of input\";\n    },\n    other: function (expectation) {\n      return expectation.description;\n    }\n  };\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n  function literalEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function classEscape(s) {\n    return s.replace(/\\\\/g, '\\\\\\\\').replace(/\\]/g, '\\\\]').replace(/\\^/g, '\\\\^').replace(/-/g, '\\\\-').replace(/\\0/g, '\\\\0').replace(/\\t/g, '\\\\t').replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r').replace(/[\\x00-\\x0F]/g, function (ch) {\n      return '\\\\x0' + hex(ch);\n    }).replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function (ch) {\n      return '\\\\x' + hex(ch);\n    });\n  }\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n  function describeExpected(expected) {\n    var descriptions = new Array(expected.length),\n      i,\n      j;\n    for (i = 0; i < expected.length; i++) {\n      descriptions[i] = describeExpectation(expected[i]);\n    }\n    descriptions.sort();\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n      default:\n        return descriptions.slice(0, -1).join(\", \") + \", or \" + descriptions[descriptions.length - 1];\n    }\n  }\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\nfunction peg$parse(input, options) {\n  options = options !== void 0 ? options : {};\n  var peg$FAILED = {},\n    peg$startRuleFunctions = {\n      svg_path: peg$parsesvg_path\n    },\n    peg$startRuleFunction = peg$parsesvg_path,\n    peg$c0 = function (data) {\n      if (!data) return [];\n      for (var cmds = [], i = 0; i < data.length; i++) cmds = cmds.concat.apply(cmds, data[i]);\n      var first = cmds[0];\n      if (first && first.code == 'm') {\n        // Per spec, first moveto is never relative\n        delete first.relative;\n        first.code = 'M';\n      }\n      return cmds;\n    },\n    peg$c1 = function (first, more) {\n      return merge(first, more);\n    },\n    peg$c2 = /^[Mm]/,\n    peg$c3 = peg$classExpectation([\"M\", \"m\"], false, false),\n    peg$c4 = function (c, first, more) {\n      var move = commands(c, [first]);\n      if (more) move = move.concat(commands(c == 'M' ? 'L' : 'l', more[1]));\n      return move;\n    },\n    peg$c5 = /^[Zz]/,\n    peg$c6 = peg$classExpectation([\"Z\", \"z\"], false, false),\n    peg$c7 = function () {\n      return commands('Z');\n    },\n    peg$c8 = /^[Ll]/,\n    peg$c9 = peg$classExpectation([\"L\", \"l\"], false, false),\n    peg$c10 = function (c, args) {\n      return commands(c, args);\n    },\n    peg$c11 = /^[Hh]/,\n    peg$c12 = peg$classExpectation([\"H\", \"h\"], false, false),\n    peg$c13 = function (c, args) {\n      return commands(c, args.map(function (x) {\n        return {\n          x: x\n        };\n      }));\n    },\n    peg$c14 = /^[Vv]/,\n    peg$c15 = peg$classExpectation([\"V\", \"v\"], false, false),\n    peg$c16 = function (c, args) {\n      return commands(c, args.map(function (y) {\n        return {\n          y: y\n        };\n      }));\n    },\n    peg$c17 = /^[Cc]/,\n    peg$c18 = peg$classExpectation([\"C\", \"c\"], false, false),\n    peg$c19 = function (a, b, c) {\n      return {\n        x1: a.x,\n        y1: a.y,\n        x2: b.x,\n        y2: b.y,\n        x: c.x,\n        y: c.y\n      };\n    },\n    peg$c20 = /^[Ss]/,\n    peg$c21 = peg$classExpectation([\"S\", \"s\"], false, false),\n    peg$c22 = function (b, c) {\n      return {\n        x2: b.x,\n        y2: b.y,\n        x: c.x,\n        y: c.y\n      };\n    },\n    peg$c23 = /^[Qq]/,\n    peg$c24 = peg$classExpectation([\"Q\", \"q\"], false, false),\n    peg$c25 = function (a, b) {\n      return {\n        x1: a.x,\n        y1: a.y,\n        x: b.x,\n        y: b.y\n      };\n    },\n    peg$c26 = /^[Tt]/,\n    peg$c27 = peg$classExpectation([\"T\", \"t\"], false, false),\n    peg$c28 = /^[Aa]/,\n    peg$c29 = peg$classExpectation([\"A\", \"a\"], false, false),\n    peg$c30 = function (rx, ry, xrot, large, sweep, xy) {\n      return {\n        rx: rx,\n        ry: ry,\n        xAxisRotation: xrot,\n        largeArc: large,\n        sweep: sweep,\n        x: xy.x,\n        y: xy.y\n      };\n    },\n    peg$c31 = function (x, y) {\n      return {\n        x: x,\n        y: y\n      };\n    },\n    peg$c32 = function (n) {\n      return n * 1;\n    },\n    peg$c33 = function (parts) {\n      return parts.join('') * 1;\n    },\n    peg$c34 = /^[01]/,\n    peg$c35 = peg$classExpectation([\"0\", \"1\"], false, false),\n    peg$c36 = function (bit) {\n      return bit == '1';\n    },\n    peg$c37 = function () {\n      return '';\n    },\n    peg$c38 = \",\",\n    peg$c39 = peg$literalExpectation(\",\", false),\n    peg$c40 = function (parts) {\n      return parts.join('');\n    },\n    peg$c41 = \".\",\n    peg$c42 = peg$literalExpectation(\".\", false),\n    peg$c43 = /^[eE]/,\n    peg$c44 = peg$classExpectation([\"e\", \"E\"], false, false),\n    peg$c45 = /^[+\\-]/,\n    peg$c46 = peg$classExpectation([\"+\", \"-\"], false, false),\n    peg$c47 = /^[0-9]/,\n    peg$c48 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n    peg$c49 = function (digits) {\n      return digits.join('');\n    },\n    peg$c50 = /^[ \\t\\n\\r]/,\n    peg$c51 = peg$classExpectation([\" \", \"\\t\", \"\\n\", \"\\r\"], false, false),\n    peg$currPos = 0,\n    peg$savedPos = 0,\n    peg$posDetailsCache = [{\n      line: 1,\n      column: 1\n    }],\n    peg$maxFailPos = 0,\n    peg$maxFailExpected = [],\n    peg$silentFails = 0,\n    peg$result;\n  if (\"startRule\" in options) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n  function expected(description, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildStructuredError([peg$otherExpectation(description)], input.substring(peg$savedPos, peg$currPos), location);\n  }\n  function error(message, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos);\n    throw peg$buildSimpleError(message, location);\n  }\n  function peg$literalExpectation(text, ignoreCase) {\n    return {\n      type: \"literal\",\n      text: text,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return {\n      type: \"class\",\n      parts: parts,\n      inverted: inverted,\n      ignoreCase: ignoreCase\n    };\n  }\n  function peg$anyExpectation() {\n    return {\n      type: \"any\"\n    };\n  }\n  function peg$endExpectation() {\n    return {\n      type: \"end\"\n    };\n  }\n  function peg$otherExpectation(description) {\n    return {\n      type: \"other\",\n      description: description\n    };\n  }\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos],\n      p;\n    if (details) {\n      return details;\n    } else {\n      p = pos - 1;\n      while (!peg$posDetailsCache[p]) {\n        p--;\n      }\n      details = peg$posDetailsCache[p];\n      details = {\n        line: details.line,\n        column: details.column\n      };\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n        p++;\n      }\n      peg$posDetailsCache[pos] = details;\n      return details;\n    }\n  }\n  function peg$computeLocation(startPos, endPos) {\n    var startPosDetails = peg$computePosDetails(startPos),\n      endPosDetails = peg$computePosDetails(endPos);\n    return {\n      start: {\n        offset: startPos,\n        line: startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line: endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n  }\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) {\n      return;\n    }\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n    peg$maxFailExpected.push(expected);\n  }\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(peg$SyntaxError.buildMessage(expected, found), expected, found, location);\n  }\n  function peg$parsesvg_path() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = peg$parsewsp();\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsemoveTo_drawTo_commandGroups();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c0(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsemoveTo_drawTo_commandGroups() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsemoveTo_drawTo_commandGroup();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = [];\n      s5 = peg$parsewsp();\n      while (s5 !== peg$FAILED) {\n        s4.push(s5);\n        s5 = peg$parsewsp();\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsemoveTo_drawTo_commandGroup();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = [];\n        s5 = peg$parsewsp();\n        while (s5 !== peg$FAILED) {\n          s4.push(s5);\n          s5 = peg$parsewsp();\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsemoveTo_drawTo_commandGroup();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsemoveTo_drawTo_commandGroup() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsemoveto();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = [];\n      s5 = peg$parsewsp();\n      while (s5 !== peg$FAILED) {\n        s4.push(s5);\n        s5 = peg$parsewsp();\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsedrawto_command();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = [];\n        s5 = peg$parsewsp();\n        while (s5 !== peg$FAILED) {\n          s4.push(s5);\n          s5 = peg$parsewsp();\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsedrawto_command();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsedrawto_command() {\n    var s0;\n    s0 = peg$parseclosepath();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parselineto();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsehorizontal_lineto();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parsevertical_lineto();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsecurveto();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parsesmooth_curveto();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsequadratic_bezier_curveto();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parsesmooth_quadratic_bezier_curveto();\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parseelliptical_arc();\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    return s0;\n  }\n  function peg$parsemoveto() {\n    var s0, s1, s2, s3, s4, s5, s6;\n    s0 = peg$currPos;\n    if (peg$c2.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c3);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$currPos;\n          s5 = peg$parsecomma_wsp();\n          if (s5 === peg$FAILED) {\n            s5 = null;\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parselineto_argument_sequence();\n            if (s6 !== peg$FAILED) {\n              s5 = [s5, s6];\n              s4 = s5;\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s4;\n            s4 = peg$FAILED;\n          }\n          if (s4 === peg$FAILED) {\n            s4 = null;\n          }\n          if (s4 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c4(s1, s3, s4);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseclosepath() {\n    var s0, s1;\n    s0 = peg$currPos;\n    if (peg$c5.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c6);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c7();\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parselineto() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c8.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c9);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parselineto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parselineto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsecoordinate_pair();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsecoordinate_pair();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsehorizontal_lineto() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c11.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c12);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c13(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecoordinate_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsenumber();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsenumber();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsenumber();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsevertical_lineto() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c14.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c15);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c16(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecurveto() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c17.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c18);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecurveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecurveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsecurveto_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsecurveto_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsecurveto_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecurveto_argument() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parsecomma_wsp();\n          if (s4 === peg$FAILED) {\n            s4 = null;\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsecoordinate_pair();\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c19(s1, s3, s5);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsesmooth_curveto() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c20.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c21);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsesmooth_curveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsesmooth_curveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsesmooth_curveto_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsesmooth_curveto_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsesmooth_curveto_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsesmooth_curveto_argument() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c22(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsequadratic_bezier_curveto() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c23.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c24);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsequadratic_bezier_curveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsequadratic_bezier_curveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsequadratic_bezier_curveto_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsequadratic_bezier_curveto_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsequadratic_bezier_curveto_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsequadratic_bezier_curveto_argument() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c25(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsesmooth_quadratic_bezier_curveto() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c26.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c27);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsesmooth_quadratic_bezier_curveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsesmooth_quadratic_bezier_curveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsecoordinate_pair();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsecoordinate_pair();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseelliptical_arc() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    if (peg$c28.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c29);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseelliptical_arc_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseelliptical_arc_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n    s0 = peg$currPos;\n    s1 = peg$parseelliptical_arc_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parseelliptical_arc_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parseelliptical_arc_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parseelliptical_arc_argument() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11;\n    s0 = peg$currPos;\n    s1 = peg$parsenonnegative_number();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsenonnegative_number();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parsecomma_wsp();\n          if (s4 === peg$FAILED) {\n            s4 = null;\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecomma_wsp();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseflag();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parsecomma_wsp();\n                  if (s8 === peg$FAILED) {\n                    s8 = null;\n                  }\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parseflag();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parsecomma_wsp();\n                      if (s10 === peg$FAILED) {\n                        s10 = null;\n                      }\n                      if (s10 !== peg$FAILED) {\n                        s11 = peg$parsecoordinate_pair();\n                        if (s11 !== peg$FAILED) {\n                          peg$savedPos = s0;\n                          s1 = peg$c30(s1, s3, s5, s7, s9, s11);\n                          s0 = s1;\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsecoordinate_pair() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$parsenumber();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsenumber();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c31(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    return s0;\n  }\n  function peg$parsenonnegative_number() {\n    var s0, s1;\n    s0 = peg$currPos;\n    s1 = peg$parsefloating_point_constant();\n    if (s1 === peg$FAILED) {\n      s1 = peg$parsedigit_sequence();\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c32(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsenumber() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsesign();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsefloating_point_constant();\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 === peg$FAILED) {\n      s1 = peg$currPos;\n      s2 = peg$parsesign();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsedigit_sequence();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c33(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parseflag() {\n    var s0, s1;\n    s0 = peg$currPos;\n    if (peg$c34.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c35);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c36(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsecomma_wsp() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsewsp();\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          s1 = [s1, s2, s3];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsecomma();\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c37();\n      }\n      s0 = s1;\n    }\n    return s0;\n  }\n  function peg$parsecomma() {\n    var s0;\n    if (input.charCodeAt(peg$currPos) === 44) {\n      s0 = peg$c38;\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c39);\n      }\n    }\n    return s0;\n  }\n  function peg$parsefloating_point_constant() {\n    var s0, s1, s2, s3;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsefractional_constant();\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parseexponent();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 === peg$FAILED) {\n      s1 = peg$currPos;\n      s2 = peg$parsedigit_sequence();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseexponent();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c40(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsefractional_constant() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsedigit_sequence();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    if (s2 !== peg$FAILED) {\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s3 = peg$c41;\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) {\n          peg$fail(peg$c42);\n        }\n      }\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsedigit_sequence();\n        if (s4 !== peg$FAILED) {\n          s2 = [s2, s3, s4];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 === peg$FAILED) {\n      s1 = peg$currPos;\n      s2 = peg$parsedigit_sequence();\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s3 = peg$c41;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c42);\n          }\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c40(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parseexponent() {\n    var s0, s1, s2, s3, s4;\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    if (peg$c43.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c44);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsesign();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsedigit_sequence();\n        if (s4 !== peg$FAILED) {\n          s2 = [s2, s3, s4];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c40(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsesign() {\n    var s0;\n    if (peg$c45.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c46);\n      }\n    }\n    return s0;\n  }\n  function peg$parsedigit_sequence() {\n    var s0, s1, s2;\n    s0 = peg$currPos;\n    s1 = [];\n    if (peg$c47.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c48);\n      }\n    }\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        if (peg$c47.test(input.charAt(peg$currPos))) {\n          s2 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) {\n            peg$fail(peg$c48);\n          }\n        }\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c49(s1);\n    }\n    s0 = s1;\n    return s0;\n  }\n  function peg$parsewsp() {\n    var s0, s1;\n    s0 = peg$currPos;\n    if (peg$c50.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) {\n        peg$fail(peg$c51);\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c37();\n    }\n    s0 = s1;\n    return s0;\n  }\n  function merge(first, more) {\n    if (!more) return [first];\n    for (var a = [first], i = 0, l = more.length; i < l; i++) a[i + 1] = more[i][1];\n    return a;\n  }\n  var cmds = {\n    m: 'moveto',\n    l: 'lineto',\n    h: 'horizontal lineto',\n    v: 'vertical lineto',\n    c: 'curveto',\n    s: 'smooth curveto',\n    q: 'quadratic curveto',\n    t: 'smooth quadratic curveto',\n    a: 'elliptical arc',\n    z: 'closepath'\n  };\n  for (var code in cmds) cmds[code.toUpperCase()] = cmds[code];\n  function commands(code, args) {\n    if (!args) args = [{}];\n    for (var i = args.length; i--;) {\n      var cmd = {\n        code: code,\n        command: cmds[code]\n      };\n      if (code == code.toLowerCase()) cmd.relative = true;\n      for (var k in args[i]) cmd[k] = args[i][k];\n      args[i] = cmd;\n    }\n    return args;\n  }\n  peg$result = peg$startRuleFunction();\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n    throw peg$buildStructuredError(peg$maxFailExpected, peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null, peg$maxFailPos < input.length ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1) : peg$computeLocation(peg$maxFailPos, peg$maxFailPos));\n  }\n}\nmodule.exports = {\n  SyntaxError: peg$SyntaxError,\n  parse: peg$parse\n};", "map": {"version": 3, "names": ["peg$subclass", "child", "parent", "ctor", "constructor", "prototype", "peg$SyntaxError", "message", "expected", "found", "location", "name", "Error", "captureStackTrace", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "class", "escapedParts", "i", "parts", "length", "Array", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "replace", "describeExpectation", "type", "describeExpected", "descriptions", "j", "sort", "slice", "join", "describeFound", "peg$parse", "input", "options", "peg$FAILED", "peg$startRuleFunctions", "svg_path", "peg$parsesvg_path", "peg$startRuleFunction", "peg$c0", "data", "cmds", "concat", "apply", "first", "code", "relative", "peg$c1", "more", "merge", "peg$c2", "peg$c3", "peg$classExpectation", "peg$c4", "c", "move", "commands", "peg$c5", "peg$c6", "peg$c7", "peg$c8", "peg$c9", "peg$c10", "args", "peg$c11", "peg$c12", "peg$c13", "map", "x", "peg$c14", "peg$c15", "peg$c16", "y", "peg$c17", "peg$c18", "peg$c19", "a", "b", "x1", "y1", "x2", "y2", "peg$c20", "peg$c21", "peg$c22", "peg$c23", "peg$c24", "peg$c25", "peg$c26", "peg$c27", "peg$c28", "peg$c29", "peg$c30", "rx", "ry", "xrot", "large", "sweep", "xy", "xAxisRotation", "largeArc", "peg$c31", "peg$c32", "n", "peg$c33", "peg$c34", "peg$c35", "peg$c36", "bit", "peg$c37", "peg$c38", "peg$c39", "peg$literalExpectation", "peg$c40", "peg$c41", "peg$c42", "peg$c43", "peg$c44", "peg$c45", "peg$c46", "peg$c47", "peg$c48", "peg$c49", "digits", "peg$c50", "peg$c51", "peg$currPos", "peg$savedPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$silentFails", "peg$result", "startRule", "substring", "peg$computeLocation", "peg$buildStructuredError", "peg$otherExpectation", "error", "peg$buildSimpleError", "ignoreCase", "peg$anyExpectation", "peg$endExpectation", "peg$computePosDetails", "pos", "details", "p", "startPos", "endPos", "startPosDetails", "endPosDetails", "start", "offset", "peg$fail", "push", "s0", "s1", "s2", "s3", "s4", "peg$parsewsp", "peg$parsemoveTo_drawTo_commandGroups", "s5", "peg$parsemoveTo_drawTo_commandGroup", "peg$parsemoveto", "peg$parsedrawto_command", "peg$parseclosepath", "peg$parselineto", "peg$parsehorizontal_lineto", "peg$parsevertical_lineto", "peg$parsecurveto", "peg$parsesmooth_curveto", "peg$parsequadratic_bezier_curveto", "peg$parsesmooth_quadratic_bezier_curveto", "peg$parseelliptical_arc", "s6", "test", "char<PERSON>t", "peg$parsecoordinate_pair", "peg$parsecomma_wsp", "peg$parselineto_argument_sequence", "peg$parsecoordinate_sequence", "peg$parsenumber", "peg$parsecurveto_argument_sequence", "peg$parsecurveto_argument", "peg$parsesmooth_curveto_argument_sequence", "peg$parsesmooth_curveto_argument", "peg$parsequadratic_bezier_curveto_argument_sequence", "peg$parsequadratic_bezier_curveto_argument", "peg$parsesmooth_quadratic_bezier_curveto_argument_sequence", "peg$parseelliptical_arc_argument_sequence", "peg$parseelliptical_arc_argument", "s7", "s8", "s9", "s10", "s11", "peg$parsenonnegative_number", "peg$parseflag", "peg$parsefloating_point_constant", "peg$parsedigit_sequence", "peg$parsesign", "peg$parsecomma", "peg$parsefractional_constant", "peg$parseexponent", "l", "m", "h", "v", "q", "t", "z", "cmd", "command", "toLowerCase", "k", "module", "exports", "SyntaxError", "parse"], "sources": ["/root/mes-system/mes-frontend/node_modules/svg-path-parser/parser.js"], "sourcesContent": ["/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n\n\"use strict\";\n\nfunction peg$subclass(child, parent) {\n  function ctor() { this.constructor = child; }\n  ctor.prototype = parent.prototype;\n  child.prototype = new ctor();\n}\n\nfunction peg$SyntaxError(message, expected, found, location) {\n  this.message  = message;\n  this.expected = expected;\n  this.found    = found;\n  this.location = location;\n  this.name     = \"SyntaxError\";\n\n  if (typeof Error.captureStackTrace === \"function\") {\n    Error.captureStackTrace(this, peg$SyntaxError);\n  }\n}\n\npeg$subclass(peg$SyntaxError, Error);\n\npeg$SyntaxError.buildMessage = function(expected, found) {\n  var DESCRIBE_EXPECTATION_FNS = {\n        literal: function(expectation) {\n          return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n        },\n\n        \"class\": function(expectation) {\n          var escapedParts = \"\",\n              i;\n\n          for (i = 0; i < expectation.parts.length; i++) {\n            escapedParts += expectation.parts[i] instanceof Array\n              ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n              : classEscape(expectation.parts[i]);\n          }\n\n          return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n        },\n\n        any: function(expectation) {\n          return \"any character\";\n        },\n\n        end: function(expectation) {\n          return \"end of input\";\n        },\n\n        other: function(expectation) {\n          return expectation.description;\n        }\n      };\n\n  function hex(ch) {\n    return ch.charCodeAt(0).toString(16).toUpperCase();\n  }\n\n  function literalEscape(s) {\n    return s\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\"/g,  '\\\\\"')\n      .replace(/\\0/g, '\\\\0')\n      .replace(/\\t/g, '\\\\t')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n  }\n\n  function classEscape(s) {\n    return s\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\\]/g, '\\\\]')\n      .replace(/\\^/g, '\\\\^')\n      .replace(/-/g,  '\\\\-')\n      .replace(/\\0/g, '\\\\0')\n      .replace(/\\t/g, '\\\\t')\n      .replace(/\\n/g, '\\\\n')\n      .replace(/\\r/g, '\\\\r')\n      .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n      .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n  }\n\n  function describeExpectation(expectation) {\n    return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n  }\n\n  function describeExpected(expected) {\n    var descriptions = new Array(expected.length),\n        i, j;\n\n    for (i = 0; i < expected.length; i++) {\n      descriptions[i] = describeExpectation(expected[i]);\n    }\n\n    descriptions.sort();\n\n    if (descriptions.length > 0) {\n      for (i = 1, j = 1; i < descriptions.length; i++) {\n        if (descriptions[i - 1] !== descriptions[i]) {\n          descriptions[j] = descriptions[i];\n          j++;\n        }\n      }\n      descriptions.length = j;\n    }\n\n    switch (descriptions.length) {\n      case 1:\n        return descriptions[0];\n\n      case 2:\n        return descriptions[0] + \" or \" + descriptions[1];\n\n      default:\n        return descriptions.slice(0, -1).join(\", \")\n          + \", or \"\n          + descriptions[descriptions.length - 1];\n    }\n  }\n\n  function describeFound(found) {\n    return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n  }\n\n  return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n};\n\nfunction peg$parse(input, options) {\n  options = options !== void 0 ? options : {};\n\n  var peg$FAILED = {},\n\n      peg$startRuleFunctions = { svg_path: peg$parsesvg_path },\n      peg$startRuleFunction  = peg$parsesvg_path,\n\n      peg$c0 = function(data) {\n          if (!data) return [];\n          for (var cmds=[],i=0;i<data.length;i++) cmds=cmds.concat.apply(cmds,data[i]);\n          var first=cmds[0];\n          if (first && first.code=='m'){ // Per spec, first moveto is never relative\n            delete first.relative;\n            first.code = 'M';\n          }\n          return cmds;\n        },\n      peg$c1 = function(first, more) { return merge(first,more) },\n      peg$c2 = /^[Mm]/,\n      peg$c3 = peg$classExpectation([\"M\", \"m\"], false, false),\n      peg$c4 = function(c, first, more) {\n          var move = commands(c,[first]);\n          if (more) move = move.concat(commands(c=='M' ? 'L' : 'l',more[1]));\n          return move;\n        },\n      peg$c5 = /^[Zz]/,\n      peg$c6 = peg$classExpectation([\"Z\", \"z\"], false, false),\n      peg$c7 = function() { return commands('Z') },\n      peg$c8 = /^[Ll]/,\n      peg$c9 = peg$classExpectation([\"L\", \"l\"], false, false),\n      peg$c10 = function(c, args) { return commands(c,args) },\n      peg$c11 = /^[Hh]/,\n      peg$c12 = peg$classExpectation([\"H\", \"h\"], false, false),\n      peg$c13 = function(c, args) { return commands(c,args.map(function(x){ return {x:x}})) },\n      peg$c14 = /^[Vv]/,\n      peg$c15 = peg$classExpectation([\"V\", \"v\"], false, false),\n      peg$c16 = function(c, args) { return commands(c,args.map(function(y){ return {y:y}})) },\n      peg$c17 = /^[Cc]/,\n      peg$c18 = peg$classExpectation([\"C\", \"c\"], false, false),\n      peg$c19 = function(a, b, c) { return { x1:a.x, y1:a.y, x2:b.x, y2:b.y, x:c.x, y:c.y } },\n      peg$c20 = /^[Ss]/,\n      peg$c21 = peg$classExpectation([\"S\", \"s\"], false, false),\n      peg$c22 = function(b, c) { return { x2:b.x, y2:b.y, x:c.x, y:c.y } },\n      peg$c23 = /^[Qq]/,\n      peg$c24 = peg$classExpectation([\"Q\", \"q\"], false, false),\n      peg$c25 = function(a, b) { return { x1:a.x, y1:a.y, x:b.x, y:b.y } },\n      peg$c26 = /^[Tt]/,\n      peg$c27 = peg$classExpectation([\"T\", \"t\"], false, false),\n      peg$c28 = /^[Aa]/,\n      peg$c29 = peg$classExpectation([\"A\", \"a\"], false, false),\n      peg$c30 = function(rx, ry, xrot, large, sweep, xy) { return { rx:rx, ry:ry, xAxisRotation:xrot, largeArc:large, sweep:sweep, x:xy.x, y:xy.y } },\n      peg$c31 = function(x, y) { return { x:x, y:y } },\n      peg$c32 = function(n) { return n*1 },\n      peg$c33 = function(parts) { return parts.join('')*1 },\n      peg$c34 = /^[01]/,\n      peg$c35 = peg$classExpectation([\"0\", \"1\"], false, false),\n      peg$c36 = function(bit) { return bit=='1' },\n      peg$c37 = function() { return '' },\n      peg$c38 = \",\",\n      peg$c39 = peg$literalExpectation(\",\", false),\n      peg$c40 = function(parts) { return parts.join('') },\n      peg$c41 = \".\",\n      peg$c42 = peg$literalExpectation(\".\", false),\n      peg$c43 = /^[eE]/,\n      peg$c44 = peg$classExpectation([\"e\", \"E\"], false, false),\n      peg$c45 = /^[+\\-]/,\n      peg$c46 = peg$classExpectation([\"+\", \"-\"], false, false),\n      peg$c47 = /^[0-9]/,\n      peg$c48 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n      peg$c49 = function(digits) { return digits.join('') },\n      peg$c50 = /^[ \\t\\n\\r]/,\n      peg$c51 = peg$classExpectation([\" \", \"\\t\", \"\\n\", \"\\r\"], false, false),\n\n      peg$currPos          = 0,\n      peg$savedPos         = 0,\n      peg$posDetailsCache  = [{ line: 1, column: 1 }],\n      peg$maxFailPos       = 0,\n      peg$maxFailExpected  = [],\n      peg$silentFails      = 0,\n\n      peg$result;\n\n  if (\"startRule\" in options) {\n    if (!(options.startRule in peg$startRuleFunctions)) {\n      throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n    }\n\n    peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n  }\n\n  function text() {\n    return input.substring(peg$savedPos, peg$currPos);\n  }\n\n  function location() {\n    return peg$computeLocation(peg$savedPos, peg$currPos);\n  }\n\n  function expected(description, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n    throw peg$buildStructuredError(\n      [peg$otherExpectation(description)],\n      input.substring(peg$savedPos, peg$currPos),\n      location\n    );\n  }\n\n  function error(message, location) {\n    location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n    throw peg$buildSimpleError(message, location);\n  }\n\n  function peg$literalExpectation(text, ignoreCase) {\n    return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n  }\n\n  function peg$classExpectation(parts, inverted, ignoreCase) {\n    return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n  }\n\n  function peg$anyExpectation() {\n    return { type: \"any\" };\n  }\n\n  function peg$endExpectation() {\n    return { type: \"end\" };\n  }\n\n  function peg$otherExpectation(description) {\n    return { type: \"other\", description: description };\n  }\n\n  function peg$computePosDetails(pos) {\n    var details = peg$posDetailsCache[pos], p;\n\n    if (details) {\n      return details;\n    } else {\n      p = pos - 1;\n      while (!peg$posDetailsCache[p]) {\n        p--;\n      }\n\n      details = peg$posDetailsCache[p];\n      details = {\n        line:   details.line,\n        column: details.column\n      };\n\n      while (p < pos) {\n        if (input.charCodeAt(p) === 10) {\n          details.line++;\n          details.column = 1;\n        } else {\n          details.column++;\n        }\n\n        p++;\n      }\n\n      peg$posDetailsCache[pos] = details;\n      return details;\n    }\n  }\n\n  function peg$computeLocation(startPos, endPos) {\n    var startPosDetails = peg$computePosDetails(startPos),\n        endPosDetails   = peg$computePosDetails(endPos);\n\n    return {\n      start: {\n        offset: startPos,\n        line:   startPosDetails.line,\n        column: startPosDetails.column\n      },\n      end: {\n        offset: endPos,\n        line:   endPosDetails.line,\n        column: endPosDetails.column\n      }\n    };\n  }\n\n  function peg$fail(expected) {\n    if (peg$currPos < peg$maxFailPos) { return; }\n\n    if (peg$currPos > peg$maxFailPos) {\n      peg$maxFailPos = peg$currPos;\n      peg$maxFailExpected = [];\n    }\n\n    peg$maxFailExpected.push(expected);\n  }\n\n  function peg$buildSimpleError(message, location) {\n    return new peg$SyntaxError(message, null, null, location);\n  }\n\n  function peg$buildStructuredError(expected, found, location) {\n    return new peg$SyntaxError(\n      peg$SyntaxError.buildMessage(expected, found),\n      expected,\n      found,\n      location\n    );\n  }\n\n  function peg$parsesvg_path() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    while (s2 !== peg$FAILED) {\n      s1.push(s2);\n      s2 = peg$parsewsp();\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsemoveTo_drawTo_commandGroups();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c0(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsemoveTo_drawTo_commandGroups() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsemoveTo_drawTo_commandGroup();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = [];\n      s5 = peg$parsewsp();\n      while (s5 !== peg$FAILED) {\n        s4.push(s5);\n        s5 = peg$parsewsp();\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsemoveTo_drawTo_commandGroup();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = [];\n        s5 = peg$parsewsp();\n        while (s5 !== peg$FAILED) {\n          s4.push(s5);\n          s5 = peg$parsewsp();\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsemoveTo_drawTo_commandGroup();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsemoveTo_drawTo_commandGroup() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsemoveto();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = [];\n      s5 = peg$parsewsp();\n      while (s5 !== peg$FAILED) {\n        s4.push(s5);\n        s5 = peg$parsewsp();\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsedrawto_command();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = [];\n        s5 = peg$parsewsp();\n        while (s5 !== peg$FAILED) {\n          s4.push(s5);\n          s5 = peg$parsewsp();\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsedrawto_command();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsedrawto_command() {\n    var s0;\n\n    s0 = peg$parseclosepath();\n    if (s0 === peg$FAILED) {\n      s0 = peg$parselineto();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parsehorizontal_lineto();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parsevertical_lineto();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsecurveto();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parsesmooth_curveto();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsequadratic_bezier_curveto();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parsesmooth_quadratic_bezier_curveto();\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parseelliptical_arc();\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    return s0;\n  }\n\n  function peg$parsemoveto() {\n    var s0, s1, s2, s3, s4, s5, s6;\n\n    s0 = peg$currPos;\n    if (peg$c2.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c3); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$currPos;\n          s5 = peg$parsecomma_wsp();\n          if (s5 === peg$FAILED) {\n            s5 = null;\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parselineto_argument_sequence();\n            if (s6 !== peg$FAILED) {\n              s5 = [s5, s6];\n              s4 = s5;\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s4;\n            s4 = peg$FAILED;\n          }\n          if (s4 === peg$FAILED) {\n            s4 = null;\n          }\n          if (s4 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c4(s1, s3, s4);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parseclosepath() {\n    var s0, s1;\n\n    s0 = peg$currPos;\n    if (peg$c5.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c6); }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c7();\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parselineto() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c8.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c9); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parselineto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parselineto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsecoordinate_pair();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsecoordinate_pair();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsehorizontal_lineto() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c11.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c12); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c13(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecoordinate_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsenumber();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsenumber();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsenumber();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsevertical_lineto() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c14.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c15); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c16(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecurveto() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c17.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c18); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecurveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecurveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecurveto_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsecurveto_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsecurveto_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecurveto_argument() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parsecomma_wsp();\n          if (s4 === peg$FAILED) {\n            s4 = null;\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsecoordinate_pair();\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c19(s1, s3, s5);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsesmooth_curveto() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c20.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c21); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsesmooth_curveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsesmooth_curveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsesmooth_curveto_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsesmooth_curveto_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsesmooth_curveto_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsesmooth_curveto_argument() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c22(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsequadratic_bezier_curveto() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c23.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c24); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsequadratic_bezier_curveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsequadratic_bezier_curveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsequadratic_bezier_curveto_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsequadratic_bezier_curveto_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsequadratic_bezier_curveto_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsequadratic_bezier_curveto_argument() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsecoordinate_pair();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c25(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsesmooth_quadratic_bezier_curveto() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c26.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c27); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsesmooth_quadratic_bezier_curveto_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsesmooth_quadratic_bezier_curveto_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parsecoordinate_pair();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parsecoordinate_pair();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsecoordinate_pair();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parseelliptical_arc() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    if (peg$c28.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c29); }\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$parsewsp();\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$parsewsp();\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseelliptical_arc_argument_sequence();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c10(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parseelliptical_arc_argument_sequence() {\n    var s0, s1, s2, s3, s4, s5;\n\n    s0 = peg$currPos;\n    s1 = peg$parseelliptical_arc_argument();\n    if (s1 !== peg$FAILED) {\n      s2 = [];\n      s3 = peg$currPos;\n      s4 = peg$parsecomma_wsp();\n      if (s4 === peg$FAILED) {\n        s4 = null;\n      }\n      if (s4 !== peg$FAILED) {\n        s5 = peg$parseelliptical_arc_argument();\n        if (s5 !== peg$FAILED) {\n          s4 = [s4, s5];\n          s3 = s4;\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s3;\n        s3 = peg$FAILED;\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        s3 = peg$currPos;\n        s4 = peg$parsecomma_wsp();\n        if (s4 === peg$FAILED) {\n          s4 = null;\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parseelliptical_arc_argument();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c1(s1, s2);\n        s0 = s1;\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parseelliptical_arc_argument() {\n    var s0, s1, s2, s3, s4, s5, s6, s7, s8, s9, s10, s11;\n\n    s0 = peg$currPos;\n    s1 = peg$parsenonnegative_number();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsenonnegative_number();\n        if (s3 !== peg$FAILED) {\n          s4 = peg$parsecomma_wsp();\n          if (s4 === peg$FAILED) {\n            s4 = null;\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsenumber();\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parsecomma_wsp();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseflag();\n                if (s7 !== peg$FAILED) {\n                  s8 = peg$parsecomma_wsp();\n                  if (s8 === peg$FAILED) {\n                    s8 = null;\n                  }\n                  if (s8 !== peg$FAILED) {\n                    s9 = peg$parseflag();\n                    if (s9 !== peg$FAILED) {\n                      s10 = peg$parsecomma_wsp();\n                      if (s10 === peg$FAILED) {\n                        s10 = null;\n                      }\n                      if (s10 !== peg$FAILED) {\n                        s11 = peg$parsecoordinate_pair();\n                        if (s11 !== peg$FAILED) {\n                          peg$savedPos = s0;\n                          s1 = peg$c30(s1, s3, s5, s7, s9, s11);\n                          s0 = s1;\n                        } else {\n                          peg$currPos = s0;\n                          s0 = peg$FAILED;\n                        }\n                      } else {\n                        peg$currPos = s0;\n                        s0 = peg$FAILED;\n                      }\n                    } else {\n                      peg$currPos = s0;\n                      s0 = peg$FAILED;\n                    }\n                  } else {\n                    peg$currPos = s0;\n                    s0 = peg$FAILED;\n                  }\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecoordinate_pair() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$parsenumber();\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma_wsp();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsenumber();\n        if (s3 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c31(s1, s3);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n\n    return s0;\n  }\n\n  function peg$parsenonnegative_number() {\n    var s0, s1;\n\n    s0 = peg$currPos;\n    s1 = peg$parsefloating_point_constant();\n    if (s1 === peg$FAILED) {\n      s1 = peg$parsedigit_sequence();\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c32(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsenumber() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsesign();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsefloating_point_constant();\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 === peg$FAILED) {\n      s1 = peg$currPos;\n      s2 = peg$parsesign();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parsedigit_sequence();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c33(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parseflag() {\n    var s0, s1;\n\n    s0 = peg$currPos;\n    if (peg$c34.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c35); }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c36(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsecomma_wsp() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = [];\n    s2 = peg$parsewsp();\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        s2 = peg$parsewsp();\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      s2 = peg$parsecomma();\n      if (s2 === peg$FAILED) {\n        s2 = null;\n      }\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          s1 = [s1, s2, s3];\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s0;\n      s0 = peg$FAILED;\n    }\n    if (s0 === peg$FAILED) {\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = peg$parsecomma();\n      if (s2 !== peg$FAILED) {\n        s3 = [];\n        s4 = peg$parsewsp();\n        while (s4 !== peg$FAILED) {\n          s3.push(s4);\n          s4 = peg$parsewsp();\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c37();\n      }\n      s0 = s1;\n    }\n\n    return s0;\n  }\n\n  function peg$parsecomma() {\n    var s0;\n\n    if (input.charCodeAt(peg$currPos) === 44) {\n      s0 = peg$c38;\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c39); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsefloating_point_constant() {\n    var s0, s1, s2, s3;\n\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsefractional_constant();\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parseexponent();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s2 = [s2, s3];\n        s1 = s2;\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 === peg$FAILED) {\n      s1 = peg$currPos;\n      s2 = peg$parsedigit_sequence();\n      if (s2 !== peg$FAILED) {\n        s3 = peg$parseexponent();\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c40(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsefractional_constant() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    s2 = peg$parsedigit_sequence();\n    if (s2 === peg$FAILED) {\n      s2 = null;\n    }\n    if (s2 !== peg$FAILED) {\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s3 = peg$c41;\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c42); }\n      }\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsedigit_sequence();\n        if (s4 !== peg$FAILED) {\n          s2 = [s2, s3, s4];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 === peg$FAILED) {\n      s1 = peg$currPos;\n      s2 = peg$parsedigit_sequence();\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s3 = peg$c41;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c42); }\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c40(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parseexponent() {\n    var s0, s1, s2, s3, s4;\n\n    s0 = peg$currPos;\n    s1 = peg$currPos;\n    if (peg$c43.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c44); }\n    }\n    if (s2 !== peg$FAILED) {\n      s3 = peg$parsesign();\n      if (s3 === peg$FAILED) {\n        s3 = null;\n      }\n      if (s3 !== peg$FAILED) {\n        s4 = peg$parsedigit_sequence();\n        if (s4 !== peg$FAILED) {\n          s2 = [s2, s3, s4];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n    } else {\n      peg$currPos = s1;\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c40(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsesign() {\n    var s0;\n\n    if (peg$c45.test(input.charAt(peg$currPos))) {\n      s0 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s0 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c46); }\n    }\n\n    return s0;\n  }\n\n  function peg$parsedigit_sequence() {\n    var s0, s1, s2;\n\n    s0 = peg$currPos;\n    s1 = [];\n    if (peg$c47.test(input.charAt(peg$currPos))) {\n      s2 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s2 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c48); }\n    }\n    if (s2 !== peg$FAILED) {\n      while (s2 !== peg$FAILED) {\n        s1.push(s2);\n        if (peg$c47.test(input.charAt(peg$currPos))) {\n          s2 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c48); }\n        }\n      }\n    } else {\n      s1 = peg$FAILED;\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c49(s1);\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n  function peg$parsewsp() {\n    var s0, s1;\n\n    s0 = peg$currPos;\n    if (peg$c50.test(input.charAt(peg$currPos))) {\n      s1 = input.charAt(peg$currPos);\n      peg$currPos++;\n    } else {\n      s1 = peg$FAILED;\n      if (peg$silentFails === 0) { peg$fail(peg$c51); }\n    }\n    if (s1 !== peg$FAILED) {\n      peg$savedPos = s0;\n      s1 = peg$c37();\n    }\n    s0 = s1;\n\n    return s0;\n  }\n\n\n    function merge(first,more){\n      if (!more) return [first];\n      for (var a=[first],i=0,l=more.length;i<l;i++) a[i+1]=more[i][1];\n      return a;\n    }\n\n    var cmds = {m:'moveto',l:'lineto',h:'horizontal lineto',v:'vertical lineto',c:'curveto',s:'smooth curveto',q:'quadratic curveto',t:'smooth quadratic curveto',a:'elliptical arc',z:'closepath'};\n    for (var code in cmds) cmds[code.toUpperCase()]=cmds[code];\n    function commands(code,args){\n      if (!args) args=[{}];\n      for (var i=args.length;i--;){\n        var cmd={code:code,command:cmds[code]};\n        if (code==code.toLowerCase()) cmd.relative=true;\n        for (var k in args[i]) cmd[k]=args[i][k];\n        args[i] = cmd;\n      }\n      return args;\n    }\n\n\n  peg$result = peg$startRuleFunction();\n\n  if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n    return peg$result;\n  } else {\n    if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n      peg$fail(peg$endExpectation());\n    }\n\n    throw peg$buildStructuredError(\n      peg$maxFailExpected,\n      peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n      peg$maxFailPos < input.length\n        ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n        : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n    );\n  }\n}\n\nmodule.exports = {\n  SyntaxError: peg$SyntaxError,\n  parse:       peg$parse\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,SAASA,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACnC,SAASC,IAAIA,CAAA,EAAG;IAAE,IAAI,CAACC,WAAW,GAAGH,KAAK;EAAE;EAC5CE,IAAI,CAACE,SAAS,GAAGH,MAAM,CAACG,SAAS;EACjCJ,KAAK,CAACI,SAAS,GAAG,IAAIF,IAAI,CAAC,CAAC;AAC9B;AAEA,SAASG,eAAeA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EAC3D,IAAI,CAACH,OAAO,GAAIA,OAAO;EACvB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,KAAK,GAAMA,KAAK;EACrB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EACxB,IAAI,CAACC,IAAI,GAAO,aAAa;EAE7B,IAAI,OAAOC,KAAK,CAACC,iBAAiB,KAAK,UAAU,EAAE;IACjDD,KAAK,CAACC,iBAAiB,CAAC,IAAI,EAAEP,eAAe,CAAC;EAChD;AACF;AAEAN,YAAY,CAACM,eAAe,EAAEM,KAAK,CAAC;AAEpCN,eAAe,CAACQ,YAAY,GAAG,UAASN,QAAQ,EAAEC,KAAK,EAAE;EACvD,IAAIM,wBAAwB,GAAG;IACzBC,OAAO,EAAE,SAAAA,CAASC,WAAW,EAAE;MAC7B,OAAO,IAAI,GAAGC,aAAa,CAACD,WAAW,CAACE,IAAI,CAAC,GAAG,IAAI;IACtD,CAAC;IAED,OAAO,EAAE,SAAAC,CAASH,WAAW,EAAE;MAC7B,IAAII,YAAY,GAAG,EAAE;QACjBC,CAAC;MAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,WAAW,CAACM,KAAK,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC7CD,YAAY,IAAIJ,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,YAAYG,KAAK,GACjDC,WAAW,CAACT,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGI,WAAW,CAACT,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACjFI,WAAW,CAACT,WAAW,CAACM,KAAK,CAACD,CAAC,CAAC,CAAC;MACvC;MAEA,OAAO,GAAG,IAAIL,WAAW,CAACU,QAAQ,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGN,YAAY,GAAG,GAAG;IACrE,CAAC;IAEDO,GAAG,EAAE,SAAAA,CAASX,WAAW,EAAE;MACzB,OAAO,eAAe;IACxB,CAAC;IAEDY,GAAG,EAAE,SAAAA,CAASZ,WAAW,EAAE;MACzB,OAAO,cAAc;IACvB,CAAC;IAEDa,KAAK,EAAE,SAAAA,CAASb,WAAW,EAAE;MAC3B,OAAOA,WAAW,CAACc,WAAW;IAChC;EACF,CAAC;EAEL,SAASC,GAAGA,CAACC,EAAE,EAAE;IACf,OAAOA,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EACpD;EAEA,SAASlB,aAAaA,CAACmB,CAAC,EAAE;IACxB,OAAOA,CAAC,CACLC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAG,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,cAAc,EAAW,UAASL,EAAE,EAAE;MAAE,OAAO,MAAM,GAAGD,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC,CAC3EK,OAAO,CAAC,uBAAuB,EAAE,UAASL,EAAE,EAAE;MAAE,OAAO,KAAK,GAAID,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC;EAChF;EAEA,SAASP,WAAWA,CAACW,CAAC,EAAE;IACtB,OAAOA,CAAC,CACLC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CACtBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAG,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CACrBA,OAAO,CAAC,cAAc,EAAW,UAASL,EAAE,EAAE;MAAE,OAAO,MAAM,GAAGD,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC,CAC3EK,OAAO,CAAC,uBAAuB,EAAE,UAASL,EAAE,EAAE;MAAE,OAAO,KAAK,GAAID,GAAG,CAACC,EAAE,CAAC;IAAE,CAAC,CAAC;EAChF;EAEA,SAASM,mBAAmBA,CAACtB,WAAW,EAAE;IACxC,OAAOF,wBAAwB,CAACE,WAAW,CAACuB,IAAI,CAAC,CAACvB,WAAW,CAAC;EAChE;EAEA,SAASwB,gBAAgBA,CAACjC,QAAQ,EAAE;IAClC,IAAIkC,YAAY,GAAG,IAAIjB,KAAK,CAACjB,QAAQ,CAACgB,MAAM,CAAC;MACzCF,CAAC;MAAEqB,CAAC;IAER,KAAKrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,QAAQ,CAACgB,MAAM,EAAEF,CAAC,EAAE,EAAE;MACpCoB,YAAY,CAACpB,CAAC,CAAC,GAAGiB,mBAAmB,CAAC/B,QAAQ,CAACc,CAAC,CAAC,CAAC;IACpD;IAEAoB,YAAY,CAACE,IAAI,CAAC,CAAC;IAEnB,IAAIF,YAAY,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC3B,KAAKF,CAAC,GAAG,CAAC,EAAEqB,CAAC,GAAG,CAAC,EAAErB,CAAC,GAAGoB,YAAY,CAAClB,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC/C,IAAIoB,YAAY,CAACpB,CAAC,GAAG,CAAC,CAAC,KAAKoB,YAAY,CAACpB,CAAC,CAAC,EAAE;UAC3CoB,YAAY,CAACC,CAAC,CAAC,GAAGD,YAAY,CAACpB,CAAC,CAAC;UACjCqB,CAAC,EAAE;QACL;MACF;MACAD,YAAY,CAAClB,MAAM,GAAGmB,CAAC;IACzB;IAEA,QAAQD,YAAY,CAAClB,MAAM;MACzB,KAAK,CAAC;QACJ,OAAOkB,YAAY,CAAC,CAAC,CAAC;MAExB,KAAK,CAAC;QACJ,OAAOA,YAAY,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,YAAY,CAAC,CAAC,CAAC;MAEnD;QACE,OAAOA,YAAY,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,GACvC,OAAO,GACPJ,YAAY,CAACA,YAAY,CAAClB,MAAM,GAAG,CAAC,CAAC;IAC7C;EACF;EAEA,SAASuB,aAAaA,CAACtC,KAAK,EAAE;IAC5B,OAAOA,KAAK,GAAG,IAAI,GAAGS,aAAa,CAACT,KAAK,CAAC,GAAG,IAAI,GAAG,cAAc;EACpE;EAEA,OAAO,WAAW,GAAGgC,gBAAgB,CAACjC,QAAQ,CAAC,GAAG,OAAO,GAAGuC,aAAa,CAACtC,KAAK,CAAC,GAAG,SAAS;AAC9F,CAAC;AAED,SAASuC,SAASA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACjCA,OAAO,GAAGA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC,CAAC;EAE3C,IAAIC,UAAU,GAAG,CAAC,CAAC;IAEfC,sBAAsB,GAAG;MAAEC,QAAQ,EAAEC;IAAkB,CAAC;IACxDC,qBAAqB,GAAID,iBAAiB;IAE1CE,MAAM,GAAG,SAAAA,CAASC,IAAI,EAAE;MACpB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;MACpB,KAAK,IAAIC,IAAI,GAAC,EAAE,EAACpC,CAAC,GAAC,CAAC,EAACA,CAAC,GAACmC,IAAI,CAACjC,MAAM,EAACF,CAAC,EAAE,EAAEoC,IAAI,GAACA,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,IAAI,EAACD,IAAI,CAACnC,CAAC,CAAC,CAAC;MAC5E,IAAIuC,KAAK,GAACH,IAAI,CAAC,CAAC,CAAC;MACjB,IAAIG,KAAK,IAAIA,KAAK,CAACC,IAAI,IAAE,GAAG,EAAC;QAAE;QAC7B,OAAOD,KAAK,CAACE,QAAQ;QACrBF,KAAK,CAACC,IAAI,GAAG,GAAG;MAClB;MACA,OAAOJ,IAAI;IACb,CAAC;IACHM,MAAM,GAAG,SAAAA,CAASH,KAAK,EAAEI,IAAI,EAAE;MAAE,OAAOC,KAAK,CAACL,KAAK,EAACI,IAAI,CAAC;IAAC,CAAC;IAC3DE,MAAM,GAAG,OAAO;IAChBC,MAAM,GAAGC,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACvDC,MAAM,GAAG,SAAAA,CAASC,CAAC,EAAEV,KAAK,EAAEI,IAAI,EAAE;MAC9B,IAAIO,IAAI,GAAGC,QAAQ,CAACF,CAAC,EAAC,CAACV,KAAK,CAAC,CAAC;MAC9B,IAAII,IAAI,EAAEO,IAAI,GAAGA,IAAI,CAACb,MAAM,CAACc,QAAQ,CAACF,CAAC,IAAE,GAAG,GAAG,GAAG,GAAG,GAAG,EAACN,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAClE,OAAOO,IAAI;IACb,CAAC;IACHE,MAAM,GAAG,OAAO;IAChBC,MAAM,GAAGN,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACvDO,MAAM,GAAG,SAAAA,CAAA,EAAW;MAAE,OAAOH,QAAQ,CAAC,GAAG,CAAC;IAAC,CAAC;IAC5CI,MAAM,GAAG,OAAO;IAChBC,MAAM,GAAGT,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACvDU,OAAO,GAAG,SAAAA,CAASR,CAAC,EAAES,IAAI,EAAE;MAAE,OAAOP,QAAQ,CAACF,CAAC,EAACS,IAAI,CAAC;IAAC,CAAC;IACvDC,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGb,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDc,OAAO,GAAG,SAAAA,CAASZ,CAAC,EAAES,IAAI,EAAE;MAAE,OAAOP,QAAQ,CAACF,CAAC,EAACS,IAAI,CAACI,GAAG,CAAC,UAASC,CAAC,EAAC;QAAE,OAAO;UAACA,CAAC,EAACA;QAAC,CAAC;MAAA,CAAC,CAAC,CAAC;IAAC,CAAC;IACvFC,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGlB,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDmB,OAAO,GAAG,SAAAA,CAASjB,CAAC,EAAES,IAAI,EAAE;MAAE,OAAOP,QAAQ,CAACF,CAAC,EAACS,IAAI,CAACI,GAAG,CAAC,UAASK,CAAC,EAAC;QAAE,OAAO;UAACA,CAAC,EAACA;QAAC,CAAC;MAAA,CAAC,CAAC,CAAC;IAAC,CAAC;IACvFC,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGtB,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDuB,OAAO,GAAG,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAEvB,CAAC,EAAE;MAAE,OAAO;QAAEwB,EAAE,EAACF,CAAC,CAACR,CAAC;QAAEW,EAAE,EAACH,CAAC,CAACJ,CAAC;QAAEQ,EAAE,EAACH,CAAC,CAACT,CAAC;QAAEa,EAAE,EAACJ,CAAC,CAACL,CAAC;QAAEJ,CAAC,EAACd,CAAC,CAACc,CAAC;QAAEI,CAAC,EAAClB,CAAC,CAACkB;MAAE,CAAC;IAAC,CAAC;IACvFU,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAG/B,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDgC,OAAO,GAAG,SAAAA,CAASP,CAAC,EAAEvB,CAAC,EAAE;MAAE,OAAO;QAAE0B,EAAE,EAACH,CAAC,CAACT,CAAC;QAAEa,EAAE,EAACJ,CAAC,CAACL,CAAC;QAAEJ,CAAC,EAACd,CAAC,CAACc,CAAC;QAAEI,CAAC,EAAClB,CAAC,CAACkB;MAAE,CAAC;IAAC,CAAC;IACpEa,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGlC,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDmC,OAAO,GAAG,SAAAA,CAASX,CAAC,EAAEC,CAAC,EAAE;MAAE,OAAO;QAAEC,EAAE,EAACF,CAAC,CAACR,CAAC;QAAEW,EAAE,EAACH,CAAC,CAACJ,CAAC;QAAEJ,CAAC,EAACS,CAAC,CAACT,CAAC;QAAEI,CAAC,EAACK,CAAC,CAACL;MAAE,CAAC;IAAC,CAAC;IACpEgB,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGrC,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDsC,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGvC,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDwC,OAAO,GAAG,SAAAA,CAASC,EAAE,EAAEC,EAAE,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAE;MAAE,OAAO;QAAEL,EAAE,EAACA,EAAE;QAAEC,EAAE,EAACA,EAAE;QAAEK,aAAa,EAACJ,IAAI;QAAEK,QAAQ,EAACJ,KAAK;QAAEC,KAAK,EAACA,KAAK;QAAE7B,CAAC,EAAC8B,EAAE,CAAC9B,CAAC;QAAEI,CAAC,EAAC0B,EAAE,CAAC1B;MAAE,CAAC;IAAC,CAAC;IAC/I6B,OAAO,GAAG,SAAAA,CAASjC,CAAC,EAAEI,CAAC,EAAE;MAAE,OAAO;QAAEJ,CAAC,EAACA,CAAC;QAAEI,CAAC,EAACA;MAAE,CAAC;IAAC,CAAC;IAChD8B,OAAO,GAAG,SAAAA,CAASC,CAAC,EAAE;MAAE,OAAOA,CAAC,GAAC,CAAC;IAAC,CAAC;IACpCC,OAAO,GAAG,SAAAA,CAASlG,KAAK,EAAE;MAAE,OAAOA,KAAK,CAACuB,IAAI,CAAC,EAAE,CAAC,GAAC,CAAC;IAAC,CAAC;IACrD4E,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGtD,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDuD,OAAO,GAAG,SAAAA,CAASC,GAAG,EAAE;MAAE,OAAOA,GAAG,IAAE,GAAG;IAAC,CAAC;IAC3CC,OAAO,GAAG,SAAAA,CAAA,EAAW;MAAE,OAAO,EAAE;IAAC,CAAC;IAClCC,OAAO,GAAG,GAAG;IACbC,OAAO,GAAGC,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;IAC5CC,OAAO,GAAG,SAAAA,CAAS3G,KAAK,EAAE;MAAE,OAAOA,KAAK,CAACuB,IAAI,CAAC,EAAE,CAAC;IAAC,CAAC;IACnDqF,OAAO,GAAG,GAAG;IACbC,OAAO,GAAGH,sBAAsB,CAAC,GAAG,EAAE,KAAK,CAAC;IAC5CI,OAAO,GAAG,OAAO;IACjBC,OAAO,GAAGjE,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDkE,OAAO,GAAG,QAAQ;IAClBC,OAAO,GAAGnE,oBAAoB,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IACxDoE,OAAO,GAAG,QAAQ;IAClBC,OAAO,GAAGrE,oBAAoB,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAC1DsE,OAAO,GAAG,SAAAA,CAASC,MAAM,EAAE;MAAE,OAAOA,MAAM,CAAC9F,IAAI,CAAC,EAAE,CAAC;IAAC,CAAC;IACrD+F,OAAO,GAAG,YAAY;IACtBC,OAAO,GAAGzE,oBAAoB,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAErE0E,WAAW,GAAY,CAAC;IACxBC,YAAY,GAAW,CAAC;IACxBC,mBAAmB,GAAI,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC,CAAC;IAC/CC,cAAc,GAAS,CAAC;IACxBC,mBAAmB,GAAI,EAAE;IACzBC,eAAe,GAAQ,CAAC;IAExBC,UAAU;EAEd,IAAI,WAAW,IAAIrG,OAAO,EAAE;IAC1B,IAAI,EAAEA,OAAO,CAACsG,SAAS,IAAIpG,sBAAsB,CAAC,EAAE;MAClD,MAAM,IAAIxC,KAAK,CAAC,kCAAkC,GAAGsC,OAAO,CAACsG,SAAS,GAAG,KAAK,CAAC;IACjF;IAEAjG,qBAAqB,GAAGH,sBAAsB,CAACF,OAAO,CAACsG,SAAS,CAAC;EACnE;EAEA,SAASrI,IAAIA,CAAA,EAAG;IACd,OAAO8B,KAAK,CAACwG,SAAS,CAACT,YAAY,EAAED,WAAW,CAAC;EACnD;EAEA,SAASrI,QAAQA,CAAA,EAAG;IAClB,OAAOgJ,mBAAmB,CAACV,YAAY,EAAED,WAAW,CAAC;EACvD;EAEA,SAASvI,QAAQA,CAACuB,WAAW,EAAErB,QAAQ,EAAE;IACvCA,QAAQ,GAAGA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGgJ,mBAAmB,CAACV,YAAY,EAAED,WAAW,CAAC;IAE1F,MAAMY,wBAAwB,CAC5B,CAACC,oBAAoB,CAAC7H,WAAW,CAAC,CAAC,EACnCkB,KAAK,CAACwG,SAAS,CAACT,YAAY,EAAED,WAAW,CAAC,EAC1CrI,QACF,CAAC;EACH;EAEA,SAASmJ,KAAKA,CAACtJ,OAAO,EAAEG,QAAQ,EAAE;IAChCA,QAAQ,GAAGA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGgJ,mBAAmB,CAACV,YAAY,EAAED,WAAW,CAAC;IAE1F,MAAMe,oBAAoB,CAACvJ,OAAO,EAAEG,QAAQ,CAAC;EAC/C;EAEA,SAASuH,sBAAsBA,CAAC9G,IAAI,EAAE4I,UAAU,EAAE;IAChD,OAAO;MAAEvH,IAAI,EAAE,SAAS;MAAErB,IAAI,EAAEA,IAAI;MAAE4I,UAAU,EAAEA;IAAW,CAAC;EAChE;EAEA,SAAS1F,oBAAoBA,CAAC9C,KAAK,EAAEI,QAAQ,EAAEoI,UAAU,EAAE;IACzD,OAAO;MAAEvH,IAAI,EAAE,OAAO;MAAEjB,KAAK,EAAEA,KAAK;MAAEI,QAAQ,EAAEA,QAAQ;MAAEoI,UAAU,EAAEA;IAAW,CAAC;EACpF;EAEA,SAASC,kBAAkBA,CAAA,EAAG;IAC5B,OAAO;MAAExH,IAAI,EAAE;IAAM,CAAC;EACxB;EAEA,SAASyH,kBAAkBA,CAAA,EAAG;IAC5B,OAAO;MAAEzH,IAAI,EAAE;IAAM,CAAC;EACxB;EAEA,SAASoH,oBAAoBA,CAAC7H,WAAW,EAAE;IACzC,OAAO;MAAES,IAAI,EAAE,OAAO;MAAET,WAAW,EAAEA;IAAY,CAAC;EACpD;EAEA,SAASmI,qBAAqBA,CAACC,GAAG,EAAE;IAClC,IAAIC,OAAO,GAAGnB,mBAAmB,CAACkB,GAAG,CAAC;MAAEE,CAAC;IAEzC,IAAID,OAAO,EAAE;MACX,OAAOA,OAAO;IAChB,CAAC,MAAM;MACLC,CAAC,GAAGF,GAAG,GAAG,CAAC;MACX,OAAO,CAAClB,mBAAmB,CAACoB,CAAC,CAAC,EAAE;QAC9BA,CAAC,EAAE;MACL;MAEAD,OAAO,GAAGnB,mBAAmB,CAACoB,CAAC,CAAC;MAChCD,OAAO,GAAG;QACRlB,IAAI,EAAIkB,OAAO,CAAClB,IAAI;QACpBC,MAAM,EAAEiB,OAAO,CAACjB;MAClB,CAAC;MAED,OAAOkB,CAAC,GAAGF,GAAG,EAAE;QACd,IAAIlH,KAAK,CAACf,UAAU,CAACmI,CAAC,CAAC,KAAK,EAAE,EAAE;UAC9BD,OAAO,CAAClB,IAAI,EAAE;UACdkB,OAAO,CAACjB,MAAM,GAAG,CAAC;QACpB,CAAC,MAAM;UACLiB,OAAO,CAACjB,MAAM,EAAE;QAClB;QAEAkB,CAAC,EAAE;MACL;MAEApB,mBAAmB,CAACkB,GAAG,CAAC,GAAGC,OAAO;MAClC,OAAOA,OAAO;IAChB;EACF;EAEA,SAASV,mBAAmBA,CAACY,QAAQ,EAAEC,MAAM,EAAE;IAC7C,IAAIC,eAAe,GAAGN,qBAAqB,CAACI,QAAQ,CAAC;MACjDG,aAAa,GAAKP,qBAAqB,CAACK,MAAM,CAAC;IAEnD,OAAO;MACLG,KAAK,EAAE;QACLC,MAAM,EAAEL,QAAQ;QAChBpB,IAAI,EAAIsB,eAAe,CAACtB,IAAI;QAC5BC,MAAM,EAAEqB,eAAe,CAACrB;MAC1B,CAAC;MACDtH,GAAG,EAAE;QACH8I,MAAM,EAAEJ,MAAM;QACdrB,IAAI,EAAIuB,aAAa,CAACvB,IAAI;QAC1BC,MAAM,EAAEsB,aAAa,CAACtB;MACxB;IACF,CAAC;EACH;EAEA,SAASyB,QAAQA,CAACpK,QAAQ,EAAE;IAC1B,IAAIuI,WAAW,GAAGK,cAAc,EAAE;MAAE;IAAQ;IAE5C,IAAIL,WAAW,GAAGK,cAAc,EAAE;MAChCA,cAAc,GAAGL,WAAW;MAC5BM,mBAAmB,GAAG,EAAE;IAC1B;IAEAA,mBAAmB,CAACwB,IAAI,CAACrK,QAAQ,CAAC;EACpC;EAEA,SAASsJ,oBAAoBA,CAACvJ,OAAO,EAAEG,QAAQ,EAAE;IAC/C,OAAO,IAAIJ,eAAe,CAACC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAEG,QAAQ,CAAC;EAC3D;EAEA,SAASiJ,wBAAwBA,CAACnJ,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IAC3D,OAAO,IAAIJ,eAAe,CACxBA,eAAe,CAACQ,YAAY,CAACN,QAAQ,EAAEC,KAAK,CAAC,EAC7CD,QAAQ,EACRC,KAAK,EACLC,QACF,CAAC;EACH;EAEA,SAAS4C,iBAAiBA,CAAA,EAAG;IAC3B,IAAIwH,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG,EAAE;IACPC,EAAE,GAAGG,YAAY,CAAC,CAAC;IACnB,OAAOH,EAAE,KAAK7H,UAAU,EAAE;MACxB4H,EAAE,CAACF,IAAI,CAACG,EAAE,CAAC;MACXA,EAAE,GAAGG,YAAY,CAAC,CAAC;IACrB;IACA,IAAIJ,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAGI,oCAAoC,CAAC,CAAC;MAC3C,IAAIJ,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG,EAAE;QACPC,EAAE,GAAGC,YAAY,CAAC,CAAC;QACnB,OAAOD,EAAE,KAAK/H,UAAU,EAAE;UACxB8H,EAAE,CAACJ,IAAI,CAACK,EAAE,CAAC;UACXA,EAAE,GAAGC,YAAY,CAAC,CAAC;QACrB;QACA,IAAIF,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGvH,MAAM,CAACwH,EAAE,CAAC;UACfF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASM,oCAAoCA,CAAA,EAAG;IAC9C,IAAIN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGO,mCAAmC,CAAC,CAAC;IAC1C,IAAIP,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAG,EAAE;MACPG,EAAE,GAAGF,YAAY,CAAC,CAAC;MACnB,OAAOE,EAAE,KAAKlI,UAAU,EAAE;QACxB+H,EAAE,CAACL,IAAI,CAACQ,EAAE,CAAC;QACXA,EAAE,GAAGF,YAAY,CAAC,CAAC;MACrB;MACA,IAAID,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAGC,mCAAmC,CAAC,CAAC;QAC1C,IAAID,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAG,EAAE;QACPG,EAAE,GAAGF,YAAY,CAAC,CAAC;QACnB,OAAOE,EAAE,KAAKlI,UAAU,EAAE;UACxB+H,EAAE,CAACL,IAAI,CAACQ,EAAE,CAAC;UACXA,EAAE,GAAGF,YAAY,CAAC,CAAC;QACrB;QACA,IAAID,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAGC,mCAAmC,CAAC,CAAC;UAC1C,IAAID,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASQ,mCAAmCA,CAAA,EAAG;IAC7C,IAAIR,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGQ,eAAe,CAAC,CAAC;IACtB,IAAIR,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAG,EAAE;MACPG,EAAE,GAAGF,YAAY,CAAC,CAAC;MACnB,OAAOE,EAAE,KAAKlI,UAAU,EAAE;QACxB+H,EAAE,CAACL,IAAI,CAACQ,EAAE,CAAC;QACXA,EAAE,GAAGF,YAAY,CAAC,CAAC;MACrB;MACA,IAAID,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAGG,uBAAuB,CAAC,CAAC;QAC9B,IAAIH,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAG,EAAE;QACPG,EAAE,GAAGF,YAAY,CAAC,CAAC;QACnB,OAAOE,EAAE,KAAKlI,UAAU,EAAE;UACxB+H,EAAE,CAACL,IAAI,CAACQ,EAAE,CAAC;UACXA,EAAE,GAAGF,YAAY,CAAC,CAAC;QACrB;QACA,IAAID,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAGG,uBAAuB,CAAC,CAAC;UAC9B,IAAIH,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASU,uBAAuBA,CAAA,EAAG;IACjC,IAAIV,EAAE;IAENA,EAAE,GAAGW,kBAAkB,CAAC,CAAC;IACzB,IAAIX,EAAE,KAAK3H,UAAU,EAAE;MACrB2H,EAAE,GAAGY,eAAe,CAAC,CAAC;MACtB,IAAIZ,EAAE,KAAK3H,UAAU,EAAE;QACrB2H,EAAE,GAAGa,0BAA0B,CAAC,CAAC;QACjC,IAAIb,EAAE,KAAK3H,UAAU,EAAE;UACrB2H,EAAE,GAAGc,wBAAwB,CAAC,CAAC;UAC/B,IAAId,EAAE,KAAK3H,UAAU,EAAE;YACrB2H,EAAE,GAAGe,gBAAgB,CAAC,CAAC;YACvB,IAAIf,EAAE,KAAK3H,UAAU,EAAE;cACrB2H,EAAE,GAAGgB,uBAAuB,CAAC,CAAC;cAC9B,IAAIhB,EAAE,KAAK3H,UAAU,EAAE;gBACrB2H,EAAE,GAAGiB,iCAAiC,CAAC,CAAC;gBACxC,IAAIjB,EAAE,KAAK3H,UAAU,EAAE;kBACrB2H,EAAE,GAAGkB,wCAAwC,CAAC,CAAC;kBAC/C,IAAIlB,EAAE,KAAK3H,UAAU,EAAE;oBACrB2H,EAAE,GAAGmB,uBAAuB,CAAC,CAAC;kBAChC;gBACF;cACF;YACF;UACF;QACF;MACF;IACF;IAEA,OAAOnB,EAAE;EACX;EAEA,SAASS,eAAeA,CAAA,EAAG;IACzB,IAAIT,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE,EAAEa,EAAE;IAE9BpB,EAAE,GAAG/B,WAAW;IAChB,IAAI5E,MAAM,CAACgI,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC1CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACxG,MAAM,CAAC;MAAE;IACjD;IACA,IAAI2G,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGoB,wBAAwB,CAAC,CAAC;QAC/B,IAAIpB,EAAE,KAAK9H,UAAU,EAAE;UACrB+H,EAAE,GAAGnC,WAAW;UAChBsC,EAAE,GAAGiB,kBAAkB,CAAC,CAAC;UACzB,IAAIjB,EAAE,KAAKlI,UAAU,EAAE;YACrBkI,EAAE,GAAG,IAAI;UACX;UACA,IAAIA,EAAE,KAAKlI,UAAU,EAAE;YACrB+I,EAAE,GAAGK,iCAAiC,CAAC,CAAC;YACxC,IAAIL,EAAE,KAAK/I,UAAU,EAAE;cACrBkI,EAAE,GAAG,CAACA,EAAE,EAAEa,EAAE,CAAC;cACbhB,EAAE,GAAGG,EAAE;YACT,CAAC,MAAM;cACLtC,WAAW,GAAGmC,EAAE;cAChBA,EAAE,GAAG/H,UAAU;YACjB;UACF,CAAC,MAAM;YACL4F,WAAW,GAAGmC,EAAE;YAChBA,EAAE,GAAG/H,UAAU;UACjB;UACA,IAAI+H,EAAE,KAAK/H,UAAU,EAAE;YACrB+H,EAAE,GAAG,IAAI;UACX;UACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;YACrB6F,YAAY,GAAG8B,EAAE;YACjBC,EAAE,GAAGzG,MAAM,CAACyG,EAAE,EAAEE,EAAE,EAAEC,EAAE,CAAC;YACvBJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLhC,WAAW,GAAG+B,EAAE;YAChBA,EAAE,GAAG3H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASW,kBAAkBA,CAAA,EAAG;IAC5B,IAAIX,EAAE,EAAEC,EAAE;IAEVD,EAAE,GAAG/B,WAAW;IAChB,IAAIrE,MAAM,CAACyH,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC1CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACjG,MAAM,CAAC;MAAE;IACjD;IACA,IAAIoG,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAGnG,MAAM,CAAC,CAAC;IACf;IACAkG,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAASY,eAAeA,CAAA,EAAG;IACzB,IAAIZ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAIlE,MAAM,CAACsH,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC1CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAAC9F,MAAM,CAAC;MAAE;IACjD;IACA,IAAIiG,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGsB,iCAAiC,CAAC,CAAC;QACxC,IAAItB,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGhG,OAAO,CAACgG,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASyB,iCAAiCA,CAAA,EAAG;IAC3C,IAAIzB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGsB,wBAAwB,CAAC,CAAC;IAC/B,IAAItB,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;MACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;QACrB+H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAGgB,wBAAwB,CAAC,CAAC;QAC/B,IAAIhB,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;QACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;UACrB+H,EAAE,GAAG,IAAI;QACX;QACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAGgB,wBAAwB,CAAC,CAAC;UAC/B,IAAIhB,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASa,0BAA0BA,CAAA,EAAG;IACpC,IAAIb,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAI9D,OAAO,CAACkH,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAAC1F,OAAO,CAAC;MAAE;IAClD;IACA,IAAI6F,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGuB,4BAA4B,CAAC,CAAC;QACnC,IAAIvB,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAG5F,OAAO,CAAC4F,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAAS0B,4BAA4BA,CAAA,EAAG;IACtC,IAAI1B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG0B,eAAe,CAAC,CAAC;IACtB,IAAI1B,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;MACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;QACrB+H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAGoB,eAAe,CAAC,CAAC;QACtB,IAAIpB,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;QACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;UACrB+H,EAAE,GAAG,IAAI;QACX;QACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAGoB,eAAe,CAAC,CAAC;UACtB,IAAIpB,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASc,wBAAwBA,CAAA,EAAG;IAClC,IAAId,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAIzD,OAAO,CAAC6G,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACrF,OAAO,CAAC;MAAE;IAClD;IACA,IAAIwF,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGuB,4BAA4B,CAAC,CAAC;QACnC,IAAIvB,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGvF,OAAO,CAACuF,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASe,gBAAgBA,CAAA,EAAG;IAC1B,IAAIf,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAIrD,OAAO,CAACyG,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACjF,OAAO,CAAC;MAAE;IAClD;IACA,IAAIoF,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGyB,kCAAkC,CAAC,CAAC;QACzC,IAAIzB,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGhG,OAAO,CAACgG,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAAS4B,kCAAkCA,CAAA,EAAG;IAC5C,IAAI5B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG4B,yBAAyB,CAAC,CAAC;IAChC,IAAI5B,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;MACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;QACrB+H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAGsB,yBAAyB,CAAC,CAAC;QAChC,IAAItB,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;QACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;UACrB+H,EAAE,GAAG,IAAI;QACX;QACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAGsB,yBAAyB,CAAC,CAAC;UAChC,IAAItB,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAAS6B,yBAAyBA,CAAA,EAAG;IACnC,IAAI7B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGsB,wBAAwB,CAAC,CAAC;IAC/B,IAAItB,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAGsB,kBAAkB,CAAC,CAAC;MACzB,IAAItB,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGoB,wBAAwB,CAAC,CAAC;QAC/B,IAAIpB,EAAE,KAAK9H,UAAU,EAAE;UACrB+H,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;UACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;YACrB+H,EAAE,GAAG,IAAI;UACX;UACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;YACrBkI,EAAE,GAAGgB,wBAAwB,CAAC,CAAC;YAC/B,IAAIhB,EAAE,KAAKlI,UAAU,EAAE;cACrB6F,YAAY,GAAG8B,EAAE;cACjBC,EAAE,GAAGnF,OAAO,CAACmF,EAAE,EAAEE,EAAE,EAAEI,EAAE,CAAC;cACxBP,EAAE,GAAGC,EAAE;YACT,CAAC,MAAM;cACLhC,WAAW,GAAG+B,EAAE;cAChBA,EAAE,GAAG3H,UAAU;YACjB;UACF,CAAC,MAAM;YACL4F,WAAW,GAAG+B,EAAE;YAChBA,EAAE,GAAG3H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASgB,uBAAuBA,CAAA,EAAG;IACjC,IAAIhB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAI5C,OAAO,CAACgG,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACxE,OAAO,CAAC;MAAE;IAClD;IACA,IAAI2E,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG2B,yCAAyC,CAAC,CAAC;QAChD,IAAI3B,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGhG,OAAO,CAACgG,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAAS8B,yCAAyCA,CAAA,EAAG;IACnD,IAAI9B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG8B,gCAAgC,CAAC,CAAC;IACvC,IAAI9B,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;MACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;QACrB+H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAGwB,gCAAgC,CAAC,CAAC;QACvC,IAAIxB,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;QACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;UACrB+H,EAAE,GAAG,IAAI;QACX;QACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAGwB,gCAAgC,CAAC,CAAC;UACvC,IAAIxB,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAAS+B,gCAAgCA,CAAA,EAAG;IAC1C,IAAI/B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGsB,wBAAwB,CAAC,CAAC;IAC/B,IAAItB,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAGsB,kBAAkB,CAAC,CAAC;MACzB,IAAItB,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGoB,wBAAwB,CAAC,CAAC;QAC/B,IAAIpB,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAG1E,OAAO,CAAC0E,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASiB,iCAAiCA,CAAA,EAAG;IAC3C,IAAIjB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAIzC,OAAO,CAAC6F,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACrE,OAAO,CAAC;MAAE;IAClD;IACA,IAAIwE,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG6B,mDAAmD,CAAC,CAAC;QAC1D,IAAI7B,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGhG,OAAO,CAACgG,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASgC,mDAAmDA,CAAA,EAAG;IAC7D,IAAIhC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGgC,0CAA0C,CAAC,CAAC;IACjD,IAAIhC,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;MACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;QACrB+H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAG0B,0CAA0C,CAAC,CAAC;QACjD,IAAI1B,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;QACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;UACrB+H,EAAE,GAAG,IAAI;QACX;QACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAG0B,0CAA0C,CAAC,CAAC;UACjD,IAAI1B,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASiC,0CAA0CA,CAAA,EAAG;IACpD,IAAIjC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGsB,wBAAwB,CAAC,CAAC;IAC/B,IAAItB,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAGsB,kBAAkB,CAAC,CAAC;MACzB,IAAItB,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGoB,wBAAwB,CAAC,CAAC;QAC/B,IAAIpB,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGvE,OAAO,CAACuE,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASkB,wCAAwCA,CAAA,EAAG;IAClD,IAAIlB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAItC,OAAO,CAAC0F,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAAClE,OAAO,CAAC;MAAE;IAClD;IACA,IAAIqE,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG+B,0DAA0D,CAAC,CAAC;QACjE,IAAI/B,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGhG,OAAO,CAACgG,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASkC,0DAA0DA,CAAA,EAAG;IACpE,IAAIlC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGsB,wBAAwB,CAAC,CAAC;IAC/B,IAAItB,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;MACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;QACrB+H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAGgB,wBAAwB,CAAC,CAAC;QAC/B,IAAIhB,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;QACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;UACrB+H,EAAE,GAAG,IAAI;QACX;QACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAGgB,wBAAwB,CAAC,CAAC;UAC/B,IAAIhB,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASmB,uBAAuBA,CAAA,EAAG;IACjC,IAAInB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChB,IAAIpC,OAAO,CAACwF,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAAChE,OAAO,CAAC;MAAE;IAClD;IACA,IAAImE,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGE,YAAY,CAAC,CAAC;MACnB,OAAOF,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGE,YAAY,CAAC,CAAC;MACrB;MACA,IAAIH,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGgC,yCAAyC,CAAC,CAAC;QAChD,IAAIhC,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGhG,OAAO,CAACgG,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASmC,yCAAyCA,CAAA,EAAG;IACnD,IAAInC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE;IAE1BP,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGmC,gCAAgC,CAAC,CAAC;IACvC,IAAInC,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG,EAAE;MACPC,EAAE,GAAGlC,WAAW;MAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;MACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;QACrB+H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;QACrBkI,EAAE,GAAG6B,gCAAgC,CAAC,CAAC;QACvC,IAAI7B,EAAE,KAAKlI,UAAU,EAAE;UACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;UACbJ,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLnC,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGkC,EAAE;QAChBA,EAAE,GAAG9H,UAAU;MACjB;MACA,OAAO8H,EAAE,KAAK9H,UAAU,EAAE;QACxB6H,EAAE,CAACH,IAAI,CAACI,EAAE,CAAC;QACXA,EAAE,GAAGlC,WAAW;QAChBmC,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;QACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;UACrB+H,EAAE,GAAG,IAAI;QACX;QACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;UACrBkI,EAAE,GAAG6B,gCAAgC,CAAC,CAAC;UACvC,IAAI7B,EAAE,KAAKlI,UAAU,EAAE;YACrB+H,EAAE,GAAG,CAACA,EAAE,EAAEG,EAAE,CAAC;YACbJ,EAAE,GAAGC,EAAE;UACT,CAAC,MAAM;YACLnC,WAAW,GAAGkC,EAAE;YAChBA,EAAE,GAAG9H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAGkC,EAAE;UAChBA,EAAE,GAAG9H,UAAU;QACjB;MACF;MACA,IAAI6H,EAAE,KAAK7H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAG/G,MAAM,CAAC+G,EAAE,EAAEC,EAAE,CAAC;QACnBF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLhC,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASoC,gCAAgCA,CAAA,EAAG;IAC1C,IAAIpC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE,EAAEa,EAAE,EAAEiB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAEC,GAAG;IAEpDzC,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGyC,2BAA2B,CAAC,CAAC;IAClC,IAAIzC,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAGsB,kBAAkB,CAAC,CAAC;MACzB,IAAItB,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGuC,2BAA2B,CAAC,CAAC;QAClC,IAAIvC,EAAE,KAAK9H,UAAU,EAAE;UACrB+H,EAAE,GAAGoB,kBAAkB,CAAC,CAAC;UACzB,IAAIpB,EAAE,KAAK/H,UAAU,EAAE;YACrB+H,EAAE,GAAG,IAAI;UACX;UACA,IAAIA,EAAE,KAAK/H,UAAU,EAAE;YACrBkI,EAAE,GAAGoB,eAAe,CAAC,CAAC;YACtB,IAAIpB,EAAE,KAAKlI,UAAU,EAAE;cACrB+I,EAAE,GAAGI,kBAAkB,CAAC,CAAC;cACzB,IAAIJ,EAAE,KAAK/I,UAAU,EAAE;gBACrBgK,EAAE,GAAGM,aAAa,CAAC,CAAC;gBACpB,IAAIN,EAAE,KAAKhK,UAAU,EAAE;kBACrBiK,EAAE,GAAGd,kBAAkB,CAAC,CAAC;kBACzB,IAAIc,EAAE,KAAKjK,UAAU,EAAE;oBACrBiK,EAAE,GAAG,IAAI;kBACX;kBACA,IAAIA,EAAE,KAAKjK,UAAU,EAAE;oBACrBkK,EAAE,GAAGI,aAAa,CAAC,CAAC;oBACpB,IAAIJ,EAAE,KAAKlK,UAAU,EAAE;sBACrBmK,GAAG,GAAGhB,kBAAkB,CAAC,CAAC;sBAC1B,IAAIgB,GAAG,KAAKnK,UAAU,EAAE;wBACtBmK,GAAG,GAAG,IAAI;sBACZ;sBACA,IAAIA,GAAG,KAAKnK,UAAU,EAAE;wBACtBoK,GAAG,GAAGlB,wBAAwB,CAAC,CAAC;wBAChC,IAAIkB,GAAG,KAAKpK,UAAU,EAAE;0BACtB6F,YAAY,GAAG8B,EAAE;0BACjBC,EAAE,GAAGlE,OAAO,CAACkE,EAAE,EAAEE,EAAE,EAAEI,EAAE,EAAE8B,EAAE,EAAEE,EAAE,EAAEE,GAAG,CAAC;0BACrCzC,EAAE,GAAGC,EAAE;wBACT,CAAC,MAAM;0BACLhC,WAAW,GAAG+B,EAAE;0BAChBA,EAAE,GAAG3H,UAAU;wBACjB;sBACF,CAAC,MAAM;wBACL4F,WAAW,GAAG+B,EAAE;wBAChBA,EAAE,GAAG3H,UAAU;sBACjB;oBACF,CAAC,MAAM;sBACL4F,WAAW,GAAG+B,EAAE;sBAChBA,EAAE,GAAG3H,UAAU;oBACjB;kBACF,CAAC,MAAM;oBACL4F,WAAW,GAAG+B,EAAE;oBAChBA,EAAE,GAAG3H,UAAU;kBACjB;gBACF,CAAC,MAAM;kBACL4F,WAAW,GAAG+B,EAAE;kBAChBA,EAAE,GAAG3H,UAAU;gBACjB;cACF,CAAC,MAAM;gBACL4F,WAAW,GAAG+B,EAAE;gBAChBA,EAAE,GAAG3H,UAAU;cACjB;YACF,CAAC,MAAM;cACL4F,WAAW,GAAG+B,EAAE;cAChBA,EAAE,GAAG3H,UAAU;YACjB;UACF,CAAC,MAAM;YACL4F,WAAW,GAAG+B,EAAE;YAChBA,EAAE,GAAG3H,UAAU;UACjB;QACF,CAAC,MAAM;UACL4F,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAASuB,wBAAwBA,CAAA,EAAG;IAClC,IAAIvB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG0B,eAAe,CAAC,CAAC;IACtB,IAAI1B,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAGsB,kBAAkB,CAAC,CAAC;MACzB,IAAItB,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAGwB,eAAe,CAAC,CAAC;QACtB,IAAIxB,EAAE,KAAK9H,UAAU,EAAE;UACrB6F,YAAY,GAAG8B,EAAE;UACjBC,EAAE,GAAGzD,OAAO,CAACyD,EAAE,EAAEE,EAAE,CAAC;UACpBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IAEA,OAAO2H,EAAE;EACX;EAEA,SAAS0C,2BAA2BA,CAAA,EAAG;IACrC,IAAI1C,EAAE,EAAEC,EAAE;IAEVD,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG2C,gCAAgC,CAAC,CAAC;IACvC,IAAI3C,EAAE,KAAK5H,UAAU,EAAE;MACrB4H,EAAE,GAAG4C,uBAAuB,CAAC,CAAC;IAChC;IACA,IAAI5C,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAGxD,OAAO,CAACwD,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAAS2B,eAAeA,CAAA,EAAG;IACzB,IAAI3B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGhC,WAAW;IAChBiC,EAAE,GAAG4C,aAAa,CAAC,CAAC;IACpB,IAAI5C,EAAE,KAAK7H,UAAU,EAAE;MACrB6H,EAAE,GAAG,IAAI;IACX;IACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;MACrB8H,EAAE,GAAGyC,gCAAgC,CAAC,CAAC;MACvC,IAAIzC,EAAE,KAAK9H,UAAU,EAAE;QACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;QACbF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLjC,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAGgC,EAAE;MAChBA,EAAE,GAAG5H,UAAU;IACjB;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB4H,EAAE,GAAGhC,WAAW;MAChBiC,EAAE,GAAG4C,aAAa,CAAC,CAAC;MACpB,IAAI5C,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG0C,uBAAuB,CAAC,CAAC;QAC9B,IAAI1C,EAAE,KAAK9H,UAAU,EAAE;UACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;UACbF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLjC,WAAW,GAAGgC,EAAE;UAChBA,EAAE,GAAG5H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;IACF;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAGtD,OAAO,CAACsD,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAAS2C,aAAaA,CAAA,EAAG;IACvB,IAAI3C,EAAE,EAAEC,EAAE;IAEVD,EAAE,GAAG/B,WAAW;IAChB,IAAIrB,OAAO,CAACyE,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACjD,OAAO,CAAC;MAAE;IAClD;IACA,IAAIoD,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAGnD,OAAO,CAACmD,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAASwB,kBAAkBA,CAAA,EAAG;IAC5B,IAAIxB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG,EAAE;IACPC,EAAE,GAAGG,YAAY,CAAC,CAAC;IACnB,IAAIH,EAAE,KAAK7H,UAAU,EAAE;MACrB,OAAO6H,EAAE,KAAK7H,UAAU,EAAE;QACxB4H,EAAE,CAACF,IAAI,CAACG,EAAE,CAAC;QACXA,EAAE,GAAGG,YAAY,CAAC,CAAC;MACrB;IACF,CAAC,MAAM;MACLJ,EAAE,GAAG5H,UAAU;IACjB;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB6H,EAAE,GAAG6C,cAAc,CAAC,CAAC;MACrB,IAAI7C,EAAE,KAAK7H,UAAU,EAAE;QACrB6H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG,EAAE;QACPC,EAAE,GAAGC,YAAY,CAAC,CAAC;QACnB,OAAOD,EAAE,KAAK/H,UAAU,EAAE;UACxB8H,EAAE,CAACJ,IAAI,CAACK,EAAE,CAAC;UACXA,EAAE,GAAGC,YAAY,CAAC,CAAC;QACrB;QACA,IAAIF,EAAE,KAAK9H,UAAU,EAAE;UACrB4H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;UACjBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLhC,WAAW,GAAG+B,EAAE;UAChBA,EAAE,GAAG3H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAG+B,EAAE;QAChBA,EAAE,GAAG3H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAG+B,EAAE;MAChBA,EAAE,GAAG3H,UAAU;IACjB;IACA,IAAI2H,EAAE,KAAK3H,UAAU,EAAE;MACrB2H,EAAE,GAAG/B,WAAW;MAChBgC,EAAE,GAAGhC,WAAW;MAChBiC,EAAE,GAAG6C,cAAc,CAAC,CAAC;MACrB,IAAI7C,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG,EAAE;QACPC,EAAE,GAAGC,YAAY,CAAC,CAAC;QACnB,OAAOD,EAAE,KAAK/H,UAAU,EAAE;UACxB8H,EAAE,CAACJ,IAAI,CAACK,EAAE,CAAC;UACXA,EAAE,GAAGC,YAAY,CAAC,CAAC;QACrB;QACA,IAAIF,EAAE,KAAK9H,UAAU,EAAE;UACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;UACbF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLjC,WAAW,GAAGgC,EAAE;UAChBA,EAAE,GAAG5H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;MACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;QACrB6F,YAAY,GAAG8B,EAAE;QACjBC,EAAE,GAAGjD,OAAO,CAAC,CAAC;MAChB;MACAgD,EAAE,GAAGC,EAAE;IACT;IAEA,OAAOD,EAAE;EACX;EAEA,SAAS+C,cAAcA,CAAA,EAAG;IACxB,IAAI/C,EAAE;IAEN,IAAI7H,KAAK,CAACf,UAAU,CAAC6G,WAAW,CAAC,KAAK,EAAE,EAAE;MACxC+B,EAAE,GAAG/C,OAAO;MACZgB,WAAW,EAAE;IACf,CAAC,MAAM;MACL+B,EAAE,GAAG3H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAAC5C,OAAO,CAAC;MAAE;IAClD;IAEA,OAAO8C,EAAE;EACX;EAEA,SAAS4C,gCAAgCA,CAAA,EAAG;IAC1C,IAAI5C,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAElBH,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGhC,WAAW;IAChBiC,EAAE,GAAG8C,4BAA4B,CAAC,CAAC;IACnC,IAAI9C,EAAE,KAAK7H,UAAU,EAAE;MACrB8H,EAAE,GAAG8C,iBAAiB,CAAC,CAAC;MACxB,IAAI9C,EAAE,KAAK9H,UAAU,EAAE;QACrB8H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK9H,UAAU,EAAE;QACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;QACbF,EAAE,GAAGC,EAAE;MACT,CAAC,MAAM;QACLjC,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAGgC,EAAE;MAChBA,EAAE,GAAG5H,UAAU;IACjB;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB4H,EAAE,GAAGhC,WAAW;MAChBiC,EAAE,GAAG2C,uBAAuB,CAAC,CAAC;MAC9B,IAAI3C,EAAE,KAAK7H,UAAU,EAAE;QACrB8H,EAAE,GAAG8C,iBAAiB,CAAC,CAAC;QACxB,IAAI9C,EAAE,KAAK9H,UAAU,EAAE;UACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;UACbF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLjC,WAAW,GAAGgC,EAAE;UAChBA,EAAE,GAAG5H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;IACF;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAG7C,OAAO,CAAC6C,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAASgD,4BAA4BA,CAAA,EAAG;IACtC,IAAIhD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGhC,WAAW;IAChBiC,EAAE,GAAG2C,uBAAuB,CAAC,CAAC;IAC9B,IAAI3C,EAAE,KAAK7H,UAAU,EAAE;MACrB6H,EAAE,GAAG,IAAI;IACX;IACA,IAAIA,EAAE,KAAK7H,UAAU,EAAE;MACrB,IAAIF,KAAK,CAACf,UAAU,CAAC6G,WAAW,CAAC,KAAK,EAAE,EAAE;QACxCkC,EAAE,GAAG9C,OAAO;QACZY,WAAW,EAAE;MACf,CAAC,MAAM;QACLkC,EAAE,GAAG9H,UAAU;QACf,IAAImG,eAAe,KAAK,CAAC,EAAE;UAAEsB,QAAQ,CAACxC,OAAO,CAAC;QAAE;MAClD;MACA,IAAI6C,EAAE,KAAK9H,UAAU,EAAE;QACrB+H,EAAE,GAAGyC,uBAAuB,CAAC,CAAC;QAC9B,IAAIzC,EAAE,KAAK/H,UAAU,EAAE;UACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;UACjBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLjC,WAAW,GAAGgC,EAAE;UAChBA,EAAE,GAAG5H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAGgC,EAAE;MAChBA,EAAE,GAAG5H,UAAU;IACjB;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB4H,EAAE,GAAGhC,WAAW;MAChBiC,EAAE,GAAG2C,uBAAuB,CAAC,CAAC;MAC9B,IAAI3C,EAAE,KAAK7H,UAAU,EAAE;QACrB,IAAIF,KAAK,CAACf,UAAU,CAAC6G,WAAW,CAAC,KAAK,EAAE,EAAE;UACxCkC,EAAE,GAAG9C,OAAO;UACZY,WAAW,EAAE;QACf,CAAC,MAAM;UACLkC,EAAE,GAAG9H,UAAU;UACf,IAAImG,eAAe,KAAK,CAAC,EAAE;YAAEsB,QAAQ,CAACxC,OAAO,CAAC;UAAE;QAClD;QACA,IAAI6C,EAAE,KAAK9H,UAAU,EAAE;UACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,CAAC;UACbF,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLjC,WAAW,GAAGgC,EAAE;UAChBA,EAAE,GAAG5H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;IACF;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAG7C,OAAO,CAAC6C,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAASiD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIjD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEtBJ,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAGhC,WAAW;IAChB,IAAIV,OAAO,CAAC8D,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CiC,EAAE,GAAG/H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLiC,EAAE,GAAG7H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACtC,OAAO,CAAC;MAAE;IAClD;IACA,IAAI0C,EAAE,KAAK7H,UAAU,EAAE;MACrB8H,EAAE,GAAG2C,aAAa,CAAC,CAAC;MACpB,IAAI3C,EAAE,KAAK9H,UAAU,EAAE;QACrB8H,EAAE,GAAG,IAAI;MACX;MACA,IAAIA,EAAE,KAAK9H,UAAU,EAAE;QACrB+H,EAAE,GAAGyC,uBAAuB,CAAC,CAAC;QAC9B,IAAIzC,EAAE,KAAK/H,UAAU,EAAE;UACrB6H,EAAE,GAAG,CAACA,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;UACjBH,EAAE,GAAGC,EAAE;QACT,CAAC,MAAM;UACLjC,WAAW,GAAGgC,EAAE;UAChBA,EAAE,GAAG5H,UAAU;QACjB;MACF,CAAC,MAAM;QACL4F,WAAW,GAAGgC,EAAE;QAChBA,EAAE,GAAG5H,UAAU;MACjB;IACF,CAAC,MAAM;MACL4F,WAAW,GAAGgC,EAAE;MAChBA,EAAE,GAAG5H,UAAU;IACjB;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAG7C,OAAO,CAAC6C,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAAS8C,aAAaA,CAAA,EAAG;IACvB,IAAI9C,EAAE;IAEN,IAAIvC,OAAO,CAAC4D,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3C+B,EAAE,GAAG7H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACL+B,EAAE,GAAG3H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAACpC,OAAO,CAAC;MAAE;IAClD;IAEA,OAAOsC,EAAE;EACX;EAEA,SAAS6C,uBAAuBA,CAAA,EAAG;IACjC,IAAI7C,EAAE,EAAEC,EAAE,EAAEC,EAAE;IAEdF,EAAE,GAAG/B,WAAW;IAChBgC,EAAE,GAAG,EAAE;IACP,IAAItC,OAAO,CAAC0D,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CiC,EAAE,GAAG/H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLiC,EAAE,GAAG7H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAAClC,OAAO,CAAC;MAAE;IAClD;IACA,IAAIsC,EAAE,KAAK7H,UAAU,EAAE;MACrB,OAAO6H,EAAE,KAAK7H,UAAU,EAAE;QACxB4H,EAAE,CAACF,IAAI,CAACG,EAAE,CAAC;QACX,IAAIvC,OAAO,CAAC0D,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;UAC3CiC,EAAE,GAAG/H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;UAC9BA,WAAW,EAAE;QACf,CAAC,MAAM;UACLiC,EAAE,GAAG7H,UAAU;UACf,IAAImG,eAAe,KAAK,CAAC,EAAE;YAAEsB,QAAQ,CAAClC,OAAO,CAAC;UAAE;QAClD;MACF;IACF,CAAC,MAAM;MACLqC,EAAE,GAAG5H,UAAU;IACjB;IACA,IAAI4H,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAGpC,OAAO,CAACoC,EAAE,CAAC;IAClB;IACAD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAEA,SAASK,YAAYA,CAAA,EAAG;IACtB,IAAIL,EAAE,EAAEC,EAAE;IAEVD,EAAE,GAAG/B,WAAW;IAChB,IAAIF,OAAO,CAACsD,IAAI,CAAClJ,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC,CAAC,EAAE;MAC3CgC,EAAE,GAAG9H,KAAK,CAACmJ,MAAM,CAACrD,WAAW,CAAC;MAC9BA,WAAW,EAAE;IACf,CAAC,MAAM;MACLgC,EAAE,GAAG5H,UAAU;MACf,IAAImG,eAAe,KAAK,CAAC,EAAE;QAAEsB,QAAQ,CAAC9B,OAAO,CAAC;MAAE;IAClD;IACA,IAAIiC,EAAE,KAAK5H,UAAU,EAAE;MACrB6F,YAAY,GAAG8B,EAAE;MACjBC,EAAE,GAAGjD,OAAO,CAAC,CAAC;IAChB;IACAgD,EAAE,GAAGC,EAAE;IAEP,OAAOD,EAAE;EACX;EAGE,SAAS5G,KAAKA,CAACL,KAAK,EAACI,IAAI,EAAC;IACxB,IAAI,CAACA,IAAI,EAAE,OAAO,CAACJ,KAAK,CAAC;IACzB,KAAK,IAAIgC,CAAC,GAAC,CAAChC,KAAK,CAAC,EAACvC,CAAC,GAAC,CAAC,EAAC0M,CAAC,GAAC/J,IAAI,CAACzC,MAAM,EAACF,CAAC,GAAC0M,CAAC,EAAC1M,CAAC,EAAE,EAAEuE,CAAC,CAACvE,CAAC,GAAC,CAAC,CAAC,GAAC2C,IAAI,CAAC3C,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,OAAOuE,CAAC;EACV;EAEA,IAAInC,IAAI,GAAG;IAACuK,CAAC,EAAC,QAAQ;IAACD,CAAC,EAAC,QAAQ;IAACE,CAAC,EAAC,mBAAmB;IAACC,CAAC,EAAC,iBAAiB;IAAC5J,CAAC,EAAC,SAAS;IAAClC,CAAC,EAAC,gBAAgB;IAAC+L,CAAC,EAAC,mBAAmB;IAACC,CAAC,EAAC,0BAA0B;IAACxI,CAAC,EAAC,gBAAgB;IAACyI,CAAC,EAAC;EAAW,CAAC;EAC/L,KAAK,IAAIxK,IAAI,IAAIJ,IAAI,EAAEA,IAAI,CAACI,IAAI,CAAC1B,WAAW,CAAC,CAAC,CAAC,GAACsB,IAAI,CAACI,IAAI,CAAC;EAC1D,SAASW,QAAQA,CAACX,IAAI,EAACkB,IAAI,EAAC;IAC1B,IAAI,CAACA,IAAI,EAAEA,IAAI,GAAC,CAAC,CAAC,CAAC,CAAC;IACpB,KAAK,IAAI1D,CAAC,GAAC0D,IAAI,CAACxD,MAAM,EAACF,CAAC,EAAE,GAAE;MAC1B,IAAIiN,GAAG,GAAC;QAACzK,IAAI,EAACA,IAAI;QAAC0K,OAAO,EAAC9K,IAAI,CAACI,IAAI;MAAC,CAAC;MACtC,IAAIA,IAAI,IAAEA,IAAI,CAAC2K,WAAW,CAAC,CAAC,EAAEF,GAAG,CAACxK,QAAQ,GAAC,IAAI;MAC/C,KAAK,IAAI2K,CAAC,IAAI1J,IAAI,CAAC1D,CAAC,CAAC,EAAEiN,GAAG,CAACG,CAAC,CAAC,GAAC1J,IAAI,CAAC1D,CAAC,CAAC,CAACoN,CAAC,CAAC;MACxC1J,IAAI,CAAC1D,CAAC,CAAC,GAAGiN,GAAG;IACf;IACA,OAAOvJ,IAAI;EACb;EAGFuE,UAAU,GAAGhG,qBAAqB,CAAC,CAAC;EAEpC,IAAIgG,UAAU,KAAKpG,UAAU,IAAI4F,WAAW,KAAK9F,KAAK,CAACzB,MAAM,EAAE;IAC7D,OAAO+H,UAAU;EACnB,CAAC,MAAM;IACL,IAAIA,UAAU,KAAKpG,UAAU,IAAI4F,WAAW,GAAG9F,KAAK,CAACzB,MAAM,EAAE;MAC3DoJ,QAAQ,CAACX,kBAAkB,CAAC,CAAC,CAAC;IAChC;IAEA,MAAMN,wBAAwB,CAC5BN,mBAAmB,EACnBD,cAAc,GAAGnG,KAAK,CAACzB,MAAM,GAAGyB,KAAK,CAACmJ,MAAM,CAAChD,cAAc,CAAC,GAAG,IAAI,EACnEA,cAAc,GAAGnG,KAAK,CAACzB,MAAM,GACzBkI,mBAAmB,CAACN,cAAc,EAAEA,cAAc,GAAG,CAAC,CAAC,GACvDM,mBAAmB,CAACN,cAAc,EAAEA,cAAc,CACxD,CAAC;EACH;AACF;AAEAuF,MAAM,CAACC,OAAO,GAAG;EACfC,WAAW,EAAEvO,eAAe;EAC5BwO,KAAK,EAAQ9L;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}