{"ast": null, "code": "/** finds the zeros of a function, given two starting points (which must\n * have opposite signs */\nexport function bisect(f, a, b, parameters) {\n  parameters = parameters || {};\n  const maxIterations = parameters.maxIterations || 100;\n  const tolerance = parameters.tolerance || 1e-10;\n  const fA = f(a);\n  const fB = f(b);\n  let delta = b - a;\n  if (fA * fB > 0) {\n    throw 'Initial bisect points must have opposite signs';\n  }\n  if (fA === 0) return a;\n  if (fB === 0) return b;\n  for (let i = 0; i < maxIterations; ++i) {\n    delta /= 2;\n    const mid = a + delta;\n    const fMid = f(mid);\n    if (fMid * fA >= 0) {\n      a = mid;\n    }\n    if (Math.abs(delta) < tolerance || fMid === 0) {\n      return mid;\n    }\n  }\n  return a + delta;\n}", "map": {"version": 3, "names": ["bisect", "f", "a", "b", "parameters", "maxIterations", "tolerance", "fA", "fB", "delta", "i", "mid", "fMid", "Math", "abs"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/venn/fmin/bisect.ts"], "sourcesContent": ["/** finds the zeros of a function, given two starting points (which must\n * have opposite signs */\nexport function bisect(f, a, b, parameters?: any) {\n  parameters = parameters || {};\n  const maxIterations = parameters.maxIterations || 100;\n  const tolerance = parameters.tolerance || 1e-10;\n  const fA = f(a);\n  const fB = f(b);\n  let delta = b - a;\n\n  if (fA * fB > 0) {\n    throw 'Initial bisect points must have opposite signs';\n  }\n\n  if (fA === 0) return a;\n  if (fB === 0) return b;\n\n  for (let i = 0; i < maxIterations; ++i) {\n    delta /= 2;\n    const mid = a + delta;\n    const fMid = f(mid);\n\n    if (fMid * fA >= 0) {\n      a = mid;\n    }\n\n    if (Math.abs(delta) < tolerance || fMid === 0) {\n      return mid;\n    }\n  }\n  return a + delta;\n}\n"], "mappings": "AAAA;;AAEA,OAAM,SAAUA,MAAMA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,UAAgB;EAC9CA,UAAU,GAAGA,UAAU,IAAI,EAAE;EAC7B,MAAMC,aAAa,GAAGD,UAAU,CAACC,aAAa,IAAI,GAAG;EACrD,MAAMC,SAAS,GAAGF,UAAU,CAACE,SAAS,IAAI,KAAK;EAC/C,MAAMC,EAAE,GAAGN,CAAC,CAACC,CAAC,CAAC;EACf,MAAMM,EAAE,GAAGP,CAAC,CAACE,CAAC,CAAC;EACf,IAAIM,KAAK,GAAGN,CAAC,GAAGD,CAAC;EAEjB,IAAIK,EAAE,GAAGC,EAAE,GAAG,CAAC,EAAE;IACf,MAAM,gDAAgD;;EAGxD,IAAID,EAAE,KAAK,CAAC,EAAE,OAAOL,CAAC;EACtB,IAAIM,EAAE,KAAK,CAAC,EAAE,OAAOL,CAAC;EAEtB,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,aAAa,EAAE,EAAEK,CAAC,EAAE;IACtCD,KAAK,IAAI,CAAC;IACV,MAAME,GAAG,GAAGT,CAAC,GAAGO,KAAK;IACrB,MAAMG,IAAI,GAAGX,CAAC,CAACU,GAAG,CAAC;IAEnB,IAAIC,IAAI,GAAGL,EAAE,IAAI,CAAC,EAAE;MAClBL,CAAC,GAAGS,GAAG;;IAGT,IAAIE,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,GAAGH,SAAS,IAAIM,IAAI,KAAK,CAAC,EAAE;MAC7C,OAAOD,GAAG;;;EAGd,OAAOT,CAAC,GAAGO,KAAK;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}