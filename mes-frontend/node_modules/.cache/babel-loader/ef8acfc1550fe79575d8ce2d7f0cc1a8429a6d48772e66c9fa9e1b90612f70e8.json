{"ast": null, "code": "import { getBounds } from './bounds';\nfunction onLine(line, point) {\n  return point[0] <= Math.max(line[0][0], line[1][0]) && point[0] <= Math.min(line[0][0], line[1][0]) && point[1] <= Math.max(line[0][1], line[1][1]) && point[1] <= Math.min(line[0][1], line[1][1]);\n}\nfunction direction(a, b, c) {\n  var val = (b[1] - a[1]) * (c[0] - b[0]) - (b[0] - a[0]) * (c[1] - b[1]);\n  if (val === 0) return 0;\n  return val < 0 ? 2 : 1;\n}\nfunction isIntersect(line1, line2) {\n  var dir1 = direction(line1[0], line1[1], line2[0]);\n  var dir2 = direction(line1[0], line1[1], line2[1]);\n  var dir3 = direction(line2[0], line2[1], line1[0]);\n  var dir4 = direction(line2[0], line2[1], line1[1]);\n  if (dir1 !== dir2 && dir3 !== dir4) return true;\n  if (dir1 === 0 && onLine(line1, line2[0])) return true;\n  if (dir2 === 0 && onLine(line1, line2[1])) return true;\n  if (dir3 === 0 && onLine(line2, line1[0])) return true;\n  if (dir4 === 0 && onLine(line2, line1[1])) return true;\n  return false;\n}\nexport function isPointInsideRectangle(polygon, point) {\n  var n = polygon.length;\n  if (n < 3) return false;\n  var lineToInfinity = [point, [9999, point[1]]];\n  var count = 0;\n  var i = 0;\n  do {\n    var side = [polygon[i], polygon[(i + 1) % n]];\n    if (isIntersect(side, lineToInfinity)) {\n      if (direction(side[0], point, side[1]) === 0) return onLine(side, point);\n      count++;\n    }\n    i = (i + 1) % n;\n  } while (i !== 0);\n  return !!(count & 1);\n}\nexport function isRectangleBInsideA(rectA, rectB) {\n  return rectB.every(function (point) {\n    return isPointInsideRectangle(rectA, point);\n  });\n}\n/**\n * 检测 child 是否完全在 container 内部\n */\nexport function contain(container, child, margin) {\n  var x1 = container.x1,\n    x2 = container.x2,\n    y1 = container.y1,\n    y2 = container.y2;\n  var parent = [[x1, y1], [x2, y1], [x2, y2], [x1, y2]];\n  var element = getBounds(child, margin);\n  return isRectangleBInsideA(parent, element);\n}", "map": {"version": 3, "names": ["getBounds", "onLine", "line", "point", "Math", "max", "min", "direction", "a", "b", "c", "val", "isIntersect", "line1", "line2", "dir1", "dir2", "dir3", "dir4", "isPointInsideRectangle", "polygon", "n", "length", "lineToInfinity", "count", "i", "side", "isRectangleBInsideA", "rectA", "rectB", "every", "contain", "container", "child", "margin", "x1", "x2", "y1", "y2", "parent", "element"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/utils/contain.ts"], "sourcesContent": ["import type { DisplayObject } from '../../../shapes';\nimport type { Point } from '../../../types';\nimport type { SeriesAttr } from '../../../util';\nimport { Bounds, getBounds } from './bounds';\n\ntype Line = [Point, Point];\n\nfunction onLine(line: Line, point: Point): boolean {\n  return (\n    point[0] <= Math.max(line[0][0], line[1][0]) &&\n    point[0] <= Math.min(line[0][0], line[1][0]) &&\n    point[1] <= Math.max(line[0][1], line[1][1]) &&\n    point[1] <= Math.min(line[0][1], line[1][1])\n  );\n}\n\nfunction direction(a: Point, b: Point, c: Point): number {\n  const val = (b[1] - a[1]) * (c[0] - b[0]) - (b[0] - a[0]) * (c[1] - b[1]);\n  if (val === 0) return 0;\n  return val < 0 ? 2 : 1;\n}\n\nfunction isIntersect(line1: Line, line2: Line): boolean {\n  const dir1 = direction(line1[0], line1[1], line2[0]);\n  const dir2 = direction(line1[0], line1[1], line2[1]);\n  const dir3 = direction(line2[0], line2[1], line1[0]);\n  const dir4 = direction(line2[0], line2[1], line1[1]);\n\n  if (dir1 !== dir2 && dir3 !== dir4) return true;\n  if (dir1 === 0 && onLine(line1, line2[0])) return true;\n  if (dir2 === 0 && onLine(line1, line2[1])) return true;\n  if (dir3 === 0 && onLine(line2, line1[0])) return true;\n  if (dir4 === 0 && onLine(line2, line1[1])) return true;\n\n  return false;\n}\n\nexport function isPointInsideRectangle(polygon: Point[], point: Point): boolean {\n  const n = polygon.length;\n  if (n < 3) return false;\n\n  const lineToInfinity: Line = [point, [9999, point[1]]];\n  let count = 0;\n  let i = 0;\n  do {\n    const side: Line = [polygon[i], polygon[(i + 1) % n]];\n    if (isIntersect(side, lineToInfinity)) {\n      if (direction(side[0], point, side[1]) === 0) return onLine(side, point);\n      count++;\n    }\n    i = (i + 1) % n;\n  } while (i !== 0);\n\n  return !!(count & 1);\n}\n\nexport function isRectangleBInsideA(rectA: Point[], rectB: Point[]) {\n  return rectB.every((point) => isPointInsideRectangle(rectA, point));\n}\n\n/**\n * 检测 child 是否完全在 container 内部\n */\nexport function contain(container: Bounds, child: DisplayObject<any>, margin?: SeriesAttr) {\n  const { x1, x2, y1, y2 } = container;\n  const parent: Point[] = [\n    [x1, y1],\n    [x2, y1],\n    [x2, y2],\n    [x1, y2],\n  ];\n\n  const element = getBounds(child, margin);\n\n  return isRectangleBInsideA(parent, element);\n}\n"], "mappings": "AAGA,SAAiBA,SAAS,QAAQ,UAAU;AAI5C,SAASC,MAAMA,CAACC,IAAU,EAAEC,KAAY;EACtC,OACEA,KAAK,CAAC,CAAC,CAAC,IAAIC,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAC5CC,KAAK,CAAC,CAAC,CAAC,IAAIC,IAAI,CAACE,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAC5CC,KAAK,CAAC,CAAC,CAAC,IAAIC,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAC5CC,KAAK,CAAC,CAAC,CAAC,IAAIC,IAAI,CAACE,GAAG,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAEhD;AAEA,SAASK,SAASA,CAACC,CAAQ,EAAEC,CAAQ,EAAEC,CAAQ;EAC7C,IAAMC,GAAG,GAAG,CAACF,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,KAAKE,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACA,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,KAAKE,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,CAAC;EACzE,IAAIE,GAAG,KAAK,CAAC,EAAE,OAAO,CAAC;EACvB,OAAOA,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACxB;AAEA,SAASC,WAAWA,CAACC,KAAW,EAAEC,KAAW;EAC3C,IAAMC,IAAI,GAAGR,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EACpD,IAAME,IAAI,GAAGT,SAAS,CAACM,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EACpD,IAAMG,IAAI,GAAGV,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC,CAAC;EACpD,IAAMK,IAAI,GAAGX,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAED,KAAK,CAAC,CAAC,CAAC,CAAC;EAEpD,IAAIE,IAAI,KAAKC,IAAI,IAAIC,IAAI,KAAKC,IAAI,EAAE,OAAO,IAAI;EAC/C,IAAIH,IAAI,KAAK,CAAC,IAAId,MAAM,CAACY,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;EACtD,IAAIE,IAAI,KAAK,CAAC,IAAIf,MAAM,CAACY,KAAK,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;EACtD,IAAIG,IAAI,KAAK,CAAC,IAAIhB,MAAM,CAACa,KAAK,EAAED,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;EACtD,IAAIK,IAAI,KAAK,CAAC,IAAIjB,MAAM,CAACa,KAAK,EAAED,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;EAEtD,OAAO,KAAK;AACd;AAEA,OAAM,SAAUM,sBAAsBA,CAACC,OAAgB,EAAEjB,KAAY;EACnE,IAAMkB,CAAC,GAAGD,OAAO,CAACE,MAAM;EACxB,IAAID,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK;EAEvB,IAAME,cAAc,GAAS,CAACpB,KAAK,EAAE,CAAC,IAAI,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,IAAIqB,KAAK,GAAG,CAAC;EACb,IAAIC,CAAC,GAAG,CAAC;EACT,GAAG;IACD,IAAMC,IAAI,GAAS,CAACN,OAAO,CAACK,CAAC,CAAC,EAAEL,OAAO,CAAC,CAACK,CAAC,GAAG,CAAC,IAAIJ,CAAC,CAAC,CAAC;IACrD,IAAIT,WAAW,CAACc,IAAI,EAAEH,cAAc,CAAC,EAAE;MACrC,IAAIhB,SAAS,CAACmB,IAAI,CAAC,CAAC,CAAC,EAAEvB,KAAK,EAAEuB,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,OAAOzB,MAAM,CAACyB,IAAI,EAAEvB,KAAK,CAAC;MACxEqB,KAAK,EAAE;IACT;IACAC,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,IAAIJ,CAAC;EACjB,CAAC,QAAQI,CAAC,KAAK,CAAC;EAEhB,OAAO,CAAC,EAAED,KAAK,GAAG,CAAC,CAAC;AACtB;AAEA,OAAM,SAAUG,mBAAmBA,CAACC,KAAc,EAAEC,KAAc;EAChE,OAAOA,KAAK,CAACC,KAAK,CAAC,UAAC3B,KAAK;IAAK,OAAAgB,sBAAsB,CAACS,KAAK,EAAEzB,KAAK,CAAC;EAApC,CAAoC,CAAC;AACrE;AAEA;;;AAGA,OAAM,SAAU4B,OAAOA,CAACC,SAAiB,EAAEC,KAAyB,EAAEC,MAAmB;EAC/E,IAAAC,EAAE,GAAiBH,SAAS,CAAAG,EAA1B;IAAEC,EAAE,GAAaJ,SAAS,CAAAI,EAAtB;IAAEC,EAAE,GAASL,SAAS,CAAAK,EAAlB;IAAEC,EAAE,GAAKN,SAAS,CAAAM,EAAd;EACtB,IAAMC,MAAM,GAAY,CACtB,CAACJ,EAAE,EAAEE,EAAE,CAAC,EACR,CAACD,EAAE,EAAEC,EAAE,CAAC,EACR,CAACD,EAAE,EAAEE,EAAE,CAAC,EACR,CAACH,EAAE,EAAEG,EAAE,CAAC,CACT;EAED,IAAME,OAAO,GAAGxC,SAAS,CAACiC,KAAK,EAAEC,MAAM,CAAC;EAExC,OAAOP,mBAAmB,CAACY,MAAM,EAAEC,OAAO,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}