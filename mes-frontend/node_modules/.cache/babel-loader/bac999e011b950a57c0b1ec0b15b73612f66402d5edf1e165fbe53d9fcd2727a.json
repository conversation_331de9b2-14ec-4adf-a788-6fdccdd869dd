{"ast": null, "code": "import { dispatch } from \"d3-dispatch\";\nimport { timer } from \"d3-timer\";\nimport lcg from \"./lcg.js\";\nvar MAX_DIMENSIONS = 3;\nexport function x(d) {\n  return d.x;\n}\nexport function y(d) {\n  return d.y;\n}\nexport function z(d) {\n  return d.z;\n}\nvar initialRadius = 10,\n  initialAngleRoll = Math.PI * (3 - Math.sqrt(5)),\n  // Golden ratio angle\n  initialAngleYaw = Math.PI * 20 / (9 + Math.sqrt(221)); // Markov irrational number\n\nexport default function (nodes, numDimensions) {\n  numDimensions = numDimensions || 2;\n  var nDim = Math.min(MAX_DIMENSIONS, Math.max(1, Math.round(numDimensions))),\n    simulation,\n    alpha = 1,\n    alphaMin = 0.001,\n    alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n    alphaTarget = 0,\n    velocityDecay = 0.6,\n    forces = new Map(),\n    stepper = timer(step),\n    event = dispatch(\"tick\", \"end\"),\n    random = lcg();\n  if (nodes == null) nodes = [];\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n  function tick(iterations) {\n    var i,\n      n = nodes.length,\n      node;\n    if (iterations === undefined) iterations = 1;\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n      forces.forEach(function (force) {\n        force(alpha);\n      });\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;else node.x = node.fx, node.vx = 0;\n        if (nDim > 1) {\n          if (node.fy == null) node.y += node.vy *= velocityDecay;else node.y = node.fy, node.vy = 0;\n        }\n        if (nDim > 2) {\n          if (node.fz == null) node.z += node.vz *= velocityDecay;else node.z = node.fz, node.vz = 0;\n        }\n      }\n    }\n    return simulation;\n  }\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (node.fz != null) node.z = node.fz;\n      if (isNaN(node.x) || nDim > 1 && isNaN(node.y) || nDim > 2 && isNaN(node.z)) {\n        var radius = initialRadius * (nDim > 2 ? Math.cbrt(0.5 + i) : nDim > 1 ? Math.sqrt(0.5 + i) : i),\n          rollAngle = i * initialAngleRoll,\n          yawAngle = i * initialAngleYaw;\n        if (nDim === 1) {\n          node.x = radius;\n        } else if (nDim === 2) {\n          node.x = radius * Math.cos(rollAngle);\n          node.y = radius * Math.sin(rollAngle);\n        } else {\n          // 3 dimensions: use spherical distribution along 2 irrational number angles\n          node.x = radius * Math.sin(rollAngle) * Math.cos(yawAngle);\n          node.y = radius * Math.cos(rollAngle);\n          node.z = radius * Math.sin(rollAngle) * Math.sin(yawAngle);\n        }\n      }\n      if (isNaN(node.vx) || nDim > 1 && isNaN(node.vy) || nDim > 2 && isNaN(node.vz)) {\n        node.vx = 0;\n        if (nDim > 1) {\n          node.vy = 0;\n        }\n        if (nDim > 2) {\n          node.vz = 0;\n        }\n      }\n    }\n  }\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random, nDim);\n    return force;\n  }\n  initializeNodes();\n  return simulation = {\n    tick: tick,\n    restart: function () {\n      return stepper.restart(step), simulation;\n    },\n    stop: function () {\n      return stepper.stop(), simulation;\n    },\n    numDimensions: function (_) {\n      return arguments.length ? (nDim = Math.min(MAX_DIMENSIONS, Math.max(1, Math.round(_))), forces.forEach(initializeForce), simulation) : nDim;\n    },\n    nodes: function (_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n    alpha: function (_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n    alphaMin: function (_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n    alphaDecay: function (_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n    alphaTarget: function (_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n    velocityDecay: function (_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n    randomSource: function (_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n    force: function (name, _) {\n      return arguments.length > 1 ? (_ == null ? forces.delete(name) : forces.set(name, initializeForce(_)), simulation) : forces.get(name);\n    },\n    find: function () {\n      var args = Array.prototype.slice.call(arguments);\n      var x = args.shift() || 0,\n        y = (nDim > 1 ? args.shift() : null) || 0,\n        z = (nDim > 2 ? args.shift() : null) || 0,\n        radius = args.shift() || Infinity;\n      var i = 0,\n        n = nodes.length,\n        dx,\n        dy,\n        dz,\n        d2,\n        node,\n        closest;\n      radius *= radius;\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - (node.y || 0);\n        dz = z - (node.z || 0);\n        d2 = dx * dx + dy * dy + dz * dz;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n      return closest;\n    },\n    on: function (name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}", "map": {"version": 3, "names": ["dispatch", "timer", "lcg", "MAX_DIMENSIONS", "x", "d", "y", "z", "initialRadius", "initialAngleRoll", "Math", "PI", "sqrt", "initialAngleYaw", "nodes", "numDimensions", "nDim", "min", "max", "round", "simulation", "alpha", "alphaMin", "alphaDecay", "pow", "alphaTarget", "velocityDecay", "forces", "Map", "stepper", "step", "event", "random", "tick", "call", "stop", "iterations", "i", "n", "length", "node", "undefined", "k", "for<PERSON>ach", "force", "fx", "vx", "fy", "vy", "fz", "vz", "initializeNodes", "index", "isNaN", "radius", "cbrt", "rollAngle", "yawAngle", "cos", "sin", "initializeForce", "initialize", "restart", "_", "arguments", "randomSource", "name", "delete", "set", "get", "find", "args", "Array", "prototype", "slice", "shift", "Infinity", "dx", "dy", "dz", "d2", "closest", "on"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force-3d/src/simulation.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {timer} from \"d3-timer\";\nimport lcg from \"./lcg.js\";\n\nvar MAX_DIMENSIONS = 3;\n\nexport function x(d) {\n  return d.x;\n}\n\nexport function y(d) {\n  return d.y;\n}\n\nexport function z(d) {\n  return d.z;\n}\n\nvar initialRadius = 10,\n    initialAngleRoll = Math.PI * (3 - Math.sqrt(5)), // Golden ratio angle\n    initialAngleYaw = Math.PI * 20 / (9 + Math.sqrt(221)); // Markov irrational number\n\nexport default function(nodes, numDimensions) {\n  numDimensions = numDimensions || 2;\n\n  var nDim = Math.min(MAX_DIMENSIONS, Math.max(1, Math.round(numDimensions))),\n      simulation,\n      alpha = 1,\n      alphaMin = 0.001,\n      alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n      alphaTarget = 0,\n      velocityDecay = 0.6,\n      forces = new Map(),\n      stepper = timer(step),\n      event = dispatch(\"tick\", \"end\"),\n      random = lcg();\n\n  if (nodes == null) nodes = [];\n\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n\n  function tick(iterations) {\n    var i, n = nodes.length, node;\n\n    if (iterations === undefined) iterations = 1;\n\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n\n      forces.forEach(function (force) {\n        force(alpha);\n      });\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;\n        else node.x = node.fx, node.vx = 0;\n        if (nDim > 1) {\n          if (node.fy == null) node.y += node.vy *= velocityDecay;\n          else node.y = node.fy, node.vy = 0;\n        }\n        if (nDim > 2) {\n          if (node.fz == null) node.z += node.vz *= velocityDecay;\n          else node.z = node.fz, node.vz = 0;\n        }\n      }\n    }\n\n    return simulation;\n  }\n\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (node.fz != null) node.z = node.fz;\n      if (isNaN(node.x) || (nDim > 1 && isNaN(node.y)) || (nDim > 2 && isNaN(node.z))) {\n        var radius = initialRadius * (nDim > 2 ? Math.cbrt(0.5 + i) : (nDim > 1 ? Math.sqrt(0.5 + i) : i)),\n          rollAngle = i * initialAngleRoll,\n          yawAngle = i * initialAngleYaw;\n\n        if (nDim === 1) {\n          node.x = radius;\n        } else if (nDim === 2) {\n          node.x = radius * Math.cos(rollAngle);\n          node.y = radius * Math.sin(rollAngle);\n        } else { // 3 dimensions: use spherical distribution along 2 irrational number angles\n          node.x = radius * Math.sin(rollAngle) * Math.cos(yawAngle);\n          node.y = radius * Math.cos(rollAngle);\n          node.z = radius * Math.sin(rollAngle) * Math.sin(yawAngle);\n        }\n      }\n      if (isNaN(node.vx) || (nDim > 1 && isNaN(node.vy)) || (nDim > 2 && isNaN(node.vz))) {\n        node.vx = 0;\n        if (nDim > 1) { node.vy = 0; }\n        if (nDim > 2) { node.vz = 0; }\n      }\n    }\n  }\n\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random, nDim);\n    return force;\n  }\n\n  initializeNodes();\n\n  return simulation = {\n    tick: tick,\n\n    restart: function() {\n      return stepper.restart(step), simulation;\n    },\n\n    stop: function() {\n      return stepper.stop(), simulation;\n    },\n\n    numDimensions: function(_) {\n      return arguments.length\n          ? (nDim = Math.min(MAX_DIMENSIONS, Math.max(1, Math.round(_))), forces.forEach(initializeForce), simulation)\n          : nDim;\n    },\n\n    nodes: function(_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n\n    alpha: function(_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n\n    alphaMin: function(_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n\n    alphaDecay: function(_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n\n    alphaTarget: function(_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n\n    velocityDecay: function(_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n\n    randomSource: function(_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n\n    force: function(name, _) {\n      return arguments.length > 1 ? ((_ == null ? forces.delete(name) : forces.set(name, initializeForce(_))), simulation) : forces.get(name);\n    },\n\n    find: function() {\n      var args = Array.prototype.slice.call(arguments);\n      var x = args.shift() || 0,\n          y = (nDim > 1 ? args.shift() : null) || 0,\n          z = (nDim > 2 ? args.shift() : null) || 0,\n          radius = args.shift() || Infinity;\n\n      var i = 0,\n          n = nodes.length,\n          dx,\n          dy,\n          dz,\n          d2,\n          node,\n          closest;\n\n      radius *= radius;\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - (node.y || 0);\n        dz = z - (node.z ||0);\n        d2 = dx * dx + dy * dy + dz * dz;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n\n      return closest;\n    },\n\n    on: function(name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,aAAa;AACpC,SAAQC,KAAK,QAAO,UAAU;AAC9B,OAAOC,GAAG,MAAM,UAAU;AAE1B,IAAIC,cAAc,GAAG,CAAC;AAEtB,OAAO,SAASC,CAACA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC,CAACD,CAAC;AACZ;AAEA,OAAO,SAASE,CAACA,CAACD,CAAC,EAAE;EACnB,OAAOA,CAAC,CAACC,CAAC;AACZ;AAEA,OAAO,SAASC,CAACA,CAACF,CAAC,EAAE;EACnB,OAAOA,CAAC,CAACE,CAAC;AACZ;AAEA,IAAIC,aAAa,GAAG,EAAE;EAClBC,gBAAgB,GAAGC,IAAI,CAACC,EAAE,IAAI,CAAC,GAAGD,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;EAAE;EACjDC,eAAe,GAAGH,IAAI,CAACC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAGD,IAAI,CAACE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE3D,eAAe,UAASE,KAAK,EAAEC,aAAa,EAAE;EAC5CA,aAAa,GAAGA,aAAa,IAAI,CAAC;EAElC,IAAIC,IAAI,GAAGN,IAAI,CAACO,GAAG,CAACd,cAAc,EAAEO,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAER,IAAI,CAACS,KAAK,CAACJ,aAAa,CAAC,CAAC,CAAC;IACvEK,UAAU;IACVC,KAAK,GAAG,CAAC;IACTC,QAAQ,GAAG,KAAK;IAChBC,UAAU,GAAG,CAAC,GAAGb,IAAI,CAACc,GAAG,CAACF,QAAQ,EAAE,CAAC,GAAG,GAAG,CAAC;IAC5CG,WAAW,GAAG,CAAC;IACfC,aAAa,GAAG,GAAG;IACnBC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClBC,OAAO,GAAG5B,KAAK,CAAC6B,IAAI,CAAC;IACrBC,KAAK,GAAG/B,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;IAC/BgC,MAAM,GAAG9B,GAAG,CAAC,CAAC;EAElB,IAAIY,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;EAE7B,SAASgB,IAAIA,CAAA,EAAG;IACdG,IAAI,CAAC,CAAC;IACNF,KAAK,CAACG,IAAI,CAAC,MAAM,EAAEd,UAAU,CAAC;IAC9B,IAAIC,KAAK,GAAGC,QAAQ,EAAE;MACpBO,OAAO,CAACM,IAAI,CAAC,CAAC;MACdJ,KAAK,CAACG,IAAI,CAAC,KAAK,EAAEd,UAAU,CAAC;IAC/B;EACF;EAEA,SAASa,IAAIA,CAACG,UAAU,EAAE;IACxB,IAAIC,CAAC;MAAEC,CAAC,GAAGxB,KAAK,CAACyB,MAAM;MAAEC,IAAI;IAE7B,IAAIJ,UAAU,KAAKK,SAAS,EAAEL,UAAU,GAAG,CAAC;IAE5C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,UAAU,EAAE,EAAEM,CAAC,EAAE;MACnCrB,KAAK,IAAI,CAACI,WAAW,GAAGJ,KAAK,IAAIE,UAAU;MAE3CI,MAAM,CAACgB,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC9BA,KAAK,CAACvB,KAAK,CAAC;MACd,CAAC,CAAC;MAEF,KAAKgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtBG,IAAI,GAAG1B,KAAK,CAACuB,CAAC,CAAC;QACf,IAAIG,IAAI,CAACK,EAAE,IAAI,IAAI,EAAEL,IAAI,CAACpC,CAAC,IAAIoC,IAAI,CAACM,EAAE,IAAIpB,aAAa,CAAC,KACnDc,IAAI,CAACpC,CAAC,GAAGoC,IAAI,CAACK,EAAE,EAAEL,IAAI,CAACM,EAAE,GAAG,CAAC;QAClC,IAAI9B,IAAI,GAAG,CAAC,EAAE;UACZ,IAAIwB,IAAI,CAACO,EAAE,IAAI,IAAI,EAAEP,IAAI,CAAClC,CAAC,IAAIkC,IAAI,CAACQ,EAAE,IAAItB,aAAa,CAAC,KACnDc,IAAI,CAAClC,CAAC,GAAGkC,IAAI,CAACO,EAAE,EAAEP,IAAI,CAACQ,EAAE,GAAG,CAAC;QACpC;QACA,IAAIhC,IAAI,GAAG,CAAC,EAAE;UACZ,IAAIwB,IAAI,CAACS,EAAE,IAAI,IAAI,EAAET,IAAI,CAACjC,CAAC,IAAIiC,IAAI,CAACU,EAAE,IAAIxB,aAAa,CAAC,KACnDc,IAAI,CAACjC,CAAC,GAAGiC,IAAI,CAACS,EAAE,EAAET,IAAI,CAACU,EAAE,GAAG,CAAC;QACpC;MACF;IACF;IAEA,OAAO9B,UAAU;EACnB;EAEA,SAAS+B,eAAeA,CAAA,EAAG;IACzB,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGxB,KAAK,CAACyB,MAAM,EAAEC,IAAI,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAClDG,IAAI,GAAG1B,KAAK,CAACuB,CAAC,CAAC,EAAEG,IAAI,CAACY,KAAK,GAAGf,CAAC;MAC/B,IAAIG,IAAI,CAACK,EAAE,IAAI,IAAI,EAAEL,IAAI,CAACpC,CAAC,GAAGoC,IAAI,CAACK,EAAE;MACrC,IAAIL,IAAI,CAACO,EAAE,IAAI,IAAI,EAAEP,IAAI,CAAClC,CAAC,GAAGkC,IAAI,CAACO,EAAE;MACrC,IAAIP,IAAI,CAACS,EAAE,IAAI,IAAI,EAAET,IAAI,CAACjC,CAAC,GAAGiC,IAAI,CAACS,EAAE;MACrC,IAAII,KAAK,CAACb,IAAI,CAACpC,CAAC,CAAC,IAAKY,IAAI,GAAG,CAAC,IAAIqC,KAAK,CAACb,IAAI,CAAClC,CAAC,CAAE,IAAKU,IAAI,GAAG,CAAC,IAAIqC,KAAK,CAACb,IAAI,CAACjC,CAAC,CAAE,EAAE;QAC/E,IAAI+C,MAAM,GAAG9C,aAAa,IAAIQ,IAAI,GAAG,CAAC,GAAGN,IAAI,CAAC6C,IAAI,CAAC,GAAG,GAAGlB,CAAC,CAAC,GAAIrB,IAAI,GAAG,CAAC,GAAGN,IAAI,CAACE,IAAI,CAAC,GAAG,GAAGyB,CAAC,CAAC,GAAGA,CAAE,CAAC;UAChGmB,SAAS,GAAGnB,CAAC,GAAG5B,gBAAgB;UAChCgD,QAAQ,GAAGpB,CAAC,GAAGxB,eAAe;QAEhC,IAAIG,IAAI,KAAK,CAAC,EAAE;UACdwB,IAAI,CAACpC,CAAC,GAAGkD,MAAM;QACjB,CAAC,MAAM,IAAItC,IAAI,KAAK,CAAC,EAAE;UACrBwB,IAAI,CAACpC,CAAC,GAAGkD,MAAM,GAAG5C,IAAI,CAACgD,GAAG,CAACF,SAAS,CAAC;UACrChB,IAAI,CAAClC,CAAC,GAAGgD,MAAM,GAAG5C,IAAI,CAACiD,GAAG,CAACH,SAAS,CAAC;QACvC,CAAC,MAAM;UAAE;UACPhB,IAAI,CAACpC,CAAC,GAAGkD,MAAM,GAAG5C,IAAI,CAACiD,GAAG,CAACH,SAAS,CAAC,GAAG9C,IAAI,CAACgD,GAAG,CAACD,QAAQ,CAAC;UAC1DjB,IAAI,CAAClC,CAAC,GAAGgD,MAAM,GAAG5C,IAAI,CAACgD,GAAG,CAACF,SAAS,CAAC;UACrChB,IAAI,CAACjC,CAAC,GAAG+C,MAAM,GAAG5C,IAAI,CAACiD,GAAG,CAACH,SAAS,CAAC,GAAG9C,IAAI,CAACiD,GAAG,CAACF,QAAQ,CAAC;QAC5D;MACF;MACA,IAAIJ,KAAK,CAACb,IAAI,CAACM,EAAE,CAAC,IAAK9B,IAAI,GAAG,CAAC,IAAIqC,KAAK,CAACb,IAAI,CAACQ,EAAE,CAAE,IAAKhC,IAAI,GAAG,CAAC,IAAIqC,KAAK,CAACb,IAAI,CAACU,EAAE,CAAE,EAAE;QAClFV,IAAI,CAACM,EAAE,GAAG,CAAC;QACX,IAAI9B,IAAI,GAAG,CAAC,EAAE;UAAEwB,IAAI,CAACQ,EAAE,GAAG,CAAC;QAAE;QAC7B,IAAIhC,IAAI,GAAG,CAAC,EAAE;UAAEwB,IAAI,CAACU,EAAE,GAAG,CAAC;QAAE;MAC/B;IACF;EACF;EAEA,SAASU,eAAeA,CAAChB,KAAK,EAAE;IAC9B,IAAIA,KAAK,CAACiB,UAAU,EAAEjB,KAAK,CAACiB,UAAU,CAAC/C,KAAK,EAAEkB,MAAM,EAAEhB,IAAI,CAAC;IAC3D,OAAO4B,KAAK;EACd;EAEAO,eAAe,CAAC,CAAC;EAEjB,OAAO/B,UAAU,GAAG;IAClBa,IAAI,EAAEA,IAAI;IAEV6B,OAAO,EAAE,SAAAA,CAAA,EAAW;MAClB,OAAOjC,OAAO,CAACiC,OAAO,CAAChC,IAAI,CAAC,EAAEV,UAAU;IAC1C,CAAC;IAEDe,IAAI,EAAE,SAAAA,CAAA,EAAW;MACf,OAAON,OAAO,CAACM,IAAI,CAAC,CAAC,EAAEf,UAAU;IACnC,CAAC;IAEDL,aAAa,EAAE,SAAAA,CAASgD,CAAC,EAAE;MACzB,OAAOC,SAAS,CAACzB,MAAM,IAChBvB,IAAI,GAAGN,IAAI,CAACO,GAAG,CAACd,cAAc,EAAEO,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAER,IAAI,CAACS,KAAK,CAAC4C,CAAC,CAAC,CAAC,CAAC,EAAEpC,MAAM,CAACgB,OAAO,CAACiB,eAAe,CAAC,EAAExC,UAAU,IACzGJ,IAAI;IACZ,CAAC;IAEDF,KAAK,EAAE,SAAAA,CAASiD,CAAC,EAAE;MACjB,OAAOC,SAAS,CAACzB,MAAM,IAAIzB,KAAK,GAAGiD,CAAC,EAAEZ,eAAe,CAAC,CAAC,EAAExB,MAAM,CAACgB,OAAO,CAACiB,eAAe,CAAC,EAAExC,UAAU,IAAIN,KAAK;IAC/G,CAAC;IAEDO,KAAK,EAAE,SAAAA,CAAS0C,CAAC,EAAE;MACjB,OAAOC,SAAS,CAACzB,MAAM,IAAIlB,KAAK,GAAG,CAAC0C,CAAC,EAAE3C,UAAU,IAAIC,KAAK;IAC5D,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAASyC,CAAC,EAAE;MACpB,OAAOC,SAAS,CAACzB,MAAM,IAAIjB,QAAQ,GAAG,CAACyC,CAAC,EAAE3C,UAAU,IAAIE,QAAQ;IAClE,CAAC;IAEDC,UAAU,EAAE,SAAAA,CAASwC,CAAC,EAAE;MACtB,OAAOC,SAAS,CAACzB,MAAM,IAAIhB,UAAU,GAAG,CAACwC,CAAC,EAAE3C,UAAU,IAAI,CAACG,UAAU;IACvE,CAAC;IAEDE,WAAW,EAAE,SAAAA,CAASsC,CAAC,EAAE;MACvB,OAAOC,SAAS,CAACzB,MAAM,IAAId,WAAW,GAAG,CAACsC,CAAC,EAAE3C,UAAU,IAAIK,WAAW;IACxE,CAAC;IAEDC,aAAa,EAAE,SAAAA,CAASqC,CAAC,EAAE;MACzB,OAAOC,SAAS,CAACzB,MAAM,IAAIb,aAAa,GAAG,CAAC,GAAGqC,CAAC,EAAE3C,UAAU,IAAI,CAAC,GAAGM,aAAa;IACnF,CAAC;IAEDuC,YAAY,EAAE,SAAAA,CAASF,CAAC,EAAE;MACxB,OAAOC,SAAS,CAACzB,MAAM,IAAIP,MAAM,GAAG+B,CAAC,EAAEpC,MAAM,CAACgB,OAAO,CAACiB,eAAe,CAAC,EAAExC,UAAU,IAAIY,MAAM;IAC9F,CAAC;IAEDY,KAAK,EAAE,SAAAA,CAASsB,IAAI,EAAEH,CAAC,EAAE;MACvB,OAAOC,SAAS,CAACzB,MAAM,GAAG,CAAC,IAAKwB,CAAC,IAAI,IAAI,GAAGpC,MAAM,CAACwC,MAAM,CAACD,IAAI,CAAC,GAAGvC,MAAM,CAACyC,GAAG,CAACF,IAAI,EAAEN,eAAe,CAACG,CAAC,CAAC,CAAC,EAAG3C,UAAU,IAAIO,MAAM,CAAC0C,GAAG,CAACH,IAAI,CAAC;IACzI,CAAC;IAEDI,IAAI,EAAE,SAAAA,CAAA,EAAW;MACf,IAAIC,IAAI,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACxC,IAAI,CAAC8B,SAAS,CAAC;MAChD,IAAI5D,CAAC,GAAGmE,IAAI,CAACI,KAAK,CAAC,CAAC,IAAI,CAAC;QACrBrE,CAAC,GAAG,CAACU,IAAI,GAAG,CAAC,GAAGuD,IAAI,CAACI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;QACzCpE,CAAC,GAAG,CAACS,IAAI,GAAG,CAAC,GAAGuD,IAAI,CAACI,KAAK,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;QACzCrB,MAAM,GAAGiB,IAAI,CAACI,KAAK,CAAC,CAAC,IAAIC,QAAQ;MAErC,IAAIvC,CAAC,GAAG,CAAC;QACLC,CAAC,GAAGxB,KAAK,CAACyB,MAAM;QAChBsC,EAAE;QACFC,EAAE;QACFC,EAAE;QACFC,EAAE;QACFxC,IAAI;QACJyC,OAAO;MAEX3B,MAAM,IAAIA,MAAM;MAEhB,KAAKjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtBG,IAAI,GAAG1B,KAAK,CAACuB,CAAC,CAAC;QACfwC,EAAE,GAAGzE,CAAC,GAAGoC,IAAI,CAACpC,CAAC;QACf0E,EAAE,GAAGxE,CAAC,IAAIkC,IAAI,CAAClC,CAAC,IAAI,CAAC,CAAC;QACtByE,EAAE,GAAGxE,CAAC,IAAIiC,IAAI,CAACjC,CAAC,IAAG,CAAC,CAAC;QACrByE,EAAE,GAAGH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;QAChC,IAAIC,EAAE,GAAG1B,MAAM,EAAE2B,OAAO,GAAGzC,IAAI,EAAEc,MAAM,GAAG0B,EAAE;MAC9C;MAEA,OAAOC,OAAO;IAChB,CAAC;IAEDC,EAAE,EAAE,SAAAA,CAAShB,IAAI,EAAEH,CAAC,EAAE;MACpB,OAAOC,SAAS,CAACzB,MAAM,GAAG,CAAC,IAAIR,KAAK,CAACmD,EAAE,CAAChB,IAAI,EAAEH,CAAC,CAAC,EAAE3C,UAAU,IAAIW,KAAK,CAACmD,EAAE,CAAChB,IAAI,CAAC;IAChF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}