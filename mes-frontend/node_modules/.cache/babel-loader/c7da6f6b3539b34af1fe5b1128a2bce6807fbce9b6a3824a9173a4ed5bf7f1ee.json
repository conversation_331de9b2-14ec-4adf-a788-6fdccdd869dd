{"ast": null, "code": "export { getDefaultStyle as top } from './default';", "map": {"version": 3, "names": ["getDefaultStyle", "top"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/top.ts"], "sourcesContent": ["export { getDefaultStyle as top } from './default';\n"], "mappings": "AAAA,SAASA,eAAe,IAAIC,GAAG,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}