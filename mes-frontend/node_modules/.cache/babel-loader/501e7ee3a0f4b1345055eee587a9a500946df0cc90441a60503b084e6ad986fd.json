{"ast": null, "code": "var isArrayLike = require('./isArrayLike'),\n  isObjectLike = require('./isObjectLike');\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\nmodule.exports = isArrayLikeObject;", "map": {"version": 3, "names": ["isArrayLike", "require", "isObjectLike", "isArrayLikeObject", "value", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/isArrayLikeObject.js"], "sourcesContent": ["var isArrayLike = require('./isArrayLike'),\n    isObjectLike = require('./isObjectLike');\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\nmodule.exports = isArrayLikeObject;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC;EACtCC,YAAY,GAAGD,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACC,KAAK,EAAE;EAChC,OAAOF,YAAY,CAACE,KAAK,CAAC,IAAIJ,WAAW,CAACI,KAAK,CAAC;AAClD;AAEAC,MAAM,CAACC,OAAO,GAAGH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}