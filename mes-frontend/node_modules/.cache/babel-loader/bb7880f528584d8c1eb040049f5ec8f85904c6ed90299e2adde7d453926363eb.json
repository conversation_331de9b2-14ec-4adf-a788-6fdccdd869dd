{"ast": null, "code": "export default function (a, b) {\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n  if (a.invert && b.invert) compose.invert = function (x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n  return compose;\n}", "map": {"version": 3, "names": ["a", "b", "compose", "x", "y", "invert"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-geo/src/compose.js"], "sourcesContent": ["export default function(a, b) {\n\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n\n  if (a.invert && b.invert) compose.invert = function(x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n\n  return compose;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAE5B,SAASC,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACrB,OAAOD,CAAC,GAAGH,CAAC,CAACG,CAAC,EAAEC,CAAC,CAAC,EAAEH,CAAC,CAACE,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;EACnC;EAEA,IAAIH,CAAC,CAACK,MAAM,IAAIJ,CAAC,CAACI,MAAM,EAAEH,OAAO,CAACG,MAAM,GAAG,UAASF,CAAC,EAAEC,CAAC,EAAE;IACxD,OAAOD,CAAC,GAAGF,CAAC,CAACI,MAAM,CAACF,CAAC,EAAEC,CAAC,CAAC,EAAED,CAAC,IAAIH,CAAC,CAACK,MAAM,CAACF,CA<PERSON>,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC;EAED,OAAOD,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}