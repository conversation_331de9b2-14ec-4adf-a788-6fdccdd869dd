{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { get } from '@antv/util';\nimport { transition } from '../../../animation';\nimport { degToRad, keyframeInterpolate, omit, renderExtDo, scaleToPixel, subStyleProps } from '../../../util';\nimport { CLASS_NAMES } from '../constant';\nimport { getLineAngle, getLineTangentVector } from './utils';\nexport function getLinearValuePos(value, attr) {\n  var _a = __read(attr.startPos, 2),\n    sx = _a[0],\n    sy = _a[1],\n    _b = __read(attr.endPos, 2),\n    ex = _b[0],\n    ey = _b[1];\n  var _c = __read([ex - sx, ey - sy], 2),\n    dx = _c[0],\n    dy = _c[1];\n  return [sx + dx * value, sy + dy * value];\n}\nexport function getArcValuePos(value, attr) {\n  var radius = attr.radius,\n    _a = __read(attr.center, 2),\n    cx = _a[0],\n    cy = _a[1];\n  var angle = degToRad(getLineAngle(value, attr));\n  return [cx + radius * Math.cos(angle), cy + radius * Math.sin(angle)];\n}\nexport function getValuePos(value, attr) {\n  if (attr.type === 'linear') return getLinearValuePos(value, attr);\n  return getArcValuePos(value, attr);\n}\nexport function isAxisHorizontal(attr) {\n  return getLineTangentVector(0, attr)[1] === 0;\n}\nexport function isAxisVertical(attr) {\n  return getLineTangentVector(0, attr)[0] === 0;\n}\nfunction isCircle(startAngle, endAngle) {\n  return endAngle - startAngle === 360;\n}\nfunction getArcPath(startAngle, endAngle, cx, cy, radius) {\n  var diffAngle = endAngle - startAngle;\n  var _a = __read([radius, radius], 2),\n    rx = _a[0],\n    ry = _a[1];\n  var _b = __read([degToRad(startAngle), degToRad(endAngle)], 2),\n    startAngleRadians = _b[0],\n    endAngleRadians = _b[1];\n  var getPosByAngle = function (angle) {\n    return [cx + radius * Math.cos(angle), cy + radius * Math.sin(angle)];\n  };\n  var _c = __read(getPosByAngle(startAngleRadians), 2),\n    x1 = _c[0],\n    y1 = _c[1];\n  var _d = __read(getPosByAngle(endAngleRadians), 2),\n    x2 = _d[0],\n    y2 = _d[1];\n  if (isCircle(startAngle, endAngle)) {\n    var middleAngleRadians = (endAngleRadians + startAngleRadians) / 2;\n    var _e = __read(getPosByAngle(middleAngleRadians), 2),\n      xm = _e[0],\n      ym = _e[1];\n    return [['M', x1, y1], ['A', rx, ry, 0, 1, 0, xm, ym], ['A', rx, ry, 0, 1, 0, x2, y2]];\n  }\n  // 大小弧\n  var large = diffAngle > 180 ? 1 : 0;\n  // 1-顺时针 0-逆时针\n  var sweep = startAngle > endAngle ? 0 : 1;\n  var isClosePath = false;\n  return isClosePath ? \"M\".concat(cx, \",\").concat(cy, \",L\").concat(x1, \",\").concat(y1, \",A\").concat(rx, \",\").concat(ry, \",0,\").concat(large, \",\").concat(sweep, \",\").concat(x2, \",\").concat(y2, \",L\").concat(cx, \",\").concat(cy) : \"M\".concat(x1, \",\").concat(y1, \",A\").concat(rx, \",\").concat(ry, \",0,\").concat(large, \",\").concat(sweep, \",\").concat(x2, \",\").concat(y2);\n}\nfunction getArcAttr(arc) {\n  var _a = arc.attributes,\n    startAngle = _a.startAngle,\n    endAngle = _a.endAngle,\n    center = _a.center,\n    radius = _a.radius;\n  return __spreadArray(__spreadArray([startAngle, endAngle], __read(center), false), [radius], false);\n}\nfunction renderArc(container, attr, style, animate) {\n  var startAngle = attr.startAngle,\n    endAngle = attr.endAngle,\n    center = attr.center,\n    radius = attr.radius;\n  return container.selectAll(CLASS_NAMES.line.class).data([{\n    d: getArcPath.apply(void 0, __spreadArray(__spreadArray([startAngle, endAngle], __read(center), false), [radius], false))\n  }], function (d, i) {\n    return i;\n  }).join(function (enter) {\n    return enter.append('path').attr('className', CLASS_NAMES.line.name).styles(attr).styles({\n      d: function (d) {\n        return d.d;\n      }\n    });\n  }, function (update) {\n    return update.transition(function () {\n      var _this = this;\n      var animation = keyframeInterpolate(this, getArcAttr(this), __spreadArray(__spreadArray([startAngle, endAngle], __read(center), false), [radius], false), animate.update);\n      if (animation) {\n        var layout = function () {\n          var data = get(_this.attributes, '__keyframe_data__');\n          _this.style.d = getArcPath.apply(void 0, __spreadArray([], __read(data), false));\n        };\n        animation.onframe = layout;\n        animation.onfinish = layout;\n      }\n      return animation;\n    }).styles(attr);\n  }, function (exit) {\n    return exit.remove();\n  }).styles(style).transitions();\n}\nfunction renderTruncation(container, _a) {\n  var truncRange = _a.truncRange,\n    truncShape = _a.truncShape,\n    lineExtension = _a.lineExtension;\n  // TODO\n}\nfunction extendLine(startPos, endPos, range) {\n  if (range === void 0) {\n    range = [0, 0];\n  }\n  var _a = __read([startPos, endPos, range], 3),\n    _b = __read(_a[0], 2),\n    x1 = _b[0],\n    y1 = _b[1],\n    _c = __read(_a[1], 2),\n    x2 = _c[0],\n    y2 = _c[1],\n    _d = __read(_a[2], 2),\n    l1 = _d[0],\n    l2 = _d[1];\n  var _e = __read([x2 - x1, y2 - y1], 2),\n    x = _e[0],\n    y = _e[1];\n  var L = Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2));\n  var _f = __read([-l1 / L, l2 / L], 2),\n    s1 = _f[0],\n    s2 = _f[1];\n  return [s1 * x, s1 * y, s2 * x, s2 * y];\n}\nfunction getLinePath(points) {\n  var _a = __read(points, 2),\n    _b = __read(_a[0], 2),\n    x1 = _b[0],\n    y1 = _b[1],\n    _c = __read(_a[1], 2),\n    x2 = _c[0],\n    y2 = _c[1];\n  return {\n    x1: x1,\n    y1: y1,\n    x2: x2,\n    y2: y2\n  };\n}\nfunction renderLinear(container, attr, style, animate) {\n  var showTrunc = attr.showTrunc,\n    startPos = attr.startPos,\n    endPos = attr.endPos,\n    truncRange = attr.truncRange,\n    lineExtension = attr.lineExtension;\n  var _a = __read([startPos, endPos], 2),\n    _b = __read(_a[0], 2),\n    x1 = _b[0],\n    y1 = _b[1],\n    _c = __read(_a[1], 2),\n    x2 = _c[0],\n    y2 = _c[1];\n  var _d = __read(lineExtension ? extendLine(startPos, endPos, lineExtension) : new Array(4).fill(0), 4),\n    ox1 = _d[0],\n    oy1 = _d[1],\n    ox2 = _d[2],\n    oy2 = _d[3];\n  var renderLine = function (data) {\n    return container.selectAll(CLASS_NAMES.line.class).data(data, function (d, i) {\n      return i;\n    }).join(function (enter) {\n      return enter.append('line').attr('className', function (d) {\n        return \"\".concat(CLASS_NAMES.line.name, \" \").concat(d.className);\n      }).styles(style).transition(function (d) {\n        return transition(this, getLinePath(d.line), false);\n      });\n    }, function (update) {\n      return update.styles(style).transition(function (_a) {\n        var line = _a.line;\n        return transition(this, getLinePath(line), animate.update);\n      });\n    }, function (exit) {\n      return exit.remove();\n    }).transitions();\n  };\n  if (!showTrunc || !truncRange) {\n    return renderLine([{\n      line: [[x1 + ox1, y1 + oy1], [x2 + ox2, y2 + oy2]],\n      className: CLASS_NAMES.line.name\n    }]);\n  }\n  var _e = __read(truncRange, 2),\n    r1 = _e[0],\n    r2 = _e[1];\n  var dx = x2 - x1;\n  var dy = y2 - y1;\n  var _f = __read([x1 + dx * r1, y1 + dy * r1], 2),\n    x3 = _f[0],\n    y3 = _f[1];\n  var _g = __read([x1 + dx * r2, y1 + dy * r2], 2),\n    x4 = _g[0],\n    y4 = _g[1];\n  var animation = renderLine([{\n    line: [[x1 + ox1, y1 + oy1], [x3, y3]],\n    className: CLASS_NAMES.lineFirst.name\n  }, {\n    line: [[x4, y4], [x2 + ox2, y2 + oy2]],\n    className: CLASS_NAMES.lineSecond.name\n  }]);\n  renderTruncation(container, attr);\n  return animation;\n}\nfunction renderAxisArrow(container, type, attr, style) {\n  var showArrow = attr.showArrow,\n    showTrunc = attr.showTrunc,\n    lineArrow = attr.lineArrow,\n    lineArrowOffset = attr.lineArrowOffset,\n    lineArrowSize = attr.lineArrowSize;\n  var shapeToAddArrow;\n  if (type === 'arc') shapeToAddArrow = container.select(CLASS_NAMES.line.class);else if (showTrunc) shapeToAddArrow = container.select(CLASS_NAMES.lineSecond.class);else shapeToAddArrow = container.select(CLASS_NAMES.line.class);\n  if (!showArrow || !lineArrow || attr.type === 'arc' && isCircle(attr.startAngle, attr.endAngle)) {\n    var node = shapeToAddArrow.node();\n    if (node) node.style.markerEnd = undefined;\n    return;\n  }\n  var arrow = renderExtDo(lineArrow);\n  arrow.attr(style);\n  scaleToPixel(arrow, lineArrowSize, true);\n  shapeToAddArrow.style('markerEnd', arrow).style('markerEndOffset', -lineArrowOffset);\n}\nexport function renderAxisLine(container, attr, animate) {\n  var type = attr.type;\n  var animation;\n  var style = subStyleProps(attr, 'line');\n  if (type === 'linear') animation = renderLinear(container, attr, omit(style, 'arrow'), animate);else animation = renderArc(container, attr, omit(style, 'arrow'), animate);\n  renderAxisArrow(container, type, attr, style);\n  return animation;\n}", "map": {"version": 3, "names": ["get", "transition", "degToRad", "keyframeInterpolate", "omit", "renderExtDo", "scaleToPixel", "subStyleProps", "CLASS_NAMES", "getLineAngle", "getLineTangentVector", "getLinearValuePos", "value", "attr", "_a", "__read", "startPos", "sx", "sy", "_b", "endPos", "ex", "ey", "_c", "dx", "dy", "getArcValuePos", "radius", "center", "cx", "cy", "angle", "Math", "cos", "sin", "getValuePos", "type", "isAxisHorizontal", "isAxisVertical", "isCircle", "startAngle", "endAngle", "getArcPath", "diffAngle", "rx", "ry", "startAngleRadians", "endAngleRadians", "getPosByAngle", "x1", "y1", "_d", "x2", "y2", "middleAngleRadians", "_e", "xm", "ym", "large", "sweep", "isClosePath", "concat", "getArcAttr", "arc", "attributes", "__spread<PERSON><PERSON>y", "renderArc", "container", "style", "animate", "selectAll", "line", "class", "data", "d", "apply", "i", "join", "enter", "append", "name", "styles", "update", "_this", "animation", "layout", "onframe", "onfinish", "exit", "remove", "transitions", "renderTruncation", "truncRange", "truncShape", "lineExtension", "extendLine", "range", "l1", "l2", "x", "y", "L", "sqrt", "pow", "_f", "s1", "s2", "get<PERSON>inePath", "points", "renderLinear", "showTrunc", "Array", "fill", "ox1", "oy1", "ox2", "oy2", "renderLine", "className", "r1", "r2", "x3", "y3", "_g", "x4", "y4", "lineFirst", "lineSecond", "renderAxisArrow", "showArrow", "lineArrow", "lineArrowOffset", "lineArrowSize", "shapeToAddArrow", "select", "node", "markerEnd", "undefined", "arrow", "renderAxisLine"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/guides/line.ts"], "sourcesContent": ["import { get } from '@antv/util';\nimport type { AnimationResult, StandardAnimationOption } from '../../../animation';\nimport { transition } from '../../../animation';\nimport type { DisplayObject, Line } from '../../../shapes';\nimport type { Point, Vector2 } from '../../../types';\nimport {\n  Selection,\n  degToRad,\n  keyframeInterpolate,\n  omit,\n  renderExtDo,\n  scaleToPixel,\n  subStyleProps,\n} from '../../../util';\nimport { CLASS_NAMES } from '../constant';\nimport type { RequiredArcAxisStyleProps, RequiredAxisStyleProps, RequiredLinearAxisStyleProps } from '../types';\nimport { getLineAngle, getLineTangentVector } from './utils';\n\ntype LineDatum = {\n  line: [Vector2, Vector2];\n  className: string;\n};\n\nexport function getLinearValuePos(value: number, attr: RequiredLinearAxisStyleProps): Vector2 {\n  const {\n    startPos: [sx, sy],\n    endPos: [ex, ey],\n  } = attr;\n  const [dx, dy] = [ex - sx, ey - sy];\n  return [sx + dx * value, sy + dy * value];\n}\n\nexport function getArcValuePos(value: number, attr: RequiredArcAxisStyleProps): Vector2 {\n  const {\n    radius,\n    center: [cx, cy],\n  } = attr;\n  const angle = degToRad(getLineAngle(value, attr));\n  return [cx + radius * Math.cos(angle), cy + radius * Math.sin(angle)];\n}\n\nexport function getValuePos(value: number, attr: RequiredAxisStyleProps) {\n  if (attr.type === 'linear') return getLinearValuePos(value, attr);\n  return getArcValuePos(value, attr);\n}\n\nexport function isAxisHorizontal(attr: RequiredLinearAxisStyleProps): boolean {\n  return getLineTangentVector(0, attr)[1] === 0;\n}\n\nexport function isAxisVertical(attr: RequiredLinearAxisStyleProps): boolean {\n  return getLineTangentVector(0, attr)[0] === 0;\n}\n\nfunction isCircle(startAngle: number, endAngle: number) {\n  return endAngle - startAngle === 360;\n}\n\nfunction getArcPath(startAngle: number, endAngle: number, cx: number, cy: number, radius: number) {\n  const diffAngle = endAngle - startAngle;\n  const [rx, ry] = [radius, radius];\n  const [startAngleRadians, endAngleRadians] = [degToRad(startAngle), degToRad(endAngle)];\n  const getPosByAngle = (angle: number) => [cx + radius * Math.cos(angle), cy + radius * Math.sin(angle)];\n\n  const [x1, y1] = getPosByAngle(startAngleRadians);\n  const [x2, y2] = getPosByAngle(endAngleRadians);\n\n  if (isCircle(startAngle, endAngle)) {\n    const middleAngleRadians = (endAngleRadians + startAngleRadians) / 2;\n    const [xm, ym] = getPosByAngle(middleAngleRadians);\n    return [\n      ['M', x1, y1],\n      ['A', rx, ry, 0, 1, 0, xm, ym],\n      ['A', rx, ry, 0, 1, 0, x2, y2],\n    ];\n  }\n\n  // 大小弧\n  const large = diffAngle > 180 ? 1 : 0;\n  // 1-顺时针 0-逆时针\n  const sweep = startAngle > endAngle ? 0 : 1;\n  const isClosePath = false;\n\n  return isClosePath\n    ? `M${cx},${cy},L${x1},${y1},A${rx},${ry},0,${large},${sweep},${x2},${y2},L${cx},${cy}`\n    : `M${x1},${y1},A${rx},${ry},0,${large},${sweep},${x2},${y2}`;\n}\n\nfunction getArcAttr(arc: DisplayObject) {\n  const { startAngle, endAngle, center, radius } = arc.attributes;\n  return [startAngle, endAngle, ...center, radius];\n}\n\nfunction renderArc(\n  container: Selection,\n  attr: RequiredArcAxisStyleProps,\n  style: RequiredArcAxisStyleProps,\n  animate: StandardAnimationOption\n) {\n  const { startAngle, endAngle, center, radius } = attr;\n\n  return container\n    .selectAll(CLASS_NAMES.line.class)\n    .data([{ d: getArcPath(startAngle, endAngle, ...center, radius) }], (d, i) => i)\n    .join(\n      (enter) =>\n        enter\n          .append('path')\n          .attr('className', CLASS_NAMES.line.name)\n          .styles(attr)\n          .styles({ d: (d: any) => d.d }),\n      (update) =>\n        update\n          .transition(function () {\n            const animation = keyframeInterpolate(\n              this,\n              getArcAttr(this),\n              [startAngle, endAngle, ...center, radius],\n              animate.update\n            );\n            if (animation) {\n              const layout = () => {\n                const data = get(this.attributes, '__keyframe_data__') as Parameters<typeof getArcPath>;\n                this.style.d = getArcPath(...data);\n              };\n              animation.onframe = layout;\n              animation.onfinish = layout;\n            }\n            return animation;\n          })\n          .styles(attr),\n      (exit) => exit.remove()\n    )\n    .styles(style)\n    .transitions();\n}\n\nfunction renderTruncation<T>(container: Selection, { truncRange, truncShape, lineExtension }: RequiredAxisStyleProps) {\n  // TODO\n}\n\nfunction extendLine(startPos: Point, endPos: Point, range: [number, number] = [0, 0]) {\n  const [[x1, y1], [x2, y2], [l1, l2]] = [startPos, endPos, range];\n  const [x, y] = [x2 - x1, y2 - y1];\n  const L = Math.sqrt(x ** 2 + y ** 2);\n  const [s1, s2] = [-l1 / L, l2 / L];\n  return [s1 * x, s1 * y, s2 * x, s2 * y];\n}\n\nfunction getLinePath(points: [Vector2, Vector2]) {\n  const [[x1, y1], [x2, y2]] = points;\n  return { x1, y1, x2, y2 };\n}\n\nfunction renderLinear(\n  container: Selection,\n  attr: RequiredLinearAxisStyleProps,\n  style: RequiredLinearAxisStyleProps,\n  animate: StandardAnimationOption\n) {\n  const { showTrunc, startPos, endPos, truncRange, lineExtension } = attr;\n  const [[x1, y1], [x2, y2]] = [startPos, endPos];\n  const [ox1, oy1, ox2, oy2] = lineExtension ? extendLine(startPos, endPos, lineExtension) : new Array(4).fill(0);\n  const renderLine = (data: LineDatum[]) => {\n    return container\n      .selectAll(CLASS_NAMES.line.class)\n      .data(data, (d, i) => i)\n      .join(\n        (enter) =>\n          enter\n            .append('line')\n            .attr('className', (d: LineDatum) => `${CLASS_NAMES.line.name} ${d.className}`)\n            .styles(style)\n            .transition(function (d: LineDatum) {\n              return transition(this, getLinePath(d.line), false);\n            }),\n        (update) =>\n          update.styles(style).transition(function ({ line }: LineDatum) {\n            return transition(this, getLinePath(line), animate.update);\n          }),\n        (exit) => exit.remove()\n      )\n      .transitions();\n  };\n\n  if (!showTrunc || !truncRange) {\n    return renderLine([\n      {\n        line: [\n          [x1 + ox1, y1 + oy1],\n          [x2 + ox2, y2 + oy2],\n        ],\n        className: CLASS_NAMES.line.name,\n      },\n    ]);\n  }\n  const [r1, r2] = truncRange;\n  const dx = x2 - x1;\n  const dy = y2 - y1;\n  const [x3, y3] = [x1 + dx * r1, y1 + dy * r1];\n  const [x4, y4] = [x1 + dx * r2, y1 + dy * r2];\n  const animation = renderLine([\n    {\n      line: [\n        [x1 + ox1, y1 + oy1],\n        [x3, y3],\n      ],\n      className: CLASS_NAMES.lineFirst.name,\n    },\n    {\n      line: [\n        [x4, y4],\n        [x2 + ox2, y2 + oy2],\n      ],\n      className: CLASS_NAMES.lineSecond.name,\n    },\n  ]);\n  renderTruncation(container, attr);\n  return animation;\n}\n\nfunction renderAxisArrow(\n  container: Selection,\n  type: 'linear' | 'arc',\n  attr: RequiredAxisStyleProps,\n  style: RequiredAxisStyleProps\n) {\n  const { showArrow, showTrunc, lineArrow, lineArrowOffset, lineArrowSize } = attr;\n\n  let shapeToAddArrow: Selection;\n  if (type === 'arc') shapeToAddArrow = container.select(CLASS_NAMES.line.class);\n  else if (showTrunc) shapeToAddArrow = container.select(CLASS_NAMES.lineSecond.class);\n  else shapeToAddArrow = container.select(CLASS_NAMES.line.class);\n  if (!showArrow || !lineArrow || (attr.type === 'arc' && isCircle(attr.startAngle, attr.endAngle))) {\n    const node = shapeToAddArrow.node<Line>();\n    if (node) node.style.markerEnd = undefined;\n    return;\n  }\n  const arrow = renderExtDo(lineArrow);\n  arrow.attr(style);\n  scaleToPixel(arrow, lineArrowSize!, true);\n  shapeToAddArrow.style('markerEnd', arrow).style('markerEndOffset', -lineArrowOffset);\n}\n\nexport function renderAxisLine(container: Selection, attr: RequiredAxisStyleProps, animate: StandardAnimationOption) {\n  const { type } = attr;\n  let animation: AnimationResult[];\n  const style = subStyleProps<RequiredAxisStyleProps>(attr, 'line');\n\n  if (type === 'linear')\n    animation = renderLinear(\n      container,\n      attr as RequiredLinearAxisStyleProps,\n      omit(style, 'arrow') as RequiredLinearAxisStyleProps,\n      animate\n    );\n  else\n    animation = renderArc(\n      container,\n      attr as RequiredArcAxisStyleProps,\n      omit(style, 'arrow') as RequiredArcAxisStyleProps,\n      animate\n    );\n  renderAxisArrow(container, type, attr, style);\n  return animation;\n}\n"], "mappings": ";AAAA,SAASA,GAAG,QAAQ,YAAY;AAEhC,SAASC,UAAU,QAAQ,oBAAoB;AAG/C,SAEEC,QAAQ,EACRC,mBAAmB,EACnBC,IAAI,EACJC,WAAW,EACXC,YAAY,EACZC,aAAa,QACR,eAAe;AACtB,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,SAAS;AAO5D,OAAM,SAAUC,iBAAiBA,CAACC,KAAa,EAAEC,IAAkC;EAE/E,IAAAC,EAAA,GAAAC,MAAA,CAEEF,IAAI,CAAAG,QAAA,IAFY;IAAPC,EAAE,GAAAH,EAAA;IAAEI,EAAE,GAAAJ,EAAA;IACjBK,EAAA,GAAAJ,MAAA,CACEF,IAAI,CAAAO,MAAA,IADU;IAAPC,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA,GAAC;EAEZ,IAAAI,EAAA,GAAAR,MAAA,CAAW,CAACM,EAAE,GAAGJ,EAAE,EAAEK,EAAE,GAAGJ,EAAE,CAAC;IAA5BM,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAsB;EACnC,OAAO,CAACN,EAAE,GAAGO,EAAE,GAAGZ,KAAK,EAAEM,EAAE,GAAGO,EAAE,GAAGb,KAAK,CAAC;AAC3C;AAEA,OAAM,SAAUc,cAAcA,CAACd,KAAa,EAAEC,IAA+B;EAEzE,IAAAc,MAAM,GAEJd,IAAI,CAAAc,MAFA;IACNb,EAAA,GAAAC,MAAA,CACEF,IAAI,CAAAe,MAAA,IADU;IAAPC,EAAE,GAAAf,EAAA;IAAEgB,EAAE,GAAAhB,EAAA,GAAC;EAElB,IAAMiB,KAAK,GAAG7B,QAAQ,CAACO,YAAY,CAACG,KAAK,EAAEC,IAAI,CAAC,CAAC;EACjD,OAAO,CAACgB,EAAE,GAAGF,MAAM,GAAGK,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC,EAAED,EAAE,GAAGH,MAAM,GAAGK,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC,CAAC;AACvE;AAEA,OAAM,SAAUI,WAAWA,CAACvB,KAAa,EAAEC,IAA4B;EACrE,IAAIA,IAAI,CAACuB,IAAI,KAAK,QAAQ,EAAE,OAAOzB,iBAAiB,CAACC,KAAK,EAAEC,IAAI,CAAC;EACjE,OAAOa,cAAc,CAACd,KAAK,EAAEC,IAAI,CAAC;AACpC;AAEA,OAAM,SAAUwB,gBAAgBA,CAACxB,IAAkC;EACjE,OAAOH,oBAAoB,CAAC,CAAC,EAAEG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/C;AAEA,OAAM,SAAUyB,cAAcA,CAACzB,IAAkC;EAC/D,OAAOH,oBAAoB,CAAC,CAAC,EAAEG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/C;AAEA,SAAS0B,QAAQA,CAACC,UAAkB,EAAEC,QAAgB;EACpD,OAAOA,QAAQ,GAAGD,UAAU,KAAK,GAAG;AACtC;AAEA,SAASE,UAAUA,CAACF,UAAkB,EAAEC,QAAgB,EAAEZ,EAAU,EAAEC,EAAU,EAAEH,MAAc;EAC9F,IAAMgB,SAAS,GAAGF,QAAQ,GAAGD,UAAU;EACjC,IAAA1B,EAAA,GAAAC,MAAA,CAAW,CAACY,MAAM,EAAEA,MAAM,CAAC;IAA1BiB,EAAE,GAAA9B,EAAA;IAAE+B,EAAE,GAAA/B,EAAA,GAAoB;EAC3B,IAAAK,EAAA,GAAAJ,MAAA,CAAuC,CAACb,QAAQ,CAACsC,UAAU,CAAC,EAAEtC,QAAQ,CAACuC,QAAQ,CAAC,CAAC;IAAhFK,iBAAiB,GAAA3B,EAAA;IAAE4B,eAAe,GAAA5B,EAAA,GAA8C;EACvF,IAAM6B,aAAa,GAAG,SAAAA,CAACjB,KAAa;IAAK,QAACF,EAAE,GAAGF,MAAM,GAAGK,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC,EAAED,EAAE,GAAGH,MAAM,GAAGK,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC,CAAC;EAA9D,CAA8D;EAEjG,IAAAR,EAAA,GAAAR,MAAA,CAAWiC,aAAa,CAACF,iBAAiB,CAAC;IAA1CG,EAAE,GAAA1B,EAAA;IAAE2B,EAAE,GAAA3B,EAAA,GAAoC;EAC3C,IAAA4B,EAAA,GAAApC,MAAA,CAAWiC,aAAa,CAACD,eAAe,CAAC;IAAxCK,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAkC;EAE/C,IAAIZ,QAAQ,CAACC,UAAU,EAAEC,QAAQ,CAAC,EAAE;IAClC,IAAMa,kBAAkB,GAAG,CAACP,eAAe,GAAGD,iBAAiB,IAAI,CAAC;IAC9D,IAAAS,EAAA,GAAAxC,MAAA,CAAWiC,aAAa,CAACM,kBAAkB,CAAC;MAA3CE,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAAqC;IAClD,OAAO,CACL,CAAC,GAAG,EAAEN,EAAE,EAAEC,EAAE,CAAC,EACb,CAAC,GAAG,EAAEN,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEW,EAAE,EAAEC,EAAE,CAAC,EAC9B,CAAC,GAAG,EAAEb,EAAE,EAAEC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEO,EAAE,EAAEC,EAAE,CAAC,CAC/B;EACH;EAEA;EACA,IAAMK,KAAK,GAAGf,SAAS,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;EACrC;EACA,IAAMgB,KAAK,GAAGnB,UAAU,GAAGC,QAAQ,GAAG,CAAC,GAAG,CAAC;EAC3C,IAAMmB,WAAW,GAAG,KAAK;EAEzB,OAAOA,WAAW,GACd,IAAAC,MAAA,CAAIhC,EAAE,OAAAgC,MAAA,CAAI/B,EAAE,QAAA+B,MAAA,CAAKZ,EAAE,OAAAY,MAAA,CAAIX,EAAE,QAAAW,MAAA,CAAKjB,EAAE,OAAAiB,MAAA,CAAIhB,EAAE,SAAAgB,MAAA,CAAMH,KAAK,OAAAG,MAAA,CAAIF,KAAK,OAAAE,MAAA,CAAIT,EAAE,OAAAS,MAAA,CAAIR,EAAE,QAAAQ,MAAA,CAAKhC,EAAE,OAAAgC,MAAA,CAAI/B,EAAE,CAAE,GACrF,IAAA+B,MAAA,CAAIZ,EAAE,OAAAY,MAAA,CAAIX,EAAE,QAAAW,MAAA,CAAKjB,EAAE,OAAAiB,MAAA,CAAIhB,EAAE,SAAAgB,MAAA,CAAMH,KAAK,OAAAG,MAAA,CAAIF,KAAK,OAAAE,MAAA,CAAIT,EAAE,OAAAS,MAAA,CAAIR,EAAE,CAAE;AACjE;AAEA,SAASS,UAAUA,CAACC,GAAkB;EAC9B,IAAAjD,EAAA,GAA2CiD,GAAG,CAACC,UAAU;IAAvDxB,UAAU,GAAA1B,EAAA,CAAA0B,UAAA;IAAEC,QAAQ,GAAA3B,EAAA,CAAA2B,QAAA;IAAEb,MAAM,GAAAd,EAAA,CAAAc,MAAA;IAAED,MAAM,GAAAb,EAAA,CAAAa,MAAmB;EAC/D,OAAAsC,aAAA,CAAAA,aAAA,EAAQzB,UAAU,EAAEC,QAAQ,GAAA1B,MAAA,CAAKa,MAAM,YAAED,MAAM;AACjD;AAEA,SAASuC,SAASA,CAChBC,SAAoB,EACpBtD,IAA+B,EAC/BuD,KAAgC,EAChCC,OAAgC;EAExB,IAAA7B,UAAU,GAA+B3B,IAAI,CAAA2B,UAAnC;IAAEC,QAAQ,GAAqB5B,IAAI,CAAA4B,QAAzB;IAAEb,MAAM,GAAaf,IAAI,CAAAe,MAAjB;IAAED,MAAM,GAAKd,IAAI,CAAAc,MAAT;EAE5C,OAAOwC,SAAS,CACbG,SAAS,CAAC9D,WAAW,CAAC+D,IAAI,CAACC,KAAK,CAAC,CACjCC,IAAI,CAAC,CAAC;IAAEC,CAAC,EAAEhC,UAAU,CAAAiC,KAAA,SAAAV,aAAA,CAAAA,aAAA,EAACzB,UAAU,EAAEC,QAAQ,GAAA1B,MAAA,CAAKa,MAAM,YAAED,MAAM;EAAC,CAAE,CAAC,EAAE,UAAC+C,CAAC,EAAEE,CAAC;IAAK,OAAAA,CAAC;EAAD,CAAC,CAAC,CAC/EC,IAAI,CACH,UAACC,KAAK;IACJ,OAAAA,KAAK,CACFC,MAAM,CAAC,MAAM,CAAC,CACdlE,IAAI,CAAC,WAAW,EAAEL,WAAW,CAAC+D,IAAI,CAACS,IAAI,CAAC,CACxCC,MAAM,CAACpE,IAAI,CAAC,CACZoE,MAAM,CAAC;MAAEP,CAAC,EAAE,SAAAA,CAACA,CAAM;QAAK,OAAAA,CAAC,CAACA,CAAC;MAAH;IAAG,CAAE,CAAC;EAJjC,CAIiC,EACnC,UAACQ,MAAM;IACL,OAAAA,MAAM,CACHjF,UAAU,CAAC;MAAA,IAAAkF,KAAA;MACV,IAAMC,SAAS,GAAGjF,mBAAmB,CACnC,IAAI,EACJ2D,UAAU,CAAC,IAAI,CAAC,EAAAG,aAAA,CAAAA,aAAA,EACfzB,UAAU,EAAEC,QAAQ,GAAA1B,MAAA,CAAKa,MAAM,YAAED,MAAM,WACxC0C,OAAO,CAACa,MAAM,CACf;MACD,IAAIE,SAAS,EAAE;QACb,IAAMC,MAAM,GAAG,SAAAA,CAAA;UACb,IAAMZ,IAAI,GAAGzE,GAAG,CAACmF,KAAI,CAACnB,UAAU,EAAE,mBAAmB,CAAkC;UACvFmB,KAAI,CAACf,KAAK,CAACM,CAAC,GAAGhC,UAAU,CAAAiC,KAAA,SAAAV,aAAA,KAAAlD,MAAA,CAAI0D,IAAI,UAAC;QACpC,CAAC;QACDW,SAAS,CAACE,OAAO,GAAGD,MAAM;QAC1BD,SAAS,CAACG,QAAQ,GAAGF,MAAM;MAC7B;MACA,OAAOD,SAAS;IAClB,CAAC,CAAC,CACDH,MAAM,CAACpE,IAAI,CAAC;EAlBf,CAkBe,EACjB,UAAC2E,IAAI;IAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;EAAb,CAAa,CACxB,CACAR,MAAM,CAACb,KAAK,CAAC,CACbsB,WAAW,EAAE;AAClB;AAEA,SAASC,gBAAgBA,CAAIxB,SAAoB,EAAErD,EAAiE;MAA/D8E,UAAU,GAAA9E,EAAA,CAAA8E,UAAA;IAAEC,UAAU,GAAA/E,EAAA,CAAA+E,UAAA;IAAEC,aAAa,GAAAhF,EAAA,CAAAgF,aAAA;EACxF;AACF;AAEA,SAASC,UAAUA,CAAC/E,QAAe,EAAEI,MAAa,EAAE4E,KAAgC;EAAhC,IAAAA,KAAA;IAAAA,KAAA,IAA2B,CAAC,EAAE,CAAC,CAAC;EAAA;EAC5E,IAAAlF,EAAA,GAAAC,MAAA,CAAiC,CAACC,QAAQ,EAAEI,MAAM,EAAE4E,KAAK,CAAC;IAAzD7E,EAAA,GAAAJ,MAAA,CAAAD,EAAA,OAAQ;IAAPmC,EAAE,GAAA9B,EAAA;IAAE+B,EAAE,GAAA/B,EAAA;IAAGI,EAAA,GAAAR,MAAA,CAAAD,EAAA,OAAQ;IAAPsC,EAAE,GAAA7B,EAAA;IAAE8B,EAAE,GAAA9B,EAAA;IAAG4B,EAAA,GAAApC,MAAA,CAAAD,EAAA,OAAQ;IAAPmF,EAAE,GAAA9C,EAAA;IAAE+C,EAAE,GAAA/C,EAAA,GAA8B;EAC1D,IAAAI,EAAA,GAAAxC,MAAA,CAAS,CAACqC,EAAE,GAAGH,EAAE,EAAEI,EAAE,GAAGH,EAAE,CAAC;IAA1BiD,CAAC,GAAA5C,EAAA;IAAE6C,CAAC,GAAA7C,EAAA,GAAsB;EACjC,IAAM8C,CAAC,GAAGrE,IAAI,CAACsE,IAAI,CAACtE,IAAA,CAAAuE,GAAA,CAAAJ,CAAC,EAAI,CAAC,IAAGnE,IAAA,CAAAuE,GAAA,CAAAH,CAAC,EAAI,CAAC,EAAC;EAC9B,IAAAI,EAAA,GAAAzF,MAAA,CAAW,CAAC,CAACkF,EAAE,GAAGI,CAAC,EAAEH,EAAE,GAAGG,CAAC,CAAC;IAA3BI,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAqB;EAClC,OAAO,CAACC,EAAE,GAAGN,CAAC,EAAEM,EAAE,GAAGL,CAAC,EAAEM,EAAE,GAAGP,CAAC,EAAEO,EAAE,GAAGN,CAAC,CAAC;AACzC;AAEA,SAASO,WAAWA,CAACC,MAA0B;EACvC,IAAA9F,EAAA,GAAAC,MAAA,CAAuB6F,MAAM;IAA5BzF,EAAA,GAAAJ,MAAA,CAAAD,EAAA,OAAQ;IAAPmC,EAAE,GAAA9B,EAAA;IAAE+B,EAAE,GAAA/B,EAAA;IAAGI,EAAA,GAAAR,MAAA,CAAAD,EAAA,OAAQ;IAAPsC,EAAE,GAAA7B,EAAA;IAAE8B,EAAE,GAAA9B,EAAA,GAAW;EACnC,OAAO;IAAE0B,EAAE,EAAAA,EAAA;IAAEC,EAAE,EAAAA,EAAA;IAAEE,EAAE,EAAAA,EAAA;IAAEC,EAAE,EAAAA;EAAA,CAAE;AAC3B;AAEA,SAASwD,YAAYA,CACnB1C,SAAoB,EACpBtD,IAAkC,EAClCuD,KAAmC,EACnCC,OAAgC;EAExB,IAAAyC,SAAS,GAAkDjG,IAAI,CAAAiG,SAAtD;IAAE9F,QAAQ,GAAwCH,IAAI,CAAAG,QAA5C;IAAEI,MAAM,GAAgCP,IAAI,CAAAO,MAApC;IAAEwE,UAAU,GAAoB/E,IAAI,CAAA+E,UAAxB;IAAEE,aAAa,GAAKjF,IAAI,CAAAiF,aAAT;EACxD,IAAAhF,EAAA,GAAAC,MAAA,CAAuB,CAACC,QAAQ,EAAEI,MAAM,CAAC;IAAxCD,EAAA,GAAAJ,MAAA,CAAAD,EAAA,OAAQ;IAAPmC,EAAE,GAAA9B,EAAA;IAAE+B,EAAE,GAAA/B,EAAA;IAAGI,EAAA,GAAAR,MAAA,CAAAD,EAAA,OAAQ;IAAPsC,EAAE,GAAA7B,EAAA;IAAE8B,EAAE,GAAA9B,EAAA,GAAuB;EACzC,IAAA4B,EAAA,GAAApC,MAAA,CAAuB+E,aAAa,GAAGC,UAAU,CAAC/E,QAAQ,EAAEI,MAAM,EAAE0E,aAAa,CAAC,GAAG,IAAIiB,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;IAAxGC,GAAG,GAAA9D,EAAA;IAAE+D,GAAG,GAAA/D,EAAA;IAAEgE,GAAG,GAAAhE,EAAA;IAAEiE,GAAG,GAAAjE,EAAA,GAAsF;EAC/G,IAAMkE,UAAU,GAAG,SAAAA,CAAC5C,IAAiB;IACnC,OAAON,SAAS,CACbG,SAAS,CAAC9D,WAAW,CAAC+D,IAAI,CAACC,KAAK,CAAC,CACjCC,IAAI,CAACA,IAAI,EAAE,UAACC,CAAC,EAAEE,CAAC;MAAK,OAAAA,CAAC;IAAD,CAAC,CAAC,CACvBC,IAAI,CACH,UAACC,KAAK;MACJ,OAAAA,KAAK,CACFC,MAAM,CAAC,MAAM,CAAC,CACdlE,IAAI,CAAC,WAAW,EAAE,UAAC6D,CAAY;QAAK,UAAAb,MAAA,CAAGrD,WAAW,CAAC+D,IAAI,CAACS,IAAI,OAAAnB,MAAA,CAAIa,CAAC,CAAC4C,SAAS,CAAE;MAAzC,CAAyC,CAAC,CAC9ErC,MAAM,CAACb,KAAK,CAAC,CACbnE,UAAU,CAAC,UAAUyE,CAAY;QAChC,OAAOzE,UAAU,CAAC,IAAI,EAAE0G,WAAW,CAACjC,CAAC,CAACH,IAAI,CAAC,EAAE,KAAK,CAAC;MACrD,CAAC,CAAC;IANJ,CAMI,EACN,UAACW,MAAM;MACL,OAAAA,MAAM,CAACD,MAAM,CAACb,KAAK,CAAC,CAACnE,UAAU,CAAC,UAAUa,EAAmB;YAAjByD,IAAI,GAAAzD,EAAA,CAAAyD,IAAA;QAC9C,OAAOtE,UAAU,CAAC,IAAI,EAAE0G,WAAW,CAACpC,IAAI,CAAC,EAAEF,OAAO,CAACa,MAAM,CAAC;MAC5D,CAAC,CAAC;IAFF,CAEE,EACJ,UAACM,IAAI;MAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;IAAb,CAAa,CACxB,CACAC,WAAW,EAAE;EAClB,CAAC;EAED,IAAI,CAACoB,SAAS,IAAI,CAAClB,UAAU,EAAE;IAC7B,OAAOyB,UAAU,CAAC,CAChB;MACE9C,IAAI,EAAE,CACJ,CAACtB,EAAE,GAAGgE,GAAG,EAAE/D,EAAE,GAAGgE,GAAG,CAAC,EACpB,CAAC9D,EAAE,GAAG+D,GAAG,EAAE9D,EAAE,GAAG+D,GAAG,CAAC,CACrB;MACDE,SAAS,EAAE9G,WAAW,CAAC+D,IAAI,CAACS;KAC7B,CACF,CAAC;EACJ;EACM,IAAAzB,EAAA,GAAAxC,MAAA,CAAW6E,UAAU;IAApB2B,EAAE,GAAAhE,EAAA;IAAEiE,EAAE,GAAAjE,EAAA,GAAc;EAC3B,IAAM/B,EAAE,GAAG4B,EAAE,GAAGH,EAAE;EAClB,IAAMxB,EAAE,GAAG4B,EAAE,GAAGH,EAAE;EACZ,IAAAsD,EAAA,GAAAzF,MAAA,CAAW,CAACkC,EAAE,GAAGzB,EAAE,GAAG+F,EAAE,EAAErE,EAAE,GAAGzB,EAAE,GAAG8F,EAAE,CAAC;IAAtCE,EAAE,GAAAjB,EAAA;IAAEkB,EAAE,GAAAlB,EAAA,GAAgC;EACvC,IAAAmB,EAAA,GAAA5G,MAAA,CAAW,CAACkC,EAAE,GAAGzB,EAAE,GAAGgG,EAAE,EAAEtE,EAAE,GAAGzB,EAAE,GAAG+F,EAAE,CAAC;IAAtCI,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAgC;EAC7C,IAAMvC,SAAS,GAAGiC,UAAU,CAAC,CAC3B;IACE9C,IAAI,EAAE,CACJ,CAACtB,EAAE,GAAGgE,GAAG,EAAE/D,EAAE,GAAGgE,GAAG,CAAC,EACpB,CAACO,EAAE,EAAEC,EAAE,CAAC,CACT;IACDJ,SAAS,EAAE9G,WAAW,CAACsH,SAAS,CAAC9C;GAClC,EACD;IACET,IAAI,EAAE,CACJ,CAACqD,EAAE,EAAEC,EAAE,CAAC,EACR,CAACzE,EAAE,GAAG+D,GAAG,EAAE9D,EAAE,GAAG+D,GAAG,CAAC,CACrB;IACDE,SAAS,EAAE9G,WAAW,CAACuH,UAAU,CAAC/C;GACnC,CACF,CAAC;EACFW,gBAAgB,CAACxB,SAAS,EAAEtD,IAAI,CAAC;EACjC,OAAOuE,SAAS;AAClB;AAEA,SAAS4C,eAAeA,CACtB7D,SAAoB,EACpB/B,IAAsB,EACtBvB,IAA4B,EAC5BuD,KAA6B;EAErB,IAAA6D,SAAS,GAA2DpH,IAAI,CAAAoH,SAA/D;IAAEnB,SAAS,GAAgDjG,IAAI,CAAAiG,SAApD;IAAEoB,SAAS,GAAqCrH,IAAI,CAAAqH,SAAzC;IAAEC,eAAe,GAAoBtH,IAAI,CAAAsH,eAAxB;IAAEC,aAAa,GAAKvH,IAAI,CAAAuH,aAAT;EAEvE,IAAIC,eAA0B;EAC9B,IAAIjG,IAAI,KAAK,KAAK,EAAEiG,eAAe,GAAGlE,SAAS,CAACmE,MAAM,CAAC9H,WAAW,CAAC+D,IAAI,CAACC,KAAK,CAAC,CAAC,KAC1E,IAAIsC,SAAS,EAAEuB,eAAe,GAAGlE,SAAS,CAACmE,MAAM,CAAC9H,WAAW,CAACuH,UAAU,CAACvD,KAAK,CAAC,CAAC,KAChF6D,eAAe,GAAGlE,SAAS,CAACmE,MAAM,CAAC9H,WAAW,CAAC+D,IAAI,CAACC,KAAK,CAAC;EAC/D,IAAI,CAACyD,SAAS,IAAI,CAACC,SAAS,IAAKrH,IAAI,CAACuB,IAAI,KAAK,KAAK,IAAIG,QAAQ,CAAC1B,IAAI,CAAC2B,UAAU,EAAE3B,IAAI,CAAC4B,QAAQ,CAAE,EAAE;IACjG,IAAM8F,IAAI,GAAGF,eAAe,CAACE,IAAI,EAAQ;IACzC,IAAIA,IAAI,EAAEA,IAAI,CAACnE,KAAK,CAACoE,SAAS,GAAGC,SAAS;IAC1C;EACF;EACA,IAAMC,KAAK,GAAGrI,WAAW,CAAC6H,SAAS,CAAC;EACpCQ,KAAK,CAAC7H,IAAI,CAACuD,KAAK,CAAC;EACjB9D,YAAY,CAACoI,KAAK,EAAEN,aAAc,EAAE,IAAI,CAAC;EACzCC,eAAe,CAACjE,KAAK,CAAC,WAAW,EAAEsE,KAAK,CAAC,CAACtE,KAAK,CAAC,iBAAiB,EAAE,CAAC+D,eAAe,CAAC;AACtF;AAEA,OAAM,SAAUQ,cAAcA,CAACxE,SAAoB,EAAEtD,IAA4B,EAAEwD,OAAgC;EACzG,IAAAjC,IAAI,GAAKvB,IAAI,CAAAuB,IAAT;EACZ,IAAIgD,SAA4B;EAChC,IAAMhB,KAAK,GAAG7D,aAAa,CAAyBM,IAAI,EAAE,MAAM,CAAC;EAEjE,IAAIuB,IAAI,KAAK,QAAQ,EACnBgD,SAAS,GAAGyB,YAAY,CACtB1C,SAAS,EACTtD,IAAoC,EACpCT,IAAI,CAACgE,KAAK,EAAE,OAAO,CAAiC,EACpDC,OAAO,CACR,CAAC,KAEFe,SAAS,GAAGlB,SAAS,CACnBC,SAAS,EACTtD,IAAiC,EACjCT,IAAI,CAACgE,KAAK,EAAE,OAAO,CAA8B,EACjDC,OAAO,CACR;EACH2D,eAAe,CAAC7D,SAAS,EAAE/B,IAAI,EAAEvB,IAAI,EAAEuD,KAAK,CAAC;EAC7C,OAAOgB,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}