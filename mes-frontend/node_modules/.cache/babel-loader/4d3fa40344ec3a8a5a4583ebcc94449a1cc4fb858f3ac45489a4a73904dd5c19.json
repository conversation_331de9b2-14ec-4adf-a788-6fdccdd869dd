{"ast": null, "code": "// Includes only the \"core\" of graphlib\nmodule.exports = {\n  Graph: require(\"./graph\"),\n  version: require(\"./version\")\n};", "map": {"version": 3, "names": ["module", "exports", "Graph", "require", "version"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/index.js"], "sourcesContent": ["// Includes only the \"core\" of graphlib\nmodule.exports = {\n  Graph: require(\"./graph\"),\n  version: require(\"./version\")\n};\n"], "mappings": "AAAA;AACAA,MAAM,CAACC,OAAO,GAAG;EACfC,KAAK,EAAEC,OAAO,CAAC,SAAS,CAAC;EACzBC,OAAO,EAAED,OAAO,CAAC,WAAW;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}