{"ast": null, "code": "import node_count from \"./count.js\";\nimport node_each from \"./each.js\";\nimport node_eachBefore from \"./eachBefore.js\";\nimport node_eachAfter from \"./eachAfter.js\";\nimport node_find from \"./find.js\";\nimport node_sum from \"./sum.js\";\nimport node_sort from \"./sort.js\";\nimport node_path from \"./path.js\";\nimport node_ancestors from \"./ancestors.js\";\nimport node_descendants from \"./descendants.js\";\nimport node_leaves from \"./leaves.js\";\nimport node_links from \"./links.js\";\nimport node_iterator from \"./iterator.js\";\nexport default function hierarchy(data, children) {\n  if (data instanceof Map) {\n    data = [undefined, data];\n    if (children === undefined) children = mapChildren;\n  } else if (children === undefined) {\n    children = objectChildren;\n  }\n  var root = new Node(data),\n    node,\n    nodes = [root],\n    child,\n    childs,\n    i,\n    n;\n  while (node = nodes.pop()) {\n    if ((childs = children(node.data)) && (n = (childs = Array.from(childs)).length)) {\n      node.children = childs;\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = childs[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n  return root.eachBefore(computeHeight);\n}\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\nfunction objectChildren(d) {\n  return d.children;\n}\nfunction mapChildren(d) {\n  return Array.isArray(d) ? d[1] : null;\n}\nfunction copyData(node) {\n  if (node.data.value !== undefined) node.value = node.data.value;\n  node.data = node.data.data;\n}\nexport function computeHeight(node) {\n  var height = 0;\n  do node.height = height; while ((node = node.parent) && node.height < ++height);\n}\nexport function Node(data) {\n  this.data = data;\n  this.depth = this.height = 0;\n  this.parent = null;\n}\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  find: node_find,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy,\n  [Symbol.iterator]: node_iterator\n};", "map": {"version": 3, "names": ["node_count", "node_each", "node_eachBefore", "node_eachAfter", "node_find", "node_sum", "node_sort", "node_path", "node_ancestors", "node_descendants", "node_leaves", "node_links", "node_iterator", "hierarchy", "data", "children", "Map", "undefined", "mapChildren", "objectChildren", "root", "Node", "node", "nodes", "child", "childs", "i", "n", "pop", "Array", "from", "length", "push", "parent", "depth", "eachBefore", "computeHeight", "node_copy", "copyData", "d", "isArray", "value", "height", "prototype", "constructor", "count", "each", "eachAfter", "find", "sum", "sort", "path", "ancestors", "descendants", "leaves", "links", "copy", "Symbol", "iterator"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/hierarchy/index.js"], "sourcesContent": ["import node_count from \"./count.js\";\nimport node_each from \"./each.js\";\nimport node_eachBefore from \"./eachBefore.js\";\nimport node_eachAfter from \"./eachAfter.js\";\nimport node_find from \"./find.js\";\nimport node_sum from \"./sum.js\";\nimport node_sort from \"./sort.js\";\nimport node_path from \"./path.js\";\nimport node_ancestors from \"./ancestors.js\";\nimport node_descendants from \"./descendants.js\";\nimport node_leaves from \"./leaves.js\";\nimport node_links from \"./links.js\";\nimport node_iterator from \"./iterator.js\";\n\nexport default function hierarchy(data, children) {\n  if (data instanceof Map) {\n    data = [undefined, data];\n    if (children === undefined) children = mapChildren;\n  } else if (children === undefined) {\n    children = objectChildren;\n  }\n\n  var root = new Node(data),\n      node,\n      nodes = [root],\n      child,\n      childs,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if ((childs = children(node.data)) && (n = (childs = Array.from(childs)).length)) {\n      node.children = childs;\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = childs[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n\n  return root.eachBefore(computeHeight);\n}\n\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\n\nfunction objectChildren(d) {\n  return d.children;\n}\n\nfunction mapChildren(d) {\n  return Array.isArray(d) ? d[1] : null;\n}\n\nfunction copyData(node) {\n  if (node.data.value !== undefined) node.value = node.data.value;\n  node.data = node.data.data;\n}\n\nexport function computeHeight(node) {\n  var height = 0;\n  do node.height = height;\n  while ((node = node.parent) && (node.height < ++height));\n}\n\nexport function Node(data) {\n  this.data = data;\n  this.depth =\n  this.height = 0;\n  this.parent = null;\n}\n\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  find: node_find,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy,\n  [Symbol.iterator]: node_iterator\n};\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,eAAe,MAAM,iBAAiB;AAC7C,OAAOC,cAAc,MAAM,gBAAgB;AAC3C,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,QAAQ,MAAM,UAAU;AAC/B,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,SAAS,MAAM,WAAW;AACjC,OAAOC,cAAc,MAAM,gBAAgB;AAC3C,OAAOC,gBAAgB,MAAM,kBAAkB;AAC/C,OAAOC,WAAW,MAAM,aAAa;AACrC,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,aAAa,MAAM,eAAe;AAEzC,eAAe,SAASC,SAASA,CAACC,IAAI,EAAEC,QAAQ,EAAE;EAChD,IAAID,IAAI,YAAYE,GAAG,EAAE;IACvBF,IAAI,GAAG,CAACG,SAAS,EAAEH,IAAI,CAAC;IACxB,IAAIC,QAAQ,KAAKE,SAAS,EAAEF,QAAQ,GAAGG,WAAW;EACpD,CAAC,MAAM,IAAIH,QAAQ,KAAKE,SAAS,EAAE;IACjCF,QAAQ,GAAGI,cAAc;EAC3B;EAEA,IAAIC,IAAI,GAAG,IAAIC,IAAI,CAACP,IAAI,CAAC;IACrBQ,IAAI;IACJC,KAAK,GAAG,CAACH,IAAI,CAAC;IACdI,KAAK;IACLC,MAAM;IACNC,CAAC;IACDC,CAAC;EAEL,OAAOL,IAAI,GAAGC,KAAK,CAACK,GAAG,CAAC,CAAC,EAAE;IACzB,IAAI,CAACH,MAAM,GAAGV,QAAQ,CAACO,IAAI,CAACR,IAAI,CAAC,MAAMa,CAAC,GAAG,CAACF,MAAM,GAAGI,KAAK,CAACC,IAAI,CAACL,MAAM,CAAC,EAAEM,MAAM,CAAC,EAAE;MAChFT,IAAI,CAACP,QAAQ,GAAGU,MAAM;MACtB,KAAKC,CAAC,GAAGC,CAAC,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC3BH,KAAK,CAACS,IAAI,CAACR,KAAK,GAAGC,MAAM,CAACC,CAAC,CAAC,GAAG,IAAIL,IAAI,CAACI,MAAM,CAACC,CAAC,CAAC,CAAC,CAAC;QACnDF,KAAK,CAACS,MAAM,GAAGX,IAAI;QACnBE,KAAK,CAACU,KAAK,GAAGZ,IAAI,CAACY,KAAK,GAAG,CAAC;MAC9B;IACF;EACF;EAEA,OAAOd,IAAI,CAACe,UAAU,CAACC,aAAa,CAAC;AACvC;AAEA,SAASC,SAASA,CAAA,EAAG;EACnB,OAAOxB,SAAS,CAAC,IAAI,CAAC,CAACsB,UAAU,CAACG,QAAQ,CAAC;AAC7C;AAEA,SAASnB,cAAcA,CAACoB,CAAC,EAAE;EACzB,OAAOA,CAAC,CAACxB,QAAQ;AACnB;AAEA,SAASG,WAAWA,CAACqB,CAAC,EAAE;EACtB,OAAOV,KAAK,CAACW,OAAO,CAACD,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACvC;AAEA,SAASD,QAAQA,CAAChB,IAAI,EAAE;EACtB,IAAIA,IAAI,CAACR,IAAI,CAAC2B,KAAK,KAAKxB,SAAS,EAAEK,IAAI,CAACmB,KAAK,GAAGnB,IAAI,CAACR,IAAI,CAAC2B,KAAK;EAC/DnB,IAAI,CAACR,IAAI,GAAGQ,IAAI,CAACR,IAAI,CAACA,IAAI;AAC5B;AAEA,OAAO,SAASsB,aAAaA,CAACd,IAAI,EAAE;EAClC,IAAIoB,MAAM,GAAG,CAAC;EACd,GAAGpB,IAAI,CAACoB,MAAM,GAAGA,MAAM,CAAC,QACjB,CAACpB,IAAI,GAAGA,IAAI,CAACW,MAAM,KAAMX,IAAI,CAACoB,MAAM,GAAG,EAAEA,MAAO;AACzD;AAEA,OAAO,SAASrB,IAAIA,CAACP,IAAI,EAAE;EACzB,IAAI,CAACA,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACoB,KAAK,GACV,IAAI,CAACQ,MAAM,GAAG,CAAC;EACf,IAAI,CAACT,MAAM,GAAG,IAAI;AACpB;AAEAZ,IAAI,CAACsB,SAAS,GAAG9B,SAAS,CAAC8B,SAAS,GAAG;EACrCC,WAAW,EAAEvB,IAAI;EACjBwB,KAAK,EAAE7C,UAAU;EACjB8C,IAAI,EAAE7C,SAAS;EACf8C,SAAS,EAAE5C,cAAc;EACzBgC,UAAU,EAAEjC,eAAe;EAC3B8C,IAAI,EAAE5C,SAAS;EACf6C,GAAG,EAAE5C,QAAQ;EACb6C,IAAI,EAAE5C,SAAS;EACf6C,IAAI,EAAE5C,SAAS;EACf6C,SAAS,EAAE5C,cAAc;EACzB6C,WAAW,EAAE5C,gBAAgB;EAC7B6C,MAAM,EAAE5C,WAAW;EACnB6C,KAAK,EAAE5C,UAAU;EACjB6C,IAAI,EAAEnB,SAAS;EACf,CAACoB,MAAM,CAACC,QAAQ,GAAG9C;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}