{"ast": null, "code": "var _ = require(\"./lodash\");\nvar Graph = require(\"./graph\");\nmodule.exports = {\n  write: write,\n  read: read\n};\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound()\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g)\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function (v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = {\n      v: v\n    };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\nfunction writeEdges(g) {\n  return _.map(g.edges(), function (e) {\n    var edgeValue = g.edge(e);\n    var edge = {\n      v: e.v,\n      w: e.w\n    };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function (entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function (entry) {\n    g.setEdge({\n      v: entry.v,\n      w: entry.w,\n      name: entry.name\n    }, entry.value);\n  });\n  return g;\n}", "map": {"version": 3, "names": ["_", "require", "Graph", "module", "exports", "write", "read", "g", "json", "options", "directed", "isDirected", "multigraph", "isMultigraph", "compound", "isCompound", "nodes", "writeNodes", "edges", "writeEdges", "isUndefined", "graph", "value", "clone", "map", "v", "nodeValue", "node", "parent", "e", "edgeValue", "edge", "w", "name", "setGraph", "each", "entry", "setNode", "setParent", "setEdge"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/json.js"], "sourcesContent": ["var _ = require(\"./lodash\");\nvar Graph = require(\"./graph\");\n\nmodule.exports = {\n  write: write,\n  read: read\n};\n\nfunction write(g) {\n  var json = {\n    options: {\n      directed: g.isDirected(),\n      multigraph: g.isMultigraph(),\n      compound: g.isCompound()\n    },\n    nodes: writeNodes(g),\n    edges: writeEdges(g)\n  };\n  if (!_.isUndefined(g.graph())) {\n    json.value = _.clone(g.graph());\n  }\n  return json;\n}\n\nfunction writeNodes(g) {\n  return _.map(g.nodes(), function(v) {\n    var nodeValue = g.node(v);\n    var parent = g.parent(v);\n    var node = { v: v };\n    if (!_.isUndefined(nodeValue)) {\n      node.value = nodeValue;\n    }\n    if (!_.isUndefined(parent)) {\n      node.parent = parent;\n    }\n    return node;\n  });\n}\n\nfunction writeEdges(g) {\n  return _.map(g.edges(), function(e) {\n    var edgeValue = g.edge(e);\n    var edge = { v: e.v, w: e.w };\n    if (!_.isUndefined(e.name)) {\n      edge.name = e.name;\n    }\n    if (!_.isUndefined(edgeValue)) {\n      edge.value = edgeValue;\n    }\n    return edge;\n  });\n}\n\nfunction read(json) {\n  var g = new Graph(json.options).setGraph(json.value);\n  _.each(json.nodes, function(entry) {\n    g.setNode(entry.v, entry.value);\n    if (entry.parent) {\n      g.setParent(entry.v, entry.parent);\n    }\n  });\n  _.each(json.edges, function(entry) {\n    g.setEdge({ v: entry.v, w: entry.w, name: entry.name }, entry.value);\n  });\n  return g;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAE9BE,MAAM,CAACC,OAAO,GAAG;EACfC,KAAK,EAAEA,KAAK;EACZC,IAAI,EAAEA;AACR,CAAC;AAED,SAASD,KAAKA,CAACE,CAAC,EAAE;EAChB,IAAIC,IAAI,GAAG;IACTC,OAAO,EAAE;MACPC,QAAQ,EAAEH,CAAC,CAACI,UAAU,CAAC,CAAC;MACxBC,UAAU,EAAEL,CAAC,CAACM,YAAY,CAAC,CAAC;MAC5BC,QAAQ,EAAEP,CAAC,CAACQ,UAAU,CAAC;IACzB,CAAC;IACDC,KAAK,EAAEC,UAAU,CAACV,CAAC,CAAC;IACpBW,KAAK,EAAEC,UAAU,CAACZ,CAAC;EACrB,CAAC;EACD,IAAI,CAACP,CAAC,CAACoB,WAAW,CAACb,CAAC,CAACc,KAAK,CAAC,CAAC,CAAC,EAAE;IAC7Bb,IAAI,CAACc,KAAK,GAAGtB,CAAC,CAACuB,KAAK,CAAChB,CAAC,CAACc,KAAK,CAAC,CAAC,CAAC;EACjC;EACA,OAAOb,IAAI;AACb;AAEA,SAASS,UAAUA,CAACV,CAAC,EAAE;EACrB,OAAOP,CAAC,CAACwB,GAAG,CAACjB,CAAC,CAACS,KAAK,CAAC,CAAC,EAAE,UAASS,CAAC,EAAE;IAClC,IAAIC,SAAS,GAAGnB,CAAC,CAACoB,IAAI,CAACF,CAAC,CAAC;IACzB,IAAIG,MAAM,GAAGrB,CAAC,CAACqB,MAAM,CAACH,CAAC,CAAC;IACxB,IAAIE,IAAI,GAAG;MAAEF,CAAC,EAAEA;IAAE,CAAC;IACnB,IAAI,CAACzB,CAAC,CAACoB,WAAW,CAACM,SAAS,CAAC,EAAE;MAC7BC,IAAI,CAACL,KAAK,GAAGI,SAAS;IACxB;IACA,IAAI,CAAC1B,CAAC,CAACoB,WAAW,CAACQ,MAAM,CAAC,EAAE;MAC1BD,IAAI,CAACC,MAAM,GAAGA,MAAM;IACtB;IACA,OAAOD,IAAI;EACb,CAAC,CAAC;AACJ;AAEA,SAASR,UAAUA,CAACZ,CAAC,EAAE;EACrB,OAAOP,CAAC,CAACwB,GAAG,CAACjB,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE,UAASW,CAAC,EAAE;IAClC,IAAIC,SAAS,GAAGvB,CAAC,CAACwB,IAAI,CAACF,CAAC,CAAC;IACzB,IAAIE,IAAI,GAAG;MAAEN,CAAC,EAAEI,CAAC,CAACJ,CAAC;MAAEO,CAAC,EAAEH,CAAC,CAACG;IAAE,CAAC;IAC7B,IAAI,CAAChC,CAAC,CAACoB,WAAW,CAACS,CAAC,CAACI,IAAI,CAAC,EAAE;MAC1BF,IAAI,CAACE,IAAI,GAAGJ,CAAC,CAACI,IAAI;IACpB;IACA,IAAI,CAACjC,CAAC,CAACoB,WAAW,CAACU,SAAS,CAAC,EAAE;MAC7BC,IAAI,CAACT,KAAK,GAAGQ,SAAS;IACxB;IACA,OAAOC,IAAI;EACb,CAAC,CAAC;AACJ;AAEA,SAASzB,IAAIA,CAACE,IAAI,EAAE;EAClB,IAAID,CAAC,GAAG,IAAIL,KAAK,CAACM,IAAI,CAACC,OAAO,CAAC,CAACyB,QAAQ,CAAC1B,IAAI,CAACc,KAAK,CAAC;EACpDtB,CAAC,CAACmC,IAAI,CAAC3B,IAAI,CAACQ,KAAK,EAAE,UAASoB,KAAK,EAAE;IACjC7B,CAAC,CAAC8B,OAAO,CAACD,KAAK,CAACX,CAAC,EAAEW,KAAK,CAACd,KAAK,CAAC;IAC/B,IAAIc,KAAK,CAACR,MAAM,EAAE;MAChBrB,CAAC,CAAC+B,SAAS,CAACF,KAAK,CAACX,CAAC,EAAEW,KAAK,CAACR,MAAM,CAAC;IACpC;EACF,CAAC,CAAC;EACF5B,CAAC,CAACmC,IAAI,CAAC3B,IAAI,CAACU,KAAK,EAAE,UAASkB,KAAK,EAAE;IACjC7B,CAAC,CAACgC,OAAO,CAAC;MAAEd,CAAC,EAAEW,KAAK,CAACX,CAAC;MAAEO,CAAC,EAAEI,KAAK,CAACJ,CAAC;MAAEC,IAAI,EAAEG,KAAK,CAACH;IAAK,CAAC,EAAEG,KAAK,CAACd,KAAK,CAAC;EACtE,CAAC,CAAC;EACF,OAAOf,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}