{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"../lodash\");\nmodule.exports = crossCount;\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i - 1], layering[i]);\n  }\n  return cc;\n}\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(southLayer, _.map(southLayer, function (v, i) {\n    return i;\n  }));\n  var southEntries = _.flatten(_.map(northLayer, function (v) {\n    return _.sortBy(_.map(g.outEdges(v), function (e) {\n      return {\n        pos: southPos[e.w],\n        weight: g.edge(e).weight\n      };\n    }), \"pos\");\n  }), true);\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function () {\n    return 0;\n  });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(southEntries.forEach(function (entry) {\n    var index = entry.pos + firstIndex;\n    tree[index] += entry.weight;\n    var weightSum = 0;\n    while (index > 0) {\n      if (index % 2) {\n        weightSum += tree[index + 1];\n      }\n      index = index - 1 >> 1;\n      tree[index] += entry.weight;\n    }\n    cc += entry.weight * weightSum;\n  }));\n  return cc;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "crossCount", "g", "layering", "cc", "i", "length", "twoLayerCrossCount", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "southPos", "zipObject", "map", "v", "southEntries", "flatten", "sortBy", "outEdges", "e", "pos", "w", "weight", "edge", "firstIndex", "treeSize", "tree", "Array", "for<PERSON>ach", "entry", "index", "weightSum"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/order/cross-count.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = crossCount;\n\n/*\n * A function that takes a layering (an array of layers, each with an array of\n * ordererd nodes) and a graph and returns a weighted crossing count.\n *\n * Pre-conditions:\n *\n *    1. Input graph must be simple (not a multigraph), directed, and include\n *       only simple edges.\n *    2. Edges in the input graph must have assigned weights.\n *\n * Post-conditions:\n *\n *    1. The graph and layering matrix are left unchanged.\n *\n * This algorithm is derived from <PERSON><PERSON>, et al., \"Bilayer Cross Counting.\"\n */\nfunction crossCount(g, layering) {\n  var cc = 0;\n  for (var i = 1; i < layering.length; ++i) {\n    cc += twoLayerCrossCount(g, layering[i-1], layering[i]);\n  }\n  return cc;\n}\n\nfunction twoLayerCrossCount(g, northLayer, southLayer) {\n  // Sort all of the edges between the north and south layers by their position\n  // in the north layer and then the south. Map these edges to the position of\n  // their head in the south layer.\n  var southPos = _.zipObject(southLayer,\n    _.map(southLayer, function (v, i) { return i; }));\n  var southEntries = _.flatten(_.map(northLayer, function(v) {\n    return _.sortBy(_.map(g.outEdges(v), function(e) {\n      return { pos: southPos[e.w], weight: g.edge(e).weight };\n    }), \"pos\");\n  }), true);\n\n  // Build the accumulator tree\n  var firstIndex = 1;\n  while (firstIndex < southLayer.length) firstIndex <<= 1;\n  var treeSize = 2 * firstIndex - 1;\n  firstIndex -= 1;\n  var tree = _.map(new Array(treeSize), function() { return 0; });\n\n  // Calculate the weighted crossings\n  var cc = 0;\n  _.forEach(southEntries.forEach(function(entry) {\n    var index = entry.pos + firstIndex;\n    tree[index] += entry.weight;\n    var weightSum = 0;\n    while (index > 0) {\n      if (index % 2) {\n        weightSum += tree[index + 1];\n      }\n      index = (index - 1) >> 1;\n      tree[index] += entry.weight;\n    }\n    cc += entry.weight * weightSum;\n  }));\n\n  return cc;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,UAAU;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,CAAC,EAAEC,QAAQ,EAAE;EAC/B,IAAIC,EAAE,GAAG,CAAC;EACV,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;IACxCD,EAAE,IAAIG,kBAAkB,CAACL,CAAC,EAAEC,QAAQ,CAACE,CAAC,GAAC,CAAC,CAAC,EAAEF,QAAQ,CAACE,CAAC,CAAC,CAAC;EACzD;EACA,OAAOD,EAAE;AACX;AAEA,SAASG,kBAAkBA,CAACL,CAAC,EAAEM,UAAU,EAAEC,UAAU,EAAE;EACrD;EACA;EACA;EACA,IAAIC,QAAQ,GAAGb,CAAC,CAACc,SAAS,CAACF,UAAU,EACnCZ,CAAC,CAACe,GAAG,CAACH,UAAU,EAAE,UAAUI,CAAC,EAAER,CAAC,EAAE;IAAE,OAAOA,CAAC;EAAE,CAAC,CAAC,CAAC;EACnD,IAAIS,YAAY,GAAGjB,CAAC,CAACkB,OAAO,CAAClB,CAAC,CAACe,GAAG,CAACJ,UAAU,EAAE,UAASK,CAAC,EAAE;IACzD,OAAOhB,CAAC,CAACmB,MAAM,CAACnB,CAAC,CAACe,GAAG,CAACV,CAAC,CAACe,QAAQ,CAACJ,CAAC,CAAC,EAAE,UAASK,CAAC,EAAE;MAC/C,OAAO;QAAEC,GAAG,EAAET,QAAQ,CAACQ,CAAC,CAACE,CAAC,CAAC;QAAEC,MAAM,EAAEnB,CAAC,CAACoB,IAAI,CAACJ,CAAC,CAAC,CAACG;MAAO,CAAC;IACzD,CAAC,CAAC,EAAE,KAAK,CAAC;EACZ,CAAC,CAAC,EAAE,IAAI,CAAC;;EAET;EACA,IAAIE,UAAU,GAAG,CAAC;EAClB,OAAOA,UAAU,GAAGd,UAAU,CAACH,MAAM,EAAEiB,UAAU,KAAK,CAAC;EACvD,IAAIC,QAAQ,GAAG,CAAC,GAAGD,UAAU,GAAG,CAAC;EACjCA,UAAU,IAAI,CAAC;EACf,IAAIE,IAAI,GAAG5B,CAAC,CAACe,GAAG,CAAC,IAAIc,KAAK,CAACF,QAAQ,CAAC,EAAE,YAAW;IAAE,OAAO,CAAC;EAAE,CAAC,CAAC;;EAE/D;EACA,IAAIpB,EAAE,GAAG,CAAC;EACVP,CAAC,CAAC8B,OAAO,CAACb,YAAY,CAACa,OAAO,CAAC,UAASC,KAAK,EAAE;IAC7C,IAAIC,KAAK,GAAGD,KAAK,CAACT,GAAG,GAAGI,UAAU;IAClCE,IAAI,CAACI,KAAK,CAAC,IAAID,KAAK,CAACP,MAAM;IAC3B,IAAIS,SAAS,GAAG,CAAC;IACjB,OAAOD,KAAK,GAAG,CAAC,EAAE;MAChB,IAAIA,KAAK,GAAG,CAAC,EAAE;QACbC,SAAS,IAAIL,IAAI,CAACI,KAAK,GAAG,CAAC,CAAC;MAC9B;MACAA,KAAK,GAAIA,KAAK,GAAG,CAAC,IAAK,CAAC;MACxBJ,IAAI,CAACI,KAAK,CAAC,IAAID,KAAK,CAACP,MAAM;IAC7B;IACAjB,EAAE,IAAIwB,KAAK,CAACP,MAAM,GAAGS,SAAS;EAChC,CAAC,CAAC,CAAC;EAEH,OAAO1B,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}