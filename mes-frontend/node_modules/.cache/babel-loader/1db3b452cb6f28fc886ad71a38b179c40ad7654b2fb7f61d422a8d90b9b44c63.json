{"ast": null, "code": "import colors from \"../colors.js\";\nexport default colors(\"1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666\");", "map": {"version": 3, "names": ["colors"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-scale-chromatic/src/categorical/Dark2.js"], "sourcesContent": ["import colors from \"../colors.js\";\n\nexport default colors(\"1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666\");\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AAEjC,eAAeA,MAAM,CAAC,kDAAkD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}