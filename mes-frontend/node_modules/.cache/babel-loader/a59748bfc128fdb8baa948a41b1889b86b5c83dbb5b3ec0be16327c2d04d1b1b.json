{"ast": null, "code": "import { getArcObject } from '../../../shape/utils';\nimport { isCircular, isRadial } from '../../../utils/coordinate';\nimport { maybePercentage } from '../../../utils/helper';\nimport { mid } from '../../../utils/vector';\nexport function inferNonCircularStyle(position, points, value, coordinate) {\n  const {\n    bounds\n  } = value;\n  const [[x0, y0], [x1, y1]] = bounds;\n  const w = x1 - x0;\n  const h = y1 - y0;\n  const xy = options => {\n    const {\n      x: ox,\n      y: oy\n    } = options;\n    const px = maybePercentage(value.x, w);\n    const py = maybePercentage(value.y, h);\n    return Object.assign(Object.assign({}, options), {\n      x: (px || ox) + x0,\n      y: (py || oy) + y0\n    });\n  };\n  // 4 direction.\n  if (position === 'left') return xy({\n    x: 0,\n    y: h / 2,\n    textAlign: 'start',\n    textBaseline: 'middle'\n  });\n  if (position === 'right') return xy({\n    x: w,\n    y: h / 2,\n    textAlign: 'end',\n    textBaseline: 'middle'\n  });\n  if (position === 'top') return xy({\n    x: w / 2,\n    y: 0,\n    textAlign: 'center',\n    textBaseline: 'top'\n  });\n  if (position === 'bottom') return xy({\n    x: w / 2,\n    y: h,\n    textAlign: 'center',\n    textBaseline: 'bottom'\n  });\n  // 4 corner position.\n  if (position === 'top-left') return xy({\n    x: 0,\n    y: 0,\n    textAlign: 'start',\n    textBaseline: 'top'\n  });\n  if (position === 'top-right') return xy({\n    x: w,\n    y: 0,\n    textAlign: 'end',\n    textBaseline: 'top'\n  });\n  if (position === 'bottom-left') return xy({\n    x: 0,\n    y: h,\n    textAlign: 'start',\n    textBaseline: 'bottom'\n  });\n  if (position === 'bottom-right') return xy({\n    x: w,\n    y: h,\n    textAlign: 'end',\n    textBaseline: 'bottom'\n  });\n  // default return 'inside'\n  return xy({\n    x: w / 2,\n    y: h / 2,\n    textAlign: 'center',\n    textBaseline: 'middle'\n  });\n}\nexport function inferRadialStyle(position, points, value, coordinate) {\n  const {\n    y,\n    y1,\n    autoRotate,\n    rotateToAlignArc\n  } = value;\n  const center = coordinate.getCenter();\n  const arcObject = getArcObject(coordinate, points, [y, y1]);\n  const {\n    innerRadius,\n    outerRadius,\n    startAngle,\n    endAngle\n  } = arcObject;\n  const angle = position === 'inside' ? (startAngle + endAngle) / 2 : endAngle;\n  const rotate = inferRotation(angle, autoRotate, rotateToAlignArc);\n  const point = (() => {\n    const [p0, p1] = points;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const [x, y] = position === 'inside' ? pointOfArc(center, angle, radius) : mid(p0, p1);\n    return {\n      x,\n      y\n    };\n  })();\n  return Object.assign(Object.assign({}, point), {\n    textAlign: position === 'inside' ? 'center' : 'start',\n    textBaseline: 'middle',\n    rotate\n  });\n}\nexport function pointOfArc(center, angle, radius) {\n  return [center[0] + Math.sin(angle) * radius, center[1] - Math.cos(angle) * radius];\n}\nexport function inferRotation(angle, autoRotate, rotateToAlignArc) {\n  if (!autoRotate) return 0;\n  const append = rotateToAlignArc ? 0 : Math.sin(angle) < 0 ? 90 : -90;\n  return angle / Math.PI * 180 + append;\n}\nfunction inferInnerCircularStyle(position, points, value, coordinate) {\n  const {\n    y,\n    y1,\n    autoRotate,\n    rotateToAlignArc,\n    radius: radiusRatio = 0.5,\n    offset = 0\n  } = value;\n  const arcObject = getArcObject(coordinate, points, [y, y1]);\n  const {\n    startAngle,\n    endAngle\n  } = arcObject;\n  const center = coordinate.getCenter();\n  const angle = (startAngle + endAngle) / 2;\n  const rotate = inferRotation(angle, autoRotate, rotateToAlignArc);\n  const textStyle = {\n    textAlign: 'center',\n    textBaseline: 'middle',\n    rotate\n  };\n  const {\n    innerRadius,\n    outerRadius\n  } = arcObject;\n  const r0 = innerRadius + (outerRadius - innerRadius) * radiusRatio;\n  const r1 = r0 + offset;\n  const [x0, y0] = pointOfArc(center, angle, r1);\n  return Object.assign({\n    x: x0,\n    y: y0\n  }, textStyle);\n}\n// Set to null will not be set with default value as below.\n// const { x = 0 } = options;\nfunction maybeUndefined(d) {\n  return d === undefined ? null : d;\n}\nexport function inferIdentityStyle(position, points, value, coordinate) {\n  const {\n    bounds\n  } = value;\n  const [p] = bounds;\n  return {\n    x: maybeUndefined(p[0]),\n    y: maybeUndefined(p[1])\n  };\n}\nexport function getDefaultStyle(position, points, value, coordinate) {\n  const {\n    bounds\n  } = value;\n  // When bounds.length = 1\n  // For series mark, such as line and area.\n  // The bounds for text is defined with only one point.\n  // Use this point as the label position.\n  if (bounds.length === 1) {\n    return inferIdentityStyle(position, points, value, coordinate);\n  }\n  const inferDefaultStyle = isRadial(coordinate) ? inferRadialStyle : isCircular(coordinate) ? inferInnerCircularStyle : inferNonCircularStyle;\n  return inferDefaultStyle(position, points, value, coordinate);\n}", "map": {"version": 3, "names": ["getArcObject", "isCircular", "isRadial", "maybePercentage", "mid", "inferNonCircularStyle", "position", "points", "value", "coordinate", "bounds", "x0", "y0", "x1", "y1", "w", "h", "xy", "options", "x", "ox", "y", "oy", "px", "py", "Object", "assign", "textAlign", "textBaseline", "inferRadialStyle", "autoRotate", "rotateToAlignArc", "center", "getCenter", "arcObject", "innerRadius", "outerRadius", "startAngle", "endAngle", "angle", "rotate", "inferRotation", "point", "p0", "p1", "radius", "pointOfArc", "Math", "sin", "cos", "append", "PI", "inferInnerCircularStyle", "radiusRatio", "offset", "textStyle", "r0", "r1", "maybeUndefined", "d", "undefined", "inferIdentityStyle", "p", "getDefaultStyle", "length", "inferDefaultStyle"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/default.ts"], "sourcesContent": ["import { Coordinate } from '@antv/coord';\nimport { Vector2 } from '../../../runtime';\nimport { getArcObject } from '../../../shape/utils';\nimport { isCircular, isRadial } from '../../../utils/coordinate';\nimport { maybePercentage } from '../../../utils/helper';\nimport { mid } from '../../../utils/vector';\n\nexport type LabelPosition =\n  | 'top'\n  | 'left'\n  | 'right'\n  | 'bottom'\n  | 'top-left'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-right'\n  | 'inside'\n  | 'outside'\n  | 'area'\n  | 'spider'\n  | 'surround';\n\nexport function inferNonCircularStyle(\n  position: LabelPosition,\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n) {\n  const { bounds } = value;\n  const [[x0, y0], [x1, y1]] = bounds;\n  const w = x1 - x0;\n  const h = y1 - y0;\n  const xy = (options) => {\n    const { x: ox, y: oy } = options;\n    const px = maybePercentage(value.x, w);\n    const py = maybePercentage(value.y, h);\n    return {\n      ...options,\n      x: (px || ox) + x0,\n      y: (py || oy) + y0,\n    };\n  };\n  // 4 direction.\n  if (position === 'left')\n    return xy({ x: 0, y: h / 2, textAlign: 'start', textBaseline: 'middle' });\n  if (position === 'right')\n    return xy({ x: w, y: h / 2, textAlign: 'end', textBaseline: 'middle' });\n  if (position === 'top')\n    return xy({ x: w / 2, y: 0, textAlign: 'center', textBaseline: 'top' });\n  if (position === 'bottom')\n    return xy({ x: w / 2, y: h, textAlign: 'center', textBaseline: 'bottom' });\n  // 4 corner position.\n  if (position === 'top-left')\n    return xy({ x: 0, y: 0, textAlign: 'start', textBaseline: 'top' });\n  if (position === 'top-right')\n    return xy({ x: w, y: 0, textAlign: 'end', textBaseline: 'top' });\n  if (position === 'bottom-left')\n    return xy({ x: 0, y: h, textAlign: 'start', textBaseline: 'bottom' });\n  if (position === 'bottom-right')\n    return xy({ x: w, y: h, textAlign: 'end', textBaseline: 'bottom' });\n  // default return 'inside'\n  return xy({\n    x: w / 2,\n    y: h / 2,\n    textAlign: 'center',\n    textBaseline: 'middle',\n  });\n}\n\nexport function inferRadialStyle(\n  position: LabelPosition,\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n) {\n  const { y, y1, autoRotate, rotateToAlignArc } = value;\n  const center = coordinate.getCenter();\n  const arcObject = getArcObject(coordinate, points, [y, y1]);\n\n  const { innerRadius, outerRadius, startAngle, endAngle } = arcObject;\n  const angle = position === 'inside' ? (startAngle + endAngle) / 2 : endAngle;\n  const rotate = inferRotation(angle, autoRotate, rotateToAlignArc);\n\n  const point = (() => {\n    const [p0, p1] = points;\n    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;\n    const [x, y] =\n      position === 'inside' ? pointOfArc(center, angle, radius) : mid(p0, p1);\n    return { x, y };\n  })();\n\n  return {\n    ...point,\n    textAlign: position === 'inside' ? 'center' : 'start',\n    textBaseline: 'middle',\n    rotate,\n  };\n}\n\nexport function pointOfArc(center: Vector2, angle, radius): Vector2 {\n  return [\n    center[0] + Math.sin(angle) * radius,\n    center[1] - Math.cos(angle) * radius,\n  ];\n}\n\nexport function inferRotation(angle, autoRotate, rotateToAlignArc) {\n  if (!autoRotate) return 0;\n\n  const append = rotateToAlignArc ? 0 : Math.sin(angle) < 0 ? 90 : -90;\n  return (angle / Math.PI) * 180 + append;\n}\n\nfunction inferInnerCircularStyle(\n  position: LabelPosition,\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n) {\n  const {\n    y,\n    y1,\n    autoRotate,\n    rotateToAlignArc,\n    radius: radiusRatio = 0.5,\n    offset = 0,\n  } = value;\n  const arcObject = getArcObject(coordinate, points, [y, y1]);\n  const { startAngle, endAngle } = arcObject;\n  const center = coordinate.getCenter();\n\n  const angle = (startAngle + endAngle) / 2;\n  const rotate = inferRotation(angle, autoRotate, rotateToAlignArc);\n\n  const textStyle = { textAlign: 'center', textBaseline: 'middle', rotate };\n  const { innerRadius, outerRadius } = arcObject;\n  const r0 = innerRadius + (outerRadius - innerRadius) * radiusRatio;\n  const r1 = r0 + offset;\n  const [x0, y0] = pointOfArc(center, angle, r1);\n\n  return {\n    x: x0,\n    y: y0,\n    ...textStyle,\n  };\n}\n\n// Set to null will not be set with default value as below.\n// const { x = 0 } = options;\nfunction maybeUndefined(d) {\n  return d === undefined ? null : d;\n}\n\nexport function inferIdentityStyle(position, points, value, coordinate) {\n  const { bounds } = value;\n  const [p] = bounds;\n  return {\n    x: maybeUndefined(p[0]),\n    y: maybeUndefined(p[1]),\n  };\n}\n\nexport function getDefaultStyle(\n  position: LabelPosition,\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n) {\n  const { bounds } = value;\n  // When bounds.length = 1\n  // For series mark, such as line and area.\n  // The bounds for text is defined with only one point.\n  // Use this point as the label position.\n  if (bounds.length === 1) {\n    return inferIdentityStyle(position, points, value, coordinate);\n  }\n\n  const inferDefaultStyle = isRadial(coordinate)\n    ? inferRadialStyle\n    : isCircular(coordinate)\n    ? inferInnerCircularStyle\n    : inferNonCircularStyle;\n\n  return inferDefaultStyle(position, points, value, coordinate);\n}\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,sBAAsB;AACnD,SAASC,UAAU,EAAEC,QAAQ,QAAQ,2BAA2B;AAChE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,GAAG,QAAQ,uBAAuB;AAiB3C,OAAM,SAAUC,qBAAqBA,CACnCC,QAAuB,EACvBC,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB;EAEtB,MAAM;IAAEC;EAAM,CAAE,GAAGF,KAAK;EACxB,MAAM,CAAC,CAACG,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC,GAAGJ,MAAM;EACnC,MAAMK,CAAC,GAAGF,EAAE,GAAGF,EAAE;EACjB,MAAMK,CAAC,GAAGF,EAAE,GAAGF,EAAE;EACjB,MAAMK,EAAE,GAAIC,OAAO,IAAI;IACrB,MAAM;MAAEC,CAAC,EAAEC,EAAE;MAAEC,CAAC,EAAEC;IAAE,CAAE,GAAGJ,OAAO;IAChC,MAAMK,EAAE,GAAGpB,eAAe,CAACK,KAAK,CAACW,CAAC,EAAEJ,CAAC,CAAC;IACtC,MAAMS,EAAE,GAAGrB,eAAe,CAACK,KAAK,CAACa,CAAC,EAAEL,CAAC,CAAC;IACtC,OAAAS,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACKR,OAAO;MACVC,CAAC,EAAE,CAACI,EAAE,IAAIH,EAAE,IAAIT,EAAE;MAClBU,CAAC,EAAE,CAACG,EAAE,IAAIF,EAAE,IAAIV;IAAE;EAEtB,CAAC;EACD;EACA,IAAIN,QAAQ,KAAK,MAAM,EACrB,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAEL,CAAC,GAAG,CAAC;IAAEW,SAAS,EAAE,OAAO;IAAEC,YAAY,EAAE;EAAQ,CAAE,CAAC;EAC3E,IAAItB,QAAQ,KAAK,OAAO,EACtB,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAEJ,CAAC;IAAEM,CAAC,EAAEL,CAAC,GAAG,CAAC;IAAEW,SAAS,EAAE,KAAK;IAAEC,YAAY,EAAE;EAAQ,CAAE,CAAC;EACzE,IAAItB,QAAQ,KAAK,KAAK,EACpB,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAEJ,CAAC,GAAG,CAAC;IAAEM,CAAC,EAAE,CAAC;IAAEM,SAAS,EAAE,QAAQ;IAAEC,YAAY,EAAE;EAAK,CAAE,CAAC;EACzE,IAAItB,QAAQ,KAAK,QAAQ,EACvB,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAEJ,CAAC,GAAG,CAAC;IAAEM,CAAC,EAAEL,CAAC;IAAEW,SAAS,EAAE,QAAQ;IAAEC,YAAY,EAAE;EAAQ,CAAE,CAAC;EAC5E;EACA,IAAItB,QAAQ,KAAK,UAAU,EACzB,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAE,CAAC;IAAEM,SAAS,EAAE,OAAO;IAAEC,YAAY,EAAE;EAAK,CAAE,CAAC;EACpE,IAAItB,QAAQ,KAAK,WAAW,EAC1B,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAEJ,CAAC;IAAEM,CAAC,EAAE,CAAC;IAAEM,SAAS,EAAE,KAAK;IAAEC,YAAY,EAAE;EAAK,CAAE,CAAC;EAClE,IAAItB,QAAQ,KAAK,aAAa,EAC5B,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAEL,CAAC;IAAEW,SAAS,EAAE,OAAO;IAAEC,YAAY,EAAE;EAAQ,CAAE,CAAC;EACvE,IAAItB,QAAQ,KAAK,cAAc,EAC7B,OAAOW,EAAE,CAAC;IAAEE,CAAC,EAAEJ,CAAC;IAAEM,CAAC,EAAEL,CAAC;IAAEW,SAAS,EAAE,KAAK;IAAEC,YAAY,EAAE;EAAQ,CAAE,CAAC;EACrE;EACA,OAAOX,EAAE,CAAC;IACRE,CAAC,EAAEJ,CAAC,GAAG,CAAC;IACRM,CAAC,EAAEL,CAAC,GAAG,CAAC;IACRW,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE;GACf,CAAC;AACJ;AAEA,OAAM,SAAUC,gBAAgBA,CAC9BvB,QAAuB,EACvBC,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB;EAEtB,MAAM;IAAEY,CAAC;IAAEP,EAAE;IAAEgB,UAAU;IAAEC;EAAgB,CAAE,GAAGvB,KAAK;EACrD,MAAMwB,MAAM,GAAGvB,UAAU,CAACwB,SAAS,EAAE;EACrC,MAAMC,SAAS,GAAGlC,YAAY,CAACS,UAAU,EAAEF,MAAM,EAAE,CAACc,CAAC,EAAEP,EAAE,CAAC,CAAC;EAE3D,MAAM;IAAEqB,WAAW;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAQ,CAAE,GAAGJ,SAAS;EACpE,MAAMK,KAAK,GAAGjC,QAAQ,KAAK,QAAQ,GAAG,CAAC+B,UAAU,GAAGC,QAAQ,IAAI,CAAC,GAAGA,QAAQ;EAC5E,MAAME,MAAM,GAAGC,aAAa,CAACF,KAAK,EAAET,UAAU,EAAEC,gBAAgB,CAAC;EAEjE,MAAMW,KAAK,GAAG,CAAC,MAAK;IAClB,MAAM,CAACC,EAAE,EAAEC,EAAE,CAAC,GAAGrC,MAAM;IACvB,MAAMsC,MAAM,GAAGV,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAI,GAAG;IAC9D,MAAM,CAAChB,CAAC,EAAEE,CAAC,CAAC,GACVf,QAAQ,KAAK,QAAQ,GAAGwC,UAAU,CAACd,MAAM,EAAEO,KAAK,EAAEM,MAAM,CAAC,GAAGzC,GAAG,CAACuC,EAAE,EAAEC,EAAE,CAAC;IACzE,OAAO;MAAEzB,CAAC;MAAEE;IAAC,CAAE;EACjB,CAAC,EAAC,CAAE;EAEJ,OAAAI,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACKgB,KAAK;IACRf,SAAS,EAAErB,QAAQ,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;IACrDsB,YAAY,EAAE,QAAQ;IACtBY;EAAM;AAEV;AAEA,OAAM,SAAUM,UAAUA,CAACd,MAAe,EAAEO,KAAK,EAAEM,MAAM;EACvD,OAAO,CACLb,MAAM,CAAC,CAAC,CAAC,GAAGe,IAAI,CAACC,GAAG,CAACT,KAAK,CAAC,GAAGM,MAAM,EACpCb,MAAM,CAAC,CAAC,CAAC,GAAGe,IAAI,CAACE,GAAG,CAACV,KAAK,CAAC,GAAGM,MAAM,CACrC;AACH;AAEA,OAAM,SAAUJ,aAAaA,CAACF,KAAK,EAAET,UAAU,EAAEC,gBAAgB;EAC/D,IAAI,CAACD,UAAU,EAAE,OAAO,CAAC;EAEzB,MAAMoB,MAAM,GAAGnB,gBAAgB,GAAG,CAAC,GAAGgB,IAAI,CAACC,GAAG,CAACT,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;EACpE,OAAQA,KAAK,GAAGQ,IAAI,CAACI,EAAE,GAAI,GAAG,GAAGD,MAAM;AACzC;AAEA,SAASE,uBAAuBA,CAC9B9C,QAAuB,EACvBC,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB;EAEtB,MAAM;IACJY,CAAC;IACDP,EAAE;IACFgB,UAAU;IACVC,gBAAgB;IAChBc,MAAM,EAAEQ,WAAW,GAAG,GAAG;IACzBC,MAAM,GAAG;EAAC,CACX,GAAG9C,KAAK;EACT,MAAM0B,SAAS,GAAGlC,YAAY,CAACS,UAAU,EAAEF,MAAM,EAAE,CAACc,CAAC,EAAEP,EAAE,CAAC,CAAC;EAC3D,MAAM;IAAEuB,UAAU;IAAEC;EAAQ,CAAE,GAAGJ,SAAS;EAC1C,MAAMF,MAAM,GAAGvB,UAAU,CAACwB,SAAS,EAAE;EAErC,MAAMM,KAAK,GAAG,CAACF,UAAU,GAAGC,QAAQ,IAAI,CAAC;EACzC,MAAME,MAAM,GAAGC,aAAa,CAACF,KAAK,EAAET,UAAU,EAAEC,gBAAgB,CAAC;EAEjE,MAAMwB,SAAS,GAAG;IAAE5B,SAAS,EAAE,QAAQ;IAAEC,YAAY,EAAE,QAAQ;IAAEY;EAAM,CAAE;EACzE,MAAM;IAAEL,WAAW;IAAEC;EAAW,CAAE,GAAGF,SAAS;EAC9C,MAAMsB,EAAE,GAAGrB,WAAW,GAAG,CAACC,WAAW,GAAGD,WAAW,IAAIkB,WAAW;EAClE,MAAMI,EAAE,GAAGD,EAAE,GAAGF,MAAM;EACtB,MAAM,CAAC3C,EAAE,EAAEC,EAAE,CAAC,GAAGkC,UAAU,CAACd,MAAM,EAAEO,KAAK,EAAEkB,EAAE,CAAC;EAE9C,OAAAhC,MAAA,CAAAC,MAAA;IACEP,CAAC,EAAER,EAAE;IACLU,CAAC,EAAET;EAAE,GACF2C,SAAS;AAEhB;AAEA;AACA;AACA,SAASG,cAAcA,CAACC,CAAC;EACvB,OAAOA,CAAC,KAAKC,SAAS,GAAG,IAAI,GAAGD,CAAC;AACnC;AAEA,OAAM,SAAUE,kBAAkBA,CAACvD,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU;EACpE,MAAM;IAAEC;EAAM,CAAE,GAAGF,KAAK;EACxB,MAAM,CAACsD,CAAC,CAAC,GAAGpD,MAAM;EAClB,OAAO;IACLS,CAAC,EAAEuC,cAAc,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC;IACvBzC,CAAC,EAAEqC,cAAc,CAACI,CAAC,CAAC,CAAC,CAAC;GACvB;AACH;AAEA,OAAM,SAAUC,eAAeA,CAC7BzD,QAAuB,EACvBC,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB;EAEtB,MAAM;IAAEC;EAAM,CAAE,GAAGF,KAAK;EACxB;EACA;EACA;EACA;EACA,IAAIE,MAAM,CAACsD,MAAM,KAAK,CAAC,EAAE;IACvB,OAAOH,kBAAkB,CAACvD,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,CAAC;;EAGhE,MAAMwD,iBAAiB,GAAG/D,QAAQ,CAACO,UAAU,CAAC,GAC1CoB,gBAAgB,GAChB5B,UAAU,CAACQ,UAAU,CAAC,GACtB2C,uBAAuB,GACvB/C,qBAAqB;EAEzB,OAAO4D,iBAAiB,CAAC3D,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,CAAC;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}