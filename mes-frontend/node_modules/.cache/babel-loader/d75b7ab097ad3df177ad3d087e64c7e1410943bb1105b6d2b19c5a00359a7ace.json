{"ast": null, "code": "var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\nmodule.exports = cloneTypedArray;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "cloneTypedArray", "typedArray", "isDeep", "buffer", "constructor", "byteOffset", "length", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_cloneTypedArray.js"], "sourcesContent": ["var cloneArrayBuffer = require('./_cloneArrayBuffer');\n\n/**\n * Creates a clone of `typedArray`.\n *\n * @private\n * @param {Object} typedArray The typed array to clone.\n * @param {boolean} [isDeep] Specify a deep clone.\n * @returns {Object} Returns the cloned typed array.\n */\nfunction cloneTypedArray(typedArray, isDeep) {\n  var buffer = isDeep ? cloneArrayBuffer(typedArray.buffer) : typedArray.buffer;\n  return new typedArray.constructor(buffer, typedArray.byteOffset, typedArray.length);\n}\n\nmodule.exports = cloneTypedArray;\n"], "mappings": "AAAA,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,qBAAqB,CAAC;;AAErD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,UAAU,EAAEC,MAAM,EAAE;EAC3C,IAAIC,MAAM,GAAGD,MAAM,GAAGJ,gBAAgB,CAACG,UAAU,CAACE,MAAM,CAAC,GAAGF,UAAU,CAACE,MAAM;EAC7E,OAAO,IAAIF,UAAU,CAACG,WAAW,CAACD,MAAM,EAAEF,UAAU,CAACI,UAAU,EAAEJ,UAAU,CAACK,MAAM,CAAC;AACrF;AAEAC,MAAM,CAACC,OAAO,GAAGR,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}