{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"../lodash\");\nvar Graph = require(\"../graphlib\").Graph;\nvar util = require(\"../util\");\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nmodule.exports = {\n  positionX: positionX,\n  findType1Conflicts: findType1Conflicts,\n  findType2Conflicts: findType2Conflicts,\n  addConflict: addConflict,\n  hasConflict: hasConflict,\n  verticalAlignment: verticalAlignment,\n  horizontalCompaction: horizontalCompaction,\n  alignCoordinates: alignCoordinates,\n  findSmallestWidthAlignment: findSmallestWidthAlignment,\n  balance: balance\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n  function visitLayer(prevLayer, layer) {\n    var\n      // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n    _.forEach(layer, function (v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i + 1), function (scanNode) {\n          _.forEach(g.predecessors(scanNode), function (u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n    return layer;\n  }\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function (i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function (u) {\n          var uNode = g.node(u);\n          if (uNode.dummy && (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n    _.forEach(south, function (v, southLookahead) {\n      if (g.node(v).dummy === \"border\") {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n    return south;\n  }\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function (u) {\n      return g.node(u).dummy;\n    });\n  }\n}\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return _.has(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function (layer) {\n    _.forEach(layer, function (v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n  _.forEach(layering, function (layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function (v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function (w) {\n          return pos[w];\n        });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n  return {\n    root: root,\n    align: align\n  };\n}\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? \"borderLeft\" : \"borderRight\";\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function (acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function (acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function (v) {\n    xs[v] = xs[root[v]];\n  });\n  return xs;\n}\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n  _.forEach(layering, function (layer) {\n    var u;\n    _.forEach(layer, function (v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n  _.forEach([\"u\", \"d\"], function (vert) {\n    _.forEach([\"l\", \"r\"], function (horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n      var xsVals = _.values(xs);\n      delta = horiz === \"l\" ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function (x) {\n          return x + delta;\n        });\n      }\n    });\n  });\n}\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function (ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n  var xss = {};\n  var adjustedLayering;\n  _.forEach([\"u\", \"d\"], function (vert) {\n    adjustedLayering = vert === \"u\" ? layering : _.values(layering).reverse();\n    _.forEach([\"l\", \"r\"], function (horiz) {\n      if (horiz === \"r\") {\n        adjustedLayering = _.map(adjustedLayering, function (inner) {\n          return _.values(inner).reverse();\n        });\n      }\n      var neighborFn = (vert === \"u\" ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, horiz === \"r\");\n      if (horiz === \"r\") {\n        xs = _.mapValues(xs, function (x) {\n          return -x;\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function (g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n    sum += vLabel.width / 2;\n    if (_.has(vLabel, \"labelpos\")) {\n      switch (vLabel.labelpos.toLowerCase()) {\n        case \"l\":\n          delta = -vLabel.width / 2;\n          break;\n        case \"r\":\n          delta = vLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += wLabel.width / 2;\n    if (_.has(wLabel, \"labelpos\")) {\n      switch (wLabel.labelpos.toLowerCase()) {\n        case \"l\":\n          delta = wLabel.width / 2;\n          break;\n        case \"r\":\n          delta = -wLabel.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n    return sum;\n  };\n}\nfunction width(g, v) {\n  return g.node(v).width;\n}", "map": {"version": 3, "names": ["_", "require", "Graph", "util", "module", "exports", "positionX", "findType1Conflicts", "findType2Conflicts", "addConflict", "hasConflict", "verticalAlignment", "horizontalCompaction", "alignCoordinates", "findSmallestWidthAlignment", "balance", "g", "layering", "conflicts", "<PERSON><PERSON><PERSON><PERSON>", "prevLayer", "layer", "k0", "scanPos", "prevL<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "lastNode", "last", "for<PERSON>ach", "v", "i", "w", "findOtherInnerSegmentNode", "k1", "node", "order", "slice", "scanNode", "predecessors", "u", "uLabel", "uPos", "dummy", "reduce", "scan", "south", "southPos", "southEnd", "prevNorthBorder", "nextNorthBorder", "range", "uNode", "north", "prevNorthPos", "nextNorthPos", "southLookahead", "find", "tmp", "conflictsV", "has", "neighborFn", "root", "align", "pos", "prevIdx", "ws", "sortBy", "mp", "Math", "floor", "il", "ceil", "reverseSep", "xs", "blockG", "buildBlockGraph", "borderType", "iterate", "setXsFunc", "nextNodesFunc", "stack", "nodes", "elem", "pop", "visited", "push", "concat", "pass1", "inEdges", "acc", "e", "max", "edge", "pass2", "min", "outEdges", "Number", "POSITIVE_INFINITY", "bind", "successors", "blockGraph", "graphLabel", "graph", "sepFn", "sep", "nodesep", "edgesep", "vRoot", "setNode", "uRoot", "prevMax", "setEdge", "xss", "minBy", "values", "NEGATIVE_INFINITY", "forIn", "x", "halfWidth", "width", "alignTo", "alignToVals", "alignToMin", "alignToMax", "vert", "horiz", "alignment", "delta", "xsVals", "mapValues", "ul", "ignore", "toLowerCase", "map", "buildLayerMatrix", "merge", "adjustedLayering", "reverse", "inner", "smallestWidth", "nodeSep", "edgeSep", "vLabel", "w<PERSON><PERSON><PERSON>", "sum", "labelpos"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/position/bk.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"../lodash\");\nvar Graph = require(\"../graphlib\").Graph;\nvar util = require(\"../util\");\n\n/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\n\nmodule.exports = {\n  positionX: positionX,\n  findType1Conflicts: findType1Conflicts,\n  findType2Conflicts: findType2Conflicts,\n  addConflict: addConflict,\n  hasConflict: hasConflict,\n  verticalAlignment: verticalAlignment,\n  horizontalCompaction: horizontalCompaction,\n  alignCoordinates: alignCoordinates,\n  findSmallestWidthAlignment: findSmallestWidthAlignment,\n  balance: balance\n};\n\n/*\n * Marks all edges in the graph with a type-1 conflict with the \"type1Conflict\"\n * property. A type-1 conflict is one where a non-inner segment crosses an\n * inner segment. An inner segment is an edge with both incident nodes marked\n * with the \"dummy\" property.\n *\n * This algorithm scans layer by layer, starting with the second, for type-1\n * conflicts between the current layer and the previous layer. For each layer\n * it scans the nodes from left to right until it reaches one that is incident\n * on an inner segment. It then scans predecessors to determine if they have\n * edges that cross that inner segment. At the end a final scan is done for all\n * nodes on the current rank to see if they cross the last visited inner\n * segment.\n *\n * This algorithm (safely) assumes that a dummy node will only be incident on a\n * single node in the layers being scanned.\n */\nfunction findType1Conflicts(g, layering) {\n  var conflicts = {};\n\n  function visitLayer(prevLayer, layer) {\n    var\n      // last visited node in the previous layer that is incident on an inner\n      // segment.\n      k0 = 0,\n      // Tracks the last node in this layer scanned for crossings with a type-1\n      // segment.\n      scanPos = 0,\n      prevLayerLength = prevLayer.length,\n      lastNode = _.last(layer);\n\n    _.forEach(layer, function(v, i) {\n      var w = findOtherInnerSegmentNode(g, v),\n        k1 = w ? g.node(w).order : prevLayerLength;\n\n      if (w || v === lastNode) {\n        _.forEach(layer.slice(scanPos, i +1), function(scanNode) {\n          _.forEach(g.predecessors(scanNode), function(u) {\n            var uLabel = g.node(u),\n              uPos = uLabel.order;\n            if ((uPos < k0 || k1 < uPos) &&\n                !(uLabel.dummy && g.node(scanNode).dummy)) {\n              addConflict(conflicts, u, scanNode);\n            }\n          });\n        });\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n\n    return layer;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findType2Conflicts(g, layering) {\n  var conflicts = {};\n\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var v;\n    _.forEach(_.range(southPos, southEnd), function(i) {\n      v = south[i];\n      if (g.node(v).dummy) {\n        _.forEach(g.predecessors(v), function(u) {\n          var uNode = g.node(u);\n          if (uNode.dummy &&\n              (uNode.order < prevNorthBorder || uNode.order > nextNorthBorder)) {\n            addConflict(conflicts, u, v);\n          }\n        });\n      }\n    });\n  }\n\n\n  function visitLayer(north, south) {\n    var prevNorthPos = -1,\n      nextNorthPos,\n      southPos = 0;\n\n    _.forEach(south, function(v, southLookahead) {\n      if (g.node(v).dummy === \"border\") {\n        var predecessors = g.predecessors(v);\n        if (predecessors.length) {\n          nextNorthPos = g.node(predecessors[0]).order;\n          scan(south, southPos, southLookahead, prevNorthPos, nextNorthPos);\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scan(south, southPos, south.length, nextNorthPos, north.length);\n    });\n\n    return south;\n  }\n\n  _.reduce(layering, visitLayer);\n  return conflicts;\n}\n\nfunction findOtherInnerSegmentNode(g, v) {\n  if (g.node(v).dummy) {\n    return _.find(g.predecessors(v), function(u) {\n      return g.node(u).dummy;\n    });\n  }\n}\n\nfunction addConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n\n  var conflictsV = conflicts[v];\n  if (!conflictsV) {\n    conflicts[v] = conflictsV = {};\n  }\n  conflictsV[w] = true;\n}\n\nfunction hasConflict(conflicts, v, w) {\n  if (v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return _.has(conflicts[v], w);\n}\n\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nfunction verticalAlignment(g, layering, conflicts, neighborFn) {\n  var root = {},\n    align = {},\n    pos = {};\n\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  _.forEach(layering, function(layer) {\n    _.forEach(layer, function(v, order) {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n\n  _.forEach(layering, function(layer) {\n    var prevIdx = -1;\n    _.forEach(layer, function(v) {\n      var ws = neighborFn(v);\n      if (ws.length) {\n        ws = _.sortBy(ws, function(w) { return pos[w]; });\n        var mp = (ws.length - 1) / 2;\n        for (var i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          var w = ws[i];\n          if (align[v] === v &&\n              prevIdx < pos[w] &&\n              !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n\n  return { root: root, align: align };\n}\n\nfunction horizontalCompaction(g, layering, root, align, reverseSep) {\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  var xs = {},\n    blockG = buildBlockGraph(g, layering, root, reverseSep),\n    borderType = reverseSep ? \"borderLeft\" : \"borderRight\";\n\n  function iterate(setXsFunc, nextNodesFunc) {\n    var stack = blockG.nodes();\n    var elem = stack.pop();\n    var visited = {};\n    while (elem) {\n      if (visited[elem]) {\n        setXsFunc(elem);\n      } else {\n        visited[elem] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem));\n      }\n\n      elem = stack.pop();\n    }\n  }\n\n  // First pass, assign smallest coordinates\n  function pass1(elem) {\n    xs[elem] = blockG.inEdges(elem).reduce(function(acc, e) {\n      return Math.max(acc, xs[e.v] + blockG.edge(e));\n    }, 0);\n  }\n\n  // Second pass, assign greatest coordinates\n  function pass2(elem) {\n    var min = blockG.outEdges(elem).reduce(function(acc, e) {\n      return Math.min(acc, xs[e.w] - blockG.edge(e));\n    }, Number.POSITIVE_INFINITY);\n\n    var node = g.node(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  }\n\n  iterate(pass1, blockG.predecessors.bind(blockG));\n  iterate(pass2, blockG.successors.bind(blockG));\n\n  // Assign x coordinates to all nodes\n  _.forEach(align, function(v) {\n    xs[v] = xs[root[v]];\n  });\n\n  return xs;\n}\n\n\nfunction buildBlockGraph(g, layering, root, reverseSep) {\n  var blockGraph = new Graph(),\n    graphLabel = g.graph(),\n    sepFn = sep(graphLabel.nodesep, graphLabel.edgesep, reverseSep);\n\n  _.forEach(layering, function(layer) {\n    var u;\n    _.forEach(layer, function(v) {\n      var vRoot = root[v];\n      blockGraph.setNode(vRoot);\n      if (u) {\n        var uRoot = root[u],\n          prevMax = blockGraph.edge(uRoot, vRoot);\n        blockGraph.setEdge(uRoot, vRoot, Math.max(sepFn(g, v, u), prevMax || 0));\n      }\n      u = v;\n    });\n  });\n\n  return blockGraph;\n}\n\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nfunction findSmallestWidthAlignment(g, xss) {\n  return _.minBy(_.values(xss), function (xs) {\n    var max = Number.NEGATIVE_INFINITY;\n    var min = Number.POSITIVE_INFINITY;\n\n    _.forIn(xs, function (x, v) {\n      var halfWidth = width(g, v) / 2;\n\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n\n    return max - min;\n  });\n}\n\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nfunction alignCoordinates(xss, alignTo) {\n  var alignToVals = _.values(alignTo),\n    alignToMin = _.min(alignToVals),\n    alignToMax = _.max(alignToVals);\n\n  _.forEach([\"u\", \"d\"], function(vert) {\n    _.forEach([\"l\", \"r\"], function(horiz) {\n      var alignment = vert + horiz,\n        xs = xss[alignment],\n        delta;\n      if (xs === alignTo) return;\n\n      var xsVals = _.values(xs);\n      delta = horiz === \"l\" ? alignToMin - _.min(xsVals) : alignToMax - _.max(xsVals);\n\n      if (delta) {\n        xss[alignment] = _.mapValues(xs, function(x) { return x + delta; });\n      }\n    });\n  });\n}\n\nfunction balance(xss, align) {\n  return _.mapValues(xss.ul, function(ignore, v) {\n    if (align) {\n      return xss[align.toLowerCase()][v];\n    } else {\n      var xs = _.sortBy(_.map(xss, v));\n      return (xs[1] + xs[2]) / 2;\n    }\n  });\n}\n\nfunction positionX(g) {\n  var layering = util.buildLayerMatrix(g);\n  var conflicts = _.merge(\n    findType1Conflicts(g, layering),\n    findType2Conflicts(g, layering));\n\n  var xss = {};\n  var adjustedLayering;\n  _.forEach([\"u\", \"d\"], function(vert) {\n    adjustedLayering = vert === \"u\" ? layering : _.values(layering).reverse();\n    _.forEach([\"l\", \"r\"], function(horiz) {\n      if (horiz === \"r\") {\n        adjustedLayering = _.map(adjustedLayering, function(inner) {\n          return _.values(inner).reverse();\n        });\n      }\n\n      var neighborFn = (vert === \"u\" ? g.predecessors : g.successors).bind(g);\n      var align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      var xs = horizontalCompaction(g, adjustedLayering,\n        align.root, align.align, horiz === \"r\");\n      if (horiz === \"r\") {\n        xs = _.mapValues(xs, function(x) { return -x; });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n\n  var smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, g.graph().align);\n}\n\nfunction sep(nodeSep, edgeSep, reverseSep) {\n  return function(g, v, w) {\n    var vLabel = g.node(v);\n    var wLabel = g.node(w);\n    var sum = 0;\n    var delta;\n\n    sum += vLabel.width / 2;\n    if (_.has(vLabel, \"labelpos\")) {\n      switch (vLabel.labelpos.toLowerCase()) {\n      case \"l\": delta = -vLabel.width / 2; break;\n      case \"r\": delta = vLabel.width / 2; break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    sum += (vLabel.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.dummy ? edgeSep : nodeSep) / 2;\n\n    sum += wLabel.width / 2;\n    if (_.has(wLabel, \"labelpos\")) {\n      switch (wLabel.labelpos.toLowerCase()) {\n      case \"l\": delta = wLabel.width / 2; break;\n      case \"r\": delta = -wLabel.width / 2; break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n\n    return sum;\n  };\n}\n\nfunction width(g, v) {\n  return g.node(v).width;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC5B,IAAIC,KAAK,GAAGD,OAAO,CAAC,aAAa,CAAC,CAACC,KAAK;AACxC,IAAIC,IAAI,GAAGF,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA;AACA;AACA;;AAEAG,MAAM,CAACC,OAAO,GAAG;EACfC,SAAS,EAAEA,SAAS;EACpBC,kBAAkB,EAAEA,kBAAkB;EACtCC,kBAAkB,EAAEA,kBAAkB;EACtCC,WAAW,EAAEA,WAAW;EACxBC,WAAW,EAAEA,WAAW;EACxBC,iBAAiB,EAAEA,iBAAiB;EACpCC,oBAAoB,EAAEA,oBAAoB;EAC1CC,gBAAgB,EAAEA,gBAAgB;EAClCC,0BAA0B,EAAEA,0BAA0B;EACtDC,OAAO,EAAEA;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASR,kBAAkBA,CAACS,CAAC,EAAEC,QAAQ,EAAE;EACvC,IAAIC,SAAS,GAAG,CAAC,CAAC;EAElB,SAASC,UAAUA,CAACC,SAAS,EAAEC,KAAK,EAAE;IACpC;MACE;MACA;MACAC,EAAE,GAAG,CAAC;MACN;MACA;MACAC,OAAO,GAAG,CAAC;MACXC,eAAe,GAAGJ,SAAS,CAACK,MAAM;MAClCC,QAAQ,GAAG1B,CAAC,CAAC2B,IAAI,CAACN,KAAK,CAAC;IAE1BrB,CAAC,CAAC4B,OAAO,CAACP,KAAK,EAAE,UAASQ,CAAC,EAAEC,CAAC,EAAE;MAC9B,IAAIC,CAAC,GAAGC,yBAAyB,CAAChB,CAAC,EAAEa,CAAC,CAAC;QACrCI,EAAE,GAAGF,CAAC,GAAGf,CAAC,CAACkB,IAAI,CAACH,CAAC,CAAC,CAACI,KAAK,GAAGX,eAAe;MAE5C,IAAIO,CAAC,IAAIF,CAAC,KAAKH,QAAQ,EAAE;QACvB1B,CAAC,CAAC4B,OAAO,CAACP,KAAK,CAACe,KAAK,CAACb,OAAO,EAAEO,CAAC,GAAE,CAAC,CAAC,EAAE,UAASO,QAAQ,EAAE;UACvDrC,CAAC,CAAC4B,OAAO,CAACZ,CAAC,CAACsB,YAAY,CAACD,QAAQ,CAAC,EAAE,UAASE,CAAC,EAAE;YAC9C,IAAIC,MAAM,GAAGxB,CAAC,CAACkB,IAAI,CAACK,CAAC,CAAC;cACpBE,IAAI,GAAGD,MAAM,CAACL,KAAK;YACrB,IAAI,CAACM,IAAI,GAAGnB,EAAE,IAAIW,EAAE,GAAGQ,IAAI,KACvB,EAAED,MAAM,CAACE,KAAK,IAAI1B,CAAC,CAACkB,IAAI,CAACG,QAAQ,CAAC,CAACK,KAAK,CAAC,EAAE;cAC7CjC,WAAW,CAACS,SAAS,EAAEqB,CAAC,EAAEF,QAAQ,CAAC;YACrC;UACF,CAAC,CAAC;QACJ,CAAC,CAAC;QACFd,OAAO,GAAGO,CAAC,GAAG,CAAC;QACfR,EAAE,GAAGW,EAAE;MACT;IACF,CAAC,CAAC;IAEF,OAAOZ,KAAK;EACd;EAEArB,CAAC,CAAC2C,MAAM,CAAC1B,QAAQ,EAAEE,UAAU,CAAC;EAC9B,OAAOD,SAAS;AAClB;AAEA,SAASV,kBAAkBA,CAACQ,CAAC,EAAEC,QAAQ,EAAE;EACvC,IAAIC,SAAS,GAAG,CAAC,CAAC;EAElB,SAAS0B,IAAIA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,eAAe,EAAE;IACzE,IAAIpB,CAAC;IACL7B,CAAC,CAAC4B,OAAO,CAAC5B,CAAC,CAACkD,KAAK,CAACJ,QAAQ,EAAEC,QAAQ,CAAC,EAAE,UAASjB,CAAC,EAAE;MACjDD,CAAC,GAAGgB,KAAK,CAACf,CAAC,CAAC;MACZ,IAAId,CAAC,CAACkB,IAAI,CAACL,CAAC,CAAC,CAACa,KAAK,EAAE;QACnB1C,CAAC,CAAC4B,OAAO,CAACZ,CAAC,CAACsB,YAAY,CAACT,CAAC,CAAC,EAAE,UAASU,CAAC,EAAE;UACvC,IAAIY,KAAK,GAAGnC,CAAC,CAACkB,IAAI,CAACK,CAAC,CAAC;UACrB,IAAIY,KAAK,CAACT,KAAK,KACVS,KAAK,CAAChB,KAAK,GAAGa,eAAe,IAAIG,KAAK,CAAChB,KAAK,GAAGc,eAAe,CAAC,EAAE;YACpExC,WAAW,CAACS,SAAS,EAAEqB,CAAC,EAAEV,CAAC,CAAC;UAC9B;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAGA,SAASV,UAAUA,CAACiC,KAAK,EAAEP,KAAK,EAAE;IAChC,IAAIQ,YAAY,GAAG,CAAC,CAAC;MACnBC,YAAY;MACZR,QAAQ,GAAG,CAAC;IAEd9C,CAAC,CAAC4B,OAAO,CAACiB,KAAK,EAAE,UAAShB,CAAC,EAAE0B,cAAc,EAAE;MAC3C,IAAIvC,CAAC,CAACkB,IAAI,CAACL,CAAC,CAAC,CAACa,KAAK,KAAK,QAAQ,EAAE;QAChC,IAAIJ,YAAY,GAAGtB,CAAC,CAACsB,YAAY,CAACT,CAAC,CAAC;QACpC,IAAIS,YAAY,CAACb,MAAM,EAAE;UACvB6B,YAAY,GAAGtC,CAAC,CAACkB,IAAI,CAACI,YAAY,CAAC,CAAC,CAAC,CAAC,CAACH,KAAK;UAC5CS,IAAI,CAACC,KAAK,EAAEC,QAAQ,EAAES,cAAc,EAAEF,YAAY,EAAEC,YAAY,CAAC;UACjER,QAAQ,GAAGS,cAAc;UACzBF,YAAY,GAAGC,YAAY;QAC7B;MACF;MACAV,IAAI,CAACC,KAAK,EAAEC,QAAQ,EAAED,KAAK,CAACpB,MAAM,EAAE6B,YAAY,EAAEF,KAAK,CAAC3B,MAAM,CAAC;IACjE,CAAC,CAAC;IAEF,OAAOoB,KAAK;EACd;EAEA7C,CAAC,CAAC2C,MAAM,CAAC1B,QAAQ,EAAEE,UAAU,CAAC;EAC9B,OAAOD,SAAS;AAClB;AAEA,SAASc,yBAAyBA,CAAChB,CAAC,EAAEa,CAAC,EAAE;EACvC,IAAIb,CAAC,CAACkB,IAAI,CAACL,CAAC,CAAC,CAACa,KAAK,EAAE;IACnB,OAAO1C,CAAC,CAACwD,IAAI,CAACxC,CAAC,CAACsB,YAAY,CAACT,CAAC,CAAC,EAAE,UAASU,CAAC,EAAE;MAC3C,OAAOvB,CAAC,CAACkB,IAAI,CAACK,CAAC,CAAC,CAACG,KAAK;IACxB,CAAC,CAAC;EACJ;AACF;AAEA,SAASjC,WAAWA,CAACS,SAAS,EAAEW,CAAC,EAAEE,CAAC,EAAE;EACpC,IAAIF,CAAC,GAAGE,CAAC,EAAE;IACT,IAAI0B,GAAG,GAAG5B,CAAC;IACXA,CAAC,GAAGE,CAAC;IACLA,CAAC,GAAG0B,GAAG;EACT;EAEA,IAAIC,UAAU,GAAGxC,SAAS,CAACW,CAAC,CAAC;EAC7B,IAAI,CAAC6B,UAAU,EAAE;IACfxC,SAAS,CAACW,CAAC,CAAC,GAAG6B,UAAU,GAAG,CAAC,CAAC;EAChC;EACAA,UAAU,CAAC3B,CAAC,CAAC,GAAG,IAAI;AACtB;AAEA,SAASrB,WAAWA,CAACQ,SAAS,EAAEW,CAAC,EAAEE,CAAC,EAAE;EACpC,IAAIF,CAAC,GAAGE,CAAC,EAAE;IACT,IAAI0B,GAAG,GAAG5B,CAAC;IACXA,CAAC,GAAGE,CAAC;IACLA,CAAC,GAAG0B,GAAG;EACT;EACA,OAAOzD,CAAC,CAAC2D,GAAG,CAACzC,SAAS,CAACW,CAAC,CAAC,EAAEE,CAAC,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASpB,iBAAiBA,CAACK,CAAC,EAAEC,QAAQ,EAAEC,SAAS,EAAE0C,UAAU,EAAE;EAC7D,IAAIC,IAAI,GAAG,CAAC,CAAC;IACXC,KAAK,GAAG,CAAC,CAAC;IACVC,GAAG,GAAG,CAAC,CAAC;;EAEV;EACA;EACA;EACA/D,CAAC,CAAC4B,OAAO,CAACX,QAAQ,EAAE,UAASI,KAAK,EAAE;IAClCrB,CAAC,CAAC4B,OAAO,CAACP,KAAK,EAAE,UAASQ,CAAC,EAAEM,KAAK,EAAE;MAClC0B,IAAI,CAAChC,CAAC,CAAC,GAAGA,CAAC;MACXiC,KAAK,CAACjC,CAAC,CAAC,GAAGA,CAAC;MACZkC,GAAG,CAAClC,CAAC,CAAC,GAAGM,KAAK;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnC,CAAC,CAAC4B,OAAO,CAACX,QAAQ,EAAE,UAASI,KAAK,EAAE;IAClC,IAAI2C,OAAO,GAAG,CAAC,CAAC;IAChBhE,CAAC,CAAC4B,OAAO,CAACP,KAAK,EAAE,UAASQ,CAAC,EAAE;MAC3B,IAAIoC,EAAE,GAAGL,UAAU,CAAC/B,CAAC,CAAC;MACtB,IAAIoC,EAAE,CAACxC,MAAM,EAAE;QACbwC,EAAE,GAAGjE,CAAC,CAACkE,MAAM,CAACD,EAAE,EAAE,UAASlC,CAAC,EAAE;UAAE,OAAOgC,GAAG,CAAChC,CAAC,CAAC;QAAE,CAAC,CAAC;QACjD,IAAIoC,EAAE,GAAG,CAACF,EAAE,CAACxC,MAAM,GAAG,CAAC,IAAI,CAAC;QAC5B,KAAK,IAAIK,CAAC,GAAGsC,IAAI,CAACC,KAAK,CAACF,EAAE,CAAC,EAAEG,EAAE,GAAGF,IAAI,CAACG,IAAI,CAACJ,EAAE,CAAC,EAAErC,CAAC,IAAIwC,EAAE,EAAE,EAAExC,CAAC,EAAE;UAC7D,IAAIC,CAAC,GAAGkC,EAAE,CAACnC,CAAC,CAAC;UACb,IAAIgC,KAAK,CAACjC,CAAC,CAAC,KAAKA,CAAC,IACdmC,OAAO,GAAGD,GAAG,CAAChC,CAAC,CAAC,IAChB,CAACrB,WAAW,CAACQ,SAAS,EAAEW,CAAC,EAAEE,CAAC,CAAC,EAAE;YACjC+B,KAAK,CAAC/B,CAAC,CAAC,GAAGF,CAAC;YACZiC,KAAK,CAACjC,CAAC,CAAC,GAAGgC,IAAI,CAAChC,CAAC,CAAC,GAAGgC,IAAI,CAAC9B,CAAC,CAAC;YAC5BiC,OAAO,GAAGD,GAAG,CAAChC,CAAC,CAAC;UAClB;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAO;IAAE8B,IAAI,EAAEA,IAAI;IAAEC,KAAK,EAAEA;EAAM,CAAC;AACrC;AAEA,SAASlD,oBAAoBA,CAACI,CAAC,EAAEC,QAAQ,EAAE4C,IAAI,EAAEC,KAAK,EAAEU,UAAU,EAAE;EAClE;EACA;EACA;EACA;EACA;EACA,IAAIC,EAAE,GAAG,CAAC,CAAC;IACTC,MAAM,GAAGC,eAAe,CAAC3D,CAAC,EAAEC,QAAQ,EAAE4C,IAAI,EAAEW,UAAU,CAAC;IACvDI,UAAU,GAAGJ,UAAU,GAAG,YAAY,GAAG,aAAa;EAExD,SAASK,OAAOA,CAACC,SAAS,EAAEC,aAAa,EAAE;IACzC,IAAIC,KAAK,GAAGN,MAAM,CAACO,KAAK,CAAC,CAAC;IAC1B,IAAIC,IAAI,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;IACtB,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,OAAOF,IAAI,EAAE;MACX,IAAIE,OAAO,CAACF,IAAI,CAAC,EAAE;QACjBJ,SAAS,CAACI,IAAI,CAAC;MACjB,CAAC,MAAM;QACLE,OAAO,CAACF,IAAI,CAAC,GAAG,IAAI;QACpBF,KAAK,CAACK,IAAI,CAACH,IAAI,CAAC;QAChBF,KAAK,GAAGA,KAAK,CAACM,MAAM,CAACP,aAAa,CAACG,IAAI,CAAC,CAAC;MAC3C;MAEAA,IAAI,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;IACpB;EACF;;EAEA;EACA,SAASI,KAAKA,CAACL,IAAI,EAAE;IACnBT,EAAE,CAACS,IAAI,CAAC,GAAGR,MAAM,CAACc,OAAO,CAACN,IAAI,CAAC,CAACvC,MAAM,CAAC,UAAS8C,GAAG,EAAEC,CAAC,EAAE;MACtD,OAAOtB,IAAI,CAACuB,GAAG,CAACF,GAAG,EAAEhB,EAAE,CAACiB,CAAC,CAAC7D,CAAC,CAAC,GAAG6C,MAAM,CAACkB,IAAI,CAACF,CAAC,CAAC,CAAC;IAChD,CAAC,EAAE,CAAC,CAAC;EACP;;EAEA;EACA,SAASG,KAAKA,CAACX,IAAI,EAAE;IACnB,IAAIY,GAAG,GAAGpB,MAAM,CAACqB,QAAQ,CAACb,IAAI,CAAC,CAACvC,MAAM,CAAC,UAAS8C,GAAG,EAAEC,CAAC,EAAE;MACtD,OAAOtB,IAAI,CAAC0B,GAAG,CAACL,GAAG,EAAEhB,EAAE,CAACiB,CAAC,CAAC3D,CAAC,CAAC,GAAG2C,MAAM,CAACkB,IAAI,CAACF,CAAC,CAAC,CAAC;IAChD,CAAC,EAAEM,MAAM,CAACC,iBAAiB,CAAC;IAE5B,IAAI/D,IAAI,GAAGlB,CAAC,CAACkB,IAAI,CAACgD,IAAI,CAAC;IACvB,IAAIY,GAAG,KAAKE,MAAM,CAACC,iBAAiB,IAAI/D,IAAI,CAAC0C,UAAU,KAAKA,UAAU,EAAE;MACtEH,EAAE,CAACS,IAAI,CAAC,GAAGd,IAAI,CAACuB,GAAG,CAAClB,EAAE,CAACS,IAAI,CAAC,EAAEY,GAAG,CAAC;IACpC;EACF;EAEAjB,OAAO,CAACU,KAAK,EAAEb,MAAM,CAACpC,YAAY,CAAC4D,IAAI,CAACxB,MAAM,CAAC,CAAC;EAChDG,OAAO,CAACgB,KAAK,EAAEnB,MAAM,CAACyB,UAAU,CAACD,IAAI,CAACxB,MAAM,CAAC,CAAC;;EAE9C;EACA1E,CAAC,CAAC4B,OAAO,CAACkC,KAAK,EAAE,UAASjC,CAAC,EAAE;IAC3B4C,EAAE,CAAC5C,CAAC,CAAC,GAAG4C,EAAE,CAACZ,IAAI,CAAChC,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC;EAEF,OAAO4C,EAAE;AACX;AAGA,SAASE,eAAeA,CAAC3D,CAAC,EAAEC,QAAQ,EAAE4C,IAAI,EAAEW,UAAU,EAAE;EACtD,IAAI4B,UAAU,GAAG,IAAIlG,KAAK,CAAC,CAAC;IAC1BmG,UAAU,GAAGrF,CAAC,CAACsF,KAAK,CAAC,CAAC;IACtBC,KAAK,GAAGC,GAAG,CAACH,UAAU,CAACI,OAAO,EAAEJ,UAAU,CAACK,OAAO,EAAElC,UAAU,CAAC;EAEjExE,CAAC,CAAC4B,OAAO,CAACX,QAAQ,EAAE,UAASI,KAAK,EAAE;IAClC,IAAIkB,CAAC;IACLvC,CAAC,CAAC4B,OAAO,CAACP,KAAK,EAAE,UAASQ,CAAC,EAAE;MAC3B,IAAI8E,KAAK,GAAG9C,IAAI,CAAChC,CAAC,CAAC;MACnBuE,UAAU,CAACQ,OAAO,CAACD,KAAK,CAAC;MACzB,IAAIpE,CAAC,EAAE;QACL,IAAIsE,KAAK,GAAGhD,IAAI,CAACtB,CAAC,CAAC;UACjBuE,OAAO,GAAGV,UAAU,CAACR,IAAI,CAACiB,KAAK,EAAEF,KAAK,CAAC;QACzCP,UAAU,CAACW,OAAO,CAACF,KAAK,EAAEF,KAAK,EAAEvC,IAAI,CAACuB,GAAG,CAACY,KAAK,CAACvF,CAAC,EAAEa,CAAC,EAAEU,CAAC,CAAC,EAAEuE,OAAO,IAAI,CAAC,CAAC,CAAC;MAC1E;MACAvE,CAAC,GAAGV,CAAC;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOuE,UAAU;AACnB;;AAEA;AACA;AACA;AACA,SAAStF,0BAA0BA,CAACE,CAAC,EAAEgG,GAAG,EAAE;EAC1C,OAAOhH,CAAC,CAACiH,KAAK,CAACjH,CAAC,CAACkH,MAAM,CAACF,GAAG,CAAC,EAAE,UAAUvC,EAAE,EAAE;IAC1C,IAAIkB,GAAG,GAAGK,MAAM,CAACmB,iBAAiB;IAClC,IAAIrB,GAAG,GAAGE,MAAM,CAACC,iBAAiB;IAElCjG,CAAC,CAACoH,KAAK,CAAC3C,EAAE,EAAE,UAAU4C,CAAC,EAAExF,CAAC,EAAE;MAC1B,IAAIyF,SAAS,GAAGC,KAAK,CAACvG,CAAC,EAAEa,CAAC,CAAC,GAAG,CAAC;MAE/B8D,GAAG,GAAGvB,IAAI,CAACuB,GAAG,CAAC0B,CAAC,GAAGC,SAAS,EAAE3B,GAAG,CAAC;MAClCG,GAAG,GAAG1B,IAAI,CAAC0B,GAAG,CAACuB,CAAC,GAAGC,SAAS,EAAExB,GAAG,CAAC;IACpC,CAAC,CAAC;IAEF,OAAOH,GAAG,GAAGG,GAAG;EAClB,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASjF,gBAAgBA,CAACmG,GAAG,EAAEQ,OAAO,EAAE;EACtC,IAAIC,WAAW,GAAGzH,CAAC,CAACkH,MAAM,CAACM,OAAO,CAAC;IACjCE,UAAU,GAAG1H,CAAC,CAAC8F,GAAG,CAAC2B,WAAW,CAAC;IAC/BE,UAAU,GAAG3H,CAAC,CAAC2F,GAAG,CAAC8B,WAAW,CAAC;EAEjCzH,CAAC,CAAC4B,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAASgG,IAAI,EAAE;IACnC5H,CAAC,CAAC4B,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAASiG,KAAK,EAAE;MACpC,IAAIC,SAAS,GAAGF,IAAI,GAAGC,KAAK;QAC1BpD,EAAE,GAAGuC,GAAG,CAACc,SAAS,CAAC;QACnBC,KAAK;MACP,IAAItD,EAAE,KAAK+C,OAAO,EAAE;MAEpB,IAAIQ,MAAM,GAAGhI,CAAC,CAACkH,MAAM,CAACzC,EAAE,CAAC;MACzBsD,KAAK,GAAGF,KAAK,KAAK,GAAG,GAAGH,UAAU,GAAG1H,CAAC,CAAC8F,GAAG,CAACkC,MAAM,CAAC,GAAGL,UAAU,GAAG3H,CAAC,CAAC2F,GAAG,CAACqC,MAAM,CAAC;MAE/E,IAAID,KAAK,EAAE;QACTf,GAAG,CAACc,SAAS,CAAC,GAAG9H,CAAC,CAACiI,SAAS,CAACxD,EAAE,EAAE,UAAS4C,CAAC,EAAE;UAAE,OAAOA,CAAC,GAAGU,KAAK;QAAE,CAAC,CAAC;MACrE;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAAShH,OAAOA,CAACiG,GAAG,EAAElD,KAAK,EAAE;EAC3B,OAAO9D,CAAC,CAACiI,SAAS,CAACjB,GAAG,CAACkB,EAAE,EAAE,UAASC,MAAM,EAAEtG,CAAC,EAAE;IAC7C,IAAIiC,KAAK,EAAE;MACT,OAAOkD,GAAG,CAAClD,KAAK,CAACsE,WAAW,CAAC,CAAC,CAAC,CAACvG,CAAC,CAAC;IACpC,CAAC,MAAM;MACL,IAAI4C,EAAE,GAAGzE,CAAC,CAACkE,MAAM,CAAClE,CAAC,CAACqI,GAAG,CAACrB,GAAG,EAAEnF,CAAC,CAAC,CAAC;MAChC,OAAO,CAAC4C,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC,CAAC;AACJ;AAEA,SAASnE,SAASA,CAACU,CAAC,EAAE;EACpB,IAAIC,QAAQ,GAAGd,IAAI,CAACmI,gBAAgB,CAACtH,CAAC,CAAC;EACvC,IAAIE,SAAS,GAAGlB,CAAC,CAACuI,KAAK,CACrBhI,kBAAkB,CAACS,CAAC,EAAEC,QAAQ,CAAC,EAC/BT,kBAAkB,CAACQ,CAAC,EAAEC,QAAQ,CAAC,CAAC;EAElC,IAAI+F,GAAG,GAAG,CAAC,CAAC;EACZ,IAAIwB,gBAAgB;EACpBxI,CAAC,CAAC4B,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAASgG,IAAI,EAAE;IACnCY,gBAAgB,GAAGZ,IAAI,KAAK,GAAG,GAAG3G,QAAQ,GAAGjB,CAAC,CAACkH,MAAM,CAACjG,QAAQ,CAAC,CAACwH,OAAO,CAAC,CAAC;IACzEzI,CAAC,CAAC4B,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,UAASiG,KAAK,EAAE;MACpC,IAAIA,KAAK,KAAK,GAAG,EAAE;QACjBW,gBAAgB,GAAGxI,CAAC,CAACqI,GAAG,CAACG,gBAAgB,EAAE,UAASE,KAAK,EAAE;UACzD,OAAO1I,CAAC,CAACkH,MAAM,CAACwB,KAAK,CAAC,CAACD,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC;MACJ;MAEA,IAAI7E,UAAU,GAAG,CAACgE,IAAI,KAAK,GAAG,GAAG5G,CAAC,CAACsB,YAAY,GAAGtB,CAAC,CAACmF,UAAU,EAAED,IAAI,CAAClF,CAAC,CAAC;MACvE,IAAI8C,KAAK,GAAGnD,iBAAiB,CAACK,CAAC,EAAEwH,gBAAgB,EAAEtH,SAAS,EAAE0C,UAAU,CAAC;MACzE,IAAIa,EAAE,GAAG7D,oBAAoB,CAACI,CAAC,EAAEwH,gBAAgB,EAC/C1E,KAAK,CAACD,IAAI,EAAEC,KAAK,CAACA,KAAK,EAAE+D,KAAK,KAAK,GAAG,CAAC;MACzC,IAAIA,KAAK,KAAK,GAAG,EAAE;QACjBpD,EAAE,GAAGzE,CAAC,CAACiI,SAAS,CAACxD,EAAE,EAAE,UAAS4C,CAAC,EAAE;UAAE,OAAO,CAACA,CAAC;QAAE,CAAC,CAAC;MAClD;MACAL,GAAG,CAACY,IAAI,GAAGC,KAAK,CAAC,GAAGpD,EAAE;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,IAAIkE,aAAa,GAAG7H,0BAA0B,CAACE,CAAC,EAAEgG,GAAG,CAAC;EACtDnG,gBAAgB,CAACmG,GAAG,EAAE2B,aAAa,CAAC;EACpC,OAAO5H,OAAO,CAACiG,GAAG,EAAEhG,CAAC,CAACsF,KAAK,CAAC,CAAC,CAACxC,KAAK,CAAC;AACtC;AAEA,SAAS0C,GAAGA,CAACoC,OAAO,EAAEC,OAAO,EAAErE,UAAU,EAAE;EACzC,OAAO,UAASxD,CAAC,EAAEa,CAAC,EAAEE,CAAC,EAAE;IACvB,IAAI+G,MAAM,GAAG9H,CAAC,CAACkB,IAAI,CAACL,CAAC,CAAC;IACtB,IAAIkH,MAAM,GAAG/H,CAAC,CAACkB,IAAI,CAACH,CAAC,CAAC;IACtB,IAAIiH,GAAG,GAAG,CAAC;IACX,IAAIjB,KAAK;IAETiB,GAAG,IAAIF,MAAM,CAACvB,KAAK,GAAG,CAAC;IACvB,IAAIvH,CAAC,CAAC2D,GAAG,CAACmF,MAAM,EAAE,UAAU,CAAC,EAAE;MAC7B,QAAQA,MAAM,CAACG,QAAQ,CAACb,WAAW,CAAC,CAAC;QACrC,KAAK,GAAG;UAAEL,KAAK,GAAG,CAACe,MAAM,CAACvB,KAAK,GAAG,CAAC;UAAE;QACrC,KAAK,GAAG;UAAEQ,KAAK,GAAGe,MAAM,CAACvB,KAAK,GAAG,CAAC;UAAE;MACpC;IACF;IACA,IAAIQ,KAAK,EAAE;MACTiB,GAAG,IAAIxE,UAAU,GAAGuD,KAAK,GAAG,CAACA,KAAK;IACpC;IACAA,KAAK,GAAG,CAAC;IAETiB,GAAG,IAAI,CAACF,MAAM,CAACpG,KAAK,GAAGmG,OAAO,GAAGD,OAAO,IAAI,CAAC;IAC7CI,GAAG,IAAI,CAACD,MAAM,CAACrG,KAAK,GAAGmG,OAAO,GAAGD,OAAO,IAAI,CAAC;IAE7CI,GAAG,IAAID,MAAM,CAACxB,KAAK,GAAG,CAAC;IACvB,IAAIvH,CAAC,CAAC2D,GAAG,CAACoF,MAAM,EAAE,UAAU,CAAC,EAAE;MAC7B,QAAQA,MAAM,CAACE,QAAQ,CAACb,WAAW,CAAC,CAAC;QACrC,KAAK,GAAG;UAAEL,KAAK,GAAGgB,MAAM,CAACxB,KAAK,GAAG,CAAC;UAAE;QACpC,KAAK,GAAG;UAAEQ,KAAK,GAAG,CAACgB,MAAM,CAACxB,KAAK,GAAG,CAAC;UAAE;MACrC;IACF;IACA,IAAIQ,KAAK,EAAE;MACTiB,GAAG,IAAIxE,UAAU,GAAGuD,KAAK,GAAG,CAACA,KAAK;IACpC;IACAA,KAAK,GAAG,CAAC;IAET,OAAOiB,GAAG;EACZ,CAAC;AACH;AAEA,SAASzB,KAAKA,CAACvG,CAAC,EAAEa,CAAC,EAAE;EACnB,OAAOb,CAAC,CAACkB,IAAI,CAACL,CAAC,CAAC,CAAC0F,KAAK;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}