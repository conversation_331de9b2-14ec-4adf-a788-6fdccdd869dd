{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = addSubgraphConstraints;\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n  _.forEach(vs, function (v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "addSubgraphConstraints", "g", "cg", "vs", "prev", "rootPrev", "for<PERSON>ach", "v", "child", "parent", "prev<PERSON><PERSON><PERSON>", "setEdge"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/order/add-subgraph-constraints.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = addSubgraphConstraints;\n\nfunction addSubgraphConstraints(g, cg, vs) {\n  var prev = {},\n    rootPrev;\n\n  _.forEach(vs, function(v) {\n    var child = g.parent(v),\n      parent,\n      prevChild;\n    while (child) {\n      parent = g.parent(child);\n      if (parent) {\n        prevChild = prev[parent];\n        prev[parent] = child;\n      } else {\n        prevChild = rootPrev;\n        rootPrev = child;\n      }\n      if (prevChild && prevChild !== child) {\n        cg.setEdge(prevChild, child);\n        return;\n      }\n      child = parent;\n    }\n  });\n\n  /*\n  function dfs(v) {\n    var children = v ? g.children(v) : g.children();\n    if (children.length) {\n      var min = Number.POSITIVE_INFINITY,\n          subgraphs = [];\n      _.each(children, function(child) {\n        var childMin = dfs(child);\n        if (g.children(child).length) {\n          subgraphs.push({ v: child, order: childMin });\n        }\n        min = Math.min(min, childMin);\n      });\n      _.reduce(_.sortBy(subgraphs, \"order\"), function(prev, curr) {\n        cg.setEdge(prev.v, curr.v);\n        return curr;\n      });\n      return min;\n    }\n    return g.node(v).order;\n  }\n  dfs(undefined);\n  */\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,sBAAsB;AAEvC,SAASA,sBAAsBA,CAACC,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACzC,IAAIC,IAAI,GAAG,CAAC,CAAC;IACXC,QAAQ;EAEVT,CAAC,CAACU,OAAO,CAACH,EAAE,EAAE,UAASI,CAAC,EAAE;IACxB,IAAIC,KAAK,GAAGP,CAAC,CAACQ,MAAM,CAACF,CAAC,CAAC;MACrBE,MAAM;MACNC,SAAS;IACX,OAAOF,KAAK,EAAE;MACZC,MAAM,GAAGR,CAAC,CAACQ,MAAM,CAACD,KAAK,CAAC;MACxB,IAAIC,MAAM,EAAE;QACVC,SAAS,GAAGN,IAAI,CAACK,MAAM,CAAC;QACxBL,IAAI,CAACK,MAAM,CAAC,GAAGD,KAAK;MACtB,CAAC,MAAM;QACLE,SAAS,GAAGL,QAAQ;QACpBA,QAAQ,GAAGG,KAAK;MAClB;MACA,IAAIE,SAAS,IAAIA,SAAS,KAAKF,KAAK,EAAE;QACpCN,EAAE,CAACS,OAAO,CAACD,SAAS,EAAEF,KAAK,CAAC;QAC5B;MACF;MACAA,KAAK,GAAGC,MAAM;IAChB;EACF,CAAC,CAAC;;EAEF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}