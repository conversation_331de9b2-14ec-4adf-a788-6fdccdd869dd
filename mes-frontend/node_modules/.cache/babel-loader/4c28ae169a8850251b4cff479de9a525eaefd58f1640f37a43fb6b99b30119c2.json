{"ast": null, "code": "/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nmodule.exports = List;\nfunction List() {\n  var sentinel = {};\n  sentinel._next = sentinel._prev = sentinel;\n  this._sentinel = sentinel;\n}\nList.prototype.dequeue = function () {\n  var sentinel = this._sentinel;\n  var entry = sentinel._prev;\n  if (entry !== sentinel) {\n    unlink(entry);\n    return entry;\n  }\n};\nList.prototype.enqueue = function (entry) {\n  var sentinel = this._sentinel;\n  if (entry._prev && entry._next) {\n    unlink(entry);\n  }\n  entry._next = sentinel._next;\n  sentinel._next._prev = entry;\n  sentinel._next = entry;\n  entry._prev = sentinel;\n};\nList.prototype.toString = function () {\n  var strs = [];\n  var sentinel = this._sentinel;\n  var curr = sentinel._prev;\n  while (curr !== sentinel) {\n    strs.push(JSON.stringify(curr, filterOutLinks));\n    curr = curr._prev;\n  }\n  return \"[\" + strs.join(\", \") + \"]\";\n};\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\nfunction filterOutLinks(k, v) {\n  if (k !== \"_next\" && k !== \"_prev\") {\n    return v;\n  }\n}", "map": {"version": 3, "names": ["module", "exports", "List", "sentinel", "_next", "_prev", "_sentinel", "prototype", "dequeue", "entry", "unlink", "enqueue", "toString", "strs", "curr", "push", "JSON", "stringify", "filterOutLinks", "join", "k", "v"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/data/list.js"], "sourcesContent": ["/*\n * Simple doubly linked list implementation derived from <PERSON><PERSON><PERSON>, et al.,\n * \"Introduction to Algorithms\".\n */\n\nmodule.exports = List;\n\nfunction List() {\n  var sentinel = {};\n  sentinel._next = sentinel._prev = sentinel;\n  this._sentinel = sentinel;\n}\n\nList.prototype.dequeue = function() {\n  var sentinel = this._sentinel;\n  var entry = sentinel._prev;\n  if (entry !== sentinel) {\n    unlink(entry);\n    return entry;\n  }\n};\n\nList.prototype.enqueue = function(entry) {\n  var sentinel = this._sentinel;\n  if (entry._prev && entry._next) {\n    unlink(entry);\n  }\n  entry._next = sentinel._next;\n  sentinel._next._prev = entry;\n  sentinel._next = entry;\n  entry._prev = sentinel;\n};\n\nList.prototype.toString = function() {\n  var strs = [];\n  var sentinel = this._sentinel;\n  var curr = sentinel._prev;\n  while (curr !== sentinel) {\n    strs.push(JSON.stringify(curr, filterOutLinks));\n    curr = curr._prev;\n  }\n  return \"[\" + strs.join(\", \") + \"]\";\n};\n\nfunction unlink(entry) {\n  entry._prev._next = entry._next;\n  entry._next._prev = entry._prev;\n  delete entry._next;\n  delete entry._prev;\n}\n\nfunction filterOutLinks(k, v) {\n  if (k !== \"_next\" && k !== \"_prev\") {\n    return v;\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEAA,MAAM,CAACC,OAAO,GAAGC,IAAI;AAErB,SAASA,IAAIA,CAAA,EAAG;EACd,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjBA,QAAQ,CAACC,KAAK,GAAGD,QAAQ,CAACE,KAAK,GAAGF,QAAQ;EAC1C,IAAI,CAACG,SAAS,GAAGH,QAAQ;AAC3B;AAEAD,IAAI,CAACK,SAAS,CAACC,OAAO,GAAG,YAAW;EAClC,IAAIL,QAAQ,GAAG,IAAI,CAACG,SAAS;EAC7B,IAAIG,KAAK,GAAGN,QAAQ,CAACE,KAAK;EAC1B,IAAII,KAAK,KAAKN,QAAQ,EAAE;IACtBO,MAAM,CAACD,KAAK,CAAC;IACb,OAAOA,KAAK;EACd;AACF,CAAC;AAEDP,IAAI,CAACK,SAAS,CAACI,OAAO,GAAG,UAASF,KAAK,EAAE;EACvC,IAAIN,QAAQ,GAAG,IAAI,CAACG,SAAS;EAC7B,IAAIG,KAAK,CAACJ,KAAK,IAAII,KAAK,CAACL,KAAK,EAAE;IAC9BM,MAAM,CAACD,KAAK,CAAC;EACf;EACAA,KAAK,CAACL,KAAK,GAAGD,QAAQ,CAACC,KAAK;EAC5BD,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAGI,KAAK;EAC5BN,QAAQ,CAACC,KAAK,GAAGK,KAAK;EACtBA,KAAK,CAACJ,KAAK,GAAGF,QAAQ;AACxB,CAAC;AAEDD,IAAI,CAACK,SAAS,CAACK,QAAQ,GAAG,YAAW;EACnC,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIV,QAAQ,GAAG,IAAI,CAACG,SAAS;EAC7B,IAAIQ,IAAI,GAAGX,QAAQ,CAACE,KAAK;EACzB,OAAOS,IAAI,KAAKX,QAAQ,EAAE;IACxBU,IAAI,CAACE,IAAI,CAACC,IAAI,CAACC,SAAS,CAACH,IAAI,EAAEI,cAAc,CAAC,CAAC;IAC/CJ,IAAI,GAAGA,IAAI,CAACT,KAAK;EACnB;EACA,OAAO,GAAG,GAAGQ,IAAI,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG;AACpC,CAAC;AAED,SAAST,MAAMA,CAACD,KAAK,EAAE;EACrBA,KAAK,CAACJ,KAAK,CAACD,KAAK,GAAGK,KAAK,CAACL,KAAK;EAC/BK,KAAK,CAACL,KAAK,CAACC,KAAK,GAAGI,KAAK,CAACJ,KAAK;EAC/B,OAAOI,KAAK,CAACL,KAAK;EAClB,OAAOK,KAAK,CAACJ,KAAK;AACpB;AAEA,SAASa,cAAcA,CAACE,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAID,CAAC,KAAK,OAAO,IAAIA,CAAC,KAAK,OAAO,EAAE;IAClC,OAAOC,CAAC;EACV;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}