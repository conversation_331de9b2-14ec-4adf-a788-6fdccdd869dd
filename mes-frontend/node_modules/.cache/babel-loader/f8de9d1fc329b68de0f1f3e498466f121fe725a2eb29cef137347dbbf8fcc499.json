{"ast": null, "code": "// Simple caching for constant-radius points.\nlet cacheDigits, cacheAppend, cacheRadius, cacheCircle;\nexport default class PathString {\n  constructor(digits) {\n    this._append = digits == null ? append : appendRound(digits);\n    this._radius = 4.5;\n    this._ = \"\";\n  }\n  pointRadius(_) {\n    this._radius = +_;\n    return this;\n  }\n  polygonStart() {\n    this._line = 0;\n  }\n  polygonEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line === 0) this._ += \"Z\";\n    this._point = NaN;\n  }\n  point(x, y) {\n    switch (this._point) {\n      case 0:\n        {\n          this._append`M${x},${y}`;\n          this._point = 1;\n          break;\n        }\n      case 1:\n        {\n          this._append`L${x},${y}`;\n          break;\n        }\n      default:\n        {\n          this._append`M${x},${y}`;\n          if (this._radius !== cacheRadius || this._append !== cacheAppend) {\n            const r = this._radius;\n            const s = this._;\n            this._ = \"\"; // stash the old string so we can cache the circle path fragment\n            this._append`m0,${r}a${r},${r} 0 1,1 0,${-2 * r}a${r},${r} 0 1,1 0,${2 * r}z`;\n            cacheRadius = r;\n            cacheAppend = this._append;\n            cacheCircle = this._;\n            this._ = s;\n          }\n          this._ += cacheCircle;\n          break;\n        }\n    }\n  }\n  result() {\n    const result = this._;\n    this._ = \"\";\n    return result.length ? result : null;\n  }\n}\nfunction append(strings) {\n  let i = 1;\n  this._ += strings[0];\n  for (const j = strings.length; i < j; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\nfunction appendRound(digits) {\n  const d = Math.floor(digits);\n  if (!(d >= 0)) throw new RangeError(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  if (d !== cacheDigits) {\n    const k = 10 ** d;\n    cacheDigits = d;\n    cacheAppend = function append(strings) {\n      let i = 1;\n      this._ += strings[0];\n      for (const j = strings.length; i < j; ++i) {\n        this._ += Math.round(arguments[i] * k) / k + strings[i];\n      }\n    };\n  }\n  return cacheAppend;\n}", "map": {"version": 3, "names": ["cacheDigits", "cacheAppend", "cacheRadius", "cacheCircle", "PathString", "constructor", "digits", "_append", "append", "appendRound", "_radius", "_", "pointRadius", "polygonStart", "_line", "polygonEnd", "NaN", "lineStart", "_point", "lineEnd", "point", "x", "y", "r", "s", "result", "length", "strings", "i", "j", "arguments", "d", "Math", "floor", "RangeError", "k", "round"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-geo/src/path/string.js"], "sourcesContent": ["// Simple caching for constant-radius points.\nlet cacheDigits, cacheAppend, cacheRadius, cacheCircle;\n\nexport default class PathString {\n  constructor(digits) {\n    this._append = digits == null ? append : appendRound(digits);\n    this._radius = 4.5;\n    this._ = \"\";\n  }\n  pointRadius(_) {\n    this._radius = +_;\n    return this;\n  }\n  polygonStart() {\n    this._line = 0;\n  }\n  polygonEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line === 0) this._ += \"Z\";\n    this._point = NaN;\n  }\n  point(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._append`M${x},${y}`;\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._append`L${x},${y}`;\n        break;\n      }\n      default: {\n        this._append`M${x},${y}`;\n        if (this._radius !== cacheRadius || this._append !== cacheAppend) {\n          const r = this._radius;\n          const s = this._;\n          this._ = \"\"; // stash the old string so we can cache the circle path fragment\n          this._append`m0,${r}a${r},${r} 0 1,1 0,${-2 * r}a${r},${r} 0 1,1 0,${2 * r}z`;\n          cacheRadius = r;\n          cacheAppend = this._append;\n          cacheCircle = this._;\n          this._ = s;\n        }\n        this._ += cacheCircle;\n        break;\n      }\n    }\n  }\n  result() {\n    const result = this._;\n    this._ = \"\";\n    return result.length ? result : null;\n  }\n}\n\nfunction append(strings) {\n  let i = 1;\n  this._ += strings[0];\n  for (const j = strings.length; i < j; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  const d = Math.floor(digits);\n  if (!(d >= 0)) throw new RangeError(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  if (d !== cacheDigits) {\n    const k = 10 ** d;\n    cacheDigits = d;\n    cacheAppend = function append(strings) {\n      let i = 1;\n      this._ += strings[0];\n      for (const j = strings.length; i < j; ++i) {\n        this._ += Math.round(arguments[i] * k) / k + strings[i];\n      }\n    };\n  }\n  return cacheAppend;\n}\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,EAAEC,WAAW,EAAEC,WAAW,EAAEC,WAAW;AAEtD,eAAe,MAAMC,UAAU,CAAC;EAC9BC,WAAWA,CAACC,MAAM,EAAE;IAClB,IAAI,CAACC,OAAO,GAAGD,MAAM,IAAI,IAAI,GAAGE,MAAM,GAAGC,WAAW,CAACH,MAAM,CAAC;IAC5D,IAAI,CAACI,OAAO,GAAG,GAAG;IAClB,IAAI,CAACC,CAAC,GAAG,EAAE;EACb;EACAC,WAAWA,CAACD,CAAC,EAAE;IACb,IAAI,CAACD,OAAO,GAAG,CAACC,CAAC;IACjB,OAAO,IAAI;EACb;EACAE,YAAYA,CAAA,EAAG;IACb,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB;EACAC,UAAUA,CAAA,EAAG;IACX,IAAI,CAACD,KAAK,GAAGE,GAAG;EAClB;EACAC,SAASA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,CAAC;EACjB;EACAC,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAACL,KAAK,KAAK,CAAC,EAAE,IAAI,CAACH,CAAC,IAAI,GAAG;IACnC,IAAI,CAACO,MAAM,GAAGF,GAAG;EACnB;EACAI,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;IACV,QAAQ,IAAI,CAACJ,MAAM;MACjB,KAAK,CAAC;QAAE;UACN,IAAI,CAACX,OAAO,IAAIc,CAAC,IAAIC,CAAC,EAAE;UACxB,IAAI,CAACJ,MAAM,GAAG,CAAC;UACf;QACF;MACA,KAAK,CAAC;QAAE;UACN,IAAI,CAACX,OAAO,IAAIc,CAAC,IAAIC,CAAC,EAAE;UACxB;QACF;MACA;QAAS;UACP,IAAI,CAACf,OAAO,IAAIc,CAAC,IAAIC,CAAC,EAAE;UACxB,IAAI,IAAI,CAACZ,OAAO,KAAKR,WAAW,IAAI,IAAI,CAACK,OAAO,KAAKN,WAAW,EAAE;YAChE,MAAMsB,CAAC,GAAG,IAAI,CAACb,OAAO;YACtB,MAAMc,CAAC,GAAG,IAAI,CAACb,CAAC;YAChB,IAAI,CAACA,CAAC,GAAG,EAAE,CAAC,CAAC;YACb,IAAI,CAACJ,OAAO,MAAMgB,CAAC,IAAIA,CAAC,IAAIA,CAAC,YAAY,CAAC,CAAC,GAAGA,CAAC,IAAIA,CAAC,IAAIA,CAAC,YAAY,CAAC,GAAGA,CAAC,GAAG;YAC7ErB,WAAW,GAAGqB,CAAC;YACftB,WAAW,GAAG,IAAI,CAACM,OAAO;YAC1BJ,WAAW,GAAG,IAAI,CAACQ,CAAC;YACpB,IAAI,CAACA,CAAC,GAAGa,CAAC;UACZ;UACA,IAAI,CAACb,CAAC,IAAIR,WAAW;UACrB;QACF;IACF;EACF;EACAsB,MAAMA,CAAA,EAAG;IACP,MAAMA,MAAM,GAAG,IAAI,CAACd,CAAC;IACrB,IAAI,CAACA,CAAC,GAAG,EAAE;IACX,OAAOc,MAAM,CAACC,MAAM,GAAGD,MAAM,GAAG,IAAI;EACtC;AACF;AAEA,SAASjB,MAAMA,CAACmB,OAAO,EAAE;EACvB,IAAIC,CAAC,GAAG,CAAC;EACT,IAAI,CAACjB,CAAC,IAAIgB,OAAO,CAAC,CAAC,CAAC;EACpB,KAAK,MAAME,CAAC,GAAGF,OAAO,CAACD,MAAM,EAAEE,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACzC,IAAI,CAACjB,CAAC,IAAImB,SAAS,CAACF,CAAC,CAAC,GAAGD,OAAO,CAACC,CAAC,CAAC;EACrC;AACF;AAEA,SAASnB,WAAWA,CAACH,MAAM,EAAE;EAC3B,MAAMyB,CAAC,GAAGC,IAAI,CAACC,KAAK,CAAC3B,MAAM,CAAC;EAC5B,IAAI,EAAEyB,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,IAAIG,UAAU,CAAC,mBAAmB5B,MAAM,EAAE,CAAC;EAChE,IAAIyB,CAAC,GAAG,EAAE,EAAE,OAAOvB,MAAM;EACzB,IAAIuB,CAAC,KAAK/B,WAAW,EAAE;IACrB,MAAMmC,CAAC,GAAG,EAAE,IAAIJ,CAAC;IACjB/B,WAAW,GAAG+B,CAAC;IACf9B,WAAW,GAAG,SAASO,MAAMA,CAACmB,OAAO,EAAE;MACrC,IAAIC,CAAC,GAAG,CAAC;MACT,IAAI,CAACjB,CAAC,IAAIgB,OAAO,CAAC,CAAC,CAAC;MACpB,KAAK,MAAME,CAAC,GAAGF,OAAO,CAACD,MAAM,EAAEE,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACzC,IAAI,CAACjB,CAAC,IAAIqB,IAAI,CAACI,KAAK,CAACN,SAAS,CAACF,CAAC,CAAC,GAAGO,CAAC,CAAC,GAAGA,CAAC,GAAGR,OAAO,CAACC,CAAC,CAAC;MACzD;IACF,CAAC;EACH;EACA,OAAO3B,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}