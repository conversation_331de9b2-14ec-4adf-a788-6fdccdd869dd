{"ast": null, "code": "import { isSpace } from './is-space';\n/**\n * Points the parser to the next character in the\n * path string every time it encounters any kind of\n * space character.\n */\nexport function skipSpaces(path) {\n  var pathValue = path.pathValue,\n    max = path.max;\n  while (path.index < max && isSpace(pathValue.charCodeAt(path.index))) {\n    path.index += 1;\n  }\n}", "map": {"version": 3, "names": ["isSpace", "skipSpaces", "path", "pathValue", "max", "index", "charCodeAt"], "sources": ["path/parser/skip-spaces.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AAGpC;;;;;AAKA,OAAM,SAAUC,UAAUA,CAACC,IAAgB;EACjC,IAAAC,SAAS,GAAUD,IAAI,CAAAC,SAAd;IAAEC,GAAG,GAAKF,IAAI,CAAAE,GAAT;EACtB,OAAOF,IAAI,CAACG,KAAK,GAAGD,GAAG,IAAIJ,OAAO,CAACG,SAAS,CAACG,UAAU,CAACJ,IAAI,CAACG,KAAK,CAAC,CAAC,EAAE;IACpEH,IAAI,CAACG,KAAK,IAAI,CAAC;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}