{"ast": null, "code": "var dfs = require(\"./dfs\");\nmodule.exports = preorder;\nfunction preorder(g, vs) {\n  return dfs(g, vs, \"pre\");\n}", "map": {"version": 3, "names": ["dfs", "require", "module", "exports", "preorder", "g", "vs"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/preorder.js"], "sourcesContent": ["var dfs = require(\"./dfs\");\n\nmodule.exports = preorder;\n\nfunction preorder(g, vs) {\n  return dfs(g, vs, \"pre\");\n}\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE1BC,MAAM,CAACC,OAAO,GAAGC,QAAQ;AAEzB,SAASA,QAAQA,CAACC,CAAC,EAAEC,EAAE,EAAE;EACvB,OAAON,GAAG,CAACK,CAAC,EAAEC,EAAE,EAAE,KAAK,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}