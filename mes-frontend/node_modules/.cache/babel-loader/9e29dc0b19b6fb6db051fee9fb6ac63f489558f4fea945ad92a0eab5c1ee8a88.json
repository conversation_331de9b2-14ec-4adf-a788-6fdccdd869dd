{"ast": null, "code": "var baseIsMatch = require('./_baseIsMatch'),\n  getMatchData = require('./_getMatchData'),\n  matchesStrictComparable = require('./_matchesStrictComparable');\n\n/**\n * The base implementation of `_.matches` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property values to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseMatches(source) {\n  var matchData = getMatchData(source);\n  if (matchData.length == 1 && matchData[0][2]) {\n    return matchesStrictComparable(matchData[0][0], matchData[0][1]);\n  }\n  return function (object) {\n    return object === source || baseIsMatch(object, source, matchData);\n  };\n}\nmodule.exports = baseMatches;", "map": {"version": 3, "names": ["baseIsMatch", "require", "getMatchData", "matchesStrictComparable", "baseMatches", "source", "matchData", "length", "object", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_baseMatches.js"], "sourcesContent": ["var baseIsMatch = require('./_baseIsMatch'),\n    getMatchData = require('./_getMatchData'),\n    matchesStrictComparable = require('./_matchesStrictComparable');\n\n/**\n * The base implementation of `_.matches` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property values to match.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseMatches(source) {\n  var matchData = getMatchData(source);\n  if (matchData.length == 1 && matchData[0][2]) {\n    return matchesStrictComparable(matchData[0][0], matchData[0][1]);\n  }\n  return function(object) {\n    return object === source || baseIsMatch(object, source, matchData);\n  };\n}\n\nmodule.exports = baseMatches;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACvCC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACzCE,uBAAuB,GAAGF,OAAO,CAAC,4BAA4B,CAAC;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,WAAWA,CAACC,MAAM,EAAE;EAC3B,IAAIC,SAAS,GAAGJ,YAAY,CAACG,MAAM,CAAC;EACpC,IAAIC,SAAS,CAACC,MAAM,IAAI,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5C,OAAOH,uBAAuB,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEA,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClE;EACA,OAAO,UAASE,MAAM,EAAE;IACtB,OAAOA,MAAM,KAAKH,MAAM,IAAIL,WAAW,CAACQ,MAAM,EAAEH,MAAM,EAAEC,SAAS,CAAC;EACpE,CAAC;AACH;AAEAG,MAAM,CAACC,OAAO,GAAGN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}