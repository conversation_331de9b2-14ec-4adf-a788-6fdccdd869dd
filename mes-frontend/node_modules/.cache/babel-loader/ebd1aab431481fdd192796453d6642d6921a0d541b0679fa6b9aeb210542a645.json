{"ast": null, "code": "export { getDefaultStyle as topLeft } from './default';", "map": {"version": 3, "names": ["getDefaultStyle", "topLeft"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/topLeft.ts"], "sourcesContent": ["export { getDefaultStyle as topLeft } from './default';\n"], "mappings": "AAAA,SAASA,eAAe,IAAIC,OAAO,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}