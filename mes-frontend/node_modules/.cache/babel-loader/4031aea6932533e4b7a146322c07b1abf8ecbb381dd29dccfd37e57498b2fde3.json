{"ast": null, "code": "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\nimport { phi, squarifyRatio } from \"./squarify.js\";\nexport default (function custom(ratio) {\n  function resquarify(parent, x0, y0, x1, y1) {\n    if ((rows = parent._squarify) && rows.ratio === ratio) {\n      var rows,\n        row,\n        nodes,\n        i,\n        j = -1,\n        n,\n        m = rows.length,\n        value = parent.value;\n      while (++j < m) {\n        row = rows[j], nodes = row.children;\n        for (i = row.value = 0, n = nodes.length; i < n; ++i) row.value += nodes[i].value;\n        if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += (y1 - y0) * row.value / value : y1);else treemapSlice(row, x0, y0, value ? x0 += (x1 - x0) * row.value / value : x1, y1);\n        value -= row.value;\n      }\n    } else {\n      parent._squarify = rows = squarifyRatio(ratio, parent, x0, y0, x1, y1);\n      rows.ratio = ratio;\n    }\n  }\n  resquarify.ratio = function (x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n  return resquarify;\n})(phi);", "map": {"version": 3, "names": ["treemapDice", "treemapSlice", "phi", "squarifyRatio", "custom", "ratio", "resquarify", "parent", "x0", "y0", "x1", "y1", "rows", "_squarify", "row", "nodes", "i", "j", "n", "m", "length", "value", "children", "dice", "x"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/treemap/resquarify.js"], "sourcesContent": ["import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\nimport {phi, squarifyRatio} from \"./squarify.js\";\n\nexport default (function custom(ratio) {\n\n  function resquarify(parent, x0, y0, x1, y1) {\n    if ((rows = parent._squarify) && (rows.ratio === ratio)) {\n      var rows,\n          row,\n          nodes,\n          i,\n          j = -1,\n          n,\n          m = rows.length,\n          value = parent.value;\n\n      while (++j < m) {\n        row = rows[j], nodes = row.children;\n        for (i = row.value = 0, n = nodes.length; i < n; ++i) row.value += nodes[i].value;\n        if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += (y1 - y0) * row.value / value : y1);\n        else treemapSlice(row, x0, y0, value ? x0 += (x1 - x0) * row.value / value : x1, y1);\n        value -= row.value;\n      }\n    } else {\n      parent._squarify = rows = squarifyRatio(ratio, parent, x0, y0, x1, y1);\n      rows.ratio = ratio;\n    }\n  }\n\n  resquarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return resquarify;\n})(phi);\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,WAAW;AACnC,OAAOC,YAAY,MAAM,YAAY;AACrC,SAAQC,GAAG,EAAEC,aAAa,QAAO,eAAe;AAEhD,eAAe,CAAC,SAASC,MAAMA,CAACC,KAAK,EAAE;EAErC,SAASC,UAAUA,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC1C,IAAI,CAACC,IAAI,GAAGL,MAAM,CAACM,SAAS,KAAMD,IAAI,CAACP,KAAK,KAAKA,KAAM,EAAE;MACvD,IAAIO,IAAI;QACJE,GAAG;QACHC,KAAK;QACLC,CAAC;QACDC,CAAC,GAAG,CAAC,CAAC;QACNC,CAAC;QACDC,CAAC,GAAGP,IAAI,CAACQ,MAAM;QACfC,KAAK,GAAGd,MAAM,CAACc,KAAK;MAExB,OAAO,EAAEJ,CAAC,GAAGE,CAAC,EAAE;QACdL,GAAG,GAAGF,IAAI,CAACK,CAAC,CAAC,EAAEF,KAAK,GAAGD,GAAG,CAACQ,QAAQ;QACnC,KAAKN,CAAC,GAAGF,GAAG,CAACO,KAAK,GAAG,CAAC,EAAEH,CAAC,GAAGH,KAAK,CAACK,MAAM,EAAEJ,CAAC,GAAGE,CAAC,EAAE,EAAEF,CAAC,EAAEF,GAAG,CAACO,KAAK,IAAIN,KAAK,CAACC,CAAC,CAAC,CAACK,KAAK;QACjF,IAAIP,GAAG,CAACS,IAAI,EAAEvB,WAAW,CAACc,GAAG,EAAEN,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEW,KAAK,GAAGZ,EAAE,IAAI,CAACE,EAAE,GAAGF,EAAE,IAAIK,GAAG,CAACO,KAAK,GAAGA,KAAK,GAAGV,EAAE,CAAC,CAAC,KACxFV,YAAY,CAACa,GAAG,EAAEN,EAAE,EAAEC,EAAE,EAAEY,KAAK,GAAGb,EAAE,IAAI,CAACE,EAAE,GAAGF,EAAE,IAAIM,GAAG,CAACO,KAAK,GAAGA,KAAK,GAAGX,EAAE,EAAEC,EAAE,CAAC;QACpFU,KAAK,IAAIP,GAAG,CAACO,KAAK;MACpB;IACF,CAAC,MAAM;MACLd,MAAM,CAACM,SAAS,GAAGD,IAAI,GAAGT,aAAa,CAACE,KAAK,EAAEE,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;MACtEC,IAAI,CAACP,KAAK,GAAGA,KAAK;IACpB;EACF;EAEAC,UAAU,CAACD,KAAK,GAAG,UAASmB,CAAC,EAAE;IAC7B,OAAOpB,MAAM,CAAC,CAACoB,CAAC,GAAG,CAACA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACrC,CAAC;EAED,OAAOlB,UAAU;AACnB,CAAC,EAAEJ,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}