{"ast": null, "code": "import { __assign, __read } from \"tslib\";\nimport { isFunction } from '@antv/util';\nimport { fadeOut, onAnimateFinished, transition } from '../../../animation';\nimport { getCallbackValue, select, splitStyle, subStyleProps } from '../../../util';\nimport { CLASS_NAMES } from '../constant';\nimport { getValuePos } from './line';\nimport { filterExec, getCallbackStyle, getDirectionVector } from './utils';\nexport function getTickVector(value, attr) {\n  return getDirectionVector(value, attr.tickDirection, attr);\n}\nexport function getTickPoints(unitVector, tickLength) {\n  var _a = __read(unitVector, 2),\n    dx = _a[0],\n    dy = _a[1];\n  return [[0, 0], [dx * tickLength, dy * tickLength]];\n}\nfunction getTickLineLayout(datum, index, data, tickVector, attr) {\n  var tickLength = attr.tickLength;\n  var _a = __read(getTickPoints(tickVector, getCallbackValue(tickLength, [datum, index, data])), 2),\n    _b = __read(_a[0], 2),\n    x1 = _b[0],\n    y1 = _b[1],\n    _c = __read(_a[1], 2),\n    x2 = _c[0],\n    y2 = _c[1];\n  return {\n    x1: x1,\n    x2: x2,\n    y1: y1,\n    y2: y2\n  };\n}\nfunction createTickEl(container, datum, index, data, attr) {\n  var formatter = attr.tickFormatter;\n  var tickVector = getTickVector(datum.value, attr);\n  var el = 'line';\n  if (isFunction(formatter)) el = function () {\n    return getCallbackValue(formatter, [datum, index, data, tickVector]);\n  };\n  return container.append(el).attr('className', CLASS_NAMES.tickItem.name);\n}\nfunction applyTickStyle(datum, index, data, tick, group, attr, style) {\n  var tickVector = getTickVector(datum.value, attr);\n  var _a = getTickLineLayout(datum, index, data, tickVector, attr),\n    x1 = _a.x1,\n    x2 = _a.x2,\n    y1 = _a.y1,\n    y2 = _a.y2;\n  var _b = __read(splitStyle(getCallbackStyle(style, [datum, index, data, tickVector])), 2),\n    tickStyle = _b[0],\n    groupStyle = _b[1];\n  tick.node().nodeName === 'line' && tick.styles(__assign({\n    x1: x1,\n    x2: x2,\n    y1: y1,\n    y2: y2\n  }, tickStyle));\n  group.attr(groupStyle);\n  tick.styles(tickStyle);\n}\nfunction createTick(datum, index, data, attr, tickAttr, animate) {\n  var tick = createTickEl(select(this), datum, index, data, attr);\n  applyTickStyle(datum, index, data, tick, this, attr, tickAttr);\n  var _a = __read(getValuePos(datum.value, attr), 2),\n    x = _a[0],\n    y = _a[1];\n  return transition(this, {\n    transform: \"translate(\".concat(x, \", \").concat(y, \")\")\n  }, animate);\n}\nexport function renderTicks(container, axisData, attr, animate) {\n  var finalData = filterExec(axisData, attr.tickFilter);\n  var tickAttr = subStyleProps(attr, 'tick');\n  return container.selectAll(CLASS_NAMES.tick.class).data(finalData, function (d) {\n    return d.id || d.label;\n  }).join(function (enter) {\n    return enter.append('g').attr('className', CLASS_NAMES.tick.name).transition(function (datum, index) {\n      return createTick.call(this, datum, index, finalData, attr, tickAttr, false);\n    });\n  }, function (update) {\n    return update.transition(function (datum, index) {\n      this.removeChildren();\n      return createTick.call(this, datum, index, finalData, attr, tickAttr, animate.update);\n    });\n  }, function (exit) {\n    return exit.transition(function () {\n      var _this = this;\n      var animation = fadeOut(this.childNodes[0], animate.exit);\n      onAnimateFinished(animation, function () {\n        return _this.remove();\n      });\n      return animation;\n    });\n  }).transitions();\n}", "map": {"version": 3, "names": ["isFunction", "fadeOut", "onAnimateFinished", "transition", "getCallbackValue", "select", "splitStyle", "subStyleProps", "CLASS_NAMES", "getValuePos", "filterExec", "getCallbackStyle", "getDirectionVector", "getTickVector", "value", "attr", "tickDirection", "getTickPoints", "unitVector", "tick<PERSON><PERSON>th", "_a", "__read", "dx", "dy", "getTickLineLayout", "datum", "index", "data", "tickVector", "_b", "x1", "y1", "_c", "x2", "y2", "createTickEl", "container", "formatter", "tick<PERSON><PERSON><PERSON><PERSON>", "el", "append", "tickItem", "name", "applyTickStyle", "tick", "group", "style", "tickStyle", "groupStyle", "node", "nodeName", "styles", "__assign", "createTick", "tickAttr", "animate", "x", "y", "transform", "concat", "renderTicks", "axisData", "finalData", "tickFilter", "selectAll", "class", "d", "id", "label", "join", "enter", "call", "update", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exit", "_this", "animation", "childNodes", "remove", "transitions"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/guides/ticks.ts"], "sourcesContent": ["import { isFunction } from '@antv/util';\nimport type { GenericAnimation, StandardAnimationOption } from '../../../animation';\nimport { fadeOut, onAnimateFinished, transition } from '../../../animation';\nimport type { Group } from '../../../shapes';\nimport type { Vector2 } from '../../../types';\nimport { Selection, getCallbackValue, select, splitStyle, subStyleProps } from '../../../util';\nimport { CLASS_NAMES } from '../constant';\nimport type { AxisDatum, AxisTickStyleProps, RequiredAxisStyleProps } from '../types';\nimport { getValuePos } from './line';\nimport { filterExec, getCallbackStyle, getDirectionVector } from './utils';\n\ntype RequiredAxisTickStyleProps = Required<AxisTickStyleProps>;\n\nexport function getTickVector(value: number, attr: RequiredAxisStyleProps): Vector2 {\n  return getDirectionVector(value, attr.tickDirection, attr);\n}\n\nexport function getTickPoints(unitVector: Vector2, tickLength: number) {\n  const [dx, dy] = unitVector;\n  return [\n    [0, 0],\n    [dx * tickLength, dy * tickLength],\n  ];\n}\n\nfunction getTickLineLayout(\n  datum: AxisDatum,\n  index: number,\n  data: AxisDatum[],\n  tickVector: Vector2,\n  attr: RequiredAxisStyleProps\n) {\n  const { tickLength } = attr;\n  const [[x1, y1], [x2, y2]] = getTickPoints(tickVector, getCallbackValue(tickLength, [datum, index, data]));\n  return { x1, x2, y1, y2 };\n}\n\nfunction createTickEl(\n  container: Selection,\n  datum: AxisDatum,\n  index: number,\n  data: AxisDatum[],\n  attr: RequiredAxisStyleProps\n) {\n  const { tickFormatter: formatter } = attr;\n  const tickVector = getTickVector(datum.value, attr);\n  let el: any = 'line';\n  if (isFunction(formatter)) el = () => getCallbackValue(formatter, [datum, index, data, tickVector]);\n  return container.append(el).attr('className', CLASS_NAMES.tickItem.name);\n}\n\nfunction applyTickStyle(\n  datum: AxisDatum,\n  index: number,\n  data: AxisDatum[],\n  tick: Selection,\n  group: Group,\n  attr: RequiredAxisStyleProps,\n  style: AxisTickStyleProps\n) {\n  const tickVector = getTickVector(datum.value, attr);\n  const { x1, x2, y1, y2 } = getTickLineLayout(datum, index, data, tickVector, attr);\n  const [tickStyle, groupStyle] = splitStyle(getCallbackStyle(style, [datum, index, data, tickVector]));\n  tick.node().nodeName === 'line' && tick.styles({ x1, x2, y1, y2, ...tickStyle });\n  group.attr(groupStyle);\n  tick.styles(tickStyle);\n}\n\nfunction createTick(\n  datum: AxisDatum,\n  index: number,\n  data: AxisDatum[],\n  attr: RequiredAxisStyleProps,\n  tickAttr: RequiredAxisTickStyleProps,\n  animate: GenericAnimation\n) {\n  const tick = createTickEl(select(this), datum, index, data, attr);\n  applyTickStyle(datum, index, data, tick, this, attr, tickAttr);\n  const [x, y] = getValuePos(datum.value, attr);\n  return transition(this, { transform: `translate(${x}, ${y})` }, animate);\n}\n\nexport function renderTicks(\n  container: Selection,\n  axisData: AxisDatum[],\n  attr: RequiredAxisStyleProps,\n  animate: StandardAnimationOption\n) {\n  const finalData = filterExec(axisData, attr.tickFilter);\n  const tickAttr = subStyleProps<RequiredAxisTickStyleProps>(attr, 'tick');\n  return container\n    .selectAll(CLASS_NAMES.tick.class)\n    .data(finalData, (d) => d.id || d.label)\n    .join(\n      (enter) =>\n        enter\n          .append('g')\n          .attr('className', CLASS_NAMES.tick.name)\n          .transition(function (datum: AxisDatum, index: number) {\n            return createTick.call(this, datum, index, finalData, attr, tickAttr, false);\n          }),\n      (update) =>\n        update.transition(function (datum: AxisDatum, index: number) {\n          this.removeChildren();\n          return createTick.call(this, datum, index, finalData, attr, tickAttr, animate.update);\n        }),\n      (exit) =>\n        exit.transition(function () {\n          const animation = fadeOut(this.childNodes[0], animate.exit);\n          onAnimateFinished(animation, () => this.remove());\n          return animation;\n        })\n    )\n    .transitions();\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,YAAY;AAEvC,SAASC,OAAO,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,oBAAoB;AAG3E,SAAoBC,gBAAgB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,aAAa,QAAQ,eAAe;AAC9F,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,WAAW,QAAQ,QAAQ;AACpC,SAASC,UAAU,EAAEC,gBAAgB,EAAEC,kBAAkB,QAAQ,SAAS;AAI1E,OAAM,SAAUC,aAAaA,CAACC,KAAa,EAAEC,IAA4B;EACvE,OAAOH,kBAAkB,CAACE,KAAK,EAAEC,IAAI,CAACC,aAAa,EAAED,IAAI,CAAC;AAC5D;AAEA,OAAM,SAAUE,aAAaA,CAACC,UAAmB,EAAEC,UAAkB;EAC7D,IAAAC,EAAA,GAAAC,MAAA,CAAWH,UAAU;IAApBI,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA,GAAc;EAC3B,OAAO,CACL,CAAC,CAAC,EAAE,CAAC,CAAC,EACN,CAACE,EAAE,GAAGH,UAAU,EAAEI,EAAE,GAAGJ,UAAU,CAAC,CACnC;AACH;AAEA,SAASK,iBAAiBA,CACxBC,KAAgB,EAChBC,KAAa,EACbC,IAAiB,EACjBC,UAAmB,EACnBb,IAA4B;EAEpB,IAAAI,UAAU,GAAKJ,IAAI,CAAAI,UAAT;EACZ,IAAAC,EAAA,GAAAC,MAAA,CAAuBJ,aAAa,CAACW,UAAU,EAAExB,gBAAgB,CAACe,UAAU,EAAE,CAACM,KAAK,EAAEC,KAAK,EAAEC,IAAI,CAAC,CAAC,CAAC;IAAnGE,EAAA,GAAAR,MAAA,CAAAD,EAAA,OAAQ;IAAPU,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA;IAAGG,EAAA,GAAAX,MAAA,CAAAD,EAAA,OAAQ;IAAPa,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAkF;EAC1G,OAAO;IAAEF,EAAE,EAAAA,EAAA;IAAEG,EAAE,EAAAA,EAAA;IAAEF,EAAE,EAAAA,EAAA;IAAEG,EAAE,EAAAA;EAAA,CAAE;AAC3B;AAEA,SAASC,YAAYA,CACnBC,SAAoB,EACpBX,KAAgB,EAChBC,KAAa,EACbC,IAAiB,EACjBZ,IAA4B;EAEpB,IAAesB,SAAS,GAAKtB,IAAI,CAAAuB,aAAT;EAChC,IAAMV,UAAU,GAAGf,aAAa,CAACY,KAAK,CAACX,KAAK,EAAEC,IAAI,CAAC;EACnD,IAAIwB,EAAE,GAAQ,MAAM;EACpB,IAAIvC,UAAU,CAACqC,SAAS,CAAC,EAAEE,EAAE,GAAG,SAAAA,CAAA;IAAM,OAAAnC,gBAAgB,CAACiC,SAAS,EAAE,CAACZ,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,UAAU,CAAC,CAAC;EAA7D,CAA6D;EACnG,OAAOQ,SAAS,CAACI,MAAM,CAACD,EAAE,CAAC,CAACxB,IAAI,CAAC,WAAW,EAAEP,WAAW,CAACiC,QAAQ,CAACC,IAAI,CAAC;AAC1E;AAEA,SAASC,cAAcA,CACrBlB,KAAgB,EAChBC,KAAa,EACbC,IAAiB,EACjBiB,IAAe,EACfC,KAAY,EACZ9B,IAA4B,EAC5B+B,KAAyB;EAEzB,IAAMlB,UAAU,GAAGf,aAAa,CAACY,KAAK,CAACX,KAAK,EAAEC,IAAI,CAAC;EAC7C,IAAAK,EAAA,GAAqBI,iBAAiB,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,UAAU,EAAEb,IAAI,CAAC;IAA1Ee,EAAE,GAAAV,EAAA,CAAAU,EAAA;IAAEG,EAAE,GAAAb,EAAA,CAAAa,EAAA;IAAEF,EAAE,GAAAX,EAAA,CAAAW,EAAA;IAAEG,EAAE,GAAAd,EAAA,CAAAc,EAA4D;EAC5E,IAAAL,EAAA,GAAAR,MAAA,CAA0Bf,UAAU,CAACK,gBAAgB,CAACmC,KAAK,EAAE,CAACrB,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,UAAU,CAAC,CAAC,CAAC;IAA9FmB,SAAS,GAAAlB,EAAA;IAAEmB,UAAU,GAAAnB,EAAA,GAAyE;EACrGe,IAAI,CAACK,IAAI,EAAE,CAACC,QAAQ,KAAK,MAAM,IAAIN,IAAI,CAACO,MAAM,CAAAC,QAAA;IAAGtB,EAAE,EAAAA,EAAA;IAAEG,EAAE,EAAAA,EAAA;IAAEF,EAAE,EAAAA,EAAA;IAAEG,EAAE,EAAAA;EAAA,GAAKa,SAAS,EAAG;EAChFF,KAAK,CAAC9B,IAAI,CAACiC,UAAU,CAAC;EACtBJ,IAAI,CAACO,MAAM,CAACJ,SAAS,CAAC;AACxB;AAEA,SAASM,UAAUA,CACjB5B,KAAgB,EAChBC,KAAa,EACbC,IAAiB,EACjBZ,IAA4B,EAC5BuC,QAAoC,EACpCC,OAAyB;EAEzB,IAAMX,IAAI,GAAGT,YAAY,CAAC9B,MAAM,CAAC,IAAI,CAAC,EAAEoB,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEZ,IAAI,CAAC;EACjE4B,cAAc,CAAClB,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEiB,IAAI,EAAE,IAAI,EAAE7B,IAAI,EAAEuC,QAAQ,CAAC;EACxD,IAAAlC,EAAA,GAAAC,MAAA,CAASZ,WAAW,CAACgB,KAAK,CAACX,KAAK,EAAEC,IAAI,CAAC;IAAtCyC,CAAC,GAAApC,EAAA;IAAEqC,CAAC,GAAArC,EAAA,GAAkC;EAC7C,OAAOjB,UAAU,CAAC,IAAI,EAAE;IAAEuD,SAAS,EAAE,aAAAC,MAAA,CAAaH,CAAC,QAAAG,MAAA,CAAKF,CAAC;EAAG,CAAE,EAAEF,OAAO,CAAC;AAC1E;AAEA,OAAM,SAAUK,WAAWA,CACzBxB,SAAoB,EACpByB,QAAqB,EACrB9C,IAA4B,EAC5BwC,OAAgC;EAEhC,IAAMO,SAAS,GAAGpD,UAAU,CAACmD,QAAQ,EAAE9C,IAAI,CAACgD,UAAU,CAAC;EACvD,IAAMT,QAAQ,GAAG/C,aAAa,CAA6BQ,IAAI,EAAE,MAAM,CAAC;EACxE,OAAOqB,SAAS,CACb4B,SAAS,CAACxD,WAAW,CAACoC,IAAI,CAACqB,KAAK,CAAC,CACjCtC,IAAI,CAACmC,SAAS,EAAE,UAACI,CAAC;IAAK,OAAAA,CAAC,CAACC,EAAE,IAAID,CAAC,CAACE,KAAK;EAAf,CAAe,CAAC,CACvCC,IAAI,CACH,UAACC,KAAK;IACJ,OAAAA,KAAK,CACF9B,MAAM,CAAC,GAAG,CAAC,CACXzB,IAAI,CAAC,WAAW,EAAEP,WAAW,CAACoC,IAAI,CAACF,IAAI,CAAC,CACxCvC,UAAU,CAAC,UAAUsB,KAAgB,EAAEC,KAAa;MACnD,OAAO2B,UAAU,CAACkB,IAAI,CAAC,IAAI,EAAE9C,KAAK,EAAEC,KAAK,EAAEoC,SAAS,EAAE/C,IAAI,EAAEuC,QAAQ,EAAE,KAAK,CAAC;IAC9E,CAAC,CAAC;EALJ,CAKI,EACN,UAACkB,MAAM;IACL,OAAAA,MAAM,CAACrE,UAAU,CAAC,UAAUsB,KAAgB,EAAEC,KAAa;MACzD,IAAI,CAAC+C,cAAc,EAAE;MACrB,OAAOpB,UAAU,CAACkB,IAAI,CAAC,IAAI,EAAE9C,KAAK,EAAEC,KAAK,EAAEoC,SAAS,EAAE/C,IAAI,EAAEuC,QAAQ,EAAEC,OAAO,CAACiB,MAAM,CAAC;IACvF,CAAC,CAAC;EAHF,CAGE,EACJ,UAACE,IAAI;IACH,OAAAA,IAAI,CAACvE,UAAU,CAAC;MAAA,IAAAwE,KAAA;MACd,IAAMC,SAAS,GAAG3E,OAAO,CAAC,IAAI,CAAC4E,UAAU,CAAC,CAAC,CAAC,EAAEtB,OAAO,CAACmB,IAAI,CAAC;MAC3DxE,iBAAiB,CAAC0E,SAAS,EAAE;QAAM,OAAAD,KAAI,CAACG,MAAM,EAAE;MAAb,CAAa,CAAC;MACjD,OAAOF,SAAS;IAClB,CAAC,CAAC;EAJF,CAIE,CACL,CACAG,WAAW,EAAE;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}