{"ast": null, "code": "import { __assign, __extends, __rest } from \"tslib\";\nimport { Text as GText } from '@antv/g';\nimport { createOffscreenGroup } from '../util/offscreen';\nvar Text = /** @class */function (_super) {\n  __extends(Text, _super);\n  function Text(_a) {\n    if (_a === void 0) {\n      _a = {};\n    }\n    var style = _a.style,\n      restOptions = __rest(_a, [\"style\"]);\n    return _super.call(this, __assign({\n      style: __assign({\n        text: '',\n        fill: 'black',\n        fontFamily: 'sans-serif',\n        fontSize: 16,\n        fontStyle: 'normal',\n        fontVariant: 'normal',\n        fontWeight: 'normal',\n        lineWidth: 1,\n        textAlign: 'start',\n        textBaseline: 'middle'\n      }, style)\n    }, restOptions)) || this;\n  }\n  Object.defineProperty(Text.prototype, \"offscreenGroup\", {\n    get: function () {\n      if (!this._offscreen) this._offscreen = createOffscreenGroup(this);\n      return this._offscreen;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Text.prototype.disconnectedCallback = function () {\n    var _a;\n    (_a = this._offscreen) === null || _a === void 0 ? void 0 : _a.destroy();\n  };\n  return Text;\n}(GText);\nexport { Text };", "map": {"version": 3, "names": ["Text", "GText", "createOffscreenGroup", "_super", "__extends", "_a", "style", "restOptions", "__rest", "call", "__assign", "text", "fill", "fontFamily", "fontSize", "fontStyle", "fontVariant", "fontWeight", "lineWidth", "textAlign", "textBaseline", "Object", "defineProperty", "prototype", "get", "_offscreen", "disconnectedCallback", "destroy"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Text.ts"], "sourcesContent": ["import type { DisplayObjectConfig, TextStyleProps as GTextStyleProps } from '@antv/g';\nimport { Group, Text as GText } from '@antv/g';\nimport { createOffscreenGroup } from '../util/offscreen';\nimport { OmitConflictStyleProps } from './types';\n\nexport type TextStyleProps = OmitConflictStyleProps<GTextStyleProps>;\n\nexport class Text extends GText {\n  private _offscreen!: Group;\n\n  protected get offscreenGroup() {\n    if (!this._offscreen) this._offscreen = createOffscreenGroup(this);\n    return this._offscreen;\n  }\n\n  disconnectedCallback(): void {\n    this._offscreen?.destroy();\n  }\n\n  constructor({ style, ...restOptions }: DisplayObjectConfig<TextStyleProps> = {}) {\n    super({\n      style: {\n        text: '',\n        fill: 'black',\n        fontFamily: 'sans-serif',\n        fontSize: 16,\n        fontStyle: 'normal',\n        fontVariant: 'normal',\n        fontWeight: 'normal',\n        lineWidth: 1,\n        textAlign: 'start',\n        textBaseline: 'middle',\n        ...style,\n      },\n      ...restOptions,\n    });\n  }\n}\n"], "mappings": ";AACA,SAAgBA,IAAI,IAAIC,KAAK,QAAQ,SAAS;AAC9C,SAASC,oBAAoB,QAAQ,mBAAmB;AAKxD,IAAAF,IAAA,0BAAAG,MAAA;EAA0BC,SAAA,CAAAJ,IAAA,EAAAG,MAAA;EAYxB,SAAAH,KAAYK,EAAmE;IAAnE,IAAAA,EAAA;MAAAA,EAAA,KAAmE;IAAA;IAAjE,IAAAC,KAAK,GAAAD,EAAA,CAAAC,KAAA;MAAKC,WAAW,GAAAC,MAAA,CAAAH,EAAA,EAAvB,SAAyB,CAAF;IACjC,OAAAF,MAAK,CAAAM,IAAA,OAAAC,QAAA;MACHJ,KAAK,EAAAI,QAAA;QACHC,IAAI,EAAE,EAAE;QACRC,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,YAAY;QACxBC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,QAAQ;QACnBC,WAAW,EAAE,QAAQ;QACrBC,UAAU,EAAE,QAAQ;QACpBC,SAAS,EAAE,CAAC;QACZC,SAAS,EAAE,OAAO;QAClBC,YAAY,EAAE;MAAQ,GACnBd,KAAK;IAAA,GAEPC,WAAW,EACd;EACJ;EA1BAc,MAAA,CAAAC,cAAA,CAActB,IAAA,CAAAuB,SAAA,kBAAc;SAA5B,SAAAC,CAAA;MACE,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACA,UAAU,GAAGvB,oBAAoB,CAAC,IAAI,CAAC;MAClE,OAAO,IAAI,CAACuB,UAAU;IACxB,CAAC;;;;EAEDzB,IAAA,CAAAuB,SAAA,CAAAG,oBAAoB,GAApB;;IACE,CAAArB,EAAA,OAAI,CAACoB,UAAU,cAAApB,EAAA,uBAAAA,EAAA,CAAEsB,OAAO,EAAE;EAC5B,CAAC;EAoBH,OAAA3B,IAAC;AAAD,CAAC,CA9ByBC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}