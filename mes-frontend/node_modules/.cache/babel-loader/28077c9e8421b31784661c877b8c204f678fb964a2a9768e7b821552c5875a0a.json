{"ast": null, "code": "var _jsxFileName = \"/root/mes-system/mes-frontend/src/pages/Project/ProjectList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Typography, Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, message, Popconfirm, Row, Col, Statistic } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ProjectOutlined, CalendarOutlined, UserOutlined } from '@ant-design/icons';\nimport { projectService } from '../../services/business';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TextArea\n} = Input;\nconst ProjectList = () => {\n  _s();\n  const navigate = useNavigate();\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟项目数据\n  const mockProjects = [{\n    id: 1,\n    project_name: '汽车零部件生产项目',\n    customer_name: '某汽车制造公司',\n    description: '生产汽车发动机相关零部件',\n    status: 'IN_PROGRESS',\n    start_date: '2024-01-01',\n    end_date: '2024-06-30',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z'\n  }, {\n    id: 2,\n    project_name: '电子产品外壳项目',\n    customer_name: '某电子科技公司',\n    description: '生产手机和平板电脑外壳',\n    status: 'PLANNING',\n    start_date: '2024-02-01',\n    end_date: '2024-08-31',\n    created_at: '2024-01-15T00:00:00Z',\n    updated_at: '2024-01-15T00:00:00Z'\n  }, {\n    id: 3,\n    project_name: '医疗器械组件项目',\n    customer_name: '某医疗设备公司',\n    description: '生产医疗设备精密组件',\n    status: 'COMPLETED',\n    start_date: '2023-09-01',\n    end_date: '2023-12-31',\n    created_at: '2023-08-15T00:00:00Z',\n    updated_at: '2023-12-31T00:00:00Z'\n  }];\n  useEffect(() => {\n    fetchProjects();\n  }, []);\n  const fetchProjects = async () => {\n    setLoading(true);\n    try {\n      // 尝试从API获取数据，失败则使用模拟数据\n      const response = await projectService.getProjects();\n      if (response.success && response.data) {\n        setProjects(response.data.data);\n      } else {\n        throw new Error('API调用失败');\n      }\n    } catch (error) {\n      console.log('使用模拟数据:', error);\n      setProjects(mockProjects);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = project => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      ...project,\n      date_range: project.start_date && project.end_date ? [dayjs(project.start_date), dayjs(project.end_date)] : undefined\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      await projectService.deleteProject(id);\n      message.success('删除成功');\n      fetchProjects();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$date_range, _values$date_range$, _values$date_range2, _values$date_range2$;\n      const projectData = {\n        ...values,\n        start_date: (_values$date_range = values.date_range) === null || _values$date_range === void 0 ? void 0 : (_values$date_range$ = _values$date_range[0]) === null || _values$date_range$ === void 0 ? void 0 : _values$date_range$.format('YYYY-MM-DD'),\n        end_date: (_values$date_range2 = values.date_range) === null || _values$date_range2 === void 0 ? void 0 : (_values$date_range2$ = _values$date_range2[1]) === null || _values$date_range2$ === void 0 ? void 0 : _values$date_range2$.format('YYYY-MM-DD')\n      };\n      delete projectData.date_range;\n      if (editingProject) {\n        await projectService.updateProject(editingProject.id, projectData);\n        message.success('更新成功');\n      } else {\n        await projectService.createProject(projectData);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchProjects();\n    } catch (error) {\n      message.error(editingProject ? '更新失败' : '创建失败');\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'PLANNING':\n        return 'blue';\n      case 'IN_PROGRESS':\n        return 'green';\n      case 'COMPLETED':\n        return 'default';\n      case 'CANCELLED':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'PLANNING':\n        return '计划中';\n      case 'IN_PROGRESS':\n        return '进行中';\n      case 'COMPLETED':\n        return '已完成';\n      case 'CANCELLED':\n        return '已取消';\n      default:\n        return '未知';\n    }\n  };\n  const columns = [{\n    title: '项目名称',\n    dataIndex: 'project_name',\n    key: 'project_name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      onClick: () => navigate(`/projects/${record.id}`),\n      style: {\n        padding: 0,\n        height: 'auto'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户名称',\n    dataIndex: 'customer_name',\n    key: 'customer_name'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '开始日期',\n    dataIndex: 'start_date',\n    key: 'start_date',\n    render: date => date ? dayjs(date).format('YYYY-MM-DD') : '-'\n  }, {\n    title: '结束日期',\n    dataIndex: 'end_date',\n    key: 'end_date',\n    render: date => date ? dayjs(date).format('YYYY-MM-DD') : '-'\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: date => dayjs(date).format('YYYY-MM-DD HH:mm')\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 19\n        }, this),\n        onClick: () => navigate(`/projects/${record.id}`),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u9879\\u76EE\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 统计数据\n  const stats = {\n    total: projects.length,\n    planning: projects.filter(p => p.status === 'PLANNING').length,\n    inProgress: projects.filter(p => p.status === 'IN_PROGRESS').length,\n    completed: projects.filter(p => p.status === 'COMPLETED').length\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"\\u9879\\u76EE\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 17\n        }, this),\n        onClick: handleCreate,\n        children: \"\\u65B0\\u5EFA\\u9879\\u76EE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u9879\\u76EE\\u6570\",\n            value: stats.total,\n            prefix: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BA1\\u5212\\u4E2D\",\n            value: stats.planning,\n            prefix: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FDB\\u884C\\u4E2D\",\n            value: stats.inProgress,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: stats.completed,\n            prefix: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 334,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: projects,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          total: projects.length,\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingProject ? '编辑项目' : '新建项目',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"project_name\",\n          label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入项目名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 381,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customer_name\",\n          label: \"\\u5BA2\\u6237\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入客户名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u9879\\u76EE\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u9879\\u76EE\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择项目状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u9879\\u76EE\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"PLANNING\",\n              children: \"\\u8BA1\\u5212\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"IN_PROGRESS\",\n              children: \"\\u8FDB\\u884C\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"COMPLETED\",\n              children: \"\\u5DF2\\u5B8C\\u6210\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"CANCELLED\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"date_range\",\n          label: \"\\u9879\\u76EE\\u5468\\u671F\",\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            style: {\n              width: '100%'\n            },\n            placeholder: ['开始日期', '结束日期']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingProject ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 371,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 282,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectList, \"g2W8o9nUU2hYGxU5aQ47s0oeDt4=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c = ProjectList;\nexport default ProjectList;\nvar _c;\n$RefreshReg$(_c, \"ProjectList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Typography", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "DatePicker", "message", "Popconfirm", "Row", "Col", "Statistic", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ProjectOutlined", "CalendarOutlined", "UserOutlined", "projectService", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Option", "RangePicker", "TextArea", "ProjectList", "_s", "navigate", "projects", "setProjects", "loading", "setLoading", "modalVisible", "setModalVisible", "editingProject", "setEditingProject", "form", "useForm", "mockProjects", "id", "project_name", "customer_name", "description", "status", "start_date", "end_date", "created_at", "updated_at", "fetchProjects", "response", "getProjects", "success", "data", "Error", "error", "console", "log", "handleCreate", "resetFields", "handleEdit", "project", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "date_range", "undefined", "handleDelete", "deleteProject", "handleSubmit", "values", "_values$date_range", "_values$date_range$", "_values$date_range2", "_values$date_range2$", "projectData", "format", "updateProject", "createProject", "getStatusColor", "getStatusText", "columns", "title", "dataIndex", "key", "render", "text", "record", "type", "onClick", "style", "padding", "height", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "date", "_", "size", "icon", "onConfirm", "okText", "cancelText", "danger", "stats", "total", "length", "planning", "filter", "p", "inProgress", "completed", "display", "justifyContent", "alignItems", "marginBottom", "level", "margin", "gutter", "span", "value", "prefix", "valueStyle", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "width", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "rows", "htmlType", "_c", "$RefreshReg$"], "sources": ["/root/mes-system/mes-frontend/src/pages/Project/ProjectList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Typography,\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  message,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ProjectOutlined,\n  CalendarOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { projectService } from '../../services/business';\nimport { Project } from '../../types';\nimport dayjs from 'dayjs';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { TextArea } = Input;\n\nconst ProjectList: React.FC = () => {\n  const navigate = useNavigate();\n  const [projects, setProjects] = useState<Project[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState<Project | null>(null);\n  const [form] = Form.useForm();\n\n  // 模拟项目数据\n  const mockProjects: Project[] = [\n    {\n      id: 1,\n      project_name: '汽车零部件生产项目',\n      customer_name: '某汽车制造公司',\n      description: '生产汽车发动机相关零部件',\n      status: 'IN_PROGRESS',\n      start_date: '2024-01-01',\n      end_date: '2024-06-30',\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-01T00:00:00Z',\n    },\n    {\n      id: 2,\n      project_name: '电子产品外壳项目',\n      customer_name: '某电子科技公司',\n      description: '生产手机和平板电脑外壳',\n      status: 'PLANNING',\n      start_date: '2024-02-01',\n      end_date: '2024-08-31',\n      created_at: '2024-01-15T00:00:00Z',\n      updated_at: '2024-01-15T00:00:00Z',\n    },\n    {\n      id: 3,\n      project_name: '医疗器械组件项目',\n      customer_name: '某医疗设备公司',\n      description: '生产医疗设备精密组件',\n      status: 'COMPLETED',\n      start_date: '2023-09-01',\n      end_date: '2023-12-31',\n      created_at: '2023-08-15T00:00:00Z',\n      updated_at: '2023-12-31T00:00:00Z',\n    },\n  ];\n\n  useEffect(() => {\n    fetchProjects();\n  }, []);\n\n  const fetchProjects = async () => {\n    setLoading(true);\n    try {\n      // 尝试从API获取数据，失败则使用模拟数据\n      const response = await projectService.getProjects();\n      if (response.success && response.data) {\n        setProjects(response.data.data);\n      } else {\n        throw new Error('API调用失败');\n      }\n    } catch (error) {\n      console.log('使用模拟数据:', error);\n      setProjects(mockProjects);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (project: Project) => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      ...project,\n      date_range: project.start_date && project.end_date\n        ? [dayjs(project.start_date), dayjs(project.end_date)]\n        : undefined,\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await projectService.deleteProject(id);\n      message.success('删除成功');\n      fetchProjects();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      const projectData = {\n        ...values,\n        start_date: values.date_range?.[0]?.format('YYYY-MM-DD'),\n        end_date: values.date_range?.[1]?.format('YYYY-MM-DD'),\n      };\n      delete projectData.date_range;\n\n      if (editingProject) {\n        await projectService.updateProject(editingProject.id, projectData);\n        message.success('更新成功');\n      } else {\n        await projectService.createProject(projectData);\n        message.success('创建成功');\n      }\n\n      setModalVisible(false);\n      fetchProjects();\n    } catch (error) {\n      message.error(editingProject ? '更新失败' : '创建失败');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PLANNING':\n        return 'blue';\n      case 'IN_PROGRESS':\n        return 'green';\n      case 'COMPLETED':\n        return 'default';\n      case 'CANCELLED':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'PLANNING':\n        return '计划中';\n      case 'IN_PROGRESS':\n        return '进行中';\n      case 'COMPLETED':\n        return '已完成';\n      case 'CANCELLED':\n        return '已取消';\n      default:\n        return '未知';\n    }\n  };\n\n  const columns = [\n    {\n      title: '项目名称',\n      dataIndex: 'project_name',\n      key: 'project_name',\n      render: (text: string, record: Project) => (\n        <Button\n          type=\"link\"\n          onClick={() => navigate(`/projects/${record.id}`)}\n          style={{ padding: 0, height: 'auto' }}\n        >\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '客户名称',\n      dataIndex: 'customer_name',\n      key: 'customer_name',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '开始日期',\n      dataIndex: 'start_date',\n      key: 'start_date',\n      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',\n    },\n    {\n      title: '结束日期',\n      dataIndex: 'end_date',\n      key: 'end_date',\n      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record: Project) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"text\"\n            icon={<EyeOutlined />}\n            onClick={() => navigate(`/projects/${record.id}`)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"text\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个项目吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计数据\n  const stats = {\n    total: projects.length,\n    planning: projects.filter(p => p.status === 'PLANNING').length,\n    inProgress: projects.filter(p => p.status === 'IN_PROGRESS').length,\n    completed: projects.filter(p => p.status === 'COMPLETED').length,\n  };\n\n  return (\n    <div>\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '24px'\n      }}>\n        <Title level={2} style={{ margin: 0 }}>\n          项目管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleCreate}\n        >\n          新建项目\n        </Button>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总项目数\"\n              value={stats.total}\n              prefix={<ProjectOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"计划中\"\n              value={stats.planning}\n              prefix={<CalendarOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"进行中\"\n              value={stats.inProgress}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={stats.completed}\n              prefix={<ProjectOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 项目列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={projects}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            total: projects.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 创建/编辑模态框 */}\n      <Modal\n        title={editingProject ? '编辑项目' : '新建项目'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"project_name\"\n            label=\"项目名称\"\n            rules={[{ required: true, message: '请输入项目名称' }]}\n          >\n            <Input placeholder=\"请输入项目名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"customer_name\"\n            label=\"客户名称\"\n            rules={[{ required: true, message: '请输入客户名称' }]}\n          >\n            <Input placeholder=\"请输入客户名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"项目描述\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入项目描述\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"项目状态\"\n            rules={[{ required: true, message: '请选择项目状态' }]}\n          >\n            <Select placeholder=\"请选择项目状态\">\n              <Option value=\"PLANNING\">计划中</Option>\n              <Option value=\"IN_PROGRESS\">进行中</Option>\n              <Option value=\"COMPLETED\">已完成</Option>\n              <Option value=\"CANCELLED\">已取消</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"date_range\"\n            label=\"项目周期\"\n          >\n            <RangePicker\n              style={{ width: '100%' }}\n              placeholder={['开始日期', '结束日期']}\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingProject ? '更新' : '创建'}\n              </Button>\n              <Button onClick={() => setModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,QACJ,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,cAAc,QAAQ,yBAAyB;AAExD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAM,CAAC,GAAG3B,UAAU;AAC5B,MAAM;EAAE4B;AAAO,CAAC,GAAGnB,MAAM;AACzB,MAAM;EAAEoB;AAAY,CAAC,GAAGnB,UAAU;AAClC,MAAM;EAAEoB;AAAS,CAAC,GAAGtB,KAAK;AAE1B,MAAMuB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGlC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,YAAY,EAAEC,eAAe,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC6C,IAAI,CAAC,GAAGnC,IAAI,CAACoC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,YAAuB,GAAG,CAC9B;IACEC,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,WAAW;IACzBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,cAAc;IAC3BC,MAAM,EAAE,aAAa;IACrBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,UAAU;IACxBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,aAAa;IAC1BC,MAAM,EAAE,UAAU;IAClBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,UAAU;IACxBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,YAAY;IACzBC,MAAM,EAAE,WAAW;IACnBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;EAEDvD,SAAS,CAAC,MAAM;IACdwD,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCjB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMkB,QAAQ,GAAG,MAAMhC,cAAc,CAACiC,WAAW,CAAC,CAAC;MACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCvB,WAAW,CAACoB,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC;MACjC,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,KAAK,CAAC;MAC7BzB,WAAW,CAACS,YAAY,CAAC;IAC3B,CAAC,SAAS;MACRP,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,YAAY,GAAGA,CAAA,KAAM;IACzBtB,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAACsB,WAAW,CAAC,CAAC;IAClBzB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM0B,UAAU,GAAIC,OAAgB,IAAK;IACvCzB,iBAAiB,CAACyB,OAAO,CAAC;IAC1BxB,IAAI,CAACyB,cAAc,CAAC;MAClB,GAAGD,OAAO;MACVE,UAAU,EAAEF,OAAO,CAAChB,UAAU,IAAIgB,OAAO,CAACf,QAAQ,GAC9C,CAAC3B,KAAK,CAAC0C,OAAO,CAAChB,UAAU,CAAC,EAAE1B,KAAK,CAAC0C,OAAO,CAACf,QAAQ,CAAC,CAAC,GACpDkB;IACN,CAAC,CAAC;IACF9B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+B,YAAY,GAAG,MAAOzB,EAAU,IAAK;IACzC,IAAI;MACF,MAAMtB,cAAc,CAACgD,aAAa,CAAC1B,EAAE,CAAC;MACtClC,OAAO,CAAC8C,OAAO,CAAC,MAAM,CAAC;MACvBH,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMY,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,oBAAA;MACF,MAAMC,WAAW,GAAG;QAClB,GAAGL,MAAM;QACTvB,UAAU,GAAAwB,kBAAA,GAAED,MAAM,CAACL,UAAU,cAAAM,kBAAA,wBAAAC,mBAAA,GAAjBD,kBAAA,CAAoB,CAAC,CAAC,cAAAC,mBAAA,uBAAtBA,mBAAA,CAAwBI,MAAM,CAAC,YAAY,CAAC;QACxD5B,QAAQ,GAAAyB,mBAAA,GAAEH,MAAM,CAACL,UAAU,cAAAQ,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,uBAAtBA,oBAAA,CAAwBE,MAAM,CAAC,YAAY;MACvD,CAAC;MACD,OAAOD,WAAW,CAACV,UAAU;MAE7B,IAAI5B,cAAc,EAAE;QAClB,MAAMjB,cAAc,CAACyD,aAAa,CAACxC,cAAc,CAACK,EAAE,EAAEiC,WAAW,CAAC;QAClEnE,OAAO,CAAC8C,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAMlC,cAAc,CAAC0D,aAAa,CAACH,WAAW,CAAC;QAC/CnE,OAAO,CAAC8C,OAAO,CAAC,MAAM,CAAC;MACzB;MAEAlB,eAAe,CAAC,KAAK,CAAC;MACtBe,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOM,KAAK,EAAE;MACdjD,OAAO,CAACiD,KAAK,CAACpB,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC;IACjD;EACF,CAAC;EAED,MAAM0C,cAAc,GAAIjC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,MAAM;MACf,KAAK,aAAa;QAChB,OAAO,OAAO;MAChB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,KAAK;MACd;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMkC,aAAa,GAAIlC,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,KAAK;MACd,KAAK,aAAa;QAChB,OAAO,KAAK;MACd,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,WAAW;QACd,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMmC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAEA,CAACC,IAAY,EAAEC,MAAe,kBACpChE,OAAA,CAACvB,MAAM;MACLwF,IAAI,EAAC,MAAM;MACXC,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,aAAayD,MAAM,CAAC7C,EAAE,EAAE,CAAE;MAClDgD,KAAK,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAC,QAAA,EAErCP;IAAI;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAEZ,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGvC,MAAc,iBACrBvB,OAAA,CAACrB,GAAG;MAACgG,KAAK,EAAEnB,cAAc,CAACjC,MAAM,CAAE;MAAA+C,QAAA,EAChCb,aAAa,CAAClC,MAAM;IAAC;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGc,IAAY,IAAKA,IAAI,GAAG9E,KAAK,CAAC8E,IAAI,CAAC,CAACvB,MAAM,CAAC,YAAY,CAAC,GAAG;EACtE,CAAC,EACD;IACEM,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGc,IAAY,IAAKA,IAAI,GAAG9E,KAAK,CAAC8E,IAAI,CAAC,CAACvB,MAAM,CAAC,YAAY,CAAC,GAAG;EACtE,CAAC,EACD;IACEM,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGc,IAAY,IAAK9E,KAAK,CAAC8E,IAAI,CAAC,CAACvB,MAAM,CAAC,kBAAkB;EACjE,CAAC,EACD;IACEM,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACe,CAAC,EAAEb,MAAe,kBACzBhE,OAAA,CAACtB,KAAK;MAACoG,IAAI,EAAC,QAAQ;MAAAR,QAAA,gBAClBtE,OAAA,CAACvB,MAAM;QACLwF,IAAI,EAAC,MAAM;QACXc,IAAI,eAAE/E,OAAA,CAACP,WAAW;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBR,OAAO,EAAEA,CAAA,KAAM3D,QAAQ,CAAC,aAAayD,MAAM,CAAC7C,EAAE,EAAE,CAAE;QAAAmD,QAAA,EACnD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1E,OAAA,CAACvB,MAAM;QACLwF,IAAI,EAAC,MAAM;QACXc,IAAI,eAAE/E,OAAA,CAACT,YAAY;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBR,OAAO,EAAEA,CAAA,KAAM3B,UAAU,CAACyB,MAAM,CAAE;QAAAM,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1E,OAAA,CAACd,UAAU;QACTyE,KAAK,EAAC,oEAAa;QACnBqB,SAAS,EAAEA,CAAA,KAAMpC,YAAY,CAACoB,MAAM,CAAC7C,EAAE,CAAE;QACzC8D,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAZ,QAAA,eAEftE,OAAA,CAACvB,MAAM;UACLwF,IAAI,EAAC,MAAM;UACXkB,MAAM;UACNJ,IAAI,eAAE/E,OAAA,CAACR,cAAc;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMU,KAAK,GAAG;IACZC,KAAK,EAAE7E,QAAQ,CAAC8E,MAAM;IACtBC,QAAQ,EAAE/E,QAAQ,CAACgF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClE,MAAM,KAAK,UAAU,CAAC,CAAC+D,MAAM;IAC9DI,UAAU,EAAElF,QAAQ,CAACgF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClE,MAAM,KAAK,aAAa,CAAC,CAAC+D,MAAM;IACnEK,SAAS,EAAEnF,QAAQ,CAACgF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClE,MAAM,KAAK,WAAW,CAAC,CAAC+D;EAC5D,CAAC;EAED,oBACEtF,OAAA;IAAAsE,QAAA,gBACEtE,OAAA;MAAKmE,KAAK,EAAE;QACVyB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE;MAChB,CAAE;MAAAzB,QAAA,gBACAtE,OAAA,CAACC,KAAK;QAAC+F,KAAK,EAAE,CAAE;QAAC7B,KAAK,EAAE;UAAE8B,MAAM,EAAE;QAAE,CAAE;QAAA3B,QAAA,EAAC;MAEvC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR1E,OAAA,CAACvB,MAAM;QACLwF,IAAI,EAAC,SAAS;QACdc,IAAI,eAAE/E,OAAA,CAACV,YAAY;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBR,OAAO,EAAE7B,YAAa;QAAAiC,QAAA,EACvB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN1E,OAAA,CAACb,GAAG;MAAC+G,MAAM,EAAE,EAAG;MAAC/B,KAAK,EAAE;QAAE4B,YAAY,EAAE;MAAO,CAAE;MAAAzB,QAAA,gBAC/CtE,OAAA,CAACZ,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACXtE,OAAA,CAACzB,IAAI;UAAA+F,QAAA,eACHtE,OAAA,CAACX,SAAS;YACRsE,KAAK,EAAC,0BAAM;YACZyC,KAAK,EAAEhB,KAAK,CAACC,KAAM;YACnBgB,MAAM,eAAErG,OAAA,CAACN,eAAe;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B4B,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1E,OAAA,CAACZ,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACXtE,OAAA,CAACzB,IAAI;UAAA+F,QAAA,eACHtE,OAAA,CAACX,SAAS;YACRsE,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAEhB,KAAK,CAACG,QAAS;YACtBc,MAAM,eAAErG,OAAA,CAACL,gBAAgB;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7B4B,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1E,OAAA,CAACZ,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACXtE,OAAA,CAACzB,IAAI;UAAA+F,QAAA,eACHtE,OAAA,CAACX,SAAS;YACRsE,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAEhB,KAAK,CAACM,UAAW;YACxBW,MAAM,eAAErG,OAAA,CAACJ,YAAY;cAAA2E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB4B,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN1E,OAAA,CAACZ,GAAG;QAAC+G,IAAI,EAAE,CAAE;QAAA7B,QAAA,eACXtE,OAAA,CAACzB,IAAI;UAAA+F,QAAA,eACHtE,OAAA,CAACX,SAAS;YACRsE,KAAK,EAAC,oBAAK;YACXyC,KAAK,EAAEhB,KAAK,CAACO,SAAU;YACvBU,MAAM,eAAErG,OAAA,CAACN,eAAe;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B4B,UAAU,EAAE;cAAE3B,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1E,OAAA,CAACzB,IAAI;MAAA+F,QAAA,eACHtE,OAAA,CAACxB,KAAK;QACJkF,OAAO,EAAEA,OAAQ;QACjB6C,UAAU,EAAE/F,QAAS;QACrBgG,MAAM,EAAC,IAAI;QACX9F,OAAO,EAAEA,OAAQ;QACjB+F,UAAU,EAAE;UACVpB,KAAK,EAAE7E,QAAQ,CAAC8E,MAAM;UACtBoB,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACxB,KAAK,EAAEyB,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQzB,KAAK;QAC1C;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1E,OAAA,CAACpB,KAAK;MACJ+E,KAAK,EAAE7C,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCiG,IAAI,EAAEnG,YAAa;MACnBoG,QAAQ,EAAEA,CAAA,KAAMnG,eAAe,CAAC,KAAK,CAAE;MACvCoG,MAAM,EAAE,IAAK;MACbC,KAAK,EAAE,GAAI;MAAA5C,QAAA,eAEXtE,OAAA,CAACnB,IAAI;QACHmC,IAAI,EAAEA,IAAK;QACXmG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEtE,YAAa;QAAAwB,QAAA,gBAEvBtE,OAAA,CAACnB,IAAI,CAACwI,IAAI;UACRC,IAAI,EAAC,cAAc;UACnBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqF,QAAA,eAEhDtE,OAAA,CAAClB,KAAK;YAAC4I,WAAW,EAAC;UAAS;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ1E,OAAA,CAACnB,IAAI,CAACwI,IAAI;UACRC,IAAI,EAAC,eAAe;UACpBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqF,QAAA,eAEhDtE,OAAA,CAAClB,KAAK;YAAC4I,WAAW,EAAC;UAAS;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ1E,OAAA,CAACnB,IAAI,CAACwI,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,0BAAM;UAAAjD,QAAA,eAEZtE,OAAA,CAACI,QAAQ;YACPuH,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAS;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1E,OAAA,CAACnB,IAAI,CAACwI,IAAI;UACRC,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAExI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqF,QAAA,eAEhDtE,OAAA,CAACjB,MAAM;YAAC2I,WAAW,EAAC,4CAAS;YAAApD,QAAA,gBAC3BtE,OAAA,CAACE,MAAM;cAACkG,KAAK,EAAC,UAAU;cAAA9B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC1E,OAAA,CAACE,MAAM;cAACkG,KAAK,EAAC,aAAa;cAAA9B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC1E,OAAA,CAACE,MAAM;cAACkG,KAAK,EAAC,WAAW;cAAA9B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC1E,OAAA,CAACE,MAAM;cAACkG,KAAK,EAAC,WAAW;cAAA9B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ1E,OAAA,CAACnB,IAAI,CAACwI,IAAI;UACRC,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAC,0BAAM;UAAAjD,QAAA,eAEZtE,OAAA,CAACG,WAAW;YACVgE,KAAK,EAAE;cAAE+C,KAAK,EAAE;YAAO,CAAE;YACzBQ,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1E,OAAA,CAACnB,IAAI,CAACwI,IAAI;UAAA/C,QAAA,eACRtE,OAAA,CAACtB,KAAK;YAAA4F,QAAA,gBACJtE,OAAA,CAACvB,MAAM;cAACwF,IAAI,EAAC,SAAS;cAAC2D,QAAQ,EAAC,QAAQ;cAAAtD,QAAA,EACrCxD,cAAc,GAAG,IAAI,GAAG;YAAI;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACT1E,OAAA,CAACvB,MAAM;cAACyF,OAAO,EAAEA,CAAA,KAAMrD,eAAe,CAAC,KAAK,CAAE;cAAAyD,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpE,EAAA,CAhZID,WAAqB;EAAA,QACRhC,WAAW,EAKbQ,IAAI,CAACoC,OAAO;AAAA;AAAA4G,EAAA,GANvBxH,WAAqB;AAkZ3B,eAAeA,WAAW;AAAC,IAAAwH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}