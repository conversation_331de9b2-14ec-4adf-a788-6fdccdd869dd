{"ast": null, "code": "var _ = require(\"../lodash\");\nvar Graph = require(\"../graph\");\nvar PriorityQueue = require(\"../data/priority-queue\");\nmodule.exports = prim;\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n  _.each(g.nodes(), function (v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (_.has(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error(\"Input graph is not connected: \" + g);\n    } else {\n      init = true;\n    }\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n  return result;\n}", "map": {"version": 3, "names": ["_", "require", "Graph", "PriorityQueue", "module", "exports", "prim", "g", "weightFunc", "result", "parents", "pq", "v", "updateNeighbors", "edge", "w", "pri", "priority", "undefined", "edgeWeight", "decrease", "nodeCount", "each", "nodes", "add", "Number", "POSITIVE_INFINITY", "setNode", "init", "size", "removeMin", "has", "setEdge", "Error", "nodeEdges", "for<PERSON>ach"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/prim.js"], "sourcesContent": ["var _ = require(\"../lodash\");\nvar Graph = require(\"../graph\");\nvar PriorityQueue = require(\"../data/priority-queue\");\n\nmodule.exports = prim;\n\nfunction prim(g, weightFunc) {\n  var result = new Graph();\n  var parents = {};\n  var pq = new PriorityQueue();\n  var v;\n\n  function updateNeighbors(edge) {\n    var w = edge.v === v ? edge.w : edge.v;\n    var pri = pq.priority(w);\n    if (pri !== undefined) {\n      var edgeWeight = weightFunc(edge);\n      if (edgeWeight < pri) {\n        parents[w] = v;\n        pq.decrease(w, edgeWeight);\n      }\n    }\n  }\n\n  if (g.nodeCount() === 0) {\n    return result;\n  }\n\n  _.each(g.nodes(), function(v) {\n    pq.add(v, Number.POSITIVE_INFINITY);\n    result.setNode(v);\n  });\n\n  // Start from an arbitrary node\n  pq.decrease(g.nodes()[0], 0);\n\n  var init = false;\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    if (_.has(parents, v)) {\n      result.setEdge(v, parents[v]);\n    } else if (init) {\n      throw new Error(\"Input graph is not connected: \" + g);\n    } else {\n      init = true;\n    }\n\n    g.nodeEdges(v).forEach(updateNeighbors);\n  }\n\n  return result;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC5B,IAAIC,KAAK,GAAGD,OAAO,CAAC,UAAU,CAAC;AAC/B,IAAIE,aAAa,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAErDG,MAAM,CAACC,OAAO,GAAGC,IAAI;AAErB,SAASA,IAAIA,CAACC,CAAC,EAAEC,UAAU,EAAE;EAC3B,IAAIC,MAAM,GAAG,IAAIP,KAAK,CAAC,CAAC;EACxB,IAAIQ,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,EAAE,GAAG,IAAIR,aAAa,CAAC,CAAC;EAC5B,IAAIS,CAAC;EAEL,SAASC,eAAeA,CAACC,IAAI,EAAE;IAC7B,IAAIC,CAAC,GAAGD,IAAI,CAACF,CAAC,KAAKA,CAAC,GAAGE,IAAI,CAACC,CAAC,GAAGD,IAAI,CAACF,CAAC;IACtC,IAAII,GAAG,GAAGL,EAAE,CAACM,QAAQ,CAACF,CAAC,CAAC;IACxB,IAAIC,GAAG,KAAKE,SAAS,EAAE;MACrB,IAAIC,UAAU,GAAGX,UAAU,CAACM,IAAI,CAAC;MACjC,IAAIK,UAAU,GAAGH,GAAG,EAAE;QACpBN,OAAO,CAACK,CAAC,CAAC,GAAGH,CAAC;QACdD,EAAE,CAACS,QAAQ,CAACL,CAAC,EAAEI,UAAU,CAAC;MAC5B;IACF;EACF;EAEA,IAAIZ,CAAC,CAACc,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE;IACvB,OAAOZ,MAAM;EACf;EAEAT,CAAC,CAACsB,IAAI,CAACf,CAAC,CAACgB,KAAK,CAAC,CAAC,EAAE,UAASX,CAAC,EAAE;IAC5BD,EAAE,CAACa,GAAG,CAACZ,CAAC,EAAEa,MAAM,CAACC,iBAAiB,CAAC;IACnCjB,MAAM,CAACkB,OAAO,CAACf,CAAC,CAAC;EACnB,CAAC,CAAC;;EAEF;EACAD,EAAE,CAACS,QAAQ,CAACb,CAAC,CAACgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;EAE5B,IAAIK,IAAI,GAAG,KAAK;EAChB,OAAOjB,EAAE,CAACkB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACpBjB,CAAC,GAAGD,EAAE,CAACmB,SAAS,CAAC,CAAC;IAClB,IAAI9B,CAAC,CAAC+B,GAAG,CAACrB,OAAO,EAAEE,CAAC,CAAC,EAAE;MACrBH,MAAM,CAACuB,OAAO,CAACpB,CAAC,EAAEF,OAAO,CAACE,CAAC,CAAC,CAAC;IAC/B,CAAC,MAAM,IAAIgB,IAAI,EAAE;MACf,MAAM,IAAIK,KAAK,CAAC,gCAAgC,GAAG1B,CAAC,CAAC;IACvD,CAAC,MAAM;MACLqB,IAAI,GAAG,IAAI;IACb;IAEArB,CAAC,CAAC2B,SAAS,CAACtB,CAAC,CAAC,CAACuB,OAAO,CAACtB,eAAe,CAAC;EACzC;EAEA,OAAOJ,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}