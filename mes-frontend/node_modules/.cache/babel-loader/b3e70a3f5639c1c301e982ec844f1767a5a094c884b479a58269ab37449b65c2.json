{"ast": null, "code": "import { formatTime } from '../../util/time';\nexport function labelFormatter(time, interval) {\n  if (typeof time === 'number') {\n    return parseBySeries(time);\n  }\n  return parseByTime(time, interval);\n}\nexport function parseByTime(time, interval) {\n  var date = new Date(time);\n  switch (interval) {\n    case 'half-hour':\n    case 'hour':\n    case 'four-hour':\n      if ([0, 6, 12, 18].includes(date.getHours()) && date.getMinutes() === 0) {\n        // HH:mm\\nYYYY-MM-DD\n        return formatTime(date, 'HH:mm\\nYYYY-MM-DD');\n      }\n      // HH:mm\n      return formatTime(date, 'HH:mm');\n    case 'half-day':\n      // A\\nYYYY-MM-DD\n      if (date.getHours() < 12) {\n        return \"AM\\n\".concat(formatTime(date, 'YYYY-MM-DD'));\n      }\n      // A\n      return 'PM';\n    case 'day':\n      if ([1, 10, 20].includes(date.getDate())) {\n        // DD\\nYYYY-MM\n        return formatTime(date, 'DD\\nYYYY-MM');\n      }\n      // DD\n      return formatTime(date, 'DD');\n    case 'week':\n      if (date.getDate() <= 7) {\n        // DD\\nYYYY-MM\n        return formatTime(date, 'DD\\nYYYY-MM');\n      }\n      // DD\n      return formatTime(date, 'DD');\n    case 'month':\n      if ([0, 6].includes(date.getMonth())) {\n        // MM月\\nYYYY\n        return formatTime(date, 'MM月\\nYYYY');\n      }\n      // MM月\n      return formatTime(date, 'MM月');\n    case 'season':\n      if ([0].includes(date.getMonth())) {\n        // MM月\\nYYYY\n        return formatTime(date, 'MM月\\nYYYY');\n      }\n      // MM月\n      return formatTime(date, 'MM月');\n    case 'year':\n      // YYYY\n      return formatTime(date, 'YYYY');\n    default:\n      // YYYY-MM-DD HH:mm\n      return formatTime(date, 'YYYY-MM-DD HH:mm');\n  }\n}\n/**\n * 按照序列数据解析，如第 1, 2, 3 秒\n * @param time\n * @param interval\n */\nexport function parseBySeries(time) {\n  var hours = String(Math.floor(time / 3600)).padStart(2, '0');\n  var minutes = String(Math.floor(time % 3600 / 60)).padStart(2, '0');\n  var seconds = String(Math.floor(time % 60)).padStart(2, '0');\n  if (time < 3600) {\n    // mm:ss\n    return \"\".concat(minutes, \":\").concat(seconds);\n  }\n  return \"\".concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n}", "map": {"version": 3, "names": ["formatTime", "labelFormatter", "time", "interval", "parseBySeries", "parseByTime", "date", "Date", "includes", "getHours", "getMinutes", "concat", "getDate", "getMonth", "hours", "String", "Math", "floor", "padStart", "minutes", "seconds"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/timebar/utils.ts"], "sourcesContent": ["import type { Interval } from './types';\nimport { formatTime } from '../../util/time';\n// TODO 需要根据传入时间、当前时间轴宽度 自动计算出最佳的时间间隔\n/**\n * 时间格式化，为 Date 时，按照时间解析，为数字时按照序列解析\n * @param time\n * @param interval 时间间隔\n * @returns\n */\nexport function labelFormatter(time: number): string;\nexport function labelFormatter(time: Date, interval: Interval): string;\nexport function labelFormatter(time: number | Date, interval?: Interval) {\n  if (typeof time === 'number') {\n    return parseBySeries(time);\n  }\n  return parseByTime(time, interval!);\n}\n\nexport function parseByTime(time: Date, interval: Interval) {\n  const date = new Date(time);\n\n  switch (interval) {\n    case 'half-hour':\n    case 'hour':\n    case 'four-hour':\n      if ([0, 6, 12, 18].includes(date.getHours()) && date.getMinutes() === 0) {\n        // HH:mm\\nYYYY-MM-DD\n        return formatTime(date, 'HH:mm\\nYYYY-MM-DD');\n      }\n      // HH:mm\n      return formatTime(date, 'HH:mm');\n    case 'half-day':\n      // A\\nYYYY-MM-DD\n      if (date.getHours() < 12) {\n        return `AM\\n${formatTime(date, 'YYYY-MM-DD')}`;\n      }\n      // A\n      return 'PM';\n    case 'day':\n      if ([1, 10, 20].includes(date.getDate())) {\n        // DD\\nYYYY-MM\n        return formatTime(date, 'DD\\nYYYY-MM');\n      }\n      // DD\n      return formatTime(date, 'DD');\n    case 'week':\n      if (date.getDate() <= 7) {\n        // DD\\nYYYY-MM\n        return formatTime(date, 'DD\\nYYYY-MM');\n      }\n      // DD\n      return formatTime(date, 'DD');\n    case 'month':\n      if ([0, 6].includes(date.getMonth())) {\n        // MM月\\nYYYY\n        return formatTime(date, 'MM月\\nYYYY');\n      }\n      // MM月\n      return formatTime(date, 'MM月');\n    case 'season':\n      if ([0].includes(date.getMonth())) {\n        // MM月\\nYYYY\n        return formatTime(date, 'MM月\\nYYYY');\n      }\n      // MM月\n      return formatTime(date, 'MM月');\n    case 'year':\n      // YYYY\n      return formatTime(date, 'YYYY');\n    default:\n      // YYYY-MM-DD HH:mm\n      return formatTime(date, 'YYYY-MM-DD HH:mm');\n  }\n}\n\n/**\n * 按照序列数据解析，如第 1, 2, 3 秒\n * @param time\n * @param interval\n */\nexport function parseBySeries(time: number) {\n  const hours = String(Math.floor(time / 3600)).padStart(2, '0');\n  const minutes = String(Math.floor((time % 3600) / 60)).padStart(2, '0');\n  const seconds = String(Math.floor(time % 60)).padStart(2, '0');\n  if (time < 3600) {\n    // mm:ss\n    return `${minutes}:${seconds}`;\n  }\n  return `${hours}:${minutes}:${seconds}`;\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,iBAAiB;AAU5C,OAAM,SAAUC,cAAcA,CAACC,IAAmB,EAAEC,QAAmB;EACrE,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOE,aAAa,CAACF,IAAI,CAAC;EAC5B;EACA,OAAOG,WAAW,CAACH,IAAI,EAAEC,QAAS,CAAC;AACrC;AAEA,OAAM,SAAUE,WAAWA,CAACH,IAAU,EAAEC,QAAkB;EACxD,IAAMG,IAAI,GAAG,IAAIC,IAAI,CAACL,IAAI,CAAC;EAE3B,QAAQC,QAAQ;IACd,KAAK,WAAW;IAChB,KAAK,MAAM;IACX,KAAK,WAAW;MACd,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAACK,QAAQ,CAACF,IAAI,CAACG,QAAQ,EAAE,CAAC,IAAIH,IAAI,CAACI,UAAU,EAAE,KAAK,CAAC,EAAE;QACvE;QACA,OAAOV,UAAU,CAACM,IAAI,EAAE,mBAAmB,CAAC;MAC9C;MACA;MACA,OAAON,UAAU,CAACM,IAAI,EAAE,OAAO,CAAC;IAClC,KAAK,UAAU;MACb;MACA,IAAIA,IAAI,CAACG,QAAQ,EAAE,GAAG,EAAE,EAAE;QACxB,OAAO,OAAAE,MAAA,CAAOX,UAAU,CAACM,IAAI,EAAE,YAAY,CAAC,CAAE;MAChD;MACA;MACA,OAAO,IAAI;IACb,KAAK,KAAK;MACR,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAACE,QAAQ,CAACF,IAAI,CAACM,OAAO,EAAE,CAAC,EAAE;QACxC;QACA,OAAOZ,UAAU,CAACM,IAAI,EAAE,aAAa,CAAC;MACxC;MACA;MACA,OAAON,UAAU,CAACM,IAAI,EAAE,IAAI,CAAC;IAC/B,KAAK,MAAM;MACT,IAAIA,IAAI,CAACM,OAAO,EAAE,IAAI,CAAC,EAAE;QACvB;QACA,OAAOZ,UAAU,CAACM,IAAI,EAAE,aAAa,CAAC;MACxC;MACA;MACA,OAAON,UAAU,CAACM,IAAI,EAAE,IAAI,CAAC;IAC/B,KAAK,OAAO;MACV,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAACE,QAAQ,CAACF,IAAI,CAACO,QAAQ,EAAE,CAAC,EAAE;QACpC;QACA,OAAOb,UAAU,CAACM,IAAI,EAAE,WAAW,CAAC;MACtC;MACA;MACA,OAAON,UAAU,CAACM,IAAI,EAAE,KAAK,CAAC;IAChC,KAAK,QAAQ;MACX,IAAI,CAAC,CAAC,CAAC,CAACE,QAAQ,CAACF,IAAI,CAACO,QAAQ,EAAE,CAAC,EAAE;QACjC;QACA,OAAOb,UAAU,CAACM,IAAI,EAAE,WAAW,CAAC;MACtC;MACA;MACA,OAAON,UAAU,CAACM,IAAI,EAAE,KAAK,CAAC;IAChC,KAAK,MAAM;MACT;MACA,OAAON,UAAU,CAACM,IAAI,EAAE,MAAM,CAAC;IACjC;MACE;MACA,OAAON,UAAU,CAACM,IAAI,EAAE,kBAAkB,CAAC;EAC/C;AACF;AAEA;;;;;AAKA,OAAM,SAAUF,aAAaA,CAACF,IAAY;EACxC,IAAMY,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACf,IAAI,GAAG,IAAI,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC9D,IAAMC,OAAO,GAAGJ,MAAM,CAACC,IAAI,CAACC,KAAK,CAAEf,IAAI,GAAG,IAAI,GAAI,EAAE,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EACvE,IAAME,OAAO,GAAGL,MAAM,CAACC,IAAI,CAACC,KAAK,CAACf,IAAI,GAAG,EAAE,CAAC,CAAC,CAACgB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC9D,IAAIhB,IAAI,GAAG,IAAI,EAAE;IACf;IACA,OAAO,GAAAS,MAAA,CAAGQ,OAAO,OAAAR,MAAA,CAAIS,OAAO,CAAE;EAChC;EACA,OAAO,GAAAT,MAAA,CAAGG,KAAK,OAAAH,MAAA,CAAIQ,OAAO,OAAAR,MAAA,CAAIS,OAAO,CAAE;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}