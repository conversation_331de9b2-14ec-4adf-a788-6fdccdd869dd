{"ast": null, "code": "/**\n * The `PathParser` is used by the `parsePathString` static method\n * to generate a `pathArray`.\n */\nvar PathParser = /** @class */function () {\n  function PathParser(pathString) {\n    this.pathValue = pathString;\n    // @ts-ignore\n    this.segments = [];\n    this.max = pathString.length;\n    this.index = 0;\n    this.param = 0.0;\n    this.segmentStart = 0;\n    this.data = [];\n    this.err = '';\n  }\n  return PathParser;\n}();\nexport { PathParser };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "pathString", "pathValue", "segments", "max", "length", "index", "param", "segmentStart", "data", "err"], "sources": ["path/parser/path-parser.ts"], "sourcesContent": [null], "mappings": "AAEA;;;;AAIA,IAAAA,UAAA;EAiBE,SAAAA,WAAYC,UAAkB;IAC5B,IAAI,CAACC,SAAS,GAAGD,UAAU;IAC3B;IACA,IAAI,CAACE,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,GAAG,GAAGH,UAAU,CAACI,MAAM;IAC5B,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,GAAG;IAChB,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,IAAI,GAAG,EAAE;IACd,IAAI,CAACC,GAAG,GAAG,EAAE;EACf;EACF,OAAAV,UAAC;AAAD,CAAC,CA5BD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}