{"ast": null, "code": "export { Polyline } from '@antv/g';", "map": {"version": 3, "names": ["Polyline"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Polyline.ts"], "sourcesContent": ["import type { PolylineStyleProps as GPolylineStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Polyline } from '@antv/g';\nexport type PolylineStyleProps = OmitConflictStyleProps<GPolylineStyleProps>;\n"], "mappings": "AAGA,SAASA,QAAQ,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}