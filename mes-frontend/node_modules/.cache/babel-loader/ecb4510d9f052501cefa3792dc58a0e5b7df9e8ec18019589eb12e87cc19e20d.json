{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = topsort;\ntopsort.CycleException = CycleException;\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n  function visit(node) {\n    if (_.has(stack, node)) {\n      throw new CycleException();\n    }\n    if (!_.has(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n  _.each(g.sinks(), visit);\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n  return results;\n}\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing", "map": {"version": 3, "names": ["_", "require", "module", "exports", "topsort", "CycleException", "g", "visited", "stack", "results", "visit", "node", "has", "each", "predecessors", "push", "sinks", "size", "nodeCount", "prototype", "Error"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/topsort.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = topsort;\ntopsort.CycleException = CycleException;\n\nfunction topsort(g) {\n  var visited = {};\n  var stack = {};\n  var results = [];\n\n  function visit(node) {\n    if (_.has(stack, node)) {\n      throw new CycleException();\n    }\n\n    if (!_.has(visited, node)) {\n      stack[node] = true;\n      visited[node] = true;\n      _.each(g.predecessors(node), visit);\n      delete stack[node];\n      results.push(node);\n    }\n  }\n\n  _.each(g.sinks(), visit);\n\n  if (_.size(visited) !== g.nodeCount()) {\n    throw new CycleException();\n  }\n\n  return results;\n}\n\nfunction CycleException() {}\nCycleException.prototype = new Error(); // must be an instance of Error to pass testing"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,OAAO;AACxBA,OAAO,CAACC,cAAc,GAAGA,cAAc;AAEvC,SAASD,OAAOA,CAACE,CAAC,EAAE;EAClB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,OAAO,GAAG,EAAE;EAEhB,SAASC,KAAKA,CAACC,IAAI,EAAE;IACnB,IAAIX,CAAC,CAACY,GAAG,CAACJ,KAAK,EAAEG,IAAI,CAAC,EAAE;MACtB,MAAM,IAAIN,cAAc,CAAC,CAAC;IAC5B;IAEA,IAAI,CAACL,CAAC,CAACY,GAAG,CAACL,OAAO,EAAEI,IAAI,CAAC,EAAE;MACzBH,KAAK,CAACG,IAAI,CAAC,GAAG,IAAI;MAClBJ,OAAO,CAACI,IAAI,CAAC,GAAG,IAAI;MACpBX,CAAC,CAACa,IAAI,CAACP,CAAC,CAACQ,YAAY,CAACH,IAAI,CAAC,EAAED,KAAK,CAAC;MACnC,OAAOF,KAAK,CAACG,IAAI,CAAC;MAClBF,OAAO,CAACM,IAAI,CAACJ,IAAI,CAAC;IACpB;EACF;EAEAX,CAAC,CAACa,IAAI,CAACP,CAAC,CAACU,KAAK,CAAC,CAAC,EAAEN,KAAK,CAAC;EAExB,IAAIV,CAAC,CAACiB,IAAI,CAACV,OAAO,CAAC,KAAKD,CAAC,CAACY,SAAS,CAAC,CAAC,EAAE;IACrC,MAAM,IAAIb,cAAc,CAAC,CAAC;EAC5B;EAEA,OAAOI,OAAO;AAChB;AAEA,SAASJ,cAAcA,CAAA,EAAG,CAAC;AAC3BA,cAAc,CAACc,SAAS,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}