{"ast": null, "code": "import { isAnyArray } from 'is-any-array';\nimport max from 'ml-array-max';\nimport min from 'ml-array-min';\nfunction rescale(input) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  if (!isAnyArray(input)) {\n    throw new TypeError('input must be an array');\n  } else if (input.length === 0) {\n    throw new TypeError('input must not be empty');\n  }\n  var output;\n  if (options.output !== undefined) {\n    if (!isAnyArray(options.output)) {\n      throw new TypeError('output option must be an array if specified');\n    }\n    output = options.output;\n  } else {\n    output = new Array(input.length);\n  }\n  var currentMin = min(input);\n  var currentMax = max(input);\n  if (currentMin === currentMax) {\n    throw new RangeError('minimum and maximum input values are equal. Cannot rescale a constant array');\n  }\n  var _options$min = options.min,\n    minValue = _options$min === void 0 ? options.autoMinMax ? currentMin : 0 : _options$min,\n    _options$max = options.max,\n    maxValue = _options$max === void 0 ? options.autoMinMax ? currentMax : 1 : _options$max;\n  if (minValue >= maxValue) {\n    throw new RangeError('min option must be smaller than max option');\n  }\n  var factor = (maxValue - minValue) / (currentMax - currentMin);\n  for (var i = 0; i < input.length; i++) {\n    output[i] = (input[i] - currentMin) * factor + minValue;\n  }\n  return output;\n}\nexport { rescale as default };", "map": {"version": 3, "names": ["isAnyArray", "max", "min", "rescale", "input", "options", "arguments", "length", "undefined", "TypeError", "output", "Array", "currentMin", "currentMax", "RangeError", "_options$min", "minValue", "autoMinMax", "_options$max", "maxValue", "factor", "i", "default"], "sources": ["/root/mes-system/mes-frontend/node_modules/ml-array-rescale/lib-es6/index.js"], "sourcesContent": ["import { isAnyArray } from 'is-any-array';\nimport max from 'ml-array-max';\nimport min from 'ml-array-min';\n\nfunction rescale(input) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  if (!isAnyArray(input)) {\n    throw new TypeError('input must be an array');\n  } else if (input.length === 0) {\n    throw new TypeError('input must not be empty');\n  }\n\n  var output;\n\n  if (options.output !== undefined) {\n    if (!isAnyArray(options.output)) {\n      throw new TypeError('output option must be an array if specified');\n    }\n\n    output = options.output;\n  } else {\n    output = new Array(input.length);\n  }\n\n  var currentMin = min(input);\n  var currentMax = max(input);\n\n  if (currentMin === currentMax) {\n    throw new RangeError('minimum and maximum input values are equal. Cannot rescale a constant array');\n  }\n\n  var _options$min = options.min,\n      minValue = _options$min === void 0 ? options.autoMinMax ? currentMin : 0 : _options$min,\n      _options$max = options.max,\n      maxValue = _options$max === void 0 ? options.autoMinMax ? currentMax : 1 : _options$max;\n\n  if (minValue >= maxValue) {\n    throw new RangeError('min option must be smaller than max option');\n  }\n\n  var factor = (maxValue - minValue) / (currentMax - currentMin);\n\n  for (var i = 0; i < input.length; i++) {\n    output[i] = (input[i] - currentMin) * factor + minValue;\n  }\n\n  return output;\n}\n\nexport { rescale as default };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,GAAG,MAAM,cAAc;AAE9B,SAASC,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAEpF,IAAI,CAACN,UAAU,CAACI,KAAK,CAAC,EAAE;IACtB,MAAM,IAAIK,SAAS,CAAC,wBAAwB,CAAC;EAC/C,CAAC,MAAM,IAAIL,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;IAC7B,MAAM,IAAIE,SAAS,CAAC,yBAAyB,CAAC;EAChD;EAEA,IAAIC,MAAM;EAEV,IAAIL,OAAO,CAACK,MAAM,KAAKF,SAAS,EAAE;IAChC,IAAI,CAACR,UAAU,CAACK,OAAO,CAACK,MAAM,CAAC,EAAE;MAC/B,MAAM,IAAID,SAAS,CAAC,6CAA6C,CAAC;IACpE;IAEAC,MAAM,GAAGL,OAAO,CAACK,MAAM;EACzB,CAAC,MAAM;IACLA,MAAM,GAAG,IAAIC,KAAK,CAACP,KAAK,CAACG,MAAM,CAAC;EAClC;EAEA,IAAIK,UAAU,GAAGV,GAAG,CAACE,KAAK,CAAC;EAC3B,IAAIS,UAAU,GAAGZ,GAAG,CAACG,KAAK,CAAC;EAE3B,IAAIQ,UAAU,KAAKC,UAAU,EAAE;IAC7B,MAAM,IAAIC,UAAU,CAAC,6EAA6E,CAAC;EACrG;EAEA,IAAIC,YAAY,GAAGV,OAAO,CAACH,GAAG;IAC1Bc,QAAQ,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGV,OAAO,CAACY,UAAU,GAAGL,UAAU,GAAG,CAAC,GAAGG,YAAY;IACvFG,YAAY,GAAGb,OAAO,CAACJ,GAAG;IAC1BkB,QAAQ,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGb,OAAO,CAACY,UAAU,GAAGJ,UAAU,GAAG,CAAC,GAAGK,YAAY;EAE3F,IAAIF,QAAQ,IAAIG,QAAQ,EAAE;IACxB,MAAM,IAAIL,UAAU,CAAC,4CAA4C,CAAC;EACpE;EAEA,IAAIM,MAAM,GAAG,CAACD,QAAQ,GAAGH,QAAQ,KAAKH,UAAU,GAAGD,UAAU,CAAC;EAE9D,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,KAAK,CAACG,MAAM,EAAEc,CAAC,EAAE,EAAE;IACrCX,MAAM,CAACW,CAAC,CAAC,GAAG,CAACjB,KAAK,CAACiB,CAAC,CAAC,GAAGT,UAAU,IAAIQ,MAAM,GAAGJ,QAAQ;EACzD;EAEA,OAAON,MAAM;AACf;AAEA,SAASP,OAAO,IAAImB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}