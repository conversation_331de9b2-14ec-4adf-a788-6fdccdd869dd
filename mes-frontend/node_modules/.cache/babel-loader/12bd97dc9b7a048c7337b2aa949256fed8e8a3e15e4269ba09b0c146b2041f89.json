{"ast": null, "code": "/* global window */\n\nvar graphlib;\nif (typeof require === \"function\") {\n  try {\n    graphlib = require(\"graphlib\");\n  } catch (e) {\n    // continue regardless of error\n  }\n}\nif (!graphlib) {\n  graphlib = window.graphlib;\n}\nmodule.exports = graphlib;", "map": {"version": 3, "names": ["graphlib", "require", "e", "window", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/graphlib.js"], "sourcesContent": ["/* global window */\n\nvar graphlib;\n\nif (typeof require === \"function\") {\n  try {\n    graphlib = require(\"graphlib\");\n  } catch (e) {\n    // continue regardless of error\n  }\n}\n\nif (!graphlib) {\n  graphlib = window.graphlib;\n}\n\nmodule.exports = graphlib;\n"], "mappings": "AAAA;;AAEA,IAAIA,QAAQ;AAEZ,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;EACjC,IAAI;IACFD,QAAQ,GAAGC,OAAO,CAAC,UAAU,CAAC;EAChC,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV;EAAA;AAEJ;AAEA,IAAI,CAACF,QAAQ,EAAE;EACbA,QAAQ,GAAGG,MAAM,CAACH,QAAQ;AAC5B;AAEAI,MAAM,CAACC,OAAO,GAAGL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}