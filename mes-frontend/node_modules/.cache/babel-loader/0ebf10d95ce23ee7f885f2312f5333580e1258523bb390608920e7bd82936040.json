{"ast": null, "code": "export default function (x) {\n  if (isNaN(x = +x)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n    x1 = this._x1;\n\n  // If the binarytree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing half boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n      node = this._root,\n      parent,\n      i;\n    while (x0 > x || x >= x1) {\n      i = +(x < x0);\n      parent = new Array(2), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0:\n          x1 = x0 + z;\n          break;\n        case 1:\n          x0 = x1 - z;\n          break;\n      }\n    }\n    if (this._root && this._root.length) this._root = node;\n  }\n  this._x0 = x0;\n  this._x1 = x1;\n  return this;\n}", "map": {"version": 3, "names": ["x", "isNaN", "x0", "_x0", "x1", "_x1", "Math", "floor", "z", "node", "_root", "parent", "i", "Array", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-binarytree/src/cover.js"], "sourcesContent": ["export default function(x) {\n  if (isNaN(x = +x)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n      x1 = this._x1;\n\n  // If the binarytree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing half boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n        node = this._root,\n        parent,\n        i;\n\n    while (x0 > x || x >= x1) {\n      i = +(x < x0);\n      parent = new Array(2), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0: x1 = x0 + z; break;\n        case 1: x0 = x1 - z; break;\n      }\n    }\n\n    if (this._root && this._root.length) this._root = node;\n  }\n\n  this._x0 = x0;\n  this._x1 = x1;\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,IAAIC,KAAK,CAACD,CAAC,GAAG,CAACA,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAEhC,IAAIE,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;;EAEjB;EACA;EACA;EACA,IAAIJ,KAAK,CAACC,EAAE,CAAC,EAAE;IACbE,EAAE,GAAG,CAACF,EAAE,GAAGI,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC,IAAI,CAAC;EAC/B;;EAEA;EAAA,KACK;IACH,IAAIQ,CAAC,GAAGJ,EAAE,GAAGF,EAAE,IAAI,CAAC;MAChBO,IAAI,GAAG,IAAI,CAACC,KAAK;MACjBC,MAAM;MACNC,CAAC;IAEL,OAAOV,EAAE,GAAGF,CAAC,IAAIA,CAAC,IAAII,EAAE,EAAE;MACxBQ,CAAC,GAAG,EAAEZ,CAAC,GAAGE,EAAE,CAAC;MACbS,MAAM,GAAG,IAAIE,KAAK,CAAC,CAAC,CAAC,EAAEF,MAAM,CAACC,CAAC,CAAC,GAAGH,IAAI,EAAEA,IAAI,GAAGE,MAAM,EAAEH,CAAC,IAAI,CAAC;MAC9D,QAAQI,CAAC;QACP,KAAK,CAAC;UAAER,EAAE,GAAGF,EAAE,GAAGM,CAAC;UAAE;QACrB,KAAK,CAAC;UAAEN,EAAE,GAAGE,EAAE,GAAGI,CAAC;UAAE;MACvB;IACF;IAEA,IAAI,IAAI,CAACE,KAAK,IAAI,IAAI,CAACA,KAAK,CAACI,MAAM,EAAE,IAAI,CAACJ,KAAK,GAAGD,IAAI;EACxD;EAEA,IAAI,CAACN,GAAG,GAAGD,EAAE;EACb,IAAI,CAACG,GAAG,GAAGD,EAAE;EACb,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}