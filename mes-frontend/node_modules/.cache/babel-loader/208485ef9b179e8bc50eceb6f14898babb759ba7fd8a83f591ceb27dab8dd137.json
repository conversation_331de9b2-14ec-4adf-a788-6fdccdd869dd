{"ast": null, "code": "import constant from \"./constant.js\";\nexport default function (x) {\n  var strength = constant(0.1),\n    nodes,\n    strengths,\n    xz;\n  if (typeof x !== \"function\") x = constant(x == null ? 0 : +x);\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vx += (xz[i] - node.x) * strengths[i] * alpha;\n    }\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length;\n    strengths = new Array(n);\n    xz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(xz[i] = +x(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n  force.initialize = function (_) {\n    nodes = _;\n    initialize();\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n  force.x = function (_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : x;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["constant", "x", "strength", "nodes", "strengths", "xz", "force", "alpha", "i", "n", "length", "node", "vx", "initialize", "Array", "isNaN", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force/src/x.js"], "sourcesContent": ["import constant from \"./constant.js\";\n\nexport default function(x) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      xz;\n\n  if (typeof x !== \"function\") x = constant(x == null ? 0 : +x);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vx += (xz[i] - node.x) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    xz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(xz[i] = +x(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : x;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,UAASC,CAAC,EAAE;EACzB,IAAIC,QAAQ,GAAGF,QAAQ,CAAC,GAAG,CAAC;IACxBG,KAAK;IACLC,SAAS;IACTC,EAAE;EAEN,IAAI,OAAOJ,CAAC,KAAK,UAAU,EAAEA,CAAC,GAAGD,QAAQ,CAACC,CAAC,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,CAAC,CAAC;EAE7D,SAASK,KAAKA,CAACC,KAAK,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGN,KAAK,CAACO,MAAM,EAAEC,IAAI,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAClDG,IAAI,GAAGR,KAAK,CAACK,CAAC,CAAC,EAAEG,IAAI,CAACC,EAAE,IAAI,CAACP,EAAE,CAACG,CAAC,CAAC,GAAGG,IAAI,CAACV,CAAC,IAAIG,SAAS,CAACI,CAAC,CAAC,GAAGD,KAAK;IACrE;EACF;EAEA,SAASM,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACV,KAAK,EAAE;IACZ,IAAIK,CAAC;MAAEC,CAAC,GAAGN,KAAK,CAACO,MAAM;IACvBN,SAAS,GAAG,IAAIU,KAAK,CAACL,CAAC,CAAC;IACxBJ,EAAE,GAAG,IAAIS,KAAK,CAACL,CAAC,CAAC;IACjB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBJ,SAAS,CAACI,CAAC,CAAC,GAAGO,KAAK,CAACV,EAAE,CAACG,CAAC,CAAC,GAAG,CAACP,CAAC,CAACE,KAAK,CAACK,CAAC,CAAC,EAAEA,CAAC,EAAEL,KAAK,CAAC,CAAC,GAAG,CAAC,GAAG,CAACD,QAAQ,CAACC,KAAK,CAACK,CAAC,CAAC,EAAEA,CAAC,EAAEL,KAAK,CAAC;IAC1F;EACF;EAEAG,KAAK,CAACO,UAAU,GAAG,UAASG,CAAC,EAAE;IAC7Bb,KAAK,GAAGa,CAAC;IACTH,UAAU,CAAC,CAAC;EACd,CAAC;EAEDP,KAAK,CAACJ,QAAQ,GAAG,UAASc,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACP,MAAM,IAAIR,QAAQ,GAAG,OAAOc,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGhB,QAAQ,CAAC,CAACgB,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC,EAAEP,KAAK,IAAIJ,QAAQ;EACnH,CAAC;EAEDI,KAAK,CAACL,CAAC,GAAG,UAASe,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACP,MAAM,IAAIT,CAAC,GAAG,OAAOe,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGhB,QAAQ,CAAC,CAACgB,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC,EAAEP,KAAK,IAAIL,CAAC;EACrG,CAAC;EAED,OAAOK,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}