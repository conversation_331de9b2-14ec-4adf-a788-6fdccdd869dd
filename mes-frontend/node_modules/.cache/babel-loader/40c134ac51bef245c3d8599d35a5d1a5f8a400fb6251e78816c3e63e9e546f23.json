{"ast": null, "code": "var _jsxFileName = \"/root/mes-system/mes-frontend/src/pages/Dashboard/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Row, Col, Card, Statistic, Progress, Typography, Space, Spin, Alert, Button, Tabs } from 'antd';\nimport { ProjectOutlined, FileTextOutlined, CheckCircleOutlined, TeamOutlined, ToolOutlined, TrophyOutlined, ReloadOutlined, BarChartOutlined, MonitorOutlined } from '@ant-design/icons';\nimport { dashboardService } from '../../services/business';\nimport ProductionChart from '../../components/Charts/ProductionChart';\nimport QualityChart from '../../components/Charts/QualityChart';\nimport EquipmentChart from '../../components/Charts/EquipmentChart';\nimport RealTimeMonitor from '../../components/Dashboard/RealTimeMonitor';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst Dashboard = () => {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await dashboardService.getStats();\n      if (response.success && response.data) {\n        setStats(response.data);\n      } else {\n        throw new Error(response.message || '获取数据失败');\n      }\n    } catch (err) {\n      setError(err.message || '获取仪表板数据失败');\n      // 设置模拟数据用于演示\n      setStats({\n        total_projects: 12,\n        active_projects: 8,\n        total_work_orders: 45,\n        active_work_orders: 23,\n        completed_tasks_today: 15,\n        active_operators: 28,\n        equipment_utilization: 85,\n        quality_rate: 98.5\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchStats();\n  }, []);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '50px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          children: \"\\u6B63\\u5728\\u52A0\\u8F7D\\u4EEA\\u8868\\u677F\\u6570\\u636E...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"\\u751F\\u4EA7\\u4EEA\\u8868\\u677F\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 17\n        }, this),\n        onClick: fetchStats,\n        loading: loading,\n        children: \"\\u5237\\u65B0\\u6570\\u636E\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u6570\\u636E\\u52A0\\u8F7D\\u63D0\\u793A\",\n      description: `${error}，当前显示模拟数据用于演示。`,\n      type: \"warning\",\n      showIcon: true,\n      style: {\n        marginBottom: '24px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 9\n    }, this), stats && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        style: {\n          marginBottom: '24px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u9879\\u76EE\\u6570\",\n              value: stats.total_projects,\n              prefix: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 27\n              }, this),\n              valueStyle: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: [\"\\u6D3B\\u8DC3\\u9879\\u76EE: \", stats.active_projects]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u603B\\u5DE5\\u5355\\u6570\",\n              value: stats.total_work_orders,\n              prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 27\n              }, this),\n              valueStyle: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: '8px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: [\"\\u8FDB\\u884C\\u4E2D: \", stats.active_work_orders]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u4ECA\\u65E5\\u5B8C\\u6210\\u4EFB\\u52A1\",\n              value: stats.completed_tasks_today,\n              prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 27\n              }, this),\n              valueStyle: {\n                color: '#722ed1'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          lg: 6,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            children: /*#__PURE__*/_jsxDEV(Statistic, {\n              title: \"\\u5728\\u7EBF\\u64CD\\u4F5C\\u5458\",\n              value: stats.active_operators,\n              prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 27\n              }, this),\n              valueStyle: {\n                color: '#fa8c16'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u8BBE\\u5907\\u5229\\u7528\\u7387\",\n            extra: /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 42\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Progress, {\n              type: \"circle\",\n              percent: stats.equipment_utilization,\n              size: 120,\n              strokeColor: {\n                '0%': '#108ee9',\n                '100%': '#87d068'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginTop: '16px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u8BBE\\u5907\\u8FD0\\u884C\\u72B6\\u6001\\u826F\\u597D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 12,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u8D28\\u91CF\\u5408\\u683C\\u7387\",\n            extra: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 42\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Progress, {\n              type: \"circle\",\n              percent: stats.quality_rate,\n              size: 120,\n              strokeColor: {\n                '0%': '#ffa940',\n                '100%': '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginTop: '16px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u8D28\\u91CF\\u63A7\\u5236\\u4F18\\u79C0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u6570\\u636E\\u5206\\u6790\\u548C\\u76D1\\u63A7\",\n        style: {\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          defaultActiveKey: \"charts\",\n          items: [{\n            key: 'charts',\n            label: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 23\n              }, this), \"\\u6570\\u636E\\u56FE\\u8868\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 21\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(Row, {\n              gutter: [16, 16],\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                lg: 12,\n                children: /*#__PURE__*/_jsxDEV(ProductionChart, {\n                  height: 250\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                lg: 12,\n                children: /*#__PURE__*/_jsxDEV(QualityChart, {\n                  height: 250\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                xs: 24,\n                children: /*#__PURE__*/_jsxDEV(EquipmentChart, {\n                  height: 250\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 240,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 21\n            }, this)\n          }, {\n            key: 'monitor',\n            label: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [/*#__PURE__*/_jsxDEV(MonitorOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 249,\n                columnNumber: 23\n              }, this), \"\\u5B9E\\u65F6\\u76D1\\u63A7\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 21\n            }, this),\n            children: /*#__PURE__*/_jsxDEV(RealTimeMonitor, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 29\n            }, this)\n          }]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"\\u5FEB\\u901F\\u64CD\\u4F5C\",\n        style: {\n          marginTop: '24px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          wrap: true,\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            children: \"\\u521B\\u5EFA\\u65B0\\u9879\\u76EE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            children: \"\\u521B\\u5EFA\\u5DE5\\u5355\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            children: \"\\u4EFB\\u52A1\\u8C03\\u5EA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            children: \"\\u8D28\\u91CF\\u68C0\\u67E5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            children: \"\\u751F\\u4EA7\\u62A5\\u544A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"wn5GnbEWSpy3babVhLIjrKhKWKg=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Row", "Col", "Card", "Statistic", "Progress", "Typography", "Space", "Spin", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tabs", "ProjectOutlined", "FileTextOutlined", "CheckCircleOutlined", "TeamOutlined", "ToolOutlined", "TrophyOutlined", "ReloadOutlined", "BarChartOutlined", "MonitorOutlined", "dashboardService", "ProductionChart", "QualityChart", "EquipmentChart", "RealTimeMonitor", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Text", "Dashboard", "_s", "stats", "setStats", "loading", "setLoading", "error", "setError", "fetchStats", "response", "getStats", "success", "data", "Error", "message", "err", "total_projects", "active_projects", "total_work_orders", "active_work_orders", "completed_tasks_today", "active_operators", "equipment_utilization", "quality_rate", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "display", "justifyContent", "alignItems", "marginBottom", "level", "margin", "icon", "onClick", "description", "type", "showIcon", "gutter", "xs", "sm", "lg", "title", "value", "prefix", "valueStyle", "color", "extra", "percent", "strokeColor", "defaultActiveKey", "items", "key", "label", "height", "wrap", "_c", "$RefreshReg$"], "sources": ["/root/mes-system/mes-frontend/src/pages/Dashboard/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Row,\n  Col,\n  Card,\n  Statistic,\n  Progress,\n  Typography,\n  Space,\n  Spin,\n  Alert,\n  Button,\n  Tabs,\n} from 'antd';\nimport {\n  ProjectOutlined,\n  FileTextOutlined,\n  CheckCircleOutlined,\n  TeamOutlined,\n  ToolOutlined,\n  TrophyOutlined,\n  ReloadOutlined,\n  <PERSON><PERSON><PERSON>Outlined,\n  MonitorOutlined,\n} from '@ant-design/icons';\nimport { dashboardService } from '../../services/business';\nimport { DashboardStats } from '../../types';\nimport ProductionChart from '../../components/Charts/ProductionChart';\nimport QualityChart from '../../components/Charts/QualityChart';\nimport EquipmentChart from '../../components/Charts/EquipmentChart';\nimport RealTimeMonitor from '../../components/Dashboard/RealTimeMonitor';\nimport RealTimeData from '../../components/Common/RealTimeData';\nimport { useApi } from '../../hooks/useApi';\n\nconst { Title, Text } = Typography;\n\nconst Dashboard: React.FC = () => {\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await dashboardService.getStats();\n      if (response.success && response.data) {\n        setStats(response.data);\n      } else {\n        throw new Error(response.message || '获取数据失败');\n      }\n    } catch (err: any) {\n      setError(err.message || '获取仪表板数据失败');\n      // 设置模拟数据用于演示\n      setStats({\n        total_projects: 12,\n        active_projects: 8,\n        total_work_orders: 45,\n        active_work_orders: 23,\n        completed_tasks_today: 15,\n        active_operators: 28,\n        equipment_utilization: 85,\n        quality_rate: 98.5,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchStats();\n  }, []);\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: 'center', padding: '50px' }}>\n        <Spin size=\"large\" />\n        <div style={{ marginTop: '16px' }}>\n          <Text>正在加载仪表板数据...</Text>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div style={{ \n        display: 'flex', \n        justifyContent: 'space-between', \n        alignItems: 'center', \n        marginBottom: '24px' \n      }}>\n        <Title level={2} style={{ margin: 0 }}>\n          生产仪表板\n        </Title>\n        <Button \n          icon={<ReloadOutlined />} \n          onClick={fetchStats}\n          loading={loading}\n        >\n          刷新数据\n        </Button>\n      </div>\n\n      {error && (\n        <Alert\n          message=\"数据加载提示\"\n          description={`${error}，当前显示模拟数据用于演示。`}\n          type=\"warning\"\n          showIcon\n          style={{ marginBottom: '24px' }}\n        />\n      )}\n\n      {stats && (\n        <>\n          {/* 统计卡片 */}\n          <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n            <Col xs={24} sm={12} lg={6}>\n              <Card>\n                <Statistic\n                  title=\"总项目数\"\n                  value={stats.total_projects}\n                  prefix={<ProjectOutlined />}\n                  valueStyle={{ color: '#1890ff' }}\n                />\n                <div style={{ marginTop: '8px' }}>\n                  <Text type=\"secondary\">\n                    活跃项目: {stats.active_projects}\n                  </Text>\n                </div>\n              </Card>\n            </Col>\n            \n            <Col xs={24} sm={12} lg={6}>\n              <Card>\n                <Statistic\n                  title=\"总工单数\"\n                  value={stats.total_work_orders}\n                  prefix={<FileTextOutlined />}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n                <div style={{ marginTop: '8px' }}>\n                  <Text type=\"secondary\">\n                    进行中: {stats.active_work_orders}\n                  </Text>\n                </div>\n              </Card>\n            </Col>\n            \n            <Col xs={24} sm={12} lg={6}>\n              <Card>\n                <Statistic\n                  title=\"今日完成任务\"\n                  value={stats.completed_tasks_today}\n                  prefix={<CheckCircleOutlined />}\n                  valueStyle={{ color: '#722ed1' }}\n                />\n              </Card>\n            </Col>\n            \n            <Col xs={24} sm={12} lg={6}>\n              <Card>\n                <Statistic\n                  title=\"在线操作员\"\n                  value={stats.active_operators}\n                  prefix={<TeamOutlined />}\n                  valueStyle={{ color: '#fa8c16' }}\n                />\n              </Card>\n            </Col>\n          </Row>\n\n          {/* 性能指标 */}\n          <Row gutter={[16, 16]}>\n            <Col xs={24} lg={12}>\n              <Card title=\"设备利用率\" extra={<ToolOutlined />}>\n                <Progress\n                  type=\"circle\"\n                  percent={stats.equipment_utilization}\n                  size={120}\n                  strokeColor={{\n                    '0%': '#108ee9',\n                    '100%': '#87d068',\n                  }}\n                />\n                <div style={{ textAlign: 'center', marginTop: '16px' }}>\n                  <Text type=\"secondary\">\n                    设备运行状态良好\n                  </Text>\n                </div>\n              </Card>\n            </Col>\n            \n            <Col xs={24} lg={12}>\n              <Card title=\"质量合格率\" extra={<TrophyOutlined />}>\n                <Progress\n                  type=\"circle\"\n                  percent={stats.quality_rate}\n                  size={120}\n                  strokeColor={{\n                    '0%': '#ffa940',\n                    '100%': '#52c41a',\n                  }}\n                />\n                <div style={{ textAlign: 'center', marginTop: '16px' }}>\n                  <Text type=\"secondary\">\n                    质量控制优秀\n                  </Text>\n                </div>\n              </Card>\n            </Col>\n          </Row>\n\n          {/* 数据分析和监控 */}\n          <Card\n            title=\"数据分析和监控\"\n            style={{ marginTop: '24px' }}\n          >\n            <Tabs\n              defaultActiveKey=\"charts\"\n              items={[\n                {\n                  key: 'charts',\n                  label: (\n                    <span>\n                      <BarChartOutlined />\n                      数据图表\n                    </span>\n                  ),\n                  children: (\n                    <Row gutter={[16, 16]}>\n                      <Col xs={24} lg={12}>\n                        <ProductionChart height={250} />\n                      </Col>\n                      <Col xs={24} lg={12}>\n                        <QualityChart height={250} />\n                      </Col>\n                      <Col xs={24}>\n                        <EquipmentChart height={250} />\n                      </Col>\n                    </Row>\n                  ),\n                },\n                {\n                  key: 'monitor',\n                  label: (\n                    <span>\n                      <MonitorOutlined />\n                      实时监控\n                    </span>\n                  ),\n                  children: <RealTimeMonitor />,\n                },\n              ]}\n            />\n          </Card>\n\n          {/* 快速操作 */}\n          <Card\n            title=\"快速操作\"\n            style={{ marginTop: '24px' }}\n          >\n            <Space wrap>\n              <Button type=\"primary\">创建新项目</Button>\n              <Button>创建工单</Button>\n              <Button>任务调度</Button>\n              <Button>质量检查</Button>\n              <Button>生产报告</Button>\n            </Space>\n          </Card>\n        </>\n      )}\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,IAAI,QACC,MAAM;AACb,SACEC,eAAe,EACfC,gBAAgB,EAChBC,mBAAmB,EACnBC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,eAAe,QACV,mBAAmB;AAC1B,SAASC,gBAAgB,QAAQ,yBAAyB;AAE1D,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,YAAY,MAAM,sCAAsC;AAC/D,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,eAAe,MAAM,4CAA4C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIzE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGzB,UAAU;AAElC,MAAM0B,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAwB,IAAI,CAAC;EAC/D,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMyC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,MAAME,QAAQ,GAAG,MAAMpB,gBAAgB,CAACqB,QAAQ,CAAC,CAAC;MAClD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;QACrCT,QAAQ,CAACM,QAAQ,CAACG,IAAI,CAAC;MACzB,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAACK,OAAO,IAAI,QAAQ,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOC,GAAQ,EAAE;MACjBR,QAAQ,CAACQ,GAAG,CAACD,OAAO,IAAI,WAAW,CAAC;MACpC;MACAX,QAAQ,CAAC;QACPa,cAAc,EAAE,EAAE;QAClBC,eAAe,EAAE,CAAC;QAClBC,iBAAiB,EAAE,EAAE;QACrBC,kBAAkB,EAAE,EAAE;QACtBC,qBAAqB,EAAE,EAAE;QACzBC,gBAAgB,EAAE,EAAE;QACpBC,qBAAqB,EAAE,EAAE;QACzBC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDrC,SAAS,CAAC,MAAM;IACdwC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,IAAIJ,OAAO,EAAE;IACX,oBACET,OAAA;MAAK6B,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,gBACnDhC,OAAA,CAACnB,IAAI;QAACoD,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrBrC,OAAA;QAAK6B,KAAK,EAAE;UAAES,SAAS,EAAE;QAAO,CAAE;QAAAN,QAAA,eAChChC,OAAA,CAACI,IAAI;UAAA4B,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAAgC,QAAA,gBACEhC,OAAA;MAAK6B,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE;MAChB,CAAE;MAAAV,QAAA,gBACAhC,OAAA,CAACG,KAAK;QAACwC,KAAK,EAAE,CAAE;QAACd,KAAK,EAAE;UAAEe,MAAM,EAAE;QAAE,CAAE;QAAAZ,QAAA,EAAC;MAEvC;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRrC,OAAA,CAACjB,MAAM;QACL8D,IAAI,eAAE7C,OAAA,CAACT,cAAc;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBS,OAAO,EAAEjC,UAAW;QACpBJ,OAAO,EAAEA,OAAQ;QAAAuB,QAAA,EAClB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAEL1B,KAAK,iBACJX,OAAA,CAAClB,KAAK;MACJqC,OAAO,EAAC,sCAAQ;MAChB4B,WAAW,EAAE,GAAGpC,KAAK,gBAAiB;MACtCqC,IAAI,EAAC,SAAS;MACdC,QAAQ;MACRpB,KAAK,EAAE;QAAEa,YAAY,EAAE;MAAO;IAAE;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF,EAEA9B,KAAK,iBACJP,OAAA,CAAAE,SAAA;MAAA8B,QAAA,gBAEEhC,OAAA,CAAC1B,GAAG;QAAC4E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAACrB,KAAK,EAAE;UAAEa,YAAY,EAAE;QAAO,CAAE;QAAAV,QAAA,gBACrDhC,OAAA,CAACzB,GAAG;UAAC4E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACzBhC,OAAA,CAACxB,IAAI;YAAAwD,QAAA,gBACHhC,OAAA,CAACvB,SAAS;cACR6E,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAEhD,KAAK,CAACc,cAAe;cAC5BmC,MAAM,eAAExD,OAAA,CAACf,eAAe;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC5BoB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACFrC,OAAA;cAAK6B,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAM,CAAE;cAAAN,QAAA,eAC/BhC,OAAA,CAACI,IAAI;gBAAC4C,IAAI,EAAC,WAAW;gBAAAhB,QAAA,GAAC,4BACf,EAACzB,KAAK,CAACe,eAAe;cAAA;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENrC,OAAA,CAACzB,GAAG;UAAC4E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACzBhC,OAAA,CAACxB,IAAI;YAAAwD,QAAA,gBACHhC,OAAA,CAACvB,SAAS;cACR6E,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAEhD,KAAK,CAACgB,iBAAkB;cAC/BiC,MAAM,eAAExD,OAAA,CAACd,gBAAgB;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC7BoB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACFrC,OAAA;cAAK6B,KAAK,EAAE;gBAAES,SAAS,EAAE;cAAM,CAAE;cAAAN,QAAA,eAC/BhC,OAAA,CAACI,IAAI;gBAAC4C,IAAI,EAAC,WAAW;gBAAAhB,QAAA,GAAC,sBAChB,EAACzB,KAAK,CAACiB,kBAAkB;cAAA;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENrC,OAAA,CAACzB,GAAG;UAAC4E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACzBhC,OAAA,CAACxB,IAAI;YAAAwD,QAAA,eACHhC,OAAA,CAACvB,SAAS;cACR6E,KAAK,EAAC,sCAAQ;cACdC,KAAK,EAAEhD,KAAK,CAACkB,qBAAsB;cACnC+B,MAAM,eAAExD,OAAA,CAACb,mBAAmB;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChCoB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENrC,OAAA,CAACzB,GAAG;UAAC4E,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAArB,QAAA,eACzBhC,OAAA,CAACxB,IAAI;YAAAwD,QAAA,eACHhC,OAAA,CAACvB,SAAS;cACR6E,KAAK,EAAC,gCAAO;cACbC,KAAK,EAAEhD,KAAK,CAACmB,gBAAiB;cAC9B8B,MAAM,eAAExD,OAAA,CAACZ,YAAY;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBoB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA,CAAC1B,GAAG;QAAC4E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAlB,QAAA,gBACpBhC,OAAA,CAACzB,GAAG;UAAC4E,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClBhC,OAAA,CAACxB,IAAI;YAAC8E,KAAK,EAAC,gCAAO;YAACK,KAAK,eAAE3D,OAAA,CAACX,YAAY;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,gBAC1ChC,OAAA,CAACtB,QAAQ;cACPsE,IAAI,EAAC,QAAQ;cACbY,OAAO,EAAErD,KAAK,CAACoB,qBAAsB;cACrCM,IAAI,EAAE,GAAI;cACV4B,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE;cACV;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrC,OAAA;cAAK6B,KAAK,EAAE;gBAAEC,SAAS,EAAE,QAAQ;gBAAEQ,SAAS,EAAE;cAAO,CAAE;cAAAN,QAAA,eACrDhC,OAAA,CAACI,IAAI;gBAAC4C,IAAI,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAEvB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAENrC,OAAA,CAACzB,GAAG;UAAC4E,EAAE,EAAE,EAAG;UAACE,EAAE,EAAE,EAAG;UAAArB,QAAA,eAClBhC,OAAA,CAACxB,IAAI;YAAC8E,KAAK,EAAC,gCAAO;YAACK,KAAK,eAAE3D,OAAA,CAACV,cAAc;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAL,QAAA,gBAC5ChC,OAAA,CAACtB,QAAQ;cACPsE,IAAI,EAAC,QAAQ;cACbY,OAAO,EAAErD,KAAK,CAACqB,YAAa;cAC5BK,IAAI,EAAE,GAAI;cACV4B,WAAW,EAAE;gBACX,IAAI,EAAE,SAAS;gBACf,MAAM,EAAE;cACV;YAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACFrC,OAAA;cAAK6B,KAAK,EAAE;gBAAEC,SAAS,EAAE,QAAQ;gBAAEQ,SAAS,EAAE;cAAO,CAAE;cAAAN,QAAA,eACrDhC,OAAA,CAACI,IAAI;gBAAC4C,IAAI,EAAC,WAAW;gBAAAhB,QAAA,EAAC;cAEvB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA,CAACxB,IAAI;QACH8E,KAAK,EAAC,4CAAS;QACfzB,KAAK,EAAE;UAAES,SAAS,EAAE;QAAO,CAAE;QAAAN,QAAA,eAE7BhC,OAAA,CAAChB,IAAI;UACH8E,gBAAgB,EAAC,QAAQ;UACzBC,KAAK,EAAE,CACL;YACEC,GAAG,EAAE,QAAQ;YACbC,KAAK,eACHjE,OAAA;cAAAgC,QAAA,gBACEhC,OAAA,CAACR,gBAAgB;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAEtB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;YACDL,QAAQ,eACNhC,OAAA,CAAC1B,GAAG;cAAC4E,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;cAAAlB,QAAA,gBACpBhC,OAAA,CAACzB,GAAG;gBAAC4E,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,EAAG;gBAAArB,QAAA,eAClBhC,OAAA,CAACL,eAAe;kBAACuE,MAAM,EAAE;gBAAI;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC,eACNrC,OAAA,CAACzB,GAAG;gBAAC4E,EAAE,EAAE,EAAG;gBAACE,EAAE,EAAE,EAAG;gBAAArB,QAAA,eAClBhC,OAAA,CAACJ,YAAY;kBAACsE,MAAM,EAAE;gBAAI;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACNrC,OAAA,CAACzB,GAAG;gBAAC4E,EAAE,EAAE,EAAG;gBAAAnB,QAAA,eACVhC,OAAA,CAACH,cAAc;kBAACqE,MAAM,EAAE;gBAAI;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAET,CAAC,EACD;YACE2B,GAAG,EAAE,SAAS;YACdC,KAAK,eACHjE,OAAA;cAAAgC,QAAA,gBACEhC,OAAA,CAACP,eAAe;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,4BAErB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;YACDL,QAAQ,eAAEhC,OAAA,CAACF,eAAe;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAC9B,CAAC;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGPrC,OAAA,CAACxB,IAAI;QACH8E,KAAK,EAAC,0BAAM;QACZzB,KAAK,EAAE;UAAES,SAAS,EAAE;QAAO,CAAE;QAAAN,QAAA,eAE7BhC,OAAA,CAACpB,KAAK;UAACuF,IAAI;UAAAnC,QAAA,gBACThC,OAAA,CAACjB,MAAM;YAACiE,IAAI,EAAC,SAAS;YAAAhB,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrCrC,OAAA,CAACjB,MAAM;YAAAiD,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrBrC,OAAA,CAACjB,MAAM;YAAAiD,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrBrC,OAAA,CAACjB,MAAM;YAAAiD,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACrBrC,OAAA,CAACjB,MAAM;YAAAiD,QAAA,EAAC;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA,eACP,CACH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA/OID,SAAmB;AAAA+D,EAAA,GAAnB/D,SAAmB;AAiPzB,eAAeA,SAAS;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}