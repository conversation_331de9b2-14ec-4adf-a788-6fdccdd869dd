{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"../lodash\");\nmodule.exports = {\n  longestPath: longestPath,\n  slack: slack\n};\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n  function dfs(v) {\n    var label = g.node(v);\n    if (_.has(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n    var rank = _.min(_.map(g.outEdges(v), function (e) {\n      return dfs(e.w) - g.edge(e).minlen;\n    }));\n    if (rank === Number.POSITIVE_INFINITY ||\n    // return value of _.map([]) for Lodash 3\n    rank === undefined ||\n    // return value of _.map([]) for Lodash 4\n    rank === null) {\n      // return value of _.map([null])\n      rank = 0;\n    }\n    return label.rank = rank;\n  }\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "longestPath", "slack", "g", "visited", "dfs", "v", "label", "node", "has", "rank", "min", "map", "outEdges", "e", "w", "edge", "minlen", "Number", "POSITIVE_INFINITY", "undefined", "for<PERSON>ach", "sources"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/rank/util.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = {\n  longestPath: longestPath,\n  slack: slack\n};\n\n/*\n * Initializes ranks for the input graph using the longest path algorithm. This\n * algorithm scales well and is fast in practice, it yields rather poor\n * solutions. Nodes are pushed to the lowest layer possible, leaving the bottom\n * ranks wide and leaving edges longer than necessary. However, due to its\n * speed, this algorithm is good for getting an initial ranking that can be fed\n * into other algorithms.\n *\n * This algorithm does not normalize layers because it will be used by other\n * algorithms in most cases. If using this algorithm directly, be sure to\n * run normalize at the end.\n *\n * Pre-conditions:\n *\n *    1. Input graph is a DAG.\n *    2. Input graph node labels can be assigned properties.\n *\n * Post-conditions:\n *\n *    1. Each node will be assign an (unnormalized) \"rank\" property.\n */\nfunction longestPath(g) {\n  var visited = {};\n\n  function dfs(v) {\n    var label = g.node(v);\n    if (_.has(visited, v)) {\n      return label.rank;\n    }\n    visited[v] = true;\n\n    var rank = _.min(_.map(g.outEdges(v), function(e) {\n      return dfs(e.w) - g.edge(e).minlen;\n    }));\n\n    if (rank === Number.POSITIVE_INFINITY || // return value of _.map([]) for Lodash 3\n        rank === undefined || // return value of _.map([]) for Lodash 4\n        rank === null) { // return value of _.map([null])\n      rank = 0;\n    }\n\n    return (label.rank = rank);\n  }\n\n  _.forEach(g.sources(), dfs);\n}\n\n/*\n * Returns the amount of slack for the given edge. The slack is defined as the\n * difference between the length of the edge and its minimum length.\n */\nfunction slack(g, e) {\n  return g.node(e.w).rank - g.node(e.v).rank - g.edge(e).minlen;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAG;EACfC,WAAW,EAAEA,WAAW;EACxBC,KAAK,EAAEA;AACT,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,WAAWA,CAACE,CAAC,EAAE;EACtB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAEhB,SAASC,GAAGA,CAACC,CAAC,EAAE;IACd,IAAIC,KAAK,GAAGJ,CAAC,CAACK,IAAI,CAACF,CAAC,CAAC;IACrB,IAAIT,CAAC,CAACY,GAAG,CAACL,OAAO,EAAEE,CAAC,CAAC,EAAE;MACrB,OAAOC,KAAK,CAACG,IAAI;IACnB;IACAN,OAAO,CAACE,CAAC,CAAC,GAAG,IAAI;IAEjB,IAAII,IAAI,GAAGb,CAAC,CAACc,GAAG,CAACd,CAAC,CAACe,GAAG,CAACT,CAAC,CAACU,QAAQ,CAACP,CAAC,CAAC,EAAE,UAASQ,CAAC,EAAE;MAChD,OAAOT,GAAG,CAACS,CAAC,CAACC,CAAC,CAAC,GAAGZ,CAAC,CAACa,IAAI,CAACF,CAAC,CAAC,CAACG,MAAM;IACpC,CAAC,CAAC,CAAC;IAEH,IAAIP,IAAI,KAAKQ,MAAM,CAACC,iBAAiB;IAAI;IACrCT,IAAI,KAAKU,SAAS;IAAI;IACtBV,IAAI,KAAK,IAAI,EAAE;MAAE;MACnBA,IAAI,GAAG,CAAC;IACV;IAEA,OAAQH,KAAK,CAACG,IAAI,GAAGA,IAAI;EAC3B;EAEAb,CAAC,CAACwB,OAAO,CAAClB,CAAC,CAACmB,OAAO,CAAC,CAAC,EAAEjB,GAAG,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA,SAASH,KAAKA,CAACC,CAAC,EAAEW,CAAC,EAAE;EACnB,OAAOX,CAAC,CAACK,IAAI,CAACM,CAAC,CAACC,CAAC,CAAC,CAACL,IAAI,GAAGP,CAAC,CAACK,IAAI,CAACM,CAAC,CAACR,CAAC,CAAC,CAACI,IAAI,GAAGP,CAAC,CAACa,IAAI,CAACF,CAAC,CAAC,CAACG,MAAM;AAC/D", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}