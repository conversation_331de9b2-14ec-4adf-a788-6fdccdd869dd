{"ast": null, "code": "import * as d3Hierarchy from \"@antv/vendor/d3-hierarchy\";\nimport { assign, isArray, reduce, size, filter, isString } from '@antv/util';\nconst DEFAULT_OPTIONS = {\n  field: 'value',\n  size: [1, 1],\n  // width, height\n  round: false,\n  padding: 0,\n  // Default desc.\n  sort: (a, b) => b.value - a.value,\n  as: ['x', 'y'],\n  // Whether to ignore parentValue. When set to true, the weight of the parent node is determined by the child element.\n  ignoreParentValue: true\n};\n// In the same level, the nodes under the same parent node index order.\nexport const NODE_INDEX_FIELD = 'nodeIndex';\n// Number of child nodes\nexport const CHILD_NODE_COUNT = 'childNodeCount';\n// Ancestor of the node\nexport const NODE_ANCESTORS_FIELD = 'nodeAncestor';\nconst INVALID_FIELD_ERR_MSG = 'Invalid field: it must be a string!';\nexport function getField(options, defaultField) {\n  const {\n    field,\n    fields\n  } = options;\n  if (isString(field)) {\n    return field;\n  }\n  if (isArray(field)) {\n    console.warn(INVALID_FIELD_ERR_MSG);\n    return field[0];\n  }\n  console.warn(`${INVALID_FIELD_ERR_MSG} will try to get fields instead.`);\n  if (isString(fields)) {\n    return fields;\n  }\n  if (isArray(fields) && fields.length) {\n    return fields[0];\n  }\n  if (defaultField) {\n    return defaultField;\n  }\n  throw new TypeError(INVALID_FIELD_ERR_MSG);\n}\nexport function getAllNodes(root) {\n  const nodes = [];\n  if (root && root.each) {\n    let parent;\n    let index;\n    // d3-hierarchy: Invokes the specified function for node and each descendant in **breadth-first order**\n    root.each(node => {\n      var _a, _b;\n      if (node.parent !== parent) {\n        parent = node.parent;\n        index = 0;\n      } else {\n        index += 1;\n      }\n      const ancestors = filter((((_a = node.ancestors) === null || _a === void 0 ? void 0 : _a.call(node)) || []).map(d => nodes.find(n => n.name === d.name) || d), ({\n        depth\n      }) => depth > 0 && depth < node.depth);\n      node[NODE_ANCESTORS_FIELD] = ancestors;\n      node[CHILD_NODE_COUNT] = ((_b = node.children) === null || _b === void 0 ? void 0 : _b.length) || 0;\n      node[NODE_INDEX_FIELD] = index;\n      nodes.push(node);\n    });\n  } else if (root && root.eachNode) {\n    // @antv/hierarchy\n    root.eachNode(node => {\n      nodes.push(node);\n    });\n  }\n  return nodes;\n}\nexport function partition(data, options) {\n  options = assign({}, DEFAULT_OPTIONS, options);\n  const as = options.as;\n  if (!isArray(as) || as.length !== 2) {\n    throw new TypeError('Invalid as: it must be an array with 2 strings (e.g. [ \"x\", \"y\" ])!');\n  }\n  let field;\n  try {\n    field = getField(options);\n  } catch (e) {\n    console.warn(e);\n  }\n  const partition = data => d3Hierarchy.partition().size(options.size).round(options.round).padding(options.padding)(\n  /**\n   * The sum function must be specified in the d3Hierarchy layout to compute node values by calling the specified value function\n   * from the current node in post-order traversal order for the current node and for each descendant node and returning the current node.\n   * for example:\n   * { node: 'parent', value: 10, children: [{node: 'child1', value: 5}, {node: 'child2', value: 5}, ]}.\n   * The computed value obtained by the parent is sum(node(parent)) + sum(node(child1)) + sum(node(child2)).\n   * In the sum function, d is the data passed in by the user and children is the reserved field.\n   */\n  d3Hierarchy.hierarchy(data).sum(d => size(d.children) ? options.ignoreParentValue ? 0 : d[field] - reduce(d.children, (a, b) => a + b[field], 0) : d[field]).sort(options.sort));\n  const root = partition(data);\n  /*\n   * points:\n   *   3  2\n   *   0  1\n   */\n  const x = as[0];\n  const y = as[1];\n  root.each(node => {\n    var _a, _b;\n    node[x] = [node.x0, node.x1, node.x1, node.x0];\n    node[y] = [node.y1, node.y1, node.y0, node.y0];\n    node.name = node.name || ((_a = node.data) === null || _a === void 0 ? void 0 : _a.name) || ((_b = node.data) === null || _b === void 0 ? void 0 : _b.label);\n    node.data.name = node.name;\n    ['x0', 'x1', 'y0', 'y1'].forEach(prop => {\n      if (as.indexOf(prop) === -1) {\n        delete node[prop];\n      }\n    });\n  });\n  return getAllNodes(root);\n}", "map": {"version": 3, "names": ["d3Hierarchy", "assign", "isArray", "reduce", "size", "filter", "isString", "DEFAULT_OPTIONS", "field", "round", "padding", "sort", "a", "b", "value", "as", "ignoreParentValue", "NODE_INDEX_FIELD", "CHILD_NODE_COUNT", "NODE_ANCESTORS_FIELD", "INVALID_FIELD_ERR_MSG", "getField", "options", "defaultField", "fields", "console", "warn", "length", "TypeError", "getAllNodes", "root", "nodes", "each", "parent", "index", "node", "ancestors", "_a", "call", "map", "d", "find", "n", "name", "depth", "_b", "children", "push", "eachNode", "partition", "data", "e", "hierarchy", "sum", "x", "y", "x0", "x1", "y1", "y0", "label", "for<PERSON>ach", "prop", "indexOf"], "sources": ["utils/hierarchy/partition.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,KAAKA,WAAW,MAAM,2BAA2B;AACxD,SAASC,MAAM,EAAEC,OAAO,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,YAAY;AAE5E,MAAMC,eAAe,GAAY;EAC/BC,KAAK,EAAE,OAAO;EACdJ,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAAE;EACdK,KAAK,EAAE,KAAK;EACZC,OAAO,EAAE,CAAC;EACV;EACAC,IAAI,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,KAAK,GAAGF,CAAC,CAACE,KAAK;EACjCC,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EACd;EACAC,iBAAiB,EAAE;CACpB;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG,WAAW;AAC3C;AACA,OAAO,MAAMC,gBAAgB,GAAG,gBAAgB;AAChD;AACA,OAAO,MAAMC,oBAAoB,GAAG,cAAc;AAElD,MAAMC,qBAAqB,GAAG,qCAAqC;AAEnE,OAAM,SAAUC,QAAQA,CACtBC,OAGC,EACDC,YAAqB;EAErB,MAAM;IAAEf,KAAK;IAAEgB;EAAM,CAAE,GAAGF,OAAO;EACjC,IAAIhB,QAAQ,CAACE,KAAK,CAAC,EAAE;IACnB,OAAOA,KAAK;EACd;EACA,IAAIN,OAAO,CAACM,KAAK,CAAC,EAAE;IAClBiB,OAAO,CAACC,IAAI,CAACN,qBAAqB,CAAC;IACnC,OAAOZ,KAAK,CAAC,CAAC,CAAC;EACjB;EACAiB,OAAO,CAACC,IAAI,CAAC,GAAGN,qBAAqB,kCAAkC,CAAC;EACxE,IAAId,QAAQ,CAACkB,MAAM,CAAC,EAAE;IACpB,OAAOA,MAAM;EACf;EACA,IAAItB,OAAO,CAACsB,MAAM,CAAC,IAAIA,MAAM,CAACG,MAAM,EAAE;IACpC,OAAOH,MAAM,CAAC,CAAC,CAAC;EAClB;EACA,IAAID,YAAY,EAAE;IAChB,OAAOA,YAAY;EACrB;EACA,MAAM,IAAIK,SAAS,CAACR,qBAAqB,CAAC;AAC5C;AAEA,OAAM,SAAUS,WAAWA,CAACC,IAAS;EACnC,MAAMC,KAAK,GAAU,EAAE;EACvB,IAAID,IAAI,IAAIA,IAAI,CAACE,IAAI,EAAE;IACrB,IAAIC,MAAM;IACV,IAAIC,KAAK;IACT;IACAJ,IAAI,CAACE,IAAI,CAAEG,IAAS,IAAI;;MACtB,IAAIA,IAAI,CAACF,MAAM,KAAKA,MAAM,EAAE;QAC1BA,MAAM,GAAGE,IAAI,CAACF,MAAM;QACpBC,KAAK,GAAG,CAAC;MACX,CAAC,MAAM;QACLA,KAAK,IAAI,CAAC;MACZ;MACA,MAAME,SAAS,GAAG/B,MAAM,CACtB,CAAC,EAAAgC,EAAA,GAAAF,IAAI,CAACC,SAAS,cAAAC,EAAA,uBAAAA,EAAA,CAAAC,IAAA,CAAAH,IAAA,CAAI,KAAI,EAAE,EAAEI,GAAG,CAC3BC,CAAM,IAAKT,KAAK,CAACU,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,IAAI,KAAKH,CAAC,CAACG,IAAI,CAAC,IAAIH,CAAC,CACtD,EACD,CAAC;QAAEI;MAAK,CAAE,KAAKA,KAAK,GAAG,CAAC,IAAIA,KAAK,GAAGT,IAAI,CAACS,KAAK,CAC/C;MAEDT,IAAI,CAAChB,oBAAoB,CAAC,GAAGiB,SAAS;MACtCD,IAAI,CAACjB,gBAAgB,CAAC,GAAG,EAAA2B,EAAA,GAAAV,IAAI,CAACW,QAAQ,cAAAD,EAAA,uBAAAA,EAAA,CAAElB,MAAM,KAAI,CAAC;MACnDQ,IAAI,CAAClB,gBAAgB,CAAC,GAAGiB,KAAK;MAE9BH,KAAK,CAACgB,IAAI,CAACZ,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIL,IAAI,IAAIA,IAAI,CAACkB,QAAQ,EAAE;IAChC;IACAlB,IAAI,CAACkB,QAAQ,CAAEb,IAAS,IAAI;MAC1BJ,KAAK,CAACgB,IAAI,CAACZ,IAAI,CAAC;IAClB,CAAC,CAAC;EACJ;EACA,OAAOJ,KAAK;AACd;AAaA,OAAM,SAAUkB,SAASA,CAACC,IAAS,EAAE5B,OAAgB;EACnDA,OAAO,GAAGrB,MAAM,CAAC,EAAa,EAAEM,eAAe,EAAEe,OAAO,CAAC;EACzD,MAAMP,EAAE,GAAGO,OAAO,CAACP,EAAE;EACrB,IAAI,CAACb,OAAO,CAACa,EAAE,CAAC,IAAIA,EAAE,CAACY,MAAM,KAAK,CAAC,EAAE;IACnC,MAAM,IAAIC,SAAS,CACjB,qEAAqE,CACtE;EACH;EAEA,IAAIpB,KAAK;EACT,IAAI;IACFA,KAAK,GAAGa,QAAQ,CAACC,OAAO,CAAC;EAC3B,CAAC,CAAC,OAAO6B,CAAC,EAAE;IACV1B,OAAO,CAACC,IAAI,CAACyB,CAAC,CAAC;EACjB;EAEA,MAAMF,SAAS,GAAIC,IAAI,IACrBlD,WAAW,CACRiD,SAAS,EAAE,CACX7C,IAAI,CAACkB,OAAO,CAAClB,IAAI,CAAC,CAClBK,KAAK,CAACa,OAAO,CAACb,KAAK,CAAC,CACpBC,OAAO,CAACY,OAAO,CAACZ,OAAO,CAAC;EACzB;;;;;;;;EAQAV,WAAW,CACRoD,SAAS,CAACF,IAAI,CAAC,CACfG,GAAG,CAAEb,CAAC,IACLpC,IAAI,CAACoC,CAAC,CAACM,QAAQ,CAAC,GACZxB,OAAO,CAACN,iBAAiB,GACvB,CAAC,GACDwB,CAAC,CAAChC,KAAK,CAAC,GAAGL,MAAM,CAACqC,CAAC,CAACM,QAAQ,EAAE,CAAClC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAACL,KAAK,CAAC,EAAE,CAAC,CAAC,GAC1DgC,CAAC,CAAChC,KAAK,CAAC,CACb,CACAG,IAAI,CAACW,OAAO,CAACX,IAAI,CAAC,CACtB;EACH,MAAMmB,IAAI,GAAGmB,SAAS,CAACC,IAAI,CAAC;EAE5B;;;;;EAKA,MAAMI,CAAC,GAAGvC,EAAE,CAAC,CAAC,CAAC;EACf,MAAMwC,CAAC,GAAGxC,EAAE,CAAC,CAAC,CAAC;EAEfe,IAAI,CAACE,IAAI,CAAEG,IAAS,IAAI;;IACtBA,IAAI,CAACmB,CAAC,CAAC,GAAG,CAACnB,IAAI,CAACqB,EAAE,EAAErB,IAAI,CAACsB,EAAE,EAAEtB,IAAI,CAACsB,EAAE,EAAEtB,IAAI,CAACqB,EAAE,CAAC;IAC9CrB,IAAI,CAACoB,CAAC,CAAC,GAAG,CAACpB,IAAI,CAACuB,EAAE,EAAEvB,IAAI,CAACuB,EAAE,EAAEvB,IAAI,CAACwB,EAAE,EAAExB,IAAI,CAACwB,EAAE,CAAC;IAC9CxB,IAAI,CAACQ,IAAI,GAAGR,IAAI,CAACQ,IAAI,KAAI,CAAAN,EAAA,GAAAF,IAAI,CAACe,IAAI,cAAAb,EAAA,uBAAAA,EAAA,CAAEM,IAAI,MAAI,CAAAE,EAAA,GAAAV,IAAI,CAACe,IAAI,cAAAL,EAAA,uBAAAA,EAAA,CAAEe,KAAK;IAC5DzB,IAAI,CAACe,IAAI,CAACP,IAAI,GAAGR,IAAI,CAACQ,IAAI;IAE1B,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAACkB,OAAO,CAAEC,IAAI,IAAI;MACxC,IAAI/C,EAAE,CAACgD,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;QAC3B,OAAO3B,IAAI,CAAC2B,IAAI,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOjC,WAAW,CAACC,IAAI,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}