{"ast": null, "code": "import { weightedSum } from './blas1';\n/** minimizes a function using the downhill simplex method */\nexport function nelderMead(f, x0, parameters) {\n  parameters = parameters || {};\n  const maxIterations = parameters.maxIterations || x0.length * 200;\n  const nonZeroDelta = parameters.nonZeroDelta || 1.05;\n  const zeroDelta = parameters.zeroDelta || 0.001;\n  const minErrorDelta = parameters.minErrorDelta || 1e-6;\n  const minTolerance = parameters.minErrorDelta || 1e-5;\n  const rho = parameters.rho !== undefined ? parameters.rho : 1;\n  const chi = parameters.chi !== undefined ? parameters.chi : 2;\n  const psi = parameters.psi !== undefined ? parameters.psi : -0.5;\n  const sigma = parameters.sigma !== undefined ? parameters.sigma : 0.5;\n  let maxDiff;\n  // initialize simplex.\n  const N = x0.length;\n  const simplex = new Array(N + 1);\n  simplex[0] = x0;\n  simplex[0].fx = f(x0);\n  simplex[0].id = 0;\n  for (let i = 0; i < N; ++i) {\n    const point = x0.slice();\n    point[i] = point[i] ? point[i] * nonZeroDelta : zeroDelta;\n    simplex[i + 1] = point;\n    simplex[i + 1].fx = f(point);\n    simplex[i + 1].id = i + 1;\n  }\n  function updateSimplex(value) {\n    for (let i = 0; i < value.length; i++) {\n      simplex[N][i] = value[i];\n    }\n    simplex[N].fx = value.fx;\n  }\n  const sortOrder = (a, b) => a.fx - b.fx;\n  const centroid = x0.slice();\n  const reflected = x0.slice();\n  const contracted = x0.slice();\n  const expanded = x0.slice();\n  for (let iteration = 0; iteration < maxIterations; ++iteration) {\n    simplex.sort(sortOrder);\n    if (parameters.history) {\n      // copy the simplex (since later iterations will mutate) and\n      // sort it to have a consistent order between iterations\n      const sortedSimplex = simplex.map(x => {\n        const state = x.slice();\n        state.fx = x.fx;\n        state.id = x.id;\n        return state;\n      });\n      sortedSimplex.sort((a, b) => a.id - b.id);\n      parameters.history.push({\n        x: simplex[0].slice(),\n        fx: simplex[0].fx,\n        simplex: sortedSimplex\n      });\n    }\n    maxDiff = 0;\n    for (let i = 0; i < N; ++i) {\n      maxDiff = Math.max(maxDiff, Math.abs(simplex[0][i] - simplex[1][i]));\n    }\n    if (Math.abs(simplex[0].fx - simplex[N].fx) < minErrorDelta && maxDiff < minTolerance) {\n      break;\n    }\n    // compute the centroid of all but the worst point in the simplex\n    for (let i = 0; i < N; ++i) {\n      centroid[i] = 0;\n      for (let j = 0; j < N; ++j) {\n        centroid[i] += simplex[j][i];\n      }\n      centroid[i] /= N;\n    }\n    // reflect the worst point past the centroid  and compute loss at reflected\n    // point\n    const worst = simplex[N];\n    weightedSum(reflected, 1 + rho, centroid, -rho, worst);\n    reflected.fx = f(reflected);\n    // if the reflected point is the best seen, then possibly expand\n    if (reflected.fx < simplex[0].fx) {\n      weightedSum(expanded, 1 + chi, centroid, -chi, worst);\n      expanded.fx = f(expanded);\n      if (expanded.fx < reflected.fx) {\n        updateSimplex(expanded);\n      } else {\n        updateSimplex(reflected);\n      }\n    }\n    // if the reflected point is worse than the second worst, we need to\n    // contract\n    else if (reflected.fx >= simplex[N - 1].fx) {\n      let shouldReduce = false;\n      if (reflected.fx > worst.fx) {\n        // do an inside contraction\n        weightedSum(contracted, 1 + psi, centroid, -psi, worst);\n        contracted.fx = f(contracted);\n        if (contracted.fx < worst.fx) {\n          updateSimplex(contracted);\n        } else {\n          shouldReduce = true;\n        }\n      } else {\n        // do an outside contraction\n        weightedSum(contracted, 1 - psi * rho, centroid, psi * rho, worst);\n        contracted.fx = f(contracted);\n        if (contracted.fx < reflected.fx) {\n          updateSimplex(contracted);\n        } else {\n          shouldReduce = true;\n        }\n      }\n      if (shouldReduce) {\n        // if we don't contract here, we're done\n        if (sigma >= 1) break;\n        // do a reduction\n        for (let i = 1; i < simplex.length; ++i) {\n          weightedSum(simplex[i], 1 - sigma, simplex[0], sigma, simplex[i]);\n          simplex[i].fx = f(simplex[i]);\n        }\n      }\n    } else {\n      updateSimplex(reflected);\n    }\n  }\n  simplex.sort(sortOrder);\n  return {\n    fx: simplex[0].fx,\n    x: simplex[0]\n  };\n}", "map": {"version": 3, "names": ["weightedSum", "nelderMead", "f", "x0", "parameters", "maxIterations", "length", "nonZeroDelta", "zeroDelta", "minE<PERSON><PERSON><PERSON><PERSON><PERSON>", "minTolerance", "rho", "undefined", "chi", "psi", "sigma", "maxDiff", "N", "simplex", "Array", "fx", "id", "i", "point", "slice", "updateSimplex", "value", "sortOrder", "a", "b", "centroid", "reflected", "contracted", "expanded", "iteration", "sort", "history", "sortedSimplex", "map", "x", "state", "push", "Math", "max", "abs", "j", "worst", "shouldReduce"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/venn/fmin/nelderMead.ts"], "sourcesContent": ["import { dot, norm2, weightedSum } from './blas1';\n\n/** minimizes a function using the downhill simplex method */\nexport function nelderMead(f, x0, parameters?: any) {\n  parameters = parameters || {};\n\n  const maxIterations = parameters.maxIterations || x0.length * 200;\n  const nonZeroDelta = parameters.nonZeroDelta || 1.05;\n  const zeroDelta = parameters.zeroDelta || 0.001;\n  const minErrorDelta = parameters.minErrorDelta || 1e-6;\n  const minTolerance = parameters.minErrorDelta || 1e-5;\n  const rho = parameters.rho !== undefined ? parameters.rho : 1;\n  const chi = parameters.chi !== undefined ? parameters.chi : 2;\n  const psi = parameters.psi !== undefined ? parameters.psi : -0.5;\n  const sigma = parameters.sigma !== undefined ? parameters.sigma : 0.5;\n  let maxDiff;\n\n  // initialize simplex.\n  const N = x0.length;\n  const simplex = new Array(N + 1);\n  simplex[0] = x0;\n  simplex[0].fx = f(x0);\n  simplex[0].id = 0;\n  for (let i = 0; i < N; ++i) {\n    const point = x0.slice();\n    point[i] = point[i] ? point[i] * nonZeroDelta : zeroDelta;\n    simplex[i + 1] = point;\n    simplex[i + 1].fx = f(point);\n    simplex[i + 1].id = i + 1;\n  }\n\n  function updateSimplex(value) {\n    for (let i = 0; i < value.length; i++) {\n      simplex[N][i] = value[i];\n    }\n    simplex[N].fx = value.fx;\n  }\n\n  const sortOrder = (a, b) => a.fx - b.fx;\n\n  const centroid = x0.slice();\n  const reflected = x0.slice();\n  const contracted = x0.slice();\n  const expanded = x0.slice();\n\n  for (let iteration = 0; iteration < maxIterations; ++iteration) {\n    simplex.sort(sortOrder);\n\n    if (parameters.history) {\n      // copy the simplex (since later iterations will mutate) and\n      // sort it to have a consistent order between iterations\n      const sortedSimplex = simplex.map((x) => {\n        const state = x.slice();\n        state.fx = x.fx;\n        state.id = x.id;\n        return state;\n      });\n      sortedSimplex.sort((a, b) => a.id - b.id);\n\n      parameters.history.push({\n        x: simplex[0].slice(),\n        fx: simplex[0].fx,\n        simplex: sortedSimplex,\n      });\n    }\n\n    maxDiff = 0;\n    for (let i = 0; i < N; ++i) {\n      maxDiff = Math.max(maxDiff, Math.abs(simplex[0][i] - simplex[1][i]));\n    }\n\n    if (\n      Math.abs(simplex[0].fx - simplex[N].fx) < minErrorDelta &&\n      maxDiff < minTolerance\n    ) {\n      break;\n    }\n\n    // compute the centroid of all but the worst point in the simplex\n    for (let i = 0; i < N; ++i) {\n      centroid[i] = 0;\n      for (let j = 0; j < N; ++j) {\n        centroid[i] += simplex[j][i];\n      }\n      centroid[i] /= N;\n    }\n\n    // reflect the worst point past the centroid  and compute loss at reflected\n    // point\n    const worst = simplex[N];\n    weightedSum(reflected, 1 + rho, centroid, -rho, worst);\n    reflected.fx = f(reflected);\n\n    // if the reflected point is the best seen, then possibly expand\n    if (reflected.fx < simplex[0].fx) {\n      weightedSum(expanded, 1 + chi, centroid, -chi, worst);\n      expanded.fx = f(expanded);\n      if (expanded.fx < reflected.fx) {\n        updateSimplex(expanded);\n      } else {\n        updateSimplex(reflected);\n      }\n    }\n\n    // if the reflected point is worse than the second worst, we need to\n    // contract\n    else if (reflected.fx >= simplex[N - 1].fx) {\n      let shouldReduce = false;\n\n      if (reflected.fx > worst.fx) {\n        // do an inside contraction\n        weightedSum(contracted, 1 + psi, centroid, -psi, worst);\n        contracted.fx = f(contracted);\n        if (contracted.fx < worst.fx) {\n          updateSimplex(contracted);\n        } else {\n          shouldReduce = true;\n        }\n      } else {\n        // do an outside contraction\n        weightedSum(contracted, 1 - psi * rho, centroid, psi * rho, worst);\n        contracted.fx = f(contracted);\n        if (contracted.fx < reflected.fx) {\n          updateSimplex(contracted);\n        } else {\n          shouldReduce = true;\n        }\n      }\n\n      if (shouldReduce) {\n        // if we don't contract here, we're done\n        if (sigma >= 1) break;\n\n        // do a reduction\n        for (let i = 1; i < simplex.length; ++i) {\n          weightedSum(simplex[i], 1 - sigma, simplex[0], sigma, simplex[i]);\n          simplex[i].fx = f(simplex[i]);\n        }\n      }\n    } else {\n      updateSimplex(reflected);\n    }\n  }\n\n  simplex.sort(sortOrder);\n  return { fx: simplex[0].fx, x: simplex[0] };\n}\n"], "mappings": "AAAA,SAAqBA,WAAW,QAAQ,SAAS;AAEjD;AACA,OAAM,SAAUC,UAAUA,CAACC,CAAC,EAAEC,EAAE,EAAEC,UAAgB;EAChDA,UAAU,GAAGA,UAAU,IAAI,EAAE;EAE7B,MAAMC,aAAa,GAAGD,UAAU,CAACC,aAAa,IAAIF,EAAE,CAACG,MAAM,GAAG,GAAG;EACjE,MAAMC,YAAY,GAAGH,UAAU,CAACG,YAAY,IAAI,IAAI;EACpD,MAAMC,SAAS,GAAGJ,UAAU,CAACI,SAAS,IAAI,KAAK;EAC/C,MAAMC,aAAa,GAAGL,UAAU,CAACK,aAAa,IAAI,IAAI;EACtD,MAAMC,YAAY,GAAGN,UAAU,CAACK,aAAa,IAAI,IAAI;EACrD,MAAME,GAAG,GAAGP,UAAU,CAACO,GAAG,KAAKC,SAAS,GAAGR,UAAU,CAACO,GAAG,GAAG,CAAC;EAC7D,MAAME,GAAG,GAAGT,UAAU,CAACS,GAAG,KAAKD,SAAS,GAAGR,UAAU,CAACS,GAAG,GAAG,CAAC;EAC7D,MAAMC,GAAG,GAAGV,UAAU,CAACU,GAAG,KAAKF,SAAS,GAAGR,UAAU,CAACU,GAAG,GAAG,CAAC,GAAG;EAChE,MAAMC,KAAK,GAAGX,UAAU,CAACW,KAAK,KAAKH,SAAS,GAAGR,UAAU,CAACW,KAAK,GAAG,GAAG;EACrE,IAAIC,OAAO;EAEX;EACA,MAAMC,CAAC,GAAGd,EAAE,CAACG,MAAM;EACnB,MAAMY,OAAO,GAAG,IAAIC,KAAK,CAACF,CAAC,GAAG,CAAC,CAAC;EAChCC,OAAO,CAAC,CAAC,CAAC,GAAGf,EAAE;EACfe,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE,GAAGlB,CAAC,CAACC,EAAE,CAAC;EACrBe,OAAO,CAAC,CAAC,CAAC,CAACG,EAAE,GAAG,CAAC;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAE;IAC1B,MAAMC,KAAK,GAAGpB,EAAE,CAACqB,KAAK,EAAE;IACxBD,KAAK,CAACD,CAAC,CAAC,GAAGC,KAAK,CAACD,CAAC,CAAC,GAAGC,KAAK,CAACD,CAAC,CAAC,GAAGf,YAAY,GAAGC,SAAS;IACzDU,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC,GAAGC,KAAK;IACtBL,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC,CAACF,EAAE,GAAGlB,CAAC,CAACqB,KAAK,CAAC;IAC5BL,OAAO,CAACI,CAAC,GAAG,CAAC,CAAC,CAACD,EAAE,GAAGC,CAAC,GAAG,CAAC;;EAG3B,SAASG,aAAaA,CAACC,KAAK;IAC1B,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,KAAK,CAACpB,MAAM,EAAEgB,CAAC,EAAE,EAAE;MACrCJ,OAAO,CAACD,CAAC,CAAC,CAACK,CAAC,CAAC,GAAGI,KAAK,CAACJ,CAAC,CAAC;;IAE1BJ,OAAO,CAACD,CAAC,CAAC,CAACG,EAAE,GAAGM,KAAK,CAACN,EAAE;EAC1B;EAEA,MAAMO,SAAS,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACR,EAAE,GAAGS,CAAC,CAACT,EAAE;EAEvC,MAAMU,QAAQ,GAAG3B,EAAE,CAACqB,KAAK,EAAE;EAC3B,MAAMO,SAAS,GAAG5B,EAAE,CAACqB,KAAK,EAAE;EAC5B,MAAMQ,UAAU,GAAG7B,EAAE,CAACqB,KAAK,EAAE;EAC7B,MAAMS,QAAQ,GAAG9B,EAAE,CAACqB,KAAK,EAAE;EAE3B,KAAK,IAAIU,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG7B,aAAa,EAAE,EAAE6B,SAAS,EAAE;IAC9DhB,OAAO,CAACiB,IAAI,CAACR,SAAS,CAAC;IAEvB,IAAIvB,UAAU,CAACgC,OAAO,EAAE;MACtB;MACA;MACA,MAAMC,aAAa,GAAGnB,OAAO,CAACoB,GAAG,CAAEC,CAAC,IAAI;QACtC,MAAMC,KAAK,GAAGD,CAAC,CAACf,KAAK,EAAE;QACvBgB,KAAK,CAACpB,EAAE,GAAGmB,CAAC,CAACnB,EAAE;QACfoB,KAAK,CAACnB,EAAE,GAAGkB,CAAC,CAAClB,EAAE;QACf,OAAOmB,KAAK;MACd,CAAC,CAAC;MACFH,aAAa,CAACF,IAAI,CAAC,CAACP,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACP,EAAE,GAAGQ,CAAC,CAACR,EAAE,CAAC;MAEzCjB,UAAU,CAACgC,OAAO,CAACK,IAAI,CAAC;QACtBF,CAAC,EAAErB,OAAO,CAAC,CAAC,CAAC,CAACM,KAAK,EAAE;QACrBJ,EAAE,EAAEF,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;QACjBF,OAAO,EAAEmB;OACV,CAAC;;IAGJrB,OAAO,GAAG,CAAC;IACX,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAE;MAC1BN,OAAO,GAAG0B,IAAI,CAACC,GAAG,CAAC3B,OAAO,EAAE0B,IAAI,CAACE,GAAG,CAAC1B,OAAO,CAAC,CAAC,CAAC,CAACI,CAAC,CAAC,GAAGJ,OAAO,CAAC,CAAC,CAAC,CAACI,CAAC,CAAC,CAAC,CAAC;;IAGtE,IACEoB,IAAI,CAACE,GAAG,CAAC1B,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE,GAAGF,OAAO,CAACD,CAAC,CAAC,CAACG,EAAE,CAAC,GAAGX,aAAa,IACvDO,OAAO,GAAGN,YAAY,EACtB;MACA;;IAGF;IACA,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAE;MAC1BQ,QAAQ,CAACR,CAAC,CAAC,GAAG,CAAC;MACf,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5B,CAAC,EAAE,EAAE4B,CAAC,EAAE;QAC1Bf,QAAQ,CAACR,CAAC,CAAC,IAAIJ,OAAO,CAAC2B,CAAC,CAAC,CAACvB,CAAC,CAAC;;MAE9BQ,QAAQ,CAACR,CAAC,CAAC,IAAIL,CAAC;;IAGlB;IACA;IACA,MAAM6B,KAAK,GAAG5B,OAAO,CAACD,CAAC,CAAC;IACxBjB,WAAW,CAAC+B,SAAS,EAAE,CAAC,GAAGpB,GAAG,EAAEmB,QAAQ,EAAE,CAACnB,GAAG,EAAEmC,KAAK,CAAC;IACtDf,SAAS,CAACX,EAAE,GAAGlB,CAAC,CAAC6B,SAAS,CAAC;IAE3B;IACA,IAAIA,SAAS,CAACX,EAAE,GAAGF,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE,EAAE;MAChCpB,WAAW,CAACiC,QAAQ,EAAE,CAAC,GAAGpB,GAAG,EAAEiB,QAAQ,EAAE,CAACjB,GAAG,EAAEiC,KAAK,CAAC;MACrDb,QAAQ,CAACb,EAAE,GAAGlB,CAAC,CAAC+B,QAAQ,CAAC;MACzB,IAAIA,QAAQ,CAACb,EAAE,GAAGW,SAAS,CAACX,EAAE,EAAE;QAC9BK,aAAa,CAACQ,QAAQ,CAAC;OACxB,MAAM;QACLR,aAAa,CAACM,SAAS,CAAC;;;IAI5B;IACA;IAAA,KACK,IAAIA,SAAS,CAACX,EAAE,IAAIF,OAAO,CAACD,CAAC,GAAG,CAAC,CAAC,CAACG,EAAE,EAAE;MAC1C,IAAI2B,YAAY,GAAG,KAAK;MAExB,IAAIhB,SAAS,CAACX,EAAE,GAAG0B,KAAK,CAAC1B,EAAE,EAAE;QAC3B;QACApB,WAAW,CAACgC,UAAU,EAAE,CAAC,GAAGlB,GAAG,EAAEgB,QAAQ,EAAE,CAAChB,GAAG,EAAEgC,KAAK,CAAC;QACvDd,UAAU,CAACZ,EAAE,GAAGlB,CAAC,CAAC8B,UAAU,CAAC;QAC7B,IAAIA,UAAU,CAACZ,EAAE,GAAG0B,KAAK,CAAC1B,EAAE,EAAE;UAC5BK,aAAa,CAACO,UAAU,CAAC;SAC1B,MAAM;UACLe,YAAY,GAAG,IAAI;;OAEtB,MAAM;QACL;QACA/C,WAAW,CAACgC,UAAU,EAAE,CAAC,GAAGlB,GAAG,GAAGH,GAAG,EAAEmB,QAAQ,EAAEhB,GAAG,GAAGH,GAAG,EAAEmC,KAAK,CAAC;QAClEd,UAAU,CAACZ,EAAE,GAAGlB,CAAC,CAAC8B,UAAU,CAAC;QAC7B,IAAIA,UAAU,CAACZ,EAAE,GAAGW,SAAS,CAACX,EAAE,EAAE;UAChCK,aAAa,CAACO,UAAU,CAAC;SAC1B,MAAM;UACLe,YAAY,GAAG,IAAI;;;MAIvB,IAAIA,YAAY,EAAE;QAChB;QACA,IAAIhC,KAAK,IAAI,CAAC,EAAE;QAEhB;QACA,KAAK,IAAIO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACZ,MAAM,EAAE,EAAEgB,CAAC,EAAE;UACvCtB,WAAW,CAACkB,OAAO,CAACI,CAAC,CAAC,EAAE,CAAC,GAAGP,KAAK,EAAEG,OAAO,CAAC,CAAC,CAAC,EAAEH,KAAK,EAAEG,OAAO,CAACI,CAAC,CAAC,CAAC;UACjEJ,OAAO,CAACI,CAAC,CAAC,CAACF,EAAE,GAAGlB,CAAC,CAACgB,OAAO,CAACI,CAAC,CAAC,CAAC;;;KAGlC,MAAM;MACLG,aAAa,CAACM,SAAS,CAAC;;;EAI5Bb,OAAO,CAACiB,IAAI,CAACR,SAAS,CAAC;EACvB,OAAO;IAAEP,EAAE,EAAEF,OAAO,CAAC,CAAC,CAAC,CAACE,EAAE;IAAEmB,CAAC,EAAErB,OAAO,CAAC,CAAC;EAAC,CAAE;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}