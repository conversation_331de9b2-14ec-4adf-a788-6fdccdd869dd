{"ast": null, "code": "var _ = require(\"../lodash\");\nvar tarjan = require(\"./tarjan\");\nmodule.exports = findCycles;\nfunction findCycles(g) {\n  return _.filter(tarjan(g), function (cmpt) {\n    return cmpt.length > 1 || cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]);\n  });\n}", "map": {"version": 3, "names": ["_", "require", "tarjan", "module", "exports", "findCycles", "g", "filter", "cmpt", "length", "hasEdge"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/find-cycles.js"], "sourcesContent": ["var _ = require(\"../lodash\");\nvar tarjan = require(\"./tarjan\");\n\nmodule.exports = findCycles;\n\nfunction findCycles(g) {\n  return _.filter(tarjan(g), function(cmpt) {\n    return cmpt.length > 1 || (cmpt.length === 1 && g.hasEdge(cmpt[0], cmpt[0]));\n  });\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC5B,IAAIC,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;AAEhCE,MAAM,CAACC,OAAO,GAAGC,UAAU;AAE3B,SAASA,UAAUA,CAACC,CAAC,EAAE;EACrB,OAAON,CAAC,CAACO,MAAM,CAACL,MAAM,CAACI,CAAC,CAAC,EAAE,UAASE,IAAI,EAAE;IACxC,OAAOA,IAAI,CAACC,MAAM,GAAG,CAAC,IAAKD,IAAI,CAACC,MAAM,KAAK,CAAC,IAAIH,CAAC,CAACI,OAAO,CAACF,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAE;EAC9E,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}