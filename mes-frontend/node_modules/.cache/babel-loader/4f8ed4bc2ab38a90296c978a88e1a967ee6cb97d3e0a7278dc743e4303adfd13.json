{"ast": null, "code": "import { __extends, __read } from \"tslib\";\nimport { deepAssign, hide, intersection, lineLen, rotate, scale } from '../../util';\nimport { CrosshairBase } from './base';\nimport { POLYGON_CROSSHAIR_DEFAULT_STYLE } from './constant';\nvar PolygonCrosshair = /** @class */function (_super) {\n  __extends(PolygonCrosshair, _super);\n  function PolygonCrosshair(options) {\n    return _super.call(this, deepAssign({}, PolygonCrosshair.defaultOptions, options)) || this;\n  }\n  Object.defineProperty(PolygonCrosshair.prototype, \"crosshairPath\", {\n    get: function () {\n      return this.createPolygonPath();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  PolygonCrosshair.prototype.update = function (cfg) {\n    _super.prototype.update.call(this, cfg);\n  };\n  Object.defineProperty(PolygonCrosshair.prototype, \"points\", {\n    /**\n     * 得到从中心出发，各个点方向的单位向量\n     */\n    get: function () {\n      var _a = this.attributes,\n        startAngle = _a.startAngle,\n        sides = _a.sides;\n      var a = Math.PI * 2 / sides;\n      // 单位向量\n      var unit = [1, 0];\n      var points = [];\n      for (var i = 0; i < sides; i += 1) {\n        points.push(rotate(unit, [0, 0], startAngle / 180 * Math.PI + a * i));\n      }\n      return points;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  /**\n   * 1. 判断point位于哪一个扇区\n   * 2. 计算中心到point的线段与所在扇区的边的交点\n   * 3. 计算等效半径\n   */\n  PolygonCrosshair.prototype.setPointer = function (_a) {\n    var _b = __read(_a, 2),\n      x = _b[0],\n      y = _b[1];\n    _super.prototype.setPointer.call(this, [x, y]);\n    var _c = __read(this.localPointer, 2),\n      lx = _c[0],\n      ly = _c[1];\n    var center = this.attributes.center;\n    // 求交点\n    var _d = __read(this.intersection([lx, ly]), 2),\n      ix = _d[0],\n      iy = _d[1];\n    if (!ix || !iy) return;\n    var equivalentRadius = lineLen(center, [lx, ly]) / lineLen(center, [ix, iy]);\n    var path = this.createPolygonPath(equivalentRadius);\n    this.crosshairShape.attr({\n      d: path\n    });\n  };\n  PolygonCrosshair.prototype.adjustLayout = function () {\n    hide(this.tagShape);\n  };\n  PolygonCrosshair.prototype.createPolygonPath = function (radius) {\n    var _a = this.attributes,\n      defaultRadius = _a.defaultRadius,\n      _b = __read(_a.center, 2),\n      cx = _b[0],\n      cy = _b[1];\n    var path = this.points.map(function (_a, index) {\n      var _b = __read(_a, 2),\n        x = _b[0],\n        y = _b[1];\n      var _c = __read(scale([x, y], radius || defaultRadius), 2),\n        tx = _c[0],\n        ty = _c[1];\n      return [index === 0 ? 'M' : 'L', cx + tx, cy + ty];\n    });\n    path.push(['Z']);\n    return path;\n  };\n  /**\n   * 求点与扇区单位边的交点\n   */\n  PolygonCrosshair.prototype.intersection = function (_a) {\n    var _b;\n    var _c = __read(_a, 2),\n      x = _c[0],\n      y = _c[1];\n    var points = this.points;\n    var _d = __read(this.attributes.center, 2),\n      cx = _d[0],\n      cy = _d[1];\n    var ix;\n    var iy;\n    // 遍历每个边\n    for (var i = 1; i <= points.length; i += 1) {\n      var _e = __read(points[i - 1], 2),\n        sx = _e[0],\n        sy = _e[1];\n      var _f = __read(points[i % points.length], 2),\n        ex = _f[0],\n        ey = _f[1];\n      var inter = intersection([x, y], [cx, cy], [sx + cx, sy + cy], [ex + cx, ey + cy]);\n      if (inter.length !== 0) {\n        // 存在交点\n        _b = __read(inter, 2), ix = _b[0], iy = _b[1];\n      }\n    }\n    return [ix, iy];\n  };\n  PolygonCrosshair.tag = 'polygon-crosshair';\n  PolygonCrosshair.defaultOptions = {\n    style: POLYGON_CROSSHAIR_DEFAULT_STYLE\n  };\n  return PolygonCrosshair;\n}(CrosshairBase);\nexport { PolygonCrosshair };", "map": {"version": 3, "names": ["deepAssign", "hide", "intersection", "lineLen", "rotate", "scale", "CrosshairBase", "POLYGON_CROSSHAIR_DEFAULT_STYLE", "PolygonCrosshair", "_super", "__extends", "options", "call", "defaultOptions", "Object", "defineProperty", "prototype", "get", "createPolygonPath", "update", "cfg", "_a", "attributes", "startAngle", "sides", "a", "Math", "PI", "unit", "points", "i", "push", "setPointer", "_b", "__read", "x", "y", "_c", "localPointer", "lx", "ly", "center", "_d", "ix", "iy", "equivalentRadius", "path", "cross<PERSON><PERSON><PERSON><PERSON><PERSON>", "attr", "d", "adjustLayout", "tagShape", "radius", "defaultRadius", "cx", "cy", "map", "index", "tx", "ty", "length", "_e", "sx", "sy", "_f", "ex", "ey", "inter", "tag", "style"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/crosshair/polygon.ts"], "sourcesContent": ["import type { Point } from '../../types';\nimport { deepAssign, hide, intersection, lineLen, rotate, scale } from '../../util';\nimport { CrosshairBase } from './base';\nimport { POLYGON_CROSSHAIR_DEFAULT_STYLE } from './constant';\nimport { PolygonCrosshairOptions, PolygonCrosshairStyleProps } from './types';\n\nexport type { PolygonCrosshairStyleProps, PolygonCrosshairOptions };\n\nexport class PolygonCrosshair extends CrosshairBase<Required<PolygonCrosshairStyleProps>> {\n  public static tag = 'polygon-crosshair';\n\n  protected static defaultOptions = {\n    style: POLYGON_CROSSHAIR_DEFAULT_STYLE,\n  };\n\n  protected get crosshairPath() {\n    return this.createPolygonPath();\n  }\n\n  constructor(options: PolygonCrosshairOptions) {\n    super(deepAssign({}, PolygonCrosshair.defaultOptions, options));\n  }\n\n  public update(cfg: Partial<PolygonCrosshairStyleProps>) {\n    super.update(cfg);\n  }\n\n  /**\n   * 得到从中心出发，各个点方向的单位向量\n   */\n  private get points() {\n    const { startAngle, sides } = this.attributes;\n    const a = (Math.PI * 2) / sides;\n    // 单位向量\n    const unit: [number, number] = [1, 0];\n    const points = [];\n    for (let i = 0; i < sides; i += 1) {\n      points.push(rotate(unit, [0, 0], (startAngle / 180) * Math.PI + a * i));\n    }\n    return points as Point[];\n  }\n\n  /**\n   * 1. 判断point位于哪一个扇区\n   * 2. 计算中心到point的线段与所在扇区的边的交点\n   * 3. 计算等效半径\n   */\n  public setPointer([x, y]: Point) {\n    super.setPointer([x, y]);\n    const [lx, ly] = this.localPointer;\n    const { center } = this.attributes;\n    // 求交点\n    const [ix, iy] = this.intersection([lx, ly]);\n    if (!ix || !iy) return;\n    const equivalentRadius = lineLen(center, [lx, ly]) / lineLen(center, [ix, iy]);\n    const path = this.createPolygonPath(equivalentRadius) as any;\n    this.crosshairShape.attr({ d: path });\n  }\n\n  protected adjustLayout() {\n    hide(this.tagShape);\n  }\n\n  private createPolygonPath(radius?: number) {\n    const {\n      defaultRadius,\n      center: [cx, cy],\n    } = this.attributes;\n    const path = this.points.map(([x, y], index) => {\n      const [tx, ty] = scale([x, y], radius || defaultRadius);\n      return [index === 0 ? 'M' : 'L', cx + tx, cy + ty];\n    });\n    path.push(['Z']);\n    return path as any[];\n  }\n\n  /**\n   * 求点与扇区单位边的交点\n   */\n  private intersection([x, y]: Point) {\n    const { points } = this;\n    const {\n      center: [cx, cy],\n    } = this.attributes;\n    let ix: number;\n    let iy: number;\n    // 遍历每个边\n    for (let i = 1; i <= points.length; i += 1) {\n      const [sx, sy] = points[i - 1];\n      const [ex, ey] = points[i % points.length];\n      const inter = intersection([x, y], [cx, cy], [sx + cx, sy + cy], [ex + cx, ey + cy]);\n      if (inter.length !== 0) {\n        // 存在交点\n        [ix, iy] = inter as Point;\n      }\n    }\n    return [ix!, iy!];\n  }\n}\n"], "mappings": ";AACA,SAASA,UAAU,EAAEC,IAAI,EAAEC,YAAY,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,QAAQ,YAAY;AACnF,SAASC,aAAa,QAAQ,QAAQ;AACtC,SAASC,+BAA+B,QAAQ,YAAY;AAK5D,IAAAC,gBAAA,0BAAAC,MAAA;EAAsCC,SAAA,CAAAF,gBAAA,EAAAC,MAAA;EAWpC,SAAAD,iBAAYG,OAAgC;IAC1C,OAAAF,MAAK,CAAAG,IAAA,OAACZ,UAAU,CAAC,EAAE,EAAEQ,gBAAgB,CAACK,cAAc,EAAEF,OAAO,CAAC,CAAC;EACjE;EANAG,MAAA,CAAAC,cAAA,CAAcP,gBAAA,CAAAQ,SAAA,iBAAa;SAA3B,SAAAC,CAAA;MACE,OAAO,IAAI,CAACC,iBAAiB,EAAE;IACjC,CAAC;;;;EAMMV,gBAAA,CAAAQ,SAAA,CAAAG,MAAM,GAAb,UAAcC,GAAwC;IACpDX,MAAA,CAAAO,SAAK,CAACG,MAAM,CAAAP,IAAA,OAACQ,GAAG,CAAC;EACnB,CAAC;EAKDN,MAAA,CAAAC,cAAA,CAAYP,gBAAA,CAAAQ,SAAA,UAAM;IAHlB;;;SAGA,SAAAC,CAAA;MACQ,IAAAI,EAAA,GAAwB,IAAI,CAACC,UAAU;QAArCC,UAAU,GAAAF,EAAA,CAAAE,UAAA;QAAEC,KAAK,GAAAH,EAAA,CAAAG,KAAoB;MAC7C,IAAMC,CAAC,GAAIC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAIH,KAAK;MAC/B;MACA,IAAMI,IAAI,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;MACrC,IAAMC,MAAM,GAAG,EAAE;MACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,EAAEM,CAAC,IAAI,CAAC,EAAE;QACjCD,MAAM,CAACE,IAAI,CAAC3B,MAAM,CAACwB,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAGL,UAAU,GAAG,GAAG,GAAIG,IAAI,CAACC,EAAE,GAAGF,CAAC,GAAGK,CAAC,CAAC,CAAC;MACzE;MACA,OAAOD,MAAiB;IAC1B,CAAC;;;;EAED;;;;;EAKOrB,gBAAA,CAAAQ,SAAA,CAAAgB,UAAU,GAAjB,UAAkBX,EAAa;QAAbY,EAAA,GAAAC,MAAA,CAAAb,EAAA,IAAa;MAAZc,CAAC,GAAAF,EAAA;MAAEG,CAAC,GAAAH,EAAA;IACrBxB,MAAA,CAAAO,SAAK,CAACgB,UAAU,CAAApB,IAAA,OAAC,CAACuB,CAAC,EAAEC,CAAC,CAAC,CAAC;IAClB,IAAAC,EAAA,GAAAH,MAAA,CAAW,IAAI,CAACI,YAAY;MAA3BC,EAAE,GAAAF,EAAA;MAAEG,EAAE,GAAAH,EAAA,GAAqB;IAC1B,IAAAI,MAAM,GAAK,IAAI,CAACnB,UAAU,CAAAmB,MAApB;IACd;IACM,IAAAC,EAAA,GAAAR,MAAA,CAAW,IAAI,CAAChC,YAAY,CAAC,CAACqC,EAAE,EAAEC,EAAE,CAAC,CAAC;MAArCG,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAA+B;IAC5C,IAAI,CAACC,EAAE,IAAI,CAACC,EAAE,EAAE;IAChB,IAAMC,gBAAgB,GAAG1C,OAAO,CAACsC,MAAM,EAAE,CAACF,EAAE,EAAEC,EAAE,CAAC,CAAC,GAAGrC,OAAO,CAACsC,MAAM,EAAE,CAACE,EAAE,EAAEC,EAAE,CAAC,CAAC;IAC9E,IAAME,IAAI,GAAG,IAAI,CAAC5B,iBAAiB,CAAC2B,gBAAgB,CAAQ;IAC5D,IAAI,CAACE,cAAc,CAACC,IAAI,CAAC;MAAEC,CAAC,EAAEH;IAAI,CAAE,CAAC;EACvC,CAAC;EAEStC,gBAAA,CAAAQ,SAAA,CAAAkC,YAAY,GAAtB;IACEjD,IAAI,CAAC,IAAI,CAACkD,QAAQ,CAAC;EACrB,CAAC;EAEO3C,gBAAA,CAAAQ,SAAA,CAAAE,iBAAiB,GAAzB,UAA0BkC,MAAe;IACjC,IAAA/B,EAAA,GAGF,IAAI,CAACC,UAAU;MAFjB+B,aAAa,GAAAhC,EAAA,CAAAgC,aAAA;MACbpB,EAAA,GAAAC,MAAA,CAAAb,EAAA,CAAAoB,MAAA,IAAgB;MAAPa,EAAE,GAAArB,EAAA;MAAEsB,EAAE,GAAAtB,EAAA,GACE;IACnB,IAAMa,IAAI,GAAG,IAAI,CAACjB,MAAM,CAAC2B,GAAG,CAAC,UAACnC,EAAM,EAAEoC,KAAK;UAAbxB,EAAA,GAAAC,MAAA,CAAAb,EAAA,IAAM;QAALc,CAAC,GAAAF,EAAA;QAAEG,CAAC,GAAAH,EAAA;MAC3B,IAAAI,EAAA,GAAAH,MAAA,CAAW7B,KAAK,CAAC,CAAC8B,CAAC,EAAEC,CAAC,CAAC,EAAEgB,MAAM,IAAIC,aAAa,CAAC;QAAhDK,EAAE,GAAArB,EAAA;QAAEsB,EAAE,GAAAtB,EAAA,GAA0C;MACvD,OAAO,CAACoB,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG,EAAEH,EAAE,GAAGI,EAAE,EAAEH,EAAE,GAAGI,EAAE,CAAC;IACpD,CAAC,CAAC;IACFb,IAAI,CAACf,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IAChB,OAAOe,IAAa;EACtB,CAAC;EAED;;;EAGQtC,gBAAA,CAAAQ,SAAA,CAAAd,YAAY,GAApB,UAAqBmB,EAAa;;QAAbgB,EAAA,GAAAH,MAAA,CAAAb,EAAA,IAAa;MAAZc,CAAC,GAAAE,EAAA;MAAED,CAAC,GAAAC,EAAA;IAChB,IAAAR,MAAM,GAAK,IAAI,CAAAA,MAAT;IAEZ,IAAAa,EAAA,GAAAR,MAAA,CACE,IAAI,CAACZ,UAAU,CAAAmB,MAAA,IADD;MAAPa,EAAE,GAAAZ,EAAA;MAAEa,EAAE,GAAAb,EAAA,GAAC;IAElB,IAAIC,EAAU;IACd,IAAIC,EAAU;IACd;IACA,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAID,MAAM,CAAC+B,MAAM,EAAE9B,CAAC,IAAI,CAAC,EAAE;MACpC,IAAA+B,EAAA,GAAA3B,MAAA,CAAWL,MAAM,CAACC,CAAC,GAAG,CAAC,CAAC;QAAvBgC,EAAE,GAAAD,EAAA;QAAEE,EAAE,GAAAF,EAAA,GAAiB;MACxB,IAAAG,EAAA,GAAA9B,MAAA,CAAWL,MAAM,CAACC,CAAC,GAAGD,MAAM,CAAC+B,MAAM,CAAC;QAAnCK,EAAE,GAAAD,EAAA;QAAEE,EAAE,GAAAF,EAAA,GAA6B;MAC1C,IAAMG,KAAK,GAAGjE,YAAY,CAAC,CAACiC,CAAC,EAAEC,CAAC,CAAC,EAAE,CAACkB,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACO,EAAE,GAAGR,EAAE,EAAES,EAAE,GAAGR,EAAE,CAAC,EAAE,CAACU,EAAE,GAAGX,EAAE,EAAEY,EAAE,GAAGX,EAAE,CAAC,CAAC;MACpF,IAAIY,KAAK,CAACP,MAAM,KAAK,CAAC,EAAE;QACtB;QACA3B,EAAA,GAAAC,MAAA,CAAWiC,KAAc,MAAxBxB,EAAE,GAAAV,EAAA,KAAEW,EAAE,GAAAX,EAAA;MACT;IACF;IACA,OAAO,CAACU,EAAG,EAAEC,EAAG,CAAC;EACnB,CAAC;EAxFapC,gBAAA,CAAA4D,GAAG,GAAG,mBAAmB;EAEtB5D,gBAAA,CAAAK,cAAc,GAAG;IAChCwD,KAAK,EAAE9D;GACR;EAqFH,OAAAC,gBAAC;CAAA,CA1FqCF,aAAa;SAAtCE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}