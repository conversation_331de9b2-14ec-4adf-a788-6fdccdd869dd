{"ast": null, "code": "/*\n * This module provides coordinate assignment based on <PERSON><PERSON> and <PERSON>, \"Fast\n * and Simple Horizontal Coordinate Assignment.\"\n */\nimport { Graph } from '@antv/graphlib';\nimport { buildLayerMatrix, minBy } from '../util';\nexport const findType1Conflicts = (g, layering) => {\n  const conflicts = {};\n  const visitLayer = (prevLayer, layer) => {\n    // last visited node in the previous layer that is incident on an inner\n    // segment.\n    let k0 = 0;\n    // Tracks the last node in this layer scanned for crossings with a type-1\n    // segment.\n    let scanPos = 0;\n    const prevLayerLength = prevLayer.length;\n    const lastNode = layer === null || layer === void 0 ? void 0 : layer[(layer === null || layer === void 0 ? void 0 : layer.length) - 1];\n    layer === null || layer === void 0 ? void 0 : layer.forEach((v, i) => {\n      var _a;\n      const w = findOtherInnerSegmentNode(g, v);\n      const k1 = w ? g.getNode(w.id).data.order : prevLayerLength;\n      if (w || v === lastNode) {\n        (_a = layer.slice(scanPos, i + 1)) === null || _a === void 0 ? void 0 : _a.forEach(scanNode => {\n          var _a;\n          (_a = g.getPredecessors(scanNode)) === null || _a === void 0 ? void 0 : _a.forEach(u => {\n            var _a;\n            const uLabel = g.getNode(u.id);\n            const uPos = uLabel.data.order;\n            if ((uPos < k0 || k1 < uPos) && !(uLabel.data.dummy && ((_a = g.getNode(scanNode)) === null || _a === void 0 ? void 0 : _a.data.dummy))) {\n              addConflict(conflicts, u.id, scanNode);\n            }\n          });\n        });\n        scanPos = i + 1;\n        k0 = k1;\n      }\n    });\n    return layer;\n  };\n  if (layering === null || layering === void 0 ? void 0 : layering.length) {\n    layering.reduce(visitLayer);\n  }\n  return conflicts;\n};\nexport const findType2Conflicts = (g, layering) => {\n  const conflicts = {};\n  function scan(south, southPos, southEnd, prevNorthBorder, nextNorthBorder) {\n    var _a, _b;\n    let v;\n    for (let i = southPos; i < southEnd; i++) {\n      v = south[i];\n      if ((_a = g.getNode(v)) === null || _a === void 0 ? void 0 : _a.data.dummy) {\n        (_b = g.getPredecessors(v)) === null || _b === void 0 ? void 0 : _b.forEach(u => {\n          const uNode = g.getNode(u.id);\n          if (uNode.data.dummy && (uNode.data.order < prevNorthBorder || uNode.data.order > nextNorthBorder)) {\n            addConflict(conflicts, u.id, v);\n          }\n        });\n      }\n    }\n  }\n  function getScannedKey(params) {\n    // south数组可能很大，不适合做key\n    return JSON.stringify(params.slice(1));\n  }\n  function scanIfNeeded(params, scanCache) {\n    const cacheKey = getScannedKey(params);\n    if (scanCache.get(cacheKey)) return;\n    scan(...params);\n    scanCache.set(cacheKey, true);\n  }\n  const visitLayer = (north, south) => {\n    let prevNorthPos = -1;\n    let nextNorthPos;\n    let southPos = 0;\n    const scanned = new Map();\n    south === null || south === void 0 ? void 0 : south.forEach((v, southLookahead) => {\n      var _a;\n      if (((_a = g.getNode(v)) === null || _a === void 0 ? void 0 : _a.data.dummy) === 'border') {\n        const predecessors = g.getPredecessors(v) || [];\n        if (predecessors.length) {\n          nextNorthPos = g.getNode(predecessors[0].id).data.order;\n          scanIfNeeded([south, southPos, southLookahead, prevNorthPos, nextNorthPos], scanned);\n          southPos = southLookahead;\n          prevNorthPos = nextNorthPos;\n        }\n      }\n      scanIfNeeded([south, southPos, south.length, nextNorthPos, north.length], scanned);\n    });\n    return south;\n  };\n  if (layering === null || layering === void 0 ? void 0 : layering.length) {\n    layering.reduce(visitLayer);\n  }\n  return conflicts;\n};\nexport const findOtherInnerSegmentNode = (g, v) => {\n  var _a, _b;\n  if ((_a = g.getNode(v)) === null || _a === void 0 ? void 0 : _a.data.dummy) {\n    return (_b = g.getPredecessors(v)) === null || _b === void 0 ? void 0 : _b.find(u => g.getNode(u.id).data.dummy);\n  }\n};\nexport const addConflict = (conflicts, v, w) => {\n  let vv = v;\n  let ww = w;\n  if (vv > ww) {\n    const tmp = vv;\n    vv = ww;\n    ww = tmp;\n  }\n  let conflictsV = conflicts[vv];\n  if (!conflictsV) {\n    conflicts[vv] = conflictsV = {};\n  }\n  conflictsV[ww] = true;\n};\nexport const hasConflict = (conflicts, v, w) => {\n  let vv = v;\n  let ww = w;\n  if (vv > ww) {\n    const tmp = v;\n    vv = ww;\n    ww = tmp;\n  }\n  return !!conflicts[vv];\n};\n/*\n * Try to align nodes into vertical \"blocks\" where possible. This algorithm\n * attempts to align a node with one of its median neighbors. If the edge\n * connecting a neighbor is a type-1 conflict then we ignore that possibility.\n * If a previous node has already formed a block with a node after the node\n * we're trying to form a block with, we also ignore that possibility - our\n * blocks would be split in that scenario.\n */\nexport const verticalAlignment = (g, layering, conflicts, neighborFn) => {\n  const root = {};\n  const align = {};\n  const pos = {};\n  // We cache the position here based on the layering because the graph and\n  // layering may be out of sync. The layering matrix is manipulated to\n  // generate different extreme alignments.\n  layering === null || layering === void 0 ? void 0 : layering.forEach(layer => {\n    layer === null || layer === void 0 ? void 0 : layer.forEach((v, order) => {\n      root[v] = v;\n      align[v] = v;\n      pos[v] = order;\n    });\n  });\n  layering === null || layering === void 0 ? void 0 : layering.forEach(layer => {\n    let prevIdx = -1;\n    layer === null || layer === void 0 ? void 0 : layer.forEach(v => {\n      let ws = neighborFn(v).map(n => n.id);\n      if (ws.length) {\n        ws = ws.sort((a, b) => pos[a] - pos[b]);\n        const mp = (ws.length - 1) / 2;\n        for (let i = Math.floor(mp), il = Math.ceil(mp); i <= il; ++i) {\n          const w = ws[i];\n          if (align[v] === v && prevIdx < pos[w] && !hasConflict(conflicts, v, w)) {\n            align[w] = v;\n            align[v] = root[v] = root[w];\n            prevIdx = pos[w];\n          }\n        }\n      }\n    });\n  });\n  return {\n    root,\n    align\n  };\n};\nexport const horizontalCompaction = (g, layering, root, align, nodesep, edgesep, reverseSep) => {\n  var _a;\n  // This portion of the algorithm differs from BK due to a number of problems.\n  // Instead of their algorithm we construct a new block graph and do two\n  // sweeps. The first sweep places blocks with the smallest possible\n  // coordinates. The second sweep removes unused space by moving blocks to the\n  // greatest coordinates without violating separation.\n  const xs = {};\n  const blockG = buildBlockGraph(g, layering, root, nodesep, edgesep, reverseSep);\n  const borderType = reverseSep ? 'borderLeft' : 'borderRight';\n  const iterate = (setXsFunc, nextNodesFunc) => {\n    let stack = blockG.getAllNodes();\n    let elem = stack.pop();\n    const visited = {};\n    while (elem) {\n      if (visited[elem.id]) {\n        setXsFunc(elem.id);\n      } else {\n        visited[elem.id] = true;\n        stack.push(elem);\n        stack = stack.concat(nextNodesFunc(elem.id));\n      }\n      elem = stack.pop();\n    }\n  };\n  // First pass, assign smallest coordinates\n  const pass1 = elem => {\n    xs[elem] = (blockG.getRelatedEdges(elem, 'in') || []).reduce((acc, e) => {\n      return Math.max(acc, (xs[e.source] || 0) + e.data.weight);\n    }, 0);\n  };\n  // Second pass, assign greatest coordinates\n  const pass2 = elem => {\n    const min = (blockG.getRelatedEdges(elem, 'out') || []).reduce((acc, e) => {\n      return Math.min(acc, (xs[e.target] || 0) - e.data.weight);\n    }, Number.POSITIVE_INFINITY);\n    const node = g.getNode(elem);\n    if (min !== Number.POSITIVE_INFINITY && node.data.borderType !== borderType) {\n      xs[elem] = Math.max(xs[elem], min);\n    }\n  };\n  iterate(pass1, blockG.getPredecessors.bind(blockG));\n  iterate(pass2, blockG.getSuccessors.bind(blockG));\n  // Assign x coordinates to all nodes\n  (_a = Object.values(align)) === null || _a === void 0 ? void 0 : _a.forEach(v => {\n    xs[v] = xs[root[v]];\n  });\n  return xs;\n};\nexport const buildBlockGraph = (g, layering, root, nodesep, edgesep, reverseSep) => {\n  const blockGraph = new Graph();\n  const sepFn = sep(nodesep, edgesep, reverseSep);\n  layering === null || layering === void 0 ? void 0 : layering.forEach(layer => {\n    let u;\n    layer === null || layer === void 0 ? void 0 : layer.forEach(v => {\n      const vRoot = root[v];\n      if (!blockGraph.hasNode(vRoot)) {\n        blockGraph.addNode({\n          id: vRoot,\n          data: {}\n        });\n      }\n      if (u) {\n        const uRoot = root[u];\n        const edge = blockGraph.getRelatedEdges(uRoot, 'out').find(edge => edge.target === vRoot);\n        if (!edge) {\n          blockGraph.addEdge({\n            id: `e${Math.random()}`,\n            source: uRoot,\n            target: vRoot,\n            data: {\n              weight: Math.max(sepFn(g, v, u), 0)\n            }\n          });\n        } else {\n          blockGraph.updateEdgeData(edge.id, Object.assign(Object.assign({}, edge.data), {\n            weight: Math.max(sepFn(g, v, u), edge.data.weight || 0)\n          }));\n        }\n      }\n      u = v;\n    });\n  });\n  return blockGraph;\n};\n/*\n * Returns the alignment that has the smallest width of the given alignments.\n */\nexport const findSmallestWidthAlignment = (g, xss) => {\n  return minBy(Object.values(xss), xs => {\n    var _a;\n    let max = Number.NEGATIVE_INFINITY;\n    let min = Number.POSITIVE_INFINITY;\n    (_a = Object.keys(xs)) === null || _a === void 0 ? void 0 : _a.forEach(v => {\n      const x = xs[v];\n      const halfWidth = width(g, v) / 2;\n      max = Math.max(x + halfWidth, max);\n      min = Math.min(x - halfWidth, min);\n    });\n    return max - min;\n  });\n};\n/*\n * Align the coordinates of each of the layout alignments such that\n * left-biased alignments have their minimum coordinate at the same point as\n * the minimum coordinate of the smallest width alignment and right-biased\n * alignments have their maximum coordinate at the same point as the maximum\n * coordinate of the smallest width alignment.\n */\nexport function alignCoordinates(xss, alignTo) {\n  const alignToVals = Object.values(alignTo);\n  const alignToMin = Math.min(...alignToVals);\n  const alignToMax = Math.max(...alignToVals);\n  ['u', 'd'].forEach(vert => {\n    ['l', 'r'].forEach(horiz => {\n      const alignment = vert + horiz;\n      const xs = xss[alignment];\n      let delta;\n      if (xs === alignTo) return;\n      const xsVals = Object.values(xs);\n      delta = horiz === 'l' ? alignToMin - Math.min(...xsVals) : alignToMax - Math.max(...xsVals);\n      if (delta) {\n        xss[alignment] = {};\n        Object.keys(xs).forEach(key => {\n          xss[alignment][key] = xs[key] + delta;\n        });\n      }\n    });\n  });\n}\nexport const balance = (xss, align) => {\n  const result = {};\n  Object.keys(xss.ul).forEach(key => {\n    if (align) {\n      result[key] = xss[align.toLowerCase()][key];\n    } else {\n      const values = Object.values(xss).map(x => x[key]);\n      result[key] = (values[0] + values[1]) / 2; // (ur + ul) / 2\n    }\n  });\n  return result;\n};\nexport const positionX = (g, options) => {\n  const {\n    align: graphAlign,\n    nodesep = 0,\n    edgesep = 0\n  } = options || {};\n  const layering = buildLayerMatrix(g);\n  const conflicts = Object.assign(findType1Conflicts(g, layering), findType2Conflicts(g, layering));\n  const xss = {};\n  let adjustedLayering;\n  ['u', 'd'].forEach(vert => {\n    adjustedLayering = vert === 'u' ? layering : Object.values(layering).reverse();\n    ['l', 'r'].forEach(horiz => {\n      if (horiz === 'r') {\n        adjustedLayering = adjustedLayering.map(inner => Object.values(inner).reverse());\n      }\n      const neighborFn = (vert === 'u' ? g.getPredecessors : g.getSuccessors).bind(g);\n      const align = verticalAlignment(g, adjustedLayering, conflicts, neighborFn);\n      const xs = horizontalCompaction(g, adjustedLayering, align.root, align.align, nodesep, edgesep, horiz === 'r');\n      if (horiz === 'r') {\n        Object.keys(xs).forEach(key => {\n          xs[key] = -xs[key];\n        });\n      }\n      xss[vert + horiz] = xs;\n    });\n  });\n  const smallestWidth = findSmallestWidthAlignment(g, xss);\n  alignCoordinates(xss, smallestWidth);\n  return balance(xss, graphAlign);\n};\nexport const sep = (nodeSep, edgeSep, reverseSep) => {\n  return (g, v, w) => {\n    const vLabel = g.getNode(v);\n    const wLabel = g.getNode(w);\n    let sum = 0;\n    let delta = 0;\n    sum += vLabel.data.width / 2;\n    if (vLabel.data.hasOwnProperty('labelpos')) {\n      switch ((vLabel.data.labelpos || '').toLowerCase()) {\n        case 'l':\n          delta = -vLabel.data.width / 2;\n          break;\n        case 'r':\n          delta = vLabel.data.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n    sum += (vLabel.data.dummy ? edgeSep : nodeSep) / 2;\n    sum += (wLabel.data.dummy ? edgeSep : nodeSep) / 2;\n    sum += wLabel.data.width / 2;\n    if (wLabel.data.labelpos) {\n      switch ((wLabel.data.labelpos || '').toLowerCase()) {\n        case 'l':\n          delta = wLabel.data.width / 2;\n          break;\n        case 'r':\n          delta = -wLabel.data.width / 2;\n          break;\n      }\n    }\n    if (delta) {\n      sum += reverseSep ? delta : -delta;\n    }\n    delta = 0;\n    return sum;\n  };\n};\nexport const width = (g, v) => g.getNode(v).data.width || 0;", "map": {"version": 3, "names": ["Graph", "buildLayerMatrix", "minBy", "findType1Conflicts", "g", "layering", "conflicts", "<PERSON><PERSON><PERSON><PERSON>", "prevLayer", "layer", "k0", "scanPos", "prevL<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "lastNode", "for<PERSON>ach", "v", "i", "w", "findOtherInnerSegmentNode", "k1", "getNode", "id", "data", "order", "_a", "slice", "scanNode", "getPredecessors", "u", "uLabel", "uPos", "dummy", "addConflict", "reduce", "findType2Conflicts", "scan", "south", "southPos", "southEnd", "prevNorthBorder", "nextNorthBorder", "_b", "uNode", "getScannedKey", "params", "JSON", "stringify", "scanIfNeeded", "scanCache", "cache<PERSON>ey", "get", "set", "north", "prevNorthPos", "nextNorthPos", "scanned", "Map", "southLookahead", "predecessors", "find", "vv", "ww", "tmp", "conflictsV", "hasConflict", "verticalAlignment", "neighborFn", "root", "align", "pos", "prevIdx", "ws", "map", "n", "sort", "a", "b", "mp", "Math", "floor", "il", "ceil", "horizontalCompaction", "nodesep", "edgesep", "reverseSep", "xs", "blockG", "buildBlockGraph", "borderType", "iterate", "setXsFunc", "nextNodesFunc", "stack", "getAllNodes", "elem", "pop", "visited", "push", "concat", "pass1", "getRelatedEdges", "acc", "e", "max", "source", "weight", "pass2", "min", "target", "Number", "POSITIVE_INFINITY", "node", "bind", "getSuccessors", "Object", "values", "blockGraph", "sepFn", "sep", "vRoot", "hasNode", "addNode", "uRoot", "edge", "addEdge", "random", "updateEdgeData", "assign", "findSmallestWidthAlignment", "xss", "NEGATIVE_INFINITY", "keys", "x", "halfWidth", "width", "alignCoordinates", "alignTo", "alignToVals", "alignToMin", "alignToMax", "vert", "horiz", "alignment", "delta", "xsVals", "key", "balance", "result", "ul", "toLowerCase", "positionX", "options", "graphAlign", "adjustedLayering", "reverse", "inner", "smallestWidth", "nodeSep", "edgeSep", "vLabel", "w<PERSON><PERSON><PERSON>", "sum", "hasOwnProperty", "labelpos"], "sources": ["../../../src/antv-dagre/position/bk.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;AAIA,SAASA,KAAK,QAAkB,gBAAgB;AAGhD,SAASC,gBAAgB,EAAEC,KAAK,QAAQ,SAAS;AAsBjD,OAAO,MAAMC,kBAAkB,GAAGA,CAACC,CAAS,EAAEC,QAAiB,KAAI;EACjE,MAAMC,SAAS,GAAG,EAAE;EAEpB,MAAMC,UAAU,GAAGA,CAACC,SAAe,EAAEC,KAAW,KAAI;IAClD;IACA;IACA,IAAIC,EAAE,GAAG,CAAC;IACV;IACA;IACA,IAAIC,OAAO,GAAG,CAAC;IACf,MAAMC,eAAe,GAAGJ,SAAS,CAACK,MAAM;IACxC,MAAMC,QAAQ,GAAGL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,MAAM,IAAG,CAAC,CAAC;IAE3CJ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,OAAO,CAAC,CAACC,CAAK,EAAEC,CAAS,KAAI;;MAClC,MAAMC,CAAC,GAAGC,yBAAyB,CAACf,CAAC,EAAEY,CAAC,CAAC;MACzC,MAAMI,EAAE,GAAGF,CAAC,GAAGd,CAAC,CAACiB,OAAO,CAACH,CAAC,CAACI,EAAE,CAAE,CAACC,IAAI,CAACC,KAAM,GAAGZ,eAAe;MAE7D,IAAIM,CAAC,IAAIF,CAAC,KAAKF,QAAQ,EAAE;QACvB,CAAAW,EAAA,GAAAhB,KAAK,CAACiB,KAAK,CAACf,OAAO,EAAEM,CAAC,GAAG,CAAC,CAAC,cAAAQ,EAAA,uBAAAA,EAAA,CAAEV,OAAO,CAAEY,QAAQ,IAAI;;UAChD,CAAAF,EAAA,GAAArB,CAAC,CAACwB,eAAe,CAACD,QAAQ,CAAC,cAAAF,EAAA,uBAAAA,EAAA,CAAEV,OAAO,CAAEc,CAAC,IAAI;;YACzC,MAAMC,MAAM,GAAG1B,CAAC,CAACiB,OAAO,CAACQ,CAAC,CAACP,EAAE,CAAE;YAC/B,MAAMS,IAAI,GAAGD,MAAM,CAACP,IAAI,CAACC,KAAM;YAC/B,IACE,CAACO,IAAI,GAAGrB,EAAE,IAAIU,EAAE,GAAGW,IAAI,KACvB,EAAED,MAAM,CAACP,IAAI,CAACS,KAAK,KAAI,CAAAP,EAAA,GAAArB,CAAC,CAACiB,OAAO,CAACM,QAAQ,CAAC,cAAAF,EAAA,uBAAAA,EAAA,CAAEF,IAAI,CAACS,KAAK,EAAC,EACvD;cACAC,WAAW,CAAC3B,SAAS,EAAEuB,CAAC,CAACP,EAAE,EAAEK,QAAQ,CAAC;;UAE1C,CAAC,CAAC;QACJ,CAAC,CAAC;QACFhB,OAAO,GAAGM,CAAC,GAAG,CAAC;QACfP,EAAE,GAAGU,EAAE;;IAEX,CAAC,CAAC;IAEF,OAAOX,KAAK;EACd,CAAC;EAED,IAAIJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,MAAM,EAAE;IACpBR,QAAQ,CAAC6B,MAAM,CAAC3B,UAAU,CAAC;;EAE7B,OAAOD,SAAS;AAClB,CAAC;AAED,OAAO,MAAM6B,kBAAkB,GAAGA,CAAC/B,CAAS,EAAEC,QAAiB,KAAI;EACjE,MAAMC,SAAS,GAAG,EAAE;EAEpB,SAAS8B,IAAIA,CACXC,KAAe,EACfC,QAAgB,EAChBC,QAAgB,EAChBC,eAAuB,EACvBC,eAAuB;;IAEvB,IAAIzB,CAAS;IACb,KAAK,IAAIC,CAAC,GAAGqB,QAAQ,EAAErB,CAAC,GAAGsB,QAAQ,EAAEtB,CAAC,EAAE,EAAE;MACxCD,CAAC,GAAGqB,KAAK,CAACpB,CAAC,CAAC;MACZ,IAAI,CAAAQ,EAAA,GAAArB,CAAC,CAACiB,OAAO,CAACL,CAAC,CAAC,cAAAS,EAAA,uBAAAA,EAAA,CAAEF,IAAI,CAACS,KAAK,EAAE;QAC5B,CAAAU,EAAA,GAAAtC,CAAC,CAACwB,eAAe,CAACZ,CAAC,CAAC,cAAA0B,EAAA,uBAAAA,EAAA,CAAE3B,OAAO,CAAEc,CAAC,IAAI;UAClC,MAAMc,KAAK,GAAGvC,CAAC,CAACiB,OAAO,CAACQ,CAAC,CAACP,EAAE,CAAE;UAC9B,IACEqB,KAAK,CAACpB,IAAI,CAACS,KAAK,KACfW,KAAK,CAACpB,IAAI,CAACC,KAAM,GAAGgB,eAAe,IAClCG,KAAK,CAACpB,IAAI,CAACC,KAAM,GAAGiB,eAAe,CAAC,EACtC;YACAR,WAAW,CAAC3B,SAAS,EAAEuB,CAAC,CAACP,EAAE,EAAEN,CAAC,CAAC;;QAEnC,CAAC,CAAC;;;EAGR;EAEA,SAAS4B,aAAaA,CAACC,MAA+B;IACpD;IACA,OAAOC,IAAI,CAACC,SAAS,CAACF,MAAM,CAACnB,KAAK,CAAC,CAAC,CAAC,CAAC;EACxC;EAEA,SAASsB,YAAYA,CACnBH,MAA+B,EAC/BI,SAA+B;IAE/B,MAAMC,QAAQ,GAAGN,aAAa,CAACC,MAAM,CAAC;IAEtC,IAAII,SAAS,CAACE,GAAG,CAACD,QAAQ,CAAC,EAAE;IAE7Bd,IAAI,CAAC,GAAGS,MAAM,CAAC;IACfI,SAAS,CAACG,GAAG,CAACF,QAAQ,EAAE,IAAI,CAAC;EAC/B;EAEA,MAAM3C,UAAU,GAAGA,CAAC8C,KAAe,EAAEhB,KAAe,KAAI;IACtD,IAAIiB,YAAY,GAAG,CAAC,CAAC;IACrB,IAAIC,YAAoB;IACxB,IAAIjB,QAAQ,GAAG,CAAC;IAEhB,MAAMkB,OAAO,GAAG,IAAIC,GAAG,EAAmB;IAE1CpB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEtB,OAAO,CAAC,CAACC,CAAS,EAAE0C,cAAsB,KAAI;;MACnD,IAAI,EAAAjC,EAAA,GAAArB,CAAC,CAACiB,OAAO,CAACL,CAAC,CAAC,cAAAS,EAAA,uBAAAA,EAAA,CAAEF,IAAI,CAACS,KAAK,MAAK,QAAQ,EAAE;QACzC,MAAM2B,YAAY,GAAGvD,CAAC,CAACwB,eAAe,CAACZ,CAAC,CAAC,IAAI,EAAE;QAC/C,IAAI2C,YAAY,CAAC9C,MAAM,EAAE;UACvB0C,YAAY,GAAGnD,CAAC,CAACiB,OAAO,CAACsC,YAAY,CAAC,CAAC,CAAC,CAACrC,EAAE,CAAE,CAACC,IAAI,CAACC,KAAM;UACzDwB,YAAY,CACV,CAACX,KAAK,EAAEC,QAAQ,EAAEoB,cAAc,EAAEJ,YAAY,EAAEC,YAAY,CAAC,EAC7DC,OAAO,CACR;UACDlB,QAAQ,GAAGoB,cAAc;UACzBJ,YAAY,GAAGC,YAAY;;;MAG/BP,YAAY,CACV,CAACX,KAAK,EAAEC,QAAQ,EAAED,KAAK,CAACxB,MAAM,EAAE0C,YAAY,EAAEF,KAAK,CAACxC,MAAM,CAAC,EAC3D2C,OAAO,CACR;IACH,CAAC,CAAC;IAEF,OAAOnB,KAAK;EACd,CAAC;EAED,IAAIhC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,MAAM,EAAE;IACpBR,QAAQ,CAAC6B,MAAM,CAAC3B,UAAU,CAAC;;EAE7B,OAAOD,SAAS;AAClB,CAAC;AAED,OAAO,MAAMa,yBAAyB,GAAGA,CAACf,CAAS,EAAEY,CAAK,KAAI;;EAC5D,IAAI,CAAAS,EAAA,GAAArB,CAAC,CAACiB,OAAO,CAACL,CAAC,CAAC,cAAAS,EAAA,uBAAAA,EAAA,CAAEF,IAAI,CAACS,KAAK,EAAE;IAC5B,OAAO,CAAAU,EAAA,GAAAtC,CAAC,CAACwB,eAAe,CAACZ,CAAC,CAAC,cAAA0B,EAAA,uBAAAA,EAAA,CAAEkB,IAAI,CAAE/B,CAAC,IAAKzB,CAAC,CAACiB,OAAO,CAACQ,CAAC,CAACP,EAAE,CAAC,CAACC,IAAI,CAACS,KAAK,CAAC;;AAExE,CAAC;AAED,OAAO,MAAMC,WAAW,GAAGA,CAAC3B,SAAoB,EAAEU,CAAK,EAAEE,CAAK,KAAI;EAChE,IAAI2C,EAAE,GAAG7C,CAAC;EACV,IAAI8C,EAAE,GAAG5C,CAAC;EACV,IAAI2C,EAAE,GAAGC,EAAE,EAAE;IACX,MAAMC,GAAG,GAAGF,EAAE;IACdA,EAAE,GAAGC,EAAE;IACPA,EAAE,GAAGC,GAAG;;EAGV,IAAIC,UAAU,GAAG1D,SAAS,CAACuD,EAAE,CAAC;EAC9B,IAAI,CAACG,UAAU,EAAE;IACf1D,SAAS,CAACuD,EAAE,CAAC,GAAGG,UAAU,GAAG,EAAE;;EAEjCA,UAAU,CAACF,EAAE,CAAC,GAAG,IAAI;AACvB,CAAC;AAED,OAAO,MAAMG,WAAW,GAAGA,CAAC3D,SAAoB,EAAEU,CAAK,EAAEE,CAAK,KAAI;EAChE,IAAI2C,EAAE,GAAG7C,CAAC;EACV,IAAI8C,EAAE,GAAG5C,CAAC;EACV,IAAI2C,EAAE,GAAGC,EAAE,EAAE;IACX,MAAMC,GAAG,GAAG/C,CAAC;IACb6C,EAAE,GAAGC,EAAE;IACPA,EAAE,GAAGC,GAAG;;EAEV,OAAO,CAAC,CAACzD,SAAS,CAACuD,EAAE,CAAC;AACxB,CAAC;AAED;;;;;;;;AAQA,OAAO,MAAMK,iBAAiB,GAAGA,CAC/B9D,CAAS,EACTC,QAAgB,EAChBC,SAAoB,EACpB6D,UAAuC,KACrC;EACF,MAAMC,IAAI,GAAmB,EAAE;EAC/B,MAAMC,KAAK,GAAmB,EAAE;EAChC,MAAMC,GAAG,GAAuB,EAAE;EAElC;EACA;EACA;EACAjE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,OAAO,CAAEN,KAAK,IAAI;IAC1BA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,OAAO,CAAC,CAACC,CAAC,EAAEQ,KAAa,KAAI;MAClC4C,IAAI,CAACpD,CAAC,CAAC,GAAGA,CAAC;MACXqD,KAAK,CAACrD,CAAC,CAAC,GAAGA,CAAC;MACZsD,GAAG,CAACtD,CAAC,CAAC,GAAGQ,KAAK;IAChB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEFnB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,OAAO,CAAEN,KAAK,IAAI;IAC1B,IAAI8D,OAAO,GAAG,CAAC,CAAC;IAChB9D,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,OAAO,CAAEC,CAAC,IAAI;MACnB,IAAIwD,EAAE,GAAGL,UAAU,CAACnD,CAAC,CAAC,CAACyD,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACpD,EAAE,CAAC;MAEvC,IAAIkD,EAAE,CAAC3D,MAAM,EAAE;QACb2D,EAAE,GAAGA,EAAE,CAACG,IAAI,CAAC,CAACC,CAAK,EAAEC,CAAK,KAAKP,GAAG,CAACM,CAAC,CAAC,GAAGN,GAAG,CAACO,CAAC,CAAC,CAAC;QAC/C,MAAMC,EAAE,GAAG,CAACN,EAAE,CAAC3D,MAAM,GAAG,CAAC,IAAI,CAAC;QAC9B,KAAK,IAAII,CAAC,GAAG8D,IAAI,CAACC,KAAK,CAACF,EAAE,CAAC,EAAEG,EAAE,GAAGF,IAAI,CAACG,IAAI,CAACJ,EAAE,CAAC,EAAE7D,CAAC,IAAIgE,EAAE,EAAE,EAAEhE,CAAC,EAAE;UAC7D,MAAMC,CAAC,GAAGsD,EAAE,CAACvD,CAAC,CAAC;UACf,IACEoD,KAAK,CAACrD,CAAC,CAAC,KAAKA,CAAC,IACduD,OAAO,GAAGD,GAAG,CAACpD,CAAC,CAAC,IAChB,CAAC+C,WAAW,CAAC3D,SAAS,EAAEU,CAAC,EAAEE,CAAC,CAAC,EAC7B;YACAmD,KAAK,CAACnD,CAAC,CAAC,GAAGF,CAAC;YACZqD,KAAK,CAACrD,CAAC,CAAC,GAAGoD,IAAI,CAACpD,CAAC,CAAC,GAAGoD,IAAI,CAAClD,CAAC,CAAC;YAC5BqD,OAAO,GAAGD,GAAG,CAACpD,CAAC,CAAC;;;;IAIxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAO;IAAEkD,IAAI;IAAEC;EAAK,CAAE;AACxB,CAAC;AAED,OAAO,MAAMc,oBAAoB,GAAGA,CAClC/E,CAAS,EACTC,QAAgB,EAChB+D,IAAoB,EACpBC,KAAqB,EACrBe,OAAe,EACfC,OAAe,EACfC,UAAoB,KAClB;;EACF;EACA;EACA;EACA;EACA;EACA,MAAMC,EAAE,GAAuB,EAAE;EACjC,MAAMC,MAAM,GAAGC,eAAe,CAC5BrF,CAAC,EACDC,QAAQ,EACR+D,IAAI,EACJgB,OAAO,EACPC,OAAO,EACPC,UAAU,CACX;EACD,MAAMI,UAAU,GAAGJ,UAAU,GAAG,YAAY,GAAG,aAAa;EAE5D,MAAMK,OAAO,GAAGA,CACdC,SAA8B,EAC9BC,aAA4C,KAC1C;IACF,IAAIC,KAAK,GAAGN,MAAM,CAACO,WAAW,EAAE;IAChC,IAAIC,IAAI,GAAGF,KAAK,CAACG,GAAG,EAAE;IACtB,MAAMC,OAAO,GAAwB,EAAE;IACvC,OAAOF,IAAI,EAAE;MACX,IAAIE,OAAO,CAACF,IAAI,CAAC1E,EAAE,CAAC,EAAE;QACpBsE,SAAS,CAACI,IAAI,CAAC1E,EAAE,CAAC;OACnB,MAAM;QACL4E,OAAO,CAACF,IAAI,CAAC1E,EAAE,CAAC,GAAG,IAAI;QACvBwE,KAAK,CAACK,IAAI,CAACH,IAAI,CAAC;QAChBF,KAAK,GAAGA,KAAK,CAACM,MAAM,CAACP,aAAa,CAACG,IAAI,CAAC1E,EAAE,CAAC,CAAC;;MAG9C0E,IAAI,GAAGF,KAAK,CAACG,GAAG,EAAE;;EAEtB,CAAC;EAED;EACA,MAAMI,KAAK,GAAIL,IAAQ,IAAI;IACzBT,EAAE,CAACS,IAAI,CAAC,GAAG,CAACR,MAAM,CAACc,eAAe,CAACN,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE9D,MAAM,CAC1D,CAACqE,GAAW,EAAEC,CAAC,KAAI;MACjB,OAAOzB,IAAI,CAAC0B,GAAG,CAACF,GAAG,EAAE,CAAChB,EAAE,CAACiB,CAAC,CAACE,MAAM,CAAC,IAAI,CAAC,IAAIF,CAAC,CAACjF,IAAI,CAACoF,MAAO,CAAC;IAC5D,CAAC,EACD,CAAC,CACF;EACH,CAAC;EAED;EACA,MAAMC,KAAK,GAAIZ,IAAQ,IAAI;IACzB,MAAMa,GAAG,GAAG,CAACrB,MAAM,CAACc,eAAe,CAACN,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE9D,MAAM,CAC5D,CAACqE,GAAW,EAAEC,CAAC,KAAI;MACjB,OAAOzB,IAAI,CAAC8B,GAAG,CAACN,GAAG,EAAE,CAAChB,EAAE,CAACiB,CAAC,CAACM,MAAM,CAAC,IAAI,CAAC,IAAIN,CAAC,CAACjF,IAAI,CAACoF,MAAO,CAAC;IAC5D,CAAC,EACDI,MAAM,CAACC,iBAAiB,CACzB;IAED,MAAMC,IAAI,GAAG7G,CAAC,CAACiB,OAAO,CAAC2E,IAAI,CAAE;IAC7B,IACEa,GAAG,KAAKE,MAAM,CAACC,iBAAiB,IAChCC,IAAI,CAAC1F,IAAI,CAACmE,UAAU,KAAKA,UAAU,EACnC;MACAH,EAAE,CAACS,IAAI,CAAC,GAAGjB,IAAI,CAAC0B,GAAG,CAAClB,EAAE,CAACS,IAAI,CAAC,EAAEa,GAAG,CAAC;;EAEtC,CAAC;EAEDlB,OAAO,CAACU,KAAK,EAAEb,MAAM,CAAC5D,eAAe,CAACsF,IAAI,CAAC1B,MAAM,CAAC,CAAC;EACnDG,OAAO,CAACiB,KAAK,EAAEpB,MAAM,CAAC2B,aAAa,CAACD,IAAI,CAAC1B,MAAM,CAAC,CAAC;EAEjD;EACA,CAAA/D,EAAA,GAAA2F,MAAM,CAACC,MAAM,CAAChD,KAAK,CAAC,cAAA5C,EAAA,uBAAAA,EAAA,CAAEV,OAAO,CAAEC,CAAC,IAAI;IAClCuE,EAAE,CAACvE,CAAC,CAAC,GAAGuE,EAAE,CAACnB,IAAI,CAACpD,CAAC,CAAC,CAAC;EACrB,CAAC,CAAC;EAEF,OAAOuE,EAAE;AACX,CAAC;AAED,OAAO,MAAME,eAAe,GAAGA,CAC7BrF,CAAS,EACTC,QAAgB,EAChB+D,IAAoB,EACpBgB,OAAe,EACfC,OAAe,EACfC,UAAoB,KACV;EACV,MAAMgC,UAAU,GAAG,IAAItH,KAAK,EAAsB;EAClD,MAAMuH,KAAK,GAAGC,GAAG,CAACpC,OAAO,EAAEC,OAAO,EAAEC,UAAqB,CAAC;EAE1DjF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,OAAO,CAAEN,KAAK,IAAI;IAC1B,IAAIoB,CAAK;IACTpB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,OAAO,CAAEC,CAAC,IAAI;MACnB,MAAMyG,KAAK,GAAGrD,IAAI,CAACpD,CAAC,CAAC;MACrB,IAAI,CAACsG,UAAU,CAACI,OAAO,CAACD,KAAK,CAAC,EAAE;QAC9BH,UAAU,CAACK,OAAO,CAAC;UACjBrG,EAAE,EAAEmG,KAAK;UACTlG,IAAI,EAAE;SACP,CAAC;;MAEJ,IAAIM,CAAC,EAAE;QACL,MAAM+F,KAAK,GAAGxD,IAAI,CAACvC,CAAC,CAAC;QAErB,MAAMgG,IAAI,GAAGP,UAAU,CACpBhB,eAAe,CAACsB,KAAK,EAAE,KAAK,CAAC,CAC7BhE,IAAI,CAAEiE,IAAI,IAAKA,IAAI,CAACf,MAAM,KAAKW,KAAK,CAAC;QACxC,IAAI,CAACI,IAAI,EAAE;UACTP,UAAU,CAACQ,OAAO,CAAC;YACjBxG,EAAE,EAAE,IAAIyD,IAAI,CAACgD,MAAM,EAAE,EAAE;YACvBrB,MAAM,EAAEkB,KAAK;YACbd,MAAM,EAAEW,KAAK;YACblG,IAAI,EAAE;cACJoF,MAAM,EAAE5B,IAAI,CAAC0B,GAAG,CAACc,KAAK,CAACnH,CAAC,EAAEY,CAAC,EAAEa,CAAC,CAAC,EAAE,CAAC;;WAErC,CAAC;SACH,MAAM;UACLyF,UAAU,CAACU,cAAc,CAACH,IAAI,CAACvG,EAAE,EAAA8F,MAAA,CAAAa,MAAA,CAAAb,MAAA,CAAAa,MAAA,KAC5BJ,IAAI,CAACtG,IAAI;YACZoF,MAAM,EAAE5B,IAAI,CAAC0B,GAAG,CAACc,KAAK,CAACnH,CAAC,EAAEY,CAAC,EAAEa,CAAC,CAAC,EAAEgG,IAAI,CAACtG,IAAI,CAACoF,MAAO,IAAI,CAAC;UAAC,GACxD;;;MAGN9E,CAAC,GAAGb,CAAC;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOsG,UAAU;AACnB,CAAC;AACD;;;AAGA,OAAO,MAAMY,0BAA0B,GAAGA,CACxC9H,CAAS,EACT+H,GAA2C,KACzC;EACF,OAAOjI,KAAK,CAACkH,MAAM,CAACC,MAAM,CAACc,GAAG,CAAC,EAAG5C,EAAE,IAAI;;IACtC,IAAIkB,GAAG,GAAGM,MAAM,CAACqB,iBAAiB;IAClC,IAAIvB,GAAG,GAAGE,MAAM,CAACC,iBAAiB;IAElC,CAAAvF,EAAA,GAAA2F,MAAM,CAACiB,IAAI,CAAC9C,EAAE,CAAC,cAAA9D,EAAA,uBAAAA,EAAA,CAAEV,OAAO,CAAEC,CAAS,IAAI;MACrC,MAAMsH,CAAC,GAAG/C,EAAE,CAACvE,CAAC,CAAC;MACf,MAAMuH,SAAS,GAAGC,KAAK,CAACpI,CAAC,EAAEY,CAAC,CAAC,GAAG,CAAC;MAEjCyF,GAAG,GAAG1B,IAAI,CAAC0B,GAAG,CAAC6B,CAAC,GAAGC,SAAS,EAAE9B,GAAG,CAAC;MAClCI,GAAG,GAAG9B,IAAI,CAAC8B,GAAG,CAACyB,CAAC,GAAGC,SAAS,EAAE1B,GAAG,CAAC;IACpC,CAAC,CAAC;IAEF,OAAOJ,GAAG,GAAGI,GAAG;EAClB,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;AAOA,OAAM,SAAU4B,gBAAgBA,CAC9BN,GAA2C,EAC3CO,OAA+B;EAE/B,MAAMC,WAAW,GAAGvB,MAAM,CAACC,MAAM,CAACqB,OAAO,CAAC;EAC1C,MAAME,UAAU,GAAG7D,IAAI,CAAC8B,GAAG,CAAC,GAAG8B,WAAW,CAAC;EAC3C,MAAME,UAAU,GAAG9D,IAAI,CAAC0B,GAAG,CAAC,GAAGkC,WAAW,CAAC;EAE3C,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC5H,OAAO,CAAE+H,IAAI,IAAI;IAC1B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC/H,OAAO,CAAEgI,KAAK,IAAI;MAC3B,MAAMC,SAAS,GAAGF,IAAI,GAAGC,KAAK;MAC9B,MAAMxD,EAAE,GAAG4C,GAAG,CAACa,SAAS,CAAC;MACzB,IAAIC,KAAa;MACjB,IAAI1D,EAAE,KAAKmD,OAAO,EAAE;MAEpB,MAAMQ,MAAM,GAAG9B,MAAM,CAACC,MAAM,CAAC9B,EAAE,CAAC;MAChC0D,KAAK,GACHF,KAAK,KAAK,GAAG,GACTH,UAAU,GAAG7D,IAAI,CAAC8B,GAAG,CAAC,GAAGqC,MAAM,CAAC,GAChCL,UAAU,GAAG9D,IAAI,CAAC0B,GAAG,CAAC,GAAGyC,MAAM,CAAC;MAEtC,IAAID,KAAK,EAAE;QACTd,GAAG,CAACa,SAAS,CAAC,GAAG,EAAE;QACnB5B,MAAM,CAACiB,IAAI,CAAC9C,EAAE,CAAC,CAACxE,OAAO,CAAEoI,GAAG,IAAI;UAC9BhB,GAAG,CAACa,SAAS,CAAC,CAACG,GAAG,CAAC,GAAG5D,EAAE,CAAC4D,GAAG,CAAC,GAAGF,KAAK;QACvC,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,OAAO,MAAMG,OAAO,GAAGA,CACrBjB,GAA2C,EAC3C9D,KAAkB,KAChB;EACF,MAAMgF,MAAM,GAA2B,EAAE;EACzCjC,MAAM,CAACiB,IAAI,CAACF,GAAG,CAACmB,EAAE,CAAC,CAACvI,OAAO,CAAEoI,GAAG,IAAI;IAClC,IAAI9E,KAAK,EAAE;MACTgF,MAAM,CAACF,GAAG,CAAC,GAAGhB,GAAG,CAAC9D,KAAK,CAACkF,WAAW,EAAE,CAAC,CAACJ,GAAG,CAAC;KAC5C,MAAM;MACL,MAAM9B,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACc,GAAG,CAAC,CAAC1D,GAAG,CAAE6D,CAAC,IAAKA,CAAC,CAACa,GAAG,CAAC,CAAC;MACpDE,MAAM,CAACF,GAAG,CAAC,GAAG,CAAC9B,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE/C,CAAC,CAAC;EACF,OAAOgC,MAAM;AACf,CAAC;AAED,OAAO,MAAMG,SAAS,GAAGA,CACvBpJ,CAAS,EACTqJ,OAIE,KACA;EACF,MAAM;IAAEpF,KAAK,EAAEqF,UAAU;IAAEtE,OAAO,GAAG,CAAC;IAAEC,OAAO,GAAG;EAAC,CAAE,GAAGoE,OAAO,IAAI,EAAE;EAErE,MAAMpJ,QAAQ,GAAGJ,gBAAgB,CAACG,CAAC,CAAC;EACpC,MAAME,SAAS,GAAG8G,MAAM,CAACa,MAAM,CAC7B9H,kBAAkB,CAACC,CAAC,EAAEC,QAAQ,CAAC,EAC/B8B,kBAAkB,CAAC/B,CAAC,EAAEC,QAAQ,CAAC,CAChC;EAED,MAAM8H,GAAG,GAAuC,EAAE;EAClD,IAAIwB,gBAAwB;EAC5B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC5I,OAAO,CAAE+H,IAAI,IAAI;IAC1Ba,gBAAgB,GACdb,IAAI,KAAK,GAAG,GAAGzI,QAAQ,GAAG+G,MAAM,CAACC,MAAM,CAAChH,QAAQ,CAAC,CAACuJ,OAAO,EAAE;IAC7D,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC7I,OAAO,CAAEgI,KAAK,IAAI;MAC3B,IAAIA,KAAK,KAAK,GAAG,EAAE;QACjBY,gBAAgB,GAAGA,gBAAgB,CAAClF,GAAG,CAAEoF,KAAK,IAC5CzC,MAAM,CAACC,MAAM,CAACwC,KAAK,CAAC,CAACD,OAAO,EAAE,CAC/B;;MAGH,MAAMzF,UAAU,GAAG,CACjB2E,IAAI,KAAK,GAAG,GAAG1I,CAAC,CAACwB,eAAe,GAAGxB,CAAC,CAAC+G,aAAa,EAClDD,IAAI,CAAC9G,CAAC,CAAC;MACT,MAAMiE,KAAK,GAAGH,iBAAiB,CAC7B9D,CAAC,EACDuJ,gBAAgB,EAChBrJ,SAAS,EACT6D,UAAU,CACX;MACD,MAAMoB,EAAE,GAAGJ,oBAAoB,CAC7B/E,CAAC,EACDuJ,gBAAgB,EAChBtF,KAAK,CAACD,IAAI,EACVC,KAAK,CAACA,KAAK,EACXe,OAAO,EACPC,OAAO,EACP0D,KAAK,KAAK,GAAG,CACd;MACD,IAAIA,KAAK,KAAK,GAAG,EAAE;QACjB3B,MAAM,CAACiB,IAAI,CAAC9C,EAAE,CAAC,CAACxE,OAAO,CAAEoI,GAAG,IAAI;UAC9B5D,EAAE,CAAC4D,GAAG,CAAC,GAAG,CAAC5D,EAAE,CAAC4D,GAAG,CAAC;QACpB,CAAC,CAAC;;MAEJhB,GAAG,CAACW,IAAI,GAAGC,KAAK,CAAC,GAAGxD,EAAE;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAMuE,aAAa,GAAG5B,0BAA0B,CAAC9H,CAAC,EAAE+H,GAAG,CAAC;EACxDM,gBAAgB,CAACN,GAAG,EAAE2B,aAAa,CAAC;EACpC,OAAOV,OAAO,CAACjB,GAAG,EAAEuB,UAAU,CAAC;AACjC,CAAC;AAED,OAAO,MAAMlC,GAAG,GAAGA,CAACuC,OAAe,EAAEC,OAAe,EAAE1E,UAAmB,KAAI;EAC3E,OAAO,CAAClF,CAAS,EAAEY,CAAK,EAAEE,CAAK,KAAI;IACjC,MAAM+I,MAAM,GAAG7J,CAAC,CAACiB,OAAO,CAACL,CAAC,CAAE;IAC5B,MAAMkJ,MAAM,GAAG9J,CAAC,CAACiB,OAAO,CAACH,CAAC,CAAE;IAC5B,IAAIiJ,GAAG,GAAG,CAAC;IACX,IAAIlB,KAAK,GAAW,CAAC;IAErBkB,GAAG,IAAIF,MAAM,CAAC1I,IAAI,CAACiH,KAAM,GAAG,CAAC;IAC7B,IAAIyB,MAAM,CAAC1I,IAAI,CAAC6I,cAAc,CAAC,UAAU,CAAC,EAAE;MAC1C,QAAQ,CAAEH,MAAM,CAAC1I,IAAI,CAAC8I,QAAmB,IAAI,EAAE,EAAEd,WAAW,EAAE;QAC5D,KAAK,GAAG;UACNN,KAAK,GAAG,CAACgB,MAAM,CAAC1I,IAAI,CAACiH,KAAM,GAAG,CAAC;UAC/B;QACF,KAAK,GAAG;UACNS,KAAK,GAAGgB,MAAM,CAAC1I,IAAI,CAACiH,KAAM,GAAG,CAAC;UAC9B;;;IAGN,IAAIS,KAAK,EAAE;MACTkB,GAAG,IAAI7E,UAAU,GAAG2D,KAAK,GAAG,CAACA,KAAK;;IAEpCA,KAAK,GAAG,CAAC;IAETkB,GAAG,IAAI,CAACF,MAAM,CAAC1I,IAAI,CAACS,KAAK,GAAGgI,OAAO,GAAGD,OAAO,IAAI,CAAC;IAClDI,GAAG,IAAI,CAACD,MAAM,CAAC3I,IAAI,CAACS,KAAK,GAAGgI,OAAO,GAAGD,OAAO,IAAI,CAAC;IAElDI,GAAG,IAAID,MAAM,CAAC3I,IAAI,CAACiH,KAAM,GAAG,CAAC;IAC7B,IAAI0B,MAAM,CAAC3I,IAAI,CAAC8I,QAAQ,EAAE;MACxB,QAAQ,CAAEH,MAAM,CAAC3I,IAAI,CAAC8I,QAAmB,IAAI,EAAE,EAAEd,WAAW,EAAE;QAC5D,KAAK,GAAG;UACNN,KAAK,GAAGiB,MAAM,CAAC3I,IAAI,CAACiH,KAAM,GAAG,CAAC;UAC9B;QACF,KAAK,GAAG;UACNS,KAAK,GAAG,CAACiB,MAAM,CAAC3I,IAAI,CAACiH,KAAM,GAAG,CAAC;UAC/B;;;IAGN,IAAIS,KAAK,EAAE;MACTkB,GAAG,IAAI7E,UAAU,GAAG2D,KAAK,GAAG,CAACA,KAAK;;IAEpCA,KAAK,GAAG,CAAC;IAET,OAAOkB,GAAG;EACZ,CAAC;AACH,CAAC;AAED,OAAO,MAAM3B,KAAK,GAAGA,CAACpI,CAAS,EAAEY,CAAK,KAAKZ,CAAC,CAACiB,OAAO,CAACL,CAAC,CAAE,CAACO,IAAI,CAACiH,KAAM,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}