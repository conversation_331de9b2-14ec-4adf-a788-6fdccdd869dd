{"ast": null, "code": "var topsort = require(\"./topsort\");\nmodule.exports = isAcyclic;\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof topsort.CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["topsort", "require", "module", "exports", "isAcyclic", "g", "e", "CycleException"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/is-acyclic.js"], "sourcesContent": ["var topsort = require(\"./topsort\");\n\nmodule.exports = isAcyclic;\n\nfunction isAcyclic(g) {\n  try {\n    topsort(g);\n  } catch (e) {\n    if (e instanceof topsort.CycleException) {\n      return false;\n    }\n    throw e;\n  }\n  return true;\n}\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AAElCC,MAAM,CAACC,OAAO,GAAGC,SAAS;AAE1B,SAASA,SAASA,CAACC,CAAC,EAAE;EACpB,IAAI;IACFL,OAAO,CAACK,CAAC,CAAC;EACZ,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,IAAIA,CAAC,YAAYN,OAAO,CAACO,cAAc,EAAE;MACvC,OAAO,KAAK;IACd;IACA,MAAMD,CAAC;EACT;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}