{"ast": null, "code": "var _jsxFileName = \"/root/mes-system/mes-frontend/src/components/Common/ErrorBoundary.tsx\";\nimport React, { Component } from 'react';\nimport { Result, Button, Typography, Card } from 'antd';\nimport { ReloadOutlined, BugOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Paragraph,\n  Text\n} = Typography;\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.handleReload = () => {\n      window.location.reload();\n    };\n    this.handleReset = () => {\n      this.setState({\n        hasError: false,\n        error: undefined,\n        errorInfo: undefined\n      });\n    };\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    this.setState({\n      error,\n      errorInfo\n    });\n\n    // 在开发环境下打印错误信息\n    if (process.env.NODE_ENV === 'development') {\n      console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n\n    // 这里可以添加错误上报逻辑\n    // reportError(error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          minHeight: '100vh',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          padding: '20px'\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          style: {\n            maxWidth: '600px',\n            width: '100%'\n          },\n          children: /*#__PURE__*/_jsxDEV(Result, {\n            status: \"error\",\n            title: \"\\u9875\\u9762\\u51FA\\u73B0\\u9519\\u8BEF\",\n            subTitle: \"\\u62B1\\u6B49\\uFF0C\\u9875\\u9762\\u9047\\u5230\\u4E86\\u4E00\\u4E9B\\u95EE\\u9898\\u3002\\u60A8\\u53EF\\u4EE5\\u5C1D\\u8BD5\\u5237\\u65B0\\u9875\\u9762\\u6216\\u8054\\u7CFB\\u6280\\u672F\\u652F\\u6301\\u3002\",\n            extra: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 46\n              }, this),\n              onClick: this.handleReload,\n              children: \"\\u5237\\u65B0\\u9875\\u9762\"\n            }, \"reload\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(BugOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 31\n              }, this),\n              onClick: this.handleReset,\n              children: \"\\u91CD\\u8BD5\"\n            }, \"reset\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 17\n            }, this)],\n            children: process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'left',\n                marginTop: '20px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                children: [/*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u9519\\u8BEF\\u8BE6\\u60C5\\uFF08\\u5F00\\u53D1\\u6A21\\u5F0F\\uFF09\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 83,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: /*#__PURE__*/_jsxDEV(Text, {\n                    code: true,\n                    children: this.state.error.message\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 86,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 21\n                }, this), this.state.error.stack && /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u9519\\u8BEF\\u5806\\u6808\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 90,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                    style: {\n                      background: '#f5f5f5',\n                      padding: '10px',\n                      borderRadius: '4px',\n                      fontSize: '12px',\n                      overflow: 'auto',\n                      maxHeight: '200px'\n                    },\n                    children: this.state.error.stack\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 23\n                }, this), this.state.errorInfo && /*#__PURE__*/_jsxDEV(Paragraph, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"\\u7EC4\\u4EF6\\u5806\\u6808\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                    style: {\n                      background: '#f5f5f5',\n                      padding: '10px',\n                      borderRadius: '4px',\n                      fontSize: '12px',\n                      overflow: 'auto',\n                      maxHeight: '200px'\n                    },\n                    children: this.state.errorInfo.componentStack\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "Result", "<PERSON><PERSON>", "Typography", "Card", "ReloadOutlined", "BugOutlined", "jsxDEV", "_jsxDEV", "Paragraph", "Text", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "handleReload", "window", "location", "reload", "handleReset", "setState", "<PERSON><PERSON><PERSON><PERSON>", "error", "undefined", "errorInfo", "state", "getDerivedStateFromError", "componentDidCatch", "process", "env", "NODE_ENV", "console", "render", "fallback", "style", "minHeight", "display", "alignItems", "justifyContent", "padding", "children", "max<PERSON><PERSON><PERSON>", "width", "status", "title", "subTitle", "extra", "type", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "textAlign", "marginTop", "strong", "code", "message", "stack", "background", "borderRadius", "fontSize", "overflow", "maxHeight", "componentStack"], "sources": ["/root/mes-system/mes-frontend/src/components/Common/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { Result, Button, Typography, Card } from 'antd';\nimport { ReloadOutlined, BugOutlined } from '@ant-design/icons';\n\nconst { Paragraph, Text } = Typography;\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // 在开发环境下打印错误信息\n    if (process.env.NODE_ENV === 'development') {\n      console.error('ErrorBoundary caught an error:', error, errorInfo);\n    }\n\n    // 这里可以添加错误上报逻辑\n    // reportError(error, errorInfo);\n  }\n\n  handleReload = () => {\n    window.location.reload();\n  };\n\n  handleReset = () => {\n    this.setState({ hasError: false, error: undefined, errorInfo: undefined });\n  };\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <div style={{ \n          minHeight: '100vh', \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'center',\n          padding: '20px',\n        }}>\n          <Card style={{ maxWidth: '600px', width: '100%' }}>\n            <Result\n              status=\"error\"\n              title=\"页面出现错误\"\n              subTitle=\"抱歉，页面遇到了一些问题。您可以尝试刷新页面或联系技术支持。\"\n              extra={[\n                <Button type=\"primary\" icon={<ReloadOutlined />} onClick={this.handleReload} key=\"reload\">\n                  刷新页面\n                </Button>,\n                <Button icon={<BugOutlined />} onClick={this.handleReset} key=\"reset\">\n                  重试\n                </Button>,\n              ]}\n            >\n              {process.env.NODE_ENV === 'development' && this.state.error && (\n                <div style={{ textAlign: 'left', marginTop: '20px' }}>\n                  <Typography>\n                    <Paragraph>\n                      <Text strong>错误详情（开发模式）：</Text>\n                    </Paragraph>\n                    <Paragraph>\n                      <Text code>{this.state.error.message}</Text>\n                    </Paragraph>\n                    {this.state.error.stack && (\n                      <Paragraph>\n                        <Text strong>错误堆栈：</Text>\n                        <pre style={{ \n                          background: '#f5f5f5', \n                          padding: '10px', \n                          borderRadius: '4px',\n                          fontSize: '12px',\n                          overflow: 'auto',\n                          maxHeight: '200px',\n                        }}>\n                          {this.state.error.stack}\n                        </pre>\n                      </Paragraph>\n                    )}\n                    {this.state.errorInfo && (\n                      <Paragraph>\n                        <Text strong>组件堆栈：</Text>\n                        <pre style={{ \n                          background: '#f5f5f5', \n                          padding: '10px', \n                          borderRadius: '4px',\n                          fontSize: '12px',\n                          overflow: 'auto',\n                          maxHeight: '200px',\n                        }}>\n                          {this.state.errorInfo.componentStack}\n                        </pre>\n                      </Paragraph>\n                    )}\n                  </Typography>\n                </div>\n              )}\n            </Result>\n          </Card>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAC9D,SAASC,MAAM,EAAEC,MAAM,EAAEC,UAAU,EAAEC,IAAI,QAAQ,MAAM;AACvD,SAASC,cAAc,EAAEC,WAAW,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAM;EAAEC,SAAS;EAAEC;AAAK,CAAC,GAAGP,UAAU;AAatC,MAAMQ,aAAa,SAASX,SAAS,CAAe;EAClDY,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IAAC,KAuBfC,YAAY,GAAG,MAAM;MACnBC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAAA,KAEDC,WAAW,GAAG,MAAM;MAClB,IAAI,CAACC,QAAQ,CAAC;QAAEC,QAAQ,EAAE,KAAK;QAAEC,KAAK,EAAEC,SAAS;QAAEC,SAAS,EAAED;MAAU,CAAC,CAAC;IAC5E,CAAC;IA5BC,IAAI,CAACE,KAAK,GAAG;MAAEJ,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOK,wBAAwBA,CAACJ,KAAY,EAAS;IACnD,OAAO;MAAED,QAAQ,EAAE,IAAI;MAAEC;IAAM,CAAC;EAClC;EAEAK,iBAAiBA,CAACL,KAAY,EAAEE,SAAoB,EAAE;IACpD,IAAI,CAACJ,QAAQ,CAAC;MACZE,KAAK;MACLE;IACF,CAAC,CAAC;;IAEF;IACA,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1CC,OAAO,CAACT,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEE,SAAS,CAAC;IACnE;;IAEA;IACA;EACF;EAUAQ,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACP,KAAK,CAACJ,QAAQ,EAAE;MACvB,IAAI,IAAI,CAACP,KAAK,CAACmB,QAAQ,EAAE;QACvB,OAAO,IAAI,CAACnB,KAAK,CAACmB,QAAQ;MAC5B;MAEA,oBACExB,OAAA;QAAKyB,KAAK,EAAE;UACVC,SAAS,EAAE,OAAO;UAClBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,OAAO,EAAE;QACX,CAAE;QAAAC,QAAA,eACA/B,OAAA,CAACJ,IAAI;UAAC6B,KAAK,EAAE;YAAEO,QAAQ,EAAE,OAAO;YAAEC,KAAK,EAAE;UAAO,CAAE;UAAAF,QAAA,eAChD/B,OAAA,CAACP,MAAM;YACLyC,MAAM,EAAC,OAAO;YACdC,KAAK,EAAC,sCAAQ;YACdC,QAAQ,EAAC,sLAAgC;YACzCC,KAAK,EAAE,cACLrC,OAAA,CAACN,MAAM;cAAC4C,IAAI,EAAC,SAAS;cAACC,IAAI,eAAEvC,OAAA,CAACH,cAAc;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACC,OAAO,EAAE,IAAI,CAACtC,YAAa;cAAAyB,QAAA,EAAc;YAE1F,GAFiF,QAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEjF,CAAC,eACT3C,OAAA,CAACN,MAAM;cAAC6C,IAAI,eAAEvC,OAAA,CAACF,WAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAACC,OAAO,EAAE,IAAI,CAAClC,WAAY;cAAAqB,QAAA,EAAa;YAEtE,GAF8D,OAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAE7D,CAAC,CACT;YAAAZ,QAAA,EAEDZ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAACL,KAAK,CAACH,KAAK,iBACzDb,OAAA;cAAKyB,KAAK,EAAE;gBAAEoB,SAAS,EAAE,MAAM;gBAAEC,SAAS,EAAE;cAAO,CAAE;cAAAf,QAAA,eACnD/B,OAAA,CAACL,UAAU;gBAAAoC,QAAA,gBACT/B,OAAA,CAACC,SAAS;kBAAA8B,QAAA,eACR/B,OAAA,CAACE,IAAI;oBAAC6C,MAAM;oBAAAhB,QAAA,EAAC;kBAAW;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC,eACZ3C,OAAA,CAACC,SAAS;kBAAA8B,QAAA,eACR/B,OAAA,CAACE,IAAI;oBAAC8C,IAAI;oBAAAjB,QAAA,EAAE,IAAI,CAACf,KAAK,CAACH,KAAK,CAACoC;kBAAO;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,EACX,IAAI,CAAC3B,KAAK,CAACH,KAAK,CAACqC,KAAK,iBACrBlD,OAAA,CAACC,SAAS;kBAAA8B,QAAA,gBACR/B,OAAA,CAACE,IAAI;oBAAC6C,MAAM;oBAAAhB,QAAA,EAAC;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzB3C,OAAA;oBAAKyB,KAAK,EAAE;sBACV0B,UAAU,EAAE,SAAS;sBACrBrB,OAAO,EAAE,MAAM;sBACfsB,YAAY,EAAE,KAAK;sBACnBC,QAAQ,EAAE,MAAM;sBAChBC,QAAQ,EAAE,MAAM;sBAChBC,SAAS,EAAE;oBACb,CAAE;oBAAAxB,QAAA,EACC,IAAI,CAACf,KAAK,CAACH,KAAK,CAACqC;kBAAK;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACZ,EACA,IAAI,CAAC3B,KAAK,CAACD,SAAS,iBACnBf,OAAA,CAACC,SAAS;kBAAA8B,QAAA,gBACR/B,OAAA,CAACE,IAAI;oBAAC6C,MAAM;oBAAAhB,QAAA,EAAC;kBAAK;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzB3C,OAAA;oBAAKyB,KAAK,EAAE;sBACV0B,UAAU,EAAE,SAAS;sBACrBrB,OAAO,EAAE,MAAM;sBACfsB,YAAY,EAAE,KAAK;sBACnBC,QAAQ,EAAE,MAAM;sBAChBC,QAAQ,EAAE,MAAM;sBAChBC,SAAS,EAAE;oBACb,CAAE;oBAAAxB,QAAA,EACC,IAAI,CAACf,KAAK,CAACD,SAAS,CAACyC;kBAAc;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CACZ;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEV;IAEA,OAAO,IAAI,CAACtC,KAAK,CAAC0B,QAAQ;EAC5B;AACF;AAEA,eAAe5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}