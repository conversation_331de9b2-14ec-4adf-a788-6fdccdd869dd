{"ast": null, "code": "var isArrayLike = function (value) {\n  /**\n   * isArrayLike([1, 2, 3]) => true\n   * isArrayLike(document.body.children) => true\n   * isArrayLike('abc') => true\n   * isArrayLike(Function) => false\n   */\n  return value !== null && typeof value !== 'function' && isFinite(value.length);\n};\nexport default isArrayLike;", "map": {"version": 3, "names": ["isArrayLike", "value", "isFinite", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-array-like.ts"], "sourcesContent": ["const isArrayLike = function(value: any): boolean {\n    /**\n     * isArrayLike([1, 2, 3]) => true\n     * isArrayLike(document.body.children) => true\n     * isArrayLike('abc') => true\n     * isArrayLike(Function) => false\n     */\n  return value !== null && typeof value !== 'function' && isFinite(value.length);\n};\n\nexport default isArrayLike;\n"], "mappings": "AAAA,IAAMA,WAAW,GAAG,SAAAA,CAASC,KAAU;EACnC;;;;;;EAMF,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAIC,QAAQ,CAACD,KAAK,CAACE,MAAM,CAAC;AAChF,CAAC;AAED,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}