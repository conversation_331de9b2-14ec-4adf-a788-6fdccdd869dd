{"ast": null, "code": "import { optional } from \"./accessors.js\";\nimport { Node, computeHeight } from \"./hierarchy/index.js\";\nvar preroot = {\n    depth: -1\n  },\n  ambiguous = {},\n  imputed = {};\nfunction defaultId(d) {\n  return d.id;\n}\nfunction defaultParentId(d) {\n  return d.parentId;\n}\nexport default function () {\n  var id = defaultId,\n    parentId = defaultParentId,\n    path;\n  function stratify(data) {\n    var nodes = Array.from(data),\n      currentId = id,\n      currentParentId = parentId,\n      n,\n      d,\n      i,\n      root,\n      parent,\n      node,\n      nodeId,\n      nodeKey,\n      nodeByKey = new Map();\n    if (path != null) {\n      const I = nodes.map((d, i) => normalize(path(d, i, data)));\n      const P = I.map(parentof);\n      const S = new Set(I).add(\"\");\n      for (const i of P) {\n        if (!S.has(i)) {\n          S.add(i);\n          I.push(i);\n          P.push(parentof(i));\n          nodes.push(imputed);\n        }\n      }\n      currentId = (_, i) => I[i];\n      currentParentId = (_, i) => P[i];\n    }\n    for (i = 0, n = nodes.length; i < n; ++i) {\n      d = nodes[i], node = nodes[i] = new Node(d);\n      if ((nodeId = currentId(d, i, data)) != null && (nodeId += \"\")) {\n        nodeKey = node.id = nodeId;\n        nodeByKey.set(nodeKey, nodeByKey.has(nodeKey) ? ambiguous : node);\n      }\n      if ((nodeId = currentParentId(d, i, data)) != null && (nodeId += \"\")) {\n        node.parent = nodeId;\n      }\n    }\n    for (i = 0; i < n; ++i) {\n      node = nodes[i];\n      if (nodeId = node.parent) {\n        parent = nodeByKey.get(nodeId);\n        if (!parent) throw new Error(\"missing: \" + nodeId);\n        if (parent === ambiguous) throw new Error(\"ambiguous: \" + nodeId);\n        if (parent.children) parent.children.push(node);else parent.children = [node];\n        node.parent = parent;\n      } else {\n        if (root) throw new Error(\"multiple roots\");\n        root = node;\n      }\n    }\n    if (!root) throw new Error(\"no root\");\n\n    // When imputing internal nodes, only introduce roots if needed.\n    // Then replace the imputed marker data with null.\n    if (path != null) {\n      while (root.data === imputed && root.children.length === 1) {\n        root = root.children[0], --n;\n      }\n      for (let i = nodes.length - 1; i >= 0; --i) {\n        node = nodes[i];\n        if (node.data !== imputed) break;\n        node.data = null;\n      }\n    }\n    root.parent = preroot;\n    root.eachBefore(function (node) {\n      node.depth = node.parent.depth + 1;\n      --n;\n    }).eachBefore(computeHeight);\n    root.parent = null;\n    if (n > 0) throw new Error(\"cycle\");\n    return root;\n  }\n  stratify.id = function (x) {\n    return arguments.length ? (id = optional(x), stratify) : id;\n  };\n  stratify.parentId = function (x) {\n    return arguments.length ? (parentId = optional(x), stratify) : parentId;\n  };\n  stratify.path = function (x) {\n    return arguments.length ? (path = optional(x), stratify) : path;\n  };\n  return stratify;\n}\n\n// To normalize a path, we coerce to a string, strip the trailing slash if any\n// (as long as the trailing slash is not immediately preceded by another slash),\n// and add leading slash if missing.\nfunction normalize(path) {\n  path = `${path}`;\n  let i = path.length;\n  if (slash(path, i - 1) && !slash(path, i - 2)) path = path.slice(0, -1);\n  return path[0] === \"/\" ? path : `/${path}`;\n}\n\n// Walk backwards to find the first slash that is not the leading slash, e.g.:\n// \"/foo/bar\" ⇥ \"/foo\", \"/foo\" ⇥ \"/\", \"/\" ↦ \"\". (The root is special-cased\n// because the id of the root must be a truthy value.)\nfunction parentof(path) {\n  let i = path.length;\n  if (i < 2) return \"\";\n  while (--i > 1) if (slash(path, i)) break;\n  return path.slice(0, i);\n}\n\n// Slashes can be escaped; to determine whether a slash is a path delimiter, we\n// count the number of preceding backslashes escaping the forward slash: an odd\n// number indicates an escaped forward slash.\nfunction slash(path, i) {\n  if (path[i] === \"/\") {\n    let k = 0;\n    while (i > 0 && path[--i] === \"\\\\\") ++k;\n    if ((k & 1) === 0) return true;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["optional", "Node", "computeHeight", "preroot", "depth", "ambiguous", "imputed", "defaultId", "d", "id", "defaultParentId", "parentId", "path", "stratify", "data", "nodes", "Array", "from", "currentId", "currentParentId", "n", "i", "root", "parent", "node", "nodeId", "nodeKey", "nodeByKey", "Map", "I", "map", "normalize", "P", "parentof", "S", "Set", "add", "has", "push", "_", "length", "set", "get", "Error", "children", "eachBefore", "x", "arguments", "slash", "slice", "k"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/stratify.js"], "sourcesContent": ["import {optional} from \"./accessors.js\";\nimport {Node, computeHeight} from \"./hierarchy/index.js\";\n\nvar preroot = {depth: -1},\n    ambiguous = {},\n    imputed = {};\n\nfunction defaultId(d) {\n  return d.id;\n}\n\nfunction defaultParentId(d) {\n  return d.parentId;\n}\n\nexport default function() {\n  var id = defaultId,\n      parentId = defaultParentId,\n      path;\n\n  function stratify(data) {\n    var nodes = Array.from(data),\n        currentId = id,\n        currentParentId = parentId,\n        n,\n        d,\n        i,\n        root,\n        parent,\n        node,\n        nodeId,\n        nodeKey,\n        nodeByKey = new Map;\n\n    if (path != null) {\n      const I = nodes.map((d, i) => normalize(path(d, i, data)));\n      const P = I.map(parentof);\n      const S = new Set(I).add(\"\");\n      for (const i of P) {\n        if (!S.has(i)) {\n          S.add(i);\n          I.push(i);\n          P.push(parentof(i));\n          nodes.push(imputed);\n        }\n      }\n      currentId = (_, i) => I[i];\n      currentParentId = (_, i) => P[i];\n    }\n\n    for (i = 0, n = nodes.length; i < n; ++i) {\n      d = nodes[i], node = nodes[i] = new Node(d);\n      if ((nodeId = currentId(d, i, data)) != null && (nodeId += \"\")) {\n        nodeKey = node.id = nodeId;\n        nodeByKey.set(nodeKey, nodeByKey.has(nodeKey) ? ambiguous : node);\n      }\n      if ((nodeId = currentParentId(d, i, data)) != null && (nodeId += \"\")) {\n        node.parent = nodeId;\n      }\n    }\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i];\n      if (nodeId = node.parent) {\n        parent = nodeByKey.get(nodeId);\n        if (!parent) throw new Error(\"missing: \" + nodeId);\n        if (parent === ambiguous) throw new Error(\"ambiguous: \" + nodeId);\n        if (parent.children) parent.children.push(node);\n        else parent.children = [node];\n        node.parent = parent;\n      } else {\n        if (root) throw new Error(\"multiple roots\");\n        root = node;\n      }\n    }\n\n    if (!root) throw new Error(\"no root\");\n\n    // When imputing internal nodes, only introduce roots if needed.\n    // Then replace the imputed marker data with null.\n    if (path != null) {\n      while (root.data === imputed && root.children.length === 1) {\n        root = root.children[0], --n;\n      }\n      for (let i = nodes.length - 1; i >= 0; --i) {\n        node = nodes[i];\n        if (node.data !== imputed) break;\n        node.data = null;\n      }\n    }\n\n    root.parent = preroot;\n    root.eachBefore(function(node) { node.depth = node.parent.depth + 1; --n; }).eachBefore(computeHeight);\n    root.parent = null;\n    if (n > 0) throw new Error(\"cycle\");\n\n    return root;\n  }\n\n  stratify.id = function(x) {\n    return arguments.length ? (id = optional(x), stratify) : id;\n  };\n\n  stratify.parentId = function(x) {\n    return arguments.length ? (parentId = optional(x), stratify) : parentId;\n  };\n\n  stratify.path = function(x) {\n    return arguments.length ? (path = optional(x), stratify) : path;\n  };\n\n  return stratify;\n}\n\n// To normalize a path, we coerce to a string, strip the trailing slash if any\n// (as long as the trailing slash is not immediately preceded by another slash),\n// and add leading slash if missing.\nfunction normalize(path) {\n  path = `${path}`;\n  let i = path.length;\n  if (slash(path, i - 1) && !slash(path, i - 2)) path = path.slice(0, -1);\n  return path[0] === \"/\" ? path : `/${path}`;\n}\n\n// Walk backwards to find the first slash that is not the leading slash, e.g.:\n// \"/foo/bar\" ⇥ \"/foo\", \"/foo\" ⇥ \"/\", \"/\" ↦ \"\". (The root is special-cased\n// because the id of the root must be a truthy value.)\nfunction parentof(path) {\n  let i = path.length;\n  if (i < 2) return \"\";\n  while (--i > 1) if (slash(path, i)) break;\n  return path.slice(0, i);\n}\n\n// Slashes can be escaped; to determine whether a slash is a path delimiter, we\n// count the number of preceding backslashes escaping the forward slash: an odd\n// number indicates an escaped forward slash.\nfunction slash(path, i) {\n  if (path[i] === \"/\") {\n    let k = 0;\n    while (i > 0 && path[--i] === \"\\\\\") ++k;\n    if ((k & 1) === 0) return true;\n  }\n  return false;\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,gBAAgB;AACvC,SAAQC,IAAI,EAAEC,aAAa,QAAO,sBAAsB;AAExD,IAAIC,OAAO,GAAG;IAACC,KAAK,EAAE,CAAC;EAAC,CAAC;EACrBC,SAAS,GAAG,CAAC,CAAC;EACdC,OAAO,GAAG,CAAC,CAAC;AAEhB,SAASC,SAASA,CAACC,CAAC,EAAE;EACpB,OAAOA,CAAC,CAACC,EAAE;AACb;AAEA,SAASC,eAAeA,CAACF,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAACG,QAAQ;AACnB;AAEA,eAAe,YAAW;EACxB,IAAIF,EAAE,GAAGF,SAAS;IACdI,QAAQ,GAAGD,eAAe;IAC1BE,IAAI;EAER,SAASC,QAAQA,CAACC,IAAI,EAAE;IACtB,IAAIC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACH,IAAI,CAAC;MACxBI,SAAS,GAAGT,EAAE;MACdU,eAAe,GAAGR,QAAQ;MAC1BS,CAAC;MACDZ,CAAC;MACDa,CAAC;MACDC,IAAI;MACJC,MAAM;MACNC,IAAI;MACJC,MAAM;MACNC,OAAO;MACPC,SAAS,GAAG,IAAIC,GAAG,CAAD,CAAC;IAEvB,IAAIhB,IAAI,IAAI,IAAI,EAAE;MAChB,MAAMiB,CAAC,GAAGd,KAAK,CAACe,GAAG,CAAC,CAACtB,CAAC,EAAEa,CAAC,KAAKU,SAAS,CAACnB,IAAI,CAACJ,CAAC,EAAEa,CAAC,EAAEP,IAAI,CAAC,CAAC,CAAC;MAC1D,MAAMkB,CAAC,GAAGH,CAAC,CAACC,GAAG,CAACG,QAAQ,CAAC;MACzB,MAAMC,CAAC,GAAG,IAAIC,GAAG,CAACN,CAAC,CAAC,CAACO,GAAG,CAAC,EAAE,CAAC;MAC5B,KAAK,MAAMf,CAAC,IAAIW,CAAC,EAAE;QACjB,IAAI,CAACE,CAAC,CAACG,GAAG,CAAChB,CAAC,CAAC,EAAE;UACba,CAAC,CAACE,GAAG,CAACf,CAAC,CAAC;UACRQ,CAAC,CAACS,IAAI,CAACjB,CAAC,CAAC;UACTW,CAAC,CAACM,IAAI,CAACL,QAAQ,CAACZ,CAAC,CAAC,CAAC;UACnBN,KAAK,CAACuB,IAAI,CAAChC,OAAO,CAAC;QACrB;MACF;MACAY,SAAS,GAAGA,CAACqB,CAAC,EAAElB,CAAC,KAAKQ,CAAC,CAACR,CAAC,CAAC;MAC1BF,eAAe,GAAGA,CAACoB,CAAC,EAAElB,CAAC,KAAKW,CAAC,CAACX,CAAC,CAAC;IAClC;IAEA,KAAKA,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGL,KAAK,CAACyB,MAAM,EAAEnB,CAAC,GAAGD,CAAC,EAAE,EAAEC,CAAC,EAAE;MACxCb,CAAC,GAAGO,KAAK,CAACM,CAAC,CAAC,EAAEG,IAAI,GAAGT,KAAK,CAACM,CAAC,CAAC,GAAG,IAAIpB,IAAI,CAACO,CAAC,CAAC;MAC3C,IAAI,CAACiB,MAAM,GAAGP,SAAS,CAACV,CAAC,EAAEa,CAAC,EAAEP,IAAI,CAAC,KAAK,IAAI,KAAKW,MAAM,IAAI,EAAE,CAAC,EAAE;QAC9DC,OAAO,GAAGF,IAAI,CAACf,EAAE,GAAGgB,MAAM;QAC1BE,SAAS,CAACc,GAAG,CAACf,OAAO,EAAEC,SAAS,CAACU,GAAG,CAACX,OAAO,CAAC,GAAGrB,SAAS,GAAGmB,IAAI,CAAC;MACnE;MACA,IAAI,CAACC,MAAM,GAAGN,eAAe,CAACX,CAAC,EAAEa,CAAC,EAAEP,IAAI,CAAC,KAAK,IAAI,KAAKW,MAAM,IAAI,EAAE,CAAC,EAAE;QACpED,IAAI,CAACD,MAAM,GAAGE,MAAM;MACtB;IACF;IAEA,KAAKJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,EAAE,EAAEC,CAAC,EAAE;MACtBG,IAAI,GAAGT,KAAK,CAACM,CAAC,CAAC;MACf,IAAII,MAAM,GAAGD,IAAI,CAACD,MAAM,EAAE;QACxBA,MAAM,GAAGI,SAAS,CAACe,GAAG,CAACjB,MAAM,CAAC;QAC9B,IAAI,CAACF,MAAM,EAAE,MAAM,IAAIoB,KAAK,CAAC,WAAW,GAAGlB,MAAM,CAAC;QAClD,IAAIF,MAAM,KAAKlB,SAAS,EAAE,MAAM,IAAIsC,KAAK,CAAC,aAAa,GAAGlB,MAAM,CAAC;QACjE,IAAIF,MAAM,CAACqB,QAAQ,EAAErB,MAAM,CAACqB,QAAQ,CAACN,IAAI,CAACd,IAAI,CAAC,CAAC,KAC3CD,MAAM,CAACqB,QAAQ,GAAG,CAACpB,IAAI,CAAC;QAC7BA,IAAI,CAACD,MAAM,GAAGA,MAAM;MACtB,CAAC,MAAM;QACL,IAAID,IAAI,EAAE,MAAM,IAAIqB,KAAK,CAAC,gBAAgB,CAAC;QAC3CrB,IAAI,GAAGE,IAAI;MACb;IACF;IAEA,IAAI,CAACF,IAAI,EAAE,MAAM,IAAIqB,KAAK,CAAC,SAAS,CAAC;;IAErC;IACA;IACA,IAAI/B,IAAI,IAAI,IAAI,EAAE;MAChB,OAAOU,IAAI,CAACR,IAAI,KAAKR,OAAO,IAAIgB,IAAI,CAACsB,QAAQ,CAACJ,MAAM,KAAK,CAAC,EAAE;QAC1DlB,IAAI,GAAGA,IAAI,CAACsB,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAExB,CAAC;MAC9B;MACA,KAAK,IAAIC,CAAC,GAAGN,KAAK,CAACyB,MAAM,GAAG,CAAC,EAAEnB,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC1CG,IAAI,GAAGT,KAAK,CAACM,CAAC,CAAC;QACf,IAAIG,IAAI,CAACV,IAAI,KAAKR,OAAO,EAAE;QAC3BkB,IAAI,CAACV,IAAI,GAAG,IAAI;MAClB;IACF;IAEAQ,IAAI,CAACC,MAAM,GAAGpB,OAAO;IACrBmB,IAAI,CAACuB,UAAU,CAAC,UAASrB,IAAI,EAAE;MAAEA,IAAI,CAACpB,KAAK,GAAGoB,IAAI,CAACD,MAAM,CAACnB,KAAK,GAAG,CAAC;MAAE,EAAEgB,CAAC;IAAE,CAAC,CAAC,CAACyB,UAAU,CAAC3C,aAAa,CAAC;IACtGoB,IAAI,CAACC,MAAM,GAAG,IAAI;IAClB,IAAIH,CAAC,GAAG,CAAC,EAAE,MAAM,IAAIuB,KAAK,CAAC,OAAO,CAAC;IAEnC,OAAOrB,IAAI;EACb;EAEAT,QAAQ,CAACJ,EAAE,GAAG,UAASqC,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACP,MAAM,IAAI/B,EAAE,GAAGT,QAAQ,CAAC8C,CAAC,CAAC,EAAEjC,QAAQ,IAAIJ,EAAE;EAC7D,CAAC;EAEDI,QAAQ,CAACF,QAAQ,GAAG,UAASmC,CAAC,EAAE;IAC9B,OAAOC,SAAS,CAACP,MAAM,IAAI7B,QAAQ,GAAGX,QAAQ,CAAC8C,CAAC,CAAC,EAAEjC,QAAQ,IAAIF,QAAQ;EACzE,CAAC;EAEDE,QAAQ,CAACD,IAAI,GAAG,UAASkC,CAAC,EAAE;IAC1B,OAAOC,SAAS,CAACP,MAAM,IAAI5B,IAAI,GAAGZ,QAAQ,CAAC8C,CAAC,CAAC,EAAEjC,QAAQ,IAAID,IAAI;EACjE,CAAC;EAED,OAAOC,QAAQ;AACjB;;AAEA;AACA;AACA;AACA,SAASkB,SAASA,CAACnB,IAAI,EAAE;EACvBA,IAAI,GAAG,GAAGA,IAAI,EAAE;EAChB,IAAIS,CAAC,GAAGT,IAAI,CAAC4B,MAAM;EACnB,IAAIQ,KAAK,CAACpC,IAAI,EAAES,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC2B,KAAK,CAACpC,IAAI,EAAES,CAAC,GAAG,CAAC,CAAC,EAAET,IAAI,GAAGA,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACvE,OAAOrC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,IAAI,GAAG,IAAIA,IAAI,EAAE;AAC5C;;AAEA;AACA;AACA;AACA,SAASqB,QAAQA,CAACrB,IAAI,EAAE;EACtB,IAAIS,CAAC,GAAGT,IAAI,CAAC4B,MAAM;EACnB,IAAInB,CAAC,GAAG,CAAC,EAAE,OAAO,EAAE;EACpB,OAAO,EAAEA,CAAC,GAAG,CAAC,EAAE,IAAI2B,KAAK,CAACpC,IAAI,EAAES,CAAC,CAAC,EAAE;EACpC,OAAOT,IAAI,CAACqC,KAAK,CAAC,CAAC,EAAE5B,CAAC,CAAC;AACzB;;AAEA;AACA;AACA;AACA,SAAS2B,KAAKA,CAACpC,IAAI,EAAES,CAAC,EAAE;EACtB,IAAIT,IAAI,CAACS,CAAC,CAAC,KAAK,GAAG,EAAE;IACnB,IAAI6B,CAAC,GAAG,CAAC;IACT,OAAO7B,CAAC,GAAG,CAAC,IAAIT,IAAI,CAAC,EAAES,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE6B,CAAC;IACvC,IAAI,CAACA,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,OAAO,IAAI;EAChC;EACA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}