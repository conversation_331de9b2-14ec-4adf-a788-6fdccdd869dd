{"ast": null, "code": "import { optional } from \"../accessors.js\";\nimport constant, { constant<PERSON>ero } from \"../constant.js\";\nimport lcg from \"../lcg.js\";\nimport { packSiblingsRandom } from \"./siblings.js\";\nfunction defaultRadius(d) {\n  return Math.sqrt(d.value);\n}\nexport default function () {\n  var radius = null,\n    dx = 1,\n    dy = 1,\n    padding = constantZero;\n  function pack(root) {\n    const random = lcg();\n    root.x = dx / 2, root.y = dy / 2;\n    if (radius) {\n      root.eachBefore(radiusLeaf(radius)).eachAfter(packChildrenRandom(padding, 0.5, random)).eachBefore(translateChild(1));\n    } else {\n      root.eachBefore(radiusLeaf(defaultRadius)).eachAfter(packChildrenRandom(constantZero, 1, random)).eachAfter(packChildrenRandom(padding, root.r / Math.min(dx, dy), random)).eachBefore(translateChild(Math.min(dx, dy) / (2 * root.r)));\n    }\n    return root;\n  }\n  pack.radius = function (x) {\n    return arguments.length ? (radius = optional(x), pack) : radius;\n  };\n  pack.size = function (x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], pack) : [dx, dy];\n  };\n  pack.padding = function (x) {\n    return arguments.length ? (padding = typeof x === \"function\" ? x : constant(+x), pack) : padding;\n  };\n  return pack;\n}\nfunction radiusLeaf(radius) {\n  return function (node) {\n    if (!node.children) {\n      node.r = Math.max(0, +radius(node) || 0);\n    }\n  };\n}\nfunction packChildrenRandom(padding, k, random) {\n  return function (node) {\n    if (children = node.children) {\n      var children,\n        i,\n        n = children.length,\n        r = padding(node) * k || 0,\n        e;\n      if (r) for (i = 0; i < n; ++i) children[i].r += r;\n      e = packSiblingsRandom(children, random);\n      if (r) for (i = 0; i < n; ++i) children[i].r -= r;\n      node.r = e + r;\n    }\n  };\n}\nfunction translateChild(k) {\n  return function (node) {\n    var parent = node.parent;\n    node.r *= k;\n    if (parent) {\n      node.x = parent.x + k * node.x;\n      node.y = parent.y + k * node.y;\n    }\n  };\n}", "map": {"version": 3, "names": ["optional", "constant", "constantZero", "lcg", "packSiblingsRandom", "defaultRadius", "d", "Math", "sqrt", "value", "radius", "dx", "dy", "padding", "pack", "root", "random", "x", "y", "eachBefore", "radiusLeaf", "eachAfter", "packChildrenRandom", "<PERSON><PERSON><PERSON><PERSON>", "r", "min", "arguments", "length", "size", "node", "children", "max", "k", "i", "n", "e", "parent"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/pack/index.js"], "sourcesContent": ["import {optional} from \"../accessors.js\";\nimport constant, {constant<PERSON>ero} from \"../constant.js\";\nimport lcg from \"../lcg.js\";\nimport {packSiblingsRandom} from \"./siblings.js\";\n\nfunction defaultRadius(d) {\n  return Math.sqrt(d.value);\n}\n\nexport default function() {\n  var radius = null,\n      dx = 1,\n      dy = 1,\n      padding = constantZero;\n\n  function pack(root) {\n    const random = lcg();\n    root.x = dx / 2, root.y = dy / 2;\n    if (radius) {\n      root.eachBefore(radiusLeaf(radius))\n          .eachAfter(packChildrenRandom(padding, 0.5, random))\n          .eachBefore(translateChild(1));\n    } else {\n      root.eachBefore(radiusLeaf(defaultRadius))\n          .eachAfter(packChildrenRandom(constantZero, 1, random))\n          .eachAfter(packChildrenRandom(padding, root.r / Math.min(dx, dy), random))\n          .eachBefore(translateChild(Math.min(dx, dy) / (2 * root.r)));\n    }\n    return root;\n  }\n\n  pack.radius = function(x) {\n    return arguments.length ? (radius = optional(x), pack) : radius;\n  };\n\n  pack.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], pack) : [dx, dy];\n  };\n\n  pack.padding = function(x) {\n    return arguments.length ? (padding = typeof x === \"function\" ? x : constant(+x), pack) : padding;\n  };\n\n  return pack;\n}\n\nfunction radiusLeaf(radius) {\n  return function(node) {\n    if (!node.children) {\n      node.r = Math.max(0, +radius(node) || 0);\n    }\n  };\n}\n\nfunction packChildrenRandom(padding, k, random) {\n  return function(node) {\n    if (children = node.children) {\n      var children,\n          i,\n          n = children.length,\n          r = padding(node) * k || 0,\n          e;\n\n      if (r) for (i = 0; i < n; ++i) children[i].r += r;\n      e = packSiblingsRandom(children, random);\n      if (r) for (i = 0; i < n; ++i) children[i].r -= r;\n      node.r = e + r;\n    }\n  };\n}\n\nfunction translateChild(k) {\n  return function(node) {\n    var parent = node.parent;\n    node.r *= k;\n    if (parent) {\n      node.x = parent.x + k * node.x;\n      node.y = parent.y + k * node.y;\n    }\n  };\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,iBAAiB;AACxC,OAAOC,QAAQ,IAAGC,YAAY,QAAO,gBAAgB;AACrD,OAAOC,GAAG,MAAM,WAAW;AAC3B,SAAQC,kBAAkB,QAAO,eAAe;AAEhD,SAASC,aAAaA,CAACC,CAAC,EAAE;EACxB,OAAOC,IAAI,CAACC,IAAI,CAACF,CAAC,CAACG,KAAK,CAAC;AAC3B;AAEA,eAAe,YAAW;EACxB,IAAIC,MAAM,GAAG,IAAI;IACbC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,OAAO,GAAGX,YAAY;EAE1B,SAASY,IAAIA,CAACC,IAAI,EAAE;IAClB,MAAMC,MAAM,GAAGb,GAAG,CAAC,CAAC;IACpBY,IAAI,CAACE,CAAC,GAAGN,EAAE,GAAG,CAAC,EAAEI,IAAI,CAACG,CAAC,GAAGN,EAAE,GAAG,CAAC;IAChC,IAAIF,MAAM,EAAE;MACVK,IAAI,CAACI,UAAU,CAACC,UAAU,CAACV,MAAM,CAAC,CAAC,CAC9BW,SAAS,CAACC,kBAAkB,CAACT,OAAO,EAAE,GAAG,EAAEG,MAAM,CAAC,CAAC,CACnDG,UAAU,CAACI,cAAc,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC,MAAM;MACLR,IAAI,CAACI,UAAU,CAACC,UAAU,CAACf,aAAa,CAAC,CAAC,CACrCgB,SAAS,CAACC,kBAAkB,CAACpB,YAAY,EAAE,CAAC,EAAEc,MAAM,CAAC,CAAC,CACtDK,SAAS,CAACC,kBAAkB,CAACT,OAAO,EAAEE,IAAI,CAACS,CAAC,GAAGjB,IAAI,CAACkB,GAAG,CAACd,EAAE,EAAEC,EAAE,CAAC,EAAEI,MAAM,CAAC,CAAC,CACzEG,UAAU,CAACI,cAAc,CAAChB,IAAI,CAACkB,GAAG,CAACd,EAAE,EAAEC,EAAE,CAAC,IAAI,CAAC,GAAGG,IAAI,CAACS,CAAC,CAAC,CAAC,CAAC;IAClE;IACA,OAAOT,IAAI;EACb;EAEAD,IAAI,CAACJ,MAAM,GAAG,UAASO,CAAC,EAAE;IACxB,OAAOS,SAAS,CAACC,MAAM,IAAIjB,MAAM,GAAGV,QAAQ,CAACiB,CAAC,CAAC,EAAEH,IAAI,IAAIJ,MAAM;EACjE,CAAC;EAEDI,IAAI,CAACc,IAAI,GAAG,UAASX,CAAC,EAAE;IACtB,OAAOS,SAAS,CAACC,MAAM,IAAIhB,EAAE,GAAG,CAACM,CAAC,CAAC,CAAC,CAAC,EAAEL,EAAE,GAAG,CAACK,CAAC,CAAC,CAAC,CAAC,EAAEH,IAAI,IAAI,CAACH,EAAE,EAAEC,EAAE,CAAC;EACrE,CAAC;EAEDE,IAAI,CAACD,OAAO,GAAG,UAASI,CAAC,EAAE;IACzB,OAAOS,SAAS,CAACC,MAAM,IAAId,OAAO,GAAG,OAAOI,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGhB,QAAQ,CAAC,CAACgB,CAAC,CAAC,EAAEH,IAAI,IAAID,OAAO;EAClG,CAAC;EAED,OAAOC,IAAI;AACb;AAEA,SAASM,UAAUA,CAACV,MAAM,EAAE;EAC1B,OAAO,UAASmB,IAAI,EAAE;IACpB,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;MAClBD,IAAI,CAACL,CAAC,GAAGjB,IAAI,CAACwB,GAAG,CAAC,CAAC,EAAE,CAACrB,MAAM,CAACmB,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1C;EACF,CAAC;AACH;AAEA,SAASP,kBAAkBA,CAACT,OAAO,EAAEmB,CAAC,EAAEhB,MAAM,EAAE;EAC9C,OAAO,UAASa,IAAI,EAAE;IACpB,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,EAAE;MAC5B,IAAIA,QAAQ;QACRG,CAAC;QACDC,CAAC,GAAGJ,QAAQ,CAACH,MAAM;QACnBH,CAAC,GAAGX,OAAO,CAACgB,IAAI,CAAC,GAAGG,CAAC,IAAI,CAAC;QAC1BG,CAAC;MAEL,IAAIX,CAAC,EAAE,KAAKS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEH,QAAQ,CAACG,CAAC,CAAC,CAACT,CAAC,IAAIA,CAAC;MACjDW,CAAC,GAAG/B,kBAAkB,CAAC0B,QAAQ,EAAEd,MAAM,CAAC;MACxC,IAAIQ,CAAC,EAAE,KAAKS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEH,QAAQ,CAACG,CAAC,CAAC,CAACT,CAAC,IAAIA,CAAC;MACjDK,IAAI,CAACL,CAAC,GAAGW,CAAC,GAAGX,CAAC;IAChB;EACF,CAAC;AACH;AAEA,SAASD,cAAcA,CAACS,CAAC,EAAE;EACzB,OAAO,UAASH,IAAI,EAAE;IACpB,IAAIO,MAAM,GAAGP,IAAI,CAACO,MAAM;IACxBP,IAAI,CAACL,CAAC,IAAIQ,CAAC;IACX,IAAII,MAAM,EAAE;MACVP,IAAI,CAACZ,CAAC,GAAGmB,MAAM,CAACnB,CAAC,GAAGe,CAAC,GAAGH,IAAI,CAACZ,CAAC;MAC9BY,IAAI,CAACX,CAAC,GAAGkB,MAAM,CAAClB,CAAC,GAAGc,CAAC,GAAGH,IAAI,CAACX,CAAC;IAChC;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}