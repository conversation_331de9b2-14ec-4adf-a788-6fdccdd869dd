{"ast": null, "code": "var arrayEach = require('./_arrayEach'),\n  baseCreate = require('./_baseCreate'),\n  baseForOwn = require('./_baseForOwn'),\n  baseIteratee = require('./_baseIteratee'),\n  getPrototype = require('./_getPrototype'),\n  isArray = require('./isArray'),\n  isBuffer = require('./isBuffer'),\n  isFunction = require('./isFunction'),\n  isObject = require('./isObject'),\n  isTypedArray = require('./isTypedArray');\n\n/**\n * An alternative to `_.reduce`; this method transforms `object` to a new\n * `accumulator` object which is the result of running each of its own\n * enumerable string keyed properties thru `iteratee`, with each invocation\n * potentially mutating the `accumulator` object. If `accumulator` is not\n * provided, a new object with the same `[[Prototype]]` will be used. The\n * iteratee is invoked with four arguments: (accumulator, value, key, object).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The custom accumulator value.\n * @returns {*} Returns the accumulated value.\n * @example\n *\n * _.transform([2, 3, 4], function(result, n) {\n *   result.push(n *= n);\n *   return n % 2 == 0;\n * }, []);\n * // => [4, 9]\n *\n * _.transform({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] }\n */\nfunction transform(object, iteratee, accumulator) {\n  var isArr = isArray(object),\n    isArrLike = isArr || isBuffer(object) || isTypedArray(object);\n  iteratee = baseIteratee(iteratee, 4);\n  if (accumulator == null) {\n    var Ctor = object && object.constructor;\n    if (isArrLike) {\n      accumulator = isArr ? new Ctor() : [];\n    } else if (isObject(object)) {\n      accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};\n    } else {\n      accumulator = {};\n    }\n  }\n  (isArrLike ? arrayEach : baseForOwn)(object, function (value, index, object) {\n    return iteratee(accumulator, value, index, object);\n  });\n  return accumulator;\n}\nmodule.exports = transform;", "map": {"version": 3, "names": ["arrayEach", "require", "baseCreate", "baseForOwn", "baseIteratee", "getPrototype", "isArray", "<PERSON><PERSON><PERSON><PERSON>", "isFunction", "isObject", "isTypedArray", "transform", "object", "iteratee", "accumulator", "isArr", "isArrLike", "Ctor", "constructor", "value", "index", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/transform.js"], "sourcesContent": ["var arrayEach = require('./_arrayEach'),\n    baseCreate = require('./_baseCreate'),\n    baseForOwn = require('./_baseForOwn'),\n    baseIteratee = require('./_baseIteratee'),\n    getPrototype = require('./_getPrototype'),\n    isArray = require('./isArray'),\n    isBuffer = require('./isBuffer'),\n    isFunction = require('./isFunction'),\n    isObject = require('./isObject'),\n    isTypedArray = require('./isTypedArray');\n\n/**\n * An alternative to `_.reduce`; this method transforms `object` to a new\n * `accumulator` object which is the result of running each of its own\n * enumerable string keyed properties thru `iteratee`, with each invocation\n * potentially mutating the `accumulator` object. If `accumulator` is not\n * provided, a new object with the same `[[Prototype]]` will be used. The\n * iteratee is invoked with four arguments: (accumulator, value, key, object).\n * Iteratee functions may exit iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 1.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @param {*} [accumulator] The custom accumulator value.\n * @returns {*} Returns the accumulated value.\n * @example\n *\n * _.transform([2, 3, 4], function(result, n) {\n *   result.push(n *= n);\n *   return n % 2 == 0;\n * }, []);\n * // => [4, 9]\n *\n * _.transform({ 'a': 1, 'b': 2, 'c': 1 }, function(result, value, key) {\n *   (result[value] || (result[value] = [])).push(key);\n * }, {});\n * // => { '1': ['a', 'c'], '2': ['b'] }\n */\nfunction transform(object, iteratee, accumulator) {\n  var isArr = isArray(object),\n      isArrLike = isArr || isBuffer(object) || isTypedArray(object);\n\n  iteratee = baseIteratee(iteratee, 4);\n  if (accumulator == null) {\n    var Ctor = object && object.constructor;\n    if (isArrLike) {\n      accumulator = isArr ? new Ctor : [];\n    }\n    else if (isObject(object)) {\n      accumulator = isFunction(Ctor) ? baseCreate(getPrototype(object)) : {};\n    }\n    else {\n      accumulator = {};\n    }\n  }\n  (isArrLike ? arrayEach : baseForOwn)(object, function(value, index, object) {\n    return iteratee(accumulator, value, index, object);\n  });\n  return accumulator;\n}\n\nmodule.exports = transform;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;EACnCC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;EACrCE,UAAU,GAAGF,OAAO,CAAC,eAAe,CAAC;EACrCG,YAAY,GAAGH,OAAO,CAAC,iBAAiB,CAAC;EACzCI,YAAY,GAAGJ,OAAO,CAAC,iBAAiB,CAAC;EACzCK,OAAO,GAAGL,OAAO,CAAC,WAAW,CAAC;EAC9BM,QAAQ,GAAGN,OAAO,CAAC,YAAY,CAAC;EAChCO,UAAU,GAAGP,OAAO,CAAC,cAAc,CAAC;EACpCQ,QAAQ,GAAGR,OAAO,CAAC,YAAY,CAAC;EAChCS,YAAY,GAAGT,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,SAASA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EAChD,IAAIC,KAAK,GAAGT,OAAO,CAACM,MAAM,CAAC;IACvBI,SAAS,GAAGD,KAAK,IAAIR,QAAQ,CAACK,MAAM,CAAC,IAAIF,YAAY,CAACE,MAAM,CAAC;EAEjEC,QAAQ,GAAGT,YAAY,CAACS,QAAQ,EAAE,CAAC,CAAC;EACpC,IAAIC,WAAW,IAAI,IAAI,EAAE;IACvB,IAAIG,IAAI,GAAGL,MAAM,IAAIA,MAAM,CAACM,WAAW;IACvC,IAAIF,SAAS,EAAE;MACbF,WAAW,GAAGC,KAAK,GAAG,IAAIE,IAAI,CAAD,CAAC,GAAG,EAAE;IACrC,CAAC,MACI,IAAIR,QAAQ,CAACG,MAAM,CAAC,EAAE;MACzBE,WAAW,GAAGN,UAAU,CAACS,IAAI,CAAC,GAAGf,UAAU,CAACG,YAAY,CAACO,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;IACxE,CAAC,MACI;MACHE,WAAW,GAAG,CAAC,CAAC;IAClB;EACF;EACA,CAACE,SAAS,GAAGhB,SAAS,GAAGG,UAAU,EAAES,MAAM,EAAE,UAASO,KAAK,EAAEC,KAAK,EAAER,MAAM,EAAE;IAC1E,OAAOC,QAAQ,CAACC,WAAW,EAAEK,KAAK,EAAEC,KAAK,EAAER,MAAM,CAAC;EACpD,CAAC,CAAC;EACF,OAAOE,WAAW;AACpB;AAEAO,MAAM,CAACC,OAAO,GAAGX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}