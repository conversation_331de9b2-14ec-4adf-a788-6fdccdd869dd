{"ast": null, "code": "import { __spreadArray } from \"tslib\";\nimport { midPoint } from '../util/mid-point';\nexport var lineToCubic = function (x1, y1, x2, y2) {\n  var t = 0.5;\n  var mid = midPoint([x1, y1], [x2, y2], t);\n  return __spreadArray(__spreadArray([], mid, true), [x2, y2, x2, y2], false);\n};", "map": {"version": 3, "names": ["midPoint", "lineToCubic", "x1", "y1", "x2", "y2", "t", "mid", "__spread<PERSON><PERSON>y"], "sources": ["path/process/line-2-cubic.ts"], "sourcesContent": [null], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,mBAAmB;AAE5C,OAAO,IAAMC,WAAW,GAAG,SAAAA,CAACC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;EACxE,IAAMC,CAAC,GAAG,GAAG;EACb,IAAMC,GAAG,GAAGP,QAAQ,CAAC,CAACE,EAAE,EAAEC,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,EAAEC,CAAC,CAAC;EAC3C,OAAAE,aAAA,CAAAA,aAAA,KAAWD,GAAG,UAAEH,EAAE,EAAEC,EAAE,EAAED,EAAE,EAAEC,EAAE;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}