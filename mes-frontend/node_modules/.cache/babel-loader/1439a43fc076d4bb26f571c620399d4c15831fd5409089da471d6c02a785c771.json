{"ast": null, "code": "'use strict';\n\nfunction scalarScalar(a, b) {\n  const x = a + b;\n  const bv = x - a;\n  const av = x - bv;\n  const br = b - bv;\n  const ar = a - av;\n  const y = ar + br;\n  if (y) {\n    return [y, x];\n  }\n  return [x];\n}\nexport function robustSubtract(e, f) {\n  const ne = e.length | 0;\n  const nf = f.length | 0;\n  // Base case: scalar subtraction\n  if (ne === 1 && nf === 1) {\n    return scalarScalar(e[0], -f[0]);\n  }\n  const n = ne + nf;\n  const g = new Array(n);\n  let count = 0;\n  let ep_tr = 0;\n  let fp_tr = 0;\n  const abs = Math.abs;\n  let ei = e[ep_tr];\n  let ea = abs(ei);\n  let fi = -f[fp_tr];\n  let fa = abs(fi);\n  let a, b;\n  // Initial comparison to determine `a` and `b`\n  if (ea < fa) {\n    b = ei;\n    ep_tr += 1;\n    if (ep_tr < ne) {\n      ei = e[ep_tr];\n      ea = abs(ei);\n    }\n  } else {\n    b = fi;\n    fp_tr += 1;\n    if (fp_tr < nf) {\n      fi = -f[fp_tr];\n      fa = abs(fi);\n    }\n  }\n  if (ep_tr < ne && ea < fa || fp_tr >= nf) {\n    a = ei;\n    ep_tr += 1;\n    if (ep_tr < ne) {\n      ei = e[ep_tr];\n      ea = abs(ei);\n    }\n  } else {\n    a = fi;\n    fp_tr += 1;\n    if (fp_tr < nf) {\n      fi = -f[fp_tr];\n      fa = abs(fi);\n    }\n  }\n  let x = a + b;\n  let bv = x - a;\n  let y = b - bv;\n  let q0 = y;\n  let q1 = x;\n  let _x, _bv, _av, _br, _ar;\n  // Main loop for combining expansions\n  while (ep_tr < ne && fp_tr < nf) {\n    if (ea < fa) {\n      a = ei;\n      ep_tr += 1;\n      if (ep_tr < ne) {\n        ei = e[ep_tr];\n        ea = abs(ei);\n      }\n    } else {\n      a = fi;\n      fp_tr += 1;\n      if (fp_tr < nf) {\n        fi = -f[fp_tr];\n        fa = abs(fi);\n      }\n    }\n    b = q0;\n    x = a + b;\n    bv = x - a;\n    y = b - bv;\n    if (y) {\n      g[count++] = y;\n    }\n    _x = q1 + x;\n    _bv = _x - q1;\n    _av = _x - _bv;\n    _br = x - _bv;\n    _ar = q1 - _av;\n    q0 = _ar + _br;\n    q1 = _x;\n  }\n  // Handle remaining elements in `e`\n  while (ep_tr < ne) {\n    a = ei;\n    b = q0;\n    x = a + b;\n    bv = x - a;\n    y = b - bv;\n    if (y) {\n      g[count++] = y;\n    }\n    _x = q1 + x;\n    _bv = _x - q1;\n    _av = _x - _bv;\n    _br = x - _bv;\n    _ar = q1 - _av;\n    q0 = _ar + _br;\n    q1 = _x;\n    ep_tr += 1;\n    if (ep_tr < ne) {\n      ei = e[ep_tr];\n    }\n  }\n  // Handle remaining elements in `f`\n  while (fp_tr < nf) {\n    a = fi;\n    b = q0;\n    x = a + b;\n    bv = x - a;\n    y = b - bv;\n    if (y) {\n      g[count++] = y;\n    }\n    _x = q1 + x;\n    _bv = _x - q1;\n    _av = _x - _bv;\n    _br = x - _bv;\n    _ar = q1 - _av;\n    q0 = _ar + _br;\n    q1 = _x;\n    fp_tr += 1;\n    if (fp_tr < nf) {\n      fi = -f[fp_tr];\n    }\n  }\n  // Finalize the result\n  if (q0) {\n    g[count++] = q0;\n  }\n  if (q1) {\n    g[count++] = q1;\n  }\n  if (!count) {\n    g[count++] = 0.0;\n  }\n  g.length = count;\n  return g;\n}", "map": {"version": 3, "names": ["scalarScalar", "a", "b", "x", "bv", "av", "br", "ar", "y", "robustSubtract", "e", "f", "ne", "length", "nf", "n", "g", "Array", "count", "ep_tr", "fp_tr", "abs", "Math", "ei", "ea", "fi", "fa", "q0", "q1", "_x", "_bv", "_av", "_br", "_ar"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g6/src/plugins/hull/hull/robust-subtract.ts"], "sourcesContent": ["'use strict';\n\nfunction scalarScalar(a: number, b: number): number[] {\n  const x = a + b;\n  const bv = x - a;\n  const av = x - bv;\n  const br = b - bv;\n  const ar = a - av;\n  const y = ar + br;\n\n  if (y) {\n    return [y, x];\n  }\n  return [x];\n}\n\nexport function robustSubtract(e: number[], f: number[]): number[] {\n  const ne = e.length | 0;\n  const nf = f.length | 0;\n\n  // Base case: scalar subtraction\n  if (ne === 1 && nf === 1) {\n    return scalarScalar(e[0], -f[0]);\n  }\n\n  const n = ne + nf;\n  const g = new Array(n);\n  let count = 0;\n\n  let ep_tr = 0;\n  let fp_tr = 0;\n\n  const abs = Math.abs;\n  let ei = e[ep_tr];\n  let ea = abs(ei);\n  let fi = -f[fp_tr];\n  let fa = abs(fi);\n\n  let a: number, b: number;\n\n  // Initial comparison to determine `a` and `b`\n  if (ea < fa) {\n    b = ei;\n    ep_tr += 1;\n    if (ep_tr < ne) {\n      ei = e[ep_tr];\n      ea = abs(ei);\n    }\n  } else {\n    b = fi;\n    fp_tr += 1;\n    if (fp_tr < nf) {\n      fi = -f[fp_tr];\n      fa = abs(fi);\n    }\n  }\n\n  if ((ep_tr < ne && ea < fa) || fp_tr >= nf) {\n    a = ei;\n    ep_tr += 1;\n    if (ep_tr < ne) {\n      ei = e[ep_tr];\n      ea = abs(ei);\n    }\n  } else {\n    a = fi;\n    fp_tr += 1;\n    if (fp_tr < nf) {\n      fi = -f[fp_tr];\n      fa = abs(fi);\n    }\n  }\n\n  let x = a + b;\n  let bv = x - a;\n  let y = b - bv;\n\n  let q0 = y;\n  let q1 = x;\n\n  let _x: number, _bv: number, _av: number, _br: number, _ar: number;\n\n  // Main loop for combining expansions\n  while (ep_tr < ne && fp_tr < nf) {\n    if (ea < fa) {\n      a = ei;\n      ep_tr += 1;\n      if (ep_tr < ne) {\n        ei = e[ep_tr];\n        ea = abs(ei);\n      }\n    } else {\n      a = fi;\n      fp_tr += 1;\n      if (fp_tr < nf) {\n        fi = -f[fp_tr];\n        fa = abs(fi);\n      }\n    }\n\n    b = q0;\n    x = a + b;\n    bv = x - a;\n    y = b - bv;\n\n    if (y) {\n      g[count++] = y;\n    }\n\n    _x = q1 + x;\n    _bv = _x - q1;\n    _av = _x - _bv;\n    _br = x - _bv;\n    _ar = q1 - _av;\n\n    q0 = _ar + _br;\n    q1 = _x;\n  }\n\n  // Handle remaining elements in `e`\n  while (ep_tr < ne) {\n    a = ei;\n    b = q0;\n    x = a + b;\n    bv = x - a;\n    y = b - bv;\n\n    if (y) {\n      g[count++] = y;\n    }\n\n    _x = q1 + x;\n    _bv = _x - q1;\n    _av = _x - _bv;\n    _br = x - _bv;\n    _ar = q1 - _av;\n\n    q0 = _ar + _br;\n    q1 = _x;\n\n    ep_tr += 1;\n    if (ep_tr < ne) {\n      ei = e[ep_tr];\n    }\n  }\n\n  // Handle remaining elements in `f`\n  while (fp_tr < nf) {\n    a = fi;\n    b = q0;\n    x = a + b;\n    bv = x - a;\n    y = b - bv;\n\n    if (y) {\n      g[count++] = y;\n    }\n\n    _x = q1 + x;\n    _bv = _x - q1;\n    _av = _x - _bv;\n    _br = x - _bv;\n    _ar = q1 - _av;\n\n    q0 = _ar + _br;\n    q1 = _x;\n\n    fp_tr += 1;\n    if (fp_tr < nf) {\n      fi = -f[fp_tr];\n    }\n  }\n\n  // Finalize the result\n  if (q0) {\n    g[count++] = q0;\n  }\n  if (q1) {\n    g[count++] = q1;\n  }\n  if (!count) {\n    g[count++] = 0.0;\n  }\n\n  g.length = count;\n  return g;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,YAAYA,CAACC,CAAS,EAAEC,CAAS;EACxC,MAAMC,CAAC,GAAGF,CAAC,GAAGC,CAAC;EACf,MAAME,EAAE,GAAGD,CAAC,GAAGF,CAAC;EAChB,MAAMI,EAAE,GAAGF,CAAC,GAAGC,EAAE;EACjB,MAAME,EAAE,GAAGJ,CAAC,GAAGE,EAAE;EACjB,MAAMG,EAAE,GAAGN,CAAC,GAAGI,EAAE;EACjB,MAAMG,CAAC,GAAGD,EAAE,GAAGD,EAAE;EAEjB,IAAIE,CAAC,EAAE;IACL,OAAO,CAACA,CAAC,EAAEL,CAAC,CAAC;EACf;EACA,OAAO,CAACA,CAAC,CAAC;AACZ;AAEA,OAAM,SAAUM,cAAcA,CAACC,CAAW,EAAEC,CAAW;EACrD,MAAMC,EAAE,GAAGF,CAAC,CAACG,MAAM,GAAG,CAAC;EACvB,MAAMC,EAAE,GAAGH,CAAC,CAACE,MAAM,GAAG,CAAC;EAEvB;EACA,IAAID,EAAE,KAAK,CAAC,IAAIE,EAAE,KAAK,CAAC,EAAE;IACxB,OAAOd,YAAY,CAACU,CAAC,CAAC,CAAC,CAAC,EAAE,CAACC,CAAC,CAAC,CAAC,CAAC,CAAC;EAClC;EAEA,MAAMI,CAAC,GAAGH,EAAE,GAAGE,EAAE;EACjB,MAAME,CAAC,GAAG,IAAIC,KAAK,CAACF,CAAC,CAAC;EACtB,IAAIG,KAAK,GAAG,CAAC;EAEb,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,CAAC;EAEb,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG;EACpB,IAAIE,EAAE,GAAGb,CAAC,CAACS,KAAK,CAAC;EACjB,IAAIK,EAAE,GAAGH,GAAG,CAACE,EAAE,CAAC;EAChB,IAAIE,EAAE,GAAG,CAACd,CAAC,CAACS,KAAK,CAAC;EAClB,IAAIM,EAAE,GAAGL,GAAG,CAACI,EAAE,CAAC;EAEhB,IAAIxB,CAAS,EAAEC,CAAS;EAExB;EACA,IAAIsB,EAAE,GAAGE,EAAE,EAAE;IACXxB,CAAC,GAAGqB,EAAE;IACNJ,KAAK,IAAI,CAAC;IACV,IAAIA,KAAK,GAAGP,EAAE,EAAE;MACdW,EAAE,GAAGb,CAAC,CAACS,KAAK,CAAC;MACbK,EAAE,GAAGH,GAAG,CAACE,EAAE,CAAC;IACd;EACF,CAAC,MAAM;IACLrB,CAAC,GAAGuB,EAAE;IACNL,KAAK,IAAI,CAAC;IACV,IAAIA,KAAK,GAAGN,EAAE,EAAE;MACdW,EAAE,GAAG,CAACd,CAAC,CAACS,KAAK,CAAC;MACdM,EAAE,GAAGL,GAAG,CAACI,EAAE,CAAC;IACd;EACF;EAEA,IAAKN,KAAK,GAAGP,EAAE,IAAIY,EAAE,GAAGE,EAAE,IAAKN,KAAK,IAAIN,EAAE,EAAE;IAC1Cb,CAAC,GAAGsB,EAAE;IACNJ,KAAK,IAAI,CAAC;IACV,IAAIA,KAAK,GAAGP,EAAE,EAAE;MACdW,EAAE,GAAGb,CAAC,CAACS,KAAK,CAAC;MACbK,EAAE,GAAGH,GAAG,CAACE,EAAE,CAAC;IACd;EACF,CAAC,MAAM;IACLtB,CAAC,GAAGwB,EAAE;IACNL,KAAK,IAAI,CAAC;IACV,IAAIA,KAAK,GAAGN,EAAE,EAAE;MACdW,EAAE,GAAG,CAACd,CAAC,CAACS,KAAK,CAAC;MACdM,EAAE,GAAGL,GAAG,CAACI,EAAE,CAAC;IACd;EACF;EAEA,IAAItB,CAAC,GAAGF,CAAC,GAAGC,CAAC;EACb,IAAIE,EAAE,GAAGD,CAAC,GAAGF,CAAC;EACd,IAAIO,CAAC,GAAGN,CAAC,GAAGE,EAAE;EAEd,IAAIuB,EAAE,GAAGnB,CAAC;EACV,IAAIoB,EAAE,GAAGzB,CAAC;EAEV,IAAI0B,EAAU,EAAEC,GAAW,EAAEC,GAAW,EAAEC,GAAW,EAAEC,GAAW;EAElE;EACA,OAAOd,KAAK,GAAGP,EAAE,IAAIQ,KAAK,GAAGN,EAAE,EAAE;IAC/B,IAAIU,EAAE,GAAGE,EAAE,EAAE;MACXzB,CAAC,GAAGsB,EAAE;MACNJ,KAAK,IAAI,CAAC;MACV,IAAIA,KAAK,GAAGP,EAAE,EAAE;QACdW,EAAE,GAAGb,CAAC,CAACS,KAAK,CAAC;QACbK,EAAE,GAAGH,GAAG,CAACE,EAAE,CAAC;MACd;IACF,CAAC,MAAM;MACLtB,CAAC,GAAGwB,EAAE;MACNL,KAAK,IAAI,CAAC;MACV,IAAIA,KAAK,GAAGN,EAAE,EAAE;QACdW,EAAE,GAAG,CAACd,CAAC,CAACS,KAAK,CAAC;QACdM,EAAE,GAAGL,GAAG,CAACI,EAAE,CAAC;MACd;IACF;IAEAvB,CAAC,GAAGyB,EAAE;IACNxB,CAAC,GAAGF,CAAC,GAAGC,CAAC;IACTE,EAAE,GAAGD,CAAC,GAAGF,CAAC;IACVO,CAAC,GAAGN,CAAC,GAAGE,EAAE;IAEV,IAAII,CAAC,EAAE;MACLQ,CAAC,CAACE,KAAK,EAAE,CAAC,GAAGV,CAAC;IAChB;IAEAqB,EAAE,GAAGD,EAAE,GAAGzB,CAAC;IACX2B,GAAG,GAAGD,EAAE,GAAGD,EAAE;IACbG,GAAG,GAAGF,EAAE,GAAGC,GAAG;IACdE,GAAG,GAAG7B,CAAC,GAAG2B,GAAG;IACbG,GAAG,GAAGL,EAAE,GAAGG,GAAG;IAEdJ,EAAE,GAAGM,GAAG,GAAGD,GAAG;IACdJ,EAAE,GAAGC,EAAE;EACT;EAEA;EACA,OAAOV,KAAK,GAAGP,EAAE,EAAE;IACjBX,CAAC,GAAGsB,EAAE;IACNrB,CAAC,GAAGyB,EAAE;IACNxB,CAAC,GAAGF,CAAC,GAAGC,CAAC;IACTE,EAAE,GAAGD,CAAC,GAAGF,CAAC;IACVO,CAAC,GAAGN,CAAC,GAAGE,EAAE;IAEV,IAAII,CAAC,EAAE;MACLQ,CAAC,CAACE,KAAK,EAAE,CAAC,GAAGV,CAAC;IAChB;IAEAqB,EAAE,GAAGD,EAAE,GAAGzB,CAAC;IACX2B,GAAG,GAAGD,EAAE,GAAGD,EAAE;IACbG,GAAG,GAAGF,EAAE,GAAGC,GAAG;IACdE,GAAG,GAAG7B,CAAC,GAAG2B,GAAG;IACbG,GAAG,GAAGL,EAAE,GAAGG,GAAG;IAEdJ,EAAE,GAAGM,GAAG,GAAGD,GAAG;IACdJ,EAAE,GAAGC,EAAE;IAEPV,KAAK,IAAI,CAAC;IACV,IAAIA,KAAK,GAAGP,EAAE,EAAE;MACdW,EAAE,GAAGb,CAAC,CAACS,KAAK,CAAC;IACf;EACF;EAEA;EACA,OAAOC,KAAK,GAAGN,EAAE,EAAE;IACjBb,CAAC,GAAGwB,EAAE;IACNvB,CAAC,GAAGyB,EAAE;IACNxB,CAAC,GAAGF,CAAC,GAAGC,CAAC;IACTE,EAAE,GAAGD,CAAC,GAAGF,CAAC;IACVO,CAAC,GAAGN,CAAC,GAAGE,EAAE;IAEV,IAAII,CAAC,EAAE;MACLQ,CAAC,CAACE,KAAK,EAAE,CAAC,GAAGV,CAAC;IAChB;IAEAqB,EAAE,GAAGD,EAAE,GAAGzB,CAAC;IACX2B,GAAG,GAAGD,EAAE,GAAGD,EAAE;IACbG,GAAG,GAAGF,EAAE,GAAGC,GAAG;IACdE,GAAG,GAAG7B,CAAC,GAAG2B,GAAG;IACbG,GAAG,GAAGL,EAAE,GAAGG,GAAG;IAEdJ,EAAE,GAAGM,GAAG,GAAGD,GAAG;IACdJ,EAAE,GAAGC,EAAE;IAEPT,KAAK,IAAI,CAAC;IACV,IAAIA,KAAK,GAAGN,EAAE,EAAE;MACdW,EAAE,GAAG,CAACd,CAAC,CAACS,KAAK,CAAC;IAChB;EACF;EAEA;EACA,IAAIO,EAAE,EAAE;IACNX,CAAC,CAACE,KAAK,EAAE,CAAC,GAAGS,EAAE;EACjB;EACA,IAAIC,EAAE,EAAE;IACNZ,CAAC,CAACE,KAAK,EAAE,CAAC,GAAGU,EAAE;EACjB;EACA,IAAI,CAACV,KAAK,EAAE;IACVF,CAAC,CAACE,KAAK,EAAE,CAAC,GAAG,GAAG;EAClB;EAEAF,CAAC,CAACH,MAAM,GAAGK,KAAK;EAChB,OAAOF,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}