{"ast": null, "code": "var _jsxFileName = \"/root/mes-system/mes-frontend/src/pages/Project/ProjectList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Typography, Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, message, Popconfirm, Row, Col, Statistic } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ProjectOutlined, CalendarOutlined, UserOutlined } from '@ant-design/icons';\nimport { projectService } from '../../services/business';\nimport { usePagination } from '../../hooks/useApi';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TextArea\n} = Input;\nconst ProjectList = () => {\n  _s();\n  const navigate = useNavigate();\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState(null);\n  const [form] = Form.useForm();\n\n  // 使用分页Hook管理项目数据\n  const {\n    list: projects,\n    total,\n    loading,\n    params,\n    changePage,\n    changeParams,\n    refresh\n  } = usePagination(projectService.getProjects, {\n    page: 1,\n    limit: 10\n  });\n\n  // 模拟项目数据\n  const mockProjects = [{\n    id: 1,\n    project_name: '汽车零部件生产项目',\n    customer_name: '某汽车制造公司',\n    description: '生产汽车发动机相关零部件',\n    status: 'IN_PROGRESS',\n    start_date: '2024-01-01',\n    end_date: '2024-06-30',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z'\n  }, {\n    id: 2,\n    project_name: '电子产品外壳项目',\n    customer_name: '某电子科技公司',\n    description: '生产手机和平板电脑外壳',\n    status: 'PLANNING',\n    start_date: '2024-02-01',\n    end_date: '2024-08-31',\n    created_at: '2024-01-15T00:00:00Z',\n    updated_at: '2024-01-15T00:00:00Z'\n  }, {\n    id: 3,\n    project_name: '医疗器械组件项目',\n    customer_name: '某医疗设备公司',\n    description: '生产医疗设备精密组件',\n    status: 'COMPLETED',\n    start_date: '2023-09-01',\n    end_date: '2023-12-31',\n    created_at: '2023-08-15T00:00:00Z',\n    updated_at: '2023-12-31T00:00:00Z'\n  }];\n\n  // 如果API失败，使用模拟数据作为后备\n  const displayProjects = projects.length > 0 ? projects : mockProjects;\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = project => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      ...project,\n      date_range: project.start_date && project.end_date ? [dayjs(project.start_date), dayjs(project.end_date)] : undefined\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      const response = await projectService.deleteProject(id);\n      if (response.success) {\n        message.success('删除成功');\n        refresh();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$date_range, _values$date_range$, _values$date_range2, _values$date_range2$;\n      const projectData = {\n        ...values,\n        start_date: (_values$date_range = values.date_range) === null || _values$date_range === void 0 ? void 0 : (_values$date_range$ = _values$date_range[0]) === null || _values$date_range$ === void 0 ? void 0 : _values$date_range$.format('YYYY-MM-DD'),\n        end_date: (_values$date_range2 = values.date_range) === null || _values$date_range2 === void 0 ? void 0 : (_values$date_range2$ = _values$date_range2[1]) === null || _values$date_range2$ === void 0 ? void 0 : _values$date_range2$.format('YYYY-MM-DD')\n      };\n      delete projectData.date_range;\n      let response;\n      if (editingProject) {\n        response = await projectService.updateProject(editingProject.id, projectData);\n      } else {\n        response = await projectService.createProject(projectData);\n      }\n      if (response.success) {\n        message.success(editingProject ? '更新成功' : '创建成功');\n        setModalVisible(false);\n        refresh();\n      } else {\n        message.error(response.message || (editingProject ? '更新失败' : '创建失败'));\n      }\n    } catch (error) {\n      message.error(editingProject ? '更新失败' : '创建失败');\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'PLANNING':\n        return 'blue';\n      case 'IN_PROGRESS':\n        return 'green';\n      case 'COMPLETED':\n        return 'default';\n      case 'CANCELLED':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'PLANNING':\n        return '计划中';\n      case 'IN_PROGRESS':\n        return '进行中';\n      case 'COMPLETED':\n        return '已完成';\n      case 'CANCELLED':\n        return '已取消';\n      default:\n        return '未知';\n    }\n  };\n  const columns = [{\n    title: '项目名称',\n    dataIndex: 'project_name',\n    key: 'project_name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Button, {\n      type: \"link\",\n      onClick: () => navigate(`/projects/${record.id}`),\n      style: {\n        padding: 0,\n        height: 'auto'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '客户名称',\n    dataIndex: 'customer_name',\n    key: 'customer_name'\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '开始日期',\n    dataIndex: 'start_date',\n    key: 'start_date',\n    render: date => date ? dayjs(date).format('YYYY-MM-DD') : '-'\n  }, {\n    title: '结束日期',\n    dataIndex: 'end_date',\n    key: 'end_date',\n    render: date => date ? dayjs(date).format('YYYY-MM-DD') : '-'\n  }, {\n    title: '创建时间',\n    dataIndex: 'created_at',\n    key: 'created_at',\n    render: date => dayjs(date).format('YYYY-MM-DD HH:mm')\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"middle\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 19\n        }, this),\n        onClick: () => navigate(`/projects/${record.id}`),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u9879\\u76EE\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 统计数据\n  const stats = {\n    total: projects.length,\n    planning: projects.filter(p => p.status === 'PLANNING').length,\n    inProgress: projects.filter(p => p.status === 'IN_PROGRESS').length,\n    completed: projects.filter(p => p.status === 'COMPLETED').length\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        style: {\n          margin: 0\n        },\n        children: \"\\u9879\\u76EE\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 17\n        }, this),\n        onClick: handleCreate,\n        children: \"\\u65B0\\u5EFA\\u9879\\u76EE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u9879\\u76EE\\u6570\",\n            value: stats.total,\n            prefix: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BA1\\u5212\\u4E2D\",\n            value: stats.planning,\n            prefix: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FDB\\u884C\\u4E2D\",\n            value: stats.inProgress,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: stats.completed,\n            prefix: /*#__PURE__*/_jsxDEV(ProjectOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#faad14'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 306,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: projects,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          total: projects.length,\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingProject ? '编辑项目' : '新建项目',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"project_name\",\n          label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入项目名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"customer_name\",\n          label: \"\\u5BA2\\u6237\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入客户名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BA2\\u6237\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u9879\\u76EE\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u9879\\u76EE\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择项目状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u9879\\u76EE\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"PLANNING\",\n              children: \"\\u8BA1\\u5212\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"IN_PROGRESS\",\n              children: \"\\u8FDB\\u884C\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"COMPLETED\",\n              children: \"\\u5DF2\\u5B8C\\u6210\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"CANCELLED\",\n              children: \"\\u5DF2\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"date_range\",\n          label: \"\\u9879\\u76EE\\u5468\\u671F\",\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            style: {\n              width: '100%'\n            },\n            placeholder: ['开始日期', '结束日期']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 419,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingProject ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectList, \"sTiTu/oEixY4qI0kfjqSQ0y4kCs=\", false, function () {\n  return [useNavigate, Form.useForm, usePagination];\n});\n_c = ProjectList;\nexport default ProjectList;\nvar _c;\n$RefreshReg$(_c, \"ProjectList\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Typography", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Modal", "Form", "Input", "Select", "DatePicker", "message", "Popconfirm", "Row", "Col", "Statistic", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ProjectOutlined", "CalendarOutlined", "UserOutlined", "projectService", "usePagination", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Option", "RangePicker", "TextArea", "ProjectList", "_s", "navigate", "modalVisible", "setModalVisible", "editingProject", "setEditingProject", "form", "useForm", "list", "projects", "total", "loading", "params", "changePage", "changeParams", "refresh", "getProjects", "page", "limit", "mockProjects", "id", "project_name", "customer_name", "description", "status", "start_date", "end_date", "created_at", "updated_at", "displayProjects", "length", "handleCreate", "resetFields", "handleEdit", "project", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "date_range", "undefined", "handleDelete", "response", "deleteProject", "success", "error", "handleSubmit", "values", "_values$date_range", "_values$date_range$", "_values$date_range2", "_values$date_range2$", "projectData", "format", "updateProject", "createProject", "getStatusColor", "getStatusText", "columns", "title", "dataIndex", "key", "render", "text", "record", "type", "onClick", "style", "padding", "height", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "date", "_", "size", "icon", "onConfirm", "okText", "cancelText", "danger", "stats", "planning", "filter", "p", "inProgress", "completed", "display", "justifyContent", "alignItems", "marginBottom", "level", "margin", "gutter", "span", "value", "prefix", "valueStyle", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "width", "layout", "onFinish", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "rows", "htmlType", "_c", "$RefreshReg$"], "sources": ["/root/mes-system/mes-frontend/src/pages/Project/ProjectList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Typography,\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  message,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic,\n  Spin,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ProjectOutlined,\n  CalendarOutlined,\n  UserOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport { projectService } from '../../services/business';\nimport { Project } from '../../types';\nimport { usePagination } from '../../hooks/useApi';\nimport dayjs from 'dayjs';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { TextArea } = Input;\n\nconst ProjectList: React.FC = () => {\n  const navigate = useNavigate();\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState<Project | null>(null);\n  const [form] = Form.useForm();\n\n  // 使用分页Hook管理项目数据\n  const {\n    list: projects,\n    total,\n    loading,\n    params,\n    changePage,\n    changeParams,\n    refresh,\n  } = usePagination(projectService.getProjects, {\n    page: 1,\n    limit: 10,\n  });\n\n  // 模拟项目数据\n  const mockProjects: Project[] = [\n    {\n      id: 1,\n      project_name: '汽车零部件生产项目',\n      customer_name: '某汽车制造公司',\n      description: '生产汽车发动机相关零部件',\n      status: 'IN_PROGRESS',\n      start_date: '2024-01-01',\n      end_date: '2024-06-30',\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-01T00:00:00Z',\n    },\n    {\n      id: 2,\n      project_name: '电子产品外壳项目',\n      customer_name: '某电子科技公司',\n      description: '生产手机和平板电脑外壳',\n      status: 'PLANNING',\n      start_date: '2024-02-01',\n      end_date: '2024-08-31',\n      created_at: '2024-01-15T00:00:00Z',\n      updated_at: '2024-01-15T00:00:00Z',\n    },\n    {\n      id: 3,\n      project_name: '医疗器械组件项目',\n      customer_name: '某医疗设备公司',\n      description: '生产医疗设备精密组件',\n      status: 'COMPLETED',\n      start_date: '2023-09-01',\n      end_date: '2023-12-31',\n      created_at: '2023-08-15T00:00:00Z',\n      updated_at: '2023-12-31T00:00:00Z',\n    },\n  ];\n\n  // 如果API失败，使用模拟数据作为后备\n  const displayProjects = projects.length > 0 ? projects : mockProjects;\n\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (project: Project) => {\n    setEditingProject(project);\n    form.setFieldsValue({\n      ...project,\n      date_range: project.start_date && project.end_date\n        ? [dayjs(project.start_date), dayjs(project.end_date)]\n        : undefined,\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await projectService.deleteProject(id);\n      if (response.success) {\n        message.success('删除成功');\n        refresh();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      const projectData = {\n        ...values,\n        start_date: values.date_range?.[0]?.format('YYYY-MM-DD'),\n        end_date: values.date_range?.[1]?.format('YYYY-MM-DD'),\n      };\n      delete projectData.date_range;\n\n      let response;\n      if (editingProject) {\n        response = await projectService.updateProject(editingProject.id, projectData);\n      } else {\n        response = await projectService.createProject(projectData);\n      }\n\n      if (response.success) {\n        message.success(editingProject ? '更新成功' : '创建成功');\n        setModalVisible(false);\n        refresh();\n      } else {\n        message.error(response.message || (editingProject ? '更新失败' : '创建失败'));\n      }\n    } catch (error) {\n      message.error(editingProject ? '更新失败' : '创建失败');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PLANNING':\n        return 'blue';\n      case 'IN_PROGRESS':\n        return 'green';\n      case 'COMPLETED':\n        return 'default';\n      case 'CANCELLED':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'PLANNING':\n        return '计划中';\n      case 'IN_PROGRESS':\n        return '进行中';\n      case 'COMPLETED':\n        return '已完成';\n      case 'CANCELLED':\n        return '已取消';\n      default:\n        return '未知';\n    }\n  };\n\n  const columns = [\n    {\n      title: '项目名称',\n      dataIndex: 'project_name',\n      key: 'project_name',\n      render: (text: string, record: Project) => (\n        <Button\n          type=\"link\"\n          onClick={() => navigate(`/projects/${record.id}`)}\n          style={{ padding: 0, height: 'auto' }}\n        >\n          {text}\n        </Button>\n      ),\n    },\n    {\n      title: '客户名称',\n      dataIndex: 'customer_name',\n      key: 'customer_name',\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '开始日期',\n      dataIndex: 'start_date',\n      key: 'start_date',\n      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',\n    },\n    {\n      title: '结束日期',\n      dataIndex: 'end_date',\n      key: 'end_date',\n      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'created_at',\n      key: 'created_at',\n      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record: Project) => (\n        <Space size=\"middle\">\n          <Button\n            type=\"text\"\n            icon={<EyeOutlined />}\n            onClick={() => navigate(`/projects/${record.id}`)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"text\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个项目吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"text\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  // 统计数据\n  const stats = {\n    total: projects.length,\n    planning: projects.filter(p => p.status === 'PLANNING').length,\n    inProgress: projects.filter(p => p.status === 'IN_PROGRESS').length,\n    completed: projects.filter(p => p.status === 'COMPLETED').length,\n  };\n\n  return (\n    <div>\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '24px'\n      }}>\n        <Title level={2} style={{ margin: 0 }}>\n          项目管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleCreate}\n        >\n          新建项目\n        </Button>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总项目数\"\n              value={stats.total}\n              prefix={<ProjectOutlined />}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"计划中\"\n              value={stats.planning}\n              prefix={<CalendarOutlined />}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"进行中\"\n              value={stats.inProgress}\n              prefix={<UserOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={stats.completed}\n              prefix={<ProjectOutlined />}\n              valueStyle={{ color: '#faad14' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 项目列表 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={projects}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            total: projects.length,\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n        />\n      </Card>\n\n      {/* 创建/编辑模态框 */}\n      <Modal\n        title={editingProject ? '编辑项目' : '新建项目'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"project_name\"\n            label=\"项目名称\"\n            rules={[{ required: true, message: '请输入项目名称' }]}\n          >\n            <Input placeholder=\"请输入项目名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"customer_name\"\n            label=\"客户名称\"\n            rules={[{ required: true, message: '请输入客户名称' }]}\n          >\n            <Input placeholder=\"请输入客户名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"项目描述\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入项目描述\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"项目状态\"\n            rules={[{ required: true, message: '请选择项目状态' }]}\n          >\n            <Select placeholder=\"请选择项目状态\">\n              <Option value=\"PLANNING\">计划中</Option>\n              <Option value=\"IN_PROGRESS\">进行中</Option>\n              <Option value=\"COMPLETED\">已完成</Option>\n              <Option value=\"CANCELLED\">已取消</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"date_range\"\n            label=\"项目周期\"\n          >\n            <RangePicker\n              style={{ width: '100%' }}\n              placeholder={['开始日期', '结束日期']}\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingProject ? '更新' : '创建'}\n              </Button>\n              <Button onClick={() => setModalVisible(false)}>\n                取消\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,QAEJ,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,YAAY,QAEP,mBAAmB;AAC1B,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC;AAAM,CAAC,GAAG5B,UAAU;AAC5B,MAAM;EAAE6B;AAAO,CAAC,GAAGpB,MAAM;AACzB,MAAM;EAAEqB;AAAY,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAS,CAAC,GAAGvB,KAAK;AAE1B,MAAMwB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACuC,cAAc,EAAEC,iBAAiB,CAAC,GAAGxC,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACyC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAM;IACJC,IAAI,EAAEC,QAAQ;IACdC,KAAK;IACLC,OAAO;IACPC,MAAM;IACNC,UAAU;IACVC,YAAY;IACZC;EACF,CAAC,GAAGxB,aAAa,CAACD,cAAc,CAAC0B,WAAW,EAAE;IAC5CC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAMC,YAAuB,GAAG,CAC9B;IACEC,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,WAAW;IACzBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,cAAc;IAC3BC,MAAM,EAAE,aAAa;IACrBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,UAAU;IACxBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,aAAa;IAC1BC,MAAM,EAAE,UAAU;IAClBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE;EACd,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,YAAY,EAAE,UAAU;IACxBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,YAAY;IACzBC,MAAM,EAAE,WAAW;IACnBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE;EACd,CAAC,CACF;;EAED;EACA,MAAMC,eAAe,GAAGpB,QAAQ,CAACqB,MAAM,GAAG,CAAC,GAAGrB,QAAQ,GAAGU,YAAY;EAErE,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzB1B,iBAAiB,CAAC,IAAI,CAAC;IACvBC,IAAI,CAAC0B,WAAW,CAAC,CAAC;IAClB7B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM8B,UAAU,GAAIC,OAAgB,IAAK;IACvC7B,iBAAiB,CAAC6B,OAAO,CAAC;IAC1B5B,IAAI,CAAC6B,cAAc,CAAC;MAClB,GAAGD,OAAO;MACVE,UAAU,EAAEF,OAAO,CAACT,UAAU,IAAIS,OAAO,CAACR,QAAQ,GAC9C,CAAClC,KAAK,CAAC0C,OAAO,CAACT,UAAU,CAAC,EAAEjC,KAAK,CAAC0C,OAAO,CAACR,QAAQ,CAAC,CAAC,GACpDW;IACN,CAAC,CAAC;IACFlC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmC,YAAY,GAAG,MAAOlB,EAAU,IAAK;IACzC,IAAI;MACF,MAAMmB,QAAQ,GAAG,MAAMjD,cAAc,CAACkD,aAAa,CAACpB,EAAE,CAAC;MACvD,IAAImB,QAAQ,CAACE,OAAO,EAAE;QACpB/D,OAAO,CAAC+D,OAAO,CAAC,MAAM,CAAC;QACvB1B,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACLrC,OAAO,CAACgE,KAAK,CAACH,QAAQ,CAAC7D,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOgE,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,oBAAA;MACF,MAAMC,WAAW,GAAG;QAClB,GAAGL,MAAM;QACTnB,UAAU,GAAAoB,kBAAA,GAAED,MAAM,CAACR,UAAU,cAAAS,kBAAA,wBAAAC,mBAAA,GAAjBD,kBAAA,CAAoB,CAAC,CAAC,cAAAC,mBAAA,uBAAtBA,mBAAA,CAAwBI,MAAM,CAAC,YAAY,CAAC;QACxDxB,QAAQ,GAAAqB,mBAAA,GAAEH,MAAM,CAACR,UAAU,cAAAW,mBAAA,wBAAAC,oBAAA,GAAjBD,mBAAA,CAAoB,CAAC,CAAC,cAAAC,oBAAA,uBAAtBA,oBAAA,CAAwBE,MAAM,CAAC,YAAY;MACvD,CAAC;MACD,OAAOD,WAAW,CAACb,UAAU;MAE7B,IAAIG,QAAQ;MACZ,IAAInC,cAAc,EAAE;QAClBmC,QAAQ,GAAG,MAAMjD,cAAc,CAAC6D,aAAa,CAAC/C,cAAc,CAACgB,EAAE,EAAE6B,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLV,QAAQ,GAAG,MAAMjD,cAAc,CAAC8D,aAAa,CAACH,WAAW,CAAC;MAC5D;MAEA,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpB/D,OAAO,CAAC+D,OAAO,CAACrC,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC;QACjDD,eAAe,CAAC,KAAK,CAAC;QACtBY,OAAO,CAAC,CAAC;MACX,CAAC,MAAM;QACLrC,OAAO,CAACgE,KAAK,CAACH,QAAQ,CAAC7D,OAAO,KAAK0B,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;MACvE;IACF,CAAC,CAAC,OAAOsC,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAACtC,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC;IACjD;EACF,CAAC;EAED,MAAMiD,cAAc,GAAI7B,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,MAAM;MACf,KAAK,aAAa;QAChB,OAAO,OAAO;MAChB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,KAAK;MACd;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAM8B,aAAa,GAAI9B,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,KAAK;MACd,KAAK,aAAa;QAChB,OAAO,KAAK;MACd,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,WAAW;QACd,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAM+B,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAEA,CAACC,IAAY,EAAEC,MAAe,kBACpCnE,OAAA,CAACxB,MAAM;MACL4F,IAAI,EAAC,MAAM;MACXC,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,aAAa4D,MAAM,CAACzC,EAAE,EAAE,CAAE;MAClD4C,KAAK,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAO,CAAE;MAAAC,QAAA,EAErCP;IAAI;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAEZ,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGnC,MAAc,iBACrB9B,OAAA,CAACtB,GAAG;MAACoG,KAAK,EAAEnB,cAAc,CAAC7B,MAAM,CAAE;MAAA2C,QAAA,EAChCb,aAAa,CAAC9B,MAAM;IAAC;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGc,IAAY,IAAKA,IAAI,GAAGjF,KAAK,CAACiF,IAAI,CAAC,CAACvB,MAAM,CAAC,YAAY,CAAC,GAAG;EACtE,CAAC,EACD;IACEM,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGc,IAAY,IAAKA,IAAI,GAAGjF,KAAK,CAACiF,IAAI,CAAC,CAACvB,MAAM,CAAC,YAAY,CAAC,GAAG;EACtE,CAAC,EACD;IACEM,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGc,IAAY,IAAKjF,KAAK,CAACiF,IAAI,CAAC,CAACvB,MAAM,CAAC,kBAAkB;EACjE,CAAC,EACD;IACEM,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACe,CAAC,EAAEb,MAAe,kBACzBnE,OAAA,CAACvB,KAAK;MAACwG,IAAI,EAAC,QAAQ;MAAAR,QAAA,gBAClBzE,OAAA,CAACxB,MAAM;QACL4F,IAAI,EAAC,MAAM;QACXc,IAAI,eAAElF,OAAA,CAACR,WAAW;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBR,OAAO,EAAEA,CAAA,KAAM9D,QAAQ,CAAC,aAAa4D,MAAM,CAACzC,EAAE,EAAE,CAAE;QAAA+C,QAAA,EACnD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7E,OAAA,CAACxB,MAAM;QACL4F,IAAI,EAAC,MAAM;QACXc,IAAI,eAAElF,OAAA,CAACV,YAAY;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBR,OAAO,EAAEA,CAAA,KAAM9B,UAAU,CAAC4B,MAAM,CAAE;QAAAM,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7E,OAAA,CAACf,UAAU;QACT6E,KAAK,EAAC,oEAAa;QACnBqB,SAAS,EAAEA,CAAA,KAAMvC,YAAY,CAACuB,MAAM,CAACzC,EAAE,CAAE;QACzC0D,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAZ,QAAA,eAEfzE,OAAA,CAACxB,MAAM;UACL4F,IAAI,EAAC,MAAM;UACXkB,MAAM;UACNJ,IAAI,eAAElF,OAAA,CAACT,cAAc;YAAAmF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMU,KAAK,GAAG;IACZvE,KAAK,EAAED,QAAQ,CAACqB,MAAM;IACtBoD,QAAQ,EAAEzE,QAAQ,CAAC0E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,MAAM,KAAK,UAAU,CAAC,CAACM,MAAM;IAC9DuD,UAAU,EAAE5E,QAAQ,CAAC0E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,MAAM,KAAK,aAAa,CAAC,CAACM,MAAM;IACnEwD,SAAS,EAAE7E,QAAQ,CAAC0E,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,MAAM,KAAK,WAAW,CAAC,CAACM;EAC5D,CAAC;EAED,oBACEpC,OAAA;IAAAyE,QAAA,gBACEzE,OAAA;MAAKsE,KAAK,EAAE;QACVuB,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE;MAChB,CAAE;MAAAvB,QAAA,gBACAzE,OAAA,CAACC,KAAK;QAACgG,KAAK,EAAE,CAAE;QAAC3B,KAAK,EAAE;UAAE4B,MAAM,EAAE;QAAE,CAAE;QAAAzB,QAAA,EAAC;MAEvC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR7E,OAAA,CAACxB,MAAM;QACL4F,IAAI,EAAC,SAAS;QACdc,IAAI,eAAElF,OAAA,CAACX,YAAY;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBR,OAAO,EAAEhC,YAAa;QAAAoC,QAAA,EACvB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7E,OAAA,CAACd,GAAG;MAACiH,MAAM,EAAE,EAAG;MAAC7B,KAAK,EAAE;QAAE0B,YAAY,EAAE;MAAO,CAAE;MAAAvB,QAAA,gBAC/CzE,OAAA,CAACb,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXzE,OAAA,CAAC1B,IAAI;UAAAmG,QAAA,eACHzE,OAAA,CAACZ,SAAS;YACR0E,KAAK,EAAC,0BAAM;YACZuC,KAAK,EAAEd,KAAK,CAACvE,KAAM;YACnBsF,MAAM,eAAEtG,OAAA,CAACP,eAAe;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B0B,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXzE,OAAA,CAAC1B,IAAI;UAAAmG,QAAA,eACHzE,OAAA,CAACZ,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXuC,KAAK,EAAEd,KAAK,CAACC,QAAS;YACtBc,MAAM,eAAEtG,OAAA,CAACN,gBAAgB;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7B0B,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXzE,OAAA,CAAC1B,IAAI;UAAAmG,QAAA,eACHzE,OAAA,CAACZ,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXuC,KAAK,EAAEd,KAAK,CAACI,UAAW;YACxBW,MAAM,eAAEtG,OAAA,CAACL,YAAY;cAAA+E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB0B,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7E,OAAA,CAACb,GAAG;QAACiH,IAAI,EAAE,CAAE;QAAA3B,QAAA,eACXzE,OAAA,CAAC1B,IAAI;UAAAmG,QAAA,eACHzE,OAAA,CAACZ,SAAS;YACR0E,KAAK,EAAC,oBAAK;YACXuC,KAAK,EAAEd,KAAK,CAACK,SAAU;YACvBU,MAAM,eAAEtG,OAAA,CAACP,eAAe;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5B0B,UAAU,EAAE;cAAEzB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA,CAAC1B,IAAI;MAAAmG,QAAA,eACHzE,OAAA,CAACzB,KAAK;QACJsF,OAAO,EAAEA,OAAQ;QACjB2C,UAAU,EAAEzF,QAAS;QACrB0F,MAAM,EAAC,IAAI;QACXxF,OAAO,EAAEA,OAAQ;QACjByF,UAAU,EAAE;UACV1F,KAAK,EAAED,QAAQ,CAACqB,MAAM;UACtBuE,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC9F,KAAK,EAAE+F,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ/F,KAAK;QAC1C;MAAE;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP7E,OAAA,CAACrB,KAAK;MACJmF,KAAK,EAAEpD,cAAc,GAAG,MAAM,GAAG,MAAO;MACxCsG,IAAI,EAAExG,YAAa;MACnByG,QAAQ,EAAEA,CAAA,KAAMxG,eAAe,CAAC,KAAK,CAAE;MACvCyG,MAAM,EAAE,IAAK;MACbC,KAAK,EAAE,GAAI;MAAA1C,QAAA,eAEXzE,OAAA,CAACpB,IAAI;QACHgC,IAAI,EAAEA,IAAK;QACXwG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEpE,YAAa;QAAAwB,QAAA,gBAEvBzE,OAAA,CAACpB,IAAI,CAAC0I,IAAI;UACRC,IAAI,EAAC,cAAc;UACnBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1I,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyF,QAAA,eAEhDzE,OAAA,CAACnB,KAAK;YAAC8I,WAAW,EAAC;UAAS;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ7E,OAAA,CAACpB,IAAI,CAAC0I,IAAI;UACRC,IAAI,EAAC,eAAe;UACpBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1I,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyF,QAAA,eAEhDzE,OAAA,CAACnB,KAAK;YAAC8I,WAAW,EAAC;UAAS;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ7E,OAAA,CAACpB,IAAI,CAAC0I,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,0BAAM;UAAA/C,QAAA,eAEZzE,OAAA,CAACI,QAAQ;YACPwH,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAS;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ7E,OAAA,CAACpB,IAAI,CAAC0I,IAAI;UACRC,IAAI,EAAC,QAAQ;UACbC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1I,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyF,QAAA,eAEhDzE,OAAA,CAAClB,MAAM;YAAC6I,WAAW,EAAC,4CAAS;YAAAlD,QAAA,gBAC3BzE,OAAA,CAACE,MAAM;cAACmG,KAAK,EAAC,UAAU;cAAA5B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrC7E,OAAA,CAACE,MAAM;cAACmG,KAAK,EAAC,aAAa;cAAA5B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxC7E,OAAA,CAACE,MAAM;cAACmG,KAAK,EAAC,WAAW;cAAA5B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtC7E,OAAA,CAACE,MAAM;cAACmG,KAAK,EAAC,WAAW;cAAA5B,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ7E,OAAA,CAACpB,IAAI,CAAC0I,IAAI;UACRC,IAAI,EAAC,YAAY;UACjBC,KAAK,EAAC,0BAAM;UAAA/C,QAAA,eAEZzE,OAAA,CAACG,WAAW;YACVmE,KAAK,EAAE;cAAE6C,KAAK,EAAE;YAAO,CAAE;YACzBQ,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM;UAAE;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ7E,OAAA,CAACpB,IAAI,CAAC0I,IAAI;UAAA7C,QAAA,eACRzE,OAAA,CAACvB,KAAK;YAAAgG,QAAA,gBACJzE,OAAA,CAACxB,MAAM;cAAC4F,IAAI,EAAC,SAAS;cAACyD,QAAQ,EAAC,QAAQ;cAAApD,QAAA,EACrC/D,cAAc,GAAG,IAAI,GAAG;YAAI;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACT7E,OAAA,CAACxB,MAAM;cAAC6F,OAAO,EAAEA,CAAA,KAAM5D,eAAe,CAAC,KAAK,CAAE;cAAAgE,QAAA,EAAC;YAE/C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACvE,EAAA,CAjZID,WAAqB;EAAA,QACRjC,WAAW,EAGbQ,IAAI,CAACiC,OAAO,EAWvBhB,aAAa;AAAA;AAAAiI,EAAA,GAfbzH,WAAqB;AAmZ3B,eAAeA,WAAW;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}