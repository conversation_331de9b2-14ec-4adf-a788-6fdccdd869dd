{"ast": null, "code": "import { transition } from './utils';\nexport default function (element, options) {\n  if (!element.style.opacity) element.style.opacity = 1;\n  return transition(element, {\n    opacity: 0\n  }, options);\n}", "map": {"version": 3, "names": ["transition", "element", "options", "style", "opacity"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/animation/fadeOut.ts"], "sourcesContent": ["import type { DisplayObject } from '../shapes';\nimport { transition } from './utils';\nimport type { GenericAnimation } from '.';\n\nexport default function (element: DisplayObject, options: GenericAnimation) {\n  if (!element.style.opacity) element.style.opacity = 1;\n  return transition(element, { opacity: 0 }, options);\n}\n"], "mappings": "AACA,SAASA,UAAU,QAAQ,SAAS;AAGpC,eAAc,UAAWC,OAAsB,EAAEC,OAAyB;EACxE,IAAI,CAACD,OAAO,CAACE,KAAK,CAACC,OAAO,EAAEH,OAAO,CAACE,KAAK,CAACC,OAAO,GAAG,CAAC;EACrD,OAAOJ,UAAU,CAACC,OAAO,EAAE;IAAEG,OAAO,EAAE;EAAC,CAAE,EAAEF,OAAO,CAAC;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}