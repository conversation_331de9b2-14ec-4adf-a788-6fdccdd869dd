{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { defined, getLocalBBox, hide } from '../../../util';\nimport { isAxisHorizontal, isAxisVertical } from '../guides/line';\nimport { boundTest } from '../utils/test';\nvar methods = {\n  parity: function (items, _a) {\n    var _b = _a.seq,\n      seq = _b === void 0 ? 2 : _b;\n    return items.filter(function (item, i) {\n      return i % seq ? (hide(item), false) : true;\n    });\n  }\n};\nvar filterDefined = function (arr) {\n  return arr.filter(defined);\n};\nexport default function hideLabels(labels, overlapCfg, attr, utils) {\n  var count = labels.length;\n  var keepHeader = overlapCfg.keepHeader,\n    keepTail = overlapCfg.keepTail;\n  if (count <= 1 || count === 2 && keepHeader && keepTail) return;\n  var parityHide = methods.parity;\n  var reset = function (els) {\n    return els.forEach(utils.show), els;\n  };\n  var seq = 2;\n  var source = labels.slice();\n  var target = labels.slice();\n  var minLabelWidth = Math.min.apply(Math, __spreadArray([1], __read(labels.map(function (d) {\n    return d.getBBox().width;\n  })), false));\n  if (attr.type === 'linear' && (isAxisHorizontal(attr) || isAxisVertical(attr))) {\n    var minX = getLocalBBox(labels[0]).left;\n    var maxX = getLocalBBox(labels[count - 1]).right;\n    var distance = Math.abs(maxX - minX) || 1;\n    seq = Math.max(Math.floor(count * minLabelWidth / distance), seq);\n  }\n  var first;\n  var last;\n  if (keepHeader) first = source.splice(0, 1)[0];\n  if (keepTail) {\n    last = source.splice(-1, 1)[0];\n    source.reverse();\n  }\n  reset(source);\n  while (seq < labels.length && boundTest(filterDefined(last ? __spreadArray(__spreadArray([last], __read(target), false), [first], false) : __spreadArray([first], __read(target), false)), attr, overlapCfg === null || overlapCfg === void 0 ? void 0 : overlapCfg.margin).length) {\n    // 每两步，减一个 (不需要考虑保留 first)\n    if (last && !first && seq % 2 === 0) {\n      var rest = source.splice(0, 1);\n      rest.forEach(utils.hide);\n    } else if (last && first) {\n      // 如果有 first 的话，每一步，减一个（增加迭代次数）\n      var rest = source.splice(0, 1);\n      rest.forEach(utils.hide);\n    }\n    target = parityHide(reset(source), {\n      seq: seq\n    });\n    seq++;\n  }\n}", "map": {"version": 3, "names": ["defined", "getLocalBBox", "hide", "isAxisHorizontal", "isAxisVertical", "boundTest", "methods", "parity", "items", "_a", "_b", "seq", "filter", "item", "i", "filterDefined", "arr", "<PERSON><PERSON><PERSON><PERSON>", "labels", "overlapCfg", "attr", "utils", "count", "length", "<PERSON><PERSON><PERSON><PERSON>", "keepTail", "parityHide", "reset", "els", "for<PERSON>ach", "show", "source", "slice", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "min", "apply", "__spread<PERSON><PERSON>y", "__read", "map", "d", "getBBox", "width", "type", "minX", "left", "maxX", "right", "distance", "abs", "max", "floor", "first", "last", "splice", "reverse", "margin", "rest"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/overlap/autoHide.ts"], "sourcesContent": ["import type { DisplayObject } from '../../../shapes';\nimport { defined, getLocalBBox, hide } from '../../../util';\nimport { isAxisHorizontal, isAxisVertical } from '../guides/line';\nimport type { AxisStyleProps, HideOverlapCfg } from '../types';\nimport { boundTest } from '../utils/test';\n\ntype Hide = (item: DisplayObject) => void;\ntype Show = (item: DisplayObject) => void;\nexport type Utils = { hide: Hide; show: Show };\n\nconst methods: Record<string, (items: DisplayObject[], args: any) => DisplayObject[]> = {\n  parity: (items: DisplayObject[], { seq = 2 }) => items.filter((item, i) => (i % seq ? (hide(item), false) : true)),\n};\n\nconst filterDefined = (arr: any[]) => arr.filter(defined);\n\nexport default function hideLabels(\n  labels: DisplayObject[],\n  overlapCfg: HideOverlapCfg,\n  attr: Required<AxisStyleProps>,\n  utils: Utils\n) {\n  const count = labels.length;\n  const { keepHeader, keepTail } = overlapCfg;\n\n  if (count <= 1 || (count === 2 && keepHeader && keepTail)) return;\n\n  const parityHide = methods.parity;\n  const reset = (els: DisplayObject[]) => (els.forEach(utils.show), els);\n  let seq = 2;\n  const source = labels.slice();\n  let target = labels.slice();\n\n  const minLabelWidth = Math.min(1, ...labels.map((d) => d.getBBox().width));\n\n  if (attr.type === 'linear' && (isAxisHorizontal(attr) || isAxisVertical(attr))) {\n    const minX = getLocalBBox(labels[0]).left;\n    const maxX = getLocalBBox(labels[count - 1]).right;\n    const distance = Math.abs(maxX - minX) || 1;\n    seq = Math.max(Math.floor((count * minLabelWidth) / distance), seq);\n  }\n\n  let first: DisplayObject | undefined;\n  let last: DisplayObject | undefined;\n\n  if (keepHeader) first = source.splice(0, 1)[0];\n  if (keepTail) {\n    last = source.splice(-1, 1)[0];\n    source.reverse();\n  }\n\n  reset(source);\n\n  while (\n    seq < labels.length &&\n    boundTest(filterDefined(last ? [last, ...target, first] : [first, ...target]), attr, overlapCfg?.margin).length\n  ) {\n    // 每两步，减一个 (不需要考虑保留 first)\n    if (last && !first && seq % 2 === 0) {\n      const rest = source.splice(0, 1);\n      rest.forEach(utils.hide);\n    } else if (last && first) {\n      // 如果有 first 的话，每一步，减一个（增加迭代次数）\n      const rest = source.splice(0, 1);\n      rest.forEach(utils.hide);\n    }\n\n    target = parityHide(reset(source), { seq });\n    seq++;\n  }\n}\n"], "mappings": ";AACA,SAASA,OAAO,EAAEC,YAAY,EAAEC,IAAI,QAAQ,eAAe;AAC3D,SAASC,gBAAgB,EAAEC,cAAc,QAAQ,gBAAgB;AAEjE,SAASC,SAAS,QAAQ,eAAe;AAMzC,IAAMC,OAAO,GAA2E;EACtFC,MAAM,EAAE,SAAAA,CAACC,KAAsB,EAAEC,EAAW;QAATC,EAAA,GAAAD,EAAA,CAAAE,GAAO;MAAPA,GAAG,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;IAAO,OAAAF,KAAK,CAACI,MAAM,CAAC,UAACC,IAAI,EAAEC,CAAC;MAAK,OAACA,CAAC,GAAGH,GAAG,IAAIT,IAAI,CAACW,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI;IAArC,CAAsC,CAAC;EAAjE;CAClD;AAED,IAAME,aAAa,GAAG,SAAAA,CAACC,GAAU;EAAK,OAAAA,GAAG,CAACJ,MAAM,CAACZ,OAAO,CAAC;AAAnB,CAAmB;AAEzD,eAAc,SAAUiB,UAAUA,CAChCC,MAAuB,EACvBC,UAA0B,EAC1BC,IAA8B,EAC9BC,KAAY;EAEZ,IAAMC,KAAK,GAAGJ,MAAM,CAACK,MAAM;EACnB,IAAAC,UAAU,GAAeL,UAAU,CAAAK,UAAzB;IAAEC,QAAQ,GAAKN,UAAU,CAAAM,QAAf;EAE5B,IAAIH,KAAK,IAAI,CAAC,IAAKA,KAAK,KAAK,CAAC,IAAIE,UAAU,IAAIC,QAAS,EAAE;EAE3D,IAAMC,UAAU,GAAGpB,OAAO,CAACC,MAAM;EACjC,IAAMoB,KAAK,GAAG,SAAAA,CAACC,GAAoB;IAAK,OAACA,GAAG,CAACC,OAAO,CAACR,KAAK,CAACS,IAAI,CAAC,EAAEF,GAAG;EAA7B,CAA8B;EACtE,IAAIjB,GAAG,GAAG,CAAC;EACX,IAAMoB,MAAM,GAAGb,MAAM,CAACc,KAAK,EAAE;EAC7B,IAAIC,MAAM,GAAGf,MAAM,CAACc,KAAK,EAAE;EAE3B,IAAME,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAAC,KAAA,CAARF,IAAI,EAAAG,aAAA,EAAK,CAAC,GAAAC,MAAA,CAAKrB,MAAM,CAACsB,GAAG,CAAC,UAACC,CAAC;IAAK,OAAAA,CAAC,CAACC,OAAO,EAAE,CAACC,KAAK;EAAjB,CAAiB,CAAC,UAAC;EAE1E,IAAIvB,IAAI,CAACwB,IAAI,KAAK,QAAQ,KAAKzC,gBAAgB,CAACiB,IAAI,CAAC,IAAIhB,cAAc,CAACgB,IAAI,CAAC,CAAC,EAAE;IAC9E,IAAMyB,IAAI,GAAG5C,YAAY,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC4B,IAAI;IACzC,IAAMC,IAAI,GAAG9C,YAAY,CAACiB,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC0B,KAAK;IAClD,IAAMC,QAAQ,GAAGd,IAAI,CAACe,GAAG,CAACH,IAAI,GAAGF,IAAI,CAAC,IAAI,CAAC;IAC3ClC,GAAG,GAAGwB,IAAI,CAACgB,GAAG,CAAChB,IAAI,CAACiB,KAAK,CAAE9B,KAAK,GAAGY,aAAa,GAAIe,QAAQ,CAAC,EAAEtC,GAAG,CAAC;EACrE;EAEA,IAAI0C,KAAgC;EACpC,IAAIC,IAA+B;EAEnC,IAAI9B,UAAU,EAAE6B,KAAK,GAAGtB,MAAM,CAACwB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9C,IAAI9B,QAAQ,EAAE;IACZ6B,IAAI,GAAGvB,MAAM,CAACwB,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9BxB,MAAM,CAACyB,OAAO,EAAE;EAClB;EAEA7B,KAAK,CAACI,MAAM,CAAC;EAEb,OACEpB,GAAG,GAAGO,MAAM,CAACK,MAAM,IACnBlB,SAAS,CAACU,aAAa,CAACuC,IAAI,GAAEhB,aAAA,CAAAA,aAAA,EAAEgB,IAAI,GAAAf,MAAA,CAAKN,MAAM,YAAEoB,KAAK,YAAGf,aAAA,EAAEe,KAAK,GAAAd,MAAA,CAAKN,MAAM,SAAC,CAAC,EAAEb,IAAI,EAAED,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEsC,MAAM,CAAC,CAAClC,MAAM,EAC/G;IACA;IACA,IAAI+B,IAAI,IAAI,CAACD,KAAK,IAAI1C,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;MACnC,IAAM+C,IAAI,GAAG3B,MAAM,CAACwB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MAChCG,IAAI,CAAC7B,OAAO,CAACR,KAAK,CAACnB,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAIoD,IAAI,IAAID,KAAK,EAAE;MACxB;MACA,IAAMK,IAAI,GAAG3B,MAAM,CAACwB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;MAChCG,IAAI,CAAC7B,OAAO,CAACR,KAAK,CAACnB,IAAI,CAAC;IAC1B;IAEA+B,MAAM,GAAGP,UAAU,CAACC,KAAK,CAACI,MAAM,CAAC,EAAE;MAAEpB,GAAG,EAAAA;IAAA,CAAE,CAAC;IAC3CA,GAAG,EAAE;EACP;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}