{"ast": null, "code": "import roundNode from \"./treemap/round.js\";\nimport treemapDice from \"./treemap/dice.js\";\nexport default function () {\n  var dx = 1,\n    dy = 1,\n    padding = 0,\n    round = false;\n  function partition(root) {\n    var n = root.height + 1;\n    root.x0 = root.y0 = padding;\n    root.x1 = dx;\n    root.y1 = dy / n;\n    root.eachBefore(positionNode(dy, n));\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n  function positionNode(dy, n) {\n    return function (node) {\n      if (node.children) {\n        treemapDice(node, node.x0, dy * (node.depth + 1) / n, node.x1, dy * (node.depth + 2) / n);\n      }\n      var x0 = node.x0,\n        y0 = node.y0,\n        x1 = node.x1 - padding,\n        y1 = node.y1 - padding;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      node.x0 = x0;\n      node.y0 = y0;\n      node.x1 = x1;\n      node.y1 = y1;\n    };\n  }\n  partition.round = function (x) {\n    return arguments.length ? (round = !!x, partition) : round;\n  };\n  partition.size = function (x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], partition) : [dx, dy];\n  };\n  partition.padding = function (x) {\n    return arguments.length ? (padding = +x, partition) : padding;\n  };\n  return partition;\n}", "map": {"version": 3, "names": ["roundNode", "treemapDice", "dx", "dy", "padding", "round", "partition", "root", "n", "height", "x0", "y0", "x1", "y1", "eachBefore", "positionNode", "node", "children", "depth", "x", "arguments", "length", "size"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/partition.js"], "sourcesContent": ["import roundNode from \"./treemap/round.js\";\nimport treemapDice from \"./treemap/dice.js\";\n\nexport default function() {\n  var dx = 1,\n      dy = 1,\n      padding = 0,\n      round = false;\n\n  function partition(root) {\n    var n = root.height + 1;\n    root.x0 =\n    root.y0 = padding;\n    root.x1 = dx;\n    root.y1 = dy / n;\n    root.eachBefore(positionNode(dy, n));\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(dy, n) {\n    return function(node) {\n      if (node.children) {\n        treemapDice(node, node.x0, dy * (node.depth + 1) / n, node.x1, dy * (node.depth + 2) / n);\n      }\n      var x0 = node.x0,\n          y0 = node.y0,\n          x1 = node.x1 - padding,\n          y1 = node.y1 - padding;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      node.x0 = x0;\n      node.y0 = y0;\n      node.x1 = x1;\n      node.y1 = y1;\n    };\n  }\n\n  partition.round = function(x) {\n    return arguments.length ? (round = !!x, partition) : round;\n  };\n\n  partition.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], partition) : [dx, dy];\n  };\n\n  partition.padding = function(x) {\n    return arguments.length ? (padding = +x, partition) : padding;\n  };\n\n  return partition;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,WAAW,MAAM,mBAAmB;AAE3C,eAAe,YAAW;EACxB,IAAIC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,OAAO,GAAG,CAAC;IACXC,KAAK,GAAG,KAAK;EAEjB,SAASC,SAASA,CAACC,IAAI,EAAE;IACvB,IAAIC,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC;IACvBF,IAAI,CAACG,EAAE,GACPH,IAAI,CAACI,EAAE,GAAGP,OAAO;IACjBG,IAAI,CAACK,EAAE,GAAGV,EAAE;IACZK,IAAI,CAACM,EAAE,GAAGV,EAAE,GAAGK,CAAC;IAChBD,IAAI,CAACO,UAAU,CAACC,YAAY,CAACZ,EAAE,EAAEK,CAAC,CAAC,CAAC;IACpC,IAAIH,KAAK,EAAEE,IAAI,CAACO,UAAU,CAACd,SAAS,CAAC;IACrC,OAAOO,IAAI;EACb;EAEA,SAASQ,YAAYA,CAACZ,EAAE,EAAEK,CAAC,EAAE;IAC3B,OAAO,UAASQ,IAAI,EAAE;MACpB,IAAIA,IAAI,CAACC,QAAQ,EAAE;QACjBhB,WAAW,CAACe,IAAI,EAAEA,IAAI,CAACN,EAAE,EAAEP,EAAE,IAAIa,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,GAAGV,CAAC,EAAEQ,IAAI,CAACJ,EAAE,EAAET,EAAE,IAAIa,IAAI,CAACE,KAAK,GAAG,CAAC,CAAC,GAAGV,CAAC,CAAC;MAC3F;MACA,IAAIE,EAAE,GAAGM,IAAI,CAACN,EAAE;QACZC,EAAE,GAAGK,IAAI,CAACL,EAAE;QACZC,EAAE,GAAGI,IAAI,CAACJ,EAAE,GAAGR,OAAO;QACtBS,EAAE,GAAGG,IAAI,CAACH,EAAE,GAAGT,OAAO;MAC1B,IAAIQ,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGE,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,IAAI,CAAC;MACpC,IAAIC,EAAE,GAAGF,EAAE,EAAEA,EAAE,GAAGE,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,IAAI,CAAC;MACpCG,IAAI,CAACN,EAAE,GAAGA,EAAE;MACZM,IAAI,CAACL,EAAE,GAAGA,EAAE;MACZK,IAAI,CAACJ,EAAE,GAAGA,EAAE;MACZI,IAAI,CAACH,EAAE,GAAGA,EAAE;IACd,CAAC;EACH;EAEAP,SAAS,CAACD,KAAK,GAAG,UAASc,CAAC,EAAE;IAC5B,OAAOC,SAAS,CAACC,MAAM,IAAIhB,KAAK,GAAG,CAAC,CAACc,CAAC,EAAEb,SAAS,IAAID,KAAK;EAC5D,CAAC;EAEDC,SAAS,CAACgB,IAAI,GAAG,UAASH,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAInB,EAAE,GAAG,CAACiB,CAAC,CAAC,CAAC,CAAC,EAAEhB,EAAE,GAAG,CAACgB,CAAC,CAAC,CAAC,CAAC,EAAEb,SAAS,IAAI,CAACJ,EAAE,EAAEC,EAAE,CAAC;EAC1E,CAAC;EAEDG,SAAS,CAACF,OAAO,GAAG,UAASe,CAAC,EAAE;IAC9B,OAAOC,SAAS,CAACC,MAAM,IAAIjB,OAAO,GAAG,CAACe,CAAC,EAAEb,SAAS,IAAIF,OAAO;EAC/D,CAAC;EAED,OAAOE,SAAS;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}