{"ast": null, "code": "export var secondReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2})$/;\nexport var dateReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2}) (\\d{1,2}):(\\d{1,2}):(\\d{1,2})$/;", "map": {"version": 3, "names": ["secondReg", "dateReg"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/es/constants/time.js"], "sourcesContent": ["export var secondReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2})$/;\nexport var dateReg = /^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2}) (\\d{1,2}):(\\d{1,2}):(\\d{1,2})$/;"], "mappings": "AAAA,OAAO,IAAIA,SAAS,GAAG,uCAAuC;AAC9D,OAAO,IAAIC,OAAO,GAAG,qEAAqE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}