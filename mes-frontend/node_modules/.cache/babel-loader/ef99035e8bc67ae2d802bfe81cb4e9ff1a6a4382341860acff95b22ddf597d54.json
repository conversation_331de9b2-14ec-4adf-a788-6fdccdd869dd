{"ast": null, "code": "import { barycenter } from './barycenter';\nimport resolveConflicts from './resolve-conflicts';\nimport { sort } from './sort';\nexport const sortSubgraph = (g, v, cg, biasRight, usePrev, keepNodeOrder) => {\n  var _a, _b, _c, _d;\n  let movable = g.getChildren(v).map(n => n.id);\n  // fixorder的点不参与排序（这个方案不合适，只排了新增节点，和原来的分离）\n  const node = g.getNode(v);\n  const bl = node ? node.data.borderLeft : undefined;\n  const br = node ? node.data.borderRight : undefined;\n  const subgraphs = {};\n  if (bl) {\n    movable = movable === null || movable === void 0 ? void 0 : movable.filter(w => {\n      return w !== bl && w !== br;\n    });\n  }\n  const barycenters = barycenter(g, movable || []);\n  barycenters === null || barycenters === void 0 ? void 0 : barycenters.forEach(entry => {\n    var _a;\n    if ((_a = g.getChildren(entry.v)) === null || _a === void 0 ? void 0 : _a.length) {\n      const subgraphResult = sortSubgraph(g, entry.v, cg, biasRight, keepNodeOrder);\n      subgraphs[entry.v] = subgraphResult;\n      if (subgraphResult.hasOwnProperty('barycenter')) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n  const entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n  // 添加fixorder信息到entries里边\n  // TODO: 不考虑复合情况，只用第一个点的fixorder信息，后续考虑更完备的实现\n  (_a = entries.filter(e => e.vs.length > 0)) === null || _a === void 0 ? void 0 : _a.forEach(e => {\n    const node = g.getNode(e.vs[0]);\n    if (node) {\n      e.fixorder = node.data.fixorder;\n      e.order = node.data.order;\n    }\n  });\n  const result = sort(entries, biasRight, usePrev, keepNodeOrder);\n  if (bl) {\n    result.vs = [bl, result.vs, br].flat();\n    if ((_b = g.getPredecessors(bl)) === null || _b === void 0 ? void 0 : _b.length) {\n      const blPred = g.getNode(((_c = g.getPredecessors(bl)) === null || _c === void 0 ? void 0 : _c[0].id) || '');\n      const brPred = g.getNode(((_d = g.getPredecessors(br)) === null || _d === void 0 ? void 0 : _d[0].id) || '');\n      if (!result.hasOwnProperty('barycenter')) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter = (result.barycenter * result.weight + blPred.data.order + brPred.data.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n  return result;\n};\nconst expandSubgraphs = (entries, subgraphs) => {\n  entries === null || entries === void 0 ? void 0 : entries.forEach(entry => {\n    var _a;\n    const vss = (_a = entry.vs) === null || _a === void 0 ? void 0 : _a.map(v => {\n      if (subgraphs[v]) {\n        return subgraphs[v].vs;\n      }\n      return v;\n    });\n    entry.vs = vss.flat();\n  });\n};\nconst mergeBarycenters = (target, other) => {\n  if (target.barycenter !== undefined) {\n    target.barycenter = (target.barycenter * target.weight + other.barycenter * other.weight) / (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n};", "map": {"version": 3, "names": ["barycenter", "resolveConflicts", "sort", "sortSubgraph", "g", "v", "cg", "biasRight", "usePrev", "keepNodeOrder", "movable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "map", "n", "id", "node", "getNode", "bl", "data", "borderLeft", "undefined", "br", "borderRight", "subgraphs", "filter", "w", "barycenters", "for<PERSON>ach", "entry", "_a", "length", "subgraphResult", "hasOwnProperty", "mergeBarycenters", "entries", "expandSubgraphs", "e", "vs", "fixorder", "order", "result", "flat", "_b", "getPredecessors", "blPred", "_c", "br<PERSON><PERSON>", "_d", "weight", "vss", "target", "other"], "sources": ["../../../src/antv-dagre/order/sort-subgraph.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,UAAU,QAAQ,cAAc;AACzC,OAAOC,gBAAmC,MAAM,qBAAqB;AACrE,SAASC,IAAI,QAAQ,QAAQ;AAE7B,OAAO,MAAMC,YAAY,GAAGA,CAC1BC,CAAQ,EACRC,CAAK,EACLC,EAAS,EACTC,SAAmB,EACnBC,OAAiB,EACjBC,aAAuB,KACrB;;EACF,IAAIC,OAAO,GAAGN,CAAC,CAACO,WAAW,CAACN,CAAC,CAAC,CAACO,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,EAAE,CAAC;EAC/C;EACA,MAAMC,IAAI,GAAGX,CAAC,CAACY,OAAO,CAACX,CAAC,CAAE;EAC1B,MAAMY,EAAE,GAAGF,IAAI,GAAIA,IAAI,CAACG,IAAI,CAACC,UAAiB,GAAGC,SAAS;EAC1D,MAAMC,EAAE,GAAGN,IAAI,GAAIA,IAAI,CAACG,IAAI,CAACI,WAAkB,GAAGF,SAAS;EAC3D,MAAMG,SAAS,GAA2C,EAAE;EAE5D,IAAIN,EAAE,EAAE;IACNP,OAAO,GAAGA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEc,MAAM,CAAEC,CAAC,IAAI;MAC9B,OAAOA,CAAC,KAAKR,EAAE,IAAIQ,CAAC,KAAKJ,EAAE;IAC7B,CAAC,CAAC;;EAGJ,MAAMK,WAAW,GAAG1B,UAAU,CAACI,CAAC,EAAEM,OAAO,IAAI,EAAE,CAAC;EAChDgB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEC,OAAO,CAAEC,KAAK,IAAI;;IAC7B,IAAI,CAAAC,EAAA,GAAAzB,CAAC,CAACO,WAAW,CAACiB,KAAK,CAACvB,CAAC,CAAC,cAAAwB,EAAA,uBAAAA,EAAA,CAAEC,MAAM,EAAE;MAClC,MAAMC,cAAc,GAAG5B,YAAY,CACjCC,CAAC,EACDwB,KAAK,CAACvB,CAAC,EACPC,EAAE,EACFC,SAAS,EACTE,aAAa,CACd;MACDc,SAAS,CAACK,KAAK,CAACvB,CAAC,CAAC,GAAG0B,cAAc;MACnC,IAAIA,cAAc,CAACC,cAAc,CAAC,YAAY,CAAC,EAAE;QAC/CC,gBAAgB,CAACL,KAAK,EAAEG,cAAc,CAAC;;;EAG7C,CAAC,CAAC;EAEF,MAAMG,OAAO,GAAGjC,gBAAgB,CAACyB,WAAW,EAAEpB,EAAE,CAAC;EACjD6B,eAAe,CAACD,OAAO,EAAEX,SAAS,CAAC;EAEnC;EACA;EACA,CAAAM,EAAA,GAAAK,OAAO,CACJV,MAAM,CAAEY,CAAC,IAAKA,CAAC,CAACC,EAAE,CAACP,MAAM,GAAG,CAAC,CAAC,cAAAD,EAAA,uBAAAA,EAAA,CAC7BF,OAAO,CAAES,CAAC,IAAI;IACd,MAAMrB,IAAI,GAAGX,CAAC,CAACY,OAAO,CAACoB,CAAC,CAACC,EAAE,CAAC,CAAC,CAAC,CAAE;IAChC,IAAItB,IAAI,EAAE;MACRqB,CAAC,CAACE,QAAQ,GAAGvB,IAAI,CAACG,IAAI,CAACoB,QAAS;MAChCF,CAAC,CAACG,KAAK,GAAGxB,IAAI,CAACG,IAAI,CAACqB,KAAM;;EAE9B,CAAC,CAAC;EAEJ,MAAMC,MAAM,GAAGtC,IAAI,CAACgC,OAAO,EAAE3B,SAAS,EAAEC,OAAO,EAAEC,aAAa,CAAC;EAE/D,IAAIQ,EAAE,EAAE;IACNuB,MAAM,CAACH,EAAE,GAAG,CAACpB,EAAE,EAAEuB,MAAM,CAACH,EAAE,EAAEhB,EAAE,CAAC,CAACoB,IAAI,EAAU;IAC9C,IAAI,CAAAC,EAAA,GAAAtC,CAAC,CAACuC,eAAe,CAAC1B,EAAE,CAAC,cAAAyB,EAAA,uBAAAA,EAAA,CAAEZ,MAAM,EAAE;MACjC,MAAMc,MAAM,GAAGxC,CAAC,CAACY,OAAO,CAAC,EAAA6B,EAAA,GAAAzC,CAAC,CAACuC,eAAe,CAAC1B,EAAE,CAAC,cAAA4B,EAAA,uBAAAA,EAAA,CAAG,CAAC,EAAE/B,EAAE,KAAI,EAAE,CAAE;MAC9D,MAAMgC,MAAM,GAAG1C,CAAC,CAACY,OAAO,CAAC,EAAA+B,EAAA,GAAA3C,CAAC,CAACuC,eAAe,CAACtB,EAAG,CAAC,cAAA0B,EAAA,uBAAAA,EAAA,CAAG,CAAC,EAAEjC,EAAE,KAAI,EAAE,CAAE;MAC/D,IAAI,CAAC0B,MAAM,CAACR,cAAc,CAAC,YAAY,CAAC,EAAE;QACxCQ,MAAM,CAACxC,UAAU,GAAG,CAAC;QACrBwC,MAAM,CAACQ,MAAM,GAAG,CAAC;;MAEnBR,MAAM,CAACxC,UAAU,GACf,CAACwC,MAAM,CAACxC,UAAW,GAAGwC,MAAM,CAACQ,MAAO,GAClCJ,MAAM,CAAC1B,IAAI,CAACqB,KAAM,GAClBO,MAAM,CAAC5B,IAAI,CAACqB,KAAM,KACnBC,MAAM,CAACQ,MAAO,GAAG,CAAC,CAAC;MACtBR,MAAM,CAACQ,MAAO,IAAI,CAAC;;;EAIvB,OAAOR,MAAM;AACf,CAAC;AAED,MAAML,eAAe,GAAGA,CACtBD,OAAwB,EACxBX,SAAiD,KAC/C;EACFW,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEP,OAAO,CAAEC,KAAK,IAAI;;IACzB,MAAMqB,GAAG,GAAG,CAAApB,EAAA,GAAAD,KAAK,CAACS,EAAE,cAAAR,EAAA,uBAAAA,EAAA,CAAEjB,GAAG,CAAEP,CAAS,IAAI;MACtC,IAAIkB,SAAS,CAAClB,CAAC,CAAC,EAAE;QAChB,OAAOkB,SAAS,CAAClB,CAAC,CAAC,CAACgC,EAAG;;MAEzB,OAAOhC,CAAC;IACV,CAAC,CAAC;IACFuB,KAAK,CAACS,EAAE,GAAGY,GAAG,CAACR,IAAI,EAAE;EACvB,CAAC,CAAC;AACJ,CAAC;AAED,MAAMR,gBAAgB,GAAGA,CACvBiB,MAAgD,EAChDC,KAA+C,KAC7C;EACF,IAAID,MAAM,CAAClD,UAAU,KAAKoB,SAAS,EAAE;IACnC8B,MAAM,CAAClD,UAAU,GACf,CAACkD,MAAM,CAAClD,UAAU,GAAGkD,MAAM,CAACF,MAAO,GAAGG,KAAK,CAACnD,UAAW,GAAGmD,KAAK,CAACH,MAAO,KACtEE,MAAM,CAACF,MAAO,GAAGG,KAAK,CAACH,MAAO,CAAC;IAClCE,MAAM,CAACF,MAAO,IAAIG,KAAK,CAACH,MAAO;GAChC,MAAM;IACLE,MAAM,CAAClD,UAAU,GAAGmD,KAAK,CAACnD,UAAU;IACpCkD,MAAM,CAACF,MAAM,GAAGG,KAAK,CAACH,MAAM;;AAEhC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}