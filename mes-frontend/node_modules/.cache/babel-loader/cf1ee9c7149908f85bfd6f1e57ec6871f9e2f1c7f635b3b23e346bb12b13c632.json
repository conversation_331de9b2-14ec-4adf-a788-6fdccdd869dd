{"ast": null, "code": "var dijkstra = require(\"./dijkstra\");\nvar _ = require(\"../lodash\");\nmodule.exports = dijkstraAll;\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return _.transform(g.nodes(), function (acc, v) {\n    acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n  }, {});\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "require", "_", "module", "exports", "dijkstraAll", "g", "weightFunc", "edgeFunc", "transform", "nodes", "acc", "v"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/dijkstra-all.js"], "sourcesContent": ["var dijkstra = require(\"./dijkstra\");\nvar _ = require(\"../lodash\");\n\nmodule.exports = dijkstraAll;\n\nfunction dijkstraAll(g, weightFunc, edgeFunc) {\n  return _.transform(g.nodes(), function(acc, v) {\n    acc[v] = dijkstra(g, v, weightFunc, edgeFunc);\n  }, {});\n}\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;AACpC,IAAIC,CAAC,GAAGD,OAAO,CAAC,WAAW,CAAC;AAE5BE,MAAM,CAACC,OAAO,GAAGC,WAAW;AAE5B,SAASA,WAAWA,CAACC,CAAC,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EAC5C,OAAON,CAAC,CAACO,SAAS,CAACH,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,UAASC,GAAG,EAAEC,CAAC,EAAE;IAC7CD,GAAG,CAACC,CAAC,CAAC,GAAGZ,QAAQ,CAACM,CAAC,EAAEM,CAAC,EAAEL,UAAU,EAAEC,QAAQ,CAAC;EAC/C,CAAC,EAAE,CAAC,CAAC,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}