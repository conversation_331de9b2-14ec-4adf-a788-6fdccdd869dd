{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = tarjan;\nfunction tarjan(g) {\n  var index = 0;\n  var stack = [];\n  var visited = {}; // node id -> { onStack, lowlink, index }\n  var results = [];\n  function dfs(v) {\n    var entry = visited[v] = {\n      onStack: true,\n      lowlink: index,\n      index: index++\n    };\n    stack.push(v);\n    g.successors(v).forEach(function (w) {\n      if (!_.has(visited, w)) {\n        dfs(w);\n        entry.lowlink = Math.min(entry.lowlink, visited[w].lowlink);\n      } else if (visited[w].onStack) {\n        entry.lowlink = Math.min(entry.lowlink, visited[w].index);\n      }\n    });\n    if (entry.lowlink === entry.index) {\n      var cmpt = [];\n      var w;\n      do {\n        w = stack.pop();\n        visited[w].onStack = false;\n        cmpt.push(w);\n      } while (v !== w);\n      results.push(cmpt);\n    }\n  }\n  g.nodes().forEach(function (v) {\n    if (!_.has(visited, v)) {\n      dfs(v);\n    }\n  });\n  return results;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "tarjan", "g", "index", "stack", "visited", "results", "dfs", "v", "entry", "onStack", "lowlink", "push", "successors", "for<PERSON>ach", "w", "has", "Math", "min", "cmpt", "pop", "nodes"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/tarjan.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = tarjan;\n\nfunction tarjan(g) {\n  var index = 0;\n  var stack = [];\n  var visited = {}; // node id -> { onStack, lowlink, index }\n  var results = [];\n\n  function dfs(v) {\n    var entry = visited[v] = {\n      onStack: true,\n      lowlink: index,\n      index: index++\n    };\n    stack.push(v);\n\n    g.successors(v).forEach(function(w) {\n      if (!_.has(visited, w)) {\n        dfs(w);\n        entry.lowlink = Math.min(entry.lowlink, visited[w].lowlink);\n      } else if (visited[w].onStack) {\n        entry.lowlink = Math.min(entry.lowlink, visited[w].index);\n      }\n    });\n\n    if (entry.lowlink === entry.index) {\n      var cmpt = [];\n      var w;\n      do {\n        w = stack.pop();\n        visited[w].onStack = false;\n        cmpt.push(w);\n      } while (v !== w);\n      results.push(cmpt);\n    }\n  }\n\n  g.nodes().forEach(function(v) {\n    if (!_.has(visited, v)) {\n      dfs(v);\n    }\n  });\n\n  return results;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,MAAM;AAEvB,SAASA,MAAMA,CAACC,CAAC,EAAE;EACjB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;EAClB,IAAIC,OAAO,GAAG,EAAE;EAEhB,SAASC,GAAGA,CAACC,CAAC,EAAE;IACd,IAAIC,KAAK,GAAGJ,OAAO,CAACG,CAAC,CAAC,GAAG;MACvBE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAER,KAAK;MACdA,KAAK,EAAEA,KAAK;IACd,CAAC;IACDC,KAAK,CAACQ,IAAI,CAACJ,CAAC,CAAC;IAEbN,CAAC,CAACW,UAAU,CAACL,CAAC,CAAC,CAACM,OAAO,CAAC,UAASC,CAAC,EAAE;MAClC,IAAI,CAAClB,CAAC,CAACmB,GAAG,CAACX,OAAO,EAAEU,CAAC,CAAC,EAAE;QACtBR,GAAG,CAACQ,CAAC,CAAC;QACNN,KAAK,CAACE,OAAO,GAAGM,IAAI,CAACC,GAAG,CAACT,KAAK,CAACE,OAAO,EAAEN,OAAO,CAACU,CAAC,CAAC,CAACJ,OAAO,CAAC;MAC7D,CAAC,MAAM,IAAIN,OAAO,CAACU,CAAC,CAAC,CAACL,OAAO,EAAE;QAC7BD,KAAK,CAACE,OAAO,GAAGM,IAAI,CAACC,GAAG,CAACT,KAAK,CAACE,OAAO,EAAEN,OAAO,CAACU,CAAC,CAAC,CAACZ,KAAK,CAAC;MAC3D;IACF,CAAC,CAAC;IAEF,IAAIM,KAAK,CAACE,OAAO,KAAKF,KAAK,CAACN,KAAK,EAAE;MACjC,IAAIgB,IAAI,GAAG,EAAE;MACb,IAAIJ,CAAC;MACL,GAAG;QACDA,CAAC,GAAGX,KAAK,CAACgB,GAAG,CAAC,CAAC;QACff,OAAO,CAACU,CAAC,CAAC,CAACL,OAAO,GAAG,KAAK;QAC1BS,IAAI,CAACP,IAAI,CAACG,CAAC,CAAC;MACd,CAAC,QAAQP,CAAC,KAAKO,CAAC;MAChBT,OAAO,CAACM,IAAI,CAACO,IAAI,CAAC;IACpB;EACF;EAEAjB,CAAC,CAACmB,KAAK,CAAC,CAAC,CAACP,OAAO,CAAC,UAASN,CAAC,EAAE;IAC5B,IAAI,CAACX,CAAC,CAACmB,GAAG,CAACX,OAAO,EAAEG,CAAC,CAAC,EAAE;MACtBD,GAAG,CAACC,CAAC,CAAC;IACR;EACF,CAAC,CAAC;EAEF,OAAOF,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}