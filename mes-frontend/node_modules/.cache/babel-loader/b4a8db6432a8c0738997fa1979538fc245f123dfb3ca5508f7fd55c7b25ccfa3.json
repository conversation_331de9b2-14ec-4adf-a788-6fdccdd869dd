{"ast": null, "code": "export { HTML } from '@antv/g';", "map": {"version": 3, "names": ["HTML"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/HTML.ts"], "sourcesContent": ["import type { HTMLStyleProps as GHTMLStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { HTML } from '@antv/g';\nexport type HTMLStyleProps = OmitConflictStyleProps<GHTMLStyleProps>;\n"], "mappings": "AAGA,SAASA,IAAI,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}