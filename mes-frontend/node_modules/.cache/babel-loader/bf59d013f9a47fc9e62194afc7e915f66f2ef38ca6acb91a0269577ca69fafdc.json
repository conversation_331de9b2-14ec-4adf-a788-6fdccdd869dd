{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar acyclic = require(\"./acyclic\");\nvar normalize = require(\"./normalize\");\nvar rank = require(\"./rank\");\nvar normalizeRanks = require(\"./util\").normalizeRanks;\nvar parentDummyChains = require(\"./parent-dummy-chains\");\nvar removeEmptyRanks = require(\"./util\").removeEmptyRanks;\nvar nestingGraph = require(\"./nesting-graph\");\nvar addBorderSegments = require(\"./add-border-segments\");\nvar coordinateSystem = require(\"./coordinate-system\");\nvar order = require(\"./order\");\nvar position = require(\"./position\");\nvar util = require(\"./util\");\nvar Graph = require(\"./graphlib\").Graph;\nmodule.exports = layout;\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time(\"layout\", function () {\n    var layoutGraph = time(\"  buildLayoutGraph\", function () {\n      return buildLayoutGraph(g);\n    });\n    time(\"  runLayout\", function () {\n      runLayout(layoutGraph, time);\n    });\n    time(\"  updateInputGraph\", function () {\n      updateInputGraph(g, layoutGraph);\n    });\n  });\n}\nfunction runLayout(g, time) {\n  time(\"    makeSpaceForEdgeLabels\", function () {\n    makeSpaceForEdgeLabels(g);\n  });\n  time(\"    removeSelfEdges\", function () {\n    removeSelfEdges(g);\n  });\n  time(\"    acyclic\", function () {\n    acyclic.run(g);\n  });\n  time(\"    nestingGraph.run\", function () {\n    nestingGraph.run(g);\n  });\n  time(\"    rank\", function () {\n    rank(util.asNonCompoundGraph(g));\n  });\n  time(\"    injectEdgeLabelProxies\", function () {\n    injectEdgeLabelProxies(g);\n  });\n  time(\"    removeEmptyRanks\", function () {\n    removeEmptyRanks(g);\n  });\n  time(\"    nestingGraph.cleanup\", function () {\n    nestingGraph.cleanup(g);\n  });\n  time(\"    normalizeRanks\", function () {\n    normalizeRanks(g);\n  });\n  time(\"    assignRankMinMax\", function () {\n    assignRankMinMax(g);\n  });\n  time(\"    removeEdgeLabelProxies\", function () {\n    removeEdgeLabelProxies(g);\n  });\n  time(\"    normalize.run\", function () {\n    normalize.run(g);\n  });\n  time(\"    parentDummyChains\", function () {\n    parentDummyChains(g);\n  });\n  time(\"    addBorderSegments\", function () {\n    addBorderSegments(g);\n  });\n  time(\"    order\", function () {\n    order(g);\n  });\n  time(\"    insertSelfEdges\", function () {\n    insertSelfEdges(g);\n  });\n  time(\"    adjustCoordinateSystem\", function () {\n    coordinateSystem.adjust(g);\n  });\n  time(\"    position\", function () {\n    position(g);\n  });\n  time(\"    positionSelfEdges\", function () {\n    positionSelfEdges(g);\n  });\n  time(\"    removeBorderNodes\", function () {\n    removeBorderNodes(g);\n  });\n  time(\"    normalize.undo\", function () {\n    normalize.undo(g);\n  });\n  time(\"    fixupEdgeLabelCoords\", function () {\n    fixupEdgeLabelCoords(g);\n  });\n  time(\"    undoCoordinateSystem\", function () {\n    coordinateSystem.undo(g);\n  });\n  time(\"    translateGraph\", function () {\n    translateGraph(g);\n  });\n  time(\"    assignNodeIntersects\", function () {\n    assignNodeIntersects(g);\n  });\n  time(\"    reversePoints\", function () {\n    reversePointsForReversedEdges(g);\n  });\n  time(\"    acyclic.undo\", function () {\n    acyclic.undo(g);\n  });\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function (v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n  _.forEach(inputGraph.edges(), function (e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n    inputLabel.points = layoutLabel.points;\n    if (_.has(layoutLabel, \"x\")) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\nvar graphNumAttrs = [\"nodesep\", \"edgesep\", \"ranksep\", \"marginx\", \"marginy\"];\nvar graphDefaults = {\n  ranksep: 50,\n  edgesep: 20,\n  nodesep: 50,\n  rankdir: \"tb\"\n};\nvar graphAttrs = [\"acyclicer\", \"ranker\", \"rankdir\", \"align\"];\nvar nodeNumAttrs = [\"width\", \"height\"];\nvar nodeDefaults = {\n  width: 0,\n  height: 0\n};\nvar edgeNumAttrs = [\"minlen\", \"weight\", \"width\", \"height\", \"labeloffset\"];\nvar edgeDefaults = {\n  minlen: 1,\n  weight: 1,\n  width: 0,\n  height: 0,\n  labeloffset: 10,\n  labelpos: \"r\"\n};\nvar edgeAttrs = [\"labelpos\"];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({\n    multigraph: true,\n    compound: true\n  });\n  var graph = canonicalize(inputGraph.graph());\n  g.setGraph(_.merge({}, graphDefaults, selectNumberAttrs(graph, graphNumAttrs), _.pick(graph, graphAttrs)));\n  _.forEach(inputGraph.nodes(), function (v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n  _.forEach(inputGraph.edges(), function (e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(e, _.merge({}, edgeDefaults, selectNumberAttrs(edge, edgeNumAttrs), _.pick(edge, edgeAttrs)));\n  });\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== \"c\") {\n      if (graph.rankdir === \"TB\" || graph.rankdir === \"BT\") {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = {\n        rank: (w.rank - v.rank) / 2 + v.rank,\n        e: e\n      };\n      util.addDummyNode(g, \"edge-proxy\", label, \"_ep\");\n    }\n  });\n}\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === \"edge-proxy\") {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n  _.forEach(g.nodes(), function (v) {\n    getExtremes(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (_.has(edge, \"x\")) {\n      getExtremes(edge);\n    }\n  });\n  minX -= marginX;\n  minY -= marginY;\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function (p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (_.has(edge, \"x\")) {\n      edge.x -= minX;\n    }\n    if (_.has(edge, \"y\")) {\n      edge.y -= minY;\n    }\n  });\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (_.has(edge, \"x\")) {\n      if (edge.labelpos === \"l\" || edge.labelpos === \"r\") {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n        case \"l\":\n          edge.x -= edge.width / 2 + edge.labeloffset;\n          break;\n        case \"r\":\n          edge.x += edge.width / 2 + edge.labeloffset;\n          break;\n      }\n    }\n  });\n}\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function (v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n  _.forEach(g.nodes(), function (v) {\n    if (g.node(v).dummy === \"border\") {\n      g.removeNode(v);\n    }\n  });\n}\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function (e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({\n        e: e,\n        label: g.edge(e)\n      });\n      g.removeEdge(e);\n    }\n  });\n}\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function (layer) {\n    var orderShift = 0;\n    _.forEach(layer, function (v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function (selfEdge) {\n        util.addDummyNode(g, \"selfedge\", {\n          width: selfEdge.label.width,\n          height: selfEdge.label.height,\n          rank: node.rank,\n          order: i + ++orderShift,\n          e: selfEdge.e,\n          label: selfEdge.label\n        }, \"_se\");\n      });\n      delete node.selfEdges;\n    });\n  });\n}\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (node.dummy === \"selfedge\") {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [{\n        x: x + 2 * dx / 3,\n        y: y - dy\n      }, {\n        x: x + 5 * dx / 6,\n        y: y - dy\n      }, {\n        x: x + dx,\n        y: y\n      }, {\n        x: x + 5 * dx / 6,\n        y: y + dy\n      }, {\n        x: x + 2 * dx / 3,\n        y: y + dy\n      }];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function (v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}", "map": {"version": 3, "names": ["_", "require", "acyclic", "normalize", "rank", "normalizeRanks", "parent<PERSON>ummy<PERSON><PERSON><PERSON>", "removeEmptyRanks", "nestingGraph", "addBorderSegments", "coordinateSystem", "order", "position", "util", "Graph", "module", "exports", "layout", "g", "opts", "time", "debugTiming", "notime", "layoutGraph", "buildLayoutGraph", "runLayout", "updateInputGraph", "makeSpaceForEdgeLabels", "removeSelfEdges", "run", "asNonCompoundGraph", "injectEdgeLabelProxies", "cleanup", "assignRankMinMax", "removeEdgeLabelProxies", "insertSelf<PERSON>dges", "adjust", "position<PERSON><PERSON><PERSON><PERSON>", "removeBorderNodes", "undo", "fixupEdgeLabelCoords", "translateGraph", "assignNodeIntersects", "reversePointsForReversedEdges", "inputGraph", "for<PERSON>ach", "nodes", "v", "inputLabel", "node", "layoutLabel", "x", "y", "children", "length", "width", "height", "edges", "e", "edge", "points", "has", "graph", "graphNumAttrs", "graphDefaults", "ranksep", "edgesep", "nodesep", "rankdir", "graphAttrs", "nodeNumAttrs", "nodeDefaults", "edgeNumAttrs", "edgeDefaults", "minlen", "weight", "labeloffset", "labelpos", "edgeAttrs", "multigraph", "compound", "canonicalize", "setGraph", "merge", "selectNumberAttrs", "pick", "setNode", "defaults", "setParent", "parent", "setEdge", "toLowerCase", "w", "label", "addDummyNode", "maxRank", "borderTop", "minRank", "borderBottom", "max", "dummy", "labelRank", "removeNode", "minX", "Number", "POSITIVE_INFINITY", "maxX", "minY", "maxY", "graphLabel", "marginX", "marginx", "marginY", "marginy", "getExtremes", "attrs", "h", "Math", "min", "p", "nodeV", "nodeW", "p1", "p2", "unshift", "intersectRect", "push", "reversed", "reverse", "t", "b", "l", "last", "borderLeft", "r", "borderRight", "abs", "selfEdges", "removeEdge", "layers", "buildLayerMatrix", "layer", "orderShift", "i", "selfEdge", "selfNode", "dx", "dy", "obj", "mapValues", "newAttrs", "k"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/layout.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar acyclic = require(\"./acyclic\");\nvar normalize = require(\"./normalize\");\nvar rank = require(\"./rank\");\nvar normalizeRanks = require(\"./util\").normalizeRanks;\nvar parentDummyChains = require(\"./parent-dummy-chains\");\nvar removeEmptyRanks = require(\"./util\").removeEmptyRanks;\nvar nestingGraph = require(\"./nesting-graph\");\nvar addBorderSegments = require(\"./add-border-segments\");\nvar coordinateSystem = require(\"./coordinate-system\");\nvar order = require(\"./order\");\nvar position = require(\"./position\");\nvar util = require(\"./util\");\nvar Graph = require(\"./graphlib\").Graph;\n\nmodule.exports = layout;\n\nfunction layout(g, opts) {\n  var time = opts && opts.debugTiming ? util.time : util.notime;\n  time(\"layout\", function() {\n    var layoutGraph = \n      time(\"  buildLayoutGraph\", function() { return buildLayoutGraph(g); });\n    time(\"  runLayout\",        function() { runLayout(layoutGraph, time); });\n    time(\"  updateInputGraph\", function() { updateInputGraph(g, layoutGraph); });\n  });\n}\n\nfunction runLayout(g, time) {\n  time(\"    makeSpaceForEdgeLabels\", function() { makeSpaceForEdgeLabels(g); });\n  time(\"    removeSelfEdges\",        function() { removeSelfEdges(g); });\n  time(\"    acyclic\",                function() { acyclic.run(g); });\n  time(\"    nestingGraph.run\",       function() { nestingGraph.run(g); });\n  time(\"    rank\",                   function() { rank(util.asNonCompoundGraph(g)); });\n  time(\"    injectEdgeLabelProxies\", function() { injectEdgeLabelProxies(g); });\n  time(\"    removeEmptyRanks\",       function() { removeEmptyRanks(g); });\n  time(\"    nestingGraph.cleanup\",   function() { nestingGraph.cleanup(g); });\n  time(\"    normalizeRanks\",         function() { normalizeRanks(g); });\n  time(\"    assignRankMinMax\",       function() { assignRankMinMax(g); });\n  time(\"    removeEdgeLabelProxies\", function() { removeEdgeLabelProxies(g); });\n  time(\"    normalize.run\",          function() { normalize.run(g); });\n  time(\"    parentDummyChains\",      function() { parentDummyChains(g); });\n  time(\"    addBorderSegments\",      function() { addBorderSegments(g); });\n  time(\"    order\",                  function() { order(g); });\n  time(\"    insertSelfEdges\",        function() { insertSelfEdges(g); });\n  time(\"    adjustCoordinateSystem\", function() { coordinateSystem.adjust(g); });\n  time(\"    position\",               function() { position(g); });\n  time(\"    positionSelfEdges\",      function() { positionSelfEdges(g); });\n  time(\"    removeBorderNodes\",      function() { removeBorderNodes(g); });\n  time(\"    normalize.undo\",         function() { normalize.undo(g); });\n  time(\"    fixupEdgeLabelCoords\",   function() { fixupEdgeLabelCoords(g); });\n  time(\"    undoCoordinateSystem\",   function() { coordinateSystem.undo(g); });\n  time(\"    translateGraph\",         function() { translateGraph(g); });\n  time(\"    assignNodeIntersects\",   function() { assignNodeIntersects(g); });\n  time(\"    reversePoints\",          function() { reversePointsForReversedEdges(g); });\n  time(\"    acyclic.undo\",           function() { acyclic.undo(g); });\n}\n\n/*\n * Copies final layout information from the layout graph back to the input\n * graph. This process only copies whitelisted attributes from the layout graph\n * to the input graph, so it serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction updateInputGraph(inputGraph, layoutGraph) {\n  _.forEach(inputGraph.nodes(), function(v) {\n    var inputLabel = inputGraph.node(v);\n    var layoutLabel = layoutGraph.node(v);\n\n    if (inputLabel) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n\n      if (layoutGraph.children(v).length) {\n        inputLabel.width = layoutLabel.width;\n        inputLabel.height = layoutLabel.height;\n      }\n    }\n  });\n\n  _.forEach(inputGraph.edges(), function(e) {\n    var inputLabel = inputGraph.edge(e);\n    var layoutLabel = layoutGraph.edge(e);\n\n    inputLabel.points = layoutLabel.points;\n    if (_.has(layoutLabel, \"x\")) {\n      inputLabel.x = layoutLabel.x;\n      inputLabel.y = layoutLabel.y;\n    }\n  });\n\n  inputGraph.graph().width = layoutGraph.graph().width;\n  inputGraph.graph().height = layoutGraph.graph().height;\n}\n\nvar graphNumAttrs = [\"nodesep\", \"edgesep\", \"ranksep\", \"marginx\", \"marginy\"];\nvar graphDefaults = { ranksep: 50, edgesep: 20, nodesep: 50, rankdir: \"tb\" };\nvar graphAttrs = [\"acyclicer\", \"ranker\", \"rankdir\", \"align\"];\nvar nodeNumAttrs = [\"width\", \"height\"];\nvar nodeDefaults = { width: 0, height: 0 };\nvar edgeNumAttrs = [\"minlen\", \"weight\", \"width\", \"height\", \"labeloffset\"];\nvar edgeDefaults = {\n  minlen: 1, weight: 1, width: 0, height: 0,\n  labeloffset: 10, labelpos: \"r\"\n};\nvar edgeAttrs = [\"labelpos\"];\n\n/*\n * Constructs a new graph from the input graph, which can be used for layout.\n * This process copies only whitelisted attributes from the input graph to the\n * layout graph. Thus this function serves as a good place to determine what\n * attributes can influence layout.\n */\nfunction buildLayoutGraph(inputGraph) {\n  var g = new Graph({ multigraph: true, compound: true });\n  var graph = canonicalize(inputGraph.graph());\n\n  g.setGraph(_.merge({},\n    graphDefaults,\n    selectNumberAttrs(graph, graphNumAttrs),\n    _.pick(graph, graphAttrs)));\n\n  _.forEach(inputGraph.nodes(), function(v) {\n    var node = canonicalize(inputGraph.node(v));\n    g.setNode(v, _.defaults(selectNumberAttrs(node, nodeNumAttrs), nodeDefaults));\n    g.setParent(v, inputGraph.parent(v));\n  });\n\n  _.forEach(inputGraph.edges(), function(e) {\n    var edge = canonicalize(inputGraph.edge(e));\n    g.setEdge(e, _.merge({},\n      edgeDefaults,\n      selectNumberAttrs(edge, edgeNumAttrs),\n      _.pick(edge, edgeAttrs)));\n  });\n\n  return g;\n}\n\n/*\n * This idea comes from the Gansner paper: to account for edge labels in our\n * layout we split each rank in half by doubling minlen and halving ranksep.\n * Then we can place labels at these mid-points between nodes.\n *\n * We also add some minimal padding to the width to push the label for the edge\n * away from the edge itself a bit.\n */\nfunction makeSpaceForEdgeLabels(g) {\n  var graph = g.graph();\n  graph.ranksep /= 2;\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    edge.minlen *= 2;\n    if (edge.labelpos.toLowerCase() !== \"c\") {\n      if (graph.rankdir === \"TB\" || graph.rankdir === \"BT\") {\n        edge.width += edge.labeloffset;\n      } else {\n        edge.height += edge.labeloffset;\n      }\n    }\n  });\n}\n\n/*\n * Creates temporary dummy nodes that capture the rank in which each edge's\n * label is going to, if it has one of non-zero width and height. We do this\n * so that we can safely remove empty ranks while preserving balance for the\n * label's position.\n */\nfunction injectEdgeLabelProxies(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (edge.width && edge.height) {\n      var v = g.node(e.v);\n      var w = g.node(e.w);\n      var label = { rank: (w.rank - v.rank) / 2 + v.rank, e: e };\n      util.addDummyNode(g, \"edge-proxy\", label, \"_ep\");\n    }\n  });\n}\n\nfunction assignRankMinMax(g) {\n  var maxRank = 0;\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (node.borderTop) {\n      node.minRank = g.node(node.borderTop).rank;\n      node.maxRank = g.node(node.borderBottom).rank;\n      maxRank = _.max(maxRank, node.maxRank);\n    }\n  });\n  g.graph().maxRank = maxRank;\n}\n\nfunction removeEdgeLabelProxies(g) {\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (node.dummy === \"edge-proxy\") {\n      g.edge(node.e).labelRank = node.rank;\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction translateGraph(g) {\n  var minX = Number.POSITIVE_INFINITY;\n  var maxX = 0;\n  var minY = Number.POSITIVE_INFINITY;\n  var maxY = 0;\n  var graphLabel = g.graph();\n  var marginX = graphLabel.marginx || 0;\n  var marginY = graphLabel.marginy || 0;\n\n  function getExtremes(attrs) {\n    var x = attrs.x;\n    var y = attrs.y;\n    var w = attrs.width;\n    var h = attrs.height;\n    minX = Math.min(minX, x - w / 2);\n    maxX = Math.max(maxX, x + w / 2);\n    minY = Math.min(minY, y - h / 2);\n    maxY = Math.max(maxY, y + h / 2);\n  }\n\n  _.forEach(g.nodes(), function(v) { getExtremes(g.node(v)); });\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (_.has(edge, \"x\")) {\n      getExtremes(edge);\n    }\n  });\n\n  minX -= marginX;\n  minY -= marginY;\n\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    node.x -= minX;\n    node.y -= minY;\n  });\n\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, function(p) {\n      p.x -= minX;\n      p.y -= minY;\n    });\n    if (_.has(edge, \"x\")) { edge.x -= minX; }\n    if (_.has(edge, \"y\")) { edge.y -= minY; }\n  });\n\n  graphLabel.width = maxX - minX + marginX;\n  graphLabel.height = maxY - minY + marginY;\n}\n\nfunction assignNodeIntersects(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    var nodeV = g.node(e.v);\n    var nodeW = g.node(e.w);\n    var p1, p2;\n    if (!edge.points) {\n      edge.points = [];\n      p1 = nodeW;\n      p2 = nodeV;\n    } else {\n      p1 = edge.points[0];\n      p2 = edge.points[edge.points.length - 1];\n    }\n    edge.points.unshift(util.intersectRect(nodeV, p1));\n    edge.points.push(util.intersectRect(nodeW, p2));\n  });\n}\n\nfunction fixupEdgeLabelCoords(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (_.has(edge, \"x\")) {\n      if (edge.labelpos === \"l\" || edge.labelpos === \"r\") {\n        edge.width -= edge.labeloffset;\n      }\n      switch (edge.labelpos) {\n      case \"l\": edge.x -= edge.width / 2 + edge.labeloffset; break;\n      case \"r\": edge.x += edge.width / 2 + edge.labeloffset; break;\n      }\n    }\n  });\n}\n\nfunction reversePointsForReversedEdges(g) {\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    if (edge.reversed) {\n      edge.points.reverse();\n    }\n  });\n}\n\nfunction removeBorderNodes(g) {\n  _.forEach(g.nodes(), function(v) {\n    if (g.children(v).length) {\n      var node = g.node(v);\n      var t = g.node(node.borderTop);\n      var b = g.node(node.borderBottom);\n      var l = g.node(_.last(node.borderLeft));\n      var r = g.node(_.last(node.borderRight));\n\n      node.width = Math.abs(r.x - l.x);\n      node.height = Math.abs(b.y - t.y);\n      node.x = l.x + node.width / 2;\n      node.y = t.y + node.height / 2;\n    }\n  });\n\n  _.forEach(g.nodes(), function(v) {\n    if (g.node(v).dummy === \"border\") {\n      g.removeNode(v);\n    }\n  });\n}\n\nfunction removeSelfEdges(g) {\n  _.forEach(g.edges(), function(e) {\n    if (e.v === e.w) {\n      var node = g.node(e.v);\n      if (!node.selfEdges) {\n        node.selfEdges = [];\n      }\n      node.selfEdges.push({ e: e, label: g.edge(e) });\n      g.removeEdge(e);\n    }\n  });\n}\n\nfunction insertSelfEdges(g) {\n  var layers = util.buildLayerMatrix(g);\n  _.forEach(layers, function(layer) {\n    var orderShift = 0;\n    _.forEach(layer, function(v, i) {\n      var node = g.node(v);\n      node.order = i + orderShift;\n      _.forEach(node.selfEdges, function(selfEdge) {\n        util.addDummyNode(g, \"selfedge\", {\n          width: selfEdge.label.width,\n          height: selfEdge.label.height,\n          rank: node.rank,\n          order: i + (++orderShift),\n          e: selfEdge.e,\n          label: selfEdge.label\n        }, \"_se\");\n      });\n      delete node.selfEdges;\n    });\n  });\n}\n\nfunction positionSelfEdges(g) {\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (node.dummy === \"selfedge\") {\n      var selfNode = g.node(node.e.v);\n      var x = selfNode.x + selfNode.width / 2;\n      var y = selfNode.y;\n      var dx = node.x - x;\n      var dy = selfNode.height / 2;\n      g.setEdge(node.e, node.label);\n      g.removeNode(v);\n      node.label.points = [\n        { x: x + 2 * dx / 3, y: y - dy },\n        { x: x + 5 * dx / 6, y: y - dy },\n        { x: x +     dx    , y: y },\n        { x: x + 5 * dx / 6, y: y + dy },\n        { x: x + 2 * dx / 3, y: y + dy }\n      ];\n      node.label.x = node.x;\n      node.label.y = node.y;\n    }\n  });\n}\n\nfunction selectNumberAttrs(obj, attrs) {\n  return _.mapValues(_.pick(obj, attrs), Number);\n}\n\nfunction canonicalize(attrs) {\n  var newAttrs = {};\n  _.forEach(attrs, function(v, k) {\n    newAttrs[k.toLowerCase()] = v;\n  });\n  return newAttrs;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;AAClC,IAAIE,SAAS,GAAGF,OAAO,CAAC,aAAa,CAAC;AACtC,IAAIG,IAAI,GAAGH,OAAO,CAAC,QAAQ,CAAC;AAC5B,IAAII,cAAc,GAAGJ,OAAO,CAAC,QAAQ,CAAC,CAACI,cAAc;AACrD,IAAIC,iBAAiB,GAAGL,OAAO,CAAC,uBAAuB,CAAC;AACxD,IAAIM,gBAAgB,GAAGN,OAAO,CAAC,QAAQ,CAAC,CAACM,gBAAgB;AACzD,IAAIC,YAAY,GAAGP,OAAO,CAAC,iBAAiB,CAAC;AAC7C,IAAIQ,iBAAiB,GAAGR,OAAO,CAAC,uBAAuB,CAAC;AACxD,IAAIS,gBAAgB,GAAGT,OAAO,CAAC,qBAAqB,CAAC;AACrD,IAAIU,KAAK,GAAGV,OAAO,CAAC,SAAS,CAAC;AAC9B,IAAIW,QAAQ,GAAGX,OAAO,CAAC,YAAY,CAAC;AACpC,IAAIY,IAAI,GAAGZ,OAAO,CAAC,QAAQ,CAAC;AAC5B,IAAIa,KAAK,GAAGb,OAAO,CAAC,YAAY,CAAC,CAACa,KAAK;AAEvCC,MAAM,CAACC,OAAO,GAAGC,MAAM;AAEvB,SAASA,MAAMA,CAACC,CAAC,EAAEC,IAAI,EAAE;EACvB,IAAIC,IAAI,GAAGD,IAAI,IAAIA,IAAI,CAACE,WAAW,GAAGR,IAAI,CAACO,IAAI,GAAGP,IAAI,CAACS,MAAM;EAC7DF,IAAI,CAAC,QAAQ,EAAE,YAAW;IACxB,IAAIG,WAAW,GACbH,IAAI,CAAC,oBAAoB,EAAE,YAAW;MAAE,OAAOI,gBAAgB,CAACN,CAAC,CAAC;IAAE,CAAC,CAAC;IACxEE,IAAI,CAAC,aAAa,EAAS,YAAW;MAAEK,SAAS,CAACF,WAAW,EAAEH,IAAI,CAAC;IAAE,CAAC,CAAC;IACxEA,IAAI,CAAC,oBAAoB,EAAE,YAAW;MAAEM,gBAAgB,CAACR,CAAC,EAAEK,WAAW,CAAC;IAAE,CAAC,CAAC;EAC9E,CAAC,CAAC;AACJ;AAEA,SAASE,SAASA,CAACP,CAAC,EAAEE,IAAI,EAAE;EAC1BA,IAAI,CAAC,4BAA4B,EAAE,YAAW;IAAEO,sBAAsB,CAACT,CAAC,CAAC;EAAE,CAAC,CAAC;EAC7EE,IAAI,CAAC,qBAAqB,EAAS,YAAW;IAAEQ,eAAe,CAACV,CAAC,CAAC;EAAE,CAAC,CAAC;EACtEE,IAAI,CAAC,aAAa,EAAiB,YAAW;IAAElB,OAAO,CAAC2B,GAAG,CAACX,CAAC,CAAC;EAAE,CAAC,CAAC;EAClEE,IAAI,CAAC,sBAAsB,EAAQ,YAAW;IAAEZ,YAAY,CAACqB,GAAG,CAACX,CAAC,CAAC;EAAE,CAAC,CAAC;EACvEE,IAAI,CAAC,UAAU,EAAoB,YAAW;IAAEhB,IAAI,CAACS,IAAI,CAACiB,kBAAkB,CAACZ,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;EACpFE,IAAI,CAAC,4BAA4B,EAAE,YAAW;IAAEW,sBAAsB,CAACb,CAAC,CAAC;EAAE,CAAC,CAAC;EAC7EE,IAAI,CAAC,sBAAsB,EAAQ,YAAW;IAAEb,gBAAgB,CAACW,CAAC,CAAC;EAAE,CAAC,CAAC;EACvEE,IAAI,CAAC,0BAA0B,EAAI,YAAW;IAAEZ,YAAY,CAACwB,OAAO,CAACd,CAAC,CAAC;EAAE,CAAC,CAAC;EAC3EE,IAAI,CAAC,oBAAoB,EAAU,YAAW;IAAEf,cAAc,CAACa,CAAC,CAAC;EAAE,CAAC,CAAC;EACrEE,IAAI,CAAC,sBAAsB,EAAQ,YAAW;IAAEa,gBAAgB,CAACf,CAAC,CAAC;EAAE,CAAC,CAAC;EACvEE,IAAI,CAAC,4BAA4B,EAAE,YAAW;IAAEc,sBAAsB,CAAChB,CAAC,CAAC;EAAE,CAAC,CAAC;EAC7EE,IAAI,CAAC,mBAAmB,EAAW,YAAW;IAAEjB,SAAS,CAAC0B,GAAG,CAACX,CAAC,CAAC;EAAE,CAAC,CAAC;EACpEE,IAAI,CAAC,uBAAuB,EAAO,YAAW;IAAEd,iBAAiB,CAACY,CAAC,CAAC;EAAE,CAAC,CAAC;EACxEE,IAAI,CAAC,uBAAuB,EAAO,YAAW;IAAEX,iBAAiB,CAACS,CAAC,CAAC;EAAE,CAAC,CAAC;EACxEE,IAAI,CAAC,WAAW,EAAmB,YAAW;IAAET,KAAK,CAACO,CAAC,CAAC;EAAE,CAAC,CAAC;EAC5DE,IAAI,CAAC,qBAAqB,EAAS,YAAW;IAAEe,eAAe,CAACjB,CAAC,CAAC;EAAE,CAAC,CAAC;EACtEE,IAAI,CAAC,4BAA4B,EAAE,YAAW;IAAEV,gBAAgB,CAAC0B,MAAM,CAAClB,CAAC,CAAC;EAAE,CAAC,CAAC;EAC9EE,IAAI,CAAC,cAAc,EAAgB,YAAW;IAAER,QAAQ,CAACM,CAAC,CAAC;EAAE,CAAC,CAAC;EAC/DE,IAAI,CAAC,uBAAuB,EAAO,YAAW;IAAEiB,iBAAiB,CAACnB,CAAC,CAAC;EAAE,CAAC,CAAC;EACxEE,IAAI,CAAC,uBAAuB,EAAO,YAAW;IAAEkB,iBAAiB,CAACpB,CAAC,CAAC;EAAE,CAAC,CAAC;EACxEE,IAAI,CAAC,oBAAoB,EAAU,YAAW;IAAEjB,SAAS,CAACoC,IAAI,CAACrB,CAAC,CAAC;EAAE,CAAC,CAAC;EACrEE,IAAI,CAAC,0BAA0B,EAAI,YAAW;IAAEoB,oBAAoB,CAACtB,CAAC,CAAC;EAAE,CAAC,CAAC;EAC3EE,IAAI,CAAC,0BAA0B,EAAI,YAAW;IAAEV,gBAAgB,CAAC6B,IAAI,CAACrB,CAAC,CAAC;EAAE,CAAC,CAAC;EAC5EE,IAAI,CAAC,oBAAoB,EAAU,YAAW;IAAEqB,cAAc,CAACvB,CAAC,CAAC;EAAE,CAAC,CAAC;EACrEE,IAAI,CAAC,0BAA0B,EAAI,YAAW;IAAEsB,oBAAoB,CAACxB,CAAC,CAAC;EAAE,CAAC,CAAC;EAC3EE,IAAI,CAAC,mBAAmB,EAAW,YAAW;IAAEuB,6BAA6B,CAACzB,CAAC,CAAC;EAAE,CAAC,CAAC;EACpFE,IAAI,CAAC,kBAAkB,EAAY,YAAW;IAAElB,OAAO,CAACqC,IAAI,CAACrB,CAAC,CAAC;EAAE,CAAC,CAAC;AACrE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,gBAAgBA,CAACkB,UAAU,EAAErB,WAAW,EAAE;EACjDvB,CAAC,CAAC6C,OAAO,CAACD,UAAU,CAACE,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IACxC,IAAIC,UAAU,GAAGJ,UAAU,CAACK,IAAI,CAACF,CAAC,CAAC;IACnC,IAAIG,WAAW,GAAG3B,WAAW,CAAC0B,IAAI,CAACF,CAAC,CAAC;IAErC,IAAIC,UAAU,EAAE;MACdA,UAAU,CAACG,CAAC,GAAGD,WAAW,CAACC,CAAC;MAC5BH,UAAU,CAACI,CAAC,GAAGF,WAAW,CAACE,CAAC;MAE5B,IAAI7B,WAAW,CAAC8B,QAAQ,CAACN,CAAC,CAAC,CAACO,MAAM,EAAE;QAClCN,UAAU,CAACO,KAAK,GAAGL,WAAW,CAACK,KAAK;QACpCP,UAAU,CAACQ,MAAM,GAAGN,WAAW,CAACM,MAAM;MACxC;IACF;EACF,CAAC,CAAC;EAEFxD,CAAC,CAAC6C,OAAO,CAACD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IACxC,IAAIV,UAAU,GAAGJ,UAAU,CAACe,IAAI,CAACD,CAAC,CAAC;IACnC,IAAIR,WAAW,GAAG3B,WAAW,CAACoC,IAAI,CAACD,CAAC,CAAC;IAErCV,UAAU,CAACY,MAAM,GAAGV,WAAW,CAACU,MAAM;IACtC,IAAI5D,CAAC,CAAC6D,GAAG,CAACX,WAAW,EAAE,GAAG,CAAC,EAAE;MAC3BF,UAAU,CAACG,CAAC,GAAGD,WAAW,CAACC,CAAC;MAC5BH,UAAU,CAACI,CAAC,GAAGF,WAAW,CAACE,CAAC;IAC9B;EACF,CAAC,CAAC;EAEFR,UAAU,CAACkB,KAAK,CAAC,CAAC,CAACP,KAAK,GAAGhC,WAAW,CAACuC,KAAK,CAAC,CAAC,CAACP,KAAK;EACpDX,UAAU,CAACkB,KAAK,CAAC,CAAC,CAACN,MAAM,GAAGjC,WAAW,CAACuC,KAAK,CAAC,CAAC,CAACN,MAAM;AACxD;AAEA,IAAIO,aAAa,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;AAC3E,IAAIC,aAAa,GAAG;EAAEC,OAAO,EAAE,EAAE;EAAEC,OAAO,EAAE,EAAE;EAAEC,OAAO,EAAE,EAAE;EAAEC,OAAO,EAAE;AAAK,CAAC;AAC5E,IAAIC,UAAU,GAAG,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,CAAC;AAC5D,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC;AACtC,IAAIC,YAAY,GAAG;EAAEhB,KAAK,EAAE,CAAC;EAAEC,MAAM,EAAE;AAAE,CAAC;AAC1C,IAAIgB,YAAY,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;AACzE,IAAIC,YAAY,GAAG;EACjBC,MAAM,EAAE,CAAC;EAAEC,MAAM,EAAE,CAAC;EAAEpB,KAAK,EAAE,CAAC;EAAEC,MAAM,EAAE,CAAC;EACzCoB,WAAW,EAAE,EAAE;EAAEC,QAAQ,EAAE;AAC7B,CAAC;AACD,IAAIC,SAAS,GAAG,CAAC,UAAU,CAAC;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA,SAAStD,gBAAgBA,CAACoB,UAAU,EAAE;EACpC,IAAI1B,CAAC,GAAG,IAAIJ,KAAK,CAAC;IAAEiE,UAAU,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvD,IAAIlB,KAAK,GAAGmB,YAAY,CAACrC,UAAU,CAACkB,KAAK,CAAC,CAAC,CAAC;EAE5C5C,CAAC,CAACgE,QAAQ,CAAClF,CAAC,CAACmF,KAAK,CAAC,CAAC,CAAC,EACnBnB,aAAa,EACboB,iBAAiB,CAACtB,KAAK,EAAEC,aAAa,CAAC,EACvC/D,CAAC,CAACqF,IAAI,CAACvB,KAAK,EAAEO,UAAU,CAAC,CAAC,CAAC;EAE7BrE,CAAC,CAAC6C,OAAO,CAACD,UAAU,CAACE,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IACxC,IAAIE,IAAI,GAAGgC,YAAY,CAACrC,UAAU,CAACK,IAAI,CAACF,CAAC,CAAC,CAAC;IAC3C7B,CAAC,CAACoE,OAAO,CAACvC,CAAC,EAAE/C,CAAC,CAACuF,QAAQ,CAACH,iBAAiB,CAACnC,IAAI,EAAEqB,YAAY,CAAC,EAAEC,YAAY,CAAC,CAAC;IAC7ErD,CAAC,CAACsE,SAAS,CAACzC,CAAC,EAAEH,UAAU,CAAC6C,MAAM,CAAC1C,CAAC,CAAC,CAAC;EACtC,CAAC,CAAC;EAEF/C,CAAC,CAAC6C,OAAO,CAACD,UAAU,CAACa,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IACxC,IAAIC,IAAI,GAAGsB,YAAY,CAACrC,UAAU,CAACe,IAAI,CAACD,CAAC,CAAC,CAAC;IAC3CxC,CAAC,CAACwE,OAAO,CAAChC,CAAC,EAAE1D,CAAC,CAACmF,KAAK,CAAC,CAAC,CAAC,EACrBV,YAAY,EACZW,iBAAiB,CAACzB,IAAI,EAAEa,YAAY,CAAC,EACrCxE,CAAC,CAACqF,IAAI,CAAC1B,IAAI,EAAEmB,SAAS,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC;EAEF,OAAO5D,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,sBAAsBA,CAACT,CAAC,EAAE;EACjC,IAAI4C,KAAK,GAAG5C,CAAC,CAAC4C,KAAK,CAAC,CAAC;EACrBA,KAAK,CAACG,OAAO,IAAI,CAAC;EAClBjE,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGzC,CAAC,CAACyC,IAAI,CAACD,CAAC,CAAC;IACpBC,IAAI,CAACe,MAAM,IAAI,CAAC;IAChB,IAAIf,IAAI,CAACkB,QAAQ,CAACc,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;MACvC,IAAI7B,KAAK,CAACM,OAAO,KAAK,IAAI,IAAIN,KAAK,CAACM,OAAO,KAAK,IAAI,EAAE;QACpDT,IAAI,CAACJ,KAAK,IAAII,IAAI,CAACiB,WAAW;MAChC,CAAC,MAAM;QACLjB,IAAI,CAACH,MAAM,IAAIG,IAAI,CAACiB,WAAW;MACjC;IACF;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS7C,sBAAsBA,CAACb,CAAC,EAAE;EACjClB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGzC,CAAC,CAACyC,IAAI,CAACD,CAAC,CAAC;IACpB,IAAIC,IAAI,CAACJ,KAAK,IAAII,IAAI,CAACH,MAAM,EAAE;MAC7B,IAAIT,CAAC,GAAG7B,CAAC,CAAC+B,IAAI,CAACS,CAAC,CAACX,CAAC,CAAC;MACnB,IAAI6C,CAAC,GAAG1E,CAAC,CAAC+B,IAAI,CAACS,CAAC,CAACkC,CAAC,CAAC;MACnB,IAAIC,KAAK,GAAG;QAAEzF,IAAI,EAAE,CAACwF,CAAC,CAACxF,IAAI,GAAG2C,CAAC,CAAC3C,IAAI,IAAI,CAAC,GAAG2C,CAAC,CAAC3C,IAAI;QAAEsD,CAAC,EAAEA;MAAE,CAAC;MAC1D7C,IAAI,CAACiF,YAAY,CAAC5E,CAAC,EAAE,YAAY,EAAE2E,KAAK,EAAE,KAAK,CAAC;IAClD;EACF,CAAC,CAAC;AACJ;AAEA,SAAS5D,gBAAgBA,CAACf,CAAC,EAAE;EAC3B,IAAI6E,OAAO,GAAG,CAAC;EACf/F,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAAC4B,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIE,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC;IACpB,IAAIE,IAAI,CAAC+C,SAAS,EAAE;MAClB/C,IAAI,CAACgD,OAAO,GAAG/E,CAAC,CAAC+B,IAAI,CAACA,IAAI,CAAC+C,SAAS,CAAC,CAAC5F,IAAI;MAC1C6C,IAAI,CAAC8C,OAAO,GAAG7E,CAAC,CAAC+B,IAAI,CAACA,IAAI,CAACiD,YAAY,CAAC,CAAC9F,IAAI;MAC7C2F,OAAO,GAAG/F,CAAC,CAACmG,GAAG,CAACJ,OAAO,EAAE9C,IAAI,CAAC8C,OAAO,CAAC;IACxC;EACF,CAAC,CAAC;EACF7E,CAAC,CAAC4C,KAAK,CAAC,CAAC,CAACiC,OAAO,GAAGA,OAAO;AAC7B;AAEA,SAAS7D,sBAAsBA,CAAChB,CAAC,EAAE;EACjClB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAAC4B,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIE,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC;IACpB,IAAIE,IAAI,CAACmD,KAAK,KAAK,YAAY,EAAE;MAC/BlF,CAAC,CAACyC,IAAI,CAACV,IAAI,CAACS,CAAC,CAAC,CAAC2C,SAAS,GAAGpD,IAAI,CAAC7C,IAAI;MACpCc,CAAC,CAACoF,UAAU,CAACvD,CAAC,CAAC;IACjB;EACF,CAAC,CAAC;AACJ;AAEA,SAASN,cAAcA,CAACvB,CAAC,EAAE;EACzB,IAAIqF,IAAI,GAAGC,MAAM,CAACC,iBAAiB;EACnC,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAGH,MAAM,CAACC,iBAAiB;EACnC,IAAIG,IAAI,GAAG,CAAC;EACZ,IAAIC,UAAU,GAAG3F,CAAC,CAAC4C,KAAK,CAAC,CAAC;EAC1B,IAAIgD,OAAO,GAAGD,UAAU,CAACE,OAAO,IAAI,CAAC;EACrC,IAAIC,OAAO,GAAGH,UAAU,CAACI,OAAO,IAAI,CAAC;EAErC,SAASC,WAAWA,CAACC,KAAK,EAAE;IAC1B,IAAIhE,CAAC,GAAGgE,KAAK,CAAChE,CAAC;IACf,IAAIC,CAAC,GAAG+D,KAAK,CAAC/D,CAAC;IACf,IAAIwC,CAAC,GAAGuB,KAAK,CAAC5D,KAAK;IACnB,IAAI6D,CAAC,GAAGD,KAAK,CAAC3D,MAAM;IACpB+C,IAAI,GAAGc,IAAI,CAACC,GAAG,CAACf,IAAI,EAAEpD,CAAC,GAAGyC,CAAC,GAAG,CAAC,CAAC;IAChCc,IAAI,GAAGW,IAAI,CAAClB,GAAG,CAACO,IAAI,EAAEvD,CAAC,GAAGyC,CAAC,GAAG,CAAC,CAAC;IAChCe,IAAI,GAAGU,IAAI,CAACC,GAAG,CAACX,IAAI,EAAEvD,CAAC,GAAGgE,CAAC,GAAG,CAAC,CAAC;IAChCR,IAAI,GAAGS,IAAI,CAAClB,GAAG,CAACS,IAAI,EAAExD,CAAC,GAAGgE,CAAC,GAAG,CAAC,CAAC;EAClC;EAEApH,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAAC4B,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAAEmE,WAAW,CAAChG,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;EAC7D/C,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGzC,CAAC,CAACyC,IAAI,CAACD,CAAC,CAAC;IACpB,IAAI1D,CAAC,CAAC6D,GAAG,CAACF,IAAI,EAAE,GAAG,CAAC,EAAE;MACpBuD,WAAW,CAACvD,IAAI,CAAC;IACnB;EACF,CAAC,CAAC;EAEF4C,IAAI,IAAIO,OAAO;EACfH,IAAI,IAAIK,OAAO;EAEfhH,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAAC4B,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIE,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC;IACpBE,IAAI,CAACE,CAAC,IAAIoD,IAAI;IACdtD,IAAI,CAACG,CAAC,IAAIuD,IAAI;EAChB,CAAC,CAAC;EAEF3G,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGzC,CAAC,CAACyC,IAAI,CAACD,CAAC,CAAC;IACpB1D,CAAC,CAAC6C,OAAO,CAACc,IAAI,CAACC,MAAM,EAAE,UAAS2D,CAAC,EAAE;MACjCA,CAAC,CAACpE,CAAC,IAAIoD,IAAI;MACXgB,CAAC,CAACnE,CAAC,IAAIuD,IAAI;IACb,CAAC,CAAC;IACF,IAAI3G,CAAC,CAAC6D,GAAG,CAACF,IAAI,EAAE,GAAG,CAAC,EAAE;MAAEA,IAAI,CAACR,CAAC,IAAIoD,IAAI;IAAE;IACxC,IAAIvG,CAAC,CAAC6D,GAAG,CAACF,IAAI,EAAE,GAAG,CAAC,EAAE;MAAEA,IAAI,CAACP,CAAC,IAAIuD,IAAI;IAAE;EAC1C,CAAC,CAAC;EAEFE,UAAU,CAACtD,KAAK,GAAGmD,IAAI,GAAGH,IAAI,GAAGO,OAAO;EACxCD,UAAU,CAACrD,MAAM,GAAGoD,IAAI,GAAGD,IAAI,GAAGK,OAAO;AAC3C;AAEA,SAAStE,oBAAoBA,CAACxB,CAAC,EAAE;EAC/BlB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGzC,CAAC,CAACyC,IAAI,CAACD,CAAC,CAAC;IACpB,IAAI8D,KAAK,GAAGtG,CAAC,CAAC+B,IAAI,CAACS,CAAC,CAACX,CAAC,CAAC;IACvB,IAAI0E,KAAK,GAAGvG,CAAC,CAAC+B,IAAI,CAACS,CAAC,CAACkC,CAAC,CAAC;IACvB,IAAI8B,EAAE,EAAEC,EAAE;IACV,IAAI,CAAChE,IAAI,CAACC,MAAM,EAAE;MAChBD,IAAI,CAACC,MAAM,GAAG,EAAE;MAChB8D,EAAE,GAAGD,KAAK;MACVE,EAAE,GAAGH,KAAK;IACZ,CAAC,MAAM;MACLE,EAAE,GAAG/D,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;MACnB+D,EAAE,GAAGhE,IAAI,CAACC,MAAM,CAACD,IAAI,CAACC,MAAM,CAACN,MAAM,GAAG,CAAC,CAAC;IAC1C;IACAK,IAAI,CAACC,MAAM,CAACgE,OAAO,CAAC/G,IAAI,CAACgH,aAAa,CAACL,KAAK,EAAEE,EAAE,CAAC,CAAC;IAClD/D,IAAI,CAACC,MAAM,CAACkE,IAAI,CAACjH,IAAI,CAACgH,aAAa,CAACJ,KAAK,EAAEE,EAAE,CAAC,CAAC;EACjD,CAAC,CAAC;AACJ;AAEA,SAASnF,oBAAoBA,CAACtB,CAAC,EAAE;EAC/BlB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGzC,CAAC,CAACyC,IAAI,CAACD,CAAC,CAAC;IACpB,IAAI1D,CAAC,CAAC6D,GAAG,CAACF,IAAI,EAAE,GAAG,CAAC,EAAE;MACpB,IAAIA,IAAI,CAACkB,QAAQ,KAAK,GAAG,IAAIlB,IAAI,CAACkB,QAAQ,KAAK,GAAG,EAAE;QAClDlB,IAAI,CAACJ,KAAK,IAAII,IAAI,CAACiB,WAAW;MAChC;MACA,QAAQjB,IAAI,CAACkB,QAAQ;QACrB,KAAK,GAAG;UAAElB,IAAI,CAACR,CAAC,IAAIQ,IAAI,CAACJ,KAAK,GAAG,CAAC,GAAGI,IAAI,CAACiB,WAAW;UAAE;QACvD,KAAK,GAAG;UAAEjB,IAAI,CAACR,CAAC,IAAIQ,IAAI,CAACJ,KAAK,GAAG,CAAC,GAAGI,IAAI,CAACiB,WAAW;UAAE;MACvD;IACF;EACF,CAAC,CAAC;AACJ;AAEA,SAASjC,6BAA6BA,CAACzB,CAAC,EAAE;EACxClB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGzC,CAAC,CAACyC,IAAI,CAACD,CAAC,CAAC;IACpB,IAAIC,IAAI,CAACoE,QAAQ,EAAE;MACjBpE,IAAI,CAACC,MAAM,CAACoE,OAAO,CAAC,CAAC;IACvB;EACF,CAAC,CAAC;AACJ;AAEA,SAAS1F,iBAAiBA,CAACpB,CAAC,EAAE;EAC5BlB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAAC4B,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAI7B,CAAC,CAACmC,QAAQ,CAACN,CAAC,CAAC,CAACO,MAAM,EAAE;MACxB,IAAIL,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC;MACpB,IAAIkF,CAAC,GAAG/G,CAAC,CAAC+B,IAAI,CAACA,IAAI,CAAC+C,SAAS,CAAC;MAC9B,IAAIkC,CAAC,GAAGhH,CAAC,CAAC+B,IAAI,CAACA,IAAI,CAACiD,YAAY,CAAC;MACjC,IAAIiC,CAAC,GAAGjH,CAAC,CAAC+B,IAAI,CAACjD,CAAC,CAACoI,IAAI,CAACnF,IAAI,CAACoF,UAAU,CAAC,CAAC;MACvC,IAAIC,CAAC,GAAGpH,CAAC,CAAC+B,IAAI,CAACjD,CAAC,CAACoI,IAAI,CAACnF,IAAI,CAACsF,WAAW,CAAC,CAAC;MAExCtF,IAAI,CAACM,KAAK,GAAG8D,IAAI,CAACmB,GAAG,CAACF,CAAC,CAACnF,CAAC,GAAGgF,CAAC,CAAChF,CAAC,CAAC;MAChCF,IAAI,CAACO,MAAM,GAAG6D,IAAI,CAACmB,GAAG,CAACN,CAAC,CAAC9E,CAAC,GAAG6E,CAAC,CAAC7E,CAAC,CAAC;MACjCH,IAAI,CAACE,CAAC,GAAGgF,CAAC,CAAChF,CAAC,GAAGF,IAAI,CAACM,KAAK,GAAG,CAAC;MAC7BN,IAAI,CAACG,CAAC,GAAG6E,CAAC,CAAC7E,CAAC,GAAGH,IAAI,CAACO,MAAM,GAAG,CAAC;IAChC;EACF,CAAC,CAAC;EAEFxD,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAAC4B,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAI7B,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC,CAACqD,KAAK,KAAK,QAAQ,EAAE;MAChClF,CAAC,CAACoF,UAAU,CAACvD,CAAC,CAAC;IACjB;EACF,CAAC,CAAC;AACJ;AAEA,SAASnB,eAAeA,CAACV,CAAC,EAAE;EAC1BlB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAACuC,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIA,CAAC,CAACX,CAAC,KAAKW,CAAC,CAACkC,CAAC,EAAE;MACf,IAAI3C,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACS,CAAC,CAACX,CAAC,CAAC;MACtB,IAAI,CAACE,IAAI,CAACwF,SAAS,EAAE;QACnBxF,IAAI,CAACwF,SAAS,GAAG,EAAE;MACrB;MACAxF,IAAI,CAACwF,SAAS,CAACX,IAAI,CAAC;QAAEpE,CAAC,EAAEA,CAAC;QAAEmC,KAAK,EAAE3E,CAAC,CAACyC,IAAI,CAACD,CAAC;MAAE,CAAC,CAAC;MAC/CxC,CAAC,CAACwH,UAAU,CAAChF,CAAC,CAAC;IACjB;EACF,CAAC,CAAC;AACJ;AAEA,SAASvB,eAAeA,CAACjB,CAAC,EAAE;EAC1B,IAAIyH,MAAM,GAAG9H,IAAI,CAAC+H,gBAAgB,CAAC1H,CAAC,CAAC;EACrClB,CAAC,CAAC6C,OAAO,CAAC8F,MAAM,EAAE,UAASE,KAAK,EAAE;IAChC,IAAIC,UAAU,GAAG,CAAC;IAClB9I,CAAC,CAAC6C,OAAO,CAACgG,KAAK,EAAE,UAAS9F,CAAC,EAAEgG,CAAC,EAAE;MAC9B,IAAI9F,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC;MACpBE,IAAI,CAACtC,KAAK,GAAGoI,CAAC,GAAGD,UAAU;MAC3B9I,CAAC,CAAC6C,OAAO,CAACI,IAAI,CAACwF,SAAS,EAAE,UAASO,QAAQ,EAAE;QAC3CnI,IAAI,CAACiF,YAAY,CAAC5E,CAAC,EAAE,UAAU,EAAE;UAC/BqC,KAAK,EAAEyF,QAAQ,CAACnD,KAAK,CAACtC,KAAK;UAC3BC,MAAM,EAAEwF,QAAQ,CAACnD,KAAK,CAACrC,MAAM;UAC7BpD,IAAI,EAAE6C,IAAI,CAAC7C,IAAI;UACfO,KAAK,EAAEoI,CAAC,GAAI,EAAED,UAAW;UACzBpF,CAAC,EAAEsF,QAAQ,CAACtF,CAAC;UACbmC,KAAK,EAAEmD,QAAQ,CAACnD;QAClB,CAAC,EAAE,KAAK,CAAC;MACX,CAAC,CAAC;MACF,OAAO5C,IAAI,CAACwF,SAAS;IACvB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASpG,iBAAiBA,CAACnB,CAAC,EAAE;EAC5BlB,CAAC,CAAC6C,OAAO,CAAC3B,CAAC,CAAC4B,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIE,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACF,CAAC,CAAC;IACpB,IAAIE,IAAI,CAACmD,KAAK,KAAK,UAAU,EAAE;MAC7B,IAAI6C,QAAQ,GAAG/H,CAAC,CAAC+B,IAAI,CAACA,IAAI,CAACS,CAAC,CAACX,CAAC,CAAC;MAC/B,IAAII,CAAC,GAAG8F,QAAQ,CAAC9F,CAAC,GAAG8F,QAAQ,CAAC1F,KAAK,GAAG,CAAC;MACvC,IAAIH,CAAC,GAAG6F,QAAQ,CAAC7F,CAAC;MAClB,IAAI8F,EAAE,GAAGjG,IAAI,CAACE,CAAC,GAAGA,CAAC;MACnB,IAAIgG,EAAE,GAAGF,QAAQ,CAACzF,MAAM,GAAG,CAAC;MAC5BtC,CAAC,CAACwE,OAAO,CAACzC,IAAI,CAACS,CAAC,EAAET,IAAI,CAAC4C,KAAK,CAAC;MAC7B3E,CAAC,CAACoF,UAAU,CAACvD,CAAC,CAAC;MACfE,IAAI,CAAC4C,KAAK,CAACjC,MAAM,GAAG,CAClB;QAAET,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG+F,EAAE,GAAG,CAAC;QAAE9F,CAAC,EAAEA,CAAC,GAAG+F;MAAG,CAAC,EAChC;QAAEhG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG+F,EAAE,GAAG,CAAC;QAAE9F,CAAC,EAAEA,CAAC,GAAG+F;MAAG,CAAC,EAChC;QAAEhG,CAAC,EAAEA,CAAC,GAAO+F,EAAE;QAAM9F,CAAC,EAAEA;MAAE,CAAC,EAC3B;QAAED,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG+F,EAAE,GAAG,CAAC;QAAE9F,CAAC,EAAEA,CAAC,GAAG+F;MAAG,CAAC,EAChC;QAAEhG,CAAC,EAAEA,CAAC,GAAG,CAAC,GAAG+F,EAAE,GAAG,CAAC;QAAE9F,CAAC,EAAEA,CAAC,GAAG+F;MAAG,CAAC,CACjC;MACDlG,IAAI,CAAC4C,KAAK,CAAC1C,CAAC,GAAGF,IAAI,CAACE,CAAC;MACrBF,IAAI,CAAC4C,KAAK,CAACzC,CAAC,GAAGH,IAAI,CAACG,CAAC;IACvB;EACF,CAAC,CAAC;AACJ;AAEA,SAASgC,iBAAiBA,CAACgE,GAAG,EAAEjC,KAAK,EAAE;EACrC,OAAOnH,CAAC,CAACqJ,SAAS,CAACrJ,CAAC,CAACqF,IAAI,CAAC+D,GAAG,EAAEjC,KAAK,CAAC,EAAEX,MAAM,CAAC;AAChD;AAEA,SAASvB,YAAYA,CAACkC,KAAK,EAAE;EAC3B,IAAImC,QAAQ,GAAG,CAAC,CAAC;EACjBtJ,CAAC,CAAC6C,OAAO,CAACsE,KAAK,EAAE,UAASpE,CAAC,EAAEwG,CAAC,EAAE;IAC9BD,QAAQ,CAACC,CAAC,CAAC5D,WAAW,CAAC,CAAC,CAAC,GAAG5C,CAAC;EAC/B,CAAC,CAAC;EACF,OAAOuG,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}