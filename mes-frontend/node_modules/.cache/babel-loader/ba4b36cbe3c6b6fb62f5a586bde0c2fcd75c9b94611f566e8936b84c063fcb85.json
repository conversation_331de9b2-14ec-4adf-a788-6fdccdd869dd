{"ast": null, "code": "import { Typography, Input, Select, DatePicker } from 'antd';\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  TextArea\n} = Input;", "map": {"version": 3, "names": ["Typography", "Input", "Select", "DatePicker", "Title", "Option", "RangePicker", "TextArea"], "sources": ["/root/mes-system/mes-frontend/src/pages/Project/ProjectList.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Typography,\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  message,\n  Popconfirm,\n  Row,\n  Col,\n  Statistic,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ProjectOutlined,\n  CalendarOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport { projectService } from '../../services/business';\nimport { Project } from '../../types';\nimport dayjs from 'dayjs';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { TextArea } = Input;\n"], "mappings": "AAEA,SACEA,UAAU,EAQVC,KAAK,EACLC,MAAM,EACNC,UAAU,QAML,MAAM;AAcb,MAAM;EAAEC;AAAM,CAAC,GAAGJ,UAAU;AAC5B,MAAM;EAAEK;AAAO,CAAC,GAAGH,MAAM;AACzB,MAAM;EAAEI;AAAY,CAAC,GAAGH,UAAU;AAClC,MAAM;EAAEI;AAAS,CAAC,GAAGN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}