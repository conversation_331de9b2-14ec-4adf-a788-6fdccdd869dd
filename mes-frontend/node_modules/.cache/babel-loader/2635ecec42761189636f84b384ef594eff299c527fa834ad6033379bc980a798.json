{"ast": null, "code": "import { __extends, __rest } from \"tslib\";\nimport { CustomElement } from '../shapes';\nimport { createOffscreenGroup, deepAssign, visibility } from '../util';\nfunction applyVisibility() {\n  visibility(this, this.attributes.visibility !== 'hidden');\n}\nvar Component = /** @class */function (_super) {\n  __extends(Component, _super);\n  function Component(options, defaultStyleProps) {\n    if (defaultStyleProps === void 0) {\n      defaultStyleProps = {};\n    }\n    var _this = _super.call(this, deepAssign({}, {\n      style: defaultStyleProps\n    }, options)) || this;\n    _this.initialized = false;\n    _this._defaultOptions = defaultStyleProps;\n    return _this;\n  }\n  Object.defineProperty(Component.prototype, \"offscreenGroup\", {\n    get: function () {\n      if (!this._offscreen) this._offscreen = createOffscreenGroup(this);\n      return this._offscreen;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Component.prototype, \"defaultOptions\", {\n    get: function () {\n      return this._defaultOptions;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Component.prototype.connectedCallback = function () {\n    this.render(this.attributes, this);\n    this.bindEvents(this.attributes, this);\n    this.initialized = true;\n  };\n  Component.prototype.disconnectedCallback = function () {\n    var _a;\n    (_a = this._offscreen) === null || _a === void 0 ? void 0 : _a.destroy();\n  };\n  Component.prototype.attributeChangedCallback = function (name) {\n    if (name === 'visibility') {\n      applyVisibility.call(this);\n    }\n  };\n  Component.prototype.update = function (attr, animate) {\n    var _a;\n    this.attr(deepAssign({}, this.attributes, attr || {}));\n    return (_a = this.render) === null || _a === void 0 ? void 0 : _a.call(this, this.attributes, this, animate);\n  };\n  Component.prototype.clear = function () {\n    this.removeChildren();\n  };\n  Component.prototype.bindEvents = function (attributes, container) {};\n  Component.prototype.getSubShapeStyle = function (attributes) {\n    var x = attributes.x,\n      y = attributes.y,\n      transform = attributes.transform,\n      transformOrigin = attributes.transformOrigin,\n      _class = attributes.class,\n      className = attributes.className,\n      zIndex = attributes.zIndex,\n      style = __rest(attributes, [\"x\", \"y\", \"transform\", \"transformOrigin\", \"class\", \"className\", \"zIndex\"]);\n    return style;\n  };\n  return Component;\n}(CustomElement);\nexport { Component };", "map": {"version": 3, "names": ["CustomElement", "createOffscreenGroup", "deepAssign", "visibility", "applyVisibility", "attributes", "Component", "_super", "__extends", "options", "defaultStyleProps", "_this", "call", "style", "initialized", "_defaultOptions", "Object", "defineProperty", "prototype", "get", "_offscreen", "connectedCallback", "render", "bindEvents", "disconnectedCallback", "_a", "destroy", "attributeChangedCallback", "name", "update", "attr", "animate", "clear", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container", "getSubShapeStyle", "x", "y", "transform", "transform<PERSON><PERSON>in", "_class", "class", "className", "zIndex", "__rest"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/core/component.ts"], "sourcesContent": ["import { CustomElement, Group } from '../shapes';\nimport type { GenericAnimation, AnimationResult } from '../animation';\nimport { createOffscreenGroup, deepAssign, visibility } from '../util';\nimport type { ComponentOptions } from './types';\n\nfunction applyVisibility() {\n  visibility(this, this.attributes.visibility !== 'hidden');\n}\n\nexport abstract class Component<T extends Record<string, any>> extends CustomElement<Required<T>> {\n  protected _defaultOptions: Partial<T>;\n\n  private _offscreen!: Group;\n\n  protected get offscreenGroup() {\n    if (!this._offscreen) this._offscreen = createOffscreenGroup(this);\n    return this._offscreen;\n  }\n\n  public initialized = false;\n\n  public get defaultOptions() {\n    return this._defaultOptions;\n  }\n\n  constructor(options: ComponentOptions<Partial<T>>, defaultStyleProps: Partial<T> = {}) {\n    super(deepAssign({}, { style: defaultStyleProps }, options));\n    this._defaultOptions = defaultStyleProps;\n  }\n\n  connectedCallback() {\n    this.render(this.attributes as Required<T>, this);\n    this.bindEvents(this.attributes, this);\n    this.initialized = true;\n  }\n\n  disconnectedCallback(): void {\n    this._offscreen?.destroy();\n  }\n\n  attributeChangedCallback<Key extends keyof T>(name: Key): void {\n    if (name === 'visibility') {\n      applyVisibility.call(this);\n    }\n  }\n\n  public update(attr?: Partial<T>, animate?: GenericAnimation) {\n    this.attr(deepAssign({}, this.attributes, attr || {}));\n    return this.render?.(this.attributes as Required<T>, this, animate);\n  }\n\n  public clear() {\n    this.removeChildren();\n  }\n\n  public abstract render(\n    attributes: Required<T>,\n    container: Group,\n    animate?: GenericAnimation\n  ): void | AnimationResult[];\n\n  public bindEvents(attributes: T, container: Group): void {}\n\n  protected getSubShapeStyle(attributes: T): T {\n    const { x, y, transform, transformOrigin, class: _class, className, zIndex, ...style } = attributes;\n    return style as T;\n  }\n}\n"], "mappings": ";AAAA,SAASA,aAAa,QAAe,WAAW;AAEhD,SAASC,oBAAoB,EAAEC,UAAU,EAAEC,UAAU,QAAQ,SAAS;AAGtE,SAASC,eAAeA,CAAA;EACtBD,UAAU,CAAC,IAAI,EAAE,IAAI,CAACE,UAAU,CAACF,UAAU,KAAK,QAAQ,CAAC;AAC3D;AAEA,IAAAG,SAAA,0BAAAC,MAAA;EAAuEC,SAAA,CAAAF,SAAA,EAAAC,MAAA;EAgBrE,SAAAD,UAAYG,OAAqC,EAAEC,iBAAkC;IAAlC,IAAAA,iBAAA;MAAAA,iBAAA,KAAkC;IAAA;IACnF,IAAAC,KAAA,GAAAJ,MAAK,CAAAK,IAAA,OAACV,UAAU,CAAC,EAAE,EAAE;MAAEW,KAAK,EAAEH;IAAiB,CAAE,EAAED,OAAO,CAAC,CAAC;IAPvDE,KAAA,CAAAG,WAAW,GAAG,KAAK;IAQxBH,KAAI,CAACI,eAAe,GAAGL,iBAAiB;;EAC1C;EAdAM,MAAA,CAAAC,cAAA,CAAcX,SAAA,CAAAY,SAAA,kBAAc;SAA5B,SAAAC,CAAA;MACE,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACA,UAAU,GAAGnB,oBAAoB,CAAC,IAAI,CAAC;MAClE,OAAO,IAAI,CAACmB,UAAU;IACxB,CAAC;;;;EAIDJ,MAAA,CAAAC,cAAA,CAAWX,SAAA,CAAAY,SAAA,kBAAc;SAAzB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACJ,eAAe;IAC7B,CAAC;;;;EAODT,SAAA,CAAAY,SAAA,CAAAG,iBAAiB,GAAjB;IACE,IAAI,CAACC,MAAM,CAAC,IAAI,CAACjB,UAAyB,EAAE,IAAI,CAAC;IACjD,IAAI,CAACkB,UAAU,CAAC,IAAI,CAAClB,UAAU,EAAE,IAAI,CAAC;IACtC,IAAI,CAACS,WAAW,GAAG,IAAI;EACzB,CAAC;EAEDR,SAAA,CAAAY,SAAA,CAAAM,oBAAoB,GAApB;;IACE,CAAAC,EAAA,OAAI,CAACL,UAAU,cAAAK,EAAA,uBAAAA,EAAA,CAAEC,OAAO,EAAE;EAC5B,CAAC;EAEDpB,SAAA,CAAAY,SAAA,CAAAS,wBAAwB,GAAxB,UAA8CC,IAAS;IACrD,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzBxB,eAAe,CAACQ,IAAI,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;EAEMN,SAAA,CAAAY,SAAA,CAAAW,MAAM,GAAb,UAAcC,IAAiB,EAAEC,OAA0B;;IACzD,IAAI,CAACD,IAAI,CAAC5B,UAAU,CAAC,EAAE,EAAE,IAAI,CAACG,UAAU,EAAEyB,IAAI,IAAI,EAAE,CAAC,CAAC;IACtD,OAAO,CAAAL,EAAA,OAAI,CAACH,MAAM,cAAAG,EAAA,uBAAAA,EAAA,CAAAb,IAAA,OAAG,IAAI,CAACP,UAAyB,EAAE,IAAI,EAAE0B,OAAO,CAAC;EACrE,CAAC;EAEMzB,SAAA,CAAAY,SAAA,CAAAc,KAAK,GAAZ;IACE,IAAI,CAACC,cAAc,EAAE;EACvB,CAAC;EAQM3B,SAAA,CAAAY,SAAA,CAAAK,UAAU,GAAjB,UAAkBlB,UAAa,EAAE6B,SAAgB,GAAS,CAAC;EAEjD5B,SAAA,CAAAY,SAAA,CAAAiB,gBAAgB,GAA1B,UAA2B9B,UAAa;IAC9B,IAAA+B,CAAC,GAAgF/B,UAAU,CAAA+B,CAA1F;MAAEC,CAAC,GAA6EhC,UAAU,CAAAgC,CAAvF;MAAEC,SAAS,GAAkEjC,UAAU,CAAAiC,SAA5E;MAAEC,eAAe,GAAiDlC,UAAU,CAAAkC,eAA3D;MAASC,MAAM,GAAkCnC,UAAU,CAAAoC,KAA5C;MAAEC,SAAS,GAAuBrC,UAAU,CAAAqC,SAAjC;MAAEC,MAAM,GAAetC,UAAU,CAAAsC,MAAzB;MAAK9B,KAAK,GAAA+B,MAAA,CAAKvC,UAAU,EAA7F,0EAAgF,CAAF;IACpF,OAAOQ,KAAU;EACnB,CAAC;EACH,OAAAP,SAAC;AAAD,CAAC,CA1DsEN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}