{"ast": null, "code": "/**\n * Checks if the character is a space.\n */\nexport function isSpace(ch) {\n  var specialSpaces = [0x1680, 0x180e, 0x2000, 0x2001, 0x2002, 0x2003, 0x2004, 0x2005, 0x2006, 0x2007, 0x2008, 0x2009, 0x200a, 0x202f, 0x205f, 0x3000, 0xfeff];\n  /* istanbul ignore next */\n  return ch === 0x0a || ch === 0x0d || ch === 0x2028 || ch === 0x2029 ||\n  // Line terminators\n  // White spaces\n  ch === 0x20 || ch === 0x09 || ch === 0x0b || ch === 0x0c || ch === 0xa0 || ch >= 0x1680 && specialSpaces.includes(ch);\n}", "map": {"version": 3, "names": ["isSpace", "ch", "specialSpaces", "includes"], "sources": ["path/parser/is-space.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAM,SAAUA,OAAOA,CAACC,EAAU;EAChC,IAAMC,aAAa,GAAG,CACpB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAC9G,MAAM,EAAE,MAAM,EAAE,MAAM,CACvB;EACD;EACA,OACED,EAAE,KAAK,IAAI,IACXA,EAAE,KAAK,IAAI,IACXA,EAAE,KAAK,MAAM,IACbA,EAAE,KAAK,MAAM;EAAI;EACjB;EACAA,EAAE,KAAK,IAAI,IACXA,EAAE,KAAK,IAAI,IACXA,EAAE,KAAK,IAAI,IACXA,EAAE,KAAK,IAAI,IACXA,EAAE,KAAK,IAAI,IACVA,EAAE,IAAI,MAAM,IAAIC,aAAa,CAACC,QAAQ,CAACF,EAAE,CAAE;AAEhD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}