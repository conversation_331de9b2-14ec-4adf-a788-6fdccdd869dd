{"ast": null, "code": "var _ = require(\"../lodash\");\nvar barycenter = require(\"./barycenter\");\nvar resolveConflicts = require(\"./resolve-conflicts\");\nvar sort = require(\"./sort\");\nmodule.exports = sortSubgraph;\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight : undefined;\n  var subgraphs = {};\n  if (bl) {\n    movable = _.filter(movable, function (w) {\n      return w !== bl && w !== br;\n    });\n  }\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function (entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (_.has(subgraphResult, \"barycenter\")) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n  var result = sort(entries, biasRight);\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br], true);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!_.has(result, \"barycenter\")) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter = (result.barycenter * result.weight + blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n  return result;\n}\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function (entry) {\n    entry.vs = _.flatten(entry.vs.map(function (v) {\n      if (subgraphs[v]) {\n        return subgraphs[v].vs;\n      }\n      return v;\n    }), true);\n  });\n}\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter = (target.barycenter * target.weight + other.barycenter * other.weight) / (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}", "map": {"version": 3, "names": ["_", "require", "barycenter", "resolveConflicts", "sort", "module", "exports", "sortSubgraph", "g", "v", "cg", "biasRight", "movable", "children", "node", "bl", "borderLeft", "undefined", "br", "borderRight", "subgraphs", "filter", "w", "barycenters", "for<PERSON>ach", "entry", "length", "subgraphResult", "has", "mergeBarycenters", "entries", "expandSubgraphs", "result", "vs", "flatten", "predecessors", "blPred", "br<PERSON><PERSON>", "weight", "order", "map", "target", "other", "isUndefined"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/order/sort-subgraph.js"], "sourcesContent": ["var _ = require(\"../lodash\");\nvar barycenter = require(\"./barycenter\");\nvar resolveConflicts = require(\"./resolve-conflicts\");\nvar sort = require(\"./sort\");\n\nmodule.exports = sortSubgraph;\n\nfunction sortSubgraph(g, v, cg, biasRight) {\n  var movable = g.children(v);\n  var node = g.node(v);\n  var bl = node ? node.borderLeft : undefined;\n  var br = node ? node.borderRight: undefined;\n  var subgraphs = {};\n\n  if (bl) {\n    movable = _.filter(movable, function(w) {\n      return w !== bl && w !== br;\n    });\n  }\n\n  var barycenters = barycenter(g, movable);\n  _.forEach(barycenters, function(entry) {\n    if (g.children(entry.v).length) {\n      var subgraphResult = sortSubgraph(g, entry.v, cg, biasRight);\n      subgraphs[entry.v] = subgraphResult;\n      if (_.has(subgraphResult, \"barycenter\")) {\n        mergeBarycenters(entry, subgraphResult);\n      }\n    }\n  });\n\n  var entries = resolveConflicts(barycenters, cg);\n  expandSubgraphs(entries, subgraphs);\n\n  var result = sort(entries, biasRight);\n\n  if (bl) {\n    result.vs = _.flatten([bl, result.vs, br], true);\n    if (g.predecessors(bl).length) {\n      var blPred = g.node(g.predecessors(bl)[0]),\n        brPred = g.node(g.predecessors(br)[0]);\n      if (!_.has(result, \"barycenter\")) {\n        result.barycenter = 0;\n        result.weight = 0;\n      }\n      result.barycenter = (result.barycenter * result.weight +\n                           blPred.order + brPred.order) / (result.weight + 2);\n      result.weight += 2;\n    }\n  }\n\n  return result;\n}\n\nfunction expandSubgraphs(entries, subgraphs) {\n  _.forEach(entries, function(entry) {\n    entry.vs = _.flatten(entry.vs.map(function(v) {\n      if (subgraphs[v]) {\n        return subgraphs[v].vs;\n      }\n      return v;\n    }), true);\n  });\n}\n\nfunction mergeBarycenters(target, other) {\n  if (!_.isUndefined(target.barycenter)) {\n    target.barycenter = (target.barycenter * target.weight +\n                         other.barycenter * other.weight) /\n                        (target.weight + other.weight);\n    target.weight += other.weight;\n  } else {\n    target.barycenter = other.barycenter;\n    target.weight = other.weight;\n  }\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC5B,IAAIC,UAAU,GAAGD,OAAO,CAAC,cAAc,CAAC;AACxC,IAAIE,gBAAgB,GAAGF,OAAO,CAAC,qBAAqB,CAAC;AACrD,IAAIG,IAAI,GAAGH,OAAO,CAAC,QAAQ,CAAC;AAE5BI,MAAM,CAACC,OAAO,GAAGC,YAAY;AAE7B,SAASA,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAEC,EAAE,EAAEC,SAAS,EAAE;EACzC,IAAIC,OAAO,GAAGJ,CAAC,CAACK,QAAQ,CAACJ,CAAC,CAAC;EAC3B,IAAIK,IAAI,GAAGN,CAAC,CAACM,IAAI,CAACL,CAAC,CAAC;EACpB,IAAIM,EAAE,GAAGD,IAAI,GAAGA,IAAI,CAACE,UAAU,GAAGC,SAAS;EAC3C,IAAIC,EAAE,GAAGJ,IAAI,GAAGA,IAAI,CAACK,WAAW,GAAEF,SAAS;EAC3C,IAAIG,SAAS,GAAG,CAAC,CAAC;EAElB,IAAIL,EAAE,EAAE;IACNH,OAAO,GAAGZ,CAAC,CAACqB,MAAM,CAACT,OAAO,EAAE,UAASU,CAAC,EAAE;MACtC,OAAOA,CAAC,KAAKP,EAAE,IAAIO,CAAC,KAAKJ,EAAE;IAC7B,CAAC,CAAC;EACJ;EAEA,IAAIK,WAAW,GAAGrB,UAAU,CAACM,CAAC,EAAEI,OAAO,CAAC;EACxCZ,CAAC,CAACwB,OAAO,CAACD,WAAW,EAAE,UAASE,KAAK,EAAE;IACrC,IAAIjB,CAAC,CAACK,QAAQ,CAACY,KAAK,CAAChB,CAAC,CAAC,CAACiB,MAAM,EAAE;MAC9B,IAAIC,cAAc,GAAGpB,YAAY,CAACC,CAAC,EAAEiB,KAAK,CAAChB,CAAC,EAAEC,EAAE,EAAEC,SAAS,CAAC;MAC5DS,SAAS,CAACK,KAAK,CAAChB,CAAC,CAAC,GAAGkB,cAAc;MACnC,IAAI3B,CAAC,CAAC4B,GAAG,CAACD,cAAc,EAAE,YAAY,CAAC,EAAE;QACvCE,gBAAgB,CAACJ,KAAK,EAAEE,cAAc,CAAC;MACzC;IACF;EACF,CAAC,CAAC;EAEF,IAAIG,OAAO,GAAG3B,gBAAgB,CAACoB,WAAW,EAAEb,EAAE,CAAC;EAC/CqB,eAAe,CAACD,OAAO,EAAEV,SAAS,CAAC;EAEnC,IAAIY,MAAM,GAAG5B,IAAI,CAAC0B,OAAO,EAAEnB,SAAS,CAAC;EAErC,IAAII,EAAE,EAAE;IACNiB,MAAM,CAACC,EAAE,GAAGjC,CAAC,CAACkC,OAAO,CAAC,CAACnB,EAAE,EAAEiB,MAAM,CAACC,EAAE,EAAEf,EAAE,CAAC,EAAE,IAAI,CAAC;IAChD,IAAIV,CAAC,CAAC2B,YAAY,CAACpB,EAAE,CAAC,CAACW,MAAM,EAAE;MAC7B,IAAIU,MAAM,GAAG5B,CAAC,CAACM,IAAI,CAACN,CAAC,CAAC2B,YAAY,CAACpB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACxCsB,MAAM,GAAG7B,CAAC,CAACM,IAAI,CAACN,CAAC,CAAC2B,YAAY,CAACjB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,IAAI,CAAClB,CAAC,CAAC4B,GAAG,CAACI,MAAM,EAAE,YAAY,CAAC,EAAE;QAChCA,MAAM,CAAC9B,UAAU,GAAG,CAAC;QACrB8B,MAAM,CAACM,MAAM,GAAG,CAAC;MACnB;MACAN,MAAM,CAAC9B,UAAU,GAAG,CAAC8B,MAAM,CAAC9B,UAAU,GAAG8B,MAAM,CAACM,MAAM,GACjCF,MAAM,CAACG,KAAK,GAAGF,MAAM,CAACE,KAAK,KAAKP,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;MACvEN,MAAM,CAACM,MAAM,IAAI,CAAC;IACpB;EACF;EAEA,OAAON,MAAM;AACf;AAEA,SAASD,eAAeA,CAACD,OAAO,EAAEV,SAAS,EAAE;EAC3CpB,CAAC,CAACwB,OAAO,CAACM,OAAO,EAAE,UAASL,KAAK,EAAE;IACjCA,KAAK,CAACQ,EAAE,GAAGjC,CAAC,CAACkC,OAAO,CAACT,KAAK,CAACQ,EAAE,CAACO,GAAG,CAAC,UAAS/B,CAAC,EAAE;MAC5C,IAAIW,SAAS,CAACX,CAAC,CAAC,EAAE;QAChB,OAAOW,SAAS,CAACX,CAAC,CAAC,CAACwB,EAAE;MACxB;MACA,OAAOxB,CAAC;IACV,CAAC,CAAC,EAAE,IAAI,CAAC;EACX,CAAC,CAAC;AACJ;AAEA,SAASoB,gBAAgBA,CAACY,MAAM,EAAEC,KAAK,EAAE;EACvC,IAAI,CAAC1C,CAAC,CAAC2C,WAAW,CAACF,MAAM,CAACvC,UAAU,CAAC,EAAE;IACrCuC,MAAM,CAACvC,UAAU,GAAG,CAACuC,MAAM,CAACvC,UAAU,GAAGuC,MAAM,CAACH,MAAM,GACjCI,KAAK,CAACxC,UAAU,GAAGwC,KAAK,CAACJ,MAAM,KAC/BG,MAAM,CAACH,MAAM,GAAGI,KAAK,CAACJ,MAAM,CAAC;IAClDG,MAAM,CAACH,MAAM,IAAII,KAAK,CAACJ,MAAM;EAC/B,CAAC,MAAM;IACLG,MAAM,CAACvC,UAAU,GAAGwC,KAAK,CAACxC,UAAU;IACpCuC,MAAM,CAACH,MAAM,GAAGI,KAAK,CAACJ,MAAM;EAC9B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}