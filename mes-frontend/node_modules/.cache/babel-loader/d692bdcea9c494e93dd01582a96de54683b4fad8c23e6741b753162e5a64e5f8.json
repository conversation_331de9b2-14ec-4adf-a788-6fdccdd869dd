{"ast": null, "code": "import noop from \"../noop.js\";\nvar x0 = Infinity,\n  y0 = x0,\n  x1 = -x0,\n  y1 = x1;\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop,\n  result: function () {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\nexport default boundsStream;", "map": {"version": 3, "names": ["noop", "x0", "Infinity", "y0", "x1", "y1", "boundsStream", "point", "boundsPoint", "lineStart", "lineEnd", "polygonStart", "polygonEnd", "result", "bounds", "x", "y"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-geo/src/path/bounds.js"], "sourcesContent": ["import noop from \"../noop.js\";\n\nvar x0 = Infinity,\n    y0 = x0,\n    x1 = -x0,\n    y1 = x1;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop,\n  result: function() {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\n\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\n\nexport default boundsStream;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;AAE7B,IAAIC,EAAE,GAAGC,QAAQ;EACbC,EAAE,GAAGF,EAAE;EACPG,EAAE,GAAG,CAACH,EAAE;EACRI,EAAE,GAAGD,EAAE;AAEX,IAAIE,YAAY,GAAG;EACjBC,KAAK,EAAEC,WAAW;EAClBC,SAAS,EAAET,IAAI;EACfU,OAAO,EAAEV,IAAI;EACbW,YAAY,EAAEX,IAAI;EAClBY,UAAU,EAAEZ,IAAI;EAChBa,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,IAAIC,MAAM,GAAG,CAAC,CAACb,EAAE,EAAEE,EAAE,CAAC,EAAE,CAACC,EAAE,EAAEC,EAAE,CAAC,CAAC;IACjCD,EAAE,GAAGC,EAAE,GAAG,EAAEF,EAAE,GAAGF,EAAE,GAAGC,QAAQ,CAAC;IAC/B,OAAOY,MAAM;EACf;AACF,CAAC;AAED,SAASN,WAAWA,CAACO,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAID,CAAC,GAAGd,EAAE,EAAEA,EAAE,GAAGc,CAAC;EAClB,IAAIA,CAAC,GAAGX,EAAE,EAAEA,EAAE,GAAGW,CAAC;EAClB,IAAIC,CAAC,GAAGb,EAAE,EAAEA,EAAE,GAAGa,CAAC;EAClB,IAAIA,CAAC,GAAGX,EAAE,EAAEA,EAAE,GAAGW,CAAC;AACpB;AAEA,eAAeV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}