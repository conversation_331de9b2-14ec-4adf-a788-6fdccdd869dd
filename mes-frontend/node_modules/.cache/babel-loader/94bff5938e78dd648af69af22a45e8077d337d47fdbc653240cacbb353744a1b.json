{"ast": null, "code": "import constant from \"./constant.js\";\nexport default function (radius, x, y) {\n  var nodes,\n    strength = constant(0.1),\n    strengths,\n    radiuses;\n  if (typeof radius !== \"function\") radius = constant(+radius);\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n  function force(alpha) {\n    for (var i = 0, n = nodes.length; i < n; ++i) {\n      var node = nodes[i],\n        dx = node.x - x || 1e-6,\n        dy = node.y - y || 1e-6,\n        r = Math.sqrt(dx * dx + dy * dy),\n        k = (radiuses[i] - r) * strengths[i] * alpha / r;\n      node.vx += dx * k;\n      node.vy += dy * k;\n    }\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length;\n    strengths = new Array(n);\n    radiuses = new Array(n);\n    for (i = 0; i < n; ++i) {\n      radiuses[i] = +radius(nodes[i], i, nodes);\n      strengths[i] = isNaN(radiuses[i]) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n  force.initialize = function (_) {\n    nodes = _, initialize();\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n  force.radius = function (_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n  force.x = function (_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n  force.y = function (_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["constant", "radius", "x", "y", "nodes", "strength", "strengths", "radiuses", "force", "alpha", "i", "n", "length", "node", "dx", "dy", "r", "Math", "sqrt", "k", "vx", "vy", "initialize", "Array", "isNaN", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force/src/radial.js"], "sourcesContent": ["import constant from \"./constant.js\";\n\nexport default function(radius, x, y) {\n  var nodes,\n      strength = constant(0.1),\n      strengths,\n      radiuses;\n\n  if (typeof radius !== \"function\") radius = constant(+radius);\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length; i < n; ++i) {\n      var node = nodes[i],\n          dx = node.x - x || 1e-6,\n          dy = node.y - y || 1e-6,\n          r = Math.sqrt(dx * dx + dy * dy),\n          k = (radiuses[i] - r) * strengths[i] * alpha / r;\n      node.vx += dx * k;\n      node.vy += dy * k;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    radiuses = new Array(n);\n    for (i = 0; i < n; ++i) {\n      radiuses[i] = +radius(nodes[i], i, nodes);\n      strengths[i] = isNaN(radiuses[i]) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _, initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,UAASC,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACpC,IAAIC,KAAK;IACLC,QAAQ,GAAGL,QAAQ,CAAC,GAAG,CAAC;IACxBM,SAAS;IACTC,QAAQ;EAEZ,IAAI,OAAON,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGD,QAAQ,CAAC,CAACC,MAAM,CAAC;EAC5D,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EACpB,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EAEpB,SAASK,KAAKA,CAACC,KAAK,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGP,KAAK,CAACQ,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC5C,IAAIG,IAAI,GAAGT,KAAK,CAACM,CAAC,CAAC;QACfI,EAAE,GAAGD,IAAI,CAACX,CAAC,GAAGA,CAAC,IAAI,IAAI;QACvBa,EAAE,GAAGF,IAAI,CAACV,CAAC,GAAGA,CAAC,IAAI,IAAI;QACvBa,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACJ,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;QAChCI,CAAC,GAAG,CAACZ,QAAQ,CAACG,CAAC,CAAC,GAAGM,CAAC,IAAIV,SAAS,CAACI,CAAC,CAAC,GAAGD,KAAK,GAAGO,CAAC;MACpDH,IAAI,CAACO,EAAE,IAAIN,EAAE,GAAGK,CAAC;MACjBN,IAAI,CAACQ,EAAE,IAAIN,EAAE,GAAGI,CAAC;IACnB;EACF;EAEA,SAASG,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAClB,KAAK,EAAE;IACZ,IAAIM,CAAC;MAAEC,CAAC,GAAGP,KAAK,CAACQ,MAAM;IACvBN,SAAS,GAAG,IAAIiB,KAAK,CAACZ,CAAC,CAAC;IACxBJ,QAAQ,GAAG,IAAIgB,KAAK,CAACZ,CAAC,CAAC;IACvB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBH,QAAQ,CAACG,CAAC,CAAC,GAAG,CAACT,MAAM,CAACG,KAAK,CAACM,CAAC,CAAC,EAAEA,CAAC,EAAEN,KAAK,CAAC;MACzCE,SAAS,CAACI,CAAC,CAAC,GAAGc,KAAK,CAACjB,QAAQ,CAACG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAACL,QAAQ,CAACD,KAAK,CAACM,CAAC,CAAC,EAAEA,CAAC,EAAEN,KAAK,CAAC;IACvE;EACF;EAEAI,KAAK,CAACc,UAAU,GAAG,UAASG,CAAC,EAAE;IAC7BrB,KAAK,GAAGqB,CAAC,EAAEH,UAAU,CAAC,CAAC;EACzB,CAAC;EAEDd,KAAK,CAACH,QAAQ,GAAG,UAASoB,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACd,MAAM,IAAIP,QAAQ,GAAG,OAAOoB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGzB,QAAQ,CAAC,CAACyB,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC,EAAEd,KAAK,IAAIH,QAAQ;EACnH,CAAC;EAEDG,KAAK,CAACP,MAAM,GAAG,UAASwB,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACd,MAAM,IAAIX,MAAM,GAAG,OAAOwB,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGzB,QAAQ,CAAC,CAACyB,CAAC,CAAC,EAAEH,UAAU,CAAC,CAAC,EAAEd,KAAK,IAAIP,MAAM;EAC/G,CAAC;EAEDO,KAAK,CAACN,CAAC,GAAG,UAASuB,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIV,CAAC,GAAG,CAACuB,CAAC,EAAEjB,KAAK,IAAIN,CAAC;EAC/C,CAAC;EAEDM,KAAK,CAACL,CAAC,GAAG,UAASsB,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACd,MAAM,IAAIT,CAAC,GAAG,CAACsB,CAAC,EAAEjB,KAAK,IAAIL,CAAC;EAC/C,CAAC;EAED,OAAOK,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}