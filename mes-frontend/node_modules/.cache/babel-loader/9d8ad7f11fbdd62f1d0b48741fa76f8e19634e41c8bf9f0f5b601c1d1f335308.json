{"ast": null, "code": "import { Graph } from '@antv/graphlib';\nimport { clone } from '@antv/util';\nimport { buildLayerMatrix, maxRank } from '../util';\nimport { addSubgraphConstraints } from './add-subgraph-constraints';\nimport { buildLayerGraph } from './build-layer-graph';\nimport { crossCount } from './cross-count';\nimport { initOrder } from './init-order';\nimport { sortSubgraph } from './sort-subgraph';\n/*\n * Applies heuristics to minimize edge crossings in the graph and sets the best\n * order solution as an order attribute on each node.\n *\n * Pre-conditions:\n *\n *    1. Graph must be DAG\n *    2. Graph nodes must be objects with a \"rank\" attribute\n *    3. Graph edges must have the \"weight\" attribute\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have an \"order\" attribute based on the results of the\n *       algorithm.\n */\nexport const order = (g, keepNodeOrder) => {\n  const mxRank = maxRank(g);\n  const range1 = [];\n  const range2 = [];\n  for (let i = 1; i < mxRank + 1; i++) range1.push(i);\n  for (let i = mxRank - 1; i > -1; i--) range2.push(i);\n  const downLayerGraphs = buildLayerGraphs(g, range1, 'in');\n  const upLayerGraphs = buildLayerGraphs(g, range2, 'out');\n  let layering = initOrder(g);\n  assignOrder(g, layering);\n  let bestCC = Number.POSITIVE_INFINITY;\n  let best;\n  for (let i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2, false, keepNodeOrder);\n    layering = buildLayerMatrix(g);\n    const cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = clone(layering);\n      bestCC = cc;\n    }\n  }\n  // consider use previous result, maybe somewhat reduendant\n  layering = initOrder(g);\n  assignOrder(g, layering);\n  for (let i = 0, lastBest = 0; lastBest < 4; ++i, ++lastBest) {\n    sweepLayerGraphs(i % 2 ? downLayerGraphs : upLayerGraphs, i % 4 >= 2, true, keepNodeOrder);\n    layering = buildLayerMatrix(g);\n    const cc = crossCount(g, layering);\n    if (cc < bestCC) {\n      lastBest = 0;\n      best = clone(layering);\n      bestCC = cc;\n    }\n  }\n  assignOrder(g, best);\n};\nconst buildLayerGraphs = (g, ranks, direction) => {\n  return ranks.map(rank => {\n    return buildLayerGraph(g, rank, direction);\n  });\n};\nconst sweepLayerGraphs = (layerGraphs, biasRight, usePrev, keepNodeOrder) => {\n  const cg = new Graph();\n  layerGraphs === null || layerGraphs === void 0 ? void 0 : layerGraphs.forEach(lg => {\n    var _a;\n    // const root = lg.graph().root as string;\n    const root = lg.getRoots()[0].id;\n    const sorted = sortSubgraph(lg, root, cg, biasRight, usePrev, keepNodeOrder);\n    for (let i = 0; i < ((_a = sorted.vs) === null || _a === void 0 ? void 0 : _a.length) || 0; i++) {\n      const lnode = lg.getNode(sorted.vs[i]);\n      if (lnode) {\n        lnode.data.order = i;\n      }\n    }\n    addSubgraphConstraints(lg, cg, sorted.vs);\n  });\n};\nconst assignOrder = (g, layering) => {\n  layering === null || layering === void 0 ? void 0 : layering.forEach(layer => {\n    layer === null || layer === void 0 ? void 0 : layer.forEach((v, i) => {\n      g.getNode(v).data.order = i;\n    });\n  });\n};", "map": {"version": 3, "names": ["Graph", "clone", "buildLayerMatrix", "maxRank", "addSubgraphConstraints", "buildLayerGraph", "crossCount", "initOrder", "sortSubgraph", "order", "g", "keepNodeOrder", "mxRank", "range1", "range2", "i", "push", "downLayerGraphs", "buildLayerGraphs", "upLayerGraphs", "layering", "assignOrder", "bestCC", "Number", "POSITIVE_INFINITY", "best", "lastBest", "sweepLayerGraphs", "cc", "ranks", "direction", "map", "rank", "layerGraphs", "biasRight", "usePrev", "cg", "for<PERSON>ach", "lg", "root", "getRoots", "id", "sorted", "_a", "vs", "length", "lnode", "getNode", "data", "layer", "v"], "sources": ["../../../src/antv-dagre/order/index.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,KAAK,QAAY,gBAAgB;AAC1C,SAASC,KAAK,QAAQ,YAAY;AAElC,SAASC,gBAAgB,EAAEC,OAAO,QAAQ,SAAS;AACnD,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,UAAU,QAAQ,eAAe;AAC1C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;;;;;;;;;;;;;;;AAeA,OAAO,MAAMC,KAAK,GAAGA,CAACC,CAAS,EAAEC,aAAuB,KAAI;EAC1D,MAAMC,MAAM,GAAGT,OAAO,CAACO,CAAC,CAAC;EACzB,MAAMG,MAAM,GAAG,EAAE;EACjB,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,GAAG,CAAC,EAAEG,CAAC,EAAE,EAAEF,MAAM,CAACG,IAAI,CAACD,CAAC,CAAC;EACnD,KAAK,IAAIA,CAAC,GAAGH,MAAM,GAAG,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAED,MAAM,CAACE,IAAI,CAACD,CAAC,CAAC;EACpD,MAAME,eAAe,GAAGC,gBAAgB,CAACR,CAAC,EAAEG,MAAM,EAAE,IAAI,CAAC;EACzD,MAAMM,aAAa,GAAGD,gBAAgB,CAACR,CAAC,EAAEI,MAAM,EAAE,KAAK,CAAC;EAExD,IAAIM,QAAQ,GAAGb,SAAS,CAACG,CAAC,CAAC;EAC3BW,WAAW,CAACX,CAAC,EAAEU,QAAQ,CAAC;EAExB,IAAIE,MAAM,GAAGC,MAAM,CAACC,iBAAiB;EACrC,IAAIC,IAAgB;EACpB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEW,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAE,EAAEX,CAAC,EAAE,EAAEW,QAAQ,EAAE;IAC3DC,gBAAgB,CACdZ,CAAC,GAAG,CAAC,GAAGE,eAAe,GAAGE,aAAa,EACvCJ,CAAC,GAAG,CAAC,IAAI,CAAC,EACV,KAAK,EACLJ,aAAa,CACd;IAEDS,QAAQ,GAAGlB,gBAAgB,CAACQ,CAAC,CAAC;IAC9B,MAAMkB,EAAE,GAAGtB,UAAU,CAACI,CAAC,EAAEU,QAAQ,CAAC;IAClC,IAAIQ,EAAE,GAAGN,MAAM,EAAE;MACfI,QAAQ,GAAG,CAAC;MACZD,IAAI,GAAGxB,KAAK,CAACmB,QAAQ,CAAC;MACtBE,MAAM,GAAGM,EAAE;;;EAIf;EACAR,QAAQ,GAAGb,SAAS,CAACG,CAAC,CAAC;EACvBW,WAAW,CAACX,CAAC,EAAEU,QAAQ,CAAC;EACxB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEW,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,EAAE,EAAEX,CAAC,EAAE,EAAEW,QAAQ,EAAE;IAC3DC,gBAAgB,CACdZ,CAAC,GAAG,CAAC,GAAGE,eAAe,GAAGE,aAAa,EACvCJ,CAAC,GAAG,CAAC,IAAI,CAAC,EACV,IAAI,EACJJ,aAAa,CACd;IAEDS,QAAQ,GAAGlB,gBAAgB,CAACQ,CAAC,CAAC;IAC9B,MAAMkB,EAAE,GAAGtB,UAAU,CAACI,CAAC,EAAEU,QAAQ,CAAC;IAClC,IAAIQ,EAAE,GAAGN,MAAM,EAAE;MACfI,QAAQ,GAAG,CAAC;MACZD,IAAI,GAAGxB,KAAK,CAACmB,QAAQ,CAAC;MACtBE,MAAM,GAAGM,EAAE;;;EAGfP,WAAW,CAACX,CAAC,EAAEe,IAAK,CAAC;AACvB,CAAC;AAED,MAAMP,gBAAgB,GAAGA,CACvBR,CAAS,EACTmB,KAAe,EACfC,SAAuB,KACrB;EACF,OAAOD,KAAK,CAACE,GAAG,CAAEC,IAAI,IAAI;IACxB,OAAO3B,eAAe,CAACK,CAAC,EAAEsB,IAAI,EAAEF,SAAS,CAAC;EAC5C,CAAC,CAAC;AACJ,CAAC;AAED,MAAMH,gBAAgB,GAAGA,CACvBM,WAAqB,EACrBC,SAAkB,EAClBC,OAAiB,EACjBxB,aAAuB,KACrB;EACF,MAAMyB,EAAE,GAAG,IAAIpC,KAAK,EAAE;EACtBiC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEI,OAAO,CAAEC,EAAE,IAAI;;IAC1B;IACA,MAAMC,IAAI,GAAGD,EAAE,CAACE,QAAQ,EAAE,CAAC,CAAC,CAAC,CAACC,EAAE;IAChC,MAAMC,MAAM,GAAGlC,YAAY,CACzB8B,EAAE,EACFC,IAAI,EACJH,EAAE,EACFF,SAAS,EACTC,OAAO,EACPxB,aAAa,CACd;IACD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAG,CAAA4B,EAAA,GAAAD,MAAM,CAACE,EAAE,cAAAD,EAAA,uBAAAA,EAAA,CAAEE,MAAM,KAAI,CAAC,EAAE9B,CAAC,EAAE,EAAE;MAC/C,MAAM+B,KAAK,GAAGR,EAAE,CAACS,OAAO,CAACL,MAAM,CAACE,EAAE,CAAC7B,CAAC,CAAC,CAAC;MACtC,IAAI+B,KAAK,EAAE;QACTA,KAAK,CAACE,IAAI,CAACvC,KAAK,GAAGM,CAAC;;;IAGxBX,sBAAsB,CAACkC,EAAE,EAAEF,EAAE,EAAEM,MAAM,CAACE,EAAE,CAAC;EAC3C,CAAC,CAAC;AACJ,CAAC;AAED,MAAMvB,WAAW,GAAGA,CAACX,CAAS,EAAEU,QAAgB,KAAI;EAClDA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiB,OAAO,CAAEY,KAAK,IAAI;IAC1BA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEZ,OAAO,CAAC,CAACa,CAAK,EAAEnC,CAAS,KAAI;MAClCL,CAAC,CAACqC,OAAO,CAACG,CAAC,CAAC,CAACF,IAAI,CAACvC,KAAK,GAAGM,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}