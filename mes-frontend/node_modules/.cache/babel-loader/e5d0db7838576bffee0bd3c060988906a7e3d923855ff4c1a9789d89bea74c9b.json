{"ast": null, "code": "import { group, sum } from '@antv/vendor/d3-array';\nimport { error } from '../../../utils/helper';\nimport * as SortMethods from './sort';\nconst DEFAULT_OPTIONS = {\n  y: 0,\n  thickness: 0.05,\n  weight: false,\n  marginRatio: 0.1,\n  id: node => node.id,\n  source: edge => edge.source,\n  target: edge => edge.target,\n  sourceWeight: edge => edge.value || 1,\n  targetWeight: edge => edge.value || 1,\n  sortBy: null\n};\n/**\n * Layout for Arc / Chord diagram with d3 style.\n */\nexport function Arc(options) {\n  const {\n    y,\n    thickness,\n    weight,\n    marginRatio,\n    id,\n    source,\n    target,\n    sourceWeight,\n    targetWeight,\n    sortBy\n  } = Object.assign(Object.assign({}, DEFAULT_OPTIONS), options);\n  function arc(data) {\n    // Clone first.\n    const nodes = data.nodes.map(n => Object.assign({}, n));\n    const edges = data.edges.map(n => Object.assign({}, n));\n    // Keep reference in below functions.\n    preprocess(nodes, edges);\n    sortNodes(nodes, edges);\n    layoutNodes(nodes, edges);\n    layoutEdges(nodes, edges);\n    return {\n      nodes,\n      edges\n    };\n  }\n  /**\n   * Calculate id, value, frequency for node, and source,target for edge.\n   */\n  function preprocess(nodes, edges) {\n    edges.forEach(edge => {\n      edge.source = source(edge);\n      edge.target = target(edge);\n      edge.sourceWeight = sourceWeight(edge);\n      edge.targetWeight = targetWeight(edge);\n    });\n    // Group edges by source, target.\n    const edgesBySource = group(edges, e => e.source);\n    const edgesByTarget = group(edges, e => e.target);\n    nodes.forEach(node => {\n      node.id = id(node);\n      const sources = edgesBySource.has(node.id) ? edgesBySource.get(node.id) : [];\n      const targets = edgesByTarget.has(node.id) ? edgesByTarget.get(node.id) : [];\n      node.frequency = sources.length + targets.length;\n      node.value = sum(sources, d => d.sourceWeight) + sum(targets, d => d.targetWeight);\n    });\n    return {\n      nodes,\n      edges\n    };\n  }\n  function sortNodes(nodes, edges) {\n    const method = typeof sortBy === 'function' ? sortBy : SortMethods[sortBy];\n    if (method) {\n      nodes.sort(method);\n    }\n  }\n  function layoutNodes(nodes, edges) {\n    const size = nodes.length;\n    if (!size) {\n      throw error(\"Invalid nodes: it's empty!\");\n    }\n    // No weight.\n    if (!weight) {\n      const deltaX = 1 / size;\n      nodes.forEach((node, i) => {\n        node.x = (i + 0.5) * deltaX;\n        node.y = y;\n      });\n      return {\n        nodes,\n        edges\n      };\n    }\n    // todo: marginRatio should be in [0, 1)\n    // todo: thickness shoule be in (0, 1)\n    const margin = marginRatio / (2 * size);\n    const total = nodes.reduce((prev, node) => prev += node.value, 0);\n    nodes.reduce((deltaX, node) => {\n      node.weight = node.value / total;\n      node.width = node.weight * (1 - marginRatio);\n      node.height = thickness;\n      /* points\n       * 3---2\n       * |   |\n       * 0---1\n       */\n      const minX = margin + deltaX;\n      const maxX = minX + node.width;\n      const minY = y - thickness / 2;\n      const maxY = minY + thickness;\n      node.x = [minX, maxX, maxX, minX];\n      node.y = [minY, minY, maxY, maxY];\n      // Return next deltaX.\n      return deltaX + node.width + 2 * margin;\n    }, 0);\n    return {\n      nodes,\n      edges\n    };\n  }\n  /**\n   * Get edge layout information from nodes, and save into edge object.\n   */\n  function layoutEdges(nodes, edges) {\n    const nodesMap = new Map(nodes.map(d => [d.id, d]));\n    if (!weight) {\n      edges.forEach(edge => {\n        const sourceId = source(edge);\n        const targetId = target(edge);\n        const sourceNode = nodesMap.get(sourceId);\n        const targetNode = nodesMap.get(targetId);\n        // Edge's layout information is Equal with node.\n        if (sourceNode && targetNode) {\n          edge.x = [sourceNode.x, targetNode.x];\n          edge.y = [sourceNode.y, targetNode.y];\n        }\n      });\n      return {\n        nodes,\n        edges\n      };\n    }\n    // Initial edge.x, edge.y.\n    edges.forEach(edge => {\n      edge.x = [0, 0, 0, 0];\n      edge.y = [y, y, y, y];\n    });\n    // Group edges by source, target.\n    const edgesBySource = group(edges, e => e.source);\n    const edgesByTarget = group(edges, e => e.target);\n    // When weight = true, we need to calculation the bbox of edge start/end.\n    nodes.forEach(node => {\n      const {\n        edges,\n        width,\n        x,\n        y,\n        value,\n        id\n      } = node;\n      const sourceEdges = edgesBySource.get(id) || [];\n      const targetEdges = edgesByTarget.get(id) || [];\n      let offset = 0;\n      /* points\n       * 0----------2\n       * |          |\n       * 1----------3\n       */\n      sourceEdges.map(edge => {\n        const w = edge.sourceWeight / value * width;\n        edge.x[0] = x[0] + offset;\n        edge.x[1] = x[0] + offset + w;\n        offset += w;\n      });\n      targetEdges.forEach(edge => {\n        const w = edge.targetWeight / value * width;\n        edge.x[3] = x[0] + offset;\n        edge.x[2] = x[0] + offset + w;\n        offset += w;\n      });\n    });\n  }\n  return arc;\n}", "map": {"version": 3, "names": ["group", "sum", "error", "SortMethods", "DEFAULT_OPTIONS", "y", "thickness", "weight", "marginRatio", "id", "node", "source", "edge", "target", "sourceWeight", "value", "targetWeight", "sortBy", "Arc", "options", "Object", "assign", "arc", "data", "nodes", "map", "n", "edges", "preprocess", "sortNodes", "layoutNodes", "<PERSON><PERSON><PERSON>", "for<PERSON>ach", "edgesBySource", "e", "edgesByTarget", "sources", "has", "get", "targets", "frequency", "length", "d", "method", "sort", "size", "deltaX", "i", "x", "margin", "total", "reduce", "prev", "width", "height", "minX", "maxX", "minY", "maxY", "nodesMap", "Map", "sourceId", "targetId", "sourceNode", "targetNode", "sourceEdges", "targetEdges", "offset", "w"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/arc/arc.ts"], "sourcesContent": ["import { group, sum } from '@antv/vendor/d3-array';\nimport { error } from '../../../utils/helper';\nimport { ArcData, ArcOptions, ArcEdge, ArcNode } from './types';\nimport * as SortMethods from './sort';\n\nconst DEFAULT_OPTIONS = {\n  y: 0,\n  thickness: 0.05,\n  weight: false,\n  marginRatio: 0.1,\n  id: (node) => node.id,\n  source: (edge) => edge.source,\n  target: (edge) => edge.target,\n  sourceWeight: (edge) => edge.value || 1,\n  targetWeight: (edge) => edge.value || 1,\n  sortBy: null,\n};\n\n/**\n * Layout for Arc / Chord diagram with d3 style.\n */\nexport function Arc(options?: ArcOptions) {\n  const {\n    y,\n    thickness,\n    weight,\n    marginRatio,\n    id,\n    source,\n    target,\n    sourceWeight,\n    targetWeight,\n    sortBy,\n  } = {\n    ...DEFAULT_OPTIONS,\n    ...options,\n  };\n\n  function arc(data: ArcData) {\n    // Clone first.\n    const nodes = data.nodes.map((n) => ({ ...n }));\n    const edges = data.edges.map((n) => ({ ...n }));\n\n    // Keep reference in below functions.\n    preprocess(nodes, edges);\n    sortNodes(nodes, edges);\n    layoutNodes(nodes, edges);\n    layoutEdges(nodes, edges);\n\n    return { nodes, edges };\n  }\n\n  /**\n   * Calculate id, value, frequency for node, and source,target for edge.\n   */\n  function preprocess(nodes: ArcNode[], edges: ArcEdge[]) {\n    edges.forEach((edge) => {\n      edge.source = source(edge);\n      edge.target = target(edge);\n      edge.sourceWeight = sourceWeight(edge);\n      edge.targetWeight = targetWeight(edge);\n    });\n\n    // Group edges by source, target.\n    const edgesBySource = group(edges, (e: any) => e.source);\n    const edgesByTarget = group(edges, (e: any) => e.target);\n\n    nodes.forEach((node) => {\n      node.id = id(node);\n      const sources = edgesBySource.has(node.id)\n        ? edgesBySource.get(node.id)\n        : [];\n      const targets = edgesByTarget.has(node.id)\n        ? edgesByTarget.get(node.id)\n        : [];\n      node.frequency = sources.length + targets.length;\n\n      node.value =\n        sum(sources, (d) => d.sourceWeight) +\n        sum(targets, (d) => d.targetWeight);\n    });\n\n    return { nodes, edges };\n  }\n\n  function sortNodes(nodes: ArcNode[], edges: ArcEdge[]) {\n    const method = typeof sortBy === 'function' ? sortBy : SortMethods[sortBy];\n\n    if (method) {\n      nodes.sort(method);\n    }\n  }\n\n  function layoutNodes(nodes: ArcNode[], edges: ArcEdge[]) {\n    const size = nodes.length;\n    if (!size) {\n      throw error(\"Invalid nodes: it's empty!\");\n    }\n\n    // No weight.\n    if (!weight) {\n      const deltaX = 1 / size;\n\n      nodes.forEach((node, i: number) => {\n        node.x = (i + 0.5) * deltaX;\n        node.y = y;\n      });\n\n      return { nodes, edges };\n    }\n\n    // todo: marginRatio should be in [0, 1)\n    // todo: thickness shoule be in (0, 1)\n    const margin = marginRatio / (2 * size);\n\n    const total = nodes.reduce((prev: number, node) => (prev += node.value), 0);\n\n    nodes.reduce((deltaX: number, node) => {\n      node.weight = node.value / total;\n      node.width = node.weight * (1 - marginRatio);\n      node.height = thickness;\n\n      /* points\n       * 3---2\n       * |   |\n       * 0---1\n       */\n      const minX = margin + deltaX;\n      const maxX = minX + node.width;\n      const minY = y - thickness / 2;\n      const maxY = minY + thickness;\n\n      node.x = [minX, maxX, maxX, minX];\n      node.y = [minY, minY, maxY, maxY];\n\n      // Return next deltaX.\n      return deltaX + node.width + 2 * margin;\n    }, 0);\n    return {\n      nodes,\n      edges,\n    };\n  }\n\n  /**\n   * Get edge layout information from nodes, and save into edge object.\n   */\n  function layoutEdges(nodes: ArcNode[], edges: ArcEdge[]) {\n    const nodesMap = new Map(nodes.map((d) => [d.id, d]));\n\n    if (!weight) {\n      edges.forEach((edge) => {\n        const sourceId = source(edge);\n        const targetId = target(edge);\n\n        const sourceNode: any = nodesMap.get(sourceId);\n        const targetNode: any = nodesMap.get(targetId);\n\n        // Edge's layout information is Equal with node.\n        if (sourceNode && targetNode) {\n          edge.x = [sourceNode.x, targetNode.x];\n          edge.y = [sourceNode.y, targetNode.y];\n        }\n      });\n      return { nodes, edges };\n    }\n\n    // Initial edge.x, edge.y.\n    edges.forEach((edge) => {\n      edge.x = [0, 0, 0, 0];\n      edge.y = [y, y, y, y];\n    });\n\n    // Group edges by source, target.\n    const edgesBySource = group(edges, (e: any) => e.source);\n    const edgesByTarget = group(edges, (e: any) => e.target);\n\n    // When weight = true, we need to calculation the bbox of edge start/end.\n    nodes.forEach((node) => {\n      const { edges, width, x, y, value, id } = node;\n\n      const sourceEdges = edgesBySource.get(id) || [];\n      const targetEdges = edgesByTarget.get(id) || [];\n\n      let offset = 0;\n      /* points\n       * 0----------2\n       * |          |\n       * 1----------3\n       */\n      sourceEdges.map((edge) => {\n        const w = (edge.sourceWeight / value) * width;\n        edge.x[0] = x[0] + offset;\n        edge.x[1] = x[0] + offset + w;\n\n        offset += w;\n      });\n\n      targetEdges.forEach((edge) => {\n        const w = (edge.targetWeight / value) * width;\n        edge.x[3] = x[0] + offset;\n        edge.x[2] = x[0] + offset + w;\n\n        offset += w;\n      });\n    });\n  }\n\n  return arc;\n}\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,GAAG,QAAQ,uBAAuB;AAClD,SAASC,KAAK,QAAQ,uBAAuB;AAE7C,OAAO,KAAKC,WAAW,MAAM,QAAQ;AAErC,MAAMC,eAAe,GAAG;EACtBC,CAAC,EAAE,CAAC;EACJC,SAAS,EAAE,IAAI;EACfC,MAAM,EAAE,KAAK;EACbC,WAAW,EAAE,GAAG;EAChBC,EAAE,EAAGC,IAAI,IAAKA,IAAI,CAACD,EAAE;EACrBE,MAAM,EAAGC,IAAI,IAAKA,IAAI,CAACD,MAAM;EAC7BE,MAAM,EAAGD,IAAI,IAAKA,IAAI,CAACC,MAAM;EAC7BC,YAAY,EAAGF,IAAI,IAAKA,IAAI,CAACG,KAAK,IAAI,CAAC;EACvCC,YAAY,EAAGJ,IAAI,IAAKA,IAAI,CAACG,KAAK,IAAI,CAAC;EACvCE,MAAM,EAAE;CACT;AAED;;;AAGA,OAAM,SAAUC,GAAGA,CAACC,OAAoB;EACtC,MAAM;IACJd,CAAC;IACDC,SAAS;IACTC,MAAM;IACNC,WAAW;IACXC,EAAE;IACFE,MAAM;IACNE,MAAM;IACNC,YAAY;IACZE,YAAY;IACZC;EAAM,CACP,GAAAG,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACIjB,eAAe,GACfe,OAAO,CACX;EAED,SAASG,GAAGA,CAACC,IAAa;IACxB;IACA,MAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK,CAACC,GAAG,CAAEC,CAAC,IAAKN,MAAA,CAAAC,MAAA,KAAMK,CAAC,CAAG,CAAC;IAC/C,MAAMC,KAAK,GAAGJ,IAAI,CAACI,KAAK,CAACF,GAAG,CAAEC,CAAC,IAAKN,MAAA,CAAAC,MAAA,KAAMK,CAAC,CAAG,CAAC;IAE/C;IACAE,UAAU,CAACJ,KAAK,EAAEG,KAAK,CAAC;IACxBE,SAAS,CAACL,KAAK,EAAEG,KAAK,CAAC;IACvBG,WAAW,CAACN,KAAK,EAAEG,KAAK,CAAC;IACzBI,WAAW,CAACP,KAAK,EAAEG,KAAK,CAAC;IAEzB,OAAO;MAAEH,KAAK;MAAEG;IAAK,CAAE;EACzB;EAEA;;;EAGA,SAASC,UAAUA,CAACJ,KAAgB,EAAEG,KAAgB;IACpDA,KAAK,CAACK,OAAO,CAAEpB,IAAI,IAAI;MACrBA,IAAI,CAACD,MAAM,GAAGA,MAAM,CAACC,IAAI,CAAC;MAC1BA,IAAI,CAACC,MAAM,GAAGA,MAAM,CAACD,IAAI,CAAC;MAC1BA,IAAI,CAACE,YAAY,GAAGA,YAAY,CAACF,IAAI,CAAC;MACtCA,IAAI,CAACI,YAAY,GAAGA,YAAY,CAACJ,IAAI,CAAC;IACxC,CAAC,CAAC;IAEF;IACA,MAAMqB,aAAa,GAAGjC,KAAK,CAAC2B,KAAK,EAAGO,CAAM,IAAKA,CAAC,CAACvB,MAAM,CAAC;IACxD,MAAMwB,aAAa,GAAGnC,KAAK,CAAC2B,KAAK,EAAGO,CAAM,IAAKA,CAAC,CAACrB,MAAM,CAAC;IAExDW,KAAK,CAACQ,OAAO,CAAEtB,IAAI,IAAI;MACrBA,IAAI,CAACD,EAAE,GAAGA,EAAE,CAACC,IAAI,CAAC;MAClB,MAAM0B,OAAO,GAAGH,aAAa,CAACI,GAAG,CAAC3B,IAAI,CAACD,EAAE,CAAC,GACtCwB,aAAa,CAACK,GAAG,CAAC5B,IAAI,CAACD,EAAE,CAAC,GAC1B,EAAE;MACN,MAAM8B,OAAO,GAAGJ,aAAa,CAACE,GAAG,CAAC3B,IAAI,CAACD,EAAE,CAAC,GACtC0B,aAAa,CAACG,GAAG,CAAC5B,IAAI,CAACD,EAAE,CAAC,GAC1B,EAAE;MACNC,IAAI,CAAC8B,SAAS,GAAGJ,OAAO,CAACK,MAAM,GAAGF,OAAO,CAACE,MAAM;MAEhD/B,IAAI,CAACK,KAAK,GACRd,GAAG,CAACmC,OAAO,EAAGM,CAAC,IAAKA,CAAC,CAAC5B,YAAY,CAAC,GACnCb,GAAG,CAACsC,OAAO,EAAGG,CAAC,IAAKA,CAAC,CAAC1B,YAAY,CAAC;IACvC,CAAC,CAAC;IAEF,OAAO;MAAEQ,KAAK;MAAEG;IAAK,CAAE;EACzB;EAEA,SAASE,SAASA,CAACL,KAAgB,EAAEG,KAAgB;IACnD,MAAMgB,MAAM,GAAG,OAAO1B,MAAM,KAAK,UAAU,GAAGA,MAAM,GAAGd,WAAW,CAACc,MAAM,CAAC;IAE1E,IAAI0B,MAAM,EAAE;MACVnB,KAAK,CAACoB,IAAI,CAACD,MAAM,CAAC;;EAEtB;EAEA,SAASb,WAAWA,CAACN,KAAgB,EAAEG,KAAgB;IACrD,MAAMkB,IAAI,GAAGrB,KAAK,CAACiB,MAAM;IACzB,IAAI,CAACI,IAAI,EAAE;MACT,MAAM3C,KAAK,CAAC,4BAA4B,CAAC;;IAG3C;IACA,IAAI,CAACK,MAAM,EAAE;MACX,MAAMuC,MAAM,GAAG,CAAC,GAAGD,IAAI;MAEvBrB,KAAK,CAACQ,OAAO,CAAC,CAACtB,IAAI,EAAEqC,CAAS,KAAI;QAChCrC,IAAI,CAACsC,CAAC,GAAG,CAACD,CAAC,GAAG,GAAG,IAAID,MAAM;QAC3BpC,IAAI,CAACL,CAAC,GAAGA,CAAC;MACZ,CAAC,CAAC;MAEF,OAAO;QAAEmB,KAAK;QAAEG;MAAK,CAAE;;IAGzB;IACA;IACA,MAAMsB,MAAM,GAAGzC,WAAW,IAAI,CAAC,GAAGqC,IAAI,CAAC;IAEvC,MAAMK,KAAK,GAAG1B,KAAK,CAAC2B,MAAM,CAAC,CAACC,IAAY,EAAE1C,IAAI,KAAM0C,IAAI,IAAI1C,IAAI,CAACK,KAAM,EAAE,CAAC,CAAC;IAE3ES,KAAK,CAAC2B,MAAM,CAAC,CAACL,MAAc,EAAEpC,IAAI,KAAI;MACpCA,IAAI,CAACH,MAAM,GAAGG,IAAI,CAACK,KAAK,GAAGmC,KAAK;MAChCxC,IAAI,CAAC2C,KAAK,GAAG3C,IAAI,CAACH,MAAM,IAAI,CAAC,GAAGC,WAAW,CAAC;MAC5CE,IAAI,CAAC4C,MAAM,GAAGhD,SAAS;MAEvB;;;;;MAKA,MAAMiD,IAAI,GAAGN,MAAM,GAAGH,MAAM;MAC5B,MAAMU,IAAI,GAAGD,IAAI,GAAG7C,IAAI,CAAC2C,KAAK;MAC9B,MAAMI,IAAI,GAAGpD,CAAC,GAAGC,SAAS,GAAG,CAAC;MAC9B,MAAMoD,IAAI,GAAGD,IAAI,GAAGnD,SAAS;MAE7BI,IAAI,CAACsC,CAAC,GAAG,CAACO,IAAI,EAAEC,IAAI,EAAEA,IAAI,EAAED,IAAI,CAAC;MACjC7C,IAAI,CAACL,CAAC,GAAG,CAACoD,IAAI,EAAEA,IAAI,EAAEC,IAAI,EAAEA,IAAI,CAAC;MAEjC;MACA,OAAOZ,MAAM,GAAGpC,IAAI,CAAC2C,KAAK,GAAG,CAAC,GAAGJ,MAAM;IACzC,CAAC,EAAE,CAAC,CAAC;IACL,OAAO;MACLzB,KAAK;MACLG;KACD;EACH;EAEA;;;EAGA,SAASI,WAAWA,CAACP,KAAgB,EAAEG,KAAgB;IACrD,MAAMgC,QAAQ,GAAG,IAAIC,GAAG,CAACpC,KAAK,CAACC,GAAG,CAAEiB,CAAC,IAAK,CAACA,CAAC,CAACjC,EAAE,EAAEiC,CAAC,CAAC,CAAC,CAAC;IAErD,IAAI,CAACnC,MAAM,EAAE;MACXoB,KAAK,CAACK,OAAO,CAAEpB,IAAI,IAAI;QACrB,MAAMiD,QAAQ,GAAGlD,MAAM,CAACC,IAAI,CAAC;QAC7B,MAAMkD,QAAQ,GAAGjD,MAAM,CAACD,IAAI,CAAC;QAE7B,MAAMmD,UAAU,GAAQJ,QAAQ,CAACrB,GAAG,CAACuB,QAAQ,CAAC;QAC9C,MAAMG,UAAU,GAAQL,QAAQ,CAACrB,GAAG,CAACwB,QAAQ,CAAC;QAE9C;QACA,IAAIC,UAAU,IAAIC,UAAU,EAAE;UAC5BpD,IAAI,CAACoC,CAAC,GAAG,CAACe,UAAU,CAACf,CAAC,EAAEgB,UAAU,CAAChB,CAAC,CAAC;UACrCpC,IAAI,CAACP,CAAC,GAAG,CAAC0D,UAAU,CAAC1D,CAAC,EAAE2D,UAAU,CAAC3D,CAAC,CAAC;;MAEzC,CAAC,CAAC;MACF,OAAO;QAAEmB,KAAK;QAAEG;MAAK,CAAE;;IAGzB;IACAA,KAAK,CAACK,OAAO,CAAEpB,IAAI,IAAI;MACrBA,IAAI,CAACoC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACrBpC,IAAI,CAACP,CAAC,GAAG,CAACA,CAAC,EAAEA,CAAC,EAAEA,CAAC,EAAEA,CAAC,CAAC;IACvB,CAAC,CAAC;IAEF;IACA,MAAM4B,aAAa,GAAGjC,KAAK,CAAC2B,KAAK,EAAGO,CAAM,IAAKA,CAAC,CAACvB,MAAM,CAAC;IACxD,MAAMwB,aAAa,GAAGnC,KAAK,CAAC2B,KAAK,EAAGO,CAAM,IAAKA,CAAC,CAACrB,MAAM,CAAC;IAExD;IACAW,KAAK,CAACQ,OAAO,CAAEtB,IAAI,IAAI;MACrB,MAAM;QAAEiB,KAAK;QAAE0B,KAAK;QAAEL,CAAC;QAAE3C,CAAC;QAAEU,KAAK;QAAEN;MAAE,CAAE,GAAGC,IAAI;MAE9C,MAAMuD,WAAW,GAAGhC,aAAa,CAACK,GAAG,CAAC7B,EAAE,CAAC,IAAI,EAAE;MAC/C,MAAMyD,WAAW,GAAG/B,aAAa,CAACG,GAAG,CAAC7B,EAAE,CAAC,IAAI,EAAE;MAE/C,IAAI0D,MAAM,GAAG,CAAC;MACd;;;;;MAKAF,WAAW,CAACxC,GAAG,CAAEb,IAAI,IAAI;QACvB,MAAMwD,CAAC,GAAIxD,IAAI,CAACE,YAAY,GAAGC,KAAK,GAAIsC,KAAK;QAC7CzC,IAAI,CAACoC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGmB,MAAM;QACzBvD,IAAI,CAACoC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGmB,MAAM,GAAGC,CAAC;QAE7BD,MAAM,IAAIC,CAAC;MACb,CAAC,CAAC;MAEFF,WAAW,CAAClC,OAAO,CAAEpB,IAAI,IAAI;QAC3B,MAAMwD,CAAC,GAAIxD,IAAI,CAACI,YAAY,GAAGD,KAAK,GAAIsC,KAAK;QAC7CzC,IAAI,CAACoC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGmB,MAAM;QACzBvD,IAAI,CAACoC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGmB,MAAM,GAAGC,CAAC;QAE7BD,MAAM,IAAIC,CAAC;MACb,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA,OAAO9C,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}