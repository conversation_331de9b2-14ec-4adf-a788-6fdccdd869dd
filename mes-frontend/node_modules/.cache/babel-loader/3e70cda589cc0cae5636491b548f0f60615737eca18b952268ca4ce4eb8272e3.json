{"ast": null, "code": "import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\nexport var scheme = new Array(3).concat(\"fee6cefdae6be6550d\", \"feeddefdbe85fd8d3cd94701\", \"feeddefdbe85fd8d3ce6550da63603\", \"feeddefdd0a2fdae6bfd8d3ce6550da63603\", \"feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04\", \"fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04\", \"fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704\").map(colors);\nexport default ramp(scheme);", "map": {"version": 3, "names": ["colors", "ramp", "scheme", "Array", "concat", "map"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-scale-chromatic/src/sequential-single/Oranges.js"], "sourcesContent": ["import colors from \"../colors.js\";\nimport ramp from \"../ramp.js\";\n\nexport var scheme = new Array(3).concat(\n  \"fee6cefdae6be6550d\",\n  \"feeddefdbe85fd8d3cd94701\",\n  \"feeddefdbe85fd8d3ce6550da63603\",\n  \"feeddefdd0a2fdae6bfd8d3ce6550da63603\",\n  \"feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04\",\n  \"fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04\",\n  \"fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704\"\n).map(colors);\n\nexport default ramp(scheme);\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAE7B,OAAO,IAAIC,MAAM,GAAG,IAAIC,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,CACrC,oBAAoB,EACpB,0BAA0B,EAC1B,gCAAgC,EAChC,sCAAsC,EACtC,4CAA4C,EAC5C,kDAAkD,EAClD,wDACF,CAAC,CAACC,GAAG,CAACL,MAAM,CAAC;AAEb,eAAeC,IAAI,CAACC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}