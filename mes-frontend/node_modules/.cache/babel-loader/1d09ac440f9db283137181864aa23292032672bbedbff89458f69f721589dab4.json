{"ast": null, "code": "export var grid = function (container, children, config) {\n  // todo\n  return [];\n};", "map": {"version": 3, "names": ["grid", "container", "children", "config"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/util/layout/grid/index.ts"], "sourcesContent": ["import type { LayoutExecuter } from '../types';\nimport type { GridLayoutConfig } from './types';\n\nexport const grid: LayoutExecuter<GridLayoutConfig> = function (container, children, config) {\n  // todo\n  return [];\n};\n"], "mappings": "AAGA,OAAO,IAAMA,IAAI,GAAqC,SAAAA,CAAUC,SAAS,EAAEC,QAAQ,EAAEC,MAAM;EACzF;EACA,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}