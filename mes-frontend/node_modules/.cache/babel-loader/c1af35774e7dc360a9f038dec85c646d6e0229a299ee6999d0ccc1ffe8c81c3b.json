{"ast": null, "code": "export { getDefaultStyle as inside } from './default';", "map": {"version": 3, "names": ["getDefaultStyle", "inside"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/inside.ts"], "sourcesContent": ["export { getDefaultStyle as inside } from './default';\n"], "mappings": "AAAA,SAASA,eAAe,IAAIC,MAAM,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}