{"ast": null, "code": "import Half from \"./half.js\";\nexport default function (callback) {\n  var halves = [],\n    q,\n    node = this._root,\n    child,\n    x0,\n    x1;\n  if (node) halves.push(new Half(node, this._x0, this._x1));\n  while (q = halves.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, x1 = q.x1) && node.length) {\n      var xm = (x0 + x1) / 2;\n      if (child = node[1]) halves.push(new Half(child, xm, x1));\n      if (child = node[0]) halves.push(new Half(child, x0, xm));\n    }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["Half", "callback", "halves", "q", "node", "_root", "child", "x0", "x1", "push", "_x0", "_x1", "pop", "length", "xm"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-binarytree/src/visit.js"], "sourcesContent": ["import Half from \"./half.js\";\n\nexport default function(callback) {\n  var halves = [], q, node = this._root, child, x0, x1;\n  if (node) halves.push(new Half(node, this._x0, this._x1));\n  while (q = halves.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, x1 = q.x1) && node.length) {\n      var xm = (x0 + x1) / 2;\n      if (child = node[1]) halves.push(new Half(child, xm, x1));\n      if (child = node[0]) halves.push(new Half(child, x0, xm));\n    }\n  }\n  return this;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,QAAQ,EAAE;EAChC,IAAIC,MAAM,GAAG,EAAE;IAAEC,CAAC;IAAEC,IAAI,GAAG,IAAI,CAACC,KAAK;IAAEC,KAAK;IAAEC,EAAE;IAAEC,EAAE;EACpD,IAAIJ,IAAI,EAAEF,MAAM,CAACO,IAAI,CAAC,IAAIT,IAAI,CAACI,IAAI,EAAE,IAAI,CAACM,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;EACzD,OAAOR,CAAC,GAAGD,MAAM,CAACU,GAAG,CAAC,CAAC,EAAE;IACvB,IAAI,CAACX,QAAQ,CAACG,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAEG,EAAE,GAAGJ,CAAC,CAACI,EAAE,EAAEC,EAAE,GAAGL,CAAC,CAACK,EAAE,CAAC,IAAIJ,IAAI,CAACS,MAAM,EAAE;MACjE,IAAIC,EAAE,GAAG,CAACP,EAAE,GAAGC,EAAE,IAAI,CAAC;MACtB,IAAIF,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,MAAM,CAACO,IAAI,CAAC,IAAIT,IAAI,CAACM,KAAK,EAAEQ,EAAE,EAAEN,EAAE,CAAC,CAAC;MACzD,IAAIF,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,MAAM,CAACO,IAAI,CAAC,IAAIT,IAAI,CAACM,KAAK,EAAEC,EAAE,EAAEO,EAAE,CAAC,CAAC;IAC3D;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}