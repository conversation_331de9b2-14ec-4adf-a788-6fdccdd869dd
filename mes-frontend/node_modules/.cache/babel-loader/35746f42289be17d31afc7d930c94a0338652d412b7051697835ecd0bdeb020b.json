{"ast": null, "code": "import isNil from './is-nil';\nimport isArray from './is-array';\nvar firstValue = function (data, name) {\n  var rst = null;\n  for (var i = 0; i < data.length; i++) {\n    var obj = data[i];\n    var value = obj[name];\n    if (!isNil(value)) {\n      if (isArray(value)) {\n        rst = value[0]; // todo 这里是否应该使用递归，调用 firstValue @绝云\n      } else {\n        rst = value;\n      }\n      break;\n    }\n  }\n  return rst;\n};\nexport default firstValue;", "map": {"version": 3, "names": ["isNil", "isArray", "firstValue", "data", "name", "rst", "i", "length", "obj", "value"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/first-value.ts"], "sourcesContent": ["import isNil from './is-nil';\nimport isArray from './is-array';\n\nconst firstValue = function(data: object[], name: string): any {\n  let rst = null;\n  for (let i = 0; i < data.length; i++) {\n    const obj = data[i];\n    const value = obj[name];\n    if (!isNil(value)) {\n      if (isArray(value)) {\n        rst = value[0]; // todo 这里是否应该使用递归，调用 firstValue @绝云\n      } else {\n        rst = value;\n      }\n      break;\n    }\n  }\n  return rst;\n};\n\nexport default firstValue;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAC5B,OAAOC,OAAO,MAAM,YAAY;AAEhC,IAAMC,UAAU,GAAG,SAAAA,CAASC,IAAc,EAAEC,IAAY;EACtD,IAAIC,GAAG,GAAG,IAAI;EACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAME,GAAG,GAAGL,IAAI,CAACG,CAAC,CAAC;IACnB,IAAMG,KAAK,GAAGD,GAAG,CAACJ,IAAI,CAAC;IACvB,IAAI,CAACJ,KAAK,CAACS,KAAK,CAAC,EAAE;MACjB,IAAIR,OAAO,CAACQ,KAAK,CAAC,EAAE;QAClBJ,GAAG,GAAGI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;OACjB,MAAM;QACLJ,GAAG,GAAGI,<PERSON>AA<PERSON>;;MAEb;;;EAGJ,OAAOJ,GAAG;AACZ,CAAC;AAED,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}