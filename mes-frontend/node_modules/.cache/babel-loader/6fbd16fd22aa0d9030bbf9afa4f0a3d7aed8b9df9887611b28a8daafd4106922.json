{"ast": null, "code": "var _ = require(\"./lodash\");\nvar util = require(\"./util\");\nmodule.exports = addBorderSegments;\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n    if (_.has(node, \"minRank\")) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1; rank < maxRank; ++rank) {\n        addBorderNode(g, \"borderLeft\", \"_bl\", v, node, rank);\n        addBorderNode(g, \"borderRight\", \"_br\", v, node, rank);\n      }\n    }\n  }\n  _.forEach(g.children(), dfs);\n}\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = {\n    width: 0,\n    height: 0,\n    rank: rank,\n    borderType: prop\n  };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, \"border\", label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, {\n      weight: 1\n    });\n  }\n}", "map": {"version": 3, "names": ["_", "require", "util", "module", "exports", "addBorderSegments", "g", "dfs", "v", "children", "node", "length", "for<PERSON>ach", "has", "borderLeft", "borderRight", "rank", "minRank", "maxRank", "addBorderNode", "prop", "prefix", "sg", "sgNode", "label", "width", "height", "borderType", "prev", "curr", "addDummyNode", "setParent", "setEdge", "weight"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/add-border-segments.js"], "sourcesContent": ["var _ = require(\"./lodash\");\nvar util = require(\"./util\");\n\nmodule.exports = addBorderSegments;\n\nfunction addBorderSegments(g) {\n  function dfs(v) {\n    var children = g.children(v);\n    var node = g.node(v);\n    if (children.length) {\n      _.forEach(children, dfs);\n    }\n\n    if (_.has(node, \"minRank\")) {\n      node.borderLeft = [];\n      node.borderRight = [];\n      for (var rank = node.minRank, maxRank = node.maxRank + 1;\n        rank < maxRank;\n        ++rank) {\n        addBorderNode(g, \"borderLeft\", \"_bl\", v, node, rank);\n        addBorderNode(g, \"borderRight\", \"_br\", v, node, rank);\n      }\n    }\n  }\n\n  _.forEach(g.children(), dfs);\n}\n\nfunction addBorderNode(g, prop, prefix, sg, sgNode, rank) {\n  var label = { width: 0, height: 0, rank: rank, borderType: prop };\n  var prev = sgNode[prop][rank - 1];\n  var curr = util.addDummyNode(g, \"border\", label, prefix);\n  sgNode[prop][rank] = curr;\n  g.setParent(curr, sg);\n  if (prev) {\n    g.setEdge(prev, curr, { weight: 1 });\n  }\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAE5BE,MAAM,CAACC,OAAO,GAAGC,iBAAiB;AAElC,SAASA,iBAAiBA,CAACC,CAAC,EAAE;EAC5B,SAASC,GAAGA,CAACC,CAAC,EAAE;IACd,IAAIC,QAAQ,GAAGH,CAAC,CAACG,QAAQ,CAACD,CAAC,CAAC;IAC5B,IAAIE,IAAI,GAAGJ,CAAC,CAACI,IAAI,CAACF,CAAC,CAAC;IACpB,IAAIC,QAAQ,CAACE,MAAM,EAAE;MACnBX,CAAC,CAACY,OAAO,CAACH,QAAQ,EAAEF,GAAG,CAAC;IAC1B;IAEA,IAAIP,CAAC,CAACa,GAAG,CAACH,IAAI,EAAE,SAAS,CAAC,EAAE;MAC1BA,IAAI,CAACI,UAAU,GAAG,EAAE;MACpBJ,IAAI,CAACK,WAAW,GAAG,EAAE;MACrB,KAAK,IAAIC,IAAI,GAAGN,IAAI,CAACO,OAAO,EAAEC,OAAO,GAAGR,IAAI,CAACQ,OAAO,GAAG,CAAC,EACtDF,IAAI,GAAGE,OAAO,EACd,EAAEF,IAAI,EAAE;QACRG,aAAa,CAACb,CAAC,EAAE,YAAY,EAAE,KAAK,EAAEE,CAAC,EAAEE,IAAI,EAAEM,IAAI,CAAC;QACpDG,aAAa,CAACb,CAAC,EAAE,aAAa,EAAE,KAAK,EAAEE,CAAC,EAAEE,IAAI,EAAEM,IAAI,CAAC;MACvD;IACF;EACF;EAEAhB,CAAC,CAACY,OAAO,CAACN,CAAC,CAACG,QAAQ,CAAC,CAAC,EAAEF,GAAG,CAAC;AAC9B;AAEA,SAASY,aAAaA,CAACb,CAAC,EAAEc,IAAI,EAAEC,MAAM,EAAEC,EAAE,EAAEC,MAAM,EAAEP,IAAI,EAAE;EACxD,IAAIQ,KAAK,GAAG;IAAEC,KAAK,EAAE,CAAC;IAAEC,MAAM,EAAE,CAAC;IAAEV,IAAI,EAAEA,IAAI;IAAEW,UAAU,EAAEP;EAAK,CAAC;EACjE,IAAIQ,IAAI,GAAGL,MAAM,CAACH,IAAI,CAAC,CAACJ,IAAI,GAAG,CAAC,CAAC;EACjC,IAAIa,IAAI,GAAG3B,IAAI,CAAC4B,YAAY,CAACxB,CAAC,EAAE,QAAQ,EAAEkB,KAAK,EAAEH,MAAM,CAAC;EACxDE,MAAM,CAACH,IAAI,CAAC,CAACJ,IAAI,CAAC,GAAGa,IAAI;EACzBvB,CAAC,CAACyB,SAAS,CAACF,IAAI,EAAEP,EAAE,CAAC;EACrB,IAAIM,IAAI,EAAE;IACRtB,CAAC,CAAC0B,OAAO,CAACJ,IAAI,EAAEC,IAAI,EAAE;MAAEI,MAAM,EAAE;IAAE,CAAC,CAAC;EACtC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}