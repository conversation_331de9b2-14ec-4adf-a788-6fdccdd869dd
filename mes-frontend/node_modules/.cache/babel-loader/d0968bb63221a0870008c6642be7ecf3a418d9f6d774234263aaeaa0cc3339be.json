{"ast": null, "code": "import { __assign, __extends } from \"tslib\";\nimport { Component } from '../../core';\nimport { Circle } from '../../shapes';\nimport { select, subStyleProps } from '../../util';\nimport { deepAssign } from '../../util/deep-assign';\nvar TimeModeHandle = /** @class */function (_super) {\n  __extends(TimeModeHandle, _super);\n  function TimeModeHandle(options) {\n    var _this = _super.call(this, deepAssign({}, TimeModeHandle.defaultOptions, options)) || this;\n    _this.bindEvents();\n    return _this;\n  }\n  TimeModeHandle.prototype.bindEvents = function () {\n    var _this = this;\n    this.addEventListener('mouseenter', function () {\n      _this.attr('lineWidth', Math.ceil(+(_this.style.r || 0) / 2));\n    });\n    this.addEventListener('mouseleave', function () {\n      _this.attr('lineWidth', 0);\n    });\n  };\n  TimeModeHandle.defaultOptions = {\n    style: {\n      r: 5,\n      fill: '#3f7cf7',\n      lineWidth: 0,\n      stroke: '#3f7cf7',\n      strokeOpacity: 0.5,\n      cursor: 'pointer'\n    }\n  };\n  return TimeModeHandle;\n}(Circle);\nexport { TimeModeHandle };\nvar ChartModeHandle = /** @class */function (_super) {\n  __extends(ChartModeHandle, _super);\n  function ChartModeHandle(options) {\n    return _super.call(this, deepAssign({}, ChartModeHandle.defaultOptions, options)) || this;\n  }\n  ChartModeHandle.prototype.renderBackground = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      width = _a.width,\n      height = _a.height;\n    var style = subStyleProps(this.attributes, 'background');\n    select(this).maybeAppend('background', 'rect').attr('className', 'background').styles(__assign({\n      x: x - width / 2,\n      y: y - height / 2,\n      width: width,\n      height: height\n    }, style));\n  };\n  ChartModeHandle.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      iconSize = _a.iconSize;\n    var style = subStyleProps(this.attributes, 'icon');\n    var diffX = 1;\n    var diffY = iconSize / 2;\n    select(this).maybeAppend('icon-left-line', 'line').attr('className', 'icon-left-line').styles(__assign({\n      x1: x - diffX,\n      y1: y - diffY,\n      x2: x - diffX,\n      y2: y + diffY\n    }, style));\n    select(this).maybeAppend('icon-right-line', 'line').attr('className', 'icon-right-line').styles(__assign({\n      x1: x + diffX,\n      y1: y - diffY,\n      x2: x + diffX,\n      y2: y + diffY\n    }, style));\n  };\n  ChartModeHandle.prototype.renderBorder = function () {\n    var _a = this.attributes,\n      xx = _a.x,\n      y = _a.y,\n      width = _a.width,\n      height = _a.height,\n      type = _a.type;\n    var style = subStyleProps(this.attributes, 'border');\n    var x = type === 'start' ? +width / 2 : -width / 2;\n    select(this).maybeAppend('border', 'line').attr('className', 'border').styles(__assign({\n      x1: x + xx,\n      y1: y - height / 2,\n      x2: x + xx,\n      y2: y + height / 2\n    }, style));\n  };\n  ChartModeHandle.prototype.render = function () {\n    this.renderBackground();\n    this.renderIcon();\n    this.renderBorder();\n  };\n  ChartModeHandle.defaultOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      width: 10,\n      height: 50,\n      iconSize: 10,\n      type: 'start',\n      backgroundFill: '#fff',\n      backgroundFillOpacity: 0.5,\n      iconStroke: '#9a9a9a',\n      iconLineWidth: 1,\n      borderStroke: '#e8e8e8',\n      borderLineWidth: 1\n    }\n  };\n  return ChartModeHandle;\n}(Component);\nexport { ChartModeHandle };", "map": {"version": 3, "names": ["Component", "Circle", "select", "subStyleProps", "deepAssign", "TimeModeHandle", "_super", "__extends", "options", "_this", "call", "defaultOptions", "bindEvents", "prototype", "addEventListener", "attr", "Math", "ceil", "style", "r", "fill", "lineWidth", "stroke", "strokeOpacity", "cursor", "ChartModeHandle", "renderBackground", "_a", "attributes", "x", "y", "width", "height", "maybe<PERSON><PERSON>nd", "styles", "__assign", "renderIcon", "iconSize", "diffX", "diffY", "x1", "y1", "x2", "y2", "renderBorder", "xx", "type", "render", "backgroundFill", "backgroundFillOpacity", "iconStroke", "iconLineWidth", "borderStroke", "borderLineWidth"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/timebar/handle.ts"], "sourcesContent": ["import { ComponentOptions, Component } from '../../core';\nimport type { GroupStyleProps, LineStyleProps, RectStyleProps, CircleStyleProps } from '../../shapes';\nimport { Circle } from '../../shapes';\nimport { PrefixObject } from '../../types';\nimport { select, subStyleProps } from '../../util';\nimport { deepAssign } from '../../util/deep-assign';\nimport type { HandleType } from '../slider/handle';\n\ntype TimeModeHandleOptions = ComponentOptions<Partial<CircleStyleProps>>;\n\nexport class TimeModeHandle extends Circle {\n  static defaultOptions = {\n    style: {\n      r: 5,\n      fill: '#3f7cf7',\n      lineWidth: 0,\n      stroke: '#3f7cf7',\n      strokeOpacity: 0.5,\n      cursor: 'pointer',\n    },\n  };\n\n  constructor(options: TimeModeHandleOptions) {\n    super(deepAssign({}, TimeModeHandle.defaultOptions, options));\n    this.bindEvents();\n  }\n\n  bindEvents() {\n    this.addEventListener('mouseenter', () => {\n      this.attr('lineWidth', Math.ceil(+(this.style.r || 0) / 2));\n    });\n\n    this.addEventListener('mouseleave', () => {\n      this.attr('lineWidth', 0);\n    });\n  }\n}\n\ntype ChartModeHandleStyleProps = GroupStyleProps &\n  PrefixObject<Omit<RectStyleProps, 'x' | 'y' | 'width' | 'height'>, 'background'> &\n  PrefixObject<Omit<LineStyleProps, 'x1' | 'y1' | 'x2' | 'y2'>, 'icon'> &\n  PrefixObject<Omit<LineStyleProps, 'x1' | 'y1' | 'x2' | 'y2'>, 'border'> & {\n    x?: number;\n    y?: number;\n    width?: number;\n    height?: number;\n    /** 图标尺寸 */\n    iconSize?: number;\n    type: HandleType;\n  };\ntype ChartModeHandleOptions = ComponentOptions<ChartModeHandleStyleProps>;\nexport class ChartModeHandle extends Component<ChartModeHandleStyleProps> {\n  static defaultOptions: ChartModeHandleOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      width: 10,\n      height: 50,\n      iconSize: 10,\n      type: 'start',\n      backgroundFill: '#fff',\n      backgroundFillOpacity: 0.5,\n      iconStroke: '#9a9a9a',\n      iconLineWidth: 1,\n      borderStroke: '#e8e8e8',\n      borderLineWidth: 1,\n    },\n  };\n\n  private renderBackground() {\n    const { x, y, width, height } = this.attributes;\n    const style = subStyleProps(this.attributes, 'background');\n    select(this)\n      .maybeAppend('background', 'rect')\n      .attr('className', 'background')\n      .styles({ x: x - width / 2, y: y - height / 2, width, height, ...style });\n  }\n\n  private renderIcon() {\n    const { x, y, iconSize } = this.attributes;\n    const style = subStyleProps(this.attributes, 'icon');\n    const diffX = 1;\n    const diffY = iconSize / 2;\n    select(this)\n      .maybeAppend('icon-left-line', 'line')\n      .attr('className', 'icon-left-line')\n      .styles({ x1: x - diffX, y1: y - diffY, x2: x - diffX, y2: y + diffY, ...style });\n    select(this)\n      .maybeAppend('icon-right-line', 'line')\n      .attr('className', 'icon-right-line')\n      .styles({ x1: x + diffX, y1: y - diffY, x2: x + diffX, y2: y + diffY, ...style });\n  }\n\n  private renderBorder() {\n    const { x: xx, y, width, height, type } = this.attributes;\n    const style = subStyleProps(this.attributes, 'border');\n    const x = type === 'start' ? +width / 2 : -width / 2;\n    select(this)\n      .maybeAppend('border', 'line')\n      .attr('className', 'border')\n      .styles({\n        x1: x + xx,\n        y1: y - height / 2,\n        x2: x + xx,\n        y2: y + height / 2,\n        ...style,\n      });\n  }\n\n  render() {\n    this.renderBackground();\n    this.renderIcon();\n    this.renderBorder();\n  }\n\n  constructor(options: ChartModeHandleOptions) {\n    super(deepAssign({}, ChartModeHandle.defaultOptions, options));\n  }\n}\n"], "mappings": ";AAAA,SAA2BA,SAAS,QAAQ,YAAY;AAExD,SAASC,MAAM,QAAQ,cAAc;AAErC,SAASC,MAAM,EAAEC,aAAa,QAAQ,YAAY;AAClD,SAASC,UAAU,QAAQ,wBAAwB;AAKnD,IAAAC,cAAA,0BAAAC,MAAA;EAAoCC,SAAA,CAAAF,cAAA,EAAAC,MAAA;EAYlC,SAAAD,eAAYG,OAA8B;IACxC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACN,UAAU,CAAC,EAAE,EAAEC,cAAc,CAACM,cAAc,EAAEH,OAAO,CAAC,CAAC;IAC7DC,KAAI,CAACG,UAAU,EAAE;;EACnB;EAEAP,cAAA,CAAAQ,SAAA,CAAAD,UAAU,GAAV;IAAA,IAAAH,KAAA;IACE,IAAI,CAACK,gBAAgB,CAAC,YAAY,EAAE;MAClCL,KAAI,CAACM,IAAI,CAAC,WAAW,EAAEC,IAAI,CAACC,IAAI,CAAC,EAAER,KAAI,CAACS,KAAK,CAACC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;IAEF,IAAI,CAACL,gBAAgB,CAAC,YAAY,EAAE;MAClCL,KAAI,CAACM,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC;EAxBMV,cAAA,CAAAM,cAAc,GAAG;IACtBO,KAAK,EAAE;MACLC,CAAC,EAAE,CAAC;MACJC,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,CAAC;MACZC,MAAM,EAAE,SAAS;MACjBC,aAAa,EAAE,GAAG;MAClBC,MAAM,EAAE;;GAEX;EAgBH,OAAAnB,cAAC;CAAA,CA1BmCJ,MAAM;SAA7BI,cAAc;AAyC3B,IAAAoB,eAAA,0BAAAnB,MAAA;EAAqCC,SAAA,CAAAkB,eAAA,EAAAnB,MAAA;EAgEnC,SAAAmB,gBAAYjB,OAA+B;IACzC,OAAAF,MAAK,CAAAI,IAAA,OAACN,UAAU,CAAC,EAAE,EAAEqB,eAAe,CAACd,cAAc,EAAEH,OAAO,CAAC,CAAC;EAChE;EAhDQiB,eAAA,CAAAZ,SAAA,CAAAa,gBAAgB,GAAxB;IACQ,IAAAC,EAAA,GAA0B,IAAI,CAACC,UAAU;MAAvCC,CAAC,GAAAF,EAAA,CAAAE,CAAA;MAAEC,CAAC,GAAAH,EAAA,CAAAG,CAAA;MAAEC,KAAK,GAAAJ,EAAA,CAAAI,KAAA;MAAEC,MAAM,GAAAL,EAAA,CAAAK,MAAoB;IAC/C,IAAMd,KAAK,GAAGf,aAAa,CAAC,IAAI,CAACyB,UAAU,EAAE,YAAY,CAAC;IAC1D1B,MAAM,CAAC,IAAI,CAAC,CACT+B,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,CACjClB,IAAI,CAAC,WAAW,EAAE,YAAY,CAAC,CAC/BmB,MAAM,CAAAC,QAAA;MAAGN,CAAC,EAAEA,CAAC,GAAGE,KAAK,GAAG,CAAC;MAAED,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC;MAAED,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,GAAKd,KAAK,EAAG;EAC7E,CAAC;EAEOO,eAAA,CAAAZ,SAAA,CAAAuB,UAAU,GAAlB;IACQ,IAAAT,EAAA,GAAqB,IAAI,CAACC,UAAU;MAAlCC,CAAC,GAAAF,EAAA,CAAAE,CAAA;MAAEC,CAAC,GAAAH,EAAA,CAAAG,CAAA;MAAEO,QAAQ,GAAAV,EAAA,CAAAU,QAAoB;IAC1C,IAAMnB,KAAK,GAAGf,aAAa,CAAC,IAAI,CAACyB,UAAU,EAAE,MAAM,CAAC;IACpD,IAAMU,KAAK,GAAG,CAAC;IACf,IAAMC,KAAK,GAAGF,QAAQ,GAAG,CAAC;IAC1BnC,MAAM,CAAC,IAAI,CAAC,CACT+B,WAAW,CAAC,gBAAgB,EAAE,MAAM,CAAC,CACrClB,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,CACnCmB,MAAM,CAAAC,QAAA;MAAGK,EAAE,EAAEX,CAAC,GAAGS,KAAK;MAAEG,EAAE,EAAEX,CAAC,GAAGS,KAAK;MAAEG,EAAE,EAAEb,CAAC,GAAGS,KAAK;MAAEK,EAAE,EAAEb,CAAC,GAAGS;IAAK,GAAKrB,KAAK,EAAG;IACnFhB,MAAM,CAAC,IAAI,CAAC,CACT+B,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,CACtClB,IAAI,CAAC,WAAW,EAAE,iBAAiB,CAAC,CACpCmB,MAAM,CAAAC,QAAA;MAAGK,EAAE,EAAEX,CAAC,GAAGS,KAAK;MAAEG,EAAE,EAAEX,CAAC,GAAGS,KAAK;MAAEG,EAAE,EAAEb,CAAC,GAAGS,KAAK;MAAEK,EAAE,EAAEb,CAAC,GAAGS;IAAK,GAAKrB,KAAK,EAAG;EACrF,CAAC;EAEOO,eAAA,CAAAZ,SAAA,CAAA+B,YAAY,GAApB;IACQ,IAAAjB,EAAA,GAAoC,IAAI,CAACC,UAAU;MAA9CiB,EAAE,GAAAlB,EAAA,CAAAE,CAAA;MAAEC,CAAC,GAAAH,EAAA,CAAAG,CAAA;MAAEC,KAAK,GAAAJ,EAAA,CAAAI,KAAA;MAAEC,MAAM,GAAAL,EAAA,CAAAK,MAAA;MAAEc,IAAI,GAAAnB,EAAA,CAAAmB,IAAoB;IACzD,IAAM5B,KAAK,GAAGf,aAAa,CAAC,IAAI,CAACyB,UAAU,EAAE,QAAQ,CAAC;IACtD,IAAMC,CAAC,GAAGiB,IAAI,KAAK,OAAO,GAAG,CAACf,KAAK,GAAG,CAAC,GAAG,CAACA,KAAK,GAAG,CAAC;IACpD7B,MAAM,CAAC,IAAI,CAAC,CACT+B,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAC7BlB,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAC3BmB,MAAM,CAAAC,QAAA;MACLK,EAAE,EAAEX,CAAC,GAAGgB,EAAE;MACVJ,EAAE,EAAEX,CAAC,GAAGE,MAAM,GAAG,CAAC;MAClBU,EAAE,EAAEb,CAAC,GAAGgB,EAAE;MACVF,EAAE,EAAEb,CAAC,GAAGE,MAAM,GAAG;IAAC,GACfd,KAAK,EACR;EACN,CAAC;EAEDO,eAAA,CAAAZ,SAAA,CAAAkC,MAAM,GAAN;IACE,IAAI,CAACrB,gBAAgB,EAAE;IACvB,IAAI,CAACU,UAAU,EAAE;IACjB,IAAI,CAACQ,YAAY,EAAE;EACrB,CAAC;EA7DMnB,eAAA,CAAAd,cAAc,GAA2B;IAC9CO,KAAK,EAAE;MACLW,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE,EAAE;MACVK,QAAQ,EAAE,EAAE;MACZS,IAAI,EAAE,OAAO;MACbE,cAAc,EAAE,MAAM;MACtBC,qBAAqB,EAAE,GAAG;MAC1BC,UAAU,EAAE,SAAS;MACrBC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,SAAS;MACvBC,eAAe,EAAE;;GAEpB;EAmDH,OAAA5B,eAAC;CAAA,CAnEoCzB,SAAS;SAAjCyB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}