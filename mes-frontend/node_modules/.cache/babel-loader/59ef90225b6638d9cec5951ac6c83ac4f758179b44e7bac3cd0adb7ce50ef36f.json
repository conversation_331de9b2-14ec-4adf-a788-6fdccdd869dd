{"ast": null, "code": "import { intersectionArea } from './circleintersection';\n/**\n * 根据圆心(x, y) 半径 r 返回圆的绘制 path\n * @param x 圆心点 x\n * @param y 圆心点 y\n * @param r 圆的半径\n * @returns 圆的 path\n */\nfunction circlePath(x, y, r) {\n  const ret = [];\n  // ret.push('\\nM', x, y);\n  // ret.push('\\nm', -r, 0);\n  // ret.push('\\na', r, r, 0, 1, 0, r * 2, 0);\n  // ret.push('\\na', r, r, 0, 1, 0, -r * 2, 0);\n  const x0 = x - r;\n  const y0 = y;\n  ret.push('M', x0, y0);\n  ret.push('A', r, r, 0, 1, 0, x0 + 2 * r, y0);\n  ret.push('A', r, r, 0, 1, 0, x0, y0);\n  return ret.join(' ');\n}\n/** returns a svg path of the intersection area of a bunch of circles */\nexport function intersectionAreaPath(circles) {\n  const stats = {};\n  intersectionArea(circles, stats);\n  const arcs = stats.arcs;\n  if (arcs.length === 0) {\n    return 'M 0 0';\n  } else if (arcs.length == 1) {\n    const circle = arcs[0].circle;\n    return circlePath(circle.x, circle.y, circle.radius);\n  } else {\n    // draw path around arcs\n    const ret = ['\\nM', arcs[0].p2.x, arcs[0].p2.y];\n    for (let i = 0; i < arcs.length; ++i) {\n      const arc = arcs[i],\n        r = arc.circle.radius,\n        wide = arc.width > r;\n      ret.push('\\nA', r, r, 0, wide ? 1 : 0, 1, arc.p1.x, arc.p1.y);\n    }\n    return ret.join(' ');\n  }\n}", "map": {"version": 3, "names": ["intersectionArea", "circlePath", "x", "y", "r", "ret", "x0", "y0", "push", "join", "intersectionAreaPath", "circles", "stats", "arcs", "length", "circle", "radius", "p2", "i", "arc", "wide", "width", "p1"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/venn/diagram.ts"], "sourcesContent": ["import { intersectionArea } from './circleintersection';\n\n/**\n * 根据圆心(x, y) 半径 r 返回圆的绘制 path\n * @param x 圆心点 x\n * @param y 圆心点 y\n * @param r 圆的半径\n * @returns 圆的 path\n */\nfunction circlePath(x, y, r) {\n  const ret = [];\n  // ret.push('\\nM', x, y);\n  // ret.push('\\nm', -r, 0);\n  // ret.push('\\na', r, r, 0, 1, 0, r * 2, 0);\n  // ret.push('\\na', r, r, 0, 1, 0, -r * 2, 0);\n  const x0 = x - r;\n  const y0 = y;\n  ret.push('M', x0, y0);\n  ret.push('A', r, r, 0, 1, 0, x0 + 2 * r, y0);\n  ret.push('A', r, r, 0, 1, 0, x0, y0);\n\n  return ret.join(' ');\n}\n\n/** returns a svg path of the intersection area of a bunch of circles */\nexport function intersectionAreaPath(circles) {\n  const stats: any = {};\n  intersectionArea(circles, stats);\n  const arcs = stats.arcs;\n\n  if (arcs.length === 0) {\n    return 'M 0 0';\n  } else if (arcs.length == 1) {\n    const circle = arcs[0].circle;\n    return circlePath(circle.x, circle.y, circle.radius);\n  } else {\n    // draw path around arcs\n    const ret = ['\\nM', arcs[0].p2.x, arcs[0].p2.y];\n    for (let i = 0; i < arcs.length; ++i) {\n      const arc = arcs[i],\n        r = arc.circle.radius,\n        wide = arc.width > r;\n      ret.push('\\nA', r, r, 0, wide ? 1 : 0, 1, arc.p1.x, arc.p1.y);\n    }\n    return ret.join(' ');\n  }\n}\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,sBAAsB;AAEvD;;;;;;;AAOA,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC;EACzB,MAAMC,GAAG,GAAG,EAAE;EACd;EACA;EACA;EACA;EACA,MAAMC,EAAE,GAAGJ,CAAC,GAAGE,CAAC;EAChB,MAAMG,EAAE,GAAGJ,CAAC;EACZE,GAAG,CAACG,IAAI,CAAC,GAAG,EAAEF,EAAE,EAAEC,EAAE,CAAC;EACrBF,GAAG,CAACG,IAAI,CAAC,GAAG,EAAEJ,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEE,EAAE,GAAG,CAAC,GAAGF,CAAC,EAAEG,EAAE,CAAC;EAC5CF,GAAG,CAACG,IAAI,CAAC,GAAG,EAAEJ,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEE,EAAE,EAAEC,EAAE,CAAC;EAEpC,OAAOF,GAAG,CAACI,IAAI,CAAC,GAAG,CAAC;AACtB;AAEA;AACA,OAAM,SAAUC,oBAAoBA,CAACC,OAAO;EAC1C,MAAMC,KAAK,GAAQ,EAAE;EACrBZ,gBAAgB,CAACW,OAAO,EAAEC,KAAK,CAAC;EAChC,MAAMC,IAAI,GAAGD,KAAK,CAACC,IAAI;EAEvB,IAAIA,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;IACrB,OAAO,OAAO;GACf,MAAM,IAAID,IAAI,CAACC,MAAM,IAAI,CAAC,EAAE;IAC3B,MAAMC,MAAM,GAAGF,IAAI,CAAC,CAAC,CAAC,CAACE,MAAM;IAC7B,OAAOd,UAAU,CAACc,MAAM,CAACb,CAAC,EAAEa,MAAM,CAACZ,CAAC,EAAEY,MAAM,CAACC,MAAM,CAAC;GACrD,MAAM;IACL;IACA,MAAMX,GAAG,GAAG,CAAC,KAAK,EAAEQ,IAAI,CAAC,CAAC,CAAC,CAACI,EAAE,CAACf,CAAC,EAAEW,IAAI,CAAC,CAAC,CAAC,CAACI,EAAE,CAACd,CAAC,CAAC;IAC/C,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,IAAI,CAACC,MAAM,EAAE,EAAEI,CAAC,EAAE;MACpC,MAAMC,GAAG,GAAGN,IAAI,CAACK,CAAC,CAAC;QACjBd,CAAC,GAAGe,GAAG,CAACJ,MAAM,CAACC,MAAM;QACrBI,IAAI,GAAGD,GAAG,CAACE,KAAK,GAAGjB,CAAC;MACtBC,GAAG,CAACG,IAAI,CAAC,KAAK,EAAEJ,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAEgB,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAED,GAAG,CAACG,EAAE,CAACpB,CAAC,EAAEiB,GAAG,CAACG,EAAE,CAACnB,CAAC,CAAC;;IAE/D,OAAOE,GAAG,CAACI,IAAI,CAAC,GAAG,CAAC;;AAExB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}