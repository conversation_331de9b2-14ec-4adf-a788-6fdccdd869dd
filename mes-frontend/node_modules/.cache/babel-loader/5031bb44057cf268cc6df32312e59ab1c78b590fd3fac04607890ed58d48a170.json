{"ast": null, "code": "/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nexport const initOrder = g => {\n  const visited = {};\n  // const simpleNodes = g.getAllNodes().filter((v) => {\n  //   return !g.getChildren(v.id)?.length;\n  // });\n  const simpleNodes = g.getAllNodes();\n  const nodeRanks = simpleNodes.map(v => {\n    var _a;\n    return (_a = v.data.rank) !== null && _a !== void 0 ? _a : -Infinity;\n  });\n  const maxRank = Math.max(...nodeRanks);\n  const layers = [];\n  for (let i = 0; i < maxRank + 1; i++) {\n    layers.push([]);\n  }\n  const orderedVs = simpleNodes.sort((a, b) => g.getNode(a.id).data.rank - g.getNode(b.id).data.rank);\n  // const orderedVs = _.sortBy(simpleNodes, function(v) { return g.node(v)!.rank; });\n  // 有fixOrder的，直接排序好放进去\n  const beforeSort = orderedVs.filter(n => {\n    return g.getNode(n.id).data.fixorder !== undefined;\n  });\n  const fixOrderNodes = beforeSort.sort((a, b) => g.getNode(a.id).data.fixorder - g.getNode(b.id).data.fixorder);\n  fixOrderNodes === null || fixOrderNodes === void 0 ? void 0 : fixOrderNodes.forEach(n => {\n    if (!isNaN(g.getNode(n.id).data.rank)) {\n      layers[g.getNode(n.id).data.rank].push(n.id);\n    }\n    visited[n.id] = true;\n  });\n  orderedVs === null || orderedVs === void 0 ? void 0 : orderedVs.forEach(n => g.dfsTree(n.id, node => {\n    if (visited.hasOwnProperty(node.id)) return true;\n    visited[node.id] = true;\n    if (!isNaN(node.data.rank)) {\n      layers[node.data.rank].push(node.id);\n    }\n  }));\n  return layers;\n};", "map": {"version": 3, "names": ["initOrder", "g", "visited", "simpleNodes", "getAllNodes", "nodeRanks", "map", "v", "_a", "data", "rank", "Infinity", "maxRank", "Math", "max", "layers", "i", "push", "orderedVs", "sort", "a", "b", "getNode", "id", "beforeSort", "filter", "n", "fixorder", "undefined", "fixOrderNodes", "for<PERSON>ach", "isNaN", "dfsTree", "node", "hasOwnProperty"], "sources": ["../../../src/antv-dagre/order/init-order.ts"], "sourcesContent": [null], "mappings": "AAGA;;;;;;;;;;;AAWA,OAAO,MAAMA,SAAS,GAAIC,CAAQ,IAAI;EACpC,MAAMC,OAAO,GAA4B,EAAE;EAC3C;EACA;EACA;EACA,MAAMC,WAAW,GAAGF,CAAC,CAACG,WAAW,EAAE;EACnC,MAAMC,SAAS,GAAGF,WAAW,CAACG,GAAG,CAAEC,CAAC,IAAI;IAAA,IAAAC,EAAA;IAAC,QAAAA,EAAA,GAAAD,CAAC,CAACE,IAAI,CAACC,IAAK,cAAAF,EAAA,cAAAA,EAAA,GAAI,CAACG,QAAQ;EAAA,EAAC;EAEnE,MAAMC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGT,SAAS,CAAC;EACtC,MAAMU,MAAM,GAAW,EAAE;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,GAAG,CAAC,EAAEI,CAAC,EAAE,EAAE;IACpCD,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC;;EAGjB,MAAMC,SAAS,GAAGf,WAAW,CAACgB,IAAI,CAChC,CAACC,CAAC,EAAEC,CAAC,KAAKpB,CAAC,CAACqB,OAAO,CAACF,CAAC,CAACG,EAAE,CAAC,CAACd,IAAI,CAACC,IAAK,GAAGT,CAAC,CAACqB,OAAO,CAACD,CAAC,CAACE,EAAE,CAAC,CAACd,IAAI,CAACC,IAAK,CAClE;EACD;EAEA;EACA,MAAMc,UAAU,GAAGN,SAAS,CAACO,MAAM,CAAEC,CAAC,IAAI;IACxC,OAAOzB,CAAC,CAACqB,OAAO,CAACI,CAAC,CAACH,EAAE,CAAC,CAACd,IAAI,CAACkB,QAAQ,KAAKC,SAAS;EACpD,CAAC,CAAC;EACF,MAAMC,aAAa,GAAGL,UAAU,CAACL,IAAI,CACnC,CAACC,CAAC,EAAEC,CAAC,KAAKpB,CAAC,CAACqB,OAAO,CAACF,CAAC,CAACG,EAAE,CAAC,CAACd,IAAI,CAACkB,QAAS,GAAG1B,CAAC,CAACqB,OAAO,CAACD,CAAC,CAACE,EAAE,CAAC,CAACd,IAAI,CAACkB,QAAS,CAC1E;EACDE,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEC,OAAO,CAAEJ,CAAC,IAAI;IAC3B,IAAI,CAACK,KAAK,CAAC9B,CAAC,CAACqB,OAAO,CAACI,CAAC,CAACH,EAAE,CAAC,CAACd,IAAI,CAACC,IAAK,CAAC,EAAE;MACtCK,MAAM,CAACd,CAAC,CAACqB,OAAO,CAACI,CAAC,CAACH,EAAE,CAAC,CAACd,IAAI,CAACC,IAAK,CAAC,CAACO,IAAI,CAACS,CAAC,CAACH,EAAE,CAAC;;IAE/CrB,OAAO,CAACwB,CAAC,CAACH,EAAE,CAAC,GAAG,IAAI;EACtB,CAAC,CAAC;EAEFL,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEY,OAAO,CAAEJ,CAAC,IACnBzB,CAAC,CAAC+B,OAAO,CAACN,CAAC,CAACH,EAAE,EAAGU,IAAI,IAAI;IACvB,IAAI/B,OAAO,CAACgC,cAAc,CAACD,IAAI,CAACV,EAAE,CAAC,EAAE,OAAO,IAAI;IAChDrB,OAAO,CAAC+B,IAAI,CAACV,EAAE,CAAC,GAAG,IAAI;IACvB,IAAI,CAACQ,KAAK,CAACE,IAAI,CAACxB,IAAI,CAACC,IAAK,CAAC,EAAE;MAC3BK,MAAM,CAACkB,IAAI,CAACxB,IAAI,CAACC,IAAK,CAAC,CAACO,IAAI,CAACgB,IAAI,CAACV,EAAE,CAAC;;EAEzC,CAAC,CAAC,CACH;EAED,OAAOR,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}