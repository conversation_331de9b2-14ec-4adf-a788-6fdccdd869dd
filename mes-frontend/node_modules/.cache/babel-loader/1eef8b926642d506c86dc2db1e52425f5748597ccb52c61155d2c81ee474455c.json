{"ast": null, "code": "import { partition } from '../util';\nexport const sort = (entries, biasRight, usePrev, keepNodeOrder) => {\n  const parts = partition(entries, entry => {\n    const hasFixOrder = entry.hasOwnProperty('fixorder') && !isNaN(entry.fixorder);\n    if (keepNodeOrder) {\n      return !hasFixOrder && entry.hasOwnProperty('barycenter');\n    }\n    // NOTE: 有fixorder的也可以排\n    return hasFixOrder || entry.hasOwnProperty('barycenter');\n  });\n  const sortable = parts.lhs;\n  const unsortable = parts.rhs.sort((a, b) => -a.i - -b.i);\n  const vs = [];\n  let sum = 0;\n  let weight = 0;\n  let vsIndex = 0;\n  sortable === null || sortable === void 0 ? void 0 : sortable.sort(compareWithBias(!!biasRight, !!usePrev));\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  sortable === null || sortable === void 0 ? void 0 : sortable.forEach(entry => {\n    var _a;\n    vsIndex += (_a = entry.vs) === null || _a === void 0 ? void 0 : _a.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n  const result = {\n    vs: vs.flat()\n  };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n};\nconst consumeUnsortable = (vs, unsortable, index) => {\n  let iindex = index;\n  let last;\n  while (unsortable.length && (last = unsortable[unsortable.length - 1]).i <= iindex) {\n    unsortable.pop();\n    vs === null || vs === void 0 ? void 0 : vs.push(last.vs);\n    iindex++;\n  }\n  return iindex;\n};\n/**\n * 配置是否考虑使用之前的布局结果\n */\nconst compareWithBias = (bias, usePrev) => {\n  return (entryV, entryW) => {\n    // 排序的时候先判断fixorder，不行再判断重心\n    if (entryV.fixorder !== undefined && entryW.fixorder !== undefined) {\n      return entryV.fixorder - entryW.fixorder;\n    }\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    }\n    if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n    // 重心相同，考虑之前排好的顺序\n    if (usePrev && entryV.order !== undefined && entryW.order !== undefined) {\n      if (entryV.order < entryW.order) {\n        return -1;\n      }\n      if (entryV.order > entryW.order) {\n        return 1;\n      }\n    }\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n};", "map": {"version": 3, "names": ["partition", "sort", "entries", "biasRight", "usePrev", "keepNodeOrder", "parts", "entry", "hasFixOrder", "hasOwnProperty", "isNaN", "fixorder", "sortable", "lhs", "unsortable", "rhs", "a", "b", "i", "vs", "sum", "weight", "vsIndex", "compareWithBias", "consumeUnsortable", "for<PERSON>ach", "_a", "length", "push", "barycenter", "result", "flat", "index", "iindex", "last", "pop", "bias", "entryV", "entryW", "undefined", "order"], "sources": ["../../../src/antv-dagre/order/sort.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,SAAS,QAAQ,SAAS;AAGnC,OAAO,MAAMC,IAAI,GAAGA,CAClBC,OAAwB,EACxBC,SAAmB,EACnBC,OAAiB,EACjBC,aAAuB,KACrB;EACF,MAAMC,KAAK,GAAGN,SAAS,CAACE,OAAO,EAAGK,KAAK,IAAI;IACzC,MAAMC,WAAW,GACfD,KAAK,CAACE,cAAc,CAAC,UAAU,CAAC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACI,QAAS,CAAC;IAC7D,IAAIN,aAAa,EAAE;MACjB,OAAO,CAACG,WAAW,IAAID,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;;IAE3D;IACA,OAAOD,WAAW,IAAID,KAAK,CAACE,cAAc,CAAC,YAAY,CAAC;EAC1D,CAAC,CAAC;EACF,MAAMG,QAAQ,GAAGN,KAAK,CAACO,GAAG;EAC1B,MAAMC,UAAU,GAAGR,KAAK,CAACS,GAAG,CAACd,IAAI,CAAC,CAACe,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,CAACE,CAAC,GAAG,CAACD,CAAC,CAACC,CAAC,CAAC;EACxD,MAAMC,EAAE,GAAW,EAAE;EACrB,IAAIC,GAAG,GAAG,CAAC;EACX,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,OAAO,GAAG,CAAC;EAEfV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEX,IAAI,CAACsB,eAAe,CAAC,CAAC,CAACpB,SAAS,EAAE,CAAC,CAACC,OAAO,CAAC,CAAC;EAEvDkB,OAAO,GAAGE,iBAAiB,CAACL,EAAE,EAAEL,UAAU,EAAEQ,OAAO,CAAC;EAEpDV,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEa,OAAO,CAAElB,KAAK,IAAI;;IAC1Be,OAAO,IAAI,CAAAI,EAAA,GAAAnB,KAAK,CAACY,EAAE,cAAAO,EAAA,uBAAAA,EAAA,CAAEC,MAAM;IAC3BR,EAAE,CAACS,IAAI,CAACrB,KAAK,CAACY,EAAE,CAAC;IACjBC,GAAG,IAAIb,KAAK,CAACsB,UAAW,GAAGtB,KAAK,CAACc,MAAO;IACxCA,MAAM,IAAId,KAAK,CAACc,MAAO;IACvBC,OAAO,GAAGE,iBAAiB,CAACL,EAAE,EAAEL,UAAU,EAAEQ,OAAO,CAAC;EACtD,CAAC,CAAC;EAEF,MAAMQ,MAAM,GAAuD;IACjEX,EAAE,EAAEA,EAAE,CAACY,IAAI;GACZ;EACD,IAAIV,MAAM,EAAE;IACVS,MAAM,CAACD,UAAU,GAAGT,GAAG,GAAGC,MAAM;IAChCS,MAAM,CAACT,MAAM,GAAGA,MAAM;;EAExB,OAAOS,MAAM;AACf,CAAC;AAED,MAAMN,iBAAiB,GAAGA,CACxBL,EAAU,EACVL,UAA2B,EAC3BkB,KAAa,KACX;EACF,IAAIC,MAAM,GAAGD,KAAK;EAClB,IAAIE,IAAI;EACR,OACEpB,UAAU,CAACa,MAAM,IACjB,CAACO,IAAI,GAAGpB,UAAU,CAACA,UAAU,CAACa,MAAM,GAAG,CAAC,CAAC,EAAET,CAAC,IAAIe,MAAM,EACtD;IACAnB,UAAU,CAACqB,GAAG,EAAE;IAChBhB,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAES,IAAI,CAACM,IAAI,CAACf,EAAE,CAAC;IACjBc,MAAM,EAAE;;EAEV,OAAOA,MAAM;AACf,CAAC;AAED;;;AAGA,MAAMV,eAAe,GAAGA,CAACa,IAAa,EAAEhC,OAAgB,KAAI;EAC1D,OAAO,CAACiC,MAAqB,EAAEC,MAAqB,KAAI;IACtD;IACA,IAAID,MAAM,CAAC1B,QAAQ,KAAK4B,SAAS,IAAID,MAAM,CAAC3B,QAAQ,KAAK4B,SAAS,EAAE;MAClE,OAAOF,MAAM,CAAC1B,QAAQ,GAAG2B,MAAM,CAAC3B,QAAQ;;IAE1C,IAAI0B,MAAM,CAACR,UAAW,GAAGS,MAAM,CAACT,UAAW,EAAE;MAC3C,OAAO,CAAC,CAAC;;IAEX,IAAIQ,MAAM,CAACR,UAAW,GAAGS,MAAM,CAACT,UAAW,EAAE;MAC3C,OAAO,CAAC;;IAEV;IACA,IAAIzB,OAAO,IAAIiC,MAAM,CAACG,KAAK,KAAKD,SAAS,IAAID,MAAM,CAACE,KAAK,KAAKD,SAAS,EAAE;MACvE,IAAIF,MAAM,CAACG,KAAK,GAAGF,MAAM,CAACE,KAAK,EAAE;QAC/B,OAAO,CAAC,CAAC;;MAEX,IAAIH,MAAM,CAACG,KAAK,GAAGF,MAAM,CAACE,KAAK,EAAE;QAC/B,OAAO,CAAC;;;IAIZ,OAAO,CAACJ,IAAI,GAAGC,MAAM,CAACnB,CAAC,GAAGoB,MAAM,CAACpB,CAAC,GAAGoB,MAAM,CAACpB,CAAC,GAAGmB,MAAM,CAACnB,CAAC;EAC1D,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}