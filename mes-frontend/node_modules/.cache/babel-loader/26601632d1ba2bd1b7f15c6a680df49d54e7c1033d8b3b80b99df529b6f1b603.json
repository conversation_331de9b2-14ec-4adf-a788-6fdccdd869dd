{"ast": null, "code": "import isNil from './is-nil';\nimport isArrayLike from './is-array-like';\nimport getType from './get-type';\nimport isPrototype from './is-prototype';\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction isEmpty(value) {\n  /**\n   * isEmpty(null) => true\n   * isEmpty() => true\n   * isEmpty(true) => true\n   * isEmpty(1) => true\n   * isEmpty([1, 2, 3]) => false\n   * isEmpty('abc') => false\n   * isEmpty({ a: 1 }) => false\n   */\n  if (isNil(value)) {\n    return true;\n  }\n  if (isArrayLike(value)) {\n    return !value.length;\n  }\n  var type = getType(value);\n  if (type === 'Map' || type === 'Set') {\n    return !value.size;\n  }\n  if (isPrototype(value)) {\n    return !Object.keys(value).length;\n  }\n  for (var key in value) {\n    if (hasOwnProperty.call(value, key)) {\n      return false;\n    }\n  }\n  return true;\n}\nexport default isEmpty;", "map": {"version": 3, "names": ["isNil", "isArrayLike", "getType", "isPrototype", "hasOwnProperty", "Object", "prototype", "isEmpty", "value", "length", "type", "size", "keys", "key", "call"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-empty.ts"], "sourcesContent": ["import isNil from './is-nil';\nimport isArrayLike from './is-array-like';\nimport getType from './get-type';\nimport isPrototype from './is-prototype';\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction isEmpty(value: any): boolean {\n  /**\n   * isEmpty(null) => true\n   * isEmpty() => true\n   * isEmpty(true) => true\n   * isEmpty(1) => true\n   * isEmpty([1, 2, 3]) => false\n   * isEmpty('abc') => false\n   * isEmpty({ a: 1 }) => false\n   */\n  if (isNil(value)) {\n    return true;\n  }\n  if (isArrayLike(value)) {\n    return !value.length;\n  }\n  const type = getType(value);\n  if (type === 'Map' || type === 'Set') {\n    return !value.size;\n  }\n  if (isPrototype(value)) {\n    return !Object.keys(value).length;\n  }\n  for (const key in value) {\n    if (hasOwnProperty.call(value, key)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default isEmpty;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,UAAU;AAC5B,OAAOC,WAAW,MAAM,iBAAiB;AACzC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,WAAW,MAAM,gBAAgB;AAExC,IAAMC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AAEtD,SAASG,OAAOA,CAACC,KAAU;EACzB;;;;;;;;;EASA,IAAIR,KAAK,CAACQ,KAAK,CAAC,EAAE;IAChB,OAAO,IAAI;;EAEb,IAAIP,WAAW,CAACO,KAAK,CAAC,EAAE;IACtB,OAAO,CAACA,KAAK,CAACC,MAAM;;EAEtB,IAAMC,IAAI,GAAGR,OAAO,CAACM,KAAK,CAAC;EAC3B,IAAIE,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,KAAK,EAAE;IACpC,OAAO,CAACF,KAAK,CAACG,IAAI;;EAEpB,IAAIR,WAAW,CAACK,KAAK,CAAC,EAAE;IACtB,OAAO,CAACH,MAAM,CAACO,IAAI,CAACJ,KAAK,CAAC,CAACC,MAAM;;EAEnC,KAAK,IAAMI,GAAG,IAAIL,KAAK,EAAE;IACvB,IAAIJ,cAAc,CAACU,IAAI,CAACN,KAAK,EAAEK,GAAG,CAAC,EAAE;MACnC,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEA,eAAeN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}