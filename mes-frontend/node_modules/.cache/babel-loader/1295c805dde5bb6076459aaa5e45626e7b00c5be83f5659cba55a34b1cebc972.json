{"ast": null, "code": "import { deepAssign } from '../../util';\nexport var CROSSHAIR_BASE_DEFAULT_STYLE = {\n  tagText: '',\n  lineStroke: '#416180',\n  lineStrokeOpacity: 0.45,\n  lineLineWidth: 1,\n  lineLineDash: [5, 5]\n};\nexport var LINE_CROSSHAIR_DEFAULT_STYLE = deepAssign({}, CROSSHAIR_BASE_DEFAULT_STYLE, {\n  type: 'line',\n  tagPosition: 'start',\n  tagAlign: 'center',\n  tagVerticalAlign: 'bottom'\n});\nexport var CIRCLE_CROSSHAIR_DEFAULT_STYLE = deepAssign({}, CROSSHAIR_BASE_DEFAULT_STYLE, {\n  type: 'circle',\n  defaultRadius: 0\n});\nexport var POLYGON_CROSSHAIR_DEFAULT_STYLE = deepAssign({}, CROSSHAIR_BASE_DEFAULT_STYLE, {\n  type: 'polygon',\n  defaultRadius: 0,\n  startAngle: 0\n});", "map": {"version": 3, "names": ["deepAssign", "CROSSHAIR_BASE_DEFAULT_STYLE", "tagText", "lineStroke", "lineStrokeOpacity", "lineLineWidth", "lineLineDash", "LINE_CROSSHAIR_DEFAULT_STYLE", "type", "tagPosition", "tagAlign", "tagVerticalAlign", "CIRCLE_CROSSHAIR_DEFAULT_STYLE", "defaultRadius", "POLYGON_CROSSHAIR_DEFAULT_STYLE", "startAngle"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/crosshair/constant.ts"], "sourcesContent": ["import { deepAssign } from '../../util';\nimport type {\n  CrosshairBaseStyleProps,\n  LineCrosshairStyleProps,\n  CircleCrosshairStyleProps,\n  PolygonCrosshairStyleProps,\n} from './types';\n\nexport const CROSSHAIR_BASE_DEFAULT_STYLE: Partial<CrosshairBaseStyleProps> = {\n  tagText: '',\n  lineStroke: '#416180',\n  lineStrokeOpacity: 0.45,\n  lineLineWidth: 1,\n  lineLineDash: [5, 5],\n};\n\nexport const LINE_CROSSHAIR_DEFAULT_STYLE: Partial<LineCrosshairStyleProps> = deepAssign(\n  {},\n  CROSSHAIR_BASE_DEFAULT_STYLE,\n  {\n    type: 'line',\n    tagPosition: 'start',\n    tagAlign: 'center',\n    tagVerticalAlign: 'bottom',\n  }\n);\n\nexport const CIRCLE_CROSSHAIR_DEFAULT_STYLE: Partial<CircleCrosshairStyleProps> = deepAssign(\n  {},\n  CROSSHAIR_BASE_DEFAULT_STYLE,\n  {\n    type: 'circle',\n    defaultRadius: 0,\n  }\n);\n\nexport const POLYGON_CROSSHAIR_DEFAULT_STYLE: Partial<PolygonCrosshairStyleProps> = deepAssign(\n  {},\n  CROSSHAIR_BASE_DEFAULT_STYLE,\n  {\n    type: 'polygon',\n    defaultRadius: 0,\n    startAngle: 0,\n  }\n);\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,YAAY;AAQvC,OAAO,IAAMC,4BAA4B,GAAqC;EAC5EC,OAAO,EAAE,EAAE;EACXC,UAAU,EAAE,SAAS;EACrBC,iBAAiB,EAAE,IAAI;EACvBC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC;CACpB;AAED,OAAO,IAAMC,4BAA4B,GAAqCP,UAAU,CACtF,EAAE,EACFC,4BAA4B,EAC5B;EACEO,IAAI,EAAE,MAAM;EACZC,WAAW,EAAE,OAAO;EACpBC,QAAQ,EAAE,QAAQ;EAClBC,gBAAgB,EAAE;CACnB,CACF;AAED,OAAO,IAAMC,8BAA8B,GAAuCZ,UAAU,CAC1F,EAAE,EACFC,4BAA4B,EAC5B;EACEO,IAAI,EAAE,QAAQ;EACdK,aAAa,EAAE;CAChB,CACF;AAED,OAAO,IAAMC,+BAA+B,GAAwCd,UAAU,CAC5F,EAAE,EACFC,4BAA4B,EAC5B;EACEO,IAAI,EAAE,SAAS;EACfK,aAAa,EAAE,CAAC;EAChBE,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}