{"ast": null, "code": "import { maxIndex } from '@antv/vendor/d3-array';\nimport { sub, angle } from '../../../utils/vector';\n/**\n * Only for Area label.\n */\nexport function area(position, points, value, coordinate) {\n  const l = points.length / 2;\n  const Y1 = points.slice(0, l);\n  const Y0 = points.slice(l);\n  // Get the maximal space for label.\n  let idx = maxIndex(Y1, (p, i) => Math.abs(p[1] - Y0[i][1]));\n  // Do not show label at first and last.\n  idx = Math.max(Math.min(idx, l - 2), 1);\n  const mid = i => [Y1[i][0], (Y1[i][1] + Y0[i][1]) / 2];\n  const point = mid(idx);\n  const prev = mid(idx - 1);\n  const next = mid(idx + 1);\n  // todo: G rotate only support deg.\n  const rotate = angle(sub(next, prev)) / Math.PI * 180;\n  return {\n    x: point[0],\n    y: point[1],\n    transform: `rotate(${rotate})`,\n    textAlign: 'center',\n    textBaseline: 'middle'\n  };\n}", "map": {"version": 3, "names": ["maxIndex", "sub", "angle", "area", "position", "points", "value", "coordinate", "l", "length", "Y1", "slice", "Y0", "idx", "p", "i", "Math", "abs", "max", "min", "mid", "point", "prev", "next", "rotate", "PI", "x", "y", "transform", "textAlign", "textBaseline"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/area.ts"], "sourcesContent": ["import { maxIndex } from '@antv/vendor/d3-array';\nimport { Coordinate } from '@antv/coord';\nimport { Vector2 } from '../../../runtime';\nimport { sub, angle } from '../../../utils/vector';\nimport { LabelPosition } from './default';\n\n/**\n * Only for Area label.\n */\nexport function area(\n  position: LabelPosition,\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n) {\n  const l = points.length / 2;\n  const Y1 = points.slice(0, l);\n  const Y0 = points.slice(l);\n  // Get the maximal space for label.\n  let idx = maxIndex(Y1, (p, i) => Math.abs(p[1] - Y0[i][1]));\n  // Do not show label at first and last.\n  idx = Math.max(Math.min(idx, l - 2), 1);\n\n  const mid = (i: number): Vector2 => [Y1[i][0], (Y1[i][1] + Y0[i][1]) / 2];\n  const point = mid(idx);\n  const prev = mid(idx - 1);\n  const next = mid(idx + 1);\n\n  // todo: G rotate only support deg.\n  const rotate = (angle(sub(next, prev)) / Math.PI) * 180;\n\n  return {\n    x: point[0],\n    y: point[1],\n    transform: `rotate(${rotate})`,\n    textAlign: 'center',\n    textBaseline: 'middle',\n  };\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,uBAAuB;AAGhD,SAASC,GAAG,EAAEC,KAAK,QAAQ,uBAAuB;AAGlD;;;AAGA,OAAM,SAAUC,IAAIA,CAClBC,QAAuB,EACvBC,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB;EAEtB,MAAMC,CAAC,GAAGH,MAAM,CAACI,MAAM,GAAG,CAAC;EAC3B,MAAMC,EAAE,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEH,CAAC,CAAC;EAC7B,MAAMI,EAAE,GAAGP,MAAM,CAACM,KAAK,CAACH,CAAC,CAAC;EAC1B;EACA,IAAIK,GAAG,GAAGb,QAAQ,CAACU,EAAE,EAAE,CAACI,CAAC,EAAEC,CAAC,KAAKC,IAAI,CAACC,GAAG,CAACH,CAAC,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D;EACAF,GAAG,GAAGG,IAAI,CAACE,GAAG,CAACF,IAAI,CAACG,GAAG,CAACN,GAAG,EAAEL,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EAEvC,MAAMY,GAAG,GAAIL,CAAS,IAAc,CAACL,EAAE,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACL,EAAE,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGH,EAAE,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACzE,MAAMM,KAAK,GAAGD,GAAG,CAACP,GAAG,CAAC;EACtB,MAAMS,IAAI,GAAGF,GAAG,CAACP,GAAG,GAAG,CAAC,CAAC;EACzB,MAAMU,IAAI,GAAGH,GAAG,CAACP,GAAG,GAAG,CAAC,CAAC;EAEzB;EACA,MAAMW,MAAM,GAAItB,KAAK,CAACD,GAAG,CAACsB,IAAI,EAAED,IAAI,CAAC,CAAC,GAAGN,IAAI,CAACS,EAAE,GAAI,GAAG;EAEvD,OAAO;IACLC,CAAC,EAAEL,KAAK,CAAC,CAAC,CAAC;IACXM,CAAC,EAAEN,KAAK,CAAC,CAAC,CAAC;IACXO,SAAS,EAAE,UAAUJ,MAAM,GAAG;IAC9BK,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE;GACf;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}