{"ast": null, "code": "import { shuffle } from \"../array.js\";\nimport lcg from \"../lcg.js\";\nexport default function (circles) {\n  return packEncloseRandom(circles, lcg());\n}\nexport function packEncloseRandom(circles, random) {\n  var i = 0,\n    n = (circles = shuffle(Array.from(circles), random)).length,\n    B = [],\n    p,\n    e;\n  while (i < n) {\n    p = circles[i];\n    if (e && enclosesWeak(e, p)) ++i;else e = encloseBasis(B = extendBasis(B, p)), i = 0;\n  }\n  return e;\n}\nfunction extendBasis(B, p) {\n  var i, j;\n  if (enclosesWeakAll(p, B)) return [p];\n\n  // If we get here then B must have at least one element.\n  for (i = 0; i < B.length; ++i) {\n    if (enclosesNot(p, B[i]) && enclosesWeakAll(encloseBasis2(B[i], p), B)) {\n      return [B[i], p];\n    }\n  }\n\n  // If we get here then B must have at least two elements.\n  for (i = 0; i < B.length - 1; ++i) {\n    for (j = i + 1; j < B.length; ++j) {\n      if (enclosesNot(encloseBasis2(B[i], B[j]), p) && enclosesNot(encloseBasis2(B[i], p), B[j]) && enclosesNot(encloseBasis2(B[j], p), B[i]) && enclosesWeakAll(encloseBasis3(B[i], B[j], p), B)) {\n        return [B[i], B[j], p];\n      }\n    }\n  }\n\n  // If we get here then something is very wrong.\n  throw new Error();\n}\nfunction enclosesNot(a, b) {\n  var dr = a.r - b.r,\n    dx = b.x - a.x,\n    dy = b.y - a.y;\n  return dr < 0 || dr * dr < dx * dx + dy * dy;\n}\nfunction enclosesWeak(a, b) {\n  var dr = a.r - b.r + Math.max(a.r, b.r, 1) * 1e-9,\n    dx = b.x - a.x,\n    dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\nfunction enclosesWeakAll(a, B) {\n  for (var i = 0; i < B.length; ++i) {\n    if (!enclosesWeak(a, B[i])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction encloseBasis(B) {\n  switch (B.length) {\n    case 1:\n      return encloseBasis1(B[0]);\n    case 2:\n      return encloseBasis2(B[0], B[1]);\n    case 3:\n      return encloseBasis3(B[0], B[1], B[2]);\n  }\n}\nfunction encloseBasis1(a) {\n  return {\n    x: a.x,\n    y: a.y,\n    r: a.r\n  };\n}\nfunction encloseBasis2(a, b) {\n  var x1 = a.x,\n    y1 = a.y,\n    r1 = a.r,\n    x2 = b.x,\n    y2 = b.y,\n    r2 = b.r,\n    x21 = x2 - x1,\n    y21 = y2 - y1,\n    r21 = r2 - r1,\n    l = Math.sqrt(x21 * x21 + y21 * y21);\n  return {\n    x: (x1 + x2 + x21 / l * r21) / 2,\n    y: (y1 + y2 + y21 / l * r21) / 2,\n    r: (l + r1 + r2) / 2\n  };\n}\nfunction encloseBasis3(a, b, c) {\n  var x1 = a.x,\n    y1 = a.y,\n    r1 = a.r,\n    x2 = b.x,\n    y2 = b.y,\n    r2 = b.r,\n    x3 = c.x,\n    y3 = c.y,\n    r3 = c.r,\n    a2 = x1 - x2,\n    a3 = x1 - x3,\n    b2 = y1 - y2,\n    b3 = y1 - y3,\n    c2 = r2 - r1,\n    c3 = r3 - r1,\n    d1 = x1 * x1 + y1 * y1 - r1 * r1,\n    d2 = d1 - x2 * x2 - y2 * y2 + r2 * r2,\n    d3 = d1 - x3 * x3 - y3 * y3 + r3 * r3,\n    ab = a3 * b2 - a2 * b3,\n    xa = (b2 * d3 - b3 * d2) / (ab * 2) - x1,\n    xb = (b3 * c2 - b2 * c3) / ab,\n    ya = (a3 * d2 - a2 * d3) / (ab * 2) - y1,\n    yb = (a2 * c3 - a3 * c2) / ab,\n    A = xb * xb + yb * yb - 1,\n    B = 2 * (r1 + xa * xb + ya * yb),\n    C = xa * xa + ya * ya - r1 * r1,\n    r = -(Math.abs(A) > 1e-6 ? (B + Math.sqrt(B * B - 4 * A * C)) / (2 * A) : C / B);\n  return {\n    x: x1 + xa + xb * r,\n    y: y1 + ya + yb * r,\n    r: r\n  };\n}", "map": {"version": 3, "names": ["shuffle", "lcg", "circles", "packEncloseRandom", "random", "i", "n", "Array", "from", "length", "B", "p", "e", "enclosesWeak", "encloseBasis", "extendBasis", "j", "enclosesWeakAll", "enclosesNot", "encloseBasis2", "encloseBasis3", "Error", "a", "b", "dr", "r", "dx", "x", "dy", "y", "Math", "max", "encloseBasis1", "x1", "y1", "r1", "x2", "y2", "r2", "x21", "y21", "r21", "l", "sqrt", "c", "x3", "y3", "r3", "a2", "a3", "b2", "b3", "c2", "c3", "d1", "d2", "d3", "ab", "xa", "xb", "ya", "yb", "A", "C", "abs"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/pack/enclose.js"], "sourcesContent": ["import {shuffle} from \"../array.js\";\nimport lcg from \"../lcg.js\";\n\nexport default function(circles) {\n  return packEncloseRandom(circles, lcg());\n}\n\nexport function packEncloseRandom(circles, random) {\n  var i = 0, n = (circles = shuffle(Array.from(circles), random)).length, B = [], p, e;\n\n  while (i < n) {\n    p = circles[i];\n    if (e && enclosesWeak(e, p)) ++i;\n    else e = encloseBasis(B = extendBasis(B, p)), i = 0;\n  }\n\n  return e;\n}\n\nfunction extendBasis(B, p) {\n  var i, j;\n\n  if (enclosesWeakAll(p, B)) return [p];\n\n  // If we get here then B must have at least one element.\n  for (i = 0; i < B.length; ++i) {\n    if (enclosesNot(p, B[i])\n        && enclosesWeakAll(encloseBasis2(B[i], p), B)) {\n      return [B[i], p];\n    }\n  }\n\n  // If we get here then B must have at least two elements.\n  for (i = 0; i < B.length - 1; ++i) {\n    for (j = i + 1; j < B.length; ++j) {\n      if (enclosesNot(encloseBasis2(B[i], B[j]), p)\n          && enclosesNot(encloseBasis2(B[i], p), B[j])\n          && enclosesNot(encloseBasis2(B[j], p), B[i])\n          && enclosesWeakAll(encloseBasis3(B[i], B[j], p), B)) {\n        return [B[i], B[j], p];\n      }\n    }\n  }\n\n  // If we get here then something is very wrong.\n  throw new Error;\n}\n\nfunction enclosesNot(a, b) {\n  var dr = a.r - b.r, dx = b.x - a.x, dy = b.y - a.y;\n  return dr < 0 || dr * dr < dx * dx + dy * dy;\n}\n\nfunction enclosesWeak(a, b) {\n  var dr = a.r - b.r + Math.max(a.r, b.r, 1) * 1e-9, dx = b.x - a.x, dy = b.y - a.y;\n  return dr > 0 && dr * dr > dx * dx + dy * dy;\n}\n\nfunction enclosesWeakAll(a, B) {\n  for (var i = 0; i < B.length; ++i) {\n    if (!enclosesWeak(a, B[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction encloseBasis(B) {\n  switch (B.length) {\n    case 1: return encloseBasis1(B[0]);\n    case 2: return encloseBasis2(B[0], B[1]);\n    case 3: return encloseBasis3(B[0], B[1], B[2]);\n  }\n}\n\nfunction encloseBasis1(a) {\n  return {\n    x: a.x,\n    y: a.y,\n    r: a.r\n  };\n}\n\nfunction encloseBasis2(a, b) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x21 = x2 - x1, y21 = y2 - y1, r21 = r2 - r1,\n      l = Math.sqrt(x21 * x21 + y21 * y21);\n  return {\n    x: (x1 + x2 + x21 / l * r21) / 2,\n    y: (y1 + y2 + y21 / l * r21) / 2,\n    r: (l + r1 + r2) / 2\n  };\n}\n\nfunction encloseBasis3(a, b, c) {\n  var x1 = a.x, y1 = a.y, r1 = a.r,\n      x2 = b.x, y2 = b.y, r2 = b.r,\n      x3 = c.x, y3 = c.y, r3 = c.r,\n      a2 = x1 - x2,\n      a3 = x1 - x3,\n      b2 = y1 - y2,\n      b3 = y1 - y3,\n      c2 = r2 - r1,\n      c3 = r3 - r1,\n      d1 = x1 * x1 + y1 * y1 - r1 * r1,\n      d2 = d1 - x2 * x2 - y2 * y2 + r2 * r2,\n      d3 = d1 - x3 * x3 - y3 * y3 + r3 * r3,\n      ab = a3 * b2 - a2 * b3,\n      xa = (b2 * d3 - b3 * d2) / (ab * 2) - x1,\n      xb = (b3 * c2 - b2 * c3) / ab,\n      ya = (a3 * d2 - a2 * d3) / (ab * 2) - y1,\n      yb = (a2 * c3 - a3 * c2) / ab,\n      A = xb * xb + yb * yb - 1,\n      B = 2 * (r1 + xa * xb + ya * yb),\n      C = xa * xa + ya * ya - r1 * r1,\n      r = -(Math.abs(A) > 1e-6 ? (B + Math.sqrt(B * B - 4 * A * C)) / (2 * A) : C / B);\n  return {\n    x: x1 + xa + xb * r,\n    y: y1 + ya + yb * r,\n    r: r\n  };\n}\n"], "mappings": "AAAA,SAAQA,OAAO,QAAO,aAAa;AACnC,OAAOC,GAAG,MAAM,WAAW;AAE3B,eAAe,UAASC,OAAO,EAAE;EAC/B,OAAOC,iBAAiB,CAACD,OAAO,EAAED,GAAG,CAAC,CAAC,CAAC;AAC1C;AAEA,OAAO,SAASE,iBAAiBA,CAACD,OAAO,EAAEE,MAAM,EAAE;EACjD,IAAIC,CAAC,GAAG,CAAC;IAAEC,CAAC,GAAG,CAACJ,OAAO,GAAGF,OAAO,CAACO,KAAK,CAACC,IAAI,CAACN,OAAO,CAAC,EAAEE,MAAM,CAAC,EAAEK,MAAM;IAAEC,CAAC,GAAG,EAAE;IAAEC,CAAC;IAAEC,CAAC;EAEpF,OAAOP,CAAC,GAAGC,CAAC,EAAE;IACZK,CAAC,GAAGT,OAAO,CAACG,CAAC,CAAC;IACd,IAAIO,CAAC,IAAIC,YAAY,CAACD,CAAC,EAAED,CAAC,CAAC,EAAE,EAAEN,CAAC,CAAC,KAC5BO,CAAC,GAAGE,YAAY,CAACJ,CAAC,GAAGK,WAAW,CAACL,CAAC,EAAEC,CAAC,CAAC,CAAC,EAAEN,CAAC,GAAG,CAAC;EACrD;EAEA,OAAOO,CAAC;AACV;AAEA,SAASG,WAAWA,CAACL,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAIN,CAAC,EAAEW,CAAC;EAER,IAAIC,eAAe,CAACN,CAAC,EAAED,CAAC,CAAC,EAAE,OAAO,CAACC,CAAC,CAAC;;EAErC;EACA,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACD,MAAM,EAAE,EAAEJ,CAAC,EAAE;IAC7B,IAAIa,WAAW,CAACP,CAAC,EAAED,CAAC,CAACL,CAAC,CAAC,CAAC,IACjBY,eAAe,CAACE,aAAa,CAACT,CAAC,CAACL,CAAC,CAAC,EAAEM,CAAC,CAAC,EAAED,CAAC,CAAC,EAAE;MACjD,OAAO,CAACA,CAAC,CAACL,CAAC,CAAC,EAAEM,CAAC,CAAC;IAClB;EACF;;EAEA;EACA,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACD,MAAM,GAAG,CAAC,EAAE,EAAEJ,CAAC,EAAE;IACjC,KAAKW,CAAC,GAAGX,CAAC,GAAG,CAAC,EAAEW,CAAC,GAAGN,CAAC,CAACD,MAAM,EAAE,EAAEO,CAAC,EAAE;MACjC,IAAIE,WAAW,CAACC,aAAa,CAACT,CAAC,CAACL,CAAC,CAAC,EAAEK,CAAC,CAACM,CAAC,CAAC,CAAC,EAAEL,CAAC,CAAC,IACtCO,WAAW,CAACC,aAAa,CAACT,CAAC,CAACL,CAAC,CAAC,EAAEM,CAAC,CAAC,EAAED,CAAC,CAACM,CAAC,CAAC,CAAC,IACzCE,WAAW,CAACC,aAAa,CAACT,CAAC,CAACM,CAAC,CAAC,EAAEL,CAAC,CAAC,EAAED,CAAC,CAACL,CAAC,CAAC,CAAC,IACzCY,eAAe,CAACG,aAAa,CAACV,CAAC,CAACL,CAAC,CAAC,EAAEK,CAAC,CAACM,CAAC,CAAC,EAAEL,CAAC,CAAC,EAAED,CAAC,CAAC,EAAE;QACvD,OAAO,CAACA,CAAC,CAACL,CAAC,CAAC,EAAEK,CAAC,CAACM,CAAC,CAAC,EAAEL,CAAC,CAAC;MACxB;IACF;EACF;;EAEA;EACA,MAAM,IAAIU,KAAK,CAAD,CAAC;AACjB;AAEA,SAASH,WAAWA,CAACI,CAAC,EAAEC,CAAC,EAAE;EACzB,IAAIC,EAAE,GAAGF,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE,CAAC;IAAEC,EAAE,GAAGH,CAAC,CAACI,CAAC,GAAGL,CAAC,CAACK,CAAC;IAAEC,EAAE,GAAGL,CAAC,CAACM,CAAC,GAAGP,CAAC,CAACO,CAAC;EAClD,OAAOL,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE;AAC9C;AAEA,SAASf,YAAYA,CAACS,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAIC,EAAE,GAAGF,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACT,CAAC,CAACG,CAAC,EAAEF,CAAC,CAACE,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI;IAAEC,EAAE,GAAGH,CAAC,CAACI,CAAC,GAAGL,CAAC,CAACK,CAAC;IAAEC,EAAE,GAAGL,CAAC,CAACM,CAAC,GAAGP,CAAC,CAACO,CAAC;EACjF,OAAOL,EAAE,GAAG,CAAC,IAAIA,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE;AAC9C;AAEA,SAASX,eAAeA,CAACK,CAAC,EAAEZ,CAAC,EAAE;EAC7B,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,CAAC,CAACD,MAAM,EAAE,EAAEJ,CAAC,EAAE;IACjC,IAAI,CAACQ,YAAY,CAACS,CAAC,EAAEZ,CAAC,CAACL,CAAC,CAAC,CAAC,EAAE;MAC1B,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASS,YAAYA,CAACJ,CAAC,EAAE;EACvB,QAAQA,CAAC,CAACD,MAAM;IACd,KAAK,CAAC;MAAE,OAAOuB,aAAa,CAACtB,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,KAAK,CAAC;MAAE,OAAOS,aAAa,CAACT,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IACxC,KAAK,CAAC;MAAE,OAAOU,aAAa,CAACV,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;AACF;AAEA,SAASsB,aAAaA,CAACV,CAAC,EAAE;EACxB,OAAO;IACLK,CAAC,EAAEL,CAAC,CAACK,CAAC;IACNE,CAAC,EAAEP,CAAC,CAACO,CAAC;IACNJ,CAAC,EAAEH,CAAC,CAACG;EACP,CAAC;AACH;AAEA,SAASN,aAAaA,CAACG,CAAC,EAAEC,CAAC,EAAE;EAC3B,IAAIU,EAAE,GAAGX,CAAC,CAACK,CAAC;IAAEO,EAAE,GAAGZ,CAAC,CAACO,CAAC;IAAEM,EAAE,GAAGb,CAAC,CAACG,CAAC;IAC5BW,EAAE,GAAGb,CAAC,CAACI,CAAC;IAAEU,EAAE,GAAGd,CAAC,CAACM,CAAC;IAAES,EAAE,GAAGf,CAAC,CAACE,CAAC;IAC5Bc,GAAG,GAAGH,EAAE,GAAGH,EAAE;IAAEO,GAAG,GAAGH,EAAE,GAAGH,EAAE;IAAEO,GAAG,GAAGH,EAAE,GAAGH,EAAE;IAC3CO,CAAC,GAAGZ,IAAI,CAACa,IAAI,CAACJ,GAAG,GAAGA,GAAG,GAAGC,GAAG,GAAGA,GAAG,CAAC;EACxC,OAAO;IACLb,CAAC,EAAE,CAACM,EAAE,GAAGG,EAAE,GAAGG,GAAG,GAAGG,CAAC,GAAGD,GAAG,IAAI,CAAC;IAChCZ,CAAC,EAAE,CAACK,EAAE,GAAGG,EAAE,GAAGG,GAAG,GAAGE,CAAC,GAAGD,GAAG,IAAI,CAAC;IAChChB,CAAC,EAAE,CAACiB,CAAC,GAAGP,EAAE,GAAGG,EAAE,IAAI;EACrB,CAAC;AACH;AAEA,SAASlB,aAAaA,CAACE,CAAC,EAAEC,CAAC,EAAEqB,CAAC,EAAE;EAC9B,IAAIX,EAAE,GAAGX,CAAC,CAACK,CAAC;IAAEO,EAAE,GAAGZ,CAAC,CAACO,CAAC;IAAEM,EAAE,GAAGb,CAAC,CAACG,CAAC;IAC5BW,EAAE,GAAGb,CAAC,CAACI,CAAC;IAAEU,EAAE,GAAGd,CAAC,CAACM,CAAC;IAAES,EAAE,GAAGf,CAAC,CAACE,CAAC;IAC5BoB,EAAE,GAAGD,CAAC,CAACjB,CAAC;IAAEmB,EAAE,GAAGF,CAAC,CAACf,CAAC;IAAEkB,EAAE,GAAGH,CAAC,CAACnB,CAAC;IAC5BuB,EAAE,GAAGf,EAAE,GAAGG,EAAE;IACZa,EAAE,GAAGhB,EAAE,GAAGY,EAAE;IACZK,EAAE,GAAGhB,EAAE,GAAGG,EAAE;IACZc,EAAE,GAAGjB,EAAE,GAAGY,EAAE;IACZM,EAAE,GAAGd,EAAE,GAAGH,EAAE;IACZkB,EAAE,GAAGN,EAAE,GAAGZ,EAAE;IACZmB,EAAE,GAAGrB,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAChCoB,EAAE,GAAGD,EAAE,GAAGlB,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IACrCkB,EAAE,GAAGF,EAAE,GAAGT,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IACrCU,EAAE,GAAGR,EAAE,GAAGC,EAAE,GAAGF,EAAE,GAAGG,EAAE;IACtBO,EAAE,GAAG,CAACR,EAAE,GAAGM,EAAE,GAAGL,EAAE,GAAGI,EAAE,KAAKE,EAAE,GAAG,CAAC,CAAC,GAAGxB,EAAE;IACxC0B,EAAE,GAAG,CAACR,EAAE,GAAGC,EAAE,GAAGF,EAAE,GAAGG,EAAE,IAAII,EAAE;IAC7BG,EAAE,GAAG,CAACX,EAAE,GAAGM,EAAE,GAAGP,EAAE,GAAGQ,EAAE,KAAKC,EAAE,GAAG,CAAC,CAAC,GAAGvB,EAAE;IACxC2B,EAAE,GAAG,CAACb,EAAE,GAAGK,EAAE,GAAGJ,EAAE,GAAGG,EAAE,IAAIK,EAAE;IAC7BK,CAAC,GAAGH,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE,GAAG,CAAC;IACzBnD,CAAC,GAAG,CAAC,IAAIyB,EAAE,GAAGuB,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAGC,EAAE,CAAC;IAChCE,CAAC,GAAGL,EAAE,GAAGA,EAAE,GAAGE,EAAE,GAAGA,EAAE,GAAGzB,EAAE,GAAGA,EAAE;IAC/BV,CAAC,GAAG,EAAEK,IAAI,CAACkC,GAAG,CAACF,CAAC,CAAC,GAAG,IAAI,GAAG,CAACpD,CAAC,GAAGoB,IAAI,CAACa,IAAI,CAACjC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAGoD,CAAC,GAAGC,CAAC,CAAC,KAAK,CAAC,GAAGD,CAAC,CAAC,GAAGC,CAAC,GAAGrD,CAAC,CAAC;EACpF,OAAO;IACLiB,CAAC,EAAEM,EAAE,GAAGyB,EAAE,GAAGC,EAAE,GAAGlC,CAAC;IACnBI,CAAC,EAAEK,EAAE,GAAG0B,EAAE,GAAGC,EAAE,GAAGpC,CAAC;IACnBA,CAAC,EAAEA;EACL,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}