{"ast": null, "code": "import { dot, gemv, norm2, scale, weightedSum } from './blas1';\nimport { wolfeLineSearch } from './linesearch';\nexport function conjugateGradient(f, initial, params) {\n  // allocate all memory up front here, keep out of the loop for perfomance\n  // reasons\n  let current = {\n    x: initial.slice(),\n    fx: 0,\n    fxprime: initial.slice()\n  };\n  let next = {\n    x: initial.slice(),\n    fx: 0,\n    fxprime: initial.slice()\n  };\n  const yk = initial.slice();\n  let temp;\n  let a = 1;\n  params = params || {};\n  const maxIterations = params.maxIterations || initial.length * 20;\n  current.fx = f(current.x, current.fxprime);\n  const pk = current.fxprime.slice();\n  scale(pk, current.fxprime, -1);\n  for (let i = 0; i < maxIterations; ++i) {\n    a = wolfeLineSearch(f, pk, current, next, a);\n    // todo: history in wrong spot?\n    if (params.history) {\n      params.history.push({\n        x: current.x.slice(),\n        fx: current.fx,\n        fxprime: current.fxprime.slice(),\n        alpha: a\n      });\n    }\n    if (!a) {\n      // faiiled to find point that satifies wolfe conditions.\n      // reset direction for next iteration\n      scale(pk, current.fxprime, -1);\n    } else {\n      // update direction using Polak–Ribiere CG method\n      weightedSum(yk, 1, next.fxprime, -1, current.fxprime);\n      const delta_k = dot(current.fxprime, current.fxprime);\n      const beta_k = Math.max(0, dot(yk, next.fxprime) / delta_k);\n      weightedSum(pk, beta_k, pk, -1, next.fxprime);\n      temp = current;\n      current = next;\n      next = temp;\n    }\n    if (norm2(current.fxprime) <= 1e-5) {\n      break;\n    }\n  }\n  if (params.history) {\n    params.history.push({\n      x: current.x.slice(),\n      fx: current.fx,\n      fxprime: current.fxprime.slice(),\n      alpha: a\n    });\n  }\n  return current;\n}\n/// Solves a system of lienar equations Ax =b for x\n/// using the conjugate gradient method.\nexport function conjugateGradientSolve(A, b, x, history) {\n  const r = x.slice();\n  const Ap = x.slice();\n  let rsold;\n  let rsnew;\n  let alpha;\n  // r = b - A*x\n  gemv(Ap, A, x);\n  weightedSum(r, 1, b, -1, Ap);\n  const p = r.slice();\n  rsold = dot(r, r);\n  for (let i = 0; i < b.length; ++i) {\n    gemv(Ap, A, p);\n    alpha = rsold / dot(p, Ap);\n    if (history) {\n      history.push({\n        x: x.slice(),\n        p: p.slice(),\n        alpha: alpha\n      });\n    }\n    //x=x+alpha*p;\n    weightedSum(x, 1, x, alpha, p);\n    // r=r-alpha*Ap;\n    weightedSum(r, 1, r, -alpha, Ap);\n    rsnew = dot(r, r);\n    if (Math.sqrt(rsnew) <= 1e-10) break;\n    // p=r+(rsnew/rsold)*p;\n    weightedSum(p, 1, r, rsnew / rsold, p);\n    rsold = rsnew;\n  }\n  if (history) {\n    history.push({\n      x: x.slice(),\n      p: p.slice(),\n      alpha: alpha\n    });\n  }\n  return x;\n}", "map": {"version": 3, "names": ["dot", "gemv", "norm2", "scale", "weightedSum", "wolfeLineSearch", "conjugateGradient", "f", "initial", "params", "current", "x", "slice", "fx", "fxprime", "next", "yk", "temp", "a", "maxIterations", "length", "pk", "i", "history", "push", "alpha", "delta_k", "beta_k", "Math", "max", "conjugateGradientSolve", "A", "b", "r", "Ap", "rsold", "rsnew", "p", "sqrt"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/venn/fmin/conjugateGradient.ts"], "sourcesContent": ["import { dot, gemv, norm2, scale, weightedSum } from './blas1';\nimport { wolfeLineSearch } from './linesearch';\n\nexport function conjugateGradient(f, initial, params) {\n  // allocate all memory up front here, keep out of the loop for perfomance\n  // reasons\n  let current = { x: initial.slice(), fx: 0, fxprime: initial.slice() };\n  let next = { x: initial.slice(), fx: 0, fxprime: initial.slice() };\n  const yk = initial.slice();\n  let temp;\n  let a = 1;\n\n  params = params || {};\n  const maxIterations = params.maxIterations || initial.length * 20;\n\n  current.fx = f(current.x, current.fxprime);\n  const pk = current.fxprime.slice();\n  scale(pk, current.fxprime, -1);\n\n  for (let i = 0; i < maxIterations; ++i) {\n    a = wolfeLineSearch(f, pk, current, next, a);\n\n    // todo: history in wrong spot?\n    if (params.history) {\n      params.history.push({\n        x: current.x.slice(),\n        fx: current.fx,\n        fxprime: current.fxprime.slice(),\n        alpha: a,\n      });\n    }\n\n    if (!a) {\n      // faiiled to find point that satifies wolfe conditions.\n      // reset direction for next iteration\n      scale(pk, current.fxprime, -1);\n    } else {\n      // update direction using Polak–Ribiere CG method\n      weightedSum(yk, 1, next.fxprime, -1, current.fxprime);\n\n      const delta_k = dot(current.fxprime, current.fxprime);\n      const beta_k = Math.max(0, dot(yk, next.fxprime) / delta_k);\n\n      weightedSum(pk, beta_k, pk, -1, next.fxprime);\n\n      temp = current;\n      current = next;\n      next = temp;\n    }\n\n    if (norm2(current.fxprime) <= 1e-5) {\n      break;\n    }\n  }\n\n  if (params.history) {\n    params.history.push({\n      x: current.x.slice(),\n      fx: current.fx,\n      fxprime: current.fxprime.slice(),\n      alpha: a,\n    });\n  }\n\n  return current;\n}\n\n/// Solves a system of lienar equations Ax =b for x\n/// using the conjugate gradient method.\nexport function conjugateGradientSolve(A, b, x, history?: any) {\n  const r = x.slice();\n  const Ap = x.slice();\n  let rsold;\n  let rsnew;\n  let alpha;\n\n  // r = b - A*x\n  gemv(Ap, A, x);\n  weightedSum(r, 1, b, -1, Ap);\n  const p = r.slice();\n  rsold = dot(r, r);\n\n  for (let i = 0; i < b.length; ++i) {\n    gemv(Ap, A, p);\n    alpha = rsold / dot(p, Ap);\n    if (history) {\n      history.push({ x: x.slice(), p: p.slice(), alpha: alpha });\n    }\n\n    //x=x+alpha*p;\n    weightedSum(x, 1, x, alpha, p);\n\n    // r=r-alpha*Ap;\n    weightedSum(r, 1, r, -alpha, Ap);\n    rsnew = dot(r, r);\n    if (Math.sqrt(rsnew) <= 1e-10) break;\n\n    // p=r+(rsnew/rsold)*p;\n    weightedSum(p, 1, r, rsnew / rsold, p);\n    rsold = rsnew;\n  }\n  if (history) {\n    history.push({ x: x.slice(), p: p.slice(), alpha: alpha });\n  }\n  return x;\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,WAAW,QAAQ,SAAS;AAC9D,SAASC,eAAe,QAAQ,cAAc;AAE9C,OAAM,SAAUC,iBAAiBA,CAACC,CAAC,EAAEC,OAAO,EAAEC,MAAM;EAClD;EACA;EACA,IAAIC,OAAO,GAAG;IAAEC,CAAC,EAAEH,OAAO,CAACI,KAAK,EAAE;IAAEC,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAEN,OAAO,CAACI,KAAK;EAAE,CAAE;EACrE,IAAIG,IAAI,GAAG;IAAEJ,CAAC,EAAEH,OAAO,CAACI,KAAK,EAAE;IAAEC,EAAE,EAAE,CAAC;IAAEC,OAAO,EAAEN,OAAO,CAACI,KAAK;EAAE,CAAE;EAClE,MAAMI,EAAE,GAAGR,OAAO,CAACI,KAAK,EAAE;EAC1B,IAAIK,IAAI;EACR,IAAIC,CAAC,GAAG,CAAC;EAETT,MAAM,GAAGA,MAAM,IAAI,EAAE;EACrB,MAAMU,aAAa,GAAGV,MAAM,CAACU,aAAa,IAAIX,OAAO,CAACY,MAAM,GAAG,EAAE;EAEjEV,OAAO,CAACG,EAAE,GAAGN,CAAC,CAACG,OAAO,CAACC,CAAC,EAAED,OAAO,CAACI,OAAO,CAAC;EAC1C,MAAMO,EAAE,GAAGX,OAAO,CAACI,OAAO,CAACF,KAAK,EAAE;EAClCT,KAAK,CAACkB,EAAE,EAAEX,OAAO,CAACI,OAAO,EAAE,CAAC,CAAC,CAAC;EAE9B,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,aAAa,EAAE,EAAEG,CAAC,EAAE;IACtCJ,CAAC,GAAGb,eAAe,CAACE,CAAC,EAAEc,EAAE,EAAEX,OAAO,EAAEK,IAAI,EAAEG,CAAC,CAAC;IAE5C;IACA,IAAIT,MAAM,CAACc,OAAO,EAAE;MAClBd,MAAM,CAACc,OAAO,CAACC,IAAI,CAAC;QAClBb,CAAC,EAAED,OAAO,CAACC,CAAC,CAACC,KAAK,EAAE;QACpBC,EAAE,EAAEH,OAAO,CAACG,EAAE;QACdC,OAAO,EAAEJ,OAAO,CAACI,OAAO,CAACF,KAAK,EAAE;QAChCa,KAAK,EAAEP;OACR,CAAC;;IAGJ,IAAI,CAACA,CAAC,EAAE;MACN;MACA;MACAf,KAAK,CAACkB,EAAE,EAAEX,OAAO,CAACI,OAAO,EAAE,CAAC,CAAC,CAAC;KAC/B,MAAM;MACL;MACAV,WAAW,CAACY,EAAE,EAAE,CAAC,EAAED,IAAI,CAACD,OAAO,EAAE,CAAC,CAAC,EAAEJ,OAAO,CAACI,OAAO,CAAC;MAErD,MAAMY,OAAO,GAAG1B,GAAG,CAACU,OAAO,CAACI,OAAO,EAAEJ,OAAO,CAACI,OAAO,CAAC;MACrD,MAAMa,MAAM,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE7B,GAAG,CAACgB,EAAE,EAAED,IAAI,CAACD,OAAO,CAAC,GAAGY,OAAO,CAAC;MAE3DtB,WAAW,CAACiB,EAAE,EAAEM,MAAM,EAAEN,EAAE,EAAE,CAAC,CAAC,EAAEN,IAAI,CAACD,OAAO,CAAC;MAE7CG,IAAI,GAAGP,OAAO;MACdA,OAAO,GAAGK,IAAI;MACdA,IAAI,GAAGE,IAAI;;IAGb,IAAIf,KAAK,CAACQ,OAAO,CAACI,OAAO,CAAC,IAAI,IAAI,EAAE;MAClC;;;EAIJ,IAAIL,MAAM,CAACc,OAAO,EAAE;IAClBd,MAAM,CAACc,OAAO,CAACC,IAAI,CAAC;MAClBb,CAAC,EAAED,OAAO,CAACC,CAAC,CAACC,KAAK,EAAE;MACpBC,EAAE,EAAEH,OAAO,CAACG,EAAE;MACdC,OAAO,EAAEJ,OAAO,CAACI,OAAO,CAACF,KAAK,EAAE;MAChCa,KAAK,EAAEP;KACR,CAAC;;EAGJ,OAAOR,OAAO;AAChB;AAEA;AACA;AACA,OAAM,SAAUoB,sBAAsBA,CAACC,CAAC,EAAEC,CAAC,EAAErB,CAAC,EAAEY,OAAa;EAC3D,MAAMU,CAAC,GAAGtB,CAAC,CAACC,KAAK,EAAE;EACnB,MAAMsB,EAAE,GAAGvB,CAAC,CAACC,KAAK,EAAE;EACpB,IAAIuB,KAAK;EACT,IAAIC,KAAK;EACT,IAAIX,KAAK;EAET;EACAxB,IAAI,CAACiC,EAAE,EAAEH,CAAC,EAAEpB,CAAC,CAAC;EACdP,WAAW,CAAC6B,CAAC,EAAE,CAAC,EAAED,CAAC,EAAE,CAAC,CAAC,EAAEE,EAAE,CAAC;EAC5B,MAAMG,CAAC,GAAGJ,CAAC,CAACrB,KAAK,EAAE;EACnBuB,KAAK,GAAGnC,GAAG,CAACiC,CAAC,EAAEA,CAAC,CAAC;EAEjB,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGU,CAAC,CAACZ,MAAM,EAAE,EAAEE,CAAC,EAAE;IACjCrB,IAAI,CAACiC,EAAE,EAAEH,CAAC,EAAEM,CAAC,CAAC;IACdZ,KAAK,GAAGU,KAAK,GAAGnC,GAAG,CAACqC,CAAC,EAAEH,EAAE,CAAC;IAC1B,IAAIX,OAAO,EAAE;MACXA,OAAO,CAACC,IAAI,CAAC;QAAEb,CAAC,EAAEA,CAAC,CAACC,KAAK,EAAE;QAAEyB,CAAC,EAAEA,CAAC,CAACzB,KAAK,EAAE;QAAEa,KAAK,EAAEA;MAAK,CAAE,CAAC;;IAG5D;IACArB,WAAW,CAACO,CAAC,EAAE,CAAC,EAAEA,CAAC,EAAEc,KAAK,EAAEY,CAAC,CAAC;IAE9B;IACAjC,WAAW,CAAC6B,CAAC,EAAE,CAAC,EAAEA,CAAC,EAAE,CAACR,KAAK,EAAES,EAAE,CAAC;IAChCE,KAAK,GAAGpC,GAAG,CAACiC,CAAC,EAAEA,CAAC,CAAC;IACjB,IAAIL,IAAI,CAACU,IAAI,CAACF,KAAK,CAAC,IAAI,KAAK,EAAE;IAE/B;IACAhC,WAAW,CAACiC,CAAC,EAAE,CAAC,EAAEJ,CAAC,EAAEG,KAAK,GAAGD,KAAK,EAAEE,CAAC,CAAC;IACtCF,KAAK,GAAGC,KAAK;;EAEf,IAAIb,OAAO,EAAE;IACXA,OAAO,CAACC,IAAI,CAAC;MAAEb,CAAC,EAAEA,CAAC,CAACC,KAAK,EAAE;MAAEyB,CAAC,EAAEA,CAAC,CAACzB,KAAK,EAAE;MAAEa,KAAK,EAAEA;IAAK,CAAE,CAAC;;EAE5D,OAAOd,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}