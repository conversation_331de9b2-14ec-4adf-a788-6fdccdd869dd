{"ast": null, "code": "/**\n * Checks if the character is or belongs to a number.\n * [0-9]|+|-|.\n */\nexport function isDigitStart(code) {\n  return code >= 48 && code <= 57 /* 0..9 */ || code === 0x2b /* + */ || code === 0x2d /* - */ || code === 0x2e; /* . */\n}\nexport function isDigit(code) {\n  return code >= 48 && code <= 57; // 0..9\n}", "map": {"version": 3, "names": ["isDigitStart", "code", "isDigit"], "sources": ["path/parser/is-digit-start.ts"], "sourcesContent": [null], "mappings": "AAAA;;;;AAIA,OAAM,SAAUA,YAAYA,CAACC,IAAY;EACvC,OACGA,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,CAAE,cAAcA,IAAI,KAAK,IAAI,CAAC,WAAWA,IAAI,KAAK,IAAI,CAAC,WAAWA,IAAI,KAAK,IAAI,CACxG,CAAC;AACL;AAEA,OAAM,SAAUC,OAAOA,CAACD,IAAY;EAClC,OAAOA,IAAI,IAAI,EAAE,IAAIA,IAAI,IAAI,EAAE,CAAC,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}