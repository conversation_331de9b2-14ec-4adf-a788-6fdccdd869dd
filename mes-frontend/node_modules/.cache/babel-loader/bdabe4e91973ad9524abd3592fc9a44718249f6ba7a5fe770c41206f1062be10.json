{"ast": null, "code": "import { __extends, __read, __rest } from \"tslib\";\nimport { Component } from '../../core';\nimport { BBox, classNames, ifShow, parseSeriesAttr, select, splitStyle } from '../../util';\nvar CLASS_NAMES = classNames({\n  text: 'text'\n}, 'title');\n/**\n * @example\n * lt -> ['l', 't']\n * left-top -> ['l', 't']\n * inner -> i\n */\nexport function parsePosition(position) {\n  if (!/\\S+-\\S+/g.test(position)) return position.length > 2 ? [position[0]] : position.split('');\n  return position.split('-').map(function (str) {\n    return str[0];\n  });\n}\n/**\n * calculate the actual bbox of the element with title\n * @example a legend with width x, height y, but the real bbox is x1 < x, y1 < y\n */\nexport function getBBox(title, content) {\n  var _a = title.attributes,\n    position = _a.position,\n    spacing = _a.spacing,\n    inset = _a.inset,\n    text = _a.text;\n  var titleBBox = title.getBBox();\n  var contentBBox = content.getBBox();\n  var pos = parsePosition(position);\n  var _b = __read(parseSeriesAttr(text ? spacing : 0), 4),\n    spacingTop = _b[0],\n    spacingRight = _b[1],\n    spacingBottom = _b[2],\n    spacingLeft = _b[3];\n  var _c = __read(parseSeriesAttr(inset), 4),\n    insetTop = _c[0],\n    insetRight = _c[1],\n    insetBottom = _c[2],\n    insetLeft = _c[3];\n  var _d = __read([spacingLeft + spacingRight, spacingTop + spacingBottom], 2),\n    spacingWidth = _d[0],\n    spacingHeight = _d[1];\n  var _e = __read([insetLeft + insetRight, insetTop + insetBottom], 2),\n    insetWidth = _e[0],\n    insetHeight = _e[1];\n  // 只基于第一个 pos 进行判断\n  // 如果在左边或者上边，直接包围盒相加再加上间距\n  if (pos[0] === 'l') {\n    return new BBox(titleBBox.x, titleBBox.y, contentBBox.width + titleBBox.width + spacingWidth + insetWidth, Math.max(contentBBox.height + insetHeight, titleBBox.height));\n  }\n  if (pos[0] === 't') {\n    return new BBox(titleBBox.x, titleBBox.y, Math.max(contentBBox.width + insetWidth, titleBBox.width), contentBBox.height + titleBBox.height + spacingHeight + insetHeight);\n  }\n  // 如果在右边或者下边，基于 content.width, content.height 相加再加上间距\n  var _f = __read([content.attributes.width || contentBBox.width, content.attributes.height || contentBBox.height], 2),\n    contentWidth = _f[0],\n    contentHeight = _f[1];\n  return new BBox(contentBBox.x, contentBBox.y, contentWidth + titleBBox.width + spacingWidth + insetWidth, contentHeight + titleBBox.height + spacingHeight + insetHeight);\n}\nfunction mayApplyStyle(el, style) {\n  var finalStyle = Object.entries(style).reduce(function (acc, _a) {\n    var _b = __read(_a, 2),\n      key = _b[0],\n      value = _b[1];\n    var currAttr = el.node().attr(key);\n    if (!currAttr) acc[key] = value;\n    return acc;\n  }, {});\n  el.styles(finalStyle);\n}\nfunction getTitleLayout(cfg) {\n  var _a, _b, _c, _d;\n  var _e = cfg,\n    width = _e.width,\n    height = _e.height,\n    position = _e.position;\n  var _f = __read([+width / 2, +height / 2], 2),\n    hW = _f[0],\n    hH = _f[1];\n  var _g = __read([+hW, +hH, 'center', 'middle'], 4),\n    x = _g[0],\n    y = _g[1],\n    textAlign = _g[2],\n    textBaseline = _g[3];\n  var pos = parsePosition(position);\n  if (pos.includes('l')) _a = __read([0, 'start'], 2), x = _a[0], textAlign = _a[1];\n  if (pos.includes('r')) _b = __read([+width, 'end'], 2), x = _b[0], textAlign = _b[1];\n  if (pos.includes('t')) _c = __read([0, 'top'], 2), y = _c[0], textBaseline = _c[1];\n  if (pos.includes('b')) _d = __read([+height, 'bottom'], 2), y = _d[0], textBaseline = _d[1];\n  return {\n    x: x,\n    y: y,\n    textAlign: textAlign,\n    textBaseline: textBaseline\n  };\n}\nvar Title = /** @class */function (_super) {\n  __extends(Title, _super);\n  function Title(options) {\n    return _super.call(this, options, {\n      text: '',\n      width: 0,\n      height: 0,\n      fill: '#4a505a',\n      fontWeight: 'bold',\n      fontSize: 12,\n      fontFamily: 'sans-serif',\n      inset: 0,\n      spacing: 0,\n      position: 'top-left'\n    }) || this;\n  }\n  Title.prototype.getAvailableSpace = function () {\n    var container = this;\n    var _a = this.attributes,\n      containerWidth = _a.width,\n      containerHeight = _a.height,\n      position = _a.position,\n      spacing = _a.spacing,\n      inset = _a.inset;\n    var title = container.querySelector(CLASS_NAMES.text.class);\n    if (!title) return new BBox(0, 0, +containerWidth, +containerHeight);\n    var _b = title.getBBox(),\n      titleWidth = _b.width,\n      titleHeight = _b.height;\n    var _c = __read(parseSeriesAttr(spacing), 4),\n      spacingTop = _c[0],\n      spacingRight = _c[1],\n      spacingBottom = _c[2],\n      spacingLeft = _c[3];\n    var _d = __read([0, 0, +containerWidth, +containerHeight], 4),\n      x = _d[0],\n      y = _d[1],\n      width = _d[2],\n      height = _d[3];\n    var pos = parsePosition(position);\n    if (pos.includes('i')) return new BBox(x, y, width, height);\n    pos.forEach(function (p, i) {\n      var _a, _b, _c, _d;\n      if (p === 't') _a = __read(i === 0 ? [titleHeight + spacingBottom, +containerHeight - titleHeight - spacingBottom] : [0, +containerHeight], 2), y = _a[0], height = _a[1];\n      if (p === 'r') _b = __read([+containerWidth - titleWidth - spacingLeft], 1), width = _b[0];\n      if (p === 'b') _c = __read([+containerHeight - titleHeight - spacingTop], 1), height = _c[0];\n      if (p === 'l') _d = __read(i === 0 ? [titleWidth + spacingRight, +containerWidth - titleWidth - spacingRight] : [0, +containerWidth], 2), x = _d[0], width = _d[1];\n    });\n    var _e = __read(parseSeriesAttr(inset), 4),\n      insetTop = _e[0],\n      insetRight = _e[1],\n      insetBottom = _e[2],\n      insetLeft = _e[3];\n    var _f = __read([insetLeft + insetRight, insetTop + insetBottom], 2),\n      insetWidth = _f[0],\n      insetHeight = _f[1];\n    return new BBox(x + insetLeft, y + insetTop, width - insetWidth, height - insetHeight);\n  };\n  Title.prototype.getBBox = function () {\n    if (this.title) return this.title.getBBox();\n    return new BBox(0, 0, 0, 0);\n  };\n  Title.prototype.render = function (attributes, container) {\n    var _this = this;\n    var width = attributes.width,\n      height = attributes.height,\n      position = attributes.position,\n      spacing = attributes.spacing,\n      restStyle = __rest(attributes, [\"width\", \"height\", \"position\", \"spacing\"]);\n    var _a = __read(splitStyle(restStyle), 1),\n      titleStyle = _a[0];\n    var _b = getTitleLayout(attributes),\n      x = _b.x,\n      y = _b.y,\n      textAlign = _b.textAlign,\n      textBaseline = _b.textBaseline;\n    ifShow(!!restStyle.text, select(container), function (group) {\n      _this.title = group.maybeAppendByClassName(CLASS_NAMES.text, 'text').styles(titleStyle).call(mayApplyStyle, {\n        x: x,\n        y: y,\n        textAlign: textAlign,\n        textBaseline: textBaseline\n      }).node();\n    });\n  };\n  return Title;\n}(Component);\nexport { Title };", "map": {"version": 3, "names": ["Component", "BBox", "classNames", "ifShow", "parseSeriesAttr", "select", "splitStyle", "CLASS_NAMES", "text", "parsePosition", "position", "test", "length", "split", "map", "str", "getBBox", "title", "content", "_a", "attributes", "spacing", "inset", "titleBBox", "contentBBox", "pos", "_b", "__read", "spacingTop", "spacingRight", "spacingBottom", "spacingLeft", "_c", "insetTop", "insetRight", "insetBottom", "insetLeft", "_d", "spacingWidth", "spacingHeight", "_e", "insetWidth", "insetHeight", "x", "y", "width", "Math", "max", "height", "_f", "contentWidth", "contentHeight", "mayApplyStyle", "el", "style", "finalStyle", "Object", "entries", "reduce", "acc", "key", "value", "currAttr", "node", "attr", "styles", "getTitleLayout", "cfg", "hW", "hH", "_g", "textAlign", "textBaseline", "includes", "Title", "_super", "__extends", "options", "call", "fill", "fontWeight", "fontSize", "fontFamily", "prototype", "getAvailableSpace", "container", "containerWidth", "containerHeight", "querySelector", "class", "titleWidth", "titleHeight", "for<PERSON>ach", "p", "i", "render", "_this", "restStyle", "__rest", "titleStyle", "group", "maybeAppendByClassName"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/title/index.ts"], "sourcesContent": ["import { Component } from '../../core';\nimport type { Group } from '../../shapes';\nimport { DisplayObject, Text } from '../../shapes';\nimport { BBox, Selection, classNames, ifShow, parseSeriesAttr, select, splitStyle } from '../../util';\nimport type { TitleOptions, TitleStyleProps } from './types';\n\nexport type { TitleOptions, TitleStyleProps };\n\nconst CLASS_NAMES = classNames(\n  {\n    text: 'text',\n  },\n  'title'\n);\n\n/**\n * @example\n * lt -> ['l', 't']\n * left-top -> ['l', 't']\n * inner -> i\n */\nexport function parsePosition(position: string): string[] {\n  if (!/\\S+-\\S+/g.test(position)) return position.length > 2 ? [position[0]] : position.split('');\n  return position.split('-').map((str) => {\n    return str[0];\n  });\n}\n\n/**\n * calculate the actual bbox of the element with title\n * @example a legend with width x, height y, but the real bbox is x1 < x, y1 < y\n */\nexport function getBBox(title: Title, content: DisplayObject): DOMRect {\n  const { position, spacing, inset, text } = title.attributes as Required<TitleStyleProps>;\n  const titleBBox = title.getBBox();\n  const contentBBox = content.getBBox();\n  const pos = parsePosition(position);\n  const [spacingTop, spacingRight, spacingBottom, spacingLeft] = parseSeriesAttr(text ? spacing : 0);\n  const [insetTop, insetRight, insetBottom, insetLeft] = parseSeriesAttr(inset);\n  const [spacingWidth, spacingHeight] = [spacingLeft + spacingRight, spacingTop + spacingBottom];\n  const [insetWidth, insetHeight] = [insetLeft + insetRight, insetTop + insetBottom];\n\n  // 只基于第一个 pos 进行判断\n  // 如果在左边或者上边，直接包围盒相加再加上间距\n  if (pos[0] === 'l') {\n    return new BBox(\n      titleBBox.x,\n      titleBBox.y,\n      contentBBox.width + titleBBox.width + spacingWidth + insetWidth,\n      Math.max(contentBBox.height + insetHeight, titleBBox.height)\n    );\n  }\n  if (pos[0] === 't') {\n    return new BBox(\n      titleBBox.x,\n      titleBBox.y,\n      Math.max(contentBBox.width + insetWidth, titleBBox.width),\n      contentBBox.height + titleBBox.height + spacingHeight + insetHeight\n    );\n  }\n  // 如果在右边或者下边，基于 content.width, content.height 相加再加上间距\n\n  const [contentWidth, contentHeight] = [\n    content.attributes.width || contentBBox.width,\n    content.attributes.height || contentBBox.height,\n  ];\n  return new BBox(\n    contentBBox.x,\n    contentBBox.y,\n    contentWidth + titleBBox.width + spacingWidth + insetWidth,\n    contentHeight + titleBBox.height + spacingHeight + insetHeight\n  );\n}\n\nfunction mayApplyStyle(el: Selection, style: any) {\n  const finalStyle = Object.entries(style).reduce((acc, [key, value]) => {\n    const currAttr = el.node().attr(key);\n    if (!currAttr) acc[key] = value;\n    return acc;\n  }, {} as Record<string, any>);\n\n  el.styles(finalStyle);\n}\n\nfunction getTitleLayout(cfg: TitleStyleProps) {\n  const { width, height, position } = cfg as Required<TitleStyleProps>;\n  const [hW, hH] = [+width / 2, +height / 2];\n  let [x, y, textAlign, textBaseline] = [+hW, +hH, 'center', 'middle'];\n  const pos = parsePosition(position);\n\n  if (pos.includes('l')) [x, textAlign] = [0, 'start'];\n  if (pos.includes('r')) [x, textAlign] = [+width, 'end'];\n  if (pos.includes('t')) [y, textBaseline] = [0, 'top'];\n  if (pos.includes('b')) [y, textBaseline] = [+height, 'bottom'];\n\n  return { x, y, textAlign, textBaseline };\n}\n\nexport class Title extends Component<TitleStyleProps> {\n  private title!: Text;\n\n  constructor(options: TitleOptions) {\n    super(options, {\n      text: '',\n      width: 0,\n      height: 0,\n      fill: '#4a505a',\n      fontWeight: 'bold',\n      fontSize: 12,\n      fontFamily: 'sans-serif',\n      inset: 0,\n      spacing: 0,\n      position: 'top-left',\n    });\n  }\n\n  public getAvailableSpace(): DOMRect {\n    const container = this;\n    const {\n      width: containerWidth,\n      height: containerHeight,\n      position,\n      spacing,\n      inset,\n    } = this.attributes as Required<TitleStyleProps>;\n    const title = container.querySelector<DisplayObject>(CLASS_NAMES.text.class);\n    if (!title) return new BBox(0, 0, +containerWidth, +containerHeight);\n    const { width: titleWidth, height: titleHeight } = title.getBBox();\n    const [spacingTop, spacingRight, spacingBottom, spacingLeft] = parseSeriesAttr(spacing);\n\n    let [x, y, width, height] = [0, 0, +containerWidth, +containerHeight];\n    const pos = parsePosition(position);\n\n    if (pos.includes('i')) return new BBox(x, y, width, height);\n\n    pos.forEach((p, i) => {\n      if (p === 't')\n        [y, height] =\n          i === 0\n            ? [titleHeight + spacingBottom, +containerHeight - titleHeight - spacingBottom]\n            : [0, +containerHeight];\n      if (p === 'r') [width] = [+containerWidth - titleWidth - spacingLeft];\n      if (p === 'b') [height] = [+containerHeight - titleHeight - spacingTop];\n      if (p === 'l')\n        [x, width] =\n          i === 0 ? [titleWidth + spacingRight, +containerWidth - titleWidth - spacingRight] : [0, +containerWidth];\n    });\n\n    const [insetTop, insetRight, insetBottom, insetLeft] = parseSeriesAttr(inset);\n    const [insetWidth, insetHeight] = [insetLeft + insetRight, insetTop + insetBottom];\n    return new BBox(x + insetLeft, y + insetTop, width - insetWidth, height - insetHeight);\n  }\n\n  public getBBox(): DOMRect {\n    if (this.title) return this.title.getBBox();\n    return new BBox(0, 0, 0, 0);\n  }\n\n  public render(attributes: Required<TitleStyleProps>, container: Group) {\n    const { width, height, position, spacing, ...restStyle } = attributes;\n\n    const [titleStyle] = splitStyle(restStyle);\n    const { x, y, textAlign, textBaseline } = getTitleLayout(attributes);\n\n    ifShow(!!restStyle.text, select(container), (group) => {\n      this.title = group\n        .maybeAppendByClassName(CLASS_NAMES.text, 'text')\n        .styles(titleStyle)\n        .call(mayApplyStyle, { x, y, textAlign, textBaseline })\n        .node();\n    });\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,YAAY;AAGtC,SAASC,IAAI,EAAaC,UAAU,EAAEC,MAAM,EAAEC,eAAe,EAAEC,MAAM,EAAEC,UAAU,QAAQ,YAAY;AAKrG,IAAMC,WAAW,GAAGL,UAAU,CAC5B;EACEM,IAAI,EAAE;CACP,EACD,OAAO,CACR;AAED;;;;;;AAMA,OAAM,SAAUC,aAAaA,CAACC,QAAgB;EAC5C,IAAI,CAAC,UAAU,CAACC,IAAI,CAACD,QAAQ,CAAC,EAAE,OAAOA,QAAQ,CAACE,MAAM,GAAG,CAAC,GAAG,CAACF,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAGA,QAAQ,CAACG,KAAK,CAAC,EAAE,CAAC;EAC/F,OAAOH,QAAQ,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,UAACC,GAAG;IACjC,OAAOA,GAAG,CAAC,CAAC,CAAC;EACf,CAAC,CAAC;AACJ;AAEA;;;;AAIA,OAAM,SAAUC,OAAOA,CAACC,KAAY,EAAEC,OAAsB;EACpD,IAAAC,EAAA,GAAqCF,KAAK,CAACG,UAAuC;IAAhFV,QAAQ,GAAAS,EAAA,CAAAT,QAAA;IAAEW,OAAO,GAAAF,EAAA,CAAAE,OAAA;IAAEC,KAAK,GAAAH,EAAA,CAAAG,KAAA;IAAEd,IAAI,GAAAW,EAAA,CAAAX,IAAkD;EACxF,IAAMe,SAAS,GAAGN,KAAK,CAACD,OAAO,EAAE;EACjC,IAAMQ,WAAW,GAAGN,OAAO,CAACF,OAAO,EAAE;EACrC,IAAMS,GAAG,GAAGhB,aAAa,CAACC,QAAQ,CAAC;EAC7B,IAAAgB,EAAA,GAAAC,MAAA,CAAyDvB,eAAe,CAACI,IAAI,GAAGa,OAAO,GAAG,CAAC,CAAC;IAA3FO,UAAU,GAAAF,EAAA;IAAEG,YAAY,GAAAH,EAAA;IAAEI,aAAa,GAAAJ,EAAA;IAAEK,WAAW,GAAAL,EAAA,GAAuC;EAC5F,IAAAM,EAAA,GAAAL,MAAA,CAAiDvB,eAAe,CAACkB,KAAK,CAAC;IAAtEW,QAAQ,GAAAD,EAAA;IAAEE,UAAU,GAAAF,EAAA;IAAEG,WAAW,GAAAH,EAAA;IAAEI,SAAS,GAAAJ,EAAA,GAA0B;EACvE,IAAAK,EAAA,GAAAV,MAAA,CAAgC,CAACI,WAAW,GAAGF,YAAY,EAAED,UAAU,GAAGE,aAAa,CAAC;IAAvFQ,YAAY,GAAAD,EAAA;IAAEE,aAAa,GAAAF,EAAA,GAA4D;EACxF,IAAAG,EAAA,GAAAb,MAAA,CAA4B,CAACS,SAAS,GAAGF,UAAU,EAAED,QAAQ,GAAGE,WAAW,CAAC;IAA3EM,UAAU,GAAAD,EAAA;IAAEE,WAAW,GAAAF,EAAA,GAAoD;EAElF;EACA;EACA,IAAIf,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClB,OAAO,IAAIxB,IAAI,CACbsB,SAAS,CAACoB,CAAC,EACXpB,SAAS,CAACqB,CAAC,EACXpB,WAAW,CAACqB,KAAK,GAAGtB,SAAS,CAACsB,KAAK,GAAGP,YAAY,GAAGG,UAAU,EAC/DK,IAAI,CAACC,GAAG,CAACvB,WAAW,CAACwB,MAAM,GAAGN,WAAW,EAAEnB,SAAS,CAACyB,MAAM,CAAC,CAC7D;EACH;EACA,IAAIvB,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClB,OAAO,IAAIxB,IAAI,CACbsB,SAAS,CAACoB,CAAC,EACXpB,SAAS,CAACqB,CAAC,EACXE,IAAI,CAACC,GAAG,CAACvB,WAAW,CAACqB,KAAK,GAAGJ,UAAU,EAAElB,SAAS,CAACsB,KAAK,CAAC,EACzDrB,WAAW,CAACwB,MAAM,GAAGzB,SAAS,CAACyB,MAAM,GAAGT,aAAa,GAAGG,WAAW,CACpE;EACH;EACA;EAEM,IAAAO,EAAA,GAAAtB,MAAA,CAAgC,CACpCT,OAAO,CAACE,UAAU,CAACyB,KAAK,IAAIrB,WAAW,CAACqB,KAAK,EAC7C3B,OAAO,CAACE,UAAU,CAAC4B,MAAM,IAAIxB,WAAW,CAACwB,MAAM,CAChD;IAHME,YAAY,GAAAD,EAAA;IAAEE,aAAa,GAAAF,EAAA,GAGjC;EACD,OAAO,IAAIhD,IAAI,CACbuB,WAAW,CAACmB,CAAC,EACbnB,WAAW,CAACoB,CAAC,EACbM,YAAY,GAAG3B,SAAS,CAACsB,KAAK,GAAGP,YAAY,GAAGG,UAAU,EAC1DU,aAAa,GAAG5B,SAAS,CAACyB,MAAM,GAAGT,aAAa,GAAGG,WAAW,CAC/D;AACH;AAEA,SAASU,aAAaA,CAACC,EAAa,EAAEC,KAAU;EAC9C,IAAMC,UAAU,GAAGC,MAAM,CAACC,OAAO,CAACH,KAAK,CAAC,CAACI,MAAM,CAAC,UAACC,GAAG,EAAExC,EAAY;QAAZO,EAAA,GAAAC,MAAA,CAAAR,EAAA,IAAY;MAAXyC,GAAG,GAAAlC,EAAA;MAAEmC,KAAK,GAAAnC,EAAA;IAC/D,IAAMoC,QAAQ,GAAGT,EAAE,CAACU,IAAI,EAAE,CAACC,IAAI,CAACJ,GAAG,CAAC;IACpC,IAAI,CAACE,QAAQ,EAAEH,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;IAC/B,OAAOF,GAAG;EACZ,CAAC,EAAE,EAAyB,CAAC;EAE7BN,EAAE,CAACY,MAAM,CAACV,UAAU,CAAC;AACvB;AAEA,SAASW,cAAcA,CAACC,GAAoB;;EACpC,IAAA3B,EAAA,GAA8B2B,GAAgC;IAA5DtB,KAAK,GAAAL,EAAA,CAAAK,KAAA;IAAEG,MAAM,GAAAR,EAAA,CAAAQ,MAAA;IAAEtC,QAAQ,GAAA8B,EAAA,CAAA9B,QAAqC;EAC9D,IAAAuC,EAAA,GAAAtB,MAAA,CAAW,CAAC,CAACkB,KAAK,GAAG,CAAC,EAAE,CAACG,MAAM,GAAG,CAAC,CAAC;IAAnCoB,EAAE,GAAAnB,EAAA;IAAEoB,EAAE,GAAApB,EAAA,GAA6B;EACtC,IAAAqB,EAAA,GAAA3C,MAAA,CAAkC,CAAC,CAACyC,EAAE,EAAE,CAACC,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC;IAA/D1B,CAAC,GAAA2B,EAAA;IAAE1B,CAAC,GAAA0B,EAAA;IAAEC,SAAS,GAAAD,EAAA;IAAEE,YAAY,GAAAF,EAAA,GAAkC;EACpE,IAAM7C,GAAG,GAAGhB,aAAa,CAACC,QAAQ,CAAC;EAEnC,IAAIe,GAAG,CAACgD,QAAQ,CAAC,GAAG,CAAC,EAAEtD,EAAA,GAAAQ,MAAA,CAAiB,CAAC,CAAC,EAAE,OAAO,CAAC,MAA5BgB,CAAC,GAAAxB,EAAA,KAAEoD,SAAS,GAAApD,EAAA;EACpC,IAAIM,GAAG,CAACgD,QAAQ,CAAC,GAAG,CAAC,EAAE/C,EAAA,GAAAC,MAAA,CAAiB,CAAC,CAACkB,KAAK,EAAE,KAAK,CAAC,MAA/BF,CAAC,GAAAjB,EAAA,KAAE6C,SAAS,GAAA7C,EAAA;EACpC,IAAID,GAAG,CAACgD,QAAQ,CAAC,GAAG,CAAC,EAAEzC,EAAA,GAAAL,MAAA,CAAoB,CAAC,CAAC,EAAE,KAAK,CAAC,MAA7BiB,CAAC,GAAAZ,EAAA,KAAEwC,YAAY,GAAAxC,EAAA;EACvC,IAAIP,GAAG,CAACgD,QAAQ,CAAC,GAAG,CAAC,EAAEpC,EAAA,GAAAV,MAAA,CAAoB,CAAC,CAACqB,MAAM,EAAE,QAAQ,CAAC,MAAtCJ,CAAC,GAAAP,EAAA,KAAEmC,YAAY,GAAAnC,EAAA;EAEvC,OAAO;IAAEM,CAAC,EAAAA,CAAA;IAAEC,CAAC,EAAAA,CAAA;IAAE2B,SAAS,EAAAA,SAAA;IAAEC,YAAY,EAAAA;EAAA,CAAE;AAC1C;AAEA,IAAAE,KAAA,0BAAAC,MAAA;EAA2BC,SAAA,CAAAF,KAAA,EAAAC,MAAA;EAGzB,SAAAD,MAAYG,OAAqB;IAC/B,OAAAF,MAAK,CAAAG,IAAA,OAACD,OAAO,EAAE;MACbrE,IAAI,EAAE,EAAE;MACRqC,KAAK,EAAE,CAAC;MACRG,MAAM,EAAE,CAAC;MACT+B,IAAI,EAAE,SAAS;MACfC,UAAU,EAAE,MAAM;MAClBC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,YAAY;MACxB5D,KAAK,EAAE,CAAC;MACRD,OAAO,EAAE,CAAC;MACVX,QAAQ,EAAE;KACX,CAAC;EACJ;EAEOgE,KAAA,CAAAS,SAAA,CAAAC,iBAAiB,GAAxB;IACE,IAAMC,SAAS,GAAG,IAAI;IAChB,IAAAlE,EAAA,GAMF,IAAI,CAACC,UAAuC;MALvCkE,cAAc,GAAAnE,EAAA,CAAA0B,KAAA;MACb0C,eAAe,GAAApE,EAAA,CAAA6B,MAAA;MACvBtC,QAAQ,GAAAS,EAAA,CAAAT,QAAA;MACRW,OAAO,GAAAF,EAAA,CAAAE,OAAA;MACPC,KAAK,GAAAH,EAAA,CAAAG,KACyC;IAChD,IAAML,KAAK,GAAGoE,SAAS,CAACG,aAAa,CAAgBjF,WAAW,CAACC,IAAI,CAACiF,KAAK,CAAC;IAC5E,IAAI,CAACxE,KAAK,EAAE,OAAO,IAAIhB,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAACqF,cAAc,EAAE,CAACC,eAAe,CAAC;IAC9D,IAAA7D,EAAA,GAA6CT,KAAK,CAACD,OAAO,EAAE;MAAnD0E,UAAU,GAAAhE,EAAA,CAAAmB,KAAA;MAAU8C,WAAW,GAAAjE,EAAA,CAAAsB,MAAoB;IAC5D,IAAAhB,EAAA,GAAAL,MAAA,CAAyDvB,eAAe,CAACiB,OAAO,CAAC;MAAhFO,UAAU,GAAAI,EAAA;MAAEH,YAAY,GAAAG,EAAA;MAAEF,aAAa,GAAAE,EAAA;MAAED,WAAW,GAAAC,EAAA,GAA4B;IAEnF,IAAAK,EAAA,GAAAV,MAAA,CAAwB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC2D,cAAc,EAAE,CAACC,eAAe,CAAC;MAAhE5C,CAAC,GAAAN,EAAA;MAAEO,CAAC,GAAAP,EAAA;MAAEQ,KAAK,GAAAR,EAAA;MAAEW,MAAM,GAAAX,EAAA,GAA6C;IACrE,IAAMZ,GAAG,GAAGhB,aAAa,CAACC,QAAQ,CAAC;IAEnC,IAAIe,GAAG,CAACgD,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,IAAIxE,IAAI,CAAC0C,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAEG,MAAM,CAAC;IAE3DvB,GAAG,CAACmE,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC;;MACf,IAAID,CAAC,KAAK,GAAG,EACX1E,EAAA,GAAAQ,MAAA,CACEmE,CAAC,KAAK,CAAC,GACH,CAACH,WAAW,GAAG7D,aAAa,EAAE,CAACyD,eAAe,GAAGI,WAAW,GAAG7D,aAAa,CAAC,GAC7E,CAAC,CAAC,EAAE,CAACyD,eAAe,CAAC,MAH1B3C,CAAC,GAAAzB,EAAA,KAAE6B,MAAM,GAAA7B,EAAA;MAIZ,IAAI0E,CAAC,KAAK,GAAG,EAAEnE,EAAA,GAAAC,MAAA,CAAU,CAAC,CAAC2D,cAAc,GAAGI,UAAU,GAAG3D,WAAW,CAAC,MAArDc,KAAK,GAAAnB,EAAA;MACrB,IAAImE,CAAC,KAAK,GAAG,EAAE7D,EAAA,GAAAL,MAAA,CAAW,CAAC,CAAC4D,eAAe,GAAGI,WAAW,GAAG/D,UAAU,CAAC,MAAvDoB,MAAM,GAAAhB,EAAA;MACtB,IAAI6D,CAAC,KAAK,GAAG,EACXxD,EAAA,GAAAV,MAAA,CACEmE,CAAC,KAAK,CAAC,GAAG,CAACJ,UAAU,GAAG7D,YAAY,EAAE,CAACyD,cAAc,GAAGI,UAAU,GAAG7D,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,CAACyD,cAAc,CAAC,MAD1G3C,CAAC,GAAAN,EAAA,KAAEQ,KAAK,GAAAR,EAAA;IAEb,CAAC,CAAC;IAEI,IAAAG,EAAA,GAAAb,MAAA,CAAiDvB,eAAe,CAACkB,KAAK,CAAC;MAAtEW,QAAQ,GAAAO,EAAA;MAAEN,UAAU,GAAAM,EAAA;MAAEL,WAAW,GAAAK,EAAA;MAAEJ,SAAS,GAAAI,EAAA,GAA0B;IACvE,IAAAS,EAAA,GAAAtB,MAAA,CAA4B,CAACS,SAAS,GAAGF,UAAU,EAAED,QAAQ,GAAGE,WAAW,CAAC;MAA3EM,UAAU,GAAAQ,EAAA;MAAEP,WAAW,GAAAO,EAAA,GAAoD;IAClF,OAAO,IAAIhD,IAAI,CAAC0C,CAAC,GAAGP,SAAS,EAAEQ,CAAC,GAAGX,QAAQ,EAAEY,KAAK,GAAGJ,UAAU,EAAEO,MAAM,GAAGN,WAAW,CAAC;EACxF,CAAC;EAEMgC,KAAA,CAAAS,SAAA,CAAAnE,OAAO,GAAd;IACE,IAAI,IAAI,CAACC,KAAK,EAAE,OAAO,IAAI,CAACA,KAAK,CAACD,OAAO,EAAE;IAC3C,OAAO,IAAIf,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC7B,CAAC;EAEMyE,KAAA,CAAAS,SAAA,CAAAY,MAAM,GAAb,UAAc3E,UAAqC,EAAEiE,SAAgB;IAArE,IAAAW,KAAA;IACU,IAAAnD,KAAK,GAA8CzB,UAAU,CAAAyB,KAAxD;MAAEG,MAAM,GAAsC5B,UAAU,CAAA4B,MAAhD;MAAEtC,QAAQ,GAA4BU,UAAU,CAAAV,QAAtC;MAAEW,OAAO,GAAmBD,UAAU,CAAAC,OAA7B;MAAK4E,SAAS,GAAAC,MAAA,CAAK9E,UAAU,EAA/D,0CAAkD,CAAF;IAEhD,IAAAD,EAAA,GAAAQ,MAAA,CAAerB,UAAU,CAAC2F,SAAS,CAAC;MAAnCE,UAAU,GAAAhF,EAAA,GAAyB;IACpC,IAAAO,EAAA,GAAoCwC,cAAc,CAAC9C,UAAU,CAAC;MAA5DuB,CAAC,GAAAjB,EAAA,CAAAiB,CAAA;MAAEC,CAAC,GAAAlB,EAAA,CAAAkB,CAAA;MAAE2B,SAAS,GAAA7C,EAAA,CAAA6C,SAAA;MAAEC,YAAY,GAAA9C,EAAA,CAAA8C,YAA+B;IAEpErE,MAAM,CAAC,CAAC,CAAC8F,SAAS,CAACzF,IAAI,EAAEH,MAAM,CAACgF,SAAS,CAAC,EAAE,UAACe,KAAK;MAChDJ,KAAI,CAAC/E,KAAK,GAAGmF,KAAK,CACfC,sBAAsB,CAAC9F,WAAW,CAACC,IAAI,EAAE,MAAM,CAAC,CAChDyD,MAAM,CAACkC,UAAU,CAAC,CAClBrB,IAAI,CAAC1B,aAAa,EAAE;QAAET,CAAC,EAAAA,CAAA;QAAEC,CAAC,EAAAA,CAAA;QAAE2B,SAAS,EAAAA,SAAA;QAAEC,YAAY,EAAAA;MAAA,CAAE,CAAC,CACtDT,IAAI,EAAE;IACX,CAAC,CAAC;EACJ,CAAC;EACH,OAAAW,KAAC;AAAD,CAAC,CA1E0B1E,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}