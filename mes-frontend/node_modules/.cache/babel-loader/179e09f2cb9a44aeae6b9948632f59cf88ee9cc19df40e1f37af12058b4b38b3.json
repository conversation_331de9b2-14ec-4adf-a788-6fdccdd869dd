{"ast": null, "code": "import { parsePathString } from '../parser/parse-path-string';\nimport { getTotalLength } from './get-total-length';\n/**\n * Returns the segment, its index and length as well as\n * the length to that segment at a given length in a path.\n */\nexport function getPropertiesAtLength(pathInput, distance) {\n  var pathArray = parsePathString(pathInput);\n  if (typeof pathArray === 'string') {\n    throw TypeError(pathArray);\n  }\n  var pathTemp = pathArray.slice();\n  var pathLength = getTotalLength(pathTemp);\n  var index = pathTemp.length - 1;\n  var lengthAtSegment = 0;\n  var length = 0;\n  var segment = pathArray[0];\n  var _a = segment.slice(-2),\n    x = _a[0],\n    y = _a[1];\n  var point = {\n    x: x,\n    y: y\n  };\n  // If the path is empty, return 0.\n  if (index <= 0 || !distance || !Number.isFinite(distance)) {\n    return {\n      segment: segment,\n      index: 0,\n      length: length,\n      point: point,\n      lengthAtSegment: lengthAtSegment\n    };\n  }\n  if (distance >= pathLength) {\n    pathTemp = pathArray.slice(0, -1);\n    lengthAtSegment = getTotalLength(pathTemp);\n    length = pathLength - lengthAtSegment;\n    return {\n      segment: pathArray[index],\n      index: index,\n      length: length,\n      lengthAtSegment: lengthAtSegment\n    };\n  }\n  var segments = [];\n  while (index > 0) {\n    segment = pathTemp[index];\n    pathTemp = pathTemp.slice(0, -1);\n    lengthAtSegment = getTotalLength(pathTemp);\n    length = pathLength - lengthAtSegment;\n    pathLength = lengthAtSegment;\n    segments.push({\n      segment: segment,\n      index: index,\n      length: length,\n      lengthAtSegment: lengthAtSegment\n    });\n    index -= 1;\n  }\n  return segments.find(function (_a) {\n    var l = _a.lengthAtSegment;\n    return l <= distance;\n  });\n}", "map": {"version": 3, "names": ["parsePathString", "getTotalLength", "getPropertiesAtLength", "pathInput", "distance", "pathArray", "TypeError", "pathTemp", "slice", "<PERSON><PERSON><PERSON><PERSON>", "index", "length", "lengthAtSegment", "segment", "_a", "x", "y", "point", "Number", "isFinite", "segments", "push", "find", "l"], "sources": ["path/util/get-properties-at-length.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,cAAc,QAAQ,oBAAoB;AAEnD;;;;AAIA,OAAM,SAAUC,qBAAqBA,CAACC,SAA6B,EAAEC,QAAiB;EACpF,IAAMC,SAAS,GAAGL,eAAe,CAACG,SAAS,CAAC;EAE5C,IAAI,OAAOE,SAAS,KAAK,QAAQ,EAAE;IACjC,MAAMC,SAAS,CAACD,SAAS,CAAC;EAC5B;EAEA,IAAIE,QAAQ,GAAcF,SAAS,CAACG,KAAK,EAAe;EACxD,IAAIC,UAAU,GAAGR,cAAc,CAACM,QAAQ,CAAC;EACzC,IAAIG,KAAK,GAAGH,QAAQ,CAACI,MAAM,GAAG,CAAC;EAC/B,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAID,MAAM,GAAG,CAAC;EACd,IAAIE,OAAO,GAAgBR,SAAS,CAAC,CAAC,CAAC;EACjC,IAAAS,EAAA,GAASD,OAAO,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;IAAzBO,CAAC,GAAAD,EAAA;IAAEE,CAAC,GAAAF,EAAA,GAAqB;EAChC,IAAMG,KAAK,GAAG;IAAEF,CAAC,EAAAA,CAAA;IAAEC,CAAC,EAAAA;EAAA,CAAE;EAEtB;EACA,IAAIN,KAAK,IAAI,CAAC,IAAI,CAACN,QAAQ,IAAI,CAACc,MAAM,CAACC,QAAQ,CAACf,QAAQ,CAAC,EAAE;IACzD,OAAO;MACLS,OAAO,EAAAA,OAAA;MACPH,KAAK,EAAE,CAAC;MACRC,MAAM,EAAAA,MAAA;MACNM,KAAK,EAAAA,KAAA;MACLL,eAAe,EAAAA;KAChB;EACH;EAEA,IAAIR,QAAQ,IAAIK,UAAU,EAAE;IAC1BF,QAAQ,GAAGF,SAAS,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAc;IAC9CI,eAAe,GAAGX,cAAc,CAACM,QAAQ,CAAC;IAC1CI,MAAM,GAAGF,UAAU,GAAGG,eAAe;IACrC,OAAO;MACLC,OAAO,EAAER,SAAS,CAACK,KAAK,CAAC;MACzBA,KAAK,EAAAA,KAAA;MACLC,MAAM,EAAAA,MAAA;MACNC,eAAe,EAAAA;KAChB;EACH;EAEA,IAAMQ,QAAQ,GAAG,EAAE;EACnB,OAAOV,KAAK,GAAG,CAAC,EAAE;IAChBG,OAAO,GAAGN,QAAQ,CAACG,KAAK,CAAC;IACzBH,QAAQ,GAAGA,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAc;IAC7CI,eAAe,GAAGX,cAAc,CAACM,QAAQ,CAAC;IAC1CI,MAAM,GAAGF,UAAU,GAAGG,eAAe;IACrCH,UAAU,GAAGG,eAAe;IAC5BQ,QAAQ,CAACC,IAAI,CAAC;MACZR,OAAO,EAAAA,OAAA;MACPH,KAAK,EAAAA,KAAA;MACLC,MAAM,EAAAA,MAAA;MACNC,eAAe,EAAAA;KAChB,CAAC;IACFF,KAAK,IAAI,CAAC;EACZ;EAEA,OAAOU,QAAQ,CAACE,IAAI,CAAC,UAACR,EAAsB;QAAHS,CAAC,GAAAT,EAAA,CAAAF,eAAA;IAAO,OAAAW,CAAC,IAAInB,QAAQ;EAAb,CAAa,CAAC;AACjE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}