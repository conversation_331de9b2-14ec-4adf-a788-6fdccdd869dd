{"ast": null, "code": "var RADIAN = Math.PI / 180;\nvar toRadian = function (degree) {\n  return RADIAN * degree;\n};\nexport default toRadian;", "map": {"version": 3, "names": ["RADIAN", "Math", "PI", "toRadian", "degree"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/to-radian.ts"], "sourcesContent": ["const RADIAN = Math.PI / 180;\n\nconst toRadian = function(degree: number): number {\n  return RADIAN * degree;\n};\n\nexport default toRadian;\n"], "mappings": "AAAA,IAAMA,MAAM,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;AAE5B,IAAMC,QAAQ,GAAG,SAAAA,CAASC,MAAc;EACtC,OAAOJ,MAAM,GAAGI,MAAM;AACxB,CAAC;AAED,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}