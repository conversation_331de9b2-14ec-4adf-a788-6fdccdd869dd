{"ast": null, "code": "var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function (value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\nmodule.exports = baseFilter;", "map": {"version": 3, "names": ["baseEach", "require", "baseFilter", "collection", "predicate", "result", "value", "index", "push", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_baseFilter.js"], "sourcesContent": ["var baseEach = require('./_baseEach');\n\n/**\n * The base implementation of `_.filter` without support for iteratee shorthands.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {Array} Returns the new filtered array.\n */\nfunction baseFilter(collection, predicate) {\n  var result = [];\n  baseEach(collection, function(value, index, collection) {\n    if (predicate(value, index, collection)) {\n      result.push(value);\n    }\n  });\n  return result;\n}\n\nmodule.exports = baseFilter;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,UAAU,EAAEC,SAAS,EAAE;EACzC,IAAIC,MAAM,GAAG,EAAE;EACfL,QAAQ,CAACG,UAAU,EAAE,UAASG,KAAK,EAAEC,KAAK,EAAEJ,UAAU,EAAE;IACtD,IAAIC,SAAS,CAACE,KAAK,EAAEC,KAAK,EAAEJ,UAAU,CAAC,EAAE;MACvCE,MAAM,CAACG,IAAI,CAACF,KAAK,CAAC;IACpB;EACF,CAAC,CAAC;EACF,OAAOD,MAAM;AACf;AAEAI,MAAM,CAACC,OAAO,GAAGR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}