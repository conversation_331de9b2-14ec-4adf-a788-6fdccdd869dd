{"ast": null, "code": "import isObjectLike from './is-object-like';\nimport isArrayLike from './is-array-like';\nimport isString from './is-string';\nvar isEqual = function (value, other) {\n  if (value === other) {\n    return true;\n  }\n  if (!value || !other) {\n    return false;\n  }\n  if (isString(value) || isString(other)) {\n    return false;\n  }\n  if (isArrayLike(value) || isArrayLike(other)) {\n    if (value.length !== other.length) {\n      return false;\n    }\n    var rst = true;\n    for (var i = 0; i < value.length; i++) {\n      rst = isEqual(value[i], other[i]);\n      if (!rst) {\n        break;\n      }\n    }\n    return rst;\n  }\n  if (isObjectLike(value) || isObjectLike(other)) {\n    var valueKeys = Object.keys(value);\n    var otherKeys = Object.keys(other);\n    if (valueKeys.length !== otherKeys.length) {\n      return false;\n    }\n    var rst = true;\n    for (var i = 0; i < valueKeys.length; i++) {\n      rst = isEqual(value[valueKeys[i]], other[valueKeys[i]]);\n      if (!rst) {\n        break;\n      }\n    }\n    return rst;\n  }\n  return false;\n};\nexport default isEqual;", "map": {"version": 3, "names": ["isObjectLike", "isArrayLike", "isString", "isEqual", "value", "other", "length", "rst", "i", "valueKeys", "Object", "keys", "otherKeys"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-equal.ts"], "sourcesContent": ["import isObjectLike from './is-object-like';\nimport isArrayLike from './is-array-like';\nimport isString from './is-string';\n\nconst isEqual = (value: any, other: any): boolean => {\n  if (value === other) {\n    return true;\n  }\n  if (!value || !other) {\n    return false;\n  }\n  if (isString(value) || isString(other)) {\n    return false;\n  }\n  if (isArrayLike(value) || isArrayLike(other)) {\n    if (value.length !== other.length) {\n      return false;\n    }\n    let rst = true;\n    for (let i = 0; i < value.length; i++) {\n      rst = isEqual(value[i], other[i]);\n      if (!rst) {\n        break;\n      }\n    }\n    return rst;\n  }\n  if (isObjectLike(value) || isObjectLike(other)) {\n    const valueKeys = Object.keys(value);\n    const otherKeys = Object.keys(other);\n    if (valueKeys.length !== otherKeys.length) {\n      return false;\n    }\n    let rst = true;\n    for (let i = 0; i < valueKeys.length; i++) {\n      rst = isEqual(value[valueKeys[i]], other[valueKeys[i]]);\n      if (!rst) {\n        break;\n      }\n    }\n    return rst;\n  }\n  return false;\n};\n\nexport default isEqual;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,kBAAkB;AAC3C,OAAOC,WAAW,MAAM,iBAAiB;AACzC,OAAOC,QAAQ,MAAM,aAAa;AAElC,IAAMC,OAAO,GAAG,SAAAA,CAACC,KAAU,EAAEC,KAAU;EACrC,IAAID,KAAK,KAAKC,KAAK,EAAE;IACnB,OAAO,IAAI;;EAEb,IAAI,CAACD,KAAK,IAAI,CAACC,KAAK,EAAE;IACpB,OAAO,KAAK;;EAEd,IAAIH,QAAQ,CAACE,KAAK,CAAC,IAAIF,QAAQ,CAACG,KAAK,CAAC,EAAE;IACtC,OAAO,KAAK;;EAEd,IAAIJ,WAAW,CAACG,KAAK,CAAC,IAAIH,WAAW,CAACI,KAAK,CAAC,EAAE;IAC5C,IAAID,KAAK,CAACE,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE;MACjC,OAAO,KAAK;;IAEd,IAAIC,GAAG,GAAG,IAAI;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,KAAK,CAACE,MAAM,EAAEE,CAAC,EAAE,EAAE;MACrCD,GAAG,GAAGJ,OAAO,CAACC,KAAK,CAACI,CAAC,CAAC,EAAEH,KAAK,CAACG,CAAC,CAAC,CAAC;MACjC,IAAI,CAACD,GAAG,EAAE;QACR;;;IAGJ,OAAOA,GAAG;;EAEZ,IAAIP,YAAY,CAACI,KAAK,CAAC,IAAIJ,YAAY,CAACK,KAAK,CAAC,EAAE;IAC9C,IAAMI,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC;IACpC,IAAMQ,SAAS,GAAGF,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC;IACpC,IAAII,SAAS,CAACH,MAAM,KAAKM,SAAS,CAACN,MAAM,EAAE;MACzC,OAAO,KAAK;;IAEd,IAAIC,GAAG,GAAG,IAAI;IACd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACH,MAAM,EAAEE,CAAC,EAAE,EAAE;MACzCD,GAAG,GAAGJ,OAAO,CAACC,KAAK,CAACK,SAAS,CAACD,CAAC,CAAC,CAAC,EAAEH,KAAK,CAACI,SAAS,CAACD,CAAC,CAAC,CAAC,CAAC;MACvD,IAAI,CAACD,GAAG,EAAE;QACR;;;IAGJ,OAAOA,GAAG;;EAEZ,OAAO,KAAK;AACd,CAAC;AAED,eAAeJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}