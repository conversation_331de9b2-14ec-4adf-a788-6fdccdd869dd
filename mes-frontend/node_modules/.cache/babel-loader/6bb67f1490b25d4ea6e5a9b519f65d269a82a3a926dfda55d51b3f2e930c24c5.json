{"ast": null, "code": "export * from './Text';\nexport * from './Polygon';", "map": {"version": 3, "names": [], "sources": ["/root/mes-system/mes-frontend/node_modules/@ant-design/plots/es/core/annotation/shapes/index.js"], "sourcesContent": ["export * from './Text';\nexport * from './Polygon';\n"], "mappings": "AAAA,cAAc,QAAQ;AACtB,cAAc,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}