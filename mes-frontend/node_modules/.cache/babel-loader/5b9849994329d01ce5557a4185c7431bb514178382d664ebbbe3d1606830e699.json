{"ast": null, "code": "import isFunction from './is-function';\nimport isMatch from './is-match';\nimport isArray from './is-array';\nimport isPlainObject from './is-plain-object';\nfunction find(arr, predicate) {\n  if (!isArray(arr)) return null;\n  var _predicate;\n  if (isFunction(predicate)) {\n    _predicate = predicate;\n  }\n  if (isPlainObject(predicate)) {\n    _predicate = function (a) {\n      return isMatch(a, predicate);\n    };\n  }\n  if (_predicate) {\n    for (var i = 0; i < arr.length; i += 1) {\n      if (_predicate(arr[i])) {\n        return arr[i];\n      }\n    }\n  }\n  return null;\n}\nexport default find;", "map": {"version": 3, "names": ["isFunction", "isMatch", "isArray", "isPlainObject", "find", "arr", "predicate", "_predicate", "a", "i", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/find.ts"], "sourcesContent": ["import isFunction from './is-function';\nimport isMatch from './is-match';\nimport isArray from './is-array';\nimport isPlainObject from './is-plain-object';\n\nfunction find<T>(arr: T[], predicate: Function): T;\nfunction find<T>(arr: T[], predicate: object): T;\n\nfunction find<T>(arr: T[], predicate: Function | object): T {\n  if (!isArray(arr)) return null;\n\n  let _predicate;\n  if (isFunction(predicate)) {\n    _predicate = predicate;\n  }\n  if (isPlainObject(predicate)) {\n    _predicate = a => isMatch(a, predicate);\n  }\n  if (_predicate) {\n    for (let i = 0; i < arr.length; i += 1) {\n      if (_predicate(arr[i])) {\n        return arr[i];\n      }\n    }\n  }\n  return null;\n}\n\nexport default find;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,eAAe;AACtC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,OAAO,MAAM,YAAY;AAChC,OAAOC,aAAa,MAAM,mBAAmB;AAK7C,SAASC,IAAIA,CAAIC,GAAQ,EAAEC,SAA4B;EACrD,IAAI,CAACJ,OAAO,CAACG,GAAG,CAAC,EAAE,OAAO,IAAI;EAE9B,IAAIE,UAAU;EACd,IAAIP,UAAU,CAACM,SAAS,CAAC,EAAE;IACzBC,UAAU,GAAGD,SAAS;;EAExB,IAAIH,aAAa,CAACG,SAAS,CAAC,EAAE;IAC5BC,UAAU,GAAG,SAAAA,CAAAC,CAAC;MAAI,OAAAP,OAAO,CAACO,CAAC,EAAEF,SAAS,CAAC;IAArB,CAAqB;;EAEzC,IAAIC,UAAU,EAAE;IACd,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACtC,IAAIF,UAAU,CAACF,GAAG,CAACI,CAAC,CAAC,CAAC,EAAE;QACtB,OAAOJ,GAAG,CAACI,CAAC,CAAC;;;;EAInB,OAAO,IAAI;AACb;AAEA,eAAeL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}