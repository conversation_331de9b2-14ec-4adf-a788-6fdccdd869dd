{"ast": null, "code": "import isNumber from './is-number';\nvar isInteger = Number.isInteger ? Number.isInteger : function (num) {\n  return isNumber(num) && num % 1 === 0;\n};\nexport default isInteger;", "map": {"version": 3, "names": ["isNumber", "isInteger", "Number", "num"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-integer.ts"], "sourcesContent": ["import isNumber from './is-number';\n\nconst isInteger = Number.isInteger ? Number.isInteger : function(num: any): boolean {\n  return isNumber(num) && num % 1 === 0;\n};\n\nexport default isInteger;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAElC,IAAMC,SAAS,GAAGC,MAAM,CAACD,SAAS,GAAGC,MAAM,CAACD,SAAS,GAAG,UAASE,GAAQ;EACvE,OAAOH,QAAQ,CAACG,GAAG,CAAC,IAAIA,GAAG,GAAG,CAAC,KAAK,CAAC;AACvC,CAAC;AAED,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}