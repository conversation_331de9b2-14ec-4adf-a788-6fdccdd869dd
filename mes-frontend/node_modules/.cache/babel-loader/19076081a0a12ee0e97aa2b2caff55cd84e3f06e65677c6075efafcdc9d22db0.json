{"ast": null, "code": "function defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\nfunction meanX(children) {\n  return children.reduce(meanXReduce, 0) / children.length;\n}\nfunction meanXReduce(x, c) {\n  return x + c.x;\n}\nfunction maxY(children) {\n  return 1 + children.reduce(maxYReduce, 0);\n}\nfunction maxYReduce(y, c) {\n  return Math.max(y, c.y);\n}\nfunction leafLeft(node) {\n  var children;\n  while (children = node.children) node = children[0];\n  return node;\n}\nfunction leafRight(node) {\n  var children;\n  while (children = node.children) node = children[children.length - 1];\n  return node;\n}\nexport default function () {\n  var separation = defaultSeparation,\n    dx = 1,\n    dy = 1,\n    nodeSize = false;\n  function cluster(root) {\n    var previousNode,\n      x = 0;\n\n    // First walk, computing the initial x & y values.\n    root.eachAfter(function (node) {\n      var children = node.children;\n      if (children) {\n        node.x = meanX(children);\n        node.y = maxY(children);\n      } else {\n        node.x = previousNode ? x += separation(node, previousNode) : 0;\n        node.y = 0;\n        previousNode = node;\n      }\n    });\n    var left = leafLeft(root),\n      right = leafRight(root),\n      x0 = left.x - separation(left, right) / 2,\n      x1 = right.x + separation(right, left) / 2;\n\n    // Second walk, normalizing x & y to the desired size.\n    return root.eachAfter(nodeSize ? function (node) {\n      node.x = (node.x - root.x) * dx;\n      node.y = (root.y - node.y) * dy;\n    } : function (node) {\n      node.x = (node.x - x0) / (x1 - x0) * dx;\n      node.y = (1 - (root.y ? node.y / root.y : 1)) * dy;\n    });\n  }\n  cluster.separation = function (x) {\n    return arguments.length ? (separation = x, cluster) : separation;\n  };\n  cluster.size = function (x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], cluster) : nodeSize ? null : [dx, dy];\n  };\n  cluster.nodeSize = function (x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], cluster) : nodeSize ? [dx, dy] : null;\n  };\n  return cluster;\n}", "map": {"version": 3, "names": ["defaultSeparation", "a", "b", "parent", "meanX", "children", "reduce", "meanXReduce", "length", "x", "c", "maxY", "maxYReduce", "y", "Math", "max", "leafLeft", "node", "leafRight", "separation", "dx", "dy", "nodeSize", "cluster", "root", "previousNode", "eachAfter", "left", "right", "x0", "x1", "arguments", "size"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/cluster.js"], "sourcesContent": ["function defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\nfunction meanX(children) {\n  return children.reduce(meanXReduce, 0) / children.length;\n}\n\nfunction meanXReduce(x, c) {\n  return x + c.x;\n}\n\nfunction maxY(children) {\n  return 1 + children.reduce(maxYReduce, 0);\n}\n\nfunction maxYReduce(y, c) {\n  return Math.max(y, c.y);\n}\n\nfunction leafLeft(node) {\n  var children;\n  while (children = node.children) node = children[0];\n  return node;\n}\n\nfunction leafRight(node) {\n  var children;\n  while (children = node.children) node = children[children.length - 1];\n  return node;\n}\n\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = false;\n\n  function cluster(root) {\n    var previousNode,\n        x = 0;\n\n    // First walk, computing the initial x & y values.\n    root.eachAfter(function(node) {\n      var children = node.children;\n      if (children) {\n        node.x = meanX(children);\n        node.y = maxY(children);\n      } else {\n        node.x = previousNode ? x += separation(node, previousNode) : 0;\n        node.y = 0;\n        previousNode = node;\n      }\n    });\n\n    var left = leafLeft(root),\n        right = leafRight(root),\n        x0 = left.x - separation(left, right) / 2,\n        x1 = right.x + separation(right, left) / 2;\n\n    // Second walk, normalizing x & y to the desired size.\n    return root.eachAfter(nodeSize ? function(node) {\n      node.x = (node.x - root.x) * dx;\n      node.y = (root.y - node.y) * dy;\n    } : function(node) {\n      node.x = (node.x - x0) / (x1 - x0) * dx;\n      node.y = (1 - (root.y ? node.y / root.y : 1)) * dy;\n    });\n  }\n\n  cluster.separation = function(x) {\n    return arguments.length ? (separation = x, cluster) : separation;\n  };\n\n  cluster.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? null : [dx, dy]);\n  };\n\n  cluster.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], cluster) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return cluster;\n}\n"], "mappings": "AAAA,SAASA,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC/B,OAAOD,CAAC,CAACE,MAAM,KAAKD,CAAC,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC;AACtC;AAEA,SAASC,KAAKA,CAACC,QAAQ,EAAE;EACvB,OAAOA,QAAQ,CAACC,MAAM,CAACC,WAAW,EAAE,CAAC,CAAC,GAAGF,QAAQ,CAACG,MAAM;AAC1D;AAEA,SAASD,WAAWA,CAACE,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOD,CAAC,GAAGC,CAAC,CAACD,CAAC;AAChB;AAEA,SAASE,IAAIA,CAACN,QAAQ,EAAE;EACtB,OAAO,CAAC,GAAGA,QAAQ,CAACC,MAAM,CAACM,UAAU,EAAE,CAAC,CAAC;AAC3C;AAEA,SAASA,UAAUA,CAACC,CAAC,EAAEH,CAAC,EAAE;EACxB,OAAOI,IAAI,CAACC,GAAG,CAACF,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC;AACzB;AAEA,SAASG,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIZ,QAAQ;EACZ,OAAOA,QAAQ,GAAGY,IAAI,CAACZ,QAAQ,EAAEY,IAAI,GAAGZ,QAAQ,CAAC,CAAC,CAAC;EACnD,OAAOY,IAAI;AACb;AAEA,SAASC,SAASA,CAACD,IAAI,EAAE;EACvB,IAAIZ,QAAQ;EACZ,OAAOA,QAAQ,GAAGY,IAAI,CAACZ,QAAQ,EAAEY,IAAI,GAAGZ,QAAQ,CAACA,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC;EACrE,OAAOS,IAAI;AACb;AAEA,eAAe,YAAW;EACxB,IAAIE,UAAU,GAAGnB,iBAAiB;IAC9BoB,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,QAAQ,GAAG,KAAK;EAEpB,SAASC,OAAOA,CAACC,IAAI,EAAE;IACrB,IAAIC,YAAY;MACZhB,CAAC,GAAG,CAAC;;IAET;IACAe,IAAI,CAACE,SAAS,CAAC,UAAST,IAAI,EAAE;MAC5B,IAAIZ,QAAQ,GAAGY,IAAI,CAACZ,QAAQ;MAC5B,IAAIA,QAAQ,EAAE;QACZY,IAAI,CAACR,CAAC,GAAGL,KAAK,CAACC,QAAQ,CAAC;QACxBY,IAAI,CAACJ,CAAC,GAAGF,IAAI,CAACN,QAAQ,CAAC;MACzB,CAAC,MAAM;QACLY,IAAI,CAACR,CAAC,GAAGgB,YAAY,GAAGhB,CAAC,IAAIU,UAAU,CAACF,IAAI,EAAEQ,YAAY,CAAC,GAAG,CAAC;QAC/DR,IAAI,CAACJ,CAAC,GAAG,CAAC;QACVY,YAAY,GAAGR,IAAI;MACrB;IACF,CAAC,CAAC;IAEF,IAAIU,IAAI,GAAGX,QAAQ,CAACQ,IAAI,CAAC;MACrBI,KAAK,GAAGV,SAAS,CAACM,IAAI,CAAC;MACvBK,EAAE,GAAGF,IAAI,CAAClB,CAAC,GAAGU,UAAU,CAACQ,IAAI,EAAEC,KAAK,CAAC,GAAG,CAAC;MACzCE,EAAE,GAAGF,KAAK,CAACnB,CAAC,GAAGU,UAAU,CAACS,KAAK,EAAED,IAAI,CAAC,GAAG,CAAC;;IAE9C;IACA,OAAOH,IAAI,CAACE,SAAS,CAACJ,QAAQ,GAAG,UAASL,IAAI,EAAE;MAC9CA,IAAI,CAACR,CAAC,GAAG,CAACQ,IAAI,CAACR,CAAC,GAAGe,IAAI,CAACf,CAAC,IAAIW,EAAE;MAC/BH,IAAI,CAACJ,CAAC,GAAG,CAACW,IAAI,CAACX,CAAC,GAAGI,IAAI,CAACJ,CAAC,IAAIQ,EAAE;IACjC,CAAC,GAAG,UAASJ,IAAI,EAAE;MACjBA,IAAI,CAACR,CAAC,GAAG,CAACQ,IAAI,CAACR,CAAC,GAAGoB,EAAE,KAAKC,EAAE,GAAGD,EAAE,CAAC,GAAGT,EAAE;MACvCH,IAAI,CAACJ,CAAC,GAAG,CAAC,CAAC,IAAIW,IAAI,CAACX,CAAC,GAAGI,IAAI,CAACJ,CAAC,GAAGW,IAAI,CAACX,CAAC,GAAG,CAAC,CAAC,IAAIQ,EAAE;IACpD,CAAC,CAAC;EACJ;EAEAE,OAAO,CAACJ,UAAU,GAAG,UAASV,CAAC,EAAE;IAC/B,OAAOsB,SAAS,CAACvB,MAAM,IAAIW,UAAU,GAAGV,CAAC,EAAEc,OAAO,IAAIJ,UAAU;EAClE,CAAC;EAEDI,OAAO,CAACS,IAAI,GAAG,UAASvB,CAAC,EAAE;IACzB,OAAOsB,SAAS,CAACvB,MAAM,IAAIc,QAAQ,GAAG,KAAK,EAAEF,EAAE,GAAG,CAACX,CAAC,CAAC,CAAC,CAAC,EAAEY,EAAE,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,EAAEc,OAAO,IAAKD,QAAQ,GAAG,IAAI,GAAG,CAACF,EAAE,EAAEC,EAAE,CAAE;EAC9G,CAAC;EAEDE,OAAO,CAACD,QAAQ,GAAG,UAASb,CAAC,EAAE;IAC7B,OAAOsB,SAAS,CAACvB,MAAM,IAAIc,QAAQ,GAAG,IAAI,EAAEF,EAAE,GAAG,CAACX,CAAC,CAAC,CAAC,CAAC,EAAEY,EAAE,GAAG,CAACZ,CAAC,CAAC,CAAC,CAAC,EAAEc,OAAO,IAAKD,QAAQ,GAAG,CAACF,EAAE,EAAEC,EAAE,CAAC,GAAG,IAAK;EAC7G,CAAC;EAED,OAAOE,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}