{"ast": null, "code": "/**\n * 是否是布尔类型\n *\n * @param {Object} value 测试的值\n * @return {Boolean}\n */\nimport isType from './is-type';\nvar isBoolean = function (value) {\n  return isType(value, 'Boolean');\n};\nexport default isBoolean;", "map": {"version": 3, "names": ["isType", "isBoolean", "value"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-boolean.ts"], "sourcesContent": ["/**\n * 是否是布尔类型\n *\n * @param {Object} value 测试的值\n * @return {Boolean}\n */\nimport isType from './is-type';\n\nconst isBoolean = function(value: any): value is boolean {\n  return isType(value, 'Boolean');\n};\n\nexport default isBoolean;\n"], "mappings": "AAAA;;;;;;AAMA,OAAOA,MAAM,MAAM,WAAW;AAE9B,IAAMC,SAAS,GAAG,SAAAA,CAASC,KAAU;EACnC,OAAOF,MAAM,CAACE,KAAK,EAAE,SAAS,CAAC;AACjC,CAAC;AAED,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}