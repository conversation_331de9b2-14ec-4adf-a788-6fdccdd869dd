{"ast": null, "code": "import { Adder } from \"d3-array\";\nimport { abs } from \"../math.js\";\nimport noop from \"../noop.js\";\nvar areaSum = new Adder(),\n  areaRingSum = new Adder(),\n  x00,\n  y00,\n  x0,\n  y0;\nvar areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function () {\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function () {\n    areaStream.lineStart = areaStream.lineEnd = areaStream.point = noop;\n    areaSum.add(abs(areaRingSum));\n    areaRingSum = new Adder();\n  },\n  result: function () {\n    var area = areaSum / 2;\n    areaSum = new Adder();\n    return area;\n  }\n};\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\nfunction areaPointFirst(x, y) {\n  areaStream.point = areaPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\nfunction areaPoint(x, y) {\n  areaRingSum.add(y0 * x - x0 * y);\n  x0 = x, y0 = y;\n}\nfunction areaRingEnd() {\n  areaPoint(x00, y00);\n}\nexport default areaStream;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "abs", "noop", "areaSum", "areaRingSum", "x00", "y00", "x0", "y0", "areaStream", "point", "lineStart", "lineEnd", "polygonStart", "areaRingStart", "areaRingEnd", "polygonEnd", "add", "result", "area", "areaPointFirst", "x", "y", "areaPoint"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-geo/src/path/area.js"], "sourcesContent": ["import {Adder} from \"d3-array\";\nimport {abs} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar areaSum = new Adder(),\n    areaRingSum = new Adder(),\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    areaStream.lineStart = areaStream.lineEnd = areaStream.point = noop;\n    areaSum.add(abs(areaRingSum));\n    areaRingSum = new Adder();\n  },\n  result: function() {\n    var area = areaSum / 2;\n    areaSum = new Adder();\n    return area;\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaPointFirst(x, y) {\n  areaStream.point = areaPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction areaPoint(x, y) {\n  areaRingSum.add(y0 * x - x0 * y);\n  x0 = x, y0 = y;\n}\n\nfunction areaRingEnd() {\n  areaPoint(x00, y00);\n}\n\nexport default areaStream;\n"], "mappings": "AAAA,SAAQA,KAAK,QAAO,UAAU;AAC9B,SAAQC,GAAG,QAAO,YAAY;AAC9B,OAAOC,IAAI,MAAM,YAAY;AAE7B,IAAIC,OAAO,GAAG,IAAIH,KAAK,CAAC,CAAC;EACrBI,WAAW,GAAG,IAAIJ,KAAK,CAAC,CAAC;EACzBK,GAAG;EACHC,GAAG;EACHC,EAAE;EACFC,EAAE;AAEN,IAAIC,UAAU,GAAG;EACfC,KAAK,EAAER,IAAI;EACXS,SAAS,EAAET,IAAI;EACfU,OAAO,EAAEV,IAAI;EACbW,YAAY,EAAE,SAAAA,CAAA,EAAW;IACvBJ,UAAU,CAACE,SAAS,GAAGG,aAAa;IACpCL,UAAU,CAACG,OAAO,GAAGG,WAAW;EAClC,CAAC;EACDC,UAAU,EAAE,SAAAA,CAAA,EAAW;IACrBP,UAAU,CAACE,SAAS,GAAGF,UAAU,CAACG,OAAO,GAAGH,UAAU,CAACC,KAAK,GAAGR,IAAI;IACnEC,OAAO,CAACc,GAAG,CAAChB,GAAG,CAACG,WAAW,CAAC,CAAC;IAC7BA,WAAW,GAAG,IAAIJ,KAAK,CAAC,CAAC;EAC3B,CAAC;EACDkB,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,IAAIC,IAAI,GAAGhB,OAAO,GAAG,CAAC;IACtBA,OAAO,GAAG,IAAIH,KAAK,CAAC,CAAC;IACrB,OAAOmB,IAAI;EACb;AACF,CAAC;AAED,SAASL,aAAaA,CAAA,EAAG;EACvBL,UAAU,CAACC,KAAK,GAAGU,cAAc;AACnC;AAEA,SAASA,cAAcA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC5Bb,UAAU,CAACC,KAAK,GAAGa,SAAS;EAC5BlB,GAAG,GAAGE,EAAE,GAAGc,CAAC,EAAEf,GAAG,GAAGE,EAAE,GAAGc,CAAC;AAC5B;AAEA,SAASC,SAASA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACvBlB,WAAW,CAACa,GAAG,CAACT,EAAE,GAAGa,CAAC,GAAGd,EAAE,GAAGe,CAAC,CAAC;EAChCf,EAAE,GAAGc,CAAC,EAAEb,EAAE,GAAGc,CAAC;AAChB;AAEA,SAASP,WAAWA,CAAA,EAAG;EACrBQ,SAAS,CAAClB,GAAG,EAAEC,GAAG,CAAC;AACrB;AAEA,eAAeG,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}