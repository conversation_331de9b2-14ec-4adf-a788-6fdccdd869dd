{"ast": null, "code": "/* global window */\n\nvar lodash;\nif (typeof require === \"function\") {\n  try {\n    lodash = {\n      clone: require(\"lodash/clone\"),\n      constant: require(\"lodash/constant\"),\n      each: require(\"lodash/each\"),\n      filter: require(\"lodash/filter\"),\n      has: require(\"lodash/has\"),\n      isArray: require(\"lodash/isArray\"),\n      isEmpty: require(\"lodash/isEmpty\"),\n      isFunction: require(\"lodash/isFunction\"),\n      isUndefined: require(\"lodash/isUndefined\"),\n      keys: require(\"lodash/keys\"),\n      map: require(\"lodash/map\"),\n      reduce: require(\"lodash/reduce\"),\n      size: require(\"lodash/size\"),\n      transform: require(\"lodash/transform\"),\n      union: require(\"lodash/union\"),\n      values: require(\"lodash/values\")\n    };\n  } catch (e) {\n    // continue regardless of error\n  }\n}\nif (!lodash) {\n  lodash = window._;\n}\nmodule.exports = lodash;", "map": {"version": 3, "names": ["lodash", "require", "clone", "constant", "each", "filter", "has", "isArray", "isEmpty", "isFunction", "isUndefined", "keys", "map", "reduce", "size", "transform", "union", "values", "e", "window", "_", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/lodash.js"], "sourcesContent": ["/* global window */\n\nvar lodash;\n\nif (typeof require === \"function\") {\n  try {\n    lodash = {\n      clone: require(\"lodash/clone\"),\n      constant: require(\"lodash/constant\"),\n      each: require(\"lodash/each\"),\n      filter: require(\"lodash/filter\"),\n      has:  require(\"lodash/has\"),\n      isArray: require(\"lodash/isArray\"),\n      isEmpty: require(\"lodash/isEmpty\"),\n      isFunction: require(\"lodash/isFunction\"),\n      isUndefined: require(\"lodash/isUndefined\"),\n      keys: require(\"lodash/keys\"),\n      map: require(\"lodash/map\"),\n      reduce: require(\"lodash/reduce\"),\n      size: require(\"lodash/size\"),\n      transform: require(\"lodash/transform\"),\n      union: require(\"lodash/union\"),\n      values: require(\"lodash/values\")\n    };\n  } catch (e) {\n    // continue regardless of error\n  }\n}\n\nif (!lodash) {\n  lodash = window._;\n}\n\nmodule.exports = lodash;\n"], "mappings": "AAAA;;AAEA,IAAIA,MAAM;AAEV,IAAI,OAAOC,OAAO,KAAK,UAAU,EAAE;EACjC,IAAI;IACFD,MAAM,GAAG;MACPE,KAAK,EAAED,OAAO,CAAC,cAAc,CAAC;MAC9BE,QAAQ,EAAEF,OAAO,CAAC,iBAAiB,CAAC;MACpCG,IAAI,EAAEH,OAAO,CAAC,aAAa,CAAC;MAC5BI,MAAM,EAAEJ,OAAO,CAAC,eAAe,CAAC;MAChCK,GAAG,EAAGL,OAAO,CAAC,YAAY,CAAC;MAC3BM,OAAO,EAAEN,OAAO,CAAC,gBAAgB,CAAC;MAClCO,OAAO,EAAEP,OAAO,CAAC,gBAAgB,CAAC;MAClCQ,UAAU,EAAER,OAAO,CAAC,mBAAmB,CAAC;MACxCS,WAAW,EAAET,OAAO,CAAC,oBAAoB,CAAC;MAC1CU,IAAI,EAAEV,OAAO,CAAC,aAAa,CAAC;MAC5BW,GAAG,EAAEX,OAAO,CAAC,YAAY,CAAC;MAC1BY,MAAM,EAAEZ,OAAO,CAAC,eAAe,CAAC;MAChCa,IAAI,EAAEb,OAAO,CAAC,aAAa,CAAC;MAC5Bc,SAAS,EAAEd,OAAO,CAAC,kBAAkB,CAAC;MACtCe,KAAK,EAAEf,OAAO,CAAC,cAAc,CAAC;MAC9BgB,MAAM,EAAEhB,OAAO,CAAC,eAAe;IACjC,CAAC;EACH,CAAC,CAAC,OAAOiB,CAAC,EAAE;IACV;EAAA;AAEJ;AAEA,IAAI,CAAClB,MAAM,EAAE;EACXA,MAAM,GAAGmB,MAAM,CAACC,CAAC;AACnB;AAEAC,MAAM,CAACC,OAAO,GAAGtB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}