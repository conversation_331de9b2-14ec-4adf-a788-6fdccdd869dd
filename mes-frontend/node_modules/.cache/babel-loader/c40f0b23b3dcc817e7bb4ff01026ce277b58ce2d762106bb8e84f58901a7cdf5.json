{"ast": null, "code": "import { __read, __values } from \"tslib\";\nimport { getBounds } from './bounds';\n/**\n * Detect whether line-line collision.\n * From: https://stackoverflow.com/questions/563198/how-do-you-detect-where-two-line-segments-intersect\n */\nfunction lineToLine(line1, line2) {\n  var _a = __read(line1, 4),\n    x0 = _a[0],\n    y0 = _a[1],\n    x1 = _a[2],\n    y1 = _a[3];\n  var _b = __read(line2, 4),\n    x2 = _b[0],\n    y2 = _b[1],\n    x3 = _b[2],\n    y3 = _b[3];\n  var s10x = x1 - x0;\n  var s10y = y1 - y0;\n  var s32x = x3 - x2;\n  var s32y = y3 - y2;\n  var denom = s10x * s32y - s32x * s10y;\n  if (denom === 0) return false;\n  var denomPositive = denom > 0;\n  var s02x = x0 - x2;\n  var s02y = y0 - y2;\n  var sNum = s10x * s02y - s10y * s02x;\n  if (sNum < 0 === denomPositive) return false;\n  var tNum = s32x * s02y - s32y * s02x;\n  if (tNum < 0 === denomPositive) return false;\n  if (sNum > denom === denomPositive || tNum > denom === denomPositive) return false;\n  return true;\n}\nfunction intersectBoxLine(box /** 八个顶点 */, line) {\n  var lines = [[box[0], box[1], box[2], box[3]], [box[2], box[3], box[4], box[5]], [box[4], box[5], box[6], box[7]], [box[6], box[7], box[0], box[1]]];\n  return lines.some(function (boxLine) {\n    return lineToLine(line, boxLine);\n  });\n}\nexport var IntersectUtils = {\n  lineToLine: lineToLine,\n  intersectBoxLine: intersectBoxLine,\n  getBounds: getBounds\n};\n/**\n * 检测两个 DisplayObject 是否相交\n */\nexport function intersect(a, b, margin) {\n  var e_1, _a;\n  var p = getBounds(a, margin).flat(1);\n  var q = getBounds(b, margin).flat(1);\n  var linesP = [[p[0], p[1], p[2], p[3]], [p[0], p[1], p[4], p[5]], [p[4], p[5], p[6], p[7]], [p[2], p[3], p[6], p[7]]];\n  try {\n    for (var linesP_1 = __values(linesP), linesP_1_1 = linesP_1.next(); !linesP_1_1.done; linesP_1_1 = linesP_1.next()) {\n      var line = linesP_1_1.value;\n      if (intersectBoxLine(q, line)) return true;\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (linesP_1_1 && !linesP_1_1.done && (_a = linesP_1.return)) _a.call(linesP_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  return false;\n}", "map": {"version": 3, "names": ["getBounds", "lineToLine", "line1", "line2", "_a", "__read", "x0", "y0", "x1", "y1", "_b", "x2", "y2", "x3", "y3", "s10x", "s10y", "s32x", "s32y", "denom", "denomPositive", "s02x", "s02y", "sNum", "tNum", "intersectBoxLine", "box", "line", "lines", "some", "boxLine", "IntersectUtils", "intersect", "a", "b", "margin", "p", "flat", "q", "linesP", "linesP_1", "__values", "linesP_1_1", "next", "done", "value"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/utils/intersect.ts"], "sourcesContent": ["import type { DisplayObject } from '../../../shapes';\nimport { getBounds } from './bounds';\n\n/**\n * Detect whether line-line collision.\n * From: https://stackoverflow.com/questions/563198/how-do-you-detect-where-two-line-segments-intersect\n */\nfunction lineToLine(line1: number[], line2: number[]) {\n  const [x0, y0, x1, y1] = line1;\n  const [x2, y2, x3, y3] = line2;\n  const s10x = x1 - x0;\n  const s10y = y1 - y0;\n  const s32x = x3 - x2;\n  const s32y = y3 - y2;\n\n  const denom = s10x * s32y - s32x * s10y;\n  if (denom === 0) return false;\n  const denomPositive = denom > 0;\n\n  const s02x = x0 - x2;\n  const s02y = y0 - y2;\n  const sNum = s10x * s02y - s10y * s02x;\n  if (sNum < 0 === denomPositive) return false;\n\n  const tNum = s32x * s02y - s32y * s02x;\n  if (tNum < 0 === denomPositive) return false;\n\n  if (sNum > denom === denomPositive || tNum > denom === denomPositive) return false;\n\n  return true;\n}\n\nfunction intersectBoxLine(box: number[] /** 八个顶点 */, line: number[]): boolean {\n  const lines = [\n    [box[0], box[1], box[2], box[3]],\n    [box[2], box[3], box[4], box[5]],\n    [box[4], box[5], box[6], box[7]],\n    [box[6], box[7], box[0], box[1]],\n  ];\n\n  return lines.some((boxLine) => lineToLine(line, boxLine));\n}\n\nexport const IntersectUtils = { lineToLine, intersectBoxLine, getBounds };\n\n/**\n * 检测两个 DisplayObject 是否相交\n */\nexport function intersect(a: DisplayObject<any>, b: DisplayObject<any>, margin?: number[]) {\n  const p = getBounds(a, margin).flat(1);\n  const q = getBounds(b, margin).flat(1);\n\n  const linesP = [\n    [p[0], p[1], p[2], p[3]],\n    [p[0], p[1], p[4], p[5]],\n    [p[4], p[5], p[6], p[7]],\n    [p[2], p[3], p[6], p[7]],\n  ];\n\n  for (const line of linesP) {\n    if (intersectBoxLine(q, line)) return true;\n  }\n\n  return false;\n}\n"], "mappings": ";AACA,SAASA,SAAS,QAAQ,UAAU;AAEpC;;;;AAIA,SAASC,UAAUA,CAACC,KAAe,EAAEC,KAAe;EAC5C,IAAAC,EAAA,GAAAC,MAAA,CAAmBH,KAAK;IAAvBI,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA;IAAEI,EAAE,GAAAJ,EAAA;IAAEK,EAAE,GAAAL,EAAA,GAAS;EACxB,IAAAM,EAAA,GAAAL,MAAA,CAAmBF,KAAK;IAAvBQ,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA;IAAEI,EAAE,GAAAJ,EAAA,GAAS;EAC9B,IAAMK,IAAI,GAAGP,EAAE,GAAGF,EAAE;EACpB,IAAMU,IAAI,GAAGP,EAAE,GAAGF,EAAE;EACpB,IAAMU,IAAI,GAAGJ,EAAE,GAAGF,EAAE;EACpB,IAAMO,IAAI,GAAGJ,EAAE,GAAGF,EAAE;EAEpB,IAAMO,KAAK,GAAGJ,IAAI,GAAGG,IAAI,GAAGD,IAAI,GAAGD,IAAI;EACvC,IAAIG,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK;EAC7B,IAAMC,aAAa,GAAGD,KAAK,GAAG,CAAC;EAE/B,IAAME,IAAI,GAAGf,EAAE,GAAGK,EAAE;EACpB,IAAMW,IAAI,GAAGf,EAAE,GAAGK,EAAE;EACpB,IAAMW,IAAI,GAAGR,IAAI,GAAGO,IAAI,GAAGN,IAAI,GAAGK,IAAI;EACtC,IAAIE,IAAI,GAAG,CAAC,KAAKH,aAAa,EAAE,OAAO,KAAK;EAE5C,IAAMI,IAAI,GAAGP,IAAI,GAAGK,IAAI,GAAGJ,IAAI,GAAGG,IAAI;EACtC,IAAIG,IAAI,GAAG,CAAC,KAAKJ,aAAa,EAAE,OAAO,KAAK;EAE5C,IAAIG,IAAI,GAAGJ,KAAK,KAAKC,aAAa,IAAII,IAAI,GAAGL,KAAK,KAAKC,aAAa,EAAE,OAAO,KAAK;EAElF,OAAO,IAAI;AACb;AAEA,SAASK,gBAAgBA,CAACC,GAAa,CAAC,aAAaC,IAAc;EACjE,IAAMC,KAAK,GAAG,CACZ,CAACF,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,EAChC,CAACA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,EAChC,CAACA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,EAChC,CAACA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,EAAEA,GAAG,CAAC,CAAC,CAAC,CAAC,CACjC;EAED,OAAOE,KAAK,CAACC,IAAI,CAAC,UAACC,OAAO;IAAK,OAAA7B,UAAU,CAAC0B,IAAI,EAAEG,OAAO,CAAC;EAAzB,CAAyB,CAAC;AAC3D;AAEA,OAAO,IAAMC,cAAc,GAAG;EAAE9B,UAAU,EAAAA,UAAA;EAAEwB,gBAAgB,EAAAA,gBAAA;EAAEzB,SAAS,EAAAA;AAAA,CAAE;AAEzE;;;AAGA,OAAM,SAAUgC,SAASA,CAACC,CAAqB,EAAEC,CAAqB,EAAEC,MAAiB;;EACvF,IAAMC,CAAC,GAAGpC,SAAS,CAACiC,CAAC,EAAEE,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACtC,IAAMC,CAAC,GAAGtC,SAAS,CAACkC,CAAC,EAAEC,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EAEtC,IAAME,MAAM,GAAG,CACb,CAACH,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EACxB,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EACxB,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,EACxB,CAACA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CACzB;;IAED,KAAmB,IAAAI,QAAA,GAAAC,QAAA,CAAAF,MAAM,GAAAG,UAAA,GAAAF,QAAA,CAAAG,IAAA,KAAAD,UAAA,CAAAE,IAAA,EAAAF,UAAA,GAAAF,QAAA,CAAAG,IAAA,IAAE;MAAtB,IAAMhB,IAAI,GAAAe,UAAA,CAAAG,KAAA;MACb,IAAIpB,gBAAgB,CAACa,CAAC,EAAEX,IAAI,CAAC,EAAE,OAAO,IAAI;IAC5C;;;;;;;;;;;;EAEA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}