{"ast": null, "code": "import { quadtree } from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport { x, y } from \"./simulation.js\";\nexport default function () {\n  var nodes,\n    node,\n    random,\n    alpha,\n    strength = constant(-30),\n    strengths,\n    distanceMin2 = 1,\n    distanceMax2 = Infinity,\n    theta2 = 0.81;\n  function force(_) {\n    var i,\n      n = nodes.length,\n      tree = quadtree(nodes, x, y).visitAfter(accumulate);\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length,\n      node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n  function accumulate(quad) {\n    var strength = 0,\n      q,\n      c,\n      weight = 0,\n      x,\n      y,\n      i;\n\n    // For internal nodes, accumulate forces from child quadrants.\n    if (quad.length) {\n      for (x = y = i = 0; i < 4; ++i) {\n        if ((q = quad[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * q.x, y += c * q.y;\n        }\n      }\n      quad.x = x / weight;\n      quad.y = y / weight;\n    }\n\n    // For leaf nodes, accumulate forces from coincident quadrants.\n    else {\n      q = quad;\n      q.x = q.data.x;\n      q.y = q.data.y;\n      do strength += strengths[q.data.index]; while (q = q.next);\n    }\n    quad.value = strength;\n  }\n  function apply(quad, x1, _, x2) {\n    if (!quad.value) return true;\n    var x = quad.x - node.x,\n      y = quad.y - node.y,\n      w = x2 - x1,\n      l = x * x + y * y;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (y === 0) y = jiggle(random), l += y * y;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * quad.value * alpha / l;\n        node.vy += y * quad.value * alpha / l;\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (quad.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (quad.data !== node || quad.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (y === 0) y = jiggle(random), l += y * y;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n    do if (quad.data !== node) {\n      w = strengths[quad.data.index] * alpha / l;\n      node.vx += x * w;\n      node.vy += y * w;\n    } while (quad = quad.next);\n  }\n  force.initialize = function (_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n  force.distanceMin = function (_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n  force.distanceMax = function (_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n  force.theta = function (_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n  return force;\n}", "map": {"version": 3, "names": ["quadtree", "constant", "jiggle", "x", "y", "nodes", "node", "random", "alpha", "strength", "strengths", "distanceMin2", "distanceMax2", "Infinity", "theta2", "force", "_", "i", "n", "length", "tree", "visitAfter", "accumulate", "visit", "apply", "initialize", "Array", "index", "quad", "q", "c", "weight", "Math", "abs", "value", "data", "next", "x1", "x2", "w", "l", "sqrt", "vx", "vy", "_nodes", "_random", "arguments", "distanceMin", "distanceMax", "theta"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force/src/manyBody.js"], "sourcesContent": ["import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport {x, y} from \"./simulation.js\";\n\nexport default function() {\n  var nodes,\n      node,\n      random,\n      alpha,\n      strength = constant(-30),\n      strengths,\n      distanceMin2 = 1,\n      distanceMax2 = Infinity,\n      theta2 = 0.81;\n\n  function force(_) {\n    var i, n = nodes.length, tree = quadtree(nodes, x, y).visitAfter(accumulate);\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n\n  function accumulate(quad) {\n    var strength = 0, q, c, weight = 0, x, y, i;\n\n    // For internal nodes, accumulate forces from child quadrants.\n    if (quad.length) {\n      for (x = y = i = 0; i < 4; ++i) {\n        if ((q = quad[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * q.x, y += c * q.y;\n        }\n      }\n      quad.x = x / weight;\n      quad.y = y / weight;\n    }\n\n    // For leaf nodes, accumulate forces from coincident quadrants.\n    else {\n      q = quad;\n      q.x = q.data.x;\n      q.y = q.data.y;\n      do strength += strengths[q.data.index];\n      while (q = q.next);\n    }\n\n    quad.value = strength;\n  }\n\n  function apply(quad, x1, _, x2) {\n    if (!quad.value) return true;\n\n    var x = quad.x - node.x,\n        y = quad.y - node.y,\n        w = x2 - x1,\n        l = x * x + y * y;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (y === 0) y = jiggle(random), l += y * y;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * quad.value * alpha / l;\n        node.vy += y * quad.value * alpha / l;\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (quad.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (quad.data !== node || quad.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (y === 0) y = jiggle(random), l += y * y;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n\n    do if (quad.data !== node) {\n      w = strengths[quad.data.index] * alpha / l;\n      node.vx += x * w;\n      node.vy += y * w;\n    } while (quad = quad.next);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.distanceMin = function(_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n\n  force.distanceMax = function(_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n\n  force.theta = function(_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,aAAa;AACpC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAChC,SAAQC,CAAC,EAAEC,CAAC,QAAO,iBAAiB;AAEpC,eAAe,YAAW;EACxB,IAAIC,KAAK;IACLC,IAAI;IACJC,MAAM;IACNC,KAAK;IACLC,QAAQ,GAAGR,QAAQ,CAAC,CAAC,EAAE,CAAC;IACxBS,SAAS;IACTC,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGC,QAAQ;IACvBC,MAAM,GAAG,IAAI;EAEjB,SAASC,KAAKA,CAACC,CAAC,EAAE;IAChB,IAAIC,CAAC;MAAEC,CAAC,GAAGb,KAAK,CAACc,MAAM;MAAEC,IAAI,GAAGpB,QAAQ,CAACK,KAAK,EAAEF,CAAC,EAAEC,CAAC,CAAC,CAACiB,UAAU,CAACC,UAAU,CAAC;IAC5E,KAAKd,KAAK,GAAGQ,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEX,IAAI,GAAGD,KAAK,CAACY,CAAC,CAAC,EAAEG,IAAI,CAACG,KAAK,CAACC,KAAK,CAAC;EACvE;EAEA,SAASC,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACpB,KAAK,EAAE;IACZ,IAAIY,CAAC;MAAEC,CAAC,GAAGb,KAAK,CAACc,MAAM;MAAEb,IAAI;IAC7BI,SAAS,GAAG,IAAIgB,KAAK,CAACR,CAAC,CAAC;IACxB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEX,IAAI,GAAGD,KAAK,CAACY,CAAC,CAAC,EAAEP,SAAS,CAACJ,IAAI,CAACqB,KAAK,CAAC,GAAG,CAAClB,QAAQ,CAACH,IAAI,EAAEW,CAAC,EAAEZ,KAAK,CAAC;EAC5F;EAEA,SAASiB,UAAUA,CAACM,IAAI,EAAE;IACxB,IAAInB,QAAQ,GAAG,CAAC;MAAEoB,CAAC;MAAEC,CAAC;MAAEC,MAAM,GAAG,CAAC;MAAE5B,CAAC;MAAEC,CAAC;MAAEa,CAAC;;IAE3C;IACA,IAAIW,IAAI,CAACT,MAAM,EAAE;MACf,KAAKhB,CAAC,GAAGC,CAAC,GAAGa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAE,EAAEA,CAAC,EAAE;QAC9B,IAAI,CAACY,CAAC,GAAGD,IAAI,CAACX,CAAC,CAAC,MAAMa,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACJ,CAAC,CAACK,KAAK,CAAC,CAAC,EAAE;UAC5CzB,QAAQ,IAAIoB,CAAC,CAACK,KAAK,EAAEH,MAAM,IAAID,CAAC,EAAE3B,CAAC,IAAI2B,CAAC,GAAGD,CAAC,CAAC1B,CAAC,EAAEC,CAAC,IAAI0B,CAAC,GAAGD,CAAC,CAACzB,CAAC;QAC9D;MACF;MACAwB,IAAI,CAACzB,CAAC,GAAGA,CAAC,GAAG4B,MAAM;MACnBH,IAAI,CAACxB,CAAC,GAAGA,CAAC,GAAG2B,MAAM;IACrB;;IAEA;IAAA,KACK;MACHF,CAAC,GAAGD,IAAI;MACRC,CAAC,CAAC1B,CAAC,GAAG0B,CAAC,CAACM,IAAI,CAAChC,CAAC;MACd0B,CAAC,CAACzB,CAAC,GAAGyB,CAAC,CAACM,IAAI,CAAC/B,CAAC;MACd,GAAGK,QAAQ,IAAIC,SAAS,CAACmB,CAAC,CAACM,IAAI,CAACR,KAAK,CAAC,CAAC,QAChCE,CAAC,GAAGA,CAAC,CAACO,IAAI;IACnB;IAEAR,IAAI,CAACM,KAAK,GAAGzB,QAAQ;EACvB;EAEA,SAASe,KAAKA,CAACI,IAAI,EAAES,EAAE,EAAErB,CAAC,EAAEsB,EAAE,EAAE;IAC9B,IAAI,CAACV,IAAI,CAACM,KAAK,EAAE,OAAO,IAAI;IAE5B,IAAI/B,CAAC,GAAGyB,IAAI,CAACzB,CAAC,GAAGG,IAAI,CAACH,CAAC;MACnBC,CAAC,GAAGwB,IAAI,CAACxB,CAAC,GAAGE,IAAI,CAACF,CAAC;MACnBmC,CAAC,GAAGD,EAAE,GAAGD,EAAE;MACXG,CAAC,GAAGrC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;;IAErB;IACA;IACA,IAAImC,CAAC,GAAGA,CAAC,GAAGzB,MAAM,GAAG0B,CAAC,EAAE;MACtB,IAAIA,CAAC,GAAG5B,YAAY,EAAE;QACpB,IAAIT,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACK,MAAM,CAAC,EAAEiC,CAAC,IAAIrC,CAAC,GAAGA,CAAC;QAC3C,IAAIC,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACK,MAAM,CAAC,EAAEiC,CAAC,IAAIpC,CAAC,GAAGA,CAAC;QAC3C,IAAIoC,CAAC,GAAG7B,YAAY,EAAE6B,CAAC,GAAGR,IAAI,CAACS,IAAI,CAAC9B,YAAY,GAAG6B,CAAC,CAAC;QACrDlC,IAAI,CAACoC,EAAE,IAAIvC,CAAC,GAAGyB,IAAI,CAACM,KAAK,GAAG1B,KAAK,GAAGgC,CAAC;QACrClC,IAAI,CAACqC,EAAE,IAAIvC,CAAC,GAAGwB,IAAI,CAACM,KAAK,GAAG1B,KAAK,GAAGgC,CAAC;MACvC;MACA,OAAO,IAAI;IACb;;IAEA;IAAA,KACK,IAAIZ,IAAI,CAACT,MAAM,IAAIqB,CAAC,IAAI5B,YAAY,EAAE;;IAE3C;IACA,IAAIgB,IAAI,CAACO,IAAI,KAAK7B,IAAI,IAAIsB,IAAI,CAACQ,IAAI,EAAE;MACnC,IAAIjC,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACK,MAAM,CAAC,EAAEiC,CAAC,IAAIrC,CAAC,GAAGA,CAAC;MAC3C,IAAIC,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACK,MAAM,CAAC,EAAEiC,CAAC,IAAIpC,CAAC,GAAGA,CAAC;MAC3C,IAAIoC,CAAC,GAAG7B,YAAY,EAAE6B,CAAC,GAAGR,IAAI,CAACS,IAAI,CAAC9B,YAAY,GAAG6B,CAAC,CAAC;IACvD;IAEA,GAAG,IAAIZ,IAAI,CAACO,IAAI,KAAK7B,IAAI,EAAE;MACzBiC,CAAC,GAAG7B,SAAS,CAACkB,IAAI,CAACO,IAAI,CAACR,KAAK,CAAC,GAAGnB,KAAK,GAAGgC,CAAC;MAC1ClC,IAAI,CAACoC,EAAE,IAAIvC,CAAC,GAAGoC,CAAC;MAChBjC,IAAI,CAACqC,EAAE,IAAIvC,CAAC,GAAGmC,CAAC;IAClB,CAAC,QAAQX,IAAI,GAAGA,IAAI,CAACQ,IAAI;EAC3B;EAEArB,KAAK,CAACU,UAAU,GAAG,UAASmB,MAAM,EAAEC,OAAO,EAAE;IAC3CxC,KAAK,GAAGuC,MAAM;IACdrC,MAAM,GAAGsC,OAAO;IAChBpB,UAAU,CAAC,CAAC;EACd,CAAC;EAEDV,KAAK,CAACN,QAAQ,GAAG,UAASO,CAAC,EAAE;IAC3B,OAAO8B,SAAS,CAAC3B,MAAM,IAAIV,QAAQ,GAAG,OAAOO,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGf,QAAQ,CAAC,CAACe,CAAC,CAAC,EAAES,UAAU,CAAC,CAAC,EAAEV,KAAK,IAAIN,QAAQ;EACnH,CAAC;EAEDM,KAAK,CAACgC,WAAW,GAAG,UAAS/B,CAAC,EAAE;IAC9B,OAAO8B,SAAS,CAAC3B,MAAM,IAAIR,YAAY,GAAGK,CAAC,GAAGA,CAAC,EAAED,KAAK,IAAIiB,IAAI,CAACS,IAAI,CAAC9B,YAAY,CAAC;EACnF,CAAC;EAEDI,KAAK,CAACiC,WAAW,GAAG,UAAShC,CAAC,EAAE;IAC9B,OAAO8B,SAAS,CAAC3B,MAAM,IAAIP,YAAY,GAAGI,CAAC,GAAGA,CAAC,EAAED,KAAK,IAAIiB,IAAI,CAACS,IAAI,CAAC7B,YAAY,CAAC;EACnF,CAAC;EAEDG,KAAK,CAACkC,KAAK,GAAG,UAASjC,CAAC,EAAE;IACxB,OAAO8B,SAAS,CAAC3B,MAAM,IAAIL,MAAM,GAAGE,CAAC,GAAGA,CAAC,EAAED,KAAK,IAAIiB,IAAI,CAACS,IAAI,CAAC3B,MAAM,CAAC;EACvE,CAAC;EAED,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}