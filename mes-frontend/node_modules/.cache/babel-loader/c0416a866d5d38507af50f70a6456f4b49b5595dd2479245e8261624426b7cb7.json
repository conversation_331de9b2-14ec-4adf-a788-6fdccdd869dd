{"ast": null, "code": "import { __read } from \"tslib\";\nimport { BBox } from '../../bbox';\nexport function getItemsBBox(items) {\n  var minX = Infinity;\n  var minY = Infinity;\n  var maxX = -Infinity;\n  var maxY = -Infinity;\n  for (var i = 0; i < items.length; i++) {\n    var _a = items[i],\n      x = _a.x,\n      y = _a.y,\n      width = _a.width,\n      height = _a.height;\n    var _b = __read([x + width, y + height], 2),\n      X = _b[0],\n      Y = _b[1];\n    if (x < minX) minX = x;\n    if (y < minY) minY = y;\n    if (X > maxX) maxX = X;\n    if (Y > maxY) maxY = Y;\n  }\n  return new BBox(minX, minY, maxX - minX, maxY - minY);\n}", "map": {"version": 3, "names": ["BBox", "getItemsBBox", "items", "minX", "Infinity", "minY", "maxX", "maxY", "i", "length", "_a", "x", "y", "width", "height", "_b", "__read", "X", "Y"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/util/layout/utils/helper.ts"], "sourcesContent": ["import type { LayoutItem } from '../types';\nimport { BBox } from '../../bbox';\n\nexport function getItemsBBox(items: LayoutItem[]) {\n  let minX = Infinity;\n  let minY = Infinity;\n  let maxX = -Infinity;\n  let maxY = -Infinity;\n\n  for (let i = 0; i < items.length; i++) {\n    const { x, y, width, height } = items[i];\n    const [X, Y] = [x + width, y + height];\n    if (x < minX) minX = x;\n    if (y < minY) minY = y;\n    if (X > maxX) maxX = X;\n    if (Y > maxY) maxY = Y;\n  }\n  return new BBox(minX, minY, maxX - minX, maxY - minY);\n}\n"], "mappings": ";AACA,SAASA,IAAI,QAAQ,YAAY;AAEjC,OAAM,SAAUC,YAAYA,CAACC,KAAmB;EAC9C,IAAIC,IAAI,GAAGC,QAAQ;EACnB,IAAIC,IAAI,GAAGD,QAAQ;EACnB,IAAIE,IAAI,GAAG,CAACF,QAAQ;EACpB,IAAIG,IAAI,GAAG,CAACH,QAAQ;EAEpB,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,KAAK,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IAC/B,IAAAE,EAAA,GAA0BR,KAAK,CAACM,CAAC,CAAC;MAAhCG,CAAC,GAAAD,EAAA,CAAAC,CAAA;MAAEC,CAAC,GAAAF,EAAA,CAAAE,CAAA;MAAEC,KAAK,GAAAH,EAAA,CAAAG,KAAA;MAAEC,MAAM,GAAAJ,EAAA,CAAAI,MAAa;IAClC,IAAAC,EAAA,GAAAC,MAAA,CAAS,CAACL,CAAC,GAAGE,KAAK,EAAED,CAAC,GAAGE,MAAM,CAAC;MAA/BG,CAAC,GAAAF,EAAA;MAAEG,CAAC,GAAAH,EAAA,GAA2B;IACtC,IAAIJ,CAAC,GAAGR,IAAI,EAAEA,IAAI,GAAGQ,CAAC;IACtB,IAAIC,CAAC,GAAGP,IAAI,EAAEA,IAAI,GAAGO,CAAC;IACtB,IAAIK,CAAC,GAAGX,IAAI,EAAEA,IAAI,GAAGW,CAAC;IACtB,IAAIC,CAAC,GAAGX,IAAI,EAAEA,IAAI,GAAGW,CAAC;EACxB;EACA,OAAO,IAAIlB,IAAI,CAACG,IAAI,EAAEE,IAAI,EAAEC,IAAI,GAAGH,IAAI,EAAEI,IAAI,GAAGF,IAAI,CAAC;AACvD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}