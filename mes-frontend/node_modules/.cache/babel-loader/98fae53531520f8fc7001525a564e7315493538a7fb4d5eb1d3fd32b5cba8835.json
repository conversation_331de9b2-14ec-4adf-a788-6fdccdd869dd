{"ast": null, "code": "import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nfunction index(d) {\n  return d.index;\n}\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\nexport default function (links) {\n  var id = index,\n    strength = defaultStrength,\n    strengths,\n    distance = constant(30),\n    distances,\n    nodes,\n    count,\n    bias,\n    random,\n    iterations = 1;\n  if (links == null) links = [];\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x, y, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        y = target.y + target.vy - source.y - source.vy || jiggle(random);\n        l = Math.sqrt(x * x + y * y);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l;\n        target.vx -= x * (b = bias[i]);\n        target.vy -= y * b;\n        source.vx += x * (b = 1 - b);\n        source.vy += y * b;\n      }\n    }\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length,\n      m = links.length,\n      nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n      link;\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n  function initializeStrength() {\n    if (!nodes) return;\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n  function initializeDistance() {\n    if (!nodes) return;\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n  force.initialize = function (_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n  force.links = function (_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n  force.id = function (_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n  force.iterations = function (_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n  force.distance = function (_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["constant", "jiggle", "index", "d", "find", "nodeById", "nodeId", "node", "get", "Error", "links", "id", "strength", "defaultStrength", "strengths", "distance", "distances", "nodes", "count", "bias", "random", "iterations", "link", "Math", "min", "source", "target", "force", "alpha", "k", "n", "length", "i", "x", "y", "l", "b", "vx", "vy", "sqrt", "initialize", "m", "Map", "map", "Array", "initializeStrength", "initializeDistance", "_nodes", "_random", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force/src/link.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction index(d) {\n  return d.index;\n}\n\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\n\nexport default function(links) {\n  var id = index,\n      strength = defaultStrength,\n      strengths,\n      distance = constant(30),\n      distances,\n      nodes,\n      count,\n      bias,\n      random,\n      iterations = 1;\n\n  if (links == null) links = [];\n\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x, y, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        y = target.y + target.vy - source.y - source.vy || jiggle(random);\n        l = Math.sqrt(x * x + y * y);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l;\n        target.vx -= x * (b = bias[i]);\n        target.vy -= y * b;\n        source.vx += x * (b = 1 - b);\n        source.vy += y * b;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n\n    var i,\n        n = nodes.length,\n        m = links.length,\n        nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n        link;\n\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n\n  function initializeStrength() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n\n  function initializeDistance() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.links = function(_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n\n  force.id = function(_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n\n  force.distance = function(_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAEhC,SAASC,KAAKA,CAACC,CAAC,EAAE;EAChB,OAAOA,CAAC,CAACD,KAAK;AAChB;AAEA,SAASE,IAAIA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAGF,QAAQ,CAACG,GAAG,CAACF,MAAM,CAAC;EAC/B,IAAI,CAACC,IAAI,EAAE,MAAM,IAAIE,KAAK,CAAC,kBAAkB,GAAGH,MAAM,CAAC;EACvD,OAAOC,IAAI;AACb;AAEA,eAAe,UAASG,KAAK,EAAE;EAC7B,IAAIC,EAAE,GAAGT,KAAK;IACVU,QAAQ,GAAGC,eAAe;IAC1BC,SAAS;IACTC,QAAQ,GAAGf,QAAQ,CAAC,EAAE,CAAC;IACvBgB,SAAS;IACTC,KAAK;IACLC,KAAK;IACLC,IAAI;IACJC,MAAM;IACNC,UAAU,GAAG,CAAC;EAElB,IAAIX,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;EAE7B,SAASG,eAAeA,CAACS,IAAI,EAAE;IAC7B,OAAO,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,CAACI,IAAI,CAACG,MAAM,CAACvB,KAAK,CAAC,EAAEgB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACxB,KAAK,CAAC,CAAC;EACzE;EAEA,SAASyB,KAAKA,CAACC,KAAK,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGpB,KAAK,CAACqB,MAAM,EAAEF,CAAC,GAAGR,UAAU,EAAE,EAAEQ,CAAC,EAAE;MACrD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEV,IAAI,EAAEG,MAAM,EAAEC,MAAM,EAAEO,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEJ,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;QAC5DV,IAAI,GAAGZ,KAAK,CAACsB,CAAC,CAAC,EAAEP,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAEC,MAAM,GAAGJ,IAAI,CAACI,MAAM;QAC3DO,CAAC,GAAGP,MAAM,CAACO,CAAC,GAAGP,MAAM,CAACW,EAAE,GAAGZ,MAAM,CAACQ,CAAC,GAAGR,MAAM,CAACY,EAAE,IAAIpC,MAAM,CAACmB,MAAM,CAAC;QACjEc,CAAC,GAAGR,MAAM,CAACQ,CAAC,GAAGR,MAAM,CAACY,EAAE,GAAGb,MAAM,CAACS,CAAC,GAAGT,MAAM,CAACa,EAAE,IAAIrC,MAAM,CAACmB,MAAM,CAAC;QACjEe,CAAC,GAAGZ,IAAI,CAACgB,IAAI,CAACN,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;QAC5BC,CAAC,GAAG,CAACA,CAAC,GAAGnB,SAAS,CAACgB,CAAC,CAAC,IAAIG,CAAC,GAAGP,KAAK,GAAGd,SAAS,CAACkB,CAAC,CAAC;QACjDC,CAAC,IAAIE,CAAC,EAAED,CAAC,IAAIC,CAAC;QACdT,MAAM,CAACW,EAAE,IAAIJ,CAAC,IAAIG,CAAC,GAAGjB,IAAI,CAACa,CAAC,CAAC,CAAC;QAC9BN,MAAM,CAACY,EAAE,IAAIJ,CAAC,GAAGE,CAAC;QAClBX,MAAM,CAACY,EAAE,IAAIJ,CAAC,IAAIG,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;QAC5BX,MAAM,CAACa,EAAE,IAAIJ,CAAC,GAAGE,CAAC;MACpB;IACF;EACF;EAEA,SAASI,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACvB,KAAK,EAAE;IAEZ,IAAIe,CAAC;MACDF,CAAC,GAAGb,KAAK,CAACc,MAAM;MAChBU,CAAC,GAAG/B,KAAK,CAACqB,MAAM;MAChB1B,QAAQ,GAAG,IAAIqC,GAAG,CAACzB,KAAK,CAAC0B,GAAG,CAAC,CAACxC,CAAC,EAAE6B,CAAC,KAAK,CAACrB,EAAE,CAACR,CAAC,EAAE6B,CAAC,EAAEf,KAAK,CAAC,EAAEd,CAAC,CAAC,CAAC,CAAC;MAC7DmB,IAAI;IAER,KAAKU,CAAC,GAAG,CAAC,EAAEd,KAAK,GAAG,IAAI0B,KAAK,CAACd,CAAC,CAAC,EAAEE,CAAC,GAAGS,CAAC,EAAE,EAAET,CAAC,EAAE;MAC5CV,IAAI,GAAGZ,KAAK,CAACsB,CAAC,CAAC,EAAEV,IAAI,CAACpB,KAAK,GAAG8B,CAAC;MAC/B,IAAI,OAAOV,IAAI,CAACG,MAAM,KAAK,QAAQ,EAAEH,IAAI,CAACG,MAAM,GAAGrB,IAAI,CAACC,QAAQ,EAAEiB,IAAI,CAACG,MAAM,CAAC;MAC9E,IAAI,OAAOH,IAAI,CAACI,MAAM,KAAK,QAAQ,EAAEJ,IAAI,CAACI,MAAM,GAAGtB,IAAI,CAACC,QAAQ,EAAEiB,IAAI,CAACI,MAAM,CAAC;MAC9ER,KAAK,CAACI,IAAI,CAACG,MAAM,CAACvB,KAAK,CAAC,GAAG,CAACgB,KAAK,CAACI,IAAI,CAACG,MAAM,CAACvB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;MAC9DgB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACxB,KAAK,CAAC,GAAG,CAACgB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACxB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IAChE;IAEA,KAAK8B,CAAC,GAAG,CAAC,EAAEb,IAAI,GAAG,IAAIyB,KAAK,CAACH,CAAC,CAAC,EAAET,CAAC,GAAGS,CAAC,EAAE,EAAET,CAAC,EAAE;MAC3CV,IAAI,GAAGZ,KAAK,CAACsB,CAAC,CAAC,EAAEb,IAAI,CAACa,CAAC,CAAC,GAAGd,KAAK,CAACI,IAAI,CAACG,MAAM,CAACvB,KAAK,CAAC,IAAIgB,KAAK,CAACI,IAAI,CAACG,MAAM,CAACvB,KAAK,CAAC,GAAGgB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACxB,KAAK,CAAC,CAAC;IAC7G;IAEAY,SAAS,GAAG,IAAI8B,KAAK,CAACH,CAAC,CAAC,EAAEI,kBAAkB,CAAC,CAAC;IAC9C7B,SAAS,GAAG,IAAI4B,KAAK,CAACH,CAAC,CAAC,EAAEK,kBAAkB,CAAC,CAAC;EAChD;EAEA,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAC5B,KAAK,EAAE;IAEZ,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAGpB,KAAK,CAACqB,MAAM,EAAEC,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MAC5ClB,SAAS,CAACkB,CAAC,CAAC,GAAG,CAACpB,QAAQ,CAACF,KAAK,CAACsB,CAAC,CAAC,EAAEA,CAAC,EAAEtB,KAAK,CAAC;IAC9C;EACF;EAEA,SAASoC,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAC7B,KAAK,EAAE;IAEZ,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAGpB,KAAK,CAACqB,MAAM,EAAEC,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MAC5ChB,SAAS,CAACgB,CAAC,CAAC,GAAG,CAACjB,QAAQ,CAACL,KAAK,CAACsB,CAAC,CAAC,EAAEA,CAAC,EAAEtB,KAAK,CAAC;IAC9C;EACF;EAEAiB,KAAK,CAACa,UAAU,GAAG,UAASO,MAAM,EAAEC,OAAO,EAAE;IAC3C/B,KAAK,GAAG8B,MAAM;IACd3B,MAAM,GAAG4B,OAAO;IAChBR,UAAU,CAAC,CAAC;EACd,CAAC;EAEDb,KAAK,CAACjB,KAAK,GAAG,UAASuC,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACnB,MAAM,IAAIrB,KAAK,GAAGuC,CAAC,EAAET,UAAU,CAAC,CAAC,EAAEb,KAAK,IAAIjB,KAAK;EACpE,CAAC;EAEDiB,KAAK,CAAChB,EAAE,GAAG,UAASsC,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACnB,MAAM,IAAIpB,EAAE,GAAGsC,CAAC,EAAEtB,KAAK,IAAIhB,EAAE;EAChD,CAAC;EAEDgB,KAAK,CAACN,UAAU,GAAG,UAAS4B,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACnB,MAAM,IAAIV,UAAU,GAAG,CAAC4B,CAAC,EAAEtB,KAAK,IAAIN,UAAU;EACjE,CAAC;EAEDM,KAAK,CAACf,QAAQ,GAAG,UAASqC,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACnB,MAAM,IAAInB,QAAQ,GAAG,OAAOqC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGjD,QAAQ,CAAC,CAACiD,CAAC,CAAC,EAAEJ,kBAAkB,CAAC,CAAC,EAAElB,KAAK,IAAIf,QAAQ;EAC3H,CAAC;EAEDe,KAAK,CAACZ,QAAQ,GAAG,UAASkC,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACnB,MAAM,IAAIhB,QAAQ,GAAG,OAAOkC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGjD,QAAQ,CAAC,CAACiD,CAAC,CAAC,EAAEH,kBAAkB,CAAC,CAAC,EAAEnB,KAAK,IAAIZ,QAAQ;EAC3H,CAAC;EAED,OAAOY,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}