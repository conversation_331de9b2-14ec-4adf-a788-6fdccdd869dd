{"ast": null, "code": "export default function (d) {\n  const x = +this._x.call(null, d),\n    y = +this._y.call(null, d),\n    z = +this._z.call(null, d);\n  return add(this.cover(x, y, z), x, y, z, d);\n}\nfunction add(tree, x, y, z, d) {\n  if (isNaN(x) || isNaN(y) || isNaN(z)) return tree; // ignore invalid points\n\n  var parent,\n    node = tree._root,\n    leaf = {\n      data: d\n    },\n    x0 = tree._x0,\n    y0 = tree._y0,\n    z0 = tree._z0,\n    x1 = tree._x1,\n    y1 = tree._y1,\n    z1 = tree._z1,\n    xm,\n    ym,\n    zm,\n    xp,\n    yp,\n    zp,\n    right,\n    bottom,\n    deep,\n    i,\n    j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;else y1 = ym;\n    if (deep = z >= (zm = (z0 + z1) / 2)) z0 = zm;else z1 = zm;\n    if (parent = node, !(node = node[i = deep << 2 | bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  zp = +tree._z.call(null, node.data);\n  if (x === xp && y === yp && z === zp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(8) : tree._root = new Array(8);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;else y1 = ym;\n    if (deep = z >= (zm = (z0 + z1) / 2)) z0 = zm;else z1 = zm;\n  } while ((i = deep << 2 | bottom << 1 | right) === (j = (zp >= zm) << 2 | (yp >= ym) << 1 | xp >= xm));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\nexport function addAll(data) {\n  if (!Array.isArray(data)) data = Array.from(data);\n  const n = data.length;\n  const xz = new Float64Array(n);\n  const yz = new Float64Array(n);\n  const zz = new Float64Array(n);\n  let x0 = Infinity,\n    y0 = Infinity,\n    z0 = Infinity,\n    x1 = -Infinity,\n    y1 = -Infinity,\n    z1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (let i = 0, d, x, y, z; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d)) || isNaN(z = +this._z.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    zz[i] = z;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n    if (z < z0) z0 = z;\n    if (z > z1) z1 = z;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1 || z0 > z1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0, z0).cover(x1, y1, z1);\n\n  // Add the new points.\n  for (let i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], zz[i], data[i]);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["d", "x", "_x", "call", "y", "_y", "z", "_z", "add", "cover", "tree", "isNaN", "parent", "node", "_root", "leaf", "data", "x0", "_x0", "y0", "_y0", "z0", "_z0", "x1", "_x1", "y1", "_y1", "z1", "_z1", "xm", "ym", "zm", "xp", "yp", "zp", "right", "bottom", "deep", "i", "j", "length", "next", "Array", "addAll", "isArray", "from", "n", "xz", "Float64Array", "yz", "zz", "Infinity"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-octree/src/add.js"], "sourcesContent": ["export default function(d) {\n  const x = +this._x.call(null, d),\n      y = +this._y.call(null, d),\n      z = +this._z.call(null, d);\n  return add(this.cover(x, y, z), x, y, z, d);\n}\n\nfunction add(tree, x, y, z, d) {\n  if (isNaN(x) || isNaN(y) || isNaN(z)) return tree; // ignore invalid points\n\n  var parent,\n      node = tree._root,\n      leaf = {data: d},\n      x0 = tree._x0,\n      y0 = tree._y0,\n      z0 = tree._z0,\n      x1 = tree._x1,\n      y1 = tree._y1,\n      z1 = tree._z1,\n      xm,\n      ym,\n      zm,\n      xp,\n      yp,\n      zp,\n      right,\n      bottom,\n      deep,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (deep = z >= (zm = (z0 + z1) / 2)) z0 = zm; else z1 = zm;\n    if (parent = node, !(node = node[i = deep << 2 | bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  zp = +tree._z.call(null, node.data);\n  if (x === xp && y === yp && z === zp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(8) : tree._root = new Array(8);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (deep = z >= (zm = (z0 + z1) / 2)) z0 = zm; else z1 = zm;\n  } while ((i = deep << 2 | bottom << 1 | right) === (j = (zp >= zm) << 2 | (yp >= ym) << 1 | (xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\n\nexport function addAll(data) {\n  if (!Array.isArray(data)) data = Array.from(data);\n  const n = data.length;\n  const xz = new Float64Array(n);\n  const yz = new Float64Array(n);\n  const zz = new Float64Array(n);\n  let x0 = Infinity,\n      y0 = Infinity,\n      z0 = Infinity,\n      x1 = -Infinity,\n      y1 = -Infinity,\n      z1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (let i = 0, d, x, y, z; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d)) || isNaN(z = +this._z.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    zz[i] = z;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n    if (z < z0) z0 = z;\n    if (z > z1) z1 = z;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1 || z0 > z1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0, z0).cover(x1, y1, z1);\n\n  // Add the new points.\n  for (let i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], zz[i], data[i]);\n  }\n\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,MAAMC,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IAC5BI,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;IAC1BM,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACJ,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;EAC9B,OAAOQ,GAAG,CAAC,IAAI,CAACC,KAAK,CAACR,CAAC,EAAEG,CAAC,EAAEE,CAAC,CAAC,EAAEL,CAAC,EAAEG,CAAC,EAAEE,CAAC,EAAEN,CAAC,CAAC;AAC7C;AAEA,SAASQ,GAAGA,CAACE,IAAI,EAAET,CAAC,EAAEG,CAAC,EAAEE,CAAC,EAAEN,CAAC,EAAE;EAC7B,IAAIW,KAAK,CAACV,CAAC,CAAC,IAAIU,KAAK,CAACP,CAAC,CAAC,IAAIO,KAAK,CAACL,CAAC,CAAC,EAAE,OAAOI,IAAI,CAAC,CAAC;;EAEnD,IAAIE,MAAM;IACNC,IAAI,GAAGH,IAAI,CAACI,KAAK;IACjBC,IAAI,GAAG;MAACC,IAAI,EAAEhB;IAAC,CAAC;IAChBiB,EAAE,GAAGP,IAAI,CAACQ,GAAG;IACbC,EAAE,GAAGT,IAAI,CAACU,GAAG;IACbC,EAAE,GAAGX,IAAI,CAACY,GAAG;IACbC,EAAE,GAAGb,IAAI,CAACc,GAAG;IACbC,EAAE,GAAGf,IAAI,CAACgB,GAAG;IACbC,EAAE,GAAGjB,IAAI,CAACkB,GAAG;IACbC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,EAAE;IACFC,KAAK;IACLC,MAAM;IACNC,IAAI;IACJC,CAAC;IACDC,CAAC;;EAEL;EACA,IAAI,CAAC1B,IAAI,EAAE,OAAOH,IAAI,CAACI,KAAK,GAAGC,IAAI,EAAEL,IAAI;;EAEzC;EACA,OAAOG,IAAI,CAAC2B,MAAM,EAAE;IAClB,IAAIL,KAAK,GAAGlC,CAAC,KAAK4B,EAAE,GAAG,CAACZ,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGY,EAAE,CAAC,KAAMN,EAAE,GAAGM,EAAE;IAC5D,IAAIO,MAAM,GAAGhC,CAAC,KAAK0B,EAAE,GAAG,CAACX,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGW,EAAE,CAAC,KAAML,EAAE,GAAGK,EAAE;IAC7D,IAAIO,IAAI,GAAG/B,CAAC,KAAKyB,EAAE,GAAG,CAACV,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGU,EAAE,CAAC,KAAMJ,EAAE,GAAGI,EAAE;IAC3D,IAAInB,MAAM,GAAGC,IAAI,EAAE,EAAEA,IAAI,GAAGA,IAAI,CAACyB,CAAC,GAAGD,IAAI,IAAI,CAAC,GAAGD,MAAM,IAAI,CAAC,GAAGD,KAAK,CAAC,CAAC,EAAE,OAAOvB,MAAM,CAAC0B,CAAC,CAAC,GAAGvB,IAAI,EAAEL,IAAI;EACvG;;EAEA;EACAsB,EAAE,GAAG,CAACtB,IAAI,CAACR,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEU,IAAI,CAACG,IAAI,CAAC;EACnCiB,EAAE,GAAG,CAACvB,IAAI,CAACL,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEU,IAAI,CAACG,IAAI,CAAC;EACnCkB,EAAE,GAAG,CAACxB,IAAI,CAACH,EAAE,CAACJ,IAAI,CAAC,IAAI,EAAEU,IAAI,CAACG,IAAI,CAAC;EACnC,IAAIf,CAAC,KAAK+B,EAAE,IAAI5B,CAAC,KAAK6B,EAAE,IAAI3B,CAAC,KAAK4B,EAAE,EAAE,OAAOnB,IAAI,CAAC0B,IAAI,GAAG5B,IAAI,EAAED,MAAM,GAAGA,MAAM,CAAC0B,CAAC,CAAC,GAAGvB,IAAI,GAAGL,IAAI,CAACI,KAAK,GAAGC,IAAI,EAAEL,IAAI;;EAElH;EACA,GAAG;IACDE,MAAM,GAAGA,MAAM,GAAGA,MAAM,CAAC0B,CAAC,CAAC,GAAG,IAAII,KAAK,CAAC,CAAC,CAAC,GAAGhC,IAAI,CAACI,KAAK,GAAG,IAAI4B,KAAK,CAAC,CAAC,CAAC;IACtE,IAAIP,KAAK,GAAGlC,CAAC,KAAK4B,EAAE,GAAG,CAACZ,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGY,EAAE,CAAC,KAAMN,EAAE,GAAGM,EAAE;IAC5D,IAAIO,MAAM,GAAGhC,CAAC,KAAK0B,EAAE,GAAG,CAACX,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGW,EAAE,CAAC,KAAML,EAAE,GAAGK,EAAE;IAC7D,IAAIO,IAAI,GAAG/B,CAAC,KAAKyB,EAAE,GAAG,CAACV,EAAE,GAAGM,EAAE,IAAI,CAAC,CAAC,EAAEN,EAAE,GAAGU,EAAE,CAAC,KAAMJ,EAAE,GAAGI,EAAE;EAC7D,CAAC,QAAQ,CAACO,CAAC,GAAGD,IAAI,IAAI,CAAC,GAAGD,MAAM,IAAI,CAAC,GAAGD,KAAK,OAAOI,CAAC,GAAG,CAACL,EAAE,IAAIH,EAAE,KAAK,CAAC,GAAG,CAACE,EAAE,IAAIH,EAAE,KAAK,CAAC,GAAIE,EAAE,IAAIH,EAAG,CAAC;EACvG,OAAOjB,MAAM,CAAC2B,CAAC,CAAC,GAAG1B,IAAI,EAAED,MAAM,CAAC0B,CAAC,CAAC,GAAGvB,IAAI,EAAEL,IAAI;AACjD;AAEA,OAAO,SAASiC,MAAMA,CAAC3B,IAAI,EAAE;EAC3B,IAAI,CAAC0B,KAAK,CAACE,OAAO,CAAC5B,IAAI,CAAC,EAAEA,IAAI,GAAG0B,KAAK,CAACG,IAAI,CAAC7B,IAAI,CAAC;EACjD,MAAM8B,CAAC,GAAG9B,IAAI,CAACwB,MAAM;EACrB,MAAMO,EAAE,GAAG,IAAIC,YAAY,CAACF,CAAC,CAAC;EAC9B,MAAMG,EAAE,GAAG,IAAID,YAAY,CAACF,CAAC,CAAC;EAC9B,MAAMI,EAAE,GAAG,IAAIF,YAAY,CAACF,CAAC,CAAC;EAC9B,IAAI7B,EAAE,GAAGkC,QAAQ;IACbhC,EAAE,GAAGgC,QAAQ;IACb9B,EAAE,GAAG8B,QAAQ;IACb5B,EAAE,GAAG,CAAC4B,QAAQ;IACd1B,EAAE,GAAG,CAAC0B,QAAQ;IACdxB,EAAE,GAAG,CAACwB,QAAQ;;EAElB;EACA,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEtC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEE,CAAC,EAAEgC,CAAC,GAAGQ,CAAC,EAAE,EAAER,CAAC,EAAE;IACtC,IAAI3B,KAAK,CAACV,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEH,CAAC,GAAGgB,IAAI,CAACsB,CAAC,CAAC,CAAC,CAAC,IAAI3B,KAAK,CAACP,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC,CAAC,IAAIW,KAAK,CAACL,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACJ,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC,CAAC,EAAE;IAC3H+C,EAAE,CAACT,CAAC,CAAC,GAAGrC,CAAC;IACTgD,EAAE,CAACX,CAAC,CAAC,GAAGlC,CAAC;IACT8C,EAAE,CAACZ,CAAC,CAAC,GAAGhC,CAAC;IACT,IAAIL,CAAC,GAAGgB,EAAE,EAAEA,EAAE,GAAGhB,CAAC;IAClB,IAAIA,CAAC,GAAGsB,EAAE,EAAEA,EAAE,GAAGtB,CAAC;IAClB,IAAIG,CAAC,GAAGe,EAAE,EAAEA,EAAE,GAAGf,CAAC;IAClB,IAAIA,CAAC,GAAGqB,EAAE,EAAEA,EAAE,GAAGrB,CAAC;IAClB,IAAIE,CAAC,GAAGe,EAAE,EAAEA,EAAE,GAAGf,CAAC;IAClB,IAAIA,CAAC,GAAGqB,EAAE,EAAEA,EAAE,GAAGrB,CAAC;EACpB;;EAEA;EACA,IAAIW,EAAE,GAAGM,EAAE,IAAIJ,EAAE,GAAGM,EAAE,IAAIJ,EAAE,GAAGM,EAAE,EAAE,OAAO,IAAI;;EAE9C;EACA,IAAI,CAAClB,KAAK,CAACQ,EAAE,EAAEE,EAAE,EAAEE,EAAE,CAAC,CAACZ,KAAK,CAACc,EAAE,EAAEE,EAAE,EAAEE,EAAE,CAAC;;EAExC;EACA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,CAAC,EAAE,EAAER,CAAC,EAAE;IAC1B9B,GAAG,CAAC,IAAI,EAAEuC,EAAE,CAACT,CAAC,CAAC,EAAEW,EAAE,CAACX,CAAC,CAAC,EAAEY,EAAE,CAACZ,CAAC,CAAC,EAAEtB,IAAI,CAACsB,CAAC,CAAC,CAAC;EACzC;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}