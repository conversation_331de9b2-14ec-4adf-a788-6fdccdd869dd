{"ast": null, "code": "var copyObject = require('./_copyObject'),\n  getSymbolsIn = require('./_getSymbolsIn');\n\n/**\n * Copies own and inherited symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbolsIn(source, object) {\n  return copyObject(source, getSymbolsIn(source), object);\n}\nmodule.exports = copySymbolsIn;", "map": {"version": 3, "names": ["copyObject", "require", "getSymbolsIn", "copySymbolsIn", "source", "object", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_copySymbolsIn.js"], "sourcesContent": ["var copyObject = require('./_copyObject'),\n    getSymbolsIn = require('./_getSymbolsIn');\n\n/**\n * Copies own and inherited symbols of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy symbols from.\n * @param {Object} [object={}] The object to copy symbols to.\n * @returns {Object} Returns `object`.\n */\nfunction copySymbolsIn(source, object) {\n  return copyObject(source, getSymbolsIn(source), object);\n}\n\nmodule.exports = copySymbolsIn;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,aAAaA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACrC,OAAOL,UAAU,CAACI,MAAM,EAAEF,YAAY,CAACE,MAAM,CAAC,EAAEC,MAAM,CAAC;AACzD;AAEAC,MAAM,CAACC,OAAO,GAAGJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}