{"ast": null, "code": "/**\n * Checks if the character is a path command.\n */\nexport function isPathCommand(code) {\n  // eslint-disable-next-line no-bitwise -- Impossible to satisfy\n  switch (code | 0x20) {\n    case 0x6d /* m */:\n    case 0x7a /* z */:\n    case 0x6c /* l */:\n    case 0x68 /* h */:\n    case 0x76 /* v */:\n    case 0x63 /* c */:\n    case 0x73 /* s */:\n    case 0x71 /* q */:\n    case 0x74 /* t */:\n    case 0x61 /* a */:\n      // case 0x72/* r */:\n      return true;\n    default:\n      return false;\n  }\n}", "map": {"version": 3, "names": ["isPathCommand", "code"], "sources": ["path/parser/is-path-command.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAM,SAAUA,aAAaA,CAACC,IAAY;EACxC;EACA,QAAQA,IAAI,GAAG,IAAI;IACjB,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;IACV,KAAK,IAAI,CAAC;MACR;MACA,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}