{"ast": null, "code": "import isArrayLike from './is-array-like';\nvar contains = function (arr, value) {\n  if (!isArrayLike(arr)) {\n    return false;\n  }\n  return arr.indexOf(value) > -1;\n};\nexport default contains;", "map": {"version": 3, "names": ["isArrayLike", "contains", "arr", "value", "indexOf"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/contains.ts"], "sourcesContent": ["import isArrayLike from './is-array-like';\n\nconst contains = function(arr: any[], value: any): boolean {\n  if (!isArrayLike(arr)) {\n    return false;\n  }\n  return arr.indexOf(value) > -1;\n};\n\nexport default contains;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,iBAAiB;AAEzC,IAAMC,QAAQ,GAAG,SAAAA,CAASC,GAAU,EAAEC,KAAU;EAC9C,IAAI,CAACH,WAAW,CAACE,GAAG,CAAC,EAAE;IACrB,OAAO,KAAK;;EAEd,OAAOA,GAAG,CAACE,OAAO,CAACD,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}