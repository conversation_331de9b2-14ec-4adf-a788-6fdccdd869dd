{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"./lodash\");\nmodule.exports = Graph;\nvar DEFAULT_EDGE_NAME = \"\\x00\";\nvar GRAPH_NODE = \"\\x00\";\nvar EDGE_KEY_DELIM = \"\\x01\";\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\nfunction Graph(opts) {\n  this._isDirected = _.has(opts, \"directed\") ? opts.directed : true;\n  this._isMultigraph = _.has(opts, \"multigraph\") ? opts.multigraph : false;\n  this._isCompound = _.has(opts, \"compound\") ? opts.compound : false;\n\n  // Label for the graph itself\n  this._label = undefined;\n\n  // Defaults to be set when creating a new node\n  this._defaultNodeLabelFn = _.constant(undefined);\n\n  // Defaults to be set when creating a new edge\n  this._defaultEdgeLabelFn = _.constant(undefined);\n\n  // v -> label\n  this._nodes = {};\n  if (this._isCompound) {\n    // v -> parent\n    this._parent = {};\n\n    // v -> children\n    this._children = {};\n    this._children[GRAPH_NODE] = {};\n  }\n\n  // v -> edgeObj\n  this._in = {};\n\n  // u -> v -> Number\n  this._preds = {};\n\n  // v -> edgeObj\n  this._out = {};\n\n  // v -> w -> Number\n  this._sucs = {};\n\n  // e -> edgeObj\n  this._edgeObjs = {};\n\n  // e -> label\n  this._edgeLabels = {};\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\n/* === Graph functions ========= */\n\nGraph.prototype.isDirected = function () {\n  return this._isDirected;\n};\nGraph.prototype.isMultigraph = function () {\n  return this._isMultigraph;\n};\nGraph.prototype.isCompound = function () {\n  return this._isCompound;\n};\nGraph.prototype.setGraph = function (label) {\n  this._label = label;\n  return this;\n};\nGraph.prototype.graph = function () {\n  return this._label;\n};\n\n/* === Node functions ========== */\n\nGraph.prototype.setDefaultNodeLabel = function (newDefault) {\n  if (!_.isFunction(newDefault)) {\n    newDefault = _.constant(newDefault);\n  }\n  this._defaultNodeLabelFn = newDefault;\n  return this;\n};\nGraph.prototype.nodeCount = function () {\n  return this._nodeCount;\n};\nGraph.prototype.nodes = function () {\n  return _.keys(this._nodes);\n};\nGraph.prototype.sources = function () {\n  var self = this;\n  return _.filter(this.nodes(), function (v) {\n    return _.isEmpty(self._in[v]);\n  });\n};\nGraph.prototype.sinks = function () {\n  var self = this;\n  return _.filter(this.nodes(), function (v) {\n    return _.isEmpty(self._out[v]);\n  });\n};\nGraph.prototype.setNodes = function (vs, value) {\n  var args = arguments;\n  var self = this;\n  _.each(vs, function (v) {\n    if (args.length > 1) {\n      self.setNode(v, value);\n    } else {\n      self.setNode(v);\n    }\n  });\n  return this;\n};\nGraph.prototype.setNode = function (v, value) {\n  if (_.has(this._nodes, v)) {\n    if (arguments.length > 1) {\n      this._nodes[v] = value;\n    }\n    return this;\n  }\n  this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n  if (this._isCompound) {\n    this._parent[v] = GRAPH_NODE;\n    this._children[v] = {};\n    this._children[GRAPH_NODE][v] = true;\n  }\n  this._in[v] = {};\n  this._preds[v] = {};\n  this._out[v] = {};\n  this._sucs[v] = {};\n  ++this._nodeCount;\n  return this;\n};\nGraph.prototype.node = function (v) {\n  return this._nodes[v];\n};\nGraph.prototype.hasNode = function (v) {\n  return _.has(this._nodes, v);\n};\nGraph.prototype.removeNode = function (v) {\n  var self = this;\n  if (_.has(this._nodes, v)) {\n    var removeEdge = function (e) {\n      self.removeEdge(self._edgeObjs[e]);\n    };\n    delete this._nodes[v];\n    if (this._isCompound) {\n      this._removeFromParentsChildList(v);\n      delete this._parent[v];\n      _.each(this.children(v), function (child) {\n        self.setParent(child);\n      });\n      delete this._children[v];\n    }\n    _.each(_.keys(this._in[v]), removeEdge);\n    delete this._in[v];\n    delete this._preds[v];\n    _.each(_.keys(this._out[v]), removeEdge);\n    delete this._out[v];\n    delete this._sucs[v];\n    --this._nodeCount;\n  }\n  return this;\n};\nGraph.prototype.setParent = function (v, parent) {\n  if (!this._isCompound) {\n    throw new Error(\"Cannot set parent in a non-compound graph\");\n  }\n  if (_.isUndefined(parent)) {\n    parent = GRAPH_NODE;\n  } else {\n    // Coerce parent to string\n    parent += \"\";\n    for (var ancestor = parent; !_.isUndefined(ancestor); ancestor = this.parent(ancestor)) {\n      if (ancestor === v) {\n        throw new Error(\"Setting \" + parent + \" as parent of \" + v + \" would create a cycle\");\n      }\n    }\n    this.setNode(parent);\n  }\n  this.setNode(v);\n  this._removeFromParentsChildList(v);\n  this._parent[v] = parent;\n  this._children[parent][v] = true;\n  return this;\n};\nGraph.prototype._removeFromParentsChildList = function (v) {\n  delete this._children[this._parent[v]][v];\n};\nGraph.prototype.parent = function (v) {\n  if (this._isCompound) {\n    var parent = this._parent[v];\n    if (parent !== GRAPH_NODE) {\n      return parent;\n    }\n  }\n};\nGraph.prototype.children = function (v) {\n  if (_.isUndefined(v)) {\n    v = GRAPH_NODE;\n  }\n  if (this._isCompound) {\n    var children = this._children[v];\n    if (children) {\n      return _.keys(children);\n    }\n  } else if (v === GRAPH_NODE) {\n    return this.nodes();\n  } else if (this.hasNode(v)) {\n    return [];\n  }\n};\nGraph.prototype.predecessors = function (v) {\n  var predsV = this._preds[v];\n  if (predsV) {\n    return _.keys(predsV);\n  }\n};\nGraph.prototype.successors = function (v) {\n  var sucsV = this._sucs[v];\n  if (sucsV) {\n    return _.keys(sucsV);\n  }\n};\nGraph.prototype.neighbors = function (v) {\n  var preds = this.predecessors(v);\n  if (preds) {\n    return _.union(preds, this.successors(v));\n  }\n};\nGraph.prototype.isLeaf = function (v) {\n  var neighbors;\n  if (this.isDirected()) {\n    neighbors = this.successors(v);\n  } else {\n    neighbors = this.neighbors(v);\n  }\n  return neighbors.length === 0;\n};\nGraph.prototype.filterNodes = function (filter) {\n  var copy = new this.constructor({\n    directed: this._isDirected,\n    multigraph: this._isMultigraph,\n    compound: this._isCompound\n  });\n  copy.setGraph(this.graph());\n  var self = this;\n  _.each(this._nodes, function (value, v) {\n    if (filter(v)) {\n      copy.setNode(v, value);\n    }\n  });\n  _.each(this._edgeObjs, function (e) {\n    if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n      copy.setEdge(e, self.edge(e));\n    }\n  });\n  var parents = {};\n  function findParent(v) {\n    var parent = self.parent(v);\n    if (parent === undefined || copy.hasNode(parent)) {\n      parents[v] = parent;\n      return parent;\n    } else if (parent in parents) {\n      return parents[parent];\n    } else {\n      return findParent(parent);\n    }\n  }\n  if (this._isCompound) {\n    _.each(copy.nodes(), function (v) {\n      copy.setParent(v, findParent(v));\n    });\n  }\n  return copy;\n};\n\n/* === Edge functions ========== */\n\nGraph.prototype.setDefaultEdgeLabel = function (newDefault) {\n  if (!_.isFunction(newDefault)) {\n    newDefault = _.constant(newDefault);\n  }\n  this._defaultEdgeLabelFn = newDefault;\n  return this;\n};\nGraph.prototype.edgeCount = function () {\n  return this._edgeCount;\n};\nGraph.prototype.edges = function () {\n  return _.values(this._edgeObjs);\n};\nGraph.prototype.setPath = function (vs, value) {\n  var self = this;\n  var args = arguments;\n  _.reduce(vs, function (v, w) {\n    if (args.length > 1) {\n      self.setEdge(v, w, value);\n    } else {\n      self.setEdge(v, w);\n    }\n    return w;\n  });\n  return this;\n};\n\n/*\n * setEdge(v, w, [value, [name]])\n * setEdge({ v, w, [name] }, [value])\n */\nGraph.prototype.setEdge = function () {\n  var v, w, name, value;\n  var valueSpecified = false;\n  var arg0 = arguments[0];\n  if (typeof arg0 === \"object\" && arg0 !== null && \"v\" in arg0) {\n    v = arg0.v;\n    w = arg0.w;\n    name = arg0.name;\n    if (arguments.length === 2) {\n      value = arguments[1];\n      valueSpecified = true;\n    }\n  } else {\n    v = arg0;\n    w = arguments[1];\n    name = arguments[3];\n    if (arguments.length > 2) {\n      value = arguments[2];\n      valueSpecified = true;\n    }\n  }\n  v = \"\" + v;\n  w = \"\" + w;\n  if (!_.isUndefined(name)) {\n    name = \"\" + name;\n  }\n  var e = edgeArgsToId(this._isDirected, v, w, name);\n  if (_.has(this._edgeLabels, e)) {\n    if (valueSpecified) {\n      this._edgeLabels[e] = value;\n    }\n    return this;\n  }\n  if (!_.isUndefined(name) && !this._isMultigraph) {\n    throw new Error(\"Cannot set a named edge when isMultigraph = false\");\n  }\n\n  // It didn't exist, so we need to create it.\n  // First ensure the nodes exist.\n  this.setNode(v);\n  this.setNode(w);\n  this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n  var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n  // Ensure we add undirected edges in a consistent way.\n  v = edgeObj.v;\n  w = edgeObj.w;\n  Object.freeze(edgeObj);\n  this._edgeObjs[e] = edgeObj;\n  incrementOrInitEntry(this._preds[w], v);\n  incrementOrInitEntry(this._sucs[v], w);\n  this._in[w][e] = edgeObj;\n  this._out[v][e] = edgeObj;\n  this._edgeCount++;\n  return this;\n};\nGraph.prototype.edge = function (v, w, name) {\n  var e = arguments.length === 1 ? edgeObjToId(this._isDirected, arguments[0]) : edgeArgsToId(this._isDirected, v, w, name);\n  return this._edgeLabels[e];\n};\nGraph.prototype.hasEdge = function (v, w, name) {\n  var e = arguments.length === 1 ? edgeObjToId(this._isDirected, arguments[0]) : edgeArgsToId(this._isDirected, v, w, name);\n  return _.has(this._edgeLabels, e);\n};\nGraph.prototype.removeEdge = function (v, w, name) {\n  var e = arguments.length === 1 ? edgeObjToId(this._isDirected, arguments[0]) : edgeArgsToId(this._isDirected, v, w, name);\n  var edge = this._edgeObjs[e];\n  if (edge) {\n    v = edge.v;\n    w = edge.w;\n    delete this._edgeLabels[e];\n    delete this._edgeObjs[e];\n    decrementOrRemoveEntry(this._preds[w], v);\n    decrementOrRemoveEntry(this._sucs[v], w);\n    delete this._in[w][e];\n    delete this._out[v][e];\n    this._edgeCount--;\n  }\n  return this;\n};\nGraph.prototype.inEdges = function (v, u) {\n  var inV = this._in[v];\n  if (inV) {\n    var edges = _.values(inV);\n    if (!u) {\n      return edges;\n    }\n    return _.filter(edges, function (edge) {\n      return edge.v === u;\n    });\n  }\n};\nGraph.prototype.outEdges = function (v, w) {\n  var outV = this._out[v];\n  if (outV) {\n    var edges = _.values(outV);\n    if (!w) {\n      return edges;\n    }\n    return _.filter(edges, function (edge) {\n      return edge.w === w;\n    });\n  }\n};\nGraph.prototype.nodeEdges = function (v, w) {\n  var inEdges = this.inEdges(v, w);\n  if (inEdges) {\n    return inEdges.concat(this.outEdges(v, w));\n  }\n};\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\nfunction decrementOrRemoveEntry(map, k) {\n  if (! --map[k]) {\n    delete map[k];\n  }\n}\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM + (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj = {\n    v: v,\n    w: w\n  };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "Graph", "DEFAULT_EDGE_NAME", "GRAPH_NODE", "EDGE_KEY_DELIM", "opts", "_isDirected", "has", "directed", "_isMultigraph", "multigraph", "_isCompound", "compound", "_label", "undefined", "_defaultNodeLabelFn", "constant", "_defaultEdgeLabelFn", "_nodes", "_parent", "_children", "_in", "_preds", "_out", "_sucs", "_edgeObjs", "_edgeLabels", "prototype", "_nodeCount", "_edgeCount", "isDirected", "isMultigraph", "isCompound", "setGraph", "label", "graph", "setDefaultNodeLabel", "newDefault", "isFunction", "nodeCount", "nodes", "keys", "sources", "self", "filter", "v", "isEmpty", "sinks", "setNodes", "vs", "value", "args", "arguments", "each", "length", "setNode", "node", "hasNode", "removeNode", "removeEdge", "e", "_removeFromParentsChildList", "children", "child", "setParent", "parent", "Error", "isUndefined", "ancestor", "predecessors", "predsV", "successors", "sucsV", "neighbors", "preds", "union", "<PERSON><PERSON><PERSON><PERSON>", "filterNodes", "copy", "constructor", "w", "setEdge", "edge", "parents", "findParent", "setDefaultEdgeLabel", "edgeCount", "edges", "values", "set<PERSON>ath", "reduce", "name", "valueSpecified", "arg0", "edgeArgsToId", "edgeObj", "edgeArgsToObj", "Object", "freeze", "incrementOrInitEntry", "edgeObjToId", "hasEdge", "decrementOrRemoveEntry", "inEdges", "u", "inV", "outEdges", "outV", "nodeEdges", "concat", "map", "k", "v_", "w_", "tmp"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/graph.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"./lodash\");\n\nmodule.exports = Graph;\n\nvar DEFAULT_EDGE_NAME = \"\\x00\";\nvar GRAPH_NODE = \"\\x00\";\nvar EDGE_KEY_DELIM = \"\\x01\";\n\n// Implementation notes:\n//\n//  * Node id query functions should return string ids for the nodes\n//  * Edge id query functions should return an \"edgeObj\", edge object, that is\n//    composed of enough information to uniquely identify an edge: {v, w, name}.\n//  * Internally we use an \"edgeId\", a stringified form of the edgeObj, to\n//    reference edges. This is because we need a performant way to look these\n//    edges up and, object properties, which have string keys, are the closest\n//    we're going to get to a performant hashtable in JavaScript.\n\nfunction Graph(opts) {\n  this._isDirected = _.has(opts, \"directed\") ? opts.directed : true;\n  this._isMultigraph = _.has(opts, \"multigraph\") ? opts.multigraph : false;\n  this._isCompound = _.has(opts, \"compound\") ? opts.compound : false;\n\n  // Label for the graph itself\n  this._label = undefined;\n\n  // Defaults to be set when creating a new node\n  this._defaultNodeLabelFn = _.constant(undefined);\n\n  // Defaults to be set when creating a new edge\n  this._defaultEdgeLabelFn = _.constant(undefined);\n\n  // v -> label\n  this._nodes = {};\n\n  if (this._isCompound) {\n    // v -> parent\n    this._parent = {};\n\n    // v -> children\n    this._children = {};\n    this._children[GRAPH_NODE] = {};\n  }\n\n  // v -> edgeObj\n  this._in = {};\n\n  // u -> v -> Number\n  this._preds = {};\n\n  // v -> edgeObj\n  this._out = {};\n\n  // v -> w -> Number\n  this._sucs = {};\n\n  // e -> edgeObj\n  this._edgeObjs = {};\n\n  // e -> label\n  this._edgeLabels = {};\n}\n\n/* Number of nodes in the graph. Should only be changed by the implementation. */\nGraph.prototype._nodeCount = 0;\n\n/* Number of edges in the graph. Should only be changed by the implementation. */\nGraph.prototype._edgeCount = 0;\n\n\n/* === Graph functions ========= */\n\nGraph.prototype.isDirected = function() {\n  return this._isDirected;\n};\n\nGraph.prototype.isMultigraph = function() {\n  return this._isMultigraph;\n};\n\nGraph.prototype.isCompound = function() {\n  return this._isCompound;\n};\n\nGraph.prototype.setGraph = function(label) {\n  this._label = label;\n  return this;\n};\n\nGraph.prototype.graph = function() {\n  return this._label;\n};\n\n\n/* === Node functions ========== */\n\nGraph.prototype.setDefaultNodeLabel = function(newDefault) {\n  if (!_.isFunction(newDefault)) {\n    newDefault = _.constant(newDefault);\n  }\n  this._defaultNodeLabelFn = newDefault;\n  return this;\n};\n\nGraph.prototype.nodeCount = function() {\n  return this._nodeCount;\n};\n\nGraph.prototype.nodes = function() {\n  return _.keys(this._nodes);\n};\n\nGraph.prototype.sources = function() {\n  var self = this;\n  return _.filter(this.nodes(), function(v) {\n    return _.isEmpty(self._in[v]);\n  });\n};\n\nGraph.prototype.sinks = function() {\n  var self = this;\n  return _.filter(this.nodes(), function(v) {\n    return _.isEmpty(self._out[v]);\n  });\n};\n\nGraph.prototype.setNodes = function(vs, value) {\n  var args = arguments;\n  var self = this;\n  _.each(vs, function(v) {\n    if (args.length > 1) {\n      self.setNode(v, value);\n    } else {\n      self.setNode(v);\n    }\n  });\n  return this;\n};\n\nGraph.prototype.setNode = function(v, value) {\n  if (_.has(this._nodes, v)) {\n    if (arguments.length > 1) {\n      this._nodes[v] = value;\n    }\n    return this;\n  }\n\n  this._nodes[v] = arguments.length > 1 ? value : this._defaultNodeLabelFn(v);\n  if (this._isCompound) {\n    this._parent[v] = GRAPH_NODE;\n    this._children[v] = {};\n    this._children[GRAPH_NODE][v] = true;\n  }\n  this._in[v] = {};\n  this._preds[v] = {};\n  this._out[v] = {};\n  this._sucs[v] = {};\n  ++this._nodeCount;\n  return this;\n};\n\nGraph.prototype.node = function(v) {\n  return this._nodes[v];\n};\n\nGraph.prototype.hasNode = function(v) {\n  return _.has(this._nodes, v);\n};\n\nGraph.prototype.removeNode =  function(v) {\n  var self = this;\n  if (_.has(this._nodes, v)) {\n    var removeEdge = function(e) { self.removeEdge(self._edgeObjs[e]); };\n    delete this._nodes[v];\n    if (this._isCompound) {\n      this._removeFromParentsChildList(v);\n      delete this._parent[v];\n      _.each(this.children(v), function(child) {\n        self.setParent(child);\n      });\n      delete this._children[v];\n    }\n    _.each(_.keys(this._in[v]), removeEdge);\n    delete this._in[v];\n    delete this._preds[v];\n    _.each(_.keys(this._out[v]), removeEdge);\n    delete this._out[v];\n    delete this._sucs[v];\n    --this._nodeCount;\n  }\n  return this;\n};\n\nGraph.prototype.setParent = function(v, parent) {\n  if (!this._isCompound) {\n    throw new Error(\"Cannot set parent in a non-compound graph\");\n  }\n\n  if (_.isUndefined(parent)) {\n    parent = GRAPH_NODE;\n  } else {\n    // Coerce parent to string\n    parent += \"\";\n    for (var ancestor = parent;\n      !_.isUndefined(ancestor);\n      ancestor = this.parent(ancestor)) {\n      if (ancestor === v) {\n        throw new Error(\"Setting \" + parent+ \" as parent of \" + v +\n                        \" would create a cycle\");\n      }\n    }\n\n    this.setNode(parent);\n  }\n\n  this.setNode(v);\n  this._removeFromParentsChildList(v);\n  this._parent[v] = parent;\n  this._children[parent][v] = true;\n  return this;\n};\n\nGraph.prototype._removeFromParentsChildList = function(v) {\n  delete this._children[this._parent[v]][v];\n};\n\nGraph.prototype.parent = function(v) {\n  if (this._isCompound) {\n    var parent = this._parent[v];\n    if (parent !== GRAPH_NODE) {\n      return parent;\n    }\n  }\n};\n\nGraph.prototype.children = function(v) {\n  if (_.isUndefined(v)) {\n    v = GRAPH_NODE;\n  }\n\n  if (this._isCompound) {\n    var children = this._children[v];\n    if (children) {\n      return _.keys(children);\n    }\n  } else if (v === GRAPH_NODE) {\n    return this.nodes();\n  } else if (this.hasNode(v)) {\n    return [];\n  }\n};\n\nGraph.prototype.predecessors = function(v) {\n  var predsV = this._preds[v];\n  if (predsV) {\n    return _.keys(predsV);\n  }\n};\n\nGraph.prototype.successors = function(v) {\n  var sucsV = this._sucs[v];\n  if (sucsV) {\n    return _.keys(sucsV);\n  }\n};\n\nGraph.prototype.neighbors = function(v) {\n  var preds = this.predecessors(v);\n  if (preds) {\n    return _.union(preds, this.successors(v));\n  }\n};\n\nGraph.prototype.isLeaf = function (v) {\n  var neighbors;\n  if (this.isDirected()) {\n    neighbors = this.successors(v);\n  } else {\n    neighbors = this.neighbors(v);\n  }\n  return neighbors.length === 0;\n};\n\nGraph.prototype.filterNodes = function(filter) {\n  var copy = new this.constructor({\n    directed: this._isDirected,\n    multigraph: this._isMultigraph,\n    compound: this._isCompound\n  });\n\n  copy.setGraph(this.graph());\n\n  var self = this;\n  _.each(this._nodes, function(value, v) {\n    if (filter(v)) {\n      copy.setNode(v, value);\n    }\n  });\n\n  _.each(this._edgeObjs, function(e) {\n    if (copy.hasNode(e.v) && copy.hasNode(e.w)) {\n      copy.setEdge(e, self.edge(e));\n    }\n  });\n\n  var parents = {};\n  function findParent(v) {\n    var parent = self.parent(v);\n    if (parent === undefined || copy.hasNode(parent)) {\n      parents[v] = parent;\n      return parent;\n    } else if (parent in parents) {\n      return parents[parent];\n    } else {\n      return findParent(parent);\n    }\n  }\n\n  if (this._isCompound) {\n    _.each(copy.nodes(), function(v) {\n      copy.setParent(v, findParent(v));\n    });\n  }\n\n  return copy;\n};\n\n/* === Edge functions ========== */\n\nGraph.prototype.setDefaultEdgeLabel = function(newDefault) {\n  if (!_.isFunction(newDefault)) {\n    newDefault = _.constant(newDefault);\n  }\n  this._defaultEdgeLabelFn = newDefault;\n  return this;\n};\n\nGraph.prototype.edgeCount = function() {\n  return this._edgeCount;\n};\n\nGraph.prototype.edges = function() {\n  return _.values(this._edgeObjs);\n};\n\nGraph.prototype.setPath = function(vs, value) {\n  var self = this;\n  var args = arguments;\n  _.reduce(vs, function(v, w) {\n    if (args.length > 1) {\n      self.setEdge(v, w, value);\n    } else {\n      self.setEdge(v, w);\n    }\n    return w;\n  });\n  return this;\n};\n\n/*\n * setEdge(v, w, [value, [name]])\n * setEdge({ v, w, [name] }, [value])\n */\nGraph.prototype.setEdge = function() {\n  var v, w, name, value;\n  var valueSpecified = false;\n  var arg0 = arguments[0];\n\n  if (typeof arg0 === \"object\" && arg0 !== null && \"v\" in arg0) {\n    v = arg0.v;\n    w = arg0.w;\n    name = arg0.name;\n    if (arguments.length === 2) {\n      value = arguments[1];\n      valueSpecified = true;\n    }\n  } else {\n    v = arg0;\n    w = arguments[1];\n    name = arguments[3];\n    if (arguments.length > 2) {\n      value = arguments[2];\n      valueSpecified = true;\n    }\n  }\n\n  v = \"\" + v;\n  w = \"\" + w;\n  if (!_.isUndefined(name)) {\n    name = \"\" + name;\n  }\n\n  var e = edgeArgsToId(this._isDirected, v, w, name);\n  if (_.has(this._edgeLabels, e)) {\n    if (valueSpecified) {\n      this._edgeLabels[e] = value;\n    }\n    return this;\n  }\n\n  if (!_.isUndefined(name) && !this._isMultigraph) {\n    throw new Error(\"Cannot set a named edge when isMultigraph = false\");\n  }\n\n  // It didn't exist, so we need to create it.\n  // First ensure the nodes exist.\n  this.setNode(v);\n  this.setNode(w);\n\n  this._edgeLabels[e] = valueSpecified ? value : this._defaultEdgeLabelFn(v, w, name);\n\n  var edgeObj = edgeArgsToObj(this._isDirected, v, w, name);\n  // Ensure we add undirected edges in a consistent way.\n  v = edgeObj.v;\n  w = edgeObj.w;\n\n  Object.freeze(edgeObj);\n  this._edgeObjs[e] = edgeObj;\n  incrementOrInitEntry(this._preds[w], v);\n  incrementOrInitEntry(this._sucs[v], w);\n  this._in[w][e] = edgeObj;\n  this._out[v][e] = edgeObj;\n  this._edgeCount++;\n  return this;\n};\n\nGraph.prototype.edge = function(v, w, name) {\n  var e = (arguments.length === 1\n    ? edgeObjToId(this._isDirected, arguments[0])\n    : edgeArgsToId(this._isDirected, v, w, name));\n  return this._edgeLabels[e];\n};\n\nGraph.prototype.hasEdge = function(v, w, name) {\n  var e = (arguments.length === 1\n    ? edgeObjToId(this._isDirected, arguments[0])\n    : edgeArgsToId(this._isDirected, v, w, name));\n  return _.has(this._edgeLabels, e);\n};\n\nGraph.prototype.removeEdge = function(v, w, name) {\n  var e = (arguments.length === 1\n    ? edgeObjToId(this._isDirected, arguments[0])\n    : edgeArgsToId(this._isDirected, v, w, name));\n  var edge = this._edgeObjs[e];\n  if (edge) {\n    v = edge.v;\n    w = edge.w;\n    delete this._edgeLabels[e];\n    delete this._edgeObjs[e];\n    decrementOrRemoveEntry(this._preds[w], v);\n    decrementOrRemoveEntry(this._sucs[v], w);\n    delete this._in[w][e];\n    delete this._out[v][e];\n    this._edgeCount--;\n  }\n  return this;\n};\n\nGraph.prototype.inEdges = function(v, u) {\n  var inV = this._in[v];\n  if (inV) {\n    var edges = _.values(inV);\n    if (!u) {\n      return edges;\n    }\n    return _.filter(edges, function(edge) { return edge.v === u; });\n  }\n};\n\nGraph.prototype.outEdges = function(v, w) {\n  var outV = this._out[v];\n  if (outV) {\n    var edges = _.values(outV);\n    if (!w) {\n      return edges;\n    }\n    return _.filter(edges, function(edge) { return edge.w === w; });\n  }\n};\n\nGraph.prototype.nodeEdges = function(v, w) {\n  var inEdges = this.inEdges(v, w);\n  if (inEdges) {\n    return inEdges.concat(this.outEdges(v, w));\n  }\n};\n\nfunction incrementOrInitEntry(map, k) {\n  if (map[k]) {\n    map[k]++;\n  } else {\n    map[k] = 1;\n  }\n}\n\nfunction decrementOrRemoveEntry(map, k) {\n  if (!--map[k]) { delete map[k]; }\n}\n\nfunction edgeArgsToId(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  return v + EDGE_KEY_DELIM + w + EDGE_KEY_DELIM +\n             (_.isUndefined(name) ? DEFAULT_EDGE_NAME : name);\n}\n\nfunction edgeArgsToObj(isDirected, v_, w_, name) {\n  var v = \"\" + v_;\n  var w = \"\" + w_;\n  if (!isDirected && v > w) {\n    var tmp = v;\n    v = w;\n    w = tmp;\n  }\n  var edgeObj =  { v: v, w: w };\n  if (name) {\n    edgeObj.name = name;\n  }\n  return edgeObj;\n}\n\nfunction edgeObjToId(isDirected, edgeObj) {\n  return edgeArgsToId(isDirected, edgeObj.v, edgeObj.w, edgeObj.name);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAE3BC,MAAM,CAACC,OAAO,GAAGC,KAAK;AAEtB,IAAIC,iBAAiB,GAAG,MAAM;AAC9B,IAAIC,UAAU,GAAG,MAAM;AACvB,IAAIC,cAAc,GAAG,MAAM;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASH,KAAKA,CAACI,IAAI,EAAE;EACnB,IAAI,CAACC,WAAW,GAAGT,CAAC,CAACU,GAAG,CAACF,IAAI,EAAE,UAAU,CAAC,GAAGA,IAAI,CAACG,QAAQ,GAAG,IAAI;EACjE,IAAI,CAACC,aAAa,GAAGZ,CAAC,CAACU,GAAG,CAACF,IAAI,EAAE,YAAY,CAAC,GAAGA,IAAI,CAACK,UAAU,GAAG,KAAK;EACxE,IAAI,CAACC,WAAW,GAAGd,CAAC,CAACU,GAAG,CAACF,IAAI,EAAE,UAAU,CAAC,GAAGA,IAAI,CAACO,QAAQ,GAAG,KAAK;;EAElE;EACA,IAAI,CAACC,MAAM,GAAGC,SAAS;;EAEvB;EACA,IAAI,CAACC,mBAAmB,GAAGlB,CAAC,CAACmB,QAAQ,CAACF,SAAS,CAAC;;EAEhD;EACA,IAAI,CAACG,mBAAmB,GAAGpB,CAAC,CAACmB,QAAQ,CAACF,SAAS,CAAC;;EAEhD;EACA,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;EAEhB,IAAI,IAAI,CAACP,WAAW,EAAE;IACpB;IACA,IAAI,CAACQ,OAAO,GAAG,CAAC,CAAC;;IAEjB;IACA,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACA,SAAS,CAACjB,UAAU,CAAC,GAAG,CAAC,CAAC;EACjC;;EAEA;EACA,IAAI,CAACkB,GAAG,GAAG,CAAC,CAAC;;EAEb;EACA,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;;EAEhB;EACA,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC;;EAEd;EACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;;EAEf;EACA,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;;EAEnB;EACA,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;AACvB;;AAEA;AACAzB,KAAK,CAAC0B,SAAS,CAACC,UAAU,GAAG,CAAC;;AAE9B;AACA3B,KAAK,CAAC0B,SAAS,CAACE,UAAU,GAAG,CAAC;;AAG9B;;AAEA5B,KAAK,CAAC0B,SAAS,CAACG,UAAU,GAAG,YAAW;EACtC,OAAO,IAAI,CAACxB,WAAW;AACzB,CAAC;AAEDL,KAAK,CAAC0B,SAAS,CAACI,YAAY,GAAG,YAAW;EACxC,OAAO,IAAI,CAACtB,aAAa;AAC3B,CAAC;AAEDR,KAAK,CAAC0B,SAAS,CAACK,UAAU,GAAG,YAAW;EACtC,OAAO,IAAI,CAACrB,WAAW;AACzB,CAAC;AAEDV,KAAK,CAAC0B,SAAS,CAACM,QAAQ,GAAG,UAASC,KAAK,EAAE;EACzC,IAAI,CAACrB,MAAM,GAAGqB,KAAK;EACnB,OAAO,IAAI;AACb,CAAC;AAEDjC,KAAK,CAAC0B,SAAS,CAACQ,KAAK,GAAG,YAAW;EACjC,OAAO,IAAI,CAACtB,MAAM;AACpB,CAAC;;AAGD;;AAEAZ,KAAK,CAAC0B,SAAS,CAACS,mBAAmB,GAAG,UAASC,UAAU,EAAE;EACzD,IAAI,CAACxC,CAAC,CAACyC,UAAU,CAACD,UAAU,CAAC,EAAE;IAC7BA,UAAU,GAAGxC,CAAC,CAACmB,QAAQ,CAACqB,UAAU,CAAC;EACrC;EACA,IAAI,CAACtB,mBAAmB,GAAGsB,UAAU;EACrC,OAAO,IAAI;AACb,CAAC;AAEDpC,KAAK,CAAC0B,SAAS,CAACY,SAAS,GAAG,YAAW;EACrC,OAAO,IAAI,CAACX,UAAU;AACxB,CAAC;AAED3B,KAAK,CAAC0B,SAAS,CAACa,KAAK,GAAG,YAAW;EACjC,OAAO3C,CAAC,CAAC4C,IAAI,CAAC,IAAI,CAACvB,MAAM,CAAC;AAC5B,CAAC;AAEDjB,KAAK,CAAC0B,SAAS,CAACe,OAAO,GAAG,YAAW;EACnC,IAAIC,IAAI,GAAG,IAAI;EACf,OAAO9C,CAAC,CAAC+C,MAAM,CAAC,IAAI,CAACJ,KAAK,CAAC,CAAC,EAAE,UAASK,CAAC,EAAE;IACxC,OAAOhD,CAAC,CAACiD,OAAO,CAACH,IAAI,CAACtB,GAAG,CAACwB,CAAC,CAAC,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAACoB,KAAK,GAAG,YAAW;EACjC,IAAIJ,IAAI,GAAG,IAAI;EACf,OAAO9C,CAAC,CAAC+C,MAAM,CAAC,IAAI,CAACJ,KAAK,CAAC,CAAC,EAAE,UAASK,CAAC,EAAE;IACxC,OAAOhD,CAAC,CAACiD,OAAO,CAACH,IAAI,CAACpB,IAAI,CAACsB,CAAC,CAAC,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAACqB,QAAQ,GAAG,UAASC,EAAE,EAAEC,KAAK,EAAE;EAC7C,IAAIC,IAAI,GAAGC,SAAS;EACpB,IAAIT,IAAI,GAAG,IAAI;EACf9C,CAAC,CAACwD,IAAI,CAACJ,EAAE,EAAE,UAASJ,CAAC,EAAE;IACrB,IAAIM,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;MACnBX,IAAI,CAACY,OAAO,CAACV,CAAC,EAAEK,KAAK,CAAC;IACxB,CAAC,MAAM;MACLP,IAAI,CAACY,OAAO,CAACV,CAAC,CAAC;IACjB;EACF,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAAC4B,OAAO,GAAG,UAASV,CAAC,EAAEK,KAAK,EAAE;EAC3C,IAAIrD,CAAC,CAACU,GAAG,CAAC,IAAI,CAACW,MAAM,EAAE2B,CAAC,CAAC,EAAE;IACzB,IAAIO,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;MACxB,IAAI,CAACpC,MAAM,CAAC2B,CAAC,CAAC,GAAGK,KAAK;IACxB;IACA,OAAO,IAAI;EACb;EAEA,IAAI,CAAChC,MAAM,CAAC2B,CAAC,CAAC,GAAGO,SAAS,CAACE,MAAM,GAAG,CAAC,GAAGJ,KAAK,GAAG,IAAI,CAACnC,mBAAmB,CAAC8B,CAAC,CAAC;EAC3E,IAAI,IAAI,CAAClC,WAAW,EAAE;IACpB,IAAI,CAACQ,OAAO,CAAC0B,CAAC,CAAC,GAAG1C,UAAU;IAC5B,IAAI,CAACiB,SAAS,CAACyB,CAAC,CAAC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACzB,SAAS,CAACjB,UAAU,CAAC,CAAC0C,CAAC,CAAC,GAAG,IAAI;EACtC;EACA,IAAI,CAACxB,GAAG,CAACwB,CAAC,CAAC,GAAG,CAAC,CAAC;EAChB,IAAI,CAACvB,MAAM,CAACuB,CAAC,CAAC,GAAG,CAAC,CAAC;EACnB,IAAI,CAACtB,IAAI,CAACsB,CAAC,CAAC,GAAG,CAAC,CAAC;EACjB,IAAI,CAACrB,KAAK,CAACqB,CAAC,CAAC,GAAG,CAAC,CAAC;EAClB,EAAE,IAAI,CAACjB,UAAU;EACjB,OAAO,IAAI;AACb,CAAC;AAED3B,KAAK,CAAC0B,SAAS,CAAC6B,IAAI,GAAG,UAASX,CAAC,EAAE;EACjC,OAAO,IAAI,CAAC3B,MAAM,CAAC2B,CAAC,CAAC;AACvB,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAAC8B,OAAO,GAAG,UAASZ,CAAC,EAAE;EACpC,OAAOhD,CAAC,CAACU,GAAG,CAAC,IAAI,CAACW,MAAM,EAAE2B,CAAC,CAAC;AAC9B,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAAC+B,UAAU,GAAI,UAASb,CAAC,EAAE;EACxC,IAAIF,IAAI,GAAG,IAAI;EACf,IAAI9C,CAAC,CAACU,GAAG,CAAC,IAAI,CAACW,MAAM,EAAE2B,CAAC,CAAC,EAAE;IACzB,IAAIc,UAAU,GAAG,SAAAA,CAASC,CAAC,EAAE;MAAEjB,IAAI,CAACgB,UAAU,CAAChB,IAAI,CAAClB,SAAS,CAACmC,CAAC,CAAC,CAAC;IAAE,CAAC;IACpE,OAAO,IAAI,CAAC1C,MAAM,CAAC2B,CAAC,CAAC;IACrB,IAAI,IAAI,CAAClC,WAAW,EAAE;MACpB,IAAI,CAACkD,2BAA2B,CAAChB,CAAC,CAAC;MACnC,OAAO,IAAI,CAAC1B,OAAO,CAAC0B,CAAC,CAAC;MACtBhD,CAAC,CAACwD,IAAI,CAAC,IAAI,CAACS,QAAQ,CAACjB,CAAC,CAAC,EAAE,UAASkB,KAAK,EAAE;QACvCpB,IAAI,CAACqB,SAAS,CAACD,KAAK,CAAC;MACvB,CAAC,CAAC;MACF,OAAO,IAAI,CAAC3C,SAAS,CAACyB,CAAC,CAAC;IAC1B;IACAhD,CAAC,CAACwD,IAAI,CAACxD,CAAC,CAAC4C,IAAI,CAAC,IAAI,CAACpB,GAAG,CAACwB,CAAC,CAAC,CAAC,EAAEc,UAAU,CAAC;IACvC,OAAO,IAAI,CAACtC,GAAG,CAACwB,CAAC,CAAC;IAClB,OAAO,IAAI,CAACvB,MAAM,CAACuB,CAAC,CAAC;IACrBhD,CAAC,CAACwD,IAAI,CAACxD,CAAC,CAAC4C,IAAI,CAAC,IAAI,CAAClB,IAAI,CAACsB,CAAC,CAAC,CAAC,EAAEc,UAAU,CAAC;IACxC,OAAO,IAAI,CAACpC,IAAI,CAACsB,CAAC,CAAC;IACnB,OAAO,IAAI,CAACrB,KAAK,CAACqB,CAAC,CAAC;IACpB,EAAE,IAAI,CAACjB,UAAU;EACnB;EACA,OAAO,IAAI;AACb,CAAC;AAED3B,KAAK,CAAC0B,SAAS,CAACqC,SAAS,GAAG,UAASnB,CAAC,EAAEoB,MAAM,EAAE;EAC9C,IAAI,CAAC,IAAI,CAACtD,WAAW,EAAE;IACrB,MAAM,IAAIuD,KAAK,CAAC,2CAA2C,CAAC;EAC9D;EAEA,IAAIrE,CAAC,CAACsE,WAAW,CAACF,MAAM,CAAC,EAAE;IACzBA,MAAM,GAAG9D,UAAU;EACrB,CAAC,MAAM;IACL;IACA8D,MAAM,IAAI,EAAE;IACZ,KAAK,IAAIG,QAAQ,GAAGH,MAAM,EACxB,CAACpE,CAAC,CAACsE,WAAW,CAACC,QAAQ,CAAC,EACxBA,QAAQ,GAAG,IAAI,CAACH,MAAM,CAACG,QAAQ,CAAC,EAAE;MAClC,IAAIA,QAAQ,KAAKvB,CAAC,EAAE;QAClB,MAAM,IAAIqB,KAAK,CAAC,UAAU,GAAGD,MAAM,GAAE,gBAAgB,GAAGpB,CAAC,GACzC,uBAAuB,CAAC;MAC1C;IACF;IAEA,IAAI,CAACU,OAAO,CAACU,MAAM,CAAC;EACtB;EAEA,IAAI,CAACV,OAAO,CAACV,CAAC,CAAC;EACf,IAAI,CAACgB,2BAA2B,CAAChB,CAAC,CAAC;EACnC,IAAI,CAAC1B,OAAO,CAAC0B,CAAC,CAAC,GAAGoB,MAAM;EACxB,IAAI,CAAC7C,SAAS,CAAC6C,MAAM,CAAC,CAACpB,CAAC,CAAC,GAAG,IAAI;EAChC,OAAO,IAAI;AACb,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAACkC,2BAA2B,GAAG,UAAShB,CAAC,EAAE;EACxD,OAAO,IAAI,CAACzB,SAAS,CAAC,IAAI,CAACD,OAAO,CAAC0B,CAAC,CAAC,CAAC,CAACA,CAAC,CAAC;AAC3C,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAACsC,MAAM,GAAG,UAASpB,CAAC,EAAE;EACnC,IAAI,IAAI,CAAClC,WAAW,EAAE;IACpB,IAAIsD,MAAM,GAAG,IAAI,CAAC9C,OAAO,CAAC0B,CAAC,CAAC;IAC5B,IAAIoB,MAAM,KAAK9D,UAAU,EAAE;MACzB,OAAO8D,MAAM;IACf;EACF;AACF,CAAC;AAEDhE,KAAK,CAAC0B,SAAS,CAACmC,QAAQ,GAAG,UAASjB,CAAC,EAAE;EACrC,IAAIhD,CAAC,CAACsE,WAAW,CAACtB,CAAC,CAAC,EAAE;IACpBA,CAAC,GAAG1C,UAAU;EAChB;EAEA,IAAI,IAAI,CAACQ,WAAW,EAAE;IACpB,IAAImD,QAAQ,GAAG,IAAI,CAAC1C,SAAS,CAACyB,CAAC,CAAC;IAChC,IAAIiB,QAAQ,EAAE;MACZ,OAAOjE,CAAC,CAAC4C,IAAI,CAACqB,QAAQ,CAAC;IACzB;EACF,CAAC,MAAM,IAAIjB,CAAC,KAAK1C,UAAU,EAAE;IAC3B,OAAO,IAAI,CAACqC,KAAK,CAAC,CAAC;EACrB,CAAC,MAAM,IAAI,IAAI,CAACiB,OAAO,CAACZ,CAAC,CAAC,EAAE;IAC1B,OAAO,EAAE;EACX;AACF,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAAC0C,YAAY,GAAG,UAASxB,CAAC,EAAE;EACzC,IAAIyB,MAAM,GAAG,IAAI,CAAChD,MAAM,CAACuB,CAAC,CAAC;EAC3B,IAAIyB,MAAM,EAAE;IACV,OAAOzE,CAAC,CAAC4C,IAAI,CAAC6B,MAAM,CAAC;EACvB;AACF,CAAC;AAEDrE,KAAK,CAAC0B,SAAS,CAAC4C,UAAU,GAAG,UAAS1B,CAAC,EAAE;EACvC,IAAI2B,KAAK,GAAG,IAAI,CAAChD,KAAK,CAACqB,CAAC,CAAC;EACzB,IAAI2B,KAAK,EAAE;IACT,OAAO3E,CAAC,CAAC4C,IAAI,CAAC+B,KAAK,CAAC;EACtB;AACF,CAAC;AAEDvE,KAAK,CAAC0B,SAAS,CAAC8C,SAAS,GAAG,UAAS5B,CAAC,EAAE;EACtC,IAAI6B,KAAK,GAAG,IAAI,CAACL,YAAY,CAACxB,CAAC,CAAC;EAChC,IAAI6B,KAAK,EAAE;IACT,OAAO7E,CAAC,CAAC8E,KAAK,CAACD,KAAK,EAAE,IAAI,CAACH,UAAU,CAAC1B,CAAC,CAAC,CAAC;EAC3C;AACF,CAAC;AAED5C,KAAK,CAAC0B,SAAS,CAACiD,MAAM,GAAG,UAAU/B,CAAC,EAAE;EACpC,IAAI4B,SAAS;EACb,IAAI,IAAI,CAAC3C,UAAU,CAAC,CAAC,EAAE;IACrB2C,SAAS,GAAG,IAAI,CAACF,UAAU,CAAC1B,CAAC,CAAC;EAChC,CAAC,MAAM;IACL4B,SAAS,GAAG,IAAI,CAACA,SAAS,CAAC5B,CAAC,CAAC;EAC/B;EACA,OAAO4B,SAAS,CAACnB,MAAM,KAAK,CAAC;AAC/B,CAAC;AAEDrD,KAAK,CAAC0B,SAAS,CAACkD,WAAW,GAAG,UAASjC,MAAM,EAAE;EAC7C,IAAIkC,IAAI,GAAG,IAAI,IAAI,CAACC,WAAW,CAAC;IAC9BvE,QAAQ,EAAE,IAAI,CAACF,WAAW;IAC1BI,UAAU,EAAE,IAAI,CAACD,aAAa;IAC9BG,QAAQ,EAAE,IAAI,CAACD;EACjB,CAAC,CAAC;EAEFmE,IAAI,CAAC7C,QAAQ,CAAC,IAAI,CAACE,KAAK,CAAC,CAAC,CAAC;EAE3B,IAAIQ,IAAI,GAAG,IAAI;EACf9C,CAAC,CAACwD,IAAI,CAAC,IAAI,CAACnC,MAAM,EAAE,UAASgC,KAAK,EAAEL,CAAC,EAAE;IACrC,IAAID,MAAM,CAACC,CAAC,CAAC,EAAE;MACbiC,IAAI,CAACvB,OAAO,CAACV,CAAC,EAAEK,KAAK,CAAC;IACxB;EACF,CAAC,CAAC;EAEFrD,CAAC,CAACwD,IAAI,CAAC,IAAI,CAAC5B,SAAS,EAAE,UAASmC,CAAC,EAAE;IACjC,IAAIkB,IAAI,CAACrB,OAAO,CAACG,CAAC,CAACf,CAAC,CAAC,IAAIiC,IAAI,CAACrB,OAAO,CAACG,CAAC,CAACoB,CAAC,CAAC,EAAE;MAC1CF,IAAI,CAACG,OAAO,CAACrB,CAAC,EAAEjB,IAAI,CAACuC,IAAI,CAACtB,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC,CAAC;EAEF,IAAIuB,OAAO,GAAG,CAAC,CAAC;EAChB,SAASC,UAAUA,CAACvC,CAAC,EAAE;IACrB,IAAIoB,MAAM,GAAGtB,IAAI,CAACsB,MAAM,CAACpB,CAAC,CAAC;IAC3B,IAAIoB,MAAM,KAAKnD,SAAS,IAAIgE,IAAI,CAACrB,OAAO,CAACQ,MAAM,CAAC,EAAE;MAChDkB,OAAO,CAACtC,CAAC,CAAC,GAAGoB,MAAM;MACnB,OAAOA,MAAM;IACf,CAAC,MAAM,IAAIA,MAAM,IAAIkB,OAAO,EAAE;MAC5B,OAAOA,OAAO,CAAClB,MAAM,CAAC;IACxB,CAAC,MAAM;MACL,OAAOmB,UAAU,CAACnB,MAAM,CAAC;IAC3B;EACF;EAEA,IAAI,IAAI,CAACtD,WAAW,EAAE;IACpBd,CAAC,CAACwD,IAAI,CAACyB,IAAI,CAACtC,KAAK,CAAC,CAAC,EAAE,UAASK,CAAC,EAAE;MAC/BiC,IAAI,CAACd,SAAS,CAACnB,CAAC,EAAEuC,UAAU,CAACvC,CAAC,CAAC,CAAC;IAClC,CAAC,CAAC;EACJ;EAEA,OAAOiC,IAAI;AACb,CAAC;;AAED;;AAEA7E,KAAK,CAAC0B,SAAS,CAAC0D,mBAAmB,GAAG,UAAShD,UAAU,EAAE;EACzD,IAAI,CAACxC,CAAC,CAACyC,UAAU,CAACD,UAAU,CAAC,EAAE;IAC7BA,UAAU,GAAGxC,CAAC,CAACmB,QAAQ,CAACqB,UAAU,CAAC;EACrC;EACA,IAAI,CAACpB,mBAAmB,GAAGoB,UAAU;EACrC,OAAO,IAAI;AACb,CAAC;AAEDpC,KAAK,CAAC0B,SAAS,CAAC2D,SAAS,GAAG,YAAW;EACrC,OAAO,IAAI,CAACzD,UAAU;AACxB,CAAC;AAED5B,KAAK,CAAC0B,SAAS,CAAC4D,KAAK,GAAG,YAAW;EACjC,OAAO1F,CAAC,CAAC2F,MAAM,CAAC,IAAI,CAAC/D,SAAS,CAAC;AACjC,CAAC;AAEDxB,KAAK,CAAC0B,SAAS,CAAC8D,OAAO,GAAG,UAASxC,EAAE,EAAEC,KAAK,EAAE;EAC5C,IAAIP,IAAI,GAAG,IAAI;EACf,IAAIQ,IAAI,GAAGC,SAAS;EACpBvD,CAAC,CAAC6F,MAAM,CAACzC,EAAE,EAAE,UAASJ,CAAC,EAAEmC,CAAC,EAAE;IAC1B,IAAI7B,IAAI,CAACG,MAAM,GAAG,CAAC,EAAE;MACnBX,IAAI,CAACsC,OAAO,CAACpC,CAAC,EAAEmC,CAAC,EAAE9B,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLP,IAAI,CAACsC,OAAO,CAACpC,CAAC,EAAEmC,CAAC,CAAC;IACpB;IACA,OAAOA,CAAC;EACV,CAAC,CAAC;EACF,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA/E,KAAK,CAAC0B,SAAS,CAACsD,OAAO,GAAG,YAAW;EACnC,IAAIpC,CAAC,EAAEmC,CAAC,EAAEW,IAAI,EAAEzC,KAAK;EACrB,IAAI0C,cAAc,GAAG,KAAK;EAC1B,IAAIC,IAAI,GAAGzC,SAAS,CAAC,CAAC,CAAC;EAEvB,IAAI,OAAOyC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,GAAG,IAAIA,IAAI,EAAE;IAC5DhD,CAAC,GAAGgD,IAAI,CAAChD,CAAC;IACVmC,CAAC,GAAGa,IAAI,CAACb,CAAC;IACVW,IAAI,GAAGE,IAAI,CAACF,IAAI;IAChB,IAAIvC,SAAS,CAACE,MAAM,KAAK,CAAC,EAAE;MAC1BJ,KAAK,GAAGE,SAAS,CAAC,CAAC,CAAC;MACpBwC,cAAc,GAAG,IAAI;IACvB;EACF,CAAC,MAAM;IACL/C,CAAC,GAAGgD,IAAI;IACRb,CAAC,GAAG5B,SAAS,CAAC,CAAC,CAAC;IAChBuC,IAAI,GAAGvC,SAAS,CAAC,CAAC,CAAC;IACnB,IAAIA,SAAS,CAACE,MAAM,GAAG,CAAC,EAAE;MACxBJ,KAAK,GAAGE,SAAS,CAAC,CAAC,CAAC;MACpBwC,cAAc,GAAG,IAAI;IACvB;EACF;EAEA/C,CAAC,GAAG,EAAE,GAAGA,CAAC;EACVmC,CAAC,GAAG,EAAE,GAAGA,CAAC;EACV,IAAI,CAACnF,CAAC,CAACsE,WAAW,CAACwB,IAAI,CAAC,EAAE;IACxBA,IAAI,GAAG,EAAE,GAAGA,IAAI;EAClB;EAEA,IAAI/B,CAAC,GAAGkC,YAAY,CAAC,IAAI,CAACxF,WAAW,EAAEuC,CAAC,EAAEmC,CAAC,EAAEW,IAAI,CAAC;EAClD,IAAI9F,CAAC,CAACU,GAAG,CAAC,IAAI,CAACmB,WAAW,EAAEkC,CAAC,CAAC,EAAE;IAC9B,IAAIgC,cAAc,EAAE;MAClB,IAAI,CAAClE,WAAW,CAACkC,CAAC,CAAC,GAAGV,KAAK;IAC7B;IACA,OAAO,IAAI;EACb;EAEA,IAAI,CAACrD,CAAC,CAACsE,WAAW,CAACwB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAClF,aAAa,EAAE;IAC/C,MAAM,IAAIyD,KAAK,CAAC,mDAAmD,CAAC;EACtE;;EAEA;EACA;EACA,IAAI,CAACX,OAAO,CAACV,CAAC,CAAC;EACf,IAAI,CAACU,OAAO,CAACyB,CAAC,CAAC;EAEf,IAAI,CAACtD,WAAW,CAACkC,CAAC,CAAC,GAAGgC,cAAc,GAAG1C,KAAK,GAAG,IAAI,CAACjC,mBAAmB,CAAC4B,CAAC,EAAEmC,CAAC,EAAEW,IAAI,CAAC;EAEnF,IAAII,OAAO,GAAGC,aAAa,CAAC,IAAI,CAAC1F,WAAW,EAAEuC,CAAC,EAAEmC,CAAC,EAAEW,IAAI,CAAC;EACzD;EACA9C,CAAC,GAAGkD,OAAO,CAAClD,CAAC;EACbmC,CAAC,GAAGe,OAAO,CAACf,CAAC;EAEbiB,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC;EACtB,IAAI,CAACtE,SAAS,CAACmC,CAAC,CAAC,GAAGmC,OAAO;EAC3BI,oBAAoB,CAAC,IAAI,CAAC7E,MAAM,CAAC0D,CAAC,CAAC,EAAEnC,CAAC,CAAC;EACvCsD,oBAAoB,CAAC,IAAI,CAAC3E,KAAK,CAACqB,CAAC,CAAC,EAAEmC,CAAC,CAAC;EACtC,IAAI,CAAC3D,GAAG,CAAC2D,CAAC,CAAC,CAACpB,CAAC,CAAC,GAAGmC,OAAO;EACxB,IAAI,CAACxE,IAAI,CAACsB,CAAC,CAAC,CAACe,CAAC,CAAC,GAAGmC,OAAO;EACzB,IAAI,CAAClE,UAAU,EAAE;EACjB,OAAO,IAAI;AACb,CAAC;AAED5B,KAAK,CAAC0B,SAAS,CAACuD,IAAI,GAAG,UAASrC,CAAC,EAAEmC,CAAC,EAAEW,IAAI,EAAE;EAC1C,IAAI/B,CAAC,GAAIR,SAAS,CAACE,MAAM,KAAK,CAAC,GAC3B8C,WAAW,CAAC,IAAI,CAAC9F,WAAW,EAAE8C,SAAS,CAAC,CAAC,CAAC,CAAC,GAC3C0C,YAAY,CAAC,IAAI,CAACxF,WAAW,EAAEuC,CAAC,EAAEmC,CAAC,EAAEW,IAAI,CAAE;EAC/C,OAAO,IAAI,CAACjE,WAAW,CAACkC,CAAC,CAAC;AAC5B,CAAC;AAED3D,KAAK,CAAC0B,SAAS,CAAC0E,OAAO,GAAG,UAASxD,CAAC,EAAEmC,CAAC,EAAEW,IAAI,EAAE;EAC7C,IAAI/B,CAAC,GAAIR,SAAS,CAACE,MAAM,KAAK,CAAC,GAC3B8C,WAAW,CAAC,IAAI,CAAC9F,WAAW,EAAE8C,SAAS,CAAC,CAAC,CAAC,CAAC,GAC3C0C,YAAY,CAAC,IAAI,CAACxF,WAAW,EAAEuC,CAAC,EAAEmC,CAAC,EAAEW,IAAI,CAAE;EAC/C,OAAO9F,CAAC,CAACU,GAAG,CAAC,IAAI,CAACmB,WAAW,EAAEkC,CAAC,CAAC;AACnC,CAAC;AAED3D,KAAK,CAAC0B,SAAS,CAACgC,UAAU,GAAG,UAASd,CAAC,EAAEmC,CAAC,EAAEW,IAAI,EAAE;EAChD,IAAI/B,CAAC,GAAIR,SAAS,CAACE,MAAM,KAAK,CAAC,GAC3B8C,WAAW,CAAC,IAAI,CAAC9F,WAAW,EAAE8C,SAAS,CAAC,CAAC,CAAC,CAAC,GAC3C0C,YAAY,CAAC,IAAI,CAACxF,WAAW,EAAEuC,CAAC,EAAEmC,CAAC,EAAEW,IAAI,CAAE;EAC/C,IAAIT,IAAI,GAAG,IAAI,CAACzD,SAAS,CAACmC,CAAC,CAAC;EAC5B,IAAIsB,IAAI,EAAE;IACRrC,CAAC,GAAGqC,IAAI,CAACrC,CAAC;IACVmC,CAAC,GAAGE,IAAI,CAACF,CAAC;IACV,OAAO,IAAI,CAACtD,WAAW,CAACkC,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACnC,SAAS,CAACmC,CAAC,CAAC;IACxB0C,sBAAsB,CAAC,IAAI,CAAChF,MAAM,CAAC0D,CAAC,CAAC,EAAEnC,CAAC,CAAC;IACzCyD,sBAAsB,CAAC,IAAI,CAAC9E,KAAK,CAACqB,CAAC,CAAC,EAAEmC,CAAC,CAAC;IACxC,OAAO,IAAI,CAAC3D,GAAG,CAAC2D,CAAC,CAAC,CAACpB,CAAC,CAAC;IACrB,OAAO,IAAI,CAACrC,IAAI,CAACsB,CAAC,CAAC,CAACe,CAAC,CAAC;IACtB,IAAI,CAAC/B,UAAU,EAAE;EACnB;EACA,OAAO,IAAI;AACb,CAAC;AAED5B,KAAK,CAAC0B,SAAS,CAAC4E,OAAO,GAAG,UAAS1D,CAAC,EAAE2D,CAAC,EAAE;EACvC,IAAIC,GAAG,GAAG,IAAI,CAACpF,GAAG,CAACwB,CAAC,CAAC;EACrB,IAAI4D,GAAG,EAAE;IACP,IAAIlB,KAAK,GAAG1F,CAAC,CAAC2F,MAAM,CAACiB,GAAG,CAAC;IACzB,IAAI,CAACD,CAAC,EAAE;MACN,OAAOjB,KAAK;IACd;IACA,OAAO1F,CAAC,CAAC+C,MAAM,CAAC2C,KAAK,EAAE,UAASL,IAAI,EAAE;MAAE,OAAOA,IAAI,CAACrC,CAAC,KAAK2D,CAAC;IAAE,CAAC,CAAC;EACjE;AACF,CAAC;AAEDvG,KAAK,CAAC0B,SAAS,CAAC+E,QAAQ,GAAG,UAAS7D,CAAC,EAAEmC,CAAC,EAAE;EACxC,IAAI2B,IAAI,GAAG,IAAI,CAACpF,IAAI,CAACsB,CAAC,CAAC;EACvB,IAAI8D,IAAI,EAAE;IACR,IAAIpB,KAAK,GAAG1F,CAAC,CAAC2F,MAAM,CAACmB,IAAI,CAAC;IAC1B,IAAI,CAAC3B,CAAC,EAAE;MACN,OAAOO,KAAK;IACd;IACA,OAAO1F,CAAC,CAAC+C,MAAM,CAAC2C,KAAK,EAAE,UAASL,IAAI,EAAE;MAAE,OAAOA,IAAI,CAACF,CAAC,KAAKA,CAAC;IAAE,CAAC,CAAC;EACjE;AACF,CAAC;AAED/E,KAAK,CAAC0B,SAAS,CAACiF,SAAS,GAAG,UAAS/D,CAAC,EAAEmC,CAAC,EAAE;EACzC,IAAIuB,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC1D,CAAC,EAAEmC,CAAC,CAAC;EAChC,IAAIuB,OAAO,EAAE;IACX,OAAOA,OAAO,CAACM,MAAM,CAAC,IAAI,CAACH,QAAQ,CAAC7D,CAAC,EAAEmC,CAAC,CAAC,CAAC;EAC5C;AACF,CAAC;AAED,SAASmB,oBAAoBA,CAACW,GAAG,EAAEC,CAAC,EAAE;EACpC,IAAID,GAAG,CAACC,CAAC,CAAC,EAAE;IACVD,GAAG,CAACC,CAAC,CAAC,EAAE;EACV,CAAC,MAAM;IACLD,GAAG,CAACC,CAAC,CAAC,GAAG,CAAC;EACZ;AACF;AAEA,SAAST,sBAAsBA,CAACQ,GAAG,EAAEC,CAAC,EAAE;EACtC,IAAI,CAAC,GAAED,GAAG,CAACC,CAAC,CAAC,EAAE;IAAE,OAAOD,GAAG,CAACC,CAAC,CAAC;EAAE;AAClC;AAEA,SAASjB,YAAYA,CAAChE,UAAU,EAAEkF,EAAE,EAAEC,EAAE,EAAEtB,IAAI,EAAE;EAC9C,IAAI9C,CAAC,GAAG,EAAE,GAAGmE,EAAE;EACf,IAAIhC,CAAC,GAAG,EAAE,GAAGiC,EAAE;EACf,IAAI,CAACnF,UAAU,IAAIe,CAAC,GAAGmC,CAAC,EAAE;IACxB,IAAIkC,GAAG,GAAGrE,CAAC;IACXA,CAAC,GAAGmC,CAAC;IACLA,CAAC,GAAGkC,GAAG;EACT;EACA,OAAOrE,CAAC,GAAGzC,cAAc,GAAG4E,CAAC,GAAG5E,cAAc,IAClCP,CAAC,CAACsE,WAAW,CAACwB,IAAI,CAAC,GAAGzF,iBAAiB,GAAGyF,IAAI,CAAC;AAC7D;AAEA,SAASK,aAAaA,CAAClE,UAAU,EAAEkF,EAAE,EAAEC,EAAE,EAAEtB,IAAI,EAAE;EAC/C,IAAI9C,CAAC,GAAG,EAAE,GAAGmE,EAAE;EACf,IAAIhC,CAAC,GAAG,EAAE,GAAGiC,EAAE;EACf,IAAI,CAACnF,UAAU,IAAIe,CAAC,GAAGmC,CAAC,EAAE;IACxB,IAAIkC,GAAG,GAAGrE,CAAC;IACXA,CAAC,GAAGmC,CAAC;IACLA,CAAC,GAAGkC,GAAG;EACT;EACA,IAAInB,OAAO,GAAI;IAAElD,CAAC,EAAEA,CAAC;IAAEmC,CAAC,EAAEA;EAAE,CAAC;EAC7B,IAAIW,IAAI,EAAE;IACRI,OAAO,CAACJ,IAAI,GAAGA,IAAI;EACrB;EACA,OAAOI,OAAO;AAChB;AAEA,SAASK,WAAWA,CAACtE,UAAU,EAAEiE,OAAO,EAAE;EACxC,OAAOD,YAAY,CAAChE,UAAU,EAAEiE,OAAO,CAAClD,CAAC,EAAEkD,OAAO,CAACf,CAAC,EAAEe,OAAO,CAACJ,IAAI,CAAC;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}