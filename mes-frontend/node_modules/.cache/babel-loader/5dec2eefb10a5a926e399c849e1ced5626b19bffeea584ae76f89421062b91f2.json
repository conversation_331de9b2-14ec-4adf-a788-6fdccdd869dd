{"ast": null, "code": "var __awaiter = this && this.__awaiter || function (thisArg, _arguments, P, generator) {\n  function adopt(value) {\n    return value instanceof P ? value : new P(function (resolve) {\n      resolve(value);\n    });\n  }\n  return new (P || (P = Promise))(function (resolve, reject) {\n    function fulfilled(value) {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function rejected(value) {\n      try {\n        step(generator[\"throw\"](value));\n      } catch (e) {\n        reject(e);\n      }\n    }\n    function step(result) {\n      result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);\n    }\n    step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n};\nimport { get, deepMix, pick, keys } from \"@antv/util\";\nimport { select, PLOT_CLASS_NAME } from \"@antv/g2\";\nimport { CHILD_NODE_COUNT } from \"../utils/hierarchy/partition\";\nimport { SUNBURST_TYPE, SUNBURST_TYPE_FIELD, SUNBURST_ANCESTOR_FIELD } from \"../mark/sunburst\";\n// Get sunburst element.\nconst getElementsSunburst = plot => {\n  return plot.querySelectorAll(\".element\").filter(item => get(item, [\"style\", SUNBURST_TYPE_FIELD]) === SUNBURST_TYPE);\n};\nfunction selectPlotArea(root) {\n  return select(root).select(`.${PLOT_CLASS_NAME}`).node();\n}\n// Default breadCrumb config.\nconst DEFAULT_BREADCRUMB = {\n  rootText: \"root\",\n  style: {\n    fill: \"rgba(0, 0, 0, 0.85)\",\n    fontSize: 12,\n    y: 1\n  },\n  active: {\n    fill: \"rgba(0, 0, 0, 0.5)\"\n  }\n};\n/**\n * @todo DrillDown interaction\n */\nexport function DrillDown(drillDownOptions = {}) {\n  const {\n    breadCrumb: textConfig = {},\n    isFixedColor = false\n  } = drillDownOptions;\n  const breadCrumb = deepMix({}, DEFAULT_BREADCRUMB, textConfig);\n  return context => {\n    const {\n      update,\n      setState,\n      container,\n      view,\n      options\n    } = context;\n    const document = container.ownerDocument;\n    const plotArea = selectPlotArea(container);\n    const sunburstMark = options.marks.find(({\n      id\n    }) => id === SUNBURST_TYPE);\n    const {\n      state\n    } = sunburstMark;\n    // Create breadCrumbTextsGroup,save textSeparator、drillTexts.\n    const textGroup = document.createElement(\"g\");\n    plotArea.appendChild(textGroup);\n    // Modify the data and scale according to the path and the level of the current click, so as to achieve the effect of drilling down and drilling up and initialization.\n    const drillDownClick = (path, depth) => __awaiter(this, void 0, void 0, function* () {\n      // Clear text.\n      textGroup.removeChildren();\n      // More path creation text.\n      if (path) {\n        // Create root text.\n        const rootText = document.createElement(\"text\", {\n          style: Object.assign({\n            x: 0,\n            text: breadCrumb.rootText,\n            // @ts-ignore\n            depth: 0\n          }, breadCrumb.style)\n        });\n        textGroup.appendChild(rootText);\n        let name = \"\";\n        const pathArray = path === null || path === void 0 ? void 0 : path.split(\" / \");\n        let y = breadCrumb.style.y;\n        let x = textGroup.getBBox().width;\n        const maxWidth = plotArea.getBBox().width;\n        // Create path: 'type1 / type2 / type3' -> '/ type1 / type2 / type3'.\n        const drillTexts = pathArray.map((text, index) => {\n          const textSeparator = document.createElement(\"text\", {\n            style: Object.assign(Object.assign({\n              x,\n              text: \" / \"\n            }, breadCrumb.style), {\n              y\n            })\n          });\n          textGroup.appendChild(textSeparator);\n          x += textSeparator.getBBox().width;\n          name = `${name}${text} / `;\n          const drillText = document.createElement(\"text\", {\n            name: name.replace(/\\s\\/\\s$/, \"\"),\n            style: Object.assign(Object.assign({\n              text,\n              x,\n              // @ts-ignore\n              depth: index + 1\n            }, breadCrumb.style), {\n              y\n            })\n          });\n          textGroup.appendChild(drillText);\n          x += drillText.getBBox().width;\n          /**\n           * Page width exceeds maximum, line feed.\n           * | ----maxWidth---- |\n           * | / tyep1 / tyep2 / type3 |\n           * ->\n           * | ----maxWidth---- |\n           * | / tyep1 / tyep2  |\n           * | / type3 |\n           */\n          if (x > maxWidth) {\n            y = textGroup.getBBox().height;\n            x = 0;\n            textSeparator.attr({\n              x,\n              y\n            });\n            x += textSeparator.getBBox().width;\n            drillText.attr({\n              x,\n              y\n            });\n            x += drillText.getBBox().width;\n          }\n          return drillText;\n        });\n        // Add Active, Add DrillDown\n        [rootText, ...drillTexts].forEach((item, index) => {\n          // Last drillText\n          if (index === drillTexts.length) return;\n          const originalAttrs = Object.assign({}, item.attributes);\n          item.attr(\"cursor\", \"pointer\");\n          item.addEventListener(\"mouseenter\", () => {\n            item.attr(breadCrumb.active);\n          });\n          item.addEventListener(\"mouseleave\", () => {\n            item.attr(originalAttrs);\n          });\n          item.addEventListener(\"click\", () => {\n            drillDownClick(item.name, get(item, [\"style\", \"depth\"]));\n          });\n        });\n      }\n      // Update marks.\n      setState(\"drillDown\", viewOptions => {\n        const {\n          marks\n        } = viewOptions;\n        // Add filter transform for every marks,\n        // which will skip for mark without color channel.\n        const newMarks = marks.map(mark => {\n          if (mark.id !== SUNBURST_TYPE && mark.type !== \"rect\") return mark;\n          // Inset after aggregate transform, such as group, and bin.\n          const {\n            data\n          } = mark;\n          const newScale = Object.fromEntries([\"color\"].map(channel => [channel, {\n            domain: view.scale[channel].getOptions().domain\n          }]));\n          const newData = data.filter(item => {\n            const key = item.path;\n            // isFixedColor true change drillDown color.\n            if (!isFixedColor) {\n              item[SUNBURST_ANCESTOR_FIELD] = key.split(\" / \")[depth];\n            }\n            if (!path) return true;\n            const reg = new RegExp(`^${path}.+`);\n            return reg.test(key);\n          });\n          // DrillDown by filtering the data and scale.\n          return deepMix({}, mark, isFixedColor ? {\n            data: newData,\n            scale: newScale\n          } : {\n            data: newData\n          });\n        });\n        return Object.assign(Object.assign({}, viewOptions), {\n          marks: newMarks\n        });\n      });\n      yield update();\n    });\n    const createDrillClick = e => {\n      const item = e.target;\n      // Element need style.markType === 'sunburst', markType === 'rect', have children.\n      if (get(item, [\"style\", SUNBURST_TYPE_FIELD]) !== SUNBURST_TYPE || get(item, [\"markType\"]) !== \"rect\" || !get(item, [\"style\", CHILD_NODE_COUNT])) return;\n      const path = get(item, [\"__data__\", \"key\"]);\n      const depth = get(item, [\"style\", \"depth\"]);\n      item.style.cursor = \"pointer\";\n      drillDownClick(path, depth);\n    };\n    // Add click drill interaction.\n    plotArea.addEventListener(\"click\", createDrillClick);\n    // Change attributes keys.\n    const changeStyleKey = keys(Object.assign(Object.assign({}, state.active), state.inactive));\n    const createActive = () => {\n      const elements = getElementsSunburst(plotArea);\n      elements.forEach(element => {\n        const childNodeCount = get(element, [\"style\", CHILD_NODE_COUNT]);\n        const cursor = get(element, [\"style\", \"cursor\"]);\n        if (cursor !== \"pointer\" && childNodeCount) {\n          element.style.cursor = \"pointer\";\n          const originalAttrs = pick(element.attributes, changeStyleKey);\n          element.addEventListener(\"mouseenter\", () => {\n            element.attr(state.active);\n          });\n          element.addEventListener(\"mouseleave\", () => {\n            element.attr(deepMix(originalAttrs, state.inactive));\n          });\n        }\n      });\n    };\n    // Animate elements update, Add active.\n    plotArea.addEventListener(\"mousemove\", createActive);\n    return () => {\n      textGroup.remove();\n      plotArea.removeEventListener(\"click\", createDrillClick);\n      plotArea.removeEventListener(\"mousemove\", createActive);\n    };\n  };\n}", "map": {"version": 3, "names": ["get", "deepMix", "pick", "keys", "select", "PLOT_CLASS_NAME", "CHILD_NODE_COUNT", "SUNBURST_TYPE", "SUNBURST_TYPE_FIELD", "SUNBURST_ANCESTOR_FIELD", "getElementsSunburst", "plot", "querySelectorAll", "filter", "item", "selectPlotArea", "root", "node", "DEFAULT_BREADCRUMB", "rootText", "style", "fill", "fontSize", "y", "active", "DrillDown", "drillDownOptions", "breadCrumb", "textConfig", "isFixedColor", "context", "update", "setState", "container", "view", "options", "document", "ownerDocument", "<PERSON><PERSON><PERSON>", "sunburstMark", "marks", "find", "id", "state", "textGroup", "createElement", "append<PERSON><PERSON><PERSON>", "drillDownClick", "path", "depth", "__awaiter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "x", "text", "name", "pathArray", "split", "getBBox", "width", "max<PERSON><PERSON><PERSON>", "drillTexts", "map", "index", "textSeparator", "drillText", "replace", "height", "attr", "for<PERSON>ach", "length", "originalAttrs", "attributes", "addEventListener", "viewOptions", "newMarks", "mark", "type", "data", "newScale", "fromEntries", "channel", "domain", "scale", "getOptions", "newData", "key", "reg", "RegExp", "test", "createDrillClick", "e", "target", "cursor", "changeStyleKey", "inactive", "createActive", "elements", "element", "childNodeCount", "remove", "removeEventListener"], "sources": ["interaction/drillDown.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,SAASA,GAAG,EAAEC,OAAO,EAAEC,IAAI,EAAEC,IAAI,QAAQ,YAAY;AACrD,SAASC,MAAM,EAAEC,eAAe,QAAQ,UAAU;AAClD,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,aAAa,EAAEC,mBAAmB,EAAEC,uBAAuB,QAAQ,kBAAkB;AAE9F;AACA,MAAMC,mBAAmB,GAAIC,IAAI,IAAI;EACnC,OAAOA,IAAI,CACRC,gBAAgB,CAAC,UAAU,CAAC,CAC5BC,MAAM,CAAEC,IAAI,IAAKd,GAAG,CAACc,IAAI,EAAE,CAAC,OAAO,EAAEN,mBAAmB,CAAC,CAAC,KAAKD,aAAa,CAAC;AAClF,CAAC;AAED,SAASQ,cAAcA,CAACC,IAAmB;EACzC,OAAOZ,MAAM,CAACY,IAAI,CAAC,CAACZ,MAAM,CAAC,IAAIC,eAAe,EAAE,CAAC,CAACY,IAAI,EAAE;AAC1D;AAYA;AACA,MAAMC,kBAAkB,GAAG;EACzBC,QAAQ,EAAE,MAAM;EAChBC,KAAK,EAAE;IACLC,IAAI,EAAE,qBAAqB;IAC3BC,QAAQ,EAAE,EAAE;IACZC,CAAC,EAAE;GACJ;EACDC,MAAM,EAAE;IACNH,IAAI,EAAE;;CAET;AAED;;;AAGA,OAAM,SAAUI,SAASA,CAACC,gBAAA,GAAqC,EAAE;EAC/D,MAAM;IAAEC,UAAU,EAAEC,UAAU,GAAG,EAAE;IAAEC,YAAY,GAAG;EAAK,CAAE,GAAGH,gBAAgB;EAC9E,MAAMC,UAAU,GAAG1B,OAAO,CAAC,EAAE,EAAEiB,kBAAkB,EAAEU,UAAU,CAAC;EAE9D,OAAQE,OAAO,IAAI;IACjB,MAAM;MAAEC,MAAM;MAAEC,QAAQ;MAAEC,SAAS;MAAEC,IAAI;MAAEC;IAAO,CAAE,GAAGL,OAAO;IAE9D,MAAMM,QAAQ,GAAGH,SAAS,CAACI,aAAa;IAExC,MAAMC,QAAQ,GAAGvB,cAAc,CAACkB,SAAS,CAAC;IAE1C,MAAMM,YAAY,GAAGJ,OAAO,CAACK,KAAK,CAACC,IAAI,CAAC,CAAC;MAAEC;IAAE,CAAE,KAAKA,EAAE,KAAKnC,aAAa,CAAC;IACzE,MAAM;MAAEoC;IAAK,CAAE,GAAGJ,YAAY;IAE9B;IACA,MAAMK,SAAS,GAAGR,QAAQ,CAACS,aAAa,CAAC,GAAG,CAAC;IAC7CP,QAAQ,CAACQ,WAAW,CAACF,SAAS,CAAC;IAE/B;IACA,MAAMG,cAAc,GAAGA,CAAOC,IAAY,EAAEC,KAAc,KAAIC,SAAA;MAC5D;MACAN,SAAS,CAACO,cAAc,EAAE;MAE1B;MACA,IAAIH,IAAI,EAAE;QACR;QACA,MAAM7B,QAAQ,GAAGiB,QAAQ,CAACS,aAAa,CAAC,MAAM,EAAE;UAC9CzB,KAAK,EAAAgC,MAAA,CAAAC,MAAA;YACHC,CAAC,EAAE,CAAC;YACJC,IAAI,EAAE5B,UAAU,CAACR,QAAQ;YACzB;YACA8B,KAAK,EAAE;UAAC,GACLtB,UAAU,CAACP,KAAK;SAEtB,CAAC;QAEFwB,SAAS,CAACE,WAAW,CAAC3B,QAAQ,CAAC;QAE/B,IAAIqC,IAAI,GAAG,EAAE;QACb,MAAMC,SAAS,GAAGT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU,KAAK,CAAC,KAAK,CAAC;QACpC,IAAInC,CAAC,GAAGI,UAAU,CAACP,KAAK,CAACG,CAAC;QAC1B,IAAI+B,CAAC,GAAGV,SAAS,CAACe,OAAO,EAAE,CAACC,KAAK;QAEjC,MAAMC,QAAQ,GAAGvB,QAAQ,CAACqB,OAAO,EAAE,CAACC,KAAK;QAEzC;QACA,MAAME,UAAU,GAAGL,SAAS,CAACM,GAAG,CAAC,CAACR,IAAI,EAAES,KAAK,KAAI;UAC/C,MAAMC,aAAa,GAAG7B,QAAQ,CAACS,aAAa,CAAC,MAAM,EAAE;YACnDzB,KAAK,EAAAgC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA;cACHC,CAAC;cACDC,IAAI,EAAE;YAAK,GACR5B,UAAU,CAACP,KAAK;cACnBG;YAAC;WAEJ,CAAC;UAEFqB,SAAS,CAACE,WAAW,CAACmB,aAAa,CAAC;UAEpCX,CAAC,IAAIW,aAAa,CAACN,OAAO,EAAE,CAACC,KAAK;UAElCJ,IAAI,GAAG,GAAGA,IAAI,GAAGD,IAAI,KAAK;UAE1B,MAAMW,SAAS,GAAG9B,QAAQ,CAACS,aAAa,CAAC,MAAM,EAAE;YAC/CW,IAAI,EAAEA,IAAI,CAACW,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;YACjC/C,KAAK,EAAAgC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA;cACHE,IAAI;cACJD,CAAC;cACD;cACAL,KAAK,EAAEe,KAAK,GAAG;YAAC,GACbrC,UAAU,CAACP,KAAK;cACnBG;YAAC;WAEJ,CAAC;UAEFqB,SAAS,CAACE,WAAW,CAACoB,SAAS,CAAC;UAEhCZ,CAAC,IAAIY,SAAS,CAACP,OAAO,EAAE,CAACC,KAAK;UAE9B;;;;;;;;;UASA,IAAIN,CAAC,GAAGO,QAAQ,EAAE;YAChBtC,CAAC,GAAGqB,SAAS,CAACe,OAAO,EAAE,CAACS,MAAM;YAC9Bd,CAAC,GAAG,CAAC;YACLW,aAAa,CAACI,IAAI,CAAC;cACjBf,CAAC;cACD/B;aACD,CAAC;YACF+B,CAAC,IAAIW,aAAa,CAACN,OAAO,EAAE,CAACC,KAAK;YAClCM,SAAS,CAACG,IAAI,CAAC;cACbf,CAAC;cACD/B;aACD,CAAC;YACF+B,CAAC,IAAIY,SAAS,CAACP,OAAO,EAAE,CAACC,KAAK;UAChC;UAEA,OAAOM,SAAS;QAClB,CAAC,CAAC;QAEF;QACA,CAAC/C,QAAQ,EAAE,GAAG2C,UAAU,CAAC,CAACQ,OAAO,CAAC,CAACxD,IAAI,EAAEkD,KAAK,KAAI;UAChD;UACA,IAAIA,KAAK,KAAKF,UAAU,CAACS,MAAM,EAAE;UACjC,MAAMC,aAAa,GAAApB,MAAA,CAAAC,MAAA,KAAQvC,IAAI,CAAC2D,UAAU,CAAE;UAC5C3D,IAAI,CAACuD,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC;UAC9BvD,IAAI,CAAC4D,gBAAgB,CAAC,YAAY,EAAE,MAAK;YACvC5D,IAAI,CAACuD,IAAI,CAAC1C,UAAU,CAACH,MAAM,CAAC;UAC9B,CAAC,CAAC;UACFV,IAAI,CAAC4D,gBAAgB,CAAC,YAAY,EAAE,MAAK;YACvC5D,IAAI,CAACuD,IAAI,CAACG,aAAa,CAAC;UAC1B,CAAC,CAAC;UACF1D,IAAI,CAAC4D,gBAAgB,CAAC,OAAO,EAAE,MAAK;YAClC3B,cAAc,CAACjC,IAAI,CAAC0C,IAAI,EAAExD,GAAG,CAACc,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;UAC1D,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;MAEA;MACAkB,QAAQ,CAAC,WAAW,EAAG2C,WAAW,IAAI;QACpC,MAAM;UAAEnC;QAAK,CAAE,GAAGmC,WAAW;QAC7B;QACA;QACA,MAAMC,QAAQ,GAAGpC,KAAK,CAACuB,GAAG,CAAEc,IAAI,IAAI;UAClC,IAAIA,IAAI,CAACnC,EAAE,KAAKnC,aAAa,IAAIsE,IAAI,CAACC,IAAI,KAAK,MAAM,EAAE,OAAOD,IAAI;UAElE;UACA,MAAM;YAAEE;UAAI,CAAE,GAAGF,IAAI;UAErB,MAAMG,QAAQ,GAAG5B,MAAM,CAAC6B,WAAW,CACjC,CAAC,OAAO,CAAC,CAAClB,GAAG,CAAEmB,OAAO,IAAK,CAACA,OAAO,EAAE;YAAEC,MAAM,EAAEjD,IAAI,CAACkD,KAAK,CAACF,OAAO,CAAC,CAACG,UAAU,EAAE,CAACF;UAAM,CAAE,CAAC,CAAC,CAC3F;UAED,MAAMG,OAAO,GAAGP,IAAI,CAAClE,MAAM,CAAEC,IAAI,IAAI;YACnC,MAAMyE,GAAG,GAAGzE,IAAI,CAACkC,IAAI;YAErB;YACA,IAAI,CAACnB,YAAY,EAAE;cACjBf,IAAI,CAACL,uBAAuB,CAAC,GAAG8E,GAAG,CAAC7B,KAAK,CAAC,KAAK,CAAC,CAACT,KAAK,CAAC;YACzD;YAEA,IAAI,CAACD,IAAI,EAAE,OAAO,IAAI;YACtB,MAAMwC,GAAG,GAAG,IAAIC,MAAM,CAAC,IAAIzC,IAAI,IAAI,CAAC;YACpC,OAAOwC,GAAG,CAACE,IAAI,CAACH,GAAG,CAAC;UACtB,CAAC,CAAC;UAEF;UACA,OAAOtF,OAAO,CACZ,EAAE,EACF4E,IAAI,EACJhD,YAAY,GACR;YACEkD,IAAI,EAAEO,OAAO;YACbF,KAAK,EAAEJ;WACR,GACD;YACED,IAAI,EAAEO;WACP,CACN;QACH,CAAC,CAAC;QACF,OAAAlC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAYsB,WAAW;UAAEnC,KAAK,EAAEoC;QAAQ;MAC1C,CAAC,CAAC;MAEF,MAAM7C,MAAM,EAAE;IAChB,CAAC;IAED,MAAM4D,gBAAgB,GAAIC,CAAC,IAAI;MAC7B,MAAM9E,IAAI,GAAG8E,CAAC,CAACC,MAAM;MAErB;MACA,IACE7F,GAAG,CAACc,IAAI,EAAE,CAAC,OAAO,EAAEN,mBAAmB,CAAC,CAAC,KAAKD,aAAa,IAC3DP,GAAG,CAACc,IAAI,EAAE,CAAC,UAAU,CAAC,CAAC,KAAK,MAAM,IAClC,CAACd,GAAG,CAACc,IAAI,EAAE,CAAC,OAAO,EAAER,gBAAgB,CAAC,CAAC,EAEvC;MAEF,MAAM0C,IAAI,GAAGhD,GAAG,CAACc,IAAI,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;MAC3C,MAAMmC,KAAK,GAAGjD,GAAG,CAACc,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;MAC3CA,IAAI,CAACM,KAAK,CAAC0E,MAAM,GAAG,SAAS;MAC7B/C,cAAc,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC7B,CAAC;IAED;IACAX,QAAQ,CAACoC,gBAAgB,CAAC,OAAO,EAAEiB,gBAAgB,CAAC;IAEpD;IACA,MAAMI,cAAc,GAAG5F,IAAI,CAAAiD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAMV,KAAK,CAACnB,MAAM,GAAKmB,KAAK,CAACqD,QAAQ,EAAG;IAEnE,MAAMC,YAAY,GAAGA,CAAA,KAAK;MACxB,MAAMC,QAAQ,GAAGxF,mBAAmB,CAAC4B,QAAQ,CAAC;MAC9C4D,QAAQ,CAAC5B,OAAO,CAAE6B,OAAO,IAAI;QAC3B,MAAMC,cAAc,GAAGpG,GAAG,CAACmG,OAAO,EAAE,CAAC,OAAO,EAAE7F,gBAAgB,CAAC,CAAC;QAChE,MAAMwF,MAAM,GAAG9F,GAAG,CAACmG,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAChD,IAAIL,MAAM,KAAK,SAAS,IAAIM,cAAc,EAAE;UAC1CD,OAAO,CAAC/E,KAAK,CAAC0E,MAAM,GAAG,SAAS;UAChC,MAAMtB,aAAa,GAAGtE,IAAI,CAACiG,OAAO,CAAC1B,UAAU,EAAEsB,cAAc,CAAC;UAE9DI,OAAO,CAACzB,gBAAgB,CAAC,YAAY,EAAE,MAAK;YAC1CyB,OAAO,CAAC9B,IAAI,CAAC1B,KAAK,CAACnB,MAAM,CAAC;UAC5B,CAAC,CAAC;UAEF2E,OAAO,CAACzB,gBAAgB,CAAC,YAAY,EAAE,MAAK;YAC1CyB,OAAO,CAAC9B,IAAI,CAACpE,OAAO,CAACuE,aAAa,EAAE7B,KAAK,CAACqD,QAAQ,CAAC,CAAC;UACtD,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAED;IACA1D,QAAQ,CAACoC,gBAAgB,CAAC,WAAW,EAAEuB,YAAY,CAAC;IAEpD,OAAO,MAAK;MACVrD,SAAS,CAACyD,MAAM,EAAE;MAClB/D,QAAQ,CAACgE,mBAAmB,CAAC,OAAO,EAAEX,gBAAgB,CAAC;MACvDrD,QAAQ,CAACgE,mBAAmB,CAAC,WAAW,EAAEL,YAAY,CAAC;IACzD,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}