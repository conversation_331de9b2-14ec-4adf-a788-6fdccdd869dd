{"ast": null, "code": "var _jsxFileName = \"/root/mes-system/mes-frontend/src/pages/Project/ProjectDetail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Typography, Card, Row, Col, Descriptions, Tag, Button, Space, Tabs, Table, Progress, Statistic, Timeline, Avatar, List } from 'antd';\nimport { ArrowLeftOutlined, EditOutlined, FileTextOutlined, ScheduleOutlined, TeamOutlined, CalendarOutlined } from '@ant-design/icons';\nimport { projectService, workOrderService } from '../../services/business';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst ProjectDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [project, setProject] = useState(null);\n  const [workOrders, setWorkOrders] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // 模拟项目详情数据\n  const mockProject = {\n    id: 1,\n    project_name: '汽车零部件生产项目',\n    customer_name: '某汽车制造公司',\n    description: '生产汽车发动机相关零部件，包括活塞、连杆、曲轴等关键组件。项目要求高精度加工，严格的质量控制标准。',\n    status: 'IN_PROGRESS',\n    start_date: '2024-01-01',\n    end_date: '2024-06-30',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z'\n  };\n\n  // 模拟工单数据\n  const mockWorkOrders = [{\n    id: 1,\n    project_id: 1,\n    part_id: 1,\n    quantity: 100,\n    status: 'IN_PROGRESS',\n    priority: 'HIGH',\n    planned_start: '2024-01-01',\n    planned_end: '2024-01-15',\n    actual_start: '2024-01-01',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n    part_number: 'PART-001',\n    part_name: '活塞组件'\n  }, {\n    id: 2,\n    project_id: 1,\n    part_id: 2,\n    quantity: 50,\n    status: 'PLANNED',\n    priority: 'MEDIUM',\n    planned_start: '2024-01-16',\n    planned_end: '2024-02-01',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n    part_number: 'PART-002',\n    part_name: '连杆组件'\n  }];\n  useEffect(() => {\n    if (id) {\n      fetchProjectDetail();\n      fetchWorkOrders();\n    }\n  }, [id]);\n  const fetchProjectDetail = async () => {\n    setLoading(true);\n    try {\n      const response = await projectService.getProject(Number(id));\n      if (response.success && response.data) {\n        setProject(response.data);\n      } else {\n        throw new Error('API调用失败');\n      }\n    } catch (error) {\n      console.log('使用模拟数据:', error);\n      setProject(mockProject);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchWorkOrders = async () => {\n    try {\n      const response = await workOrderService.getWorkOrders({\n        project_id: Number(id)\n      });\n      if (response.success && response.data) {\n        setWorkOrders(response.data.data);\n      } else {\n        throw new Error('API调用失败');\n      }\n    } catch (error) {\n      console.log('使用模拟数据:', error);\n      setWorkOrders(mockWorkOrders);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'PLANNING':\n        return 'blue';\n      case 'IN_PROGRESS':\n        return 'green';\n      case 'COMPLETED':\n        return 'default';\n      case 'CANCELLED':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'PLANNING':\n        return '计划中';\n      case 'IN_PROGRESS':\n        return '进行中';\n      case 'COMPLETED':\n        return '已完成';\n      case 'CANCELLED':\n        return '已取消';\n      default:\n        return '未知';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'URGENT':\n        return 'red';\n      case 'HIGH':\n        return 'orange';\n      case 'MEDIUM':\n        return 'blue';\n      case 'LOW':\n        return 'default';\n      default:\n        return 'default';\n    }\n  };\n  const workOrderColumns = [{\n    title: '工单编号',\n    dataIndex: 'id',\n    key: 'id',\n    render: id => `WO-${id.toString().padStart(4, '0')}`\n  }, {\n    title: '零件信息',\n    key: 'part',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          strong: true,\n          children: record.part_number\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 16\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: record.part_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 16\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '数量',\n    dataIndex: 'quantity',\n    key: 'quantity'\n  }, {\n    title: '优先级',\n    dataIndex: 'priority',\n    key: 'priority',\n    render: priority => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getPriorityColor(priority),\n      children: priority\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: getStatusText(status)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '计划开始',\n    dataIndex: 'planned_start',\n    key: 'planned_start',\n    render: date => date ? dayjs(date).format('YYYY-MM-DD') : '-'\n  }, {\n    title: '计划结束',\n    dataIndex: 'planned_end',\n    key: 'planned_end',\n    render: date => date ? dayjs(date).format('YYYY-MM-DD') : '-'\n  }];\n  if (!project) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 12\n    }, this);\n  }\n\n  // 计算项目进度\n  const totalWorkOrders = workOrders.length;\n  const completedWorkOrders = workOrders.filter(wo => wo.status === 'COMPLETED').length;\n  const progress = totalWorkOrders > 0 ? Math.round(completedWorkOrders / totalWorkOrders * 100) : 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/projects'),\n          children: \"\\u8FD4\\u56DE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            margin: 0\n          },\n          children: project.project_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: getStatusColor(project.status),\n          children: getStatusText(project.status)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 17\n        }, this),\n        children: \"\\u7F16\\u8F91\\u9879\\u76EE\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9879\\u76EE\\u8FDB\\u5EA6\",\n            value: progress,\n            suffix: \"%\",\n            prefix: /*#__PURE__*/_jsxDEV(ScheduleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: progress,\n            size: \"small\",\n            style: {\n              marginTop: '8px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DE5\\u5355\\u603B\\u6570\",\n            value: totalWorkOrders,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\\u5DE5\\u5355\",\n            value: completedWorkOrders,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u9879\\u76EE\\u5929\\u6570\",\n            value: project.start_date && project.end_date ? dayjs(project.end_date).diff(dayjs(project.start_date), 'day') : 0,\n            prefix: /*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        marginBottom: '24px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Descriptions, {\n        title: \"\\u9879\\u76EE\\u4FE1\\u606F\",\n        bordered: true,\n        column: 2,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n          children: project.project_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BA2\\u6237\\u540D\\u79F0\",\n          children: project.customer_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: getStatusColor(project.status),\n            children: getStatusText(project.status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 321,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: dayjs(project.created_at).format('YYYY-MM-DD HH:mm')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5F00\\u59CB\\u65E5\\u671F\",\n          children: project.start_date ? dayjs(project.start_date).format('YYYY-MM-DD') : '-'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u7ED3\\u675F\\u65E5\\u671F\",\n          children: project.end_date ? dayjs(project.end_date).format('YYYY-MM-DD') : '-'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u63CF\\u8FF0\",\n          span: 2,\n          children: project.description || '暂无描述'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"workorders\",\n        items: [{\n          key: 'workorders',\n          label: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 19\n            }, this), \"\\u5DE5\\u5355\\u5217\\u8868\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            columns: workOrderColumns,\n            dataSource: workOrders,\n            rowKey: \"id\",\n            pagination: false\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 17\n          }, this)\n        }, {\n          key: 'timeline',\n          label: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(ScheduleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 19\n            }, this), \"\\u9879\\u76EE\\u65F6\\u95F4\\u7EBF\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Timeline, {\n            items: [{\n              children: '项目创建',\n              color: 'blue'\n            }, {\n              children: '项目启动',\n              color: 'green'\n            }, {\n              children: '第一批工单完成',\n              color: 'green'\n            }, {\n              children: '项目进行中...',\n              color: 'blue'\n            }]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 17\n          }, this)\n        }, {\n          key: 'team',\n          label: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this), \"\\u9879\\u76EE\\u56E2\\u961F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(List, {\n            itemLayout: \"horizontal\",\n            dataSource: [{\n              name: '张工程师',\n              role: '项目经理',\n              avatar: 'Z'\n            }, {\n              name: '李师傅',\n              role: '生产主管',\n              avatar: 'L'\n            }, {\n              name: '王技师',\n              role: '质量检验',\n              avatar: 'W'\n            }],\n            renderItem: item => /*#__PURE__*/_jsxDEV(List.Item, {\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                  children: item.avatar\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 413,\n                  columnNumber: 33\n                }, this),\n                title: item.name,\n                description: item.role\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 411,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 17\n          }, this)\n        }]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectDetail, \"84eCdUAhWEQGAY5ziQxI2B3LF3Y=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ProjectDetail;\nexport default ProjectDetail;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "Typography", "Card", "Row", "Col", "Descriptions", "Tag", "<PERSON><PERSON>", "Space", "Tabs", "Table", "Progress", "Statistic", "Timeline", "Avatar", "List", "ArrowLeftOutlined", "EditOutlined", "FileTextOutlined", "ScheduleOutlined", "TeamOutlined", "CalendarOutlined", "projectService", "workOrderService", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Text", "ProjectDetail", "_s", "id", "navigate", "project", "setProject", "workOrders", "setWorkOrders", "loading", "setLoading", "mockProject", "project_name", "customer_name", "description", "status", "start_date", "end_date", "created_at", "updated_at", "mockWorkOrders", "project_id", "part_id", "quantity", "priority", "planned_start", "planned_end", "actual_start", "part_number", "part_name", "fetchProjectDetail", "fetchWorkOrders", "response", "getProject", "Number", "success", "data", "Error", "error", "console", "log", "getWorkOrders", "getStatusColor", "getStatusText", "getPriorityColor", "workOrderColumns", "title", "dataIndex", "key", "render", "toString", "padStart", "_", "record", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "color", "date", "format", "totalWorkOrders", "length", "completedWorkOrders", "filter", "wo", "progress", "Math", "round", "style", "display", "justifyContent", "alignItems", "marginBottom", "icon", "onClick", "level", "margin", "gutter", "span", "value", "suffix", "prefix", "percent", "size", "marginTop", "valueStyle", "diff", "bordered", "column", "<PERSON><PERSON>", "label", "defaultActiveKey", "items", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "itemLayout", "name", "role", "avatar", "renderItem", "item", "Meta", "_c", "$RefreshReg$"], "sources": ["/root/mes-system/mes-frontend/src/pages/Project/ProjectDetail.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Typography,\n  Card,\n  Row,\n  Col,\n  Descriptions,\n  Tag,\n  Button,\n  Space,\n  Tabs,\n  Table,\n  Progress,\n  Statistic,\n  Timeline,\n  Avatar,\n  List,\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  EditOutlined,\n  FileTextOutlined,\n  ScheduleOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n} from '@ant-design/icons';\nimport { projectService, workOrderService } from '../../services/business';\nimport { Project, WorkOrder } from '../../types';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\n\nconst ProjectDetail: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [project, setProject] = useState<Project | null>(null);\n  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);\n  const [loading, setLoading] = useState(false);\n\n  // 模拟项目详情数据\n  const mockProject: Project = {\n    id: 1,\n    project_name: '汽车零部件生产项目',\n    customer_name: '某汽车制造公司',\n    description: '生产汽车发动机相关零部件，包括活塞、连杆、曲轴等关键组件。项目要求高精度加工，严格的质量控制标准。',\n    status: 'IN_PROGRESS',\n    start_date: '2024-01-01',\n    end_date: '2024-06-30',\n    created_at: '2024-01-01T00:00:00Z',\n    updated_at: '2024-01-01T00:00:00Z',\n  };\n\n  // 模拟工单数据\n  const mockWorkOrders: WorkOrder[] = [\n    {\n      id: 1,\n      project_id: 1,\n      part_id: 1,\n      quantity: 100,\n      status: 'IN_PROGRESS',\n      priority: 'HIGH',\n      planned_start: '2024-01-01',\n      planned_end: '2024-01-15',\n      actual_start: '2024-01-01',\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-01T00:00:00Z',\n      part_number: 'PART-001',\n      part_name: '活塞组件',\n    },\n    {\n      id: 2,\n      project_id: 1,\n      part_id: 2,\n      quantity: 50,\n      status: 'PLANNED',\n      priority: 'MEDIUM',\n      planned_start: '2024-01-16',\n      planned_end: '2024-02-01',\n      created_at: '2024-01-01T00:00:00Z',\n      updated_at: '2024-01-01T00:00:00Z',\n      part_number: 'PART-002',\n      part_name: '连杆组件',\n    },\n  ];\n\n  useEffect(() => {\n    if (id) {\n      fetchProjectDetail();\n      fetchWorkOrders();\n    }\n  }, [id]);\n\n  const fetchProjectDetail = async () => {\n    setLoading(true);\n    try {\n      const response = await projectService.getProject(Number(id));\n      if (response.success && response.data) {\n        setProject(response.data);\n      } else {\n        throw new Error('API调用失败');\n      }\n    } catch (error) {\n      console.log('使用模拟数据:', error);\n      setProject(mockProject);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchWorkOrders = async () => {\n    try {\n      const response = await workOrderService.getWorkOrders({ project_id: Number(id) });\n      if (response.success && response.data) {\n        setWorkOrders(response.data.data);\n      } else {\n        throw new Error('API调用失败');\n      }\n    } catch (error) {\n      console.log('使用模拟数据:', error);\n      setWorkOrders(mockWorkOrders);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'PLANNING':\n        return 'blue';\n      case 'IN_PROGRESS':\n        return 'green';\n      case 'COMPLETED':\n        return 'default';\n      case 'CANCELLED':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'PLANNING':\n        return '计划中';\n      case 'IN_PROGRESS':\n        return '进行中';\n      case 'COMPLETED':\n        return '已完成';\n      case 'CANCELLED':\n        return '已取消';\n      default:\n        return '未知';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'URGENT':\n        return 'red';\n      case 'HIGH':\n        return 'orange';\n      case 'MEDIUM':\n        return 'blue';\n      case 'LOW':\n        return 'default';\n      default:\n        return 'default';\n    }\n  };\n\n  const workOrderColumns = [\n    {\n      title: '工单编号',\n      dataIndex: 'id',\n      key: 'id',\n      render: (id: number) => `WO-${id.toString().padStart(4, '0')}`,\n    },\n    {\n      title: '零件信息',\n      key: 'part',\n      render: (_, record: WorkOrder) => (\n        <div>\n          <div><Text strong>{record.part_number}</Text></div>\n          <div><Text type=\"secondary\">{record.part_name}</Text></div>\n        </div>\n      ),\n    },\n    {\n      title: '数量',\n      dataIndex: 'quantity',\n      key: 'quantity',\n    },\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      render: (priority: string) => (\n        <Tag color={getPriorityColor(priority)}>\n          {priority}\n        </Tag>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>\n          {getStatusText(status)}\n        </Tag>\n      ),\n    },\n    {\n      title: '计划开始',\n      dataIndex: 'planned_start',\n      key: 'planned_start',\n      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',\n    },\n    {\n      title: '计划结束',\n      dataIndex: 'planned_end',\n      key: 'planned_end',\n      render: (date: string) => date ? dayjs(date).format('YYYY-MM-DD') : '-',\n    },\n  ];\n\n  if (!project) {\n    return <div>加载中...</div>;\n  }\n\n  // 计算项目进度\n  const totalWorkOrders = workOrders.length;\n  const completedWorkOrders = workOrders.filter(wo => wo.status === 'COMPLETED').length;\n  const progress = totalWorkOrders > 0 ? Math.round((completedWorkOrders / totalWorkOrders) * 100) : 0;\n\n  return (\n    <div>\n      {/* 页面头部 */}\n      <div style={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        marginBottom: '24px'\n      }}>\n        <Space>\n          <Button\n            icon={<ArrowLeftOutlined />}\n            onClick={() => navigate('/projects')}\n          >\n            返回\n          </Button>\n          <Title level={2} style={{ margin: 0 }}>\n            {project.project_name}\n          </Title>\n          <Tag color={getStatusColor(project.status)}>\n            {getStatusText(project.status)}\n          </Tag>\n        </Space>\n        <Button\n          type=\"primary\"\n          icon={<EditOutlined />}\n        >\n          编辑项目\n        </Button>\n      </div>\n\n      {/* 项目概览 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"项目进度\"\n              value={progress}\n              suffix=\"%\"\n              prefix={<ScheduleOutlined />}\n            />\n            <Progress percent={progress} size=\"small\" style={{ marginTop: '8px' }} />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"工单总数\"\n              value={totalWorkOrders}\n              prefix={<FileTextOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成工单\"\n              value={completedWorkOrders}\n              prefix={<FileTextOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"项目天数\"\n              value={project.start_date && project.end_date\n                ? dayjs(project.end_date).diff(dayjs(project.start_date), 'day')\n                : 0\n              }\n              prefix={<CalendarOutlined />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 详细信息 */}\n      <Card style={{ marginBottom: '24px' }}>\n        <Descriptions title=\"项目信息\" bordered column={2}>\n          <Descriptions.Item label=\"项目名称\">\n            {project.project_name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"客户名称\">\n            {project.customer_name}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"项目状态\">\n            <Tag color={getStatusColor(project.status)}>\n              {getStatusText(project.status)}\n            </Tag>\n          </Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">\n            {dayjs(project.created_at).format('YYYY-MM-DD HH:mm')}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"开始日期\">\n            {project.start_date ? dayjs(project.start_date).format('YYYY-MM-DD') : '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"结束日期\">\n            {project.end_date ? dayjs(project.end_date).format('YYYY-MM-DD') : '-'}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"项目描述\" span={2}>\n            {project.description || '暂无描述'}\n          </Descriptions.Item>\n        </Descriptions>\n      </Card>\n\n      {/* 标签页内容 */}\n      <Card>\n        <Tabs\n          defaultActiveKey=\"workorders\"\n          items={[\n            {\n              key: 'workorders',\n              label: (\n                <span>\n                  <FileTextOutlined />\n                  工单列表\n                </span>\n              ),\n              children: (\n                <Table\n                  columns={workOrderColumns}\n                  dataSource={workOrders}\n                  rowKey=\"id\"\n                  pagination={false}\n                />\n              ),\n            },\n            {\n              key: 'timeline',\n              label: (\n                <span>\n                  <ScheduleOutlined />\n                  项目时间线\n                </span>\n              ),\n              children: (\n                <Timeline\n                  items={[\n                    {\n                      children: '项目创建',\n                      color: 'blue',\n                    },\n                    {\n                      children: '项目启动',\n                      color: 'green',\n                    },\n                    {\n                      children: '第一批工单完成',\n                      color: 'green',\n                    },\n                    {\n                      children: '项目进行中...',\n                      color: 'blue',\n                    },\n                  ]}\n                />\n              ),\n            },\n            {\n              key: 'team',\n              label: (\n                <span>\n                  <TeamOutlined />\n                  项目团队\n                </span>\n              ),\n              children: (\n                <List\n                  itemLayout=\"horizontal\"\n                  dataSource={[\n                    { name: '张工程师', role: '项目经理', avatar: 'Z' },\n                    { name: '李师傅', role: '生产主管', avatar: 'L' },\n                    { name: '王技师', role: '质量检验', avatar: 'W' },\n                  ]}\n                  renderItem={(item) => (\n                    <List.Item>\n                      <List.Item.Meta\n                        avatar={<Avatar>{item.avatar}</Avatar>}\n                        title={item.name}\n                        description={item.role}\n                      />\n                    </List.Item>\n                  )}\n                />\n              ),\n            },\n          ]}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default ProjectDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,UAAU,EACVC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,YAAY,EACZC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,IAAI,QACC,MAAM;AACb,SACEC,iBAAiB,EACjBC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,YAAY,EACZC,gBAAgB,QACX,mBAAmB;AAC1B,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,yBAAyB;AAE1E,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAG3B,UAAU;AAElC,MAAM4B,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAG,CAAC,GAAGhC,SAAS,CAAiB,CAAC;EAC1C,MAAMiC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAiB,IAAI,CAAC;EAC5D,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACA,MAAM0C,WAAoB,GAAG;IAC3BR,EAAE,EAAE,CAAC;IACLS,YAAY,EAAE,WAAW;IACzBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,mDAAmD;IAChEC,MAAM,EAAE,aAAa;IACrBC,UAAU,EAAE,YAAY;IACxBC,QAAQ,EAAE,YAAY;IACtBC,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE;EACd,CAAC;;EAED;EACA,MAAMC,cAA2B,GAAG,CAClC;IACEjB,EAAE,EAAE,CAAC;IACLkB,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,GAAG;IACbR,MAAM,EAAE,aAAa;IACrBS,QAAQ,EAAE,MAAM;IAChBC,aAAa,EAAE,YAAY;IAC3BC,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE,YAAY;IAC1BT,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE,sBAAsB;IAClCS,WAAW,EAAE,UAAU;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE1B,EAAE,EAAE,CAAC;IACLkB,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZR,MAAM,EAAE,SAAS;IACjBS,QAAQ,EAAE,QAAQ;IAClBC,aAAa,EAAE,YAAY;IAC3BC,WAAW,EAAE,YAAY;IACzBR,UAAU,EAAE,sBAAsB;IAClCC,UAAU,EAAE,sBAAsB;IAClCS,WAAW,EAAE,UAAU;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAED3D,SAAS,CAAC,MAAM;IACd,IAAIiC,EAAE,EAAE;MACN2B,kBAAkB,CAAC,CAAC;MACpBC,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAC5B,EAAE,CAAC,CAAC;EAER,MAAM2B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrCpB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMsB,QAAQ,GAAG,MAAMtC,cAAc,CAACuC,UAAU,CAACC,MAAM,CAAC/B,EAAE,CAAC,CAAC;MAC5D,IAAI6B,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;QACrC9B,UAAU,CAAC0B,QAAQ,CAACI,IAAI,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,KAAK,CAAC;MAC7BhC,UAAU,CAACK,WAAW,CAAC;IACzB,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMrC,gBAAgB,CAAC8C,aAAa,CAAC;QAAEpB,UAAU,EAAEa,MAAM,CAAC/B,EAAE;MAAE,CAAC,CAAC;MACjF,IAAI6B,QAAQ,CAACG,OAAO,IAAIH,QAAQ,CAACI,IAAI,EAAE;QACrC5B,aAAa,CAACwB,QAAQ,CAACI,IAAI,CAACA,IAAI,CAAC;MACnC,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,KAAK,CAAC;MAC7B9B,aAAa,CAACY,cAAc,CAAC;IAC/B;EACF,CAAC;EAED,MAAMsB,cAAc,GAAI3B,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,MAAM;MACf,KAAK,aAAa;QAChB,OAAO,OAAO;MAChB,KAAK,WAAW;QACd,OAAO,SAAS;MAClB,KAAK,WAAW;QACd,OAAO,KAAK;MACd;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAM4B,aAAa,GAAI5B,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,KAAK;MACd,KAAK,aAAa;QAChB,OAAO,KAAK;MACd,KAAK,WAAW;QACd,OAAO,KAAK;MACd,KAAK,WAAW;QACd,OAAO,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAM6B,gBAAgB,GAAIpB,QAAgB,IAAK;IAC7C,QAAQA,QAAQ;MACd,KAAK,QAAQ;QACX,OAAO,KAAK;MACd,KAAK,MAAM;QACT,OAAO,QAAQ;MACjB,KAAK,QAAQ;QACX,OAAO,MAAM;MACf,KAAK,KAAK;QACR,OAAO,SAAS;MAClB;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMqB,gBAAgB,GAAG,CACvB;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,MAAM,EAAG9C,EAAU,IAAK,MAAMA,EAAE,CAAC+C,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAC9D,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbE,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACG,CAAC,EAAEC,MAAiB,kBAC3BvD,OAAA;MAAAwD,QAAA,gBACExD,OAAA;QAAAwD,QAAA,eAAKxD,OAAA,CAACE,IAAI;UAACuD,MAAM;UAAAD,QAAA,EAAED,MAAM,CAACzB;QAAW;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnD7D,OAAA;QAAAwD,QAAA,eAAKxD,OAAA,CAACE,IAAI;UAAC4D,IAAI,EAAC,WAAW;UAAAN,QAAA,EAAED,MAAM,CAACxB;QAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGzB,QAAgB,iBACvB1B,OAAA,CAACpB,GAAG;MAACmF,KAAK,EAAEjB,gBAAgB,CAACpB,QAAQ,CAAE;MAAA8B,QAAA,EACpC9B;IAAQ;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAET,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGlC,MAAc,iBACrBjB,OAAA,CAACpB,GAAG;MAACmF,KAAK,EAAEnB,cAAc,CAAC3B,MAAM,CAAE;MAAAuC,QAAA,EAChCX,aAAa,CAAC5B,MAAM;IAAC;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB;EAET,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAGa,IAAY,IAAKA,IAAI,GAAGlE,KAAK,CAACkE,IAAI,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC,GAAG;EACtE,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGa,IAAY,IAAKA,IAAI,GAAGlE,KAAK,CAACkE,IAAI,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC,GAAG;EACtE,CAAC,CACF;EAED,IAAI,CAAC1D,OAAO,EAAE;IACZ,oBAAOP,OAAA;MAAAwD,QAAA,EAAK;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1B;;EAEA;EACA,MAAMK,eAAe,GAAGzD,UAAU,CAAC0D,MAAM;EACzC,MAAMC,mBAAmB,GAAG3D,UAAU,CAAC4D,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACrD,MAAM,KAAK,WAAW,CAAC,CAACkD,MAAM;EACrF,MAAMI,QAAQ,GAAGL,eAAe,GAAG,CAAC,GAAGM,IAAI,CAACC,KAAK,CAAEL,mBAAmB,GAAGF,eAAe,GAAI,GAAG,CAAC,GAAG,CAAC;EAEpG,oBACElE,OAAA;IAAAwD,QAAA,gBAEExD,OAAA;MAAK0E,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,YAAY,EAAE;MAChB,CAAE;MAAAtB,QAAA,gBACAxD,OAAA,CAAClB,KAAK;QAAA0E,QAAA,gBACJxD,OAAA,CAACnB,MAAM;UACLkG,IAAI,eAAE/E,OAAA,CAACV,iBAAiB;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC5BmB,OAAO,EAAEA,CAAA,KAAM1E,QAAQ,CAAC,WAAW,CAAE;UAAAkD,QAAA,EACtC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT7D,OAAA,CAACC,KAAK;UAACgF,KAAK,EAAE,CAAE;UAACP,KAAK,EAAE;YAAEQ,MAAM,EAAE;UAAE,CAAE;UAAA1B,QAAA,EACnCjD,OAAO,CAACO;QAAY;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACR7D,OAAA,CAACpB,GAAG;UAACmF,KAAK,EAAEnB,cAAc,CAACrC,OAAO,CAACU,MAAM,CAAE;UAAAuC,QAAA,EACxCX,aAAa,CAACtC,OAAO,CAACU,MAAM;QAAC;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eACR7D,OAAA,CAACnB,MAAM;QACLiF,IAAI,EAAC,SAAS;QACdiB,IAAI,eAAE/E,OAAA,CAACT,YAAY;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAL,QAAA,EACxB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7D,OAAA,CAACvB,GAAG;MAAC0G,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACT,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAtB,QAAA,gBACrDxD,OAAA,CAACtB,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXxD,OAAA,CAACxB,IAAI;UAAAgF,QAAA,gBACHxD,OAAA,CAACd,SAAS;YACR8D,KAAK,EAAC,0BAAM;YACZqC,KAAK,EAAEd,QAAS;YAChBe,MAAM,EAAC,GAAG;YACVC,MAAM,eAAEvF,OAAA,CAACP,gBAAgB;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACF7D,OAAA,CAACf,QAAQ;YAACuG,OAAO,EAAEjB,QAAS;YAACkB,IAAI,EAAC,OAAO;YAACf,KAAK,EAAE;cAAEgB,SAAS,EAAE;YAAM;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAACtB,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXxD,OAAA,CAACxB,IAAI;UAAAgF,QAAA,eACHxD,OAAA,CAACd,SAAS;YACR8D,KAAK,EAAC,0BAAM;YACZqC,KAAK,EAAEnB,eAAgB;YACvBqB,MAAM,eAAEvF,OAAA,CAACR,gBAAgB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAACtB,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXxD,OAAA,CAACxB,IAAI;UAAAgF,QAAA,eACHxD,OAAA,CAACd,SAAS;YACR8D,KAAK,EAAC,gCAAO;YACbqC,KAAK,EAAEjB,mBAAoB;YAC3BmB,MAAM,eAAEvF,OAAA,CAACR,gBAAgB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7B8B,UAAU,EAAE;cAAE5B,KAAK,EAAE;YAAU;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN7D,OAAA,CAACtB,GAAG;QAAC0G,IAAI,EAAE,CAAE;QAAA5B,QAAA,eACXxD,OAAA,CAACxB,IAAI;UAAAgF,QAAA,eACHxD,OAAA,CAACd,SAAS;YACR8D,KAAK,EAAC,0BAAM;YACZqC,KAAK,EAAE9E,OAAO,CAACW,UAAU,IAAIX,OAAO,CAACY,QAAQ,GACzCrB,KAAK,CAACS,OAAO,CAACY,QAAQ,CAAC,CAACyE,IAAI,CAAC9F,KAAK,CAACS,OAAO,CAACW,UAAU,CAAC,EAAE,KAAK,CAAC,GAC9D,CACH;YACDqE,MAAM,eAAEvF,OAAA,CAACL,gBAAgB;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7D,OAAA,CAACxB,IAAI;MAACkG,KAAK,EAAE;QAAEI,YAAY,EAAE;MAAO,CAAE;MAAAtB,QAAA,eACpCxD,OAAA,CAACrB,YAAY;QAACqE,KAAK,EAAC,0BAAM;QAAC6C,QAAQ;QAACC,MAAM,EAAE,CAAE;QAAAtC,QAAA,gBAC5CxD,OAAA,CAACrB,YAAY,CAACoH,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5BjD,OAAO,CAACO;QAAY;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACpB7D,OAAA,CAACrB,YAAY,CAACoH,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5BjD,OAAO,CAACQ;QAAa;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACpB7D,OAAA,CAACrB,YAAY,CAACoH,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,eAC7BxD,OAAA,CAACpB,GAAG;YAACmF,KAAK,EAAEnB,cAAc,CAACrC,OAAO,CAACU,MAAM,CAAE;YAAAuC,QAAA,EACxCX,aAAa,CAACtC,OAAO,CAACU,MAAM;UAAC;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpB7D,OAAA,CAACrB,YAAY,CAACoH,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5B1D,KAAK,CAACS,OAAO,CAACa,UAAU,CAAC,CAAC6C,MAAM,CAAC,kBAAkB;QAAC;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACpB7D,OAAA,CAACrB,YAAY,CAACoH,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5BjD,OAAO,CAACW,UAAU,GAAGpB,KAAK,CAACS,OAAO,CAACW,UAAU,CAAC,CAAC+C,MAAM,CAAC,YAAY,CAAC,GAAG;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACpB7D,OAAA,CAACrB,YAAY,CAACoH,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxC,QAAA,EAC5BjD,OAAO,CAACY,QAAQ,GAAGrB,KAAK,CAACS,OAAO,CAACY,QAAQ,CAAC,CAAC8C,MAAM,CAAC,YAAY,CAAC,GAAG;QAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACpB7D,OAAA,CAACrB,YAAY,CAACoH,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACZ,IAAI,EAAE,CAAE;UAAA5B,QAAA,EACrCjD,OAAO,CAACS,WAAW,IAAI;QAAM;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGP7D,OAAA,CAACxB,IAAI;MAAAgF,QAAA,eACHxD,OAAA,CAACjB,IAAI;QACHkH,gBAAgB,EAAC,YAAY;QAC7BC,KAAK,EAAE,CACL;UACEhD,GAAG,EAAE,YAAY;UACjB8C,KAAK,eACHhG,OAAA;YAAAwD,QAAA,gBACExD,OAAA,CAACR,gBAAgB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;UACDL,QAAQ,eACNxD,OAAA,CAAChB,KAAK;YACJmH,OAAO,EAAEpD,gBAAiB;YAC1BqD,UAAU,EAAE3F,UAAW;YACvB4F,MAAM,EAAC,IAAI;YACXC,UAAU,EAAE;UAAM;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB;QAEL,CAAC,EACD;UACEX,GAAG,EAAE,UAAU;UACf8C,KAAK,eACHhG,OAAA;YAAAwD,QAAA,gBACExD,OAAA,CAACP,gBAAgB;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,kCAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;UACDL,QAAQ,eACNxD,OAAA,CAACb,QAAQ;YACP+G,KAAK,EAAE,CACL;cACE1C,QAAQ,EAAE,MAAM;cAChBO,KAAK,EAAE;YACT,CAAC,EACD;cACEP,QAAQ,EAAE,MAAM;cAChBO,KAAK,EAAE;YACT,CAAC,EACD;cACEP,QAAQ,EAAE,SAAS;cACnBO,KAAK,EAAE;YACT,CAAC,EACD;cACEP,QAAQ,EAAE,UAAU;cACpBO,KAAK,EAAE;YACT,CAAC;UACD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAEL,CAAC,EACD;UACEX,GAAG,EAAE,MAAM;UACX8C,KAAK,eACHhG,OAAA;YAAAwD,QAAA,gBACExD,OAAA,CAACN,YAAY;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAElB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;UACDL,QAAQ,eACNxD,OAAA,CAACX,IAAI;YACHkH,UAAU,EAAC,YAAY;YACvBH,UAAU,EAAE,CACV;cAAEI,IAAI,EAAE,MAAM;cAAEC,IAAI,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAI,CAAC,EAC3C;cAAEF,IAAI,EAAE,KAAK;cAAEC,IAAI,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAI,CAAC,EAC1C;cAAEF,IAAI,EAAE,KAAK;cAAEC,IAAI,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAI,CAAC,CAC1C;YACFC,UAAU,EAAGC,IAAI,iBACf5G,OAAA,CAACX,IAAI,CAAC0G,IAAI;cAAAvC,QAAA,eACRxD,OAAA,CAACX,IAAI,CAAC0G,IAAI,CAACc,IAAI;gBACbH,MAAM,eAAE1G,OAAA,CAACZ,MAAM;kBAAAoE,QAAA,EAAEoD,IAAI,CAACF;gBAAM;kBAAAhD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAE;gBACvCb,KAAK,EAAE4D,IAAI,CAACJ,IAAK;gBACjBxF,WAAW,EAAE4F,IAAI,CAACH;cAAK;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UACX;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAEL,CAAC;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACzD,EAAA,CAzYID,aAAuB;EAAA,QACZ9B,SAAS,EACPC,WAAW;AAAA;AAAAwI,EAAA,GAFxB3G,aAAuB;AA2Y7B,eAAeA,aAAa;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}