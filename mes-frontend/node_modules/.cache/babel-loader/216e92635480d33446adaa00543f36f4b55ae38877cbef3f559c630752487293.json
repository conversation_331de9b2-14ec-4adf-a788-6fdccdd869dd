{"ast": null, "code": "export { getDefaultStyle as bottomRight } from './default';", "map": {"version": 3, "names": ["getDefaultStyle", "bottomRight"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/bottomRight.ts"], "sourcesContent": ["export { getDefaultStyle as bottomRight } from './default';\n"], "mappings": "AAAA,SAASA,eAAe,IAAIC,WAAW,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}