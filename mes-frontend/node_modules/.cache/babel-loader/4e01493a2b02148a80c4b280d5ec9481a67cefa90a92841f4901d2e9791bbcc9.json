{"ast": null, "code": "const SMALL = 1e-10;\n/**\n * Returns the intersection area of a bunch of circles (where each circle\n * is an object having an x,y and radius property)\n */\nexport function intersectionArea(circles, stats) {\n  // Get all the intersection points of the circles\n  const intersectionPoints = getIntersectionPoints(circles);\n  // Filter out points that aren't included in all the circles\n  const innerPoints = intersectionPoints.filter(function (p) {\n    return containedInCircles(p, circles);\n  });\n  let arcArea = 0,\n    polygonArea = 0,\n    i;\n  const arcs = [];\n  // If we have intersection points that are within all the circles,\n  // then figure out the area contained by them\n  if (innerPoints.length > 1) {\n    // Sort the points by angle from the center of the polygon, which lets\n    // us just iterate over points to get the edges\n    const center = getCenter(innerPoints);\n    for (i = 0; i < innerPoints.length; ++i) {\n      const p = innerPoints[i];\n      p.angle = Math.atan2(p.x - center.x, p.y - center.y);\n    }\n    innerPoints.sort(function (a, b) {\n      return b.angle - a.angle;\n    });\n    // Iterate over all points, get arc between the points\n    // and update the areas\n    let p2 = innerPoints[innerPoints.length - 1];\n    for (i = 0; i < innerPoints.length; ++i) {\n      const p1 = innerPoints[i];\n      // Polygon area updates easily ...\n      polygonArea += (p2.x + p1.x) * (p1.y - p2.y);\n      // Updating the arc area is a little more involved\n      const midPoint = {\n        x: (p1.x + p2.x) / 2,\n        y: (p1.y + p2.y) / 2\n      };\n      let arc = null;\n      for (let j = 0; j < p1.parentIndex.length; ++j) {\n        if (p2.parentIndex.indexOf(p1.parentIndex[j]) > -1) {\n          // Figure out the angle halfway between the two points\n          // on the current circle\n          const circle = circles[p1.parentIndex[j]],\n            a1 = Math.atan2(p1.x - circle.x, p1.y - circle.y),\n            a2 = Math.atan2(p2.x - circle.x, p2.y - circle.y);\n          let angleDiff = a2 - a1;\n          if (angleDiff < 0) {\n            angleDiff += 2 * Math.PI;\n          }\n          // and use that angle to figure out the width of the\n          // arc\n          const a = a2 - angleDiff / 2;\n          let width = distance(midPoint, {\n            x: circle.x + circle.radius * Math.sin(a),\n            y: circle.y + circle.radius * Math.cos(a)\n          });\n          // Clamp the width to the largest is can actually be\n          // (sometimes slightly overflows because of FP errors)\n          if (width > circle.radius * 2) {\n            width = circle.radius * 2;\n          }\n          // Pick the circle whose arc has the smallest width\n          if (arc === null || arc.width > width) {\n            arc = {\n              circle: circle,\n              width: width,\n              p1: p1,\n              p2: p2\n            };\n          }\n        }\n      }\n      if (arc !== null) {\n        arcs.push(arc);\n        arcArea += circleArea(arc.circle.radius, arc.width);\n        p2 = p1;\n      }\n    }\n  } else {\n    // No intersection points, is either disjoint - or is completely\n    // overlapped. figure out which by examining the smallest circle\n    let smallest = circles[0];\n    for (i = 1; i < circles.length; ++i) {\n      if (circles[i].radius < smallest.radius) {\n        smallest = circles[i];\n      }\n    }\n    // Make sure the smallest circle is completely contained in all\n    // the other circles\n    let disjoint = false;\n    for (i = 0; i < circles.length; ++i) {\n      if (distance(circles[i], smallest) > Math.abs(smallest.radius - circles[i].radius)) {\n        disjoint = true;\n        break;\n      }\n    }\n    if (disjoint) {\n      arcArea = polygonArea = 0;\n    } else {\n      arcArea = smallest.radius * smallest.radius * Math.PI;\n      arcs.push({\n        circle: smallest,\n        p1: {\n          x: smallest.x,\n          y: smallest.y + smallest.radius\n        },\n        p2: {\n          x: smallest.x - SMALL,\n          y: smallest.y + smallest.radius\n        },\n        width: smallest.radius * 2\n      });\n    }\n  }\n  polygonArea /= 2;\n  if (stats) {\n    stats.area = arcArea + polygonArea;\n    stats.arcArea = arcArea;\n    stats.polygonArea = polygonArea;\n    stats.arcs = arcs;\n    stats.innerPoints = innerPoints;\n    stats.intersectionPoints = intersectionPoints;\n  }\n  return arcArea + polygonArea;\n}\n/**\n * Returns whether a point is contained by all of a list of circles\n */\nexport function containedInCircles(point, circles) {\n  for (let i = 0; i < circles.length; ++i) {\n    if (distance(point, circles[i]) > circles[i].radius + SMALL) {\n      return false;\n    }\n  }\n  return true;\n}\n/** Gets all intersection points between a bunch of circles */\nfunction getIntersectionPoints(circles) {\n  const ret = [];\n  for (let i = 0; i < circles.length; ++i) {\n    for (let j = i + 1; j < circles.length; ++j) {\n      const intersect = circleCircleIntersection(circles[i], circles[j]);\n      for (let k = 0; k < intersect.length; ++k) {\n        const p = intersect[k];\n        p.parentIndex = [i, j];\n        ret.push(p);\n      }\n    }\n  }\n  return ret;\n}\n/** Circular segment area calculation. See http://mathworld.wolfram.com/CircularSegment.html */\nexport function circleArea(r, width) {\n  return r * r * Math.acos(1 - width / r) - (r - width) * Math.sqrt(width * (2 * r - width));\n}\n/** Euclidean distance between two points */\nexport function distance(p1, p2) {\n  return Math.sqrt((p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y));\n}\n/** Returns the overlap area of two circles of radius r1 and r2 - that\nhave their centers separated by distance d. Simpler faster\ncircle intersection for only two circles */\nexport function circleOverlap(r1, r2, d) {\n  // no overlap\n  if (d >= r1 + r2) {\n    return 0;\n  }\n  // Completely overlapped\n  if (d <= Math.abs(r1 - r2)) {\n    return Math.PI * Math.min(r1, r2) * Math.min(r1, r2);\n  }\n  const w1 = r1 - (d * d - r2 * r2 + r1 * r1) / (2 * d),\n    w2 = r2 - (d * d - r1 * r1 + r2 * r2) / (2 * d);\n  return circleArea(r1, w1) + circleArea(r2, w2);\n}\n/** Given two circles (containing a x/y/radius attributes),\nreturns the intersecting points if possible.\nnote: doesn't handle cases where there are infinitely many\nintersection points (circles are equivalent):, or only one intersection point*/\nexport function circleCircleIntersection(p1, p2) {\n  const d = distance(p1, p2),\n    r1 = p1.radius,\n    r2 = p2.radius;\n  // If to far away, or self contained - can't be done\n  if (d >= r1 + r2 || d <= Math.abs(r1 - r2)) {\n    return [];\n  }\n  const a = (r1 * r1 - r2 * r2 + d * d) / (2 * d),\n    h = Math.sqrt(r1 * r1 - a * a),\n    x0 = p1.x + a * (p2.x - p1.x) / d,\n    y0 = p1.y + a * (p2.y - p1.y) / d,\n    rx = -(p2.y - p1.y) * (h / d),\n    ry = -(p2.x - p1.x) * (h / d);\n  return [{\n    x: x0 + rx,\n    y: y0 - ry\n  }, {\n    x: x0 - rx,\n    y: y0 + ry\n  }];\n}\n/** Returns the center of a bunch of points */\nexport function getCenter(points) {\n  const center = {\n    x: 0,\n    y: 0\n  };\n  for (let i = 0; i < points.length; ++i) {\n    center.x += points[i].x;\n    center.y += points[i].y;\n  }\n  center.x /= points.length;\n  center.y /= points.length;\n  return center;\n}", "map": {"version": 3, "names": ["SMALL", "intersectionArea", "circles", "stats", "intersectionPoints", "getIntersectionPoints", "innerPoints", "filter", "p", "containedInCircles", "arcArea", "polygonArea", "i", "arcs", "length", "center", "getCenter", "angle", "Math", "atan2", "x", "y", "sort", "a", "b", "p2", "p1", "midPoint", "arc", "j", "parentIndex", "indexOf", "circle", "a1", "a2", "angleDiff", "PI", "width", "distance", "radius", "sin", "cos", "push", "circleArea", "smallest", "disjoint", "abs", "area", "point", "ret", "intersect", "circleCircleIntersection", "k", "r", "acos", "sqrt", "circleOverlap", "r1", "r2", "d", "min", "w1", "w2", "h", "x0", "y0", "rx", "ry", "points"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/venn/circleintersection.ts"], "sourcesContent": ["const SMALL = 1e-10;\n\n/**\n * Returns the intersection area of a bunch of circles (where each circle\n * is an object having an x,y and radius property)\n */\nexport function intersectionArea(circles, stats?: any) {\n  // Get all the intersection points of the circles\n  const intersectionPoints = getIntersectionPoints(circles);\n\n  // Filter out points that aren't included in all the circles\n  const innerPoints = intersectionPoints.filter(function (p) {\n    return containedInCircles(p, circles);\n  });\n\n  let arcArea = 0,\n    polygonArea = 0,\n    i;\n  const arcs = [];\n  // If we have intersection points that are within all the circles,\n  // then figure out the area contained by them\n  if (innerPoints.length > 1) {\n    // Sort the points by angle from the center of the polygon, which lets\n    // us just iterate over points to get the edges\n    const center = getCenter(innerPoints);\n    for (i = 0; i < innerPoints.length; ++i) {\n      const p = innerPoints[i];\n      p.angle = Math.atan2(p.x - center.x, p.y - center.y);\n    }\n    innerPoints.sort(function (a, b) {\n      return b.angle - a.angle;\n    });\n\n    // Iterate over all points, get arc between the points\n    // and update the areas\n    let p2 = innerPoints[innerPoints.length - 1];\n    for (i = 0; i < innerPoints.length; ++i) {\n      const p1 = innerPoints[i];\n\n      // Polygon area updates easily ...\n      polygonArea += (p2.x + p1.x) * (p1.y - p2.y);\n\n      // Updating the arc area is a little more involved\n      const midPoint = { x: (p1.x + p2.x) / 2, y: (p1.y + p2.y) / 2 };\n      let arc = null;\n\n      for (let j = 0; j < p1.parentIndex.length; ++j) {\n        if (p2.parentIndex.indexOf(p1.parentIndex[j]) > -1) {\n          // Figure out the angle halfway between the two points\n          // on the current circle\n          const circle = circles[p1.parentIndex[j]],\n            a1 = Math.atan2(p1.x - circle.x, p1.y - circle.y),\n            a2 = Math.atan2(p2.x - circle.x, p2.y - circle.y);\n\n          let angleDiff = a2 - a1;\n          if (angleDiff < 0) {\n            angleDiff += 2 * Math.PI;\n          }\n\n          // and use that angle to figure out the width of the\n          // arc\n          const a = a2 - angleDiff / 2;\n          let width = distance(midPoint, {\n            x: circle.x + circle.radius * Math.sin(a),\n            y: circle.y + circle.radius * Math.cos(a),\n          });\n\n          // Clamp the width to the largest is can actually be\n          // (sometimes slightly overflows because of FP errors)\n          if (width > circle.radius * 2) {\n            width = circle.radius * 2;\n          }\n\n          // Pick the circle whose arc has the smallest width\n          if (arc === null || arc.width > width) {\n            arc = { circle: circle, width: width, p1: p1, p2: p2 };\n          }\n        }\n      }\n\n      if (arc !== null) {\n        arcs.push(arc);\n        arcArea += circleArea(arc.circle.radius, arc.width);\n        p2 = p1;\n      }\n    }\n  } else {\n    // No intersection points, is either disjoint - or is completely\n    // overlapped. figure out which by examining the smallest circle\n    let smallest = circles[0];\n    for (i = 1; i < circles.length; ++i) {\n      if (circles[i].radius < smallest.radius) {\n        smallest = circles[i];\n      }\n    }\n\n    // Make sure the smallest circle is completely contained in all\n    // the other circles\n    let disjoint = false;\n    for (i = 0; i < circles.length; ++i) {\n      if (\n        distance(circles[i], smallest) >\n        Math.abs(smallest.radius - circles[i].radius)\n      ) {\n        disjoint = true;\n        break;\n      }\n    }\n\n    if (disjoint) {\n      arcArea = polygonArea = 0;\n    } else {\n      arcArea = smallest.radius * smallest.radius * Math.PI;\n      arcs.push({\n        circle: smallest,\n        p1: { x: smallest.x, y: smallest.y + smallest.radius },\n        p2: { x: smallest.x - SMALL, y: smallest.y + smallest.radius },\n        width: smallest.radius * 2,\n      });\n    }\n  }\n\n  polygonArea /= 2;\n  if (stats) {\n    stats.area = arcArea + polygonArea;\n    stats.arcArea = arcArea;\n    stats.polygonArea = polygonArea;\n    stats.arcs = arcs;\n    stats.innerPoints = innerPoints;\n    stats.intersectionPoints = intersectionPoints;\n  }\n\n  return arcArea + polygonArea;\n}\n\n/**\n * Returns whether a point is contained by all of a list of circles\n */\nexport function containedInCircles(point, circles) {\n  for (let i = 0; i < circles.length; ++i) {\n    if (distance(point, circles[i]) > circles[i].radius + SMALL) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/** Gets all intersection points between a bunch of circles */\nfunction getIntersectionPoints(circles) {\n  const ret = [];\n  for (let i = 0; i < circles.length; ++i) {\n    for (let j = i + 1; j < circles.length; ++j) {\n      const intersect = circleCircleIntersection(circles[i], circles[j]);\n      for (let k = 0; k < intersect.length; ++k) {\n        const p: any = intersect[k];\n        p.parentIndex = [i, j];\n        ret.push(p);\n      }\n    }\n  }\n  return ret;\n}\n\n/** Circular segment area calculation. See http://mathworld.wolfram.com/CircularSegment.html */\nexport function circleArea(r, width) {\n  return (\n    r * r * Math.acos(1 - width / r) -\n    (r - width) * Math.sqrt(width * (2 * r - width))\n  );\n}\n\n/** Euclidean distance between two points */\nexport function distance(p1, p2) {\n  return Math.sqrt(\n    (p1.x - p2.x) * (p1.x - p2.x) + (p1.y - p2.y) * (p1.y - p2.y),\n  );\n}\n\n/** Returns the overlap area of two circles of radius r1 and r2 - that\nhave their centers separated by distance d. Simpler faster\ncircle intersection for only two circles */\nexport function circleOverlap(r1, r2, d) {\n  // no overlap\n  if (d >= r1 + r2) {\n    return 0;\n  }\n\n  // Completely overlapped\n  if (d <= Math.abs(r1 - r2)) {\n    return Math.PI * Math.min(r1, r2) * Math.min(r1, r2);\n  }\n\n  const w1 = r1 - (d * d - r2 * r2 + r1 * r1) / (2 * d),\n    w2 = r2 - (d * d - r1 * r1 + r2 * r2) / (2 * d);\n  return circleArea(r1, w1) + circleArea(r2, w2);\n}\n\n/** Given two circles (containing a x/y/radius attributes),\nreturns the intersecting points if possible.\nnote: doesn't handle cases where there are infinitely many\nintersection points (circles are equivalent):, or only one intersection point*/\nexport function circleCircleIntersection(p1, p2) {\n  const d = distance(p1, p2),\n    r1 = p1.radius,\n    r2 = p2.radius;\n\n  // If to far away, or self contained - can't be done\n  if (d >= r1 + r2 || d <= Math.abs(r1 - r2)) {\n    return [];\n  }\n\n  const a = (r1 * r1 - r2 * r2 + d * d) / (2 * d),\n    h = Math.sqrt(r1 * r1 - a * a),\n    x0 = p1.x + (a * (p2.x - p1.x)) / d,\n    y0 = p1.y + (a * (p2.y - p1.y)) / d,\n    rx = -(p2.y - p1.y) * (h / d),\n    ry = -(p2.x - p1.x) * (h / d);\n\n  return [\n    { x: x0 + rx, y: y0 - ry },\n    { x: x0 - rx, y: y0 + ry },\n  ];\n}\n\n/** Returns the center of a bunch of points */\nexport function getCenter(points) {\n  const center = { x: 0, y: 0 };\n  for (let i = 0; i < points.length; ++i) {\n    center.x += points[i].x;\n    center.y += points[i].y;\n  }\n  center.x /= points.length;\n  center.y /= points.length;\n  return center;\n}\n"], "mappings": "AAAA,MAAMA,KAAK,GAAG,KAAK;AAEnB;;;;AAIA,OAAM,SAAUC,gBAAgBA,CAACC,OAAO,EAAEC,KAAW;EACnD;EACA,MAAMC,kBAAkB,GAAGC,qBAAqB,CAACH,OAAO,CAAC;EAEzD;EACA,MAAMI,WAAW,GAAGF,kBAAkB,CAACG,MAAM,CAAC,UAAUC,CAAC;IACvD,OAAOC,kBAAkB,CAACD,CAAC,EAAEN,OAAO,CAAC;EACvC,CAAC,CAAC;EAEF,IAAIQ,OAAO,GAAG,CAAC;IACbC,WAAW,GAAG,CAAC;IACfC,CAAC;EACH,MAAMC,IAAI,GAAG,EAAE;EACf;EACA;EACA,IAAIP,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAE;IAC1B;IACA;IACA,MAAMC,MAAM,GAAGC,SAAS,CAACV,WAAW,CAAC;IACrC,KAAKM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,CAACQ,MAAM,EAAE,EAAEF,CAAC,EAAE;MACvC,MAAMJ,CAAC,GAAGF,WAAW,CAACM,CAAC,CAAC;MACxBJ,CAAC,CAACS,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACX,CAAC,CAACY,CAAC,GAAGL,MAAM,CAACK,CAAC,EAAEZ,CAAC,CAACa,CAAC,GAAGN,MAAM,CAACM,CAAC,CAAC;;IAEtDf,WAAW,CAACgB,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC;MAC7B,OAAOA,CAAC,CAACP,KAAK,GAAGM,CAAC,CAACN,KAAK;IAC1B,CAAC,CAAC;IAEF;IACA;IACA,IAAIQ,EAAE,GAAGnB,WAAW,CAACA,WAAW,CAACQ,MAAM,GAAG,CAAC,CAAC;IAC5C,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,CAACQ,MAAM,EAAE,EAAEF,CAAC,EAAE;MACvC,MAAMc,EAAE,GAAGpB,WAAW,CAACM,CAAC,CAAC;MAEzB;MACAD,WAAW,IAAI,CAACc,EAAE,CAACL,CAAC,GAAGM,EAAE,CAACN,CAAC,KAAKM,EAAE,CAACL,CAAC,GAAGI,EAAE,CAACJ,CAAC,CAAC;MAE5C;MACA,MAAMM,QAAQ,GAAG;QAAEP,CAAC,EAAE,CAACM,EAAE,CAACN,CAAC,GAAGK,EAAE,CAACL,CAAC,IAAI,CAAC;QAAEC,CAAC,EAAE,CAACK,EAAE,CAACL,CAAC,GAAGI,EAAE,CAACJ,CAAC,IAAI;MAAC,CAAE;MAC/D,IAAIO,GAAG,GAAG,IAAI;MAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,EAAE,CAACI,WAAW,CAAChB,MAAM,EAAE,EAAEe,CAAC,EAAE;QAC9C,IAAIJ,EAAE,CAACK,WAAW,CAACC,OAAO,CAACL,EAAE,CAACI,WAAW,CAACD,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;UAClD;UACA;UACA,MAAMG,MAAM,GAAG9B,OAAO,CAACwB,EAAE,CAACI,WAAW,CAACD,CAAC,CAAC,CAAC;YACvCI,EAAE,GAAGf,IAAI,CAACC,KAAK,CAACO,EAAE,CAACN,CAAC,GAAGY,MAAM,CAACZ,CAAC,EAAEM,EAAE,CAACL,CAAC,GAAGW,MAAM,CAACX,CAAC,CAAC;YACjDa,EAAE,GAAGhB,IAAI,CAACC,KAAK,CAACM,EAAE,CAACL,CAAC,GAAGY,MAAM,CAACZ,CAAC,EAAEK,EAAE,CAACJ,CAAC,GAAGW,MAAM,CAACX,CAAC,CAAC;UAEnD,IAAIc,SAAS,GAAGD,EAAE,GAAGD,EAAE;UACvB,IAAIE,SAAS,GAAG,CAAC,EAAE;YACjBA,SAAS,IAAI,CAAC,GAAGjB,IAAI,CAACkB,EAAE;;UAG1B;UACA;UACA,MAAMb,CAAC,GAAGW,EAAE,GAAGC,SAAS,GAAG,CAAC;UAC5B,IAAIE,KAAK,GAAGC,QAAQ,CAACX,QAAQ,EAAE;YAC7BP,CAAC,EAAEY,MAAM,CAACZ,CAAC,GAAGY,MAAM,CAACO,MAAM,GAAGrB,IAAI,CAACsB,GAAG,CAACjB,CAAC,CAAC;YACzCF,CAAC,EAAEW,MAAM,CAACX,CAAC,GAAGW,MAAM,CAACO,MAAM,GAAGrB,IAAI,CAACuB,GAAG,CAAClB,CAAC;WACzC,CAAC;UAEF;UACA;UACA,IAAIc,KAAK,GAAGL,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;YAC7BF,KAAK,GAAGL,MAAM,CAACO,MAAM,GAAG,CAAC;;UAG3B;UACA,IAAIX,GAAG,KAAK,IAAI,IAAIA,GAAG,CAACS,KAAK,GAAGA,KAAK,EAAE;YACrCT,GAAG,GAAG;cAAEI,MAAM,EAAEA,MAAM;cAAEK,KAAK,EAAEA,KAAK;cAAEX,EAAE,EAAEA,EAAE;cAAED,EAAE,EAAEA;YAAE,CAAE;;;;MAK5D,IAAIG,GAAG,KAAK,IAAI,EAAE;QAChBf,IAAI,CAAC6B,IAAI,CAACd,GAAG,CAAC;QACdlB,OAAO,IAAIiC,UAAU,CAACf,GAAG,CAACI,MAAM,CAACO,MAAM,EAAEX,GAAG,CAACS,KAAK,CAAC;QACnDZ,EAAE,GAAGC,EAAE;;;GAGZ,MAAM;IACL;IACA;IACA,IAAIkB,QAAQ,GAAG1C,OAAO,CAAC,CAAC,CAAC;IACzB,KAAKU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,CAACY,MAAM,EAAE,EAAEF,CAAC,EAAE;MACnC,IAAIV,OAAO,CAACU,CAAC,CAAC,CAAC2B,MAAM,GAAGK,QAAQ,CAACL,MAAM,EAAE;QACvCK,QAAQ,GAAG1C,OAAO,CAACU,CAAC,CAAC;;;IAIzB;IACA;IACA,IAAIiC,QAAQ,GAAG,KAAK;IACpB,KAAKjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,CAACY,MAAM,EAAE,EAAEF,CAAC,EAAE;MACnC,IACE0B,QAAQ,CAACpC,OAAO,CAACU,CAAC,CAAC,EAAEgC,QAAQ,CAAC,GAC9B1B,IAAI,CAAC4B,GAAG,CAACF,QAAQ,CAACL,MAAM,GAAGrC,OAAO,CAACU,CAAC,CAAC,CAAC2B,MAAM,CAAC,EAC7C;QACAM,QAAQ,GAAG,IAAI;QACf;;;IAIJ,IAAIA,QAAQ,EAAE;MACZnC,OAAO,GAAGC,WAAW,GAAG,CAAC;KAC1B,MAAM;MACLD,OAAO,GAAGkC,QAAQ,CAACL,MAAM,GAAGK,QAAQ,CAACL,MAAM,GAAGrB,IAAI,CAACkB,EAAE;MACrDvB,IAAI,CAAC6B,IAAI,CAAC;QACRV,MAAM,EAAEY,QAAQ;QAChBlB,EAAE,EAAE;UAAEN,CAAC,EAAEwB,QAAQ,CAACxB,CAAC;UAAEC,CAAC,EAAEuB,QAAQ,CAACvB,CAAC,GAAGuB,QAAQ,CAACL;QAAM,CAAE;QACtDd,EAAE,EAAE;UAAEL,CAAC,EAAEwB,QAAQ,CAACxB,CAAC,GAAGpB,KAAK;UAAEqB,CAAC,EAAEuB,QAAQ,CAACvB,CAAC,GAAGuB,QAAQ,CAACL;QAAM,CAAE;QAC9DF,KAAK,EAAEO,QAAQ,CAACL,MAAM,GAAG;OAC1B,CAAC;;;EAIN5B,WAAW,IAAI,CAAC;EAChB,IAAIR,KAAK,EAAE;IACTA,KAAK,CAAC4C,IAAI,GAAGrC,OAAO,GAAGC,WAAW;IAClCR,KAAK,CAACO,OAAO,GAAGA,OAAO;IACvBP,KAAK,CAACQ,WAAW,GAAGA,WAAW;IAC/BR,KAAK,CAACU,IAAI,GAAGA,IAAI;IACjBV,KAAK,CAACG,WAAW,GAAGA,WAAW;IAC/BH,KAAK,CAACC,kBAAkB,GAAGA,kBAAkB;;EAG/C,OAAOM,OAAO,GAAGC,WAAW;AAC9B;AAEA;;;AAGA,OAAM,SAAUF,kBAAkBA,CAACuC,KAAK,EAAE9C,OAAO;EAC/C,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,CAACY,MAAM,EAAE,EAAEF,CAAC,EAAE;IACvC,IAAI0B,QAAQ,CAACU,KAAK,EAAE9C,OAAO,CAACU,CAAC,CAAC,CAAC,GAAGV,OAAO,CAACU,CAAC,CAAC,CAAC2B,MAAM,GAAGvC,KAAK,EAAE;MAC3D,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEA;AACA,SAASK,qBAAqBA,CAACH,OAAO;EACpC,MAAM+C,GAAG,GAAG,EAAE;EACd,KAAK,IAAIrC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,OAAO,CAACY,MAAM,EAAE,EAAEF,CAAC,EAAE;IACvC,KAAK,IAAIiB,CAAC,GAAGjB,CAAC,GAAG,CAAC,EAAEiB,CAAC,GAAG3B,OAAO,CAACY,MAAM,EAAE,EAAEe,CAAC,EAAE;MAC3C,MAAMqB,SAAS,GAAGC,wBAAwB,CAACjD,OAAO,CAACU,CAAC,CAAC,EAAEV,OAAO,CAAC2B,CAAC,CAAC,CAAC;MAClE,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACpC,MAAM,EAAE,EAAEsC,CAAC,EAAE;QACzC,MAAM5C,CAAC,GAAQ0C,SAAS,CAACE,CAAC,CAAC;QAC3B5C,CAAC,CAACsB,WAAW,GAAG,CAAClB,CAAC,EAAEiB,CAAC,CAAC;QACtBoB,GAAG,CAACP,IAAI,CAAClC,CAAC,CAAC;;;;EAIjB,OAAOyC,GAAG;AACZ;AAEA;AACA,OAAM,SAAUN,UAAUA,CAACU,CAAC,EAAEhB,KAAK;EACjC,OACEgB,CAAC,GAAGA,CAAC,GAAGnC,IAAI,CAACoC,IAAI,CAAC,CAAC,GAAGjB,KAAK,GAAGgB,CAAC,CAAC,GAChC,CAACA,CAAC,GAAGhB,KAAK,IAAInB,IAAI,CAACqC,IAAI,CAAClB,KAAK,IAAI,CAAC,GAAGgB,CAAC,GAAGhB,KAAK,CAAC,CAAC;AAEpD;AAEA;AACA,OAAM,SAAUC,QAAQA,CAACZ,EAAE,EAAED,EAAE;EAC7B,OAAOP,IAAI,CAACqC,IAAI,CACd,CAAC7B,EAAE,CAACN,CAAC,GAAGK,EAAE,CAACL,CAAC,KAAKM,EAAE,CAACN,CAAC,GAAGK,EAAE,CAACL,CAAC,CAAC,GAAG,CAACM,EAAE,CAACL,CAAC,GAAGI,EAAE,CAACJ,CAAC,KAAKK,EAAE,CAACL,CAAC,GAAGI,EAAE,CAACJ,CAAC,CAAC,CAC9D;AACH;AAEA;;;AAGA,OAAM,SAAUmC,aAAaA,CAACC,EAAE,EAAEC,EAAE,EAAEC,CAAC;EACrC;EACA,IAAIA,CAAC,IAAIF,EAAE,GAAGC,EAAE,EAAE;IAChB,OAAO,CAAC;;EAGV;EACA,IAAIC,CAAC,IAAIzC,IAAI,CAAC4B,GAAG,CAACW,EAAE,GAAGC,EAAE,CAAC,EAAE;IAC1B,OAAOxC,IAAI,CAACkB,EAAE,GAAGlB,IAAI,CAAC0C,GAAG,CAACH,EAAE,EAAEC,EAAE,CAAC,GAAGxC,IAAI,CAAC0C,GAAG,CAACH,EAAE,EAAEC,EAAE,CAAC;;EAGtD,MAAMG,EAAE,GAAGJ,EAAE,GAAG,CAACE,CAAC,GAAGA,CAAC,GAAGD,EAAE,GAAGA,EAAE,GAAGD,EAAE,GAAGA,EAAE,KAAK,CAAC,GAAGE,CAAC,CAAC;IACnDG,EAAE,GAAGJ,EAAE,GAAG,CAACC,CAAC,GAAGA,CAAC,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,KAAK,CAAC,GAAGC,CAAC,CAAC;EACjD,OAAOhB,UAAU,CAACc,EAAE,EAAEI,EAAE,CAAC,GAAGlB,UAAU,CAACe,EAAE,EAAEI,EAAE,CAAC;AAChD;AAEA;;;;AAIA,OAAM,SAAUX,wBAAwBA,CAACzB,EAAE,EAAED,EAAE;EAC7C,MAAMkC,CAAC,GAAGrB,QAAQ,CAACZ,EAAE,EAAED,EAAE,CAAC;IACxBgC,EAAE,GAAG/B,EAAE,CAACa,MAAM;IACdmB,EAAE,GAAGjC,EAAE,CAACc,MAAM;EAEhB;EACA,IAAIoB,CAAC,IAAIF,EAAE,GAAGC,EAAE,IAAIC,CAAC,IAAIzC,IAAI,CAAC4B,GAAG,CAACW,EAAE,GAAGC,EAAE,CAAC,EAAE;IAC1C,OAAO,EAAE;;EAGX,MAAMnC,CAAC,GAAG,CAACkC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGA,CAAC,CAAC;IAC7CI,CAAC,GAAG7C,IAAI,CAACqC,IAAI,CAACE,EAAE,GAAGA,EAAE,GAAGlC,CAAC,GAAGA,CAAC,CAAC;IAC9ByC,EAAE,GAAGtC,EAAE,CAACN,CAAC,GAAIG,CAAC,IAAIE,EAAE,CAACL,CAAC,GAAGM,EAAE,CAACN,CAAC,CAAC,GAAIuC,CAAC;IACnCM,EAAE,GAAGvC,EAAE,CAACL,CAAC,GAAIE,CAAC,IAAIE,EAAE,CAACJ,CAAC,GAAGK,EAAE,CAACL,CAAC,CAAC,GAAIsC,CAAC;IACnCO,EAAE,GAAG,EAAEzC,EAAE,CAACJ,CAAC,GAAGK,EAAE,CAACL,CAAC,CAAC,IAAI0C,CAAC,GAAGJ,CAAC,CAAC;IAC7BQ,EAAE,GAAG,EAAE1C,EAAE,CAACL,CAAC,GAAGM,EAAE,CAACN,CAAC,CAAC,IAAI2C,CAAC,GAAGJ,CAAC,CAAC;EAE/B,OAAO,CACL;IAAEvC,CAAC,EAAE4C,EAAE,GAAGE,EAAE;IAAE7C,CAAC,EAAE4C,EAAE,GAAGE;EAAE,CAAE,EAC1B;IAAE/C,CAAC,EAAE4C,EAAE,GAAGE,EAAE;IAAE7C,CAAC,EAAE4C,EAAE,GAAGE;EAAE,CAAE,CAC3B;AACH;AAEA;AACA,OAAM,SAAUnD,SAASA,CAACoD,MAAM;EAC9B,MAAMrD,MAAM,GAAG;IAAEK,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAC,CAAE;EAC7B,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwD,MAAM,CAACtD,MAAM,EAAE,EAAEF,CAAC,EAAE;IACtCG,MAAM,CAACK,CAAC,IAAIgD,MAAM,CAACxD,CAAC,CAAC,CAACQ,CAAC;IACvBL,MAAM,CAACM,CAAC,IAAI+C,MAAM,CAACxD,CAAC,CAAC,CAACS,CAAC;;EAEzBN,MAAM,CAACK,CAAC,IAAIgD,MAAM,CAACtD,MAAM;EACzBC,MAAM,CAACM,CAAC,IAAI+C,MAAM,CAACtD,MAAM;EACzB,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}