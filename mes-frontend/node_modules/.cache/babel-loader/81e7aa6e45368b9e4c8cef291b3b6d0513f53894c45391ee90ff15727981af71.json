{"ast": null, "code": "'use strict';\n\nconst SPLITTER = +(Math.pow(2, 27) + 1.0);\nexport function twoProduct(a, b, result) {\n  const x = a * b;\n  const c = SPLITTER * a;\n  const a_big = c - a;\n  const ahi = c - a_big;\n  const alo = a - ahi;\n  const d = SPLITTER * b;\n  const b_big = d - b;\n  const bhi = d - b_big;\n  const blo = b - bhi;\n  const err1 = x - ahi * bhi;\n  const err2 = err1 - alo * bhi;\n  const err3 = err2 - ahi * blo;\n  const y = alo * blo - err3;\n  if (result) {\n    result[0] = y;\n    result[1] = x;\n    return result;\n  }\n  return [y, x];\n}", "map": {"version": 3, "names": ["SPLITTER", "Math", "pow", "twoProduct", "a", "b", "result", "x", "c", "a_big", "ahi", "alo", "d", "b_big", "bhi", "blo", "err1", "err2", "err3", "y"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g6/src/plugins/hull/hull/two-product.ts"], "sourcesContent": ["'use strict';\n\nconst SPLITTER: number = +(Math.pow(2, 27) + 1.0);\n\nexport function twoProduct(a: number, b: number, result?: [number, number]): [number, number] {\n  const x: number = a * b;\n\n  const c: number = SPLITTER * a;\n  const a_big: number = c - a;\n  const ahi: number = c - a_big;\n  const alo: number = a - ahi;\n\n  const d: number = SPLITTER * b;\n  const b_big: number = d - b;\n  const bhi: number = d - b_big;\n  const blo: number = b - bhi;\n\n  const err1: number = x - ahi * bhi;\n  const err2: number = err1 - alo * bhi;\n  const err3: number = err2 - ahi * blo;\n\n  const y: number = alo * blo - err3;\n\n  if (result) {\n    result[0] = y;\n    result[1] = x;\n    return result;\n  }\n\n  return [y, x];\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,QAAQ,GAAW,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC;AAEjD,OAAM,SAAUC,UAAUA,CAACC,CAAS,EAAEC,CAAS,EAAEC,MAAyB;EACxE,MAAMC,CAAC,GAAWH,CAAC,GAAGC,CAAC;EAEvB,MAAMG,CAAC,GAAWR,QAAQ,GAAGI,CAAC;EAC9B,MAAMK,KAAK,GAAWD,CAAC,GAAGJ,CAAC;EAC3B,MAAMM,GAAG,GAAWF,CAAC,GAAGC,KAAK;EAC7B,MAAME,GAAG,GAAWP,CAAC,GAAGM,GAAG;EAE3B,MAAME,CAAC,GAAWZ,QAAQ,GAAGK,CAAC;EAC9B,MAAMQ,KAAK,GAAWD,CAAC,GAAGP,CAAC;EAC3B,MAAMS,GAAG,GAAWF,CAAC,GAAGC,KAAK;EAC7B,MAAME,GAAG,GAAWV,CAAC,GAAGS,GAAG;EAE3B,MAAME,IAAI,GAAWT,CAAC,GAAGG,GAAG,GAAGI,GAAG;EAClC,MAAMG,IAAI,GAAWD,IAAI,GAAGL,GAAG,GAAGG,GAAG;EACrC,MAAMI,IAAI,GAAWD,IAAI,GAAGP,GAAG,GAAGK,GAAG;EAErC,MAAMI,CAAC,GAAWR,GAAG,GAAGI,GAAG,GAAGG,IAAI;EAElC,IAAIZ,MAAM,EAAE;IACVA,MAAM,CAAC,CAAC,CAAC,GAAGa,CAAC;IACbb,MAAM,CAAC,CAAC,CAAC,GAAGC,CAAC;IACb,OAAOD,MAAM;EACf;EAEA,OAAO,CAACa,CAAC,EAAEZ,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}