{"ast": null, "code": "export { getDefaultStyle as bottomLeft } from './default';", "map": {"version": 3, "names": ["getDefaultStyle", "bottomLeft"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/bottomLeft.ts"], "sourcesContent": ["export { getDefaultStyle as bottomLeft } from './default';\n"], "mappings": "AAAA,SAASA,eAAe,IAAIC,UAAU,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}