{"ast": null, "code": "import { __read } from \"tslib\";\nimport { Marker } from '../marker';\n/**\n * 将值转换至步长tick上\n */\nexport function getStepValueByValue(value, step, min) {\n  var count = Math.round((value - min) / step);\n  return min + count * step;\n}\nexport function hiddenHandle(x, y, r) {\n  // 长宽比\n  var ratio = 1.4;\n  var diffY = ratio * r;\n  return [['M', x - r, y - diffY], ['L', x + r, y - diffY], ['L', x + r, y + diffY], ['L', x - r, y + diffY], ['Z']];\n}\n// 控制手柄\nvar HANDLE_HEIGHT_RATIO = 1.4;\nvar HANDLE_TRIANGLE_RATIO = 0.4;\n// 纵向手柄\nexport function verticalHandle(x, y, r) {\n  var width = r;\n  var height = width * HANDLE_HEIGHT_RATIO;\n  var halfWidth = width / 2;\n  var oneSixthWidth = width / 6;\n  var triangleX = x + height * HANDLE_TRIANGLE_RATIO;\n  return [['M', x, y], ['L', triangleX, y + halfWidth], ['L', x + height, y + halfWidth], ['L', x + height, y - halfWidth], ['L', triangleX, y - halfWidth], ['Z'],\n  // 绘制两条横线\n  ['M', triangleX, y + oneSixthWidth], ['L', x + height - 2, y + oneSixthWidth], ['M', triangleX, y - oneSixthWidth], ['L', x + height - 2, y - oneSixthWidth]];\n}\n// 横向手柄\nexport function horizontalHandle(x, y, r) {\n  var width = r;\n  var height = width * HANDLE_HEIGHT_RATIO;\n  var halfWidth = width / 2;\n  var oneSixthWidth = width / 6;\n  var triangleY = y + height * HANDLE_TRIANGLE_RATIO;\n  return [['M', x, y], ['L', x - halfWidth, triangleY], ['L', x - halfWidth, y + height], ['L', x + halfWidth, y + height], ['L', x + halfWidth, triangleY], ['Z'],\n  // 绘制两条竖线\n  ['M', x - oneSixthWidth, triangleY], ['L', x - oneSixthWidth, y + height - 2], ['M', x + oneSixthWidth, triangleY], ['L', x + oneSixthWidth, y + height - 2]];\n}\nMarker.registerSymbol('hiddenHandle', hiddenHandle);\nMarker.registerSymbol('verticalHandle', verticalHandle);\nMarker.registerSymbol('horizontalHandle', horizontalHandle);\nexport var ifH = function (orientation, a, b) {\n  if (orientation === void 0) {\n    orientation = 'horizontal';\n  }\n  return orientation === 'horizontal' ? a : b;\n};\n// 具体逻辑还没看，@chushen\nexport function getSafetySelections(domain, newSelection, oldSelection, precision) {\n  var _a;\n  if (precision === void 0) {\n    precision = 4;\n  }\n  var _b = __read(domain, 2),\n    min = _b[0],\n    max = _b[1];\n  var _c = __read(newSelection, 2),\n    start = _c[0],\n    end = _c[1];\n  var _d = __read(oldSelection, 2),\n    prevStart = _d[0],\n    prevEnd = _d[1];\n  var _e = __read([start, end], 2),\n    startVal = _e[0],\n    endVal = _e[1];\n  var range = endVal - startVal;\n  // 交换startVal endVal\n  if (startVal > endVal) {\n    _a = __read([endVal, startVal], 2), startVal = _a[0], endVal = _a[1];\n  }\n  // 超出范围就全选\n  if (range > max - min) {\n    return [min, max];\n  }\n  if (startVal < min) {\n    if (prevStart === min && prevEnd === endVal) {\n      return [min, endVal];\n    }\n    return [min, range + min];\n  }\n  if (endVal > max) {\n    if (prevEnd === max && prevStart === startVal) {\n      return [startVal, max];\n    }\n    return [max - range, max];\n  }\n  // 保留小数\n  return [startVal, endVal];\n}\nexport function ifHorizontal(orientation, a, b) {\n  if (orientation === void 0) {\n    orientation = 'horizontal';\n  }\n  return orientation === 'horizontal' ? a : b;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "getStepValueByValue", "value", "step", "min", "count", "Math", "round", "hiddenH<PERSON>le", "x", "y", "r", "ratio", "diffY", "HANDLE_HEIGHT_RATIO", "HANDLE_TRIANGLE_RATIO", "verticalHandle", "width", "height", "halfWidth", "oneSixthWidth", "triangleX", "horizontalHandle", "triangleY", "registerSymbol", "ifH", "orientation", "a", "b", "getSafetySelections", "domain", "newSelection", "oldSelection", "precision", "_b", "__read", "max", "_c", "start", "end", "_d", "prevStart", "prevEnd", "_e", "startVal", "endVal", "range", "_a", "ifHorizontal"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/legend/utils.ts"], "sourcesContent": ["import { Marker } from '../marker';\n\n/**\n * 将值转换至步长tick上\n */\nexport function getStepValueByValue(value: number, step: number, min: number) {\n  const count = Math.round((value - min) / step);\n  return min + count * step;\n}\n\nexport function hiddenHandle(x: number, y: number, r: number) {\n  // 长宽比\n  const ratio = 1.4;\n  const diffY = ratio * r;\n  return [['M', x - r, y - diffY], ['L', x + r, y - diffY], ['L', x + r, y + diffY], ['L', x - r, y + diffY], ['Z']];\n}\n\n// 控制手柄\nconst HANDLE_HEIGHT_RATIO = 1.4;\nconst HANDLE_TRIANGLE_RATIO = 0.4;\n\n// 纵向手柄\nexport function verticalHandle(x: number, y: number, r: number) {\n  const width = r;\n  const height = width * HANDLE_HEIGHT_RATIO;\n  const halfWidth = width / 2;\n  const oneSixthWidth = width / 6;\n  const triangleX = x + height * HANDLE_TRIANGLE_RATIO;\n  return [\n    ['M', x, y],\n    ['L', triangleX, y + halfWidth],\n    ['L', x + height, y + halfWidth],\n    ['L', x + height, y - halfWidth],\n    ['L', triangleX, y - halfWidth],\n    ['Z'],\n    // 绘制两条横线\n    ['M', triangleX, y + oneSixthWidth],\n    ['L', x + height - 2, y + oneSixthWidth],\n    ['M', triangleX, y - oneSixthWidth],\n    ['L', x + height - 2, y - oneSixthWidth],\n  ];\n}\n\n// 横向手柄\nexport function horizontalHandle(x: number, y: number, r: number) {\n  const width = r;\n  const height = width * HANDLE_HEIGHT_RATIO;\n  const halfWidth = width / 2;\n  const oneSixthWidth = width / 6;\n  const triangleY = y + height * HANDLE_TRIANGLE_RATIO;\n  return [\n    ['M', x, y],\n    ['L', x - halfWidth, triangleY],\n    ['L', x - halfWidth, y + height],\n    ['L', x + halfWidth, y + height],\n    ['L', x + halfWidth, triangleY],\n    ['Z'],\n    // 绘制两条竖线\n    ['M', x - oneSixthWidth, triangleY],\n    ['L', x - oneSixthWidth, y + height - 2],\n    ['M', x + oneSixthWidth, triangleY],\n    ['L', x + oneSixthWidth, y + height - 2],\n  ];\n}\n\nMarker.registerSymbol('hiddenHandle', hiddenHandle);\nMarker.registerSymbol('verticalHandle', verticalHandle);\nMarker.registerSymbol('horizontalHandle', horizontalHandle);\n\nexport const ifH = (orientation = 'horizontal', a: any, b: any) => (orientation === 'horizontal' ? a : b);\n\n// 具体逻辑还没看，@chushen\nexport function getSafetySelections(\n  domain: [number, number],\n  newSelection: [number, number],\n  oldSelection: [number, number],\n  precision: number = 4\n) {\n  const [min, max] = domain;\n  const [start, end] = newSelection;\n  const [prevStart, prevEnd] = oldSelection;\n  let [startVal, endVal] = [start, end];\n  const range = endVal - startVal;\n  // 交换startVal endVal\n  if (startVal > endVal) {\n    [startVal, endVal] = [endVal, startVal];\n  }\n  // 超出范围就全选\n  if (range > max - min) {\n    return [min, max];\n  }\n\n  if (startVal < min) {\n    if (prevStart === min && prevEnd === endVal) {\n      return [min, endVal];\n    }\n    return [min, range + min];\n  }\n  if (endVal > max) {\n    if (prevEnd === max && prevStart === startVal) {\n      return [startVal, max];\n    }\n    return [max - range, max];\n  }\n\n  // 保留小数\n  return [startVal, endVal];\n}\n\nexport function ifHorizontal<T>(orientation: string = 'horizontal', a: T, b: T): T {\n  return orientation === 'horizontal' ? a : b;\n}\n"], "mappings": ";AAAA,SAASA,MAAM,QAAQ,WAAW;AAElC;;;AAGA,OAAM,SAAUC,mBAAmBA,CAACC,KAAa,EAAEC,IAAY,EAAEC,GAAW;EAC1E,IAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,KAAK,GAAGE,GAAG,IAAID,IAAI,CAAC;EAC9C,OAAOC,GAAG,GAAGC,KAAK,GAAGF,IAAI;AAC3B;AAEA,OAAM,SAAUK,YAAYA,CAACC,CAAS,EAAEC,CAAS,EAAEC,CAAS;EAC1D;EACA,IAAMC,KAAK,GAAG,GAAG;EACjB,IAAMC,KAAK,GAAGD,KAAK,GAAGD,CAAC;EACvB,OAAO,CAAC,CAAC,GAAG,EAAEF,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGG,KAAK,CAAC,EAAE,CAAC,GAAG,EAAEJ,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGG,KAAK,CAAC,EAAE,CAAC,GAAG,EAAEJ,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGG,KAAK,CAAC,EAAE,CAAC,GAAG,EAAEJ,CAAC,GAAGE,CAAC,EAAED,CAAC,GAAGG,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACpH;AAEA;AACA,IAAMC,mBAAmB,GAAG,GAAG;AAC/B,IAAMC,qBAAqB,GAAG,GAAG;AAEjC;AACA,OAAM,SAAUC,cAAcA,CAACP,CAAS,EAAEC,CAAS,EAAEC,CAAS;EAC5D,IAAMM,KAAK,GAAGN,CAAC;EACf,IAAMO,MAAM,GAAGD,KAAK,GAAGH,mBAAmB;EAC1C,IAAMK,SAAS,GAAGF,KAAK,GAAG,CAAC;EAC3B,IAAMG,aAAa,GAAGH,KAAK,GAAG,CAAC;EAC/B,IAAMI,SAAS,GAAGZ,CAAC,GAAGS,MAAM,GAAGH,qBAAqB;EACpD,OAAO,CACL,CAAC,GAAG,EAAEN,CAAC,EAAEC,CAAC,CAAC,EACX,CAAC,GAAG,EAAEW,SAAS,EAAEX,CAAC,GAAGS,SAAS,CAAC,EAC/B,CAAC,GAAG,EAAEV,CAAC,GAAGS,MAAM,EAAER,CAAC,GAAGS,SAAS,CAAC,EAChC,CAAC,GAAG,EAAEV,CAAC,GAAGS,MAAM,EAAER,CAAC,GAAGS,SAAS,CAAC,EAChC,CAAC,GAAG,EAAEE,SAAS,EAAEX,CAAC,GAAGS,SAAS,CAAC,EAC/B,CAAC,GAAG,CAAC;EACL;EACA,CAAC,GAAG,EAAEE,SAAS,EAAEX,CAAC,GAAGU,aAAa,CAAC,EACnC,CAAC,GAAG,EAAEX,CAAC,GAAGS,MAAM,GAAG,CAAC,EAAER,CAAC,GAAGU,aAAa,CAAC,EACxC,CAAC,GAAG,EAAEC,SAAS,EAAEX,CAAC,GAAGU,aAAa,CAAC,EACnC,CAAC,GAAG,EAAEX,CAAC,GAAGS,MAAM,GAAG,CAAC,EAAER,CAAC,GAAGU,aAAa,CAAC,CACzC;AACH;AAEA;AACA,OAAM,SAAUE,gBAAgBA,CAACb,CAAS,EAAEC,CAAS,EAAEC,CAAS;EAC9D,IAAMM,KAAK,GAAGN,CAAC;EACf,IAAMO,MAAM,GAAGD,KAAK,GAAGH,mBAAmB;EAC1C,IAAMK,SAAS,GAAGF,KAAK,GAAG,CAAC;EAC3B,IAAMG,aAAa,GAAGH,KAAK,GAAG,CAAC;EAC/B,IAAMM,SAAS,GAAGb,CAAC,GAAGQ,MAAM,GAAGH,qBAAqB;EACpD,OAAO,CACL,CAAC,GAAG,EAAEN,CAAC,EAAEC,CAAC,CAAC,EACX,CAAC,GAAG,EAAED,CAAC,GAAGU,SAAS,EAAEI,SAAS,CAAC,EAC/B,CAAC,GAAG,EAAEd,CAAC,GAAGU,SAAS,EAAET,CAAC,GAAGQ,MAAM,CAAC,EAChC,CAAC,GAAG,EAAET,CAAC,GAAGU,SAAS,EAAET,CAAC,GAAGQ,MAAM,CAAC,EAChC,CAAC,GAAG,EAAET,CAAC,GAAGU,SAAS,EAAEI,SAAS,CAAC,EAC/B,CAAC,GAAG,CAAC;EACL;EACA,CAAC,GAAG,EAAEd,CAAC,GAAGW,aAAa,EAAEG,SAAS,CAAC,EACnC,CAAC,GAAG,EAAEd,CAAC,GAAGW,aAAa,EAAEV,CAAC,GAAGQ,MAAM,GAAG,CAAC,CAAC,EACxC,CAAC,GAAG,EAAET,CAAC,GAAGW,aAAa,EAAEG,SAAS,CAAC,EACnC,CAAC,GAAG,EAAEd,CAAC,GAAGW,aAAa,EAAEV,CAAC,GAAGQ,MAAM,GAAG,CAAC,CAAC,CACzC;AACH;AAEAlB,MAAM,CAACwB,cAAc,CAAC,cAAc,EAAEhB,YAAY,CAAC;AACnDR,MAAM,CAACwB,cAAc,CAAC,gBAAgB,EAAER,cAAc,CAAC;AACvDhB,MAAM,CAACwB,cAAc,CAAC,kBAAkB,EAAEF,gBAAgB,CAAC;AAE3D,OAAO,IAAMG,GAAG,GAAG,SAAAA,CAACC,WAA0B,EAAEC,CAAM,EAAEC,CAAM;EAA1C,IAAAF,WAAA;IAAAA,WAAA,eAA0B;EAAA;EAAqB,OAACA,WAAW,KAAK,YAAY,GAAGC,CAAC,GAAGC,CAAC;AAArC,CAAsC;AAEzG;AACA,OAAM,SAAUC,mBAAmBA,CACjCC,MAAwB,EACxBC,YAA8B,EAC9BC,YAA8B,EAC9BC,SAAqB;;EAArB,IAAAA,SAAA;IAAAA,SAAA,IAAqB;EAAA;EAEf,IAAAC,EAAA,GAAAC,MAAA,CAAaL,MAAM;IAAlB1B,GAAG,GAAA8B,EAAA;IAAEE,GAAG,GAAAF,EAAA,GAAU;EACnB,IAAAG,EAAA,GAAAF,MAAA,CAAeJ,YAAY;IAA1BO,KAAK,GAAAD,EAAA;IAAEE,GAAG,GAAAF,EAAA,GAAgB;EAC3B,IAAAG,EAAA,GAAAL,MAAA,CAAuBH,YAAY;IAAlCS,SAAS,GAAAD,EAAA;IAAEE,OAAO,GAAAF,EAAA,GAAgB;EACrC,IAAAG,EAAA,GAAAR,MAAA,CAAqB,CAACG,KAAK,EAAEC,GAAG,CAAC;IAAhCK,QAAQ,GAAAD,EAAA;IAAEE,MAAM,GAAAF,EAAA,GAAgB;EACrC,IAAMG,KAAK,GAAGD,MAAM,GAAGD,QAAQ;EAC/B;EACA,IAAIA,QAAQ,GAAGC,MAAM,EAAE;IACrBE,EAAA,GAAAZ,MAAA,CAAqB,CAACU,MAAM,EAAED,QAAQ,CAAC,MAAtCA,QAAQ,GAAAG,EAAA,KAAEF,MAAM,GAAAE,EAAA;EACnB;EACA;EACA,IAAID,KAAK,GAAGV,GAAG,GAAGhC,GAAG,EAAE;IACrB,OAAO,CAACA,GAAG,EAAEgC,GAAG,CAAC;EACnB;EAEA,IAAIQ,QAAQ,GAAGxC,GAAG,EAAE;IAClB,IAAIqC,SAAS,KAAKrC,GAAG,IAAIsC,OAAO,KAAKG,MAAM,EAAE;MAC3C,OAAO,CAACzC,GAAG,EAAEyC,MAAM,CAAC;IACtB;IACA,OAAO,CAACzC,GAAG,EAAE0C,KAAK,GAAG1C,GAAG,CAAC;EAC3B;EACA,IAAIyC,MAAM,GAAGT,GAAG,EAAE;IAChB,IAAIM,OAAO,KAAKN,GAAG,IAAIK,SAAS,KAAKG,QAAQ,EAAE;MAC7C,OAAO,CAACA,QAAQ,EAAER,GAAG,CAAC;IACxB;IACA,OAAO,CAACA,GAAG,GAAGU,KAAK,EAAEV,GAAG,CAAC;EAC3B;EAEA;EACA,OAAO,CAACQ,QAAQ,EAAEC,MAAM,CAAC;AAC3B;AAEA,OAAM,SAAUG,YAAYA,CAAItB,WAAkC,EAAEC,CAAI,EAAEC,CAAI;EAA9C,IAAAF,WAAA;IAAAA,WAAA,eAAkC;EAAA;EAChE,OAAOA,WAAW,KAAK,YAAY,GAAGC,CAAC,GAAGC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}