{"ast": null, "code": "import { __assign, __extends, __read, __rest, __spreadArray } from \"tslib\";\nimport { ElementEvent } from '@antv/g';\nimport { Component } from '../../core';\nimport { Group } from '../../shapes';\nimport { BBox, classNames, hide, isHorizontal, parseSeriesAttr, renderExtDo, select, show, splitStyle, subStyleProps } from '../../util';\nimport { DEFAULT_INDICATOR_STYLE_PROPS } from './constant';\nvar CLASS_NAMES = classNames({\n  background: 'background',\n  labelGroup: 'label-group',\n  label: 'label'\n}, 'indicator');\nvar Indicator = /** @class */function (_super) {\n  __extends(Indicator, _super);\n  function Indicator(options) {\n    var _this = _super.call(this, options, DEFAULT_INDICATOR_STYLE_PROPS) || this;\n    _this.point = [0, 0];\n    _this.group = _this.appendChild(new Group({}));\n    _this.isMutationObserved = true;\n    return _this;\n  }\n  Indicator.prototype.renderBackground = function () {\n    if (!this.label) return;\n    var _a = this.attributes,\n      position = _a.position,\n      padding = _a.padding;\n    var _b = __read(parseSeriesAttr(padding), 4),\n      t = _b[0],\n      r = _b[1],\n      b = _b[2],\n      l = _b[3];\n    var _c = this.label.node().getLocalBounds(),\n      min = _c.min,\n      max = _c.max;\n    var bbox = new BBox(min[0] - l, min[1] - t, max[0] + r - min[0] + l, max[1] + b - min[1] + t);\n    var path = this.getPath(position, bbox);\n    var style = subStyleProps(this.attributes, 'background');\n    this.background = select(this.group).maybeAppendByClassName(CLASS_NAMES.background, 'path').styles(__assign(__assign({}, style), {\n      d: path\n    }));\n    this.group.appendChild(this.label.node());\n  };\n  Indicator.prototype.renderLabel = function () {\n    var _a = this.attributes,\n      formatter = _a.formatter,\n      labelText = _a.labelText;\n    var style = subStyleProps(this.attributes, 'label');\n    var _b = __read(splitStyle(style), 2),\n      _c = _b[0],\n      groupStyle = _b[1],\n      rawText = _c.text,\n      textStyle = __rest(_c, [\"text\"]);\n    this.label = select(this.group).maybeAppendByClassName(CLASS_NAMES.labelGroup, 'g').styles(groupStyle);\n    if (!labelText) return;\n    var text = this.label.maybeAppendByClassName(CLASS_NAMES.label, function () {\n      return renderExtDo(formatter(labelText));\n    }).style('text', formatter(labelText).toString());\n    text.selectAll('text').styles(textStyle);\n  };\n  Indicator.prototype.adjustLayout = function () {\n    var _a = __read(this.point, 2),\n      dx = _a[0],\n      dy = _a[1];\n    var _b = this.attributes,\n      x = _b.x,\n      y = _b.y;\n    this.group.attr('transform', \"translate(\".concat(x - dx, \", \").concat(y - dy, \")\"));\n  };\n  Indicator.prototype.getPath = function (position, bbox) {\n    var r = this.attributes.radius;\n    var x = bbox.x,\n      y = bbox.y,\n      width = bbox.width,\n      height = bbox.height;\n    var pathArray = [\n    // 0 开始路径\n    ['M', x + r, y],\n    // 1 上边线\n    ['L', x + width - r, y],\n    // 2 右上角圆弧\n    ['A', r, r, 0, 0, 1, x + width, y + r],\n    // 3 右边线\n    ['L', x + width, y + height - r],\n    // 4 右下角圆弧\n    ['A', r, r, 0, 0, 1, x + width - r, y + height],\n    // 5 下边线\n    ['L', x + r, y + height],\n    // 6 左下角圆弧\n    ['A', r, r, 0, 0, 1, x, y + height - r],\n    // 7 左边线\n    ['L', x, y + r],\n    // 8 左上角圆弧\n    ['A', r, r, 0, 0, 1, x + r, y],\n    // 9 关闭路径\n    ['Z']];\n    // 将 position 反方向的边线替换为带尖角的边线\n    var revertPositionMap = {\n      top: 4,\n      right: 6,\n      bottom: 0,\n      left: 2\n    };\n    var index = revertPositionMap[position];\n    var newPath = this.createCorner([pathArray[index].slice(-2), pathArray[index + 1].slice(-2)]);\n    // 替换\n    pathArray.splice.apply(pathArray, __spreadArray([index + 1, 1], __read(newPath), false));\n    pathArray[0][0] = 'M';\n    return pathArray;\n  };\n  Indicator.prototype.createCorner = function (edge, size) {\n    if (size === void 0) {\n      size = 10;\n    }\n    // intrinsic parameter\n    var cornerScale = 0.8;\n    var isH = isHorizontal.apply(void 0, __spreadArray([], __read(edge), false));\n    var _a = __read(edge, 2),\n      _b = __read(_a[0], 2),\n      x0 = _b[0],\n      y0 = _b[1],\n      _c = __read(_a[1], 2),\n      x1 = _c[0],\n      y1 = _c[1];\n    var _d = __read(isH ? [x1 - x0, [x0, x1]] : [y1 - y0, [y0, y1]], 2),\n      len = _d[0],\n      _e = __read(_d[1], 2),\n      b0 = _e[0],\n      b1 = _e[1];\n    var hL = len / 2;\n    var sign = len / Math.abs(len);\n    var cL = size * sign;\n    var hCL = cL / 2;\n    var cS = cL * Math.sqrt(3) / 2 * cornerScale;\n    var _f = __read([b0, b0 + hL - hCL, b0 + hL, b0 + hL + hCL, b1], 5),\n      a0 = _f[0],\n      a1 = _f[1],\n      a2 = _f[2],\n      a3 = _f[3],\n      a4 = _f[4];\n    if (isH) {\n      this.point = [a2, y0 - cS];\n      return [['L', a0, y0], ['L', a1, y0], ['L', a2, y0 - cS], ['L', a3, y0], ['L', a4, y0]];\n    }\n    this.point = [x0 + cS, a2];\n    return [['L', x0, a0], ['L', x0, a1], ['L', x0 + cS, a2], ['L', x0, a3], ['L', x0, a4]];\n  };\n  Indicator.prototype.applyVisibility = function () {\n    var visibility = this.attributes.visibility;\n    if (visibility === 'hidden') hide(this);else show(this);\n  };\n  Indicator.prototype.bindEvents = function () {\n    this.label.on(ElementEvent.BOUNDS_CHANGED, this.renderBackground);\n  };\n  Indicator.prototype.render = function () {\n    this.renderLabel();\n    this.renderBackground();\n    this.adjustLayout();\n    this.applyVisibility();\n  };\n  return Indicator;\n}(Component);\nexport { Indicator };", "map": {"version": 3, "names": ["ElementEvent", "Component", "Group", "BBox", "classNames", "hide", "isHorizontal", "parseSeriesAttr", "renderExtDo", "select", "show", "splitStyle", "subStyleProps", "DEFAULT_INDICATOR_STYLE_PROPS", "CLASS_NAMES", "background", "labelGroup", "label", "Indicator", "_super", "__extends", "options", "_this", "call", "point", "group", "append<PERSON><PERSON><PERSON>", "isMutationObserved", "prototype", "renderBackground", "_a", "attributes", "position", "padding", "_b", "__read", "t", "r", "b", "l", "_c", "node", "getLocalBounds", "min", "max", "bbox", "path", "<PERSON><PERSON><PERSON>", "style", "maybeAppendByClassName", "styles", "__assign", "d", "renderLabel", "formatter", "labelText", "groupStyle", "rawText", "text", "textStyle", "__rest", "toString", "selectAll", "adjustLayout", "dx", "dy", "x", "y", "attr", "concat", "radius", "width", "height", "pathArray", "revertPositionMap", "top", "right", "bottom", "left", "index", "newPath", "create<PERSON>orner", "slice", "splice", "apply", "__spread<PERSON><PERSON>y", "edge", "size", "cornerScale", "isH", "x0", "y0", "x1", "y1", "_d", "len", "_e", "b0", "b1", "hL", "sign", "Math", "abs", "cL", "hCL", "cS", "sqrt", "_f", "a0", "a1", "a2", "a3", "a4", "applyVisibility", "visibility", "bindEvents", "on", "BOUNDS_CHANGED", "render"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/indicator/indicator.ts"], "sourcesContent": ["import { ElementEvent } from '@antv/g';\nimport type { PathArray } from '@antv/util';\nimport { Component } from '../../core';\nimport { Group } from '../../shapes';\nimport type { Point } from '../../types';\nimport {\n  BBox,\n  Selection,\n  classNames,\n  hide,\n  isHorizontal,\n  parseSeriesAttr,\n  renderExtDo,\n  select,\n  show,\n  splitStyle,\n  subStyleProps,\n} from '../../util';\nimport { DEFAULT_INDICATOR_STYLE_PROPS } from './constant';\nimport type { IndicatorOptions, IndicatorStyleProps, Position } from './types';\n\nexport type { IndicatorOptions, IndicatorStyleProps };\n\ntype Edge = [Point, Point];\n\nconst CLASS_NAMES = classNames(\n  {\n    background: 'background',\n    labelGroup: 'label-group',\n    label: 'label',\n  },\n  'indicator'\n);\nexport class Indicator extends Component<IndicatorStyleProps> {\n  constructor(options: IndicatorOptions) {\n    super(options, DEFAULT_INDICATOR_STYLE_PROPS);\n    this.group = this.appendChild(new Group({}));\n    this.isMutationObserved = true;\n  }\n\n  private group: Group;\n\n  private background!: Selection;\n\n  private label!: Selection;\n\n  private point: Point = [0, 0];\n\n  private renderBackground() {\n    if (!this.label) return;\n    const { position, padding } = this.attributes;\n    const [t, r, b, l] = parseSeriesAttr(padding);\n    const { min, max } = this.label.node().getLocalBounds();\n    const bbox: BBox = new BBox(min[0] - l, min[1] - t, max[0] + r - min[0] + l, max[1] + b - min[1] + t);\n    const path = this.getPath(position, bbox);\n\n    const style = subStyleProps(this.attributes, 'background');\n\n    this.background = select(this.group)\n      .maybeAppendByClassName(CLASS_NAMES.background, 'path')\n      .styles({ ...style, d: path });\n    this.group.appendChild(this.label.node());\n  }\n\n  private renderLabel() {\n    const { formatter, labelText } = this.attributes;\n\n    const style = subStyleProps(this.attributes, 'label');\n    const [{ text: rawText, ...textStyle }, groupStyle] = splitStyle(style);\n\n    this.label = select(this.group).maybeAppendByClassName(CLASS_NAMES.labelGroup, 'g').styles(groupStyle);\n    if (!labelText) return;\n    const text = this.label\n      .maybeAppendByClassName(CLASS_NAMES.label, () => renderExtDo(formatter(labelText)))\n      .style('text', formatter(labelText).toString());\n    text.selectAll('text').styles(textStyle);\n  }\n\n  private adjustLayout() {\n    const [dx, dy] = this.point;\n    const { x, y } = this.attributes;\n    this.group.attr('transform', `translate(${x - dx}, ${y - dy})`);\n  }\n\n  private getPath(position: Position, bbox: BBox) {\n    const { radius: r } = this.attributes;\n    const { x, y, width, height } = bbox;\n\n    const pathArray: PathArray = [\n      // 0 开始路径\n      ['M', x + r, y],\n      // 1 上边线\n      ['L', x + width - r, y],\n      // 2 右上角圆弧\n      ['A', r, r, 0, 0, 1, x + width, y + r],\n      // 3 右边线\n      ['L', x + width, y + height - r],\n      // 4 右下角圆弧\n      ['A', r, r, 0, 0, 1, x + width - r, y + height],\n      // 5 下边线\n      ['L', x + r, y + height],\n      // 6 左下角圆弧\n      ['A', r, r, 0, 0, 1, x, y + height - r],\n      // 7 左边线\n      ['L', x, y + r],\n      // 8 左上角圆弧\n      ['A', r, r, 0, 0, 1, x + r, y],\n      // 9 关闭路径\n      ['Z'],\n    ];\n\n    // 将 position 反方向的边线替换为带尖角的边线\n    const revertPositionMap = { top: 4, right: 6, bottom: 0, left: 2 };\n    const index = revertPositionMap[position];\n    const newPath: any = this.createCorner([pathArray[index].slice(-2) as any, pathArray[index + 1].slice(-2) as any]);\n    // 替换\n    pathArray.splice(index + 1, 1, ...newPath);\n    pathArray[0][0] = 'M';\n    return pathArray;\n  }\n\n  private createCorner(edge: Edge, size: number = 10) {\n    // intrinsic parameter\n    const cornerScale = 0.8;\n    const isH = isHorizontal(...edge);\n    const [[x0, y0], [x1, y1]] = edge;\n    const [len, [b0, b1]] = isH ? [x1 - x0, [x0, x1]] : [y1 - y0, [y0, y1]];\n    const hL = len / 2;\n    const sign = len / Math.abs(len);\n    const cL = size * sign;\n    const hCL = cL / 2;\n    const cS = ((cL * Math.sqrt(3)) / 2) * cornerScale;\n    const [a0, a1, a2, a3, a4] = [b0, b0 + hL - hCL, b0 + hL, b0 + hL + hCL, b1];\n\n    if (isH) {\n      this.point = [a2, y0 - cS];\n      return [\n        ['L', a0, y0],\n        ['L', a1, y0],\n        ['L', a2, y0 - cS],\n        ['L', a3, y0],\n        ['L', a4, y0],\n      ];\n    }\n    this.point = [x0 + cS, a2];\n    return [\n      ['L', x0, a0],\n      ['L', x0, a1],\n      ['L', x0 + cS, a2],\n      ['L', x0, a3],\n      ['L', x0, a4],\n    ];\n  }\n\n  private applyVisibility() {\n    const { visibility } = this.attributes;\n    if (visibility === 'hidden') hide(this);\n    else show(this);\n  }\n\n  public bindEvents() {\n    this.label.on(ElementEvent.BOUNDS_CHANGED, this.renderBackground);\n  }\n\n  public render() {\n    this.renderLabel();\n    this.renderBackground();\n    this.adjustLayout();\n    this.applyVisibility();\n  }\n}\n"], "mappings": ";AAAA,SAASA,YAAY,QAAQ,SAAS;AAEtC,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,KAAK,QAAQ,cAAc;AAEpC,SACEC,IAAI,EAEJC,UAAU,EACVC,IAAI,EACJC,YAAY,EACZC,eAAe,EACfC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,aAAa,QACR,YAAY;AACnB,SAASC,6BAA6B,QAAQ,YAAY;AAO1D,IAAMC,WAAW,GAAGV,UAAU,CAC5B;EACEW,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,aAAa;EACzBC,KAAK,EAAE;CACR,EACD,WAAW,CACZ;AACD,IAAAC,SAAA,0BAAAC,MAAA;EAA+BC,SAAA,CAAAF,SAAA,EAAAC,MAAA;EAC7B,SAAAD,UAAYG,OAAyB;IACnC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACF,OAAO,EAAER,6BAA6B,CAAC;IAWvCS,KAAA,CAAAE,KAAK,GAAU,CAAC,CAAC,EAAE,CAAC,CAAC;IAV3BF,KAAI,CAACG,KAAK,GAAGH,KAAI,CAACI,WAAW,CAAC,IAAIxB,KAAK,CAAC,EAAE,CAAC,CAAC;IAC5CoB,KAAI,CAACK,kBAAkB,GAAG,IAAI;;EAChC;EAUQT,SAAA,CAAAU,SAAA,CAAAC,gBAAgB,GAAxB;IACE,IAAI,CAAC,IAAI,CAACZ,KAAK,EAAE;IACX,IAAAa,EAAA,GAAwB,IAAI,CAACC,UAAU;MAArCC,QAAQ,GAAAF,EAAA,CAAAE,QAAA;MAAEC,OAAO,GAAAH,EAAA,CAAAG,OAAoB;IACvC,IAAAC,EAAA,GAAAC,MAAA,CAAe5B,eAAe,CAAC0B,OAAO,CAAC;MAAtCG,CAAC,GAAAF,EAAA;MAAEG,CAAC,GAAAH,EAAA;MAAEI,CAAC,GAAAJ,EAAA;MAAEK,CAAC,GAAAL,EAAA,GAA4B;IACvC,IAAAM,EAAA,GAAe,IAAI,CAACvB,KAAK,CAACwB,IAAI,EAAE,CAACC,cAAc,EAAE;MAA/CC,GAAG,GAAAH,EAAA,CAAAG,GAAA;MAAEC,GAAG,GAAAJ,EAAA,CAAAI,GAAuC;IACvD,IAAMC,IAAI,GAAS,IAAI1C,IAAI,CAACwC,GAAG,CAAC,CAAC,CAAC,GAAGJ,CAAC,EAAEI,GAAG,CAAC,CAAC,CAAC,GAAGP,CAAC,EAAEQ,GAAG,CAAC,CAAC,CAAC,GAAGP,CAAC,GAAGM,GAAG,CAAC,CAAC,CAAC,GAAGJ,CAAC,EAAEK,GAAG,CAAC,CAAC,CAAC,GAAGN,CAAC,GAAGK,GAAG,CAAC,CAAC,CAAC,GAAGP,CAAC,CAAC;IACrG,IAAMU,IAAI,GAAG,IAAI,CAACC,OAAO,CAACf,QAAQ,EAAEa,IAAI,CAAC;IAEzC,IAAMG,KAAK,GAAGpC,aAAa,CAAC,IAAI,CAACmB,UAAU,EAAE,YAAY,CAAC;IAE1D,IAAI,CAAChB,UAAU,GAAGN,MAAM,CAAC,IAAI,CAACgB,KAAK,CAAC,CACjCwB,sBAAsB,CAACnC,WAAW,CAACC,UAAU,EAAE,MAAM,CAAC,CACtDmC,MAAM,CAAAC,QAAA,CAAAA,QAAA,KAAMH,KAAK;MAAEI,CAAC,EAAEN;IAAI,GAAG;IAChC,IAAI,CAACrB,KAAK,CAACC,WAAW,CAAC,IAAI,CAACT,KAAK,CAACwB,IAAI,EAAE,CAAC;EAC3C,CAAC;EAEOvB,SAAA,CAAAU,SAAA,CAAAyB,WAAW,GAAnB;IACQ,IAAAvB,EAAA,GAA2B,IAAI,CAACC,UAAU;MAAxCuB,SAAS,GAAAxB,EAAA,CAAAwB,SAAA;MAAEC,SAAS,GAAAzB,EAAA,CAAAyB,SAAoB;IAEhD,IAAMP,KAAK,GAAGpC,aAAa,CAAC,IAAI,CAACmB,UAAU,EAAE,OAAO,CAAC;IAC/C,IAAAG,EAAA,GAAAC,MAAA,CAAgDxB,UAAU,CAACqC,KAAK,CAAC;MAAAR,EAAA,GAAAN,EAAA;MAA/BsB,UAAU,GAAAtB,EAAA;MAAnCuB,OAAO,GAAAjB,EAAA,CAAAkB,IAAA;MAAKC,SAAS,GAAAC,MAAA,CAAApB,EAAA,EAA7B,QAA+B,CAAiC;IAEvE,IAAI,CAACvB,KAAK,GAAGR,MAAM,CAAC,IAAI,CAACgB,KAAK,CAAC,CAACwB,sBAAsB,CAACnC,WAAW,CAACE,UAAU,EAAE,GAAG,CAAC,CAACkC,MAAM,CAACM,UAAU,CAAC;IACtG,IAAI,CAACD,SAAS,EAAE;IAChB,IAAMG,IAAI,GAAG,IAAI,CAACzC,KAAK,CACpBgC,sBAAsB,CAACnC,WAAW,CAACG,KAAK,EAAE;MAAM,OAAAT,WAAW,CAAC8C,SAAS,CAACC,SAAS,CAAC,CAAC;IAAjC,CAAiC,CAAC,CAClFP,KAAK,CAAC,MAAM,EAAEM,SAAS,CAACC,SAAS,CAAC,CAACM,QAAQ,EAAE,CAAC;IACjDH,IAAI,CAACI,SAAS,CAAC,MAAM,CAAC,CAACZ,MAAM,CAACS,SAAS,CAAC;EAC1C,CAAC;EAEOzC,SAAA,CAAAU,SAAA,CAAAmC,YAAY,GAApB;IACQ,IAAAjC,EAAA,GAAAK,MAAA,CAAW,IAAI,CAACX,KAAK;MAApBwC,EAAE,GAAAlC,EAAA;MAAEmC,EAAE,GAAAnC,EAAA,GAAc;IACrB,IAAAI,EAAA,GAAW,IAAI,CAACH,UAAU;MAAxBmC,CAAC,GAAAhC,EAAA,CAAAgC,CAAA;MAAEC,CAAC,GAAAjC,EAAA,CAAAiC,CAAoB;IAChC,IAAI,CAAC1C,KAAK,CAAC2C,IAAI,CAAC,WAAW,EAAE,aAAAC,MAAA,CAAaH,CAAC,GAAGF,EAAE,QAAAK,MAAA,CAAKF,CAAC,GAAGF,EAAE,MAAG,CAAC;EACjE,CAAC;EAEO/C,SAAA,CAAAU,SAAA,CAAAmB,OAAO,GAAf,UAAgBf,QAAkB,EAAEa,IAAU;IACpC,IAAQR,CAAC,GAAK,IAAI,CAACN,UAAU,CAAAuC,MAApB;IACT,IAAAJ,CAAC,GAAuBrB,IAAI,CAAAqB,CAA3B;MAAEC,CAAC,GAAoBtB,IAAI,CAAAsB,CAAxB;MAAEI,KAAK,GAAa1B,IAAI,CAAA0B,KAAjB;MAAEC,MAAM,GAAK3B,IAAI,CAAA2B,MAAT;IAE3B,IAAMC,SAAS,GAAc;IAC3B;IACA,CAAC,GAAG,EAAEP,CAAC,GAAG7B,CAAC,EAAE8B,CAAC,CAAC;IACf;IACA,CAAC,GAAG,EAAED,CAAC,GAAGK,KAAK,GAAGlC,CAAC,EAAE8B,CAAC,CAAC;IACvB;IACA,CAAC,GAAG,EAAE9B,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE6B,CAAC,GAAGK,KAAK,EAAEJ,CAAC,GAAG9B,CAAC,CAAC;IACtC;IACA,CAAC,GAAG,EAAE6B,CAAC,GAAGK,KAAK,EAAEJ,CAAC,GAAGK,MAAM,GAAGnC,CAAC,CAAC;IAChC;IACA,CAAC,GAAG,EAAEA,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE6B,CAAC,GAAGK,KAAK,GAAGlC,CAAC,EAAE8B,CAAC,GAAGK,MAAM,CAAC;IAC/C;IACA,CAAC,GAAG,EAAEN,CAAC,GAAG7B,CAAC,EAAE8B,CAAC,GAAGK,MAAM,CAAC;IACxB;IACA,CAAC,GAAG,EAAEnC,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE6B,CAAC,EAAEC,CAAC,GAAGK,MAAM,GAAGnC,CAAC,CAAC;IACvC;IACA,CAAC,GAAG,EAAE6B,CAAC,EAAEC,CAAC,GAAG9B,CAAC,CAAC;IACf;IACA,CAAC,GAAG,EAAEA,CAAC,EAAEA,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE6B,CAAC,GAAG7B,CAAC,EAAE8B,CAAC,CAAC;IAC9B;IACA,CAAC,GAAG,CAAC,CACN;IAED;IACA,IAAMO,iBAAiB,GAAG;MAAEC,GAAG,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAC,CAAE;IAClE,IAAMC,KAAK,GAAGL,iBAAiB,CAAC1C,QAAQ,CAAC;IACzC,IAAMgD,OAAO,GAAQ,IAAI,CAACC,YAAY,CAAC,CAACR,SAAS,CAACM,KAAK,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC,CAAQ,EAAET,SAAS,CAACM,KAAK,GAAG,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC,CAAQ,CAAC,CAAC;IAClH;IACAT,SAAS,CAACU,MAAM,CAAAC,KAAA,CAAhBX,SAAS,EAAAY,aAAA,EAAQN,KAAK,GAAG,CAAC,EAAE,CAAC,GAAA5C,MAAA,CAAK6C,OAAO;IACzCP,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;IACrB,OAAOA,SAAS;EAClB,CAAC;EAEOvD,SAAA,CAAAU,SAAA,CAAAqD,YAAY,GAApB,UAAqBK,IAAU,EAAEC,IAAiB;IAAjB,IAAAA,IAAA;MAAAA,IAAA,KAAiB;IAAA;IAChD;IACA,IAAMC,WAAW,GAAG,GAAG;IACvB,IAAMC,GAAG,GAAGnF,YAAY,CAAA8E,KAAA,SAAAC,aAAA,KAAAlD,MAAA,CAAImD,IAAI,UAAC;IAC3B,IAAAxD,EAAA,GAAAK,MAAA,CAAuBmD,IAAI;MAA1BpD,EAAA,GAAAC,MAAA,CAAAL,EAAA,OAAQ;MAAP4D,EAAE,GAAAxD,EAAA;MAAEyD,EAAE,GAAAzD,EAAA;MAAGM,EAAA,GAAAL,MAAA,CAAAL,EAAA,OAAQ;MAAP8D,EAAE,GAAApD,EAAA;MAAEqD,EAAE,GAAArD,EAAA,GAAS;IAC3B,IAAAsD,EAAA,GAAA3D,MAAA,CAAkBsD,GAAG,GAAG,CAACG,EAAE,GAAGF,EAAE,EAAE,CAACA,EAAE,EAAEE,EAAE,CAAC,CAAC,GAAG,CAACC,EAAE,GAAGF,EAAE,EAAE,CAACA,EAAE,EAAEE,EAAE,CAAC,CAAC;MAAhEE,GAAG,GAAAD,EAAA;MAAEE,EAAA,GAAA7D,MAAA,CAAA2D,EAAA,OAAQ;MAAPG,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAAoD;IACvE,IAAMG,EAAE,GAAGJ,GAAG,GAAG,CAAC;IAClB,IAAMK,IAAI,GAAGL,GAAG,GAAGM,IAAI,CAACC,GAAG,CAACP,GAAG,CAAC;IAChC,IAAMQ,EAAE,GAAGhB,IAAI,GAAGa,IAAI;IACtB,IAAMI,GAAG,GAAGD,EAAE,GAAG,CAAC;IAClB,IAAME,EAAE,GAAKF,EAAE,GAAGF,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC,GAAI,CAAC,GAAIlB,WAAW;IAC5C,IAAAmB,EAAA,GAAAxE,MAAA,CAAuB,CAAC8D,EAAE,EAAEA,EAAE,GAAGE,EAAE,GAAGK,GAAG,EAAEP,EAAE,GAAGE,EAAE,EAAEF,EAAE,GAAGE,EAAE,GAAGK,GAAG,EAAEN,EAAE,CAAC;MAArEU,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA;MAAEG,EAAE,GAAAH,EAAA;MAAEI,EAAE,GAAAJ,EAAA;MAAEK,EAAE,GAAAL,EAAA,GAAmD;IAE5E,IAAIlB,GAAG,EAAE;MACP,IAAI,CAACjE,KAAK,GAAG,CAACsF,EAAE,EAAEnB,EAAE,GAAGc,EAAE,CAAC;MAC1B,OAAO,CACL,CAAC,GAAG,EAAEG,EAAE,EAAEjB,EAAE,CAAC,EACb,CAAC,GAAG,EAAEkB,EAAE,EAAElB,EAAE,CAAC,EACb,CAAC,GAAG,EAAEmB,EAAE,EAAEnB,EAAE,GAAGc,EAAE,CAAC,EAClB,CAAC,GAAG,EAAEM,EAAE,EAAEpB,EAAE,CAAC,EACb,CAAC,GAAG,EAAEqB,EAAE,EAAErB,EAAE,CAAC,CACd;IACH;IACA,IAAI,CAACnE,KAAK,GAAG,CAACkE,EAAE,GAAGe,EAAE,EAAEK,EAAE,CAAC;IAC1B,OAAO,CACL,CAAC,GAAG,EAAEpB,EAAE,EAAEkB,EAAE,CAAC,EACb,CAAC,GAAG,EAAElB,EAAE,EAAEmB,EAAE,CAAC,EACb,CAAC,GAAG,EAAEnB,EAAE,GAAGe,EAAE,EAAEK,EAAE,CAAC,EAClB,CAAC,GAAG,EAAEpB,EAAE,EAAEqB,EAAE,CAAC,EACb,CAAC,GAAG,EAAErB,EAAE,EAAEsB,EAAE,CAAC,CACd;EACH,CAAC;EAEO9F,SAAA,CAAAU,SAAA,CAAAqF,eAAe,GAAvB;IACU,IAAAC,UAAU,GAAK,IAAI,CAACnF,UAAU,CAAAmF,UAApB;IAClB,IAAIA,UAAU,KAAK,QAAQ,EAAE7G,IAAI,CAAC,IAAI,CAAC,CAAC,KACnCK,IAAI,CAAC,IAAI,CAAC;EACjB,CAAC;EAEMQ,SAAA,CAAAU,SAAA,CAAAuF,UAAU,GAAjB;IACE,IAAI,CAAClG,KAAK,CAACmG,EAAE,CAACpH,YAAY,CAACqH,cAAc,EAAE,IAAI,CAACxF,gBAAgB,CAAC;EACnE,CAAC;EAEMX,SAAA,CAAAU,SAAA,CAAA0F,MAAM,GAAb;IACE,IAAI,CAACjE,WAAW,EAAE;IAClB,IAAI,CAACxB,gBAAgB,EAAE;IACvB,IAAI,CAACkC,YAAY,EAAE;IACnB,IAAI,CAACkD,eAAe,EAAE;EACxB,CAAC;EACH,OAAA/F,SAAC;AAAD,CAAC,CAzI8BjB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}