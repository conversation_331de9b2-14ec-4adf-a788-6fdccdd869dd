{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar greedyFAS = require(\"./greedy-fas\");\nmodule.exports = {\n  run: run,\n  undo: undo\n};\nfunction run(g) {\n  var fas = g.graph().acyclicer === \"greedy\" ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  _.forEach(fas, function (e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId(\"rev\"));\n  });\n  function weightFn(g) {\n    return function (e) {\n      return g.edge(e).weight;\n    };\n  }\n}\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n  function dfs(v) {\n    if (_.has(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function (e) {\n      if (_.has(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\nfunction undo(g) {\n  _.forEach(g.edges(), function (e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}", "map": {"version": 3, "names": ["_", "require", "greedyFAS", "module", "exports", "run", "undo", "g", "fas", "graph", "acyclicer", "weightFn", "dfsFAS", "for<PERSON>ach", "e", "label", "edge", "removeEdge", "<PERSON><PERSON><PERSON>", "name", "reversed", "setEdge", "w", "v", "uniqueId", "weight", "stack", "visited", "dfs", "has", "outEdges", "push", "nodes", "edges"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/acyclic.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar greedyFAS = require(\"./greedy-fas\");\n\nmodule.exports = {\n  run: run,\n  undo: undo\n};\n\nfunction run(g) {\n  var fas = (g.graph().acyclicer === \"greedy\"\n    ? greedyFAS(g, weightFn(g))\n    : dfsFAS(g));\n  _.forEach(fas, function(e) {\n    var label = g.edge(e);\n    g.removeEdge(e);\n    label.forwardName = e.name;\n    label.reversed = true;\n    g.setEdge(e.w, e.v, label, _.uniqueId(\"rev\"));\n  });\n\n  function weightFn(g) {\n    return function(e) {\n      return g.edge(e).weight;\n    };\n  }\n}\n\nfunction dfsFAS(g) {\n  var fas = [];\n  var stack = {};\n  var visited = {};\n\n  function dfs(v) {\n    if (_.has(visited, v)) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    _.forEach(g.outEdges(v), function(e) {\n      if (_.has(stack, e.w)) {\n        fas.push(e);\n      } else {\n        dfs(e.w);\n      }\n    });\n    delete stack[v];\n  }\n\n  _.forEach(g.nodes(), dfs);\n  return fas;\n}\n\nfunction undo(g) {\n  _.forEach(g.edges(), function(e) {\n    var label = g.edge(e);\n    if (label.reversed) {\n      g.removeEdge(e);\n\n      var forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.setEdge(e.w, e.v, label, forwardName);\n    }\n  });\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,SAAS,GAAGD,OAAO,CAAC,cAAc,CAAC;AAEvCE,MAAM,CAACC,OAAO,GAAG;EACfC,GAAG,EAAEA,GAAG;EACRC,IAAI,EAAEA;AACR,CAAC;AAED,SAASD,GAAGA,CAACE,CAAC,EAAE;EACd,IAAIC,GAAG,GAAID,CAAC,CAACE,KAAK,CAAC,CAAC,CAACC,SAAS,KAAK,QAAQ,GACvCR,SAAS,CAACK,CAAC,EAAEI,QAAQ,CAACJ,CAAC,CAAC,CAAC,GACzBK,MAAM,CAACL,CAAC,CAAE;EACdP,CAAC,CAACa,OAAO,CAACL,GAAG,EAAE,UAASM,CAAC,EAAE;IACzB,IAAIC,KAAK,GAAGR,CAAC,CAACS,IAAI,CAACF,CAAC,CAAC;IACrBP,CAAC,CAACU,UAAU,CAACH,CAAC,CAAC;IACfC,KAAK,CAACG,WAAW,GAAGJ,CAAC,CAACK,IAAI;IAC1BJ,KAAK,CAACK,QAAQ,GAAG,IAAI;IACrBb,CAAC,CAACc,OAAO,CAACP,CAAC,CAACQ,CAAC,EAAER,CAAC,CAACS,CAAC,EAAER,KAAK,EAAEf,CAAC,CAACwB,QAAQ,CAAC,KAAK,CAAC,CAAC;EAC/C,CAAC,CAAC;EAEF,SAASb,QAAQA,CAACJ,CAAC,EAAE;IACnB,OAAO,UAASO,CAAC,EAAE;MACjB,OAAOP,CAAC,CAACS,IAAI,CAACF,CAAC,CAAC,CAACW,MAAM;IACzB,CAAC;EACH;AACF;AAEA,SAASb,MAAMA,CAACL,CAAC,EAAE;EACjB,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAIkB,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,OAAO,GAAG,CAAC,CAAC;EAEhB,SAASC,GAAGA,CAACL,CAAC,EAAE;IACd,IAAIvB,CAAC,CAAC6B,GAAG,CAACF,OAAO,EAAEJ,CAAC,CAAC,EAAE;MACrB;IACF;IACAI,OAAO,CAACJ,CAAC,CAAC,GAAG,IAAI;IACjBG,KAAK,CAACH,CAAC,CAAC,GAAG,IAAI;IACfvB,CAAC,CAACa,OAAO,CAACN,CAAC,CAACuB,QAAQ,CAACP,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;MACnC,IAAId,CAAC,CAAC6B,GAAG,CAACH,KAAK,EAAEZ,CAAC,CAACQ,CAAC,CAAC,EAAE;QACrBd,GAAG,CAACuB,IAAI,CAACjB,CAAC,CAAC;MACb,CAAC,MAAM;QACLc,GAAG,CAACd,CAAC,CAACQ,CAAC,CAAC;MACV;IACF,CAAC,CAAC;IACF,OAAOI,KAAK,CAACH,CAAC,CAAC;EACjB;EAEAvB,CAAC,CAACa,OAAO,CAACN,CAAC,CAACyB,KAAK,CAAC,CAAC,EAAEJ,GAAG,CAAC;EACzB,OAAOpB,GAAG;AACZ;AAEA,SAASF,IAAIA,CAACC,CAAC,EAAE;EACfP,CAAC,CAACa,OAAO,CAACN,CAAC,CAAC0B,KAAK,CAAC,CAAC,EAAE,UAASnB,CAAC,EAAE;IAC/B,IAAIC,KAAK,GAAGR,CAAC,CAACS,IAAI,CAACF,CAAC,CAAC;IACrB,IAAIC,KAAK,CAACK,QAAQ,EAAE;MAClBb,CAAC,CAACU,UAAU,CAACH,CAAC,CAAC;MAEf,IAAII,WAAW,GAAGH,KAAK,CAACG,WAAW;MACnC,OAAOH,KAAK,CAACK,QAAQ;MACrB,OAAOL,KAAK,CAACG,WAAW;MACxBX,CAAC,CAACc,OAAO,CAACP,CAAC,CAACQ,CAAC,EAAER,CAAC,CAACS,CAAC,EAAER,KAAK,EAAEG,WAAW,CAAC;IACzC;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}