{"ast": null, "code": "export default function (parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n    i,\n    n = nodes.length,\n    sum,\n    sums = new Array(n + 1);\n  for (sums[0] = sum = i = 0; i < n; ++i) {\n    sums[i + 1] = sum += nodes[i].value;\n  }\n  partition(0, n, parent.value, x0, y0, x1, y1);\n  function partition(i, j, value, x0, y0, x1, y1) {\n    if (i >= j - 1) {\n      var node = nodes[i];\n      node.x0 = x0, node.y0 = y0;\n      node.x1 = x1, node.y1 = y1;\n      return;\n    }\n    var valueOffset = sums[i],\n      valueTarget = value / 2 + valueOffset,\n      k = i + 1,\n      hi = j - 1;\n    while (k < hi) {\n      var mid = k + hi >>> 1;\n      if (sums[mid] < valueTarget) k = mid + 1;else hi = mid;\n    }\n    if (valueTarget - sums[k - 1] < sums[k] - valueTarget && i + 1 < k) --k;\n    var valueLeft = sums[k] - valueOffset,\n      valueRight = value - valueLeft;\n    if (x1 - x0 > y1 - y0) {\n      var xk = value ? (x0 * valueRight + x1 * valueLeft) / value : x1;\n      partition(i, k, valueLeft, x0, y0, xk, y1);\n      partition(k, j, valueRight, xk, y0, x1, y1);\n    } else {\n      var yk = value ? (y0 * valueRight + y1 * valueLeft) / value : y1;\n      partition(i, k, valueLeft, x0, y0, x1, yk);\n      partition(k, j, valueRight, x0, yk, x1, y1);\n    }\n  }\n}", "map": {"version": 3, "names": ["parent", "x0", "y0", "x1", "y1", "nodes", "children", "i", "n", "length", "sum", "sums", "Array", "value", "partition", "j", "node", "valueOffset", "valueTarget", "k", "hi", "mid", "valueLeft", "valueRight", "xk", "yk"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/treemap/binary.js"], "sourcesContent": ["export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      i, n = nodes.length,\n      sum, sums = new Array(n + 1);\n\n  for (sums[0] = sum = i = 0; i < n; ++i) {\n    sums[i + 1] = sum += nodes[i].value;\n  }\n\n  partition(0, n, parent.value, x0, y0, x1, y1);\n\n  function partition(i, j, value, x0, y0, x1, y1) {\n    if (i >= j - 1) {\n      var node = nodes[i];\n      node.x0 = x0, node.y0 = y0;\n      node.x1 = x1, node.y1 = y1;\n      return;\n    }\n\n    var valueOffset = sums[i],\n        valueTarget = (value / 2) + valueOffset,\n        k = i + 1,\n        hi = j - 1;\n\n    while (k < hi) {\n      var mid = k + hi >>> 1;\n      if (sums[mid] < valueTarget) k = mid + 1;\n      else hi = mid;\n    }\n\n    if ((valueTarget - sums[k - 1]) < (sums[k] - valueTarget) && i + 1 < k) --k;\n\n    var valueLeft = sums[k] - valueOffset,\n        valueRight = value - valueLeft;\n\n    if ((x1 - x0) > (y1 - y0)) {\n      var xk = value ? (x0 * valueRight + x1 * valueLeft) / value : x1;\n      partition(i, k, valueLeft, x0, y0, xk, y1);\n      partition(k, j, valueRight, xk, y0, x1, y1);\n    } else {\n      var yk = value ? (y0 * valueRight + y1 * valueLeft) / value : y1;\n      partition(i, k, valueLeft, x0, y0, x1, yk);\n      partition(k, j, valueRight, x0, yk, x1, y1);\n    }\n  }\n}\n"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC9C,IAAIC,KAAK,GAAGL,MAAM,CAACM,QAAQ;IACvBC,CAAC;IAAEC,CAAC,GAAGH,KAAK,CAACI,MAAM;IACnBC,GAAG;IAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC;EAEhC,KAAKG,IAAI,CAAC,CAAC,CAAC,GAAGD,GAAG,GAAGH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACtCI,IAAI,CAACJ,CAAC,GAAG,CAAC,CAAC,GAAGG,GAAG,IAAIL,KAAK,CAACE,CAAC,CAAC,CAACM,KAAK;EACrC;EAEAC,SAAS,CAAC,CAAC,EAAEN,CAAC,EAAER,MAAM,CAACa,KAAK,EAAEZ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAE7C,SAASU,SAASA,CAACP,CAAC,EAAEQ,CAAC,EAAEF,KAAK,EAAEZ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IAC9C,IAAIG,CAAC,IAAIQ,CAAC,GAAG,CAAC,EAAE;MACd,IAAIC,IAAI,GAAGX,KAAK,CAACE,CAAC,CAAC;MACnBS,IAAI,CAACf,EAAE,GAAGA,EAAE,EAAEe,IAAI,CAACd,EAAE,GAAGA,EAAE;MAC1Bc,IAAI,CAACb,EAAE,GAAGA,EAAE,EAAEa,IAAI,CAACZ,EAAE,GAAGA,EAAE;MAC1B;IACF;IAEA,IAAIa,WAAW,GAAGN,IAAI,CAACJ,CAAC,CAAC;MACrBW,WAAW,GAAIL,KAAK,GAAG,CAAC,GAAII,WAAW;MACvCE,CAAC,GAAGZ,CAAC,GAAG,CAAC;MACTa,EAAE,GAAGL,CAAC,GAAG,CAAC;IAEd,OAAOI,CAAC,GAAGC,EAAE,EAAE;MACb,IAAIC,GAAG,GAAGF,CAAC,GAAGC,EAAE,KAAK,CAAC;MACtB,IAAIT,IAAI,CAACU,GAAG,CAAC,GAAGH,WAAW,EAAEC,CAAC,GAAGE,GAAG,GAAG,CAAC,CAAC,KACpCD,EAAE,GAAGC,GAAG;IACf;IAEA,IAAKH,WAAW,GAAGP,IAAI,CAACQ,CAAC,GAAG,CAAC,CAAC,GAAKR,IAAI,CAACQ,CAAC,CAAC,GAAGD,WAAY,IAAIX,CAAC,GAAG,CAAC,GAAGY,CAAC,EAAE,EAAEA,CAAC;IAE3E,IAAIG,SAAS,GAAGX,IAAI,CAACQ,CAAC,CAAC,GAAGF,WAAW;MACjCM,UAAU,GAAGV,KAAK,GAAGS,SAAS;IAElC,IAAKnB,EAAE,GAAGF,EAAE,GAAKG,EAAE,GAAGF,EAAG,EAAE;MACzB,IAAIsB,EAAE,GAAGX,KAAK,GAAG,CAACZ,EAAE,GAAGsB,UAAU,GAAGpB,EAAE,GAAGmB,SAAS,IAAIT,KAAK,GAAGV,EAAE;MAChEW,SAAS,CAACP,CAAC,EAAEY,CAAC,EAAEG,SAAS,EAAErB,EAAE,EAAEC,EAAE,EAAEsB,EAAE,EAAEpB,EAAE,CAAC;MAC1CU,SAAS,CAACK,CAAC,EAAEJ,CAAC,EAAEQ,UAAU,EAAEC,EAAE,EAAEtB,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;IAC7C,CAAC,MAAM;MACL,IAAIqB,EAAE,GAAGZ,KAAK,GAAG,CAACX,EAAE,GAAGqB,UAAU,GAAGnB,EAAE,GAAGkB,SAAS,IAAIT,KAAK,GAAGT,EAAE;MAChEU,SAAS,CAACP,CAAC,EAAEY,CAAC,EAAEG,SAAS,EAAErB,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEsB,EAAE,CAAC;MAC1CX,SAAS,CAACK,CAAC,EAAEJ,CAAC,EAAEQ,UAAU,EAAEtB,EAAE,EAAEwB,EAAE,EAAEtB,EAAE,EAAEC,EAAE,CAAC;IAC7C;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}