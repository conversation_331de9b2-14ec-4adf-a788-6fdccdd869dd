{"ast": null, "code": "import { binarytree } from \"d3-binarytree\";\nimport { quadtree } from \"d3-quadtree\";\nimport { octree } from \"d3-octree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nfunction x(d) {\n  return d.x + d.vx;\n}\nfunction y(d) {\n  return d.y + d.vy;\n}\nfunction z(d) {\n  return d.z + d.vz;\n}\nexport default function (radius) {\n  var nodes,\n    nDim,\n    radii,\n    random,\n    strength = 1,\n    iterations = 1;\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n  function force() {\n    var i,\n      n = nodes.length,\n      tree,\n      node,\n      xi,\n      yi,\n      zi,\n      ri,\n      ri2;\n    for (var k = 0; k < iterations; ++k) {\n      tree = (nDim === 1 ? binarytree(nodes, x) : nDim === 2 ? quadtree(nodes, x, y) : nDim === 3 ? octree(nodes, x, y, z) : null).visitAfter(prepare);\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        if (nDim > 1) {\n          yi = node.y + node.vy;\n        }\n        if (nDim > 2) {\n          zi = node.z + node.vz;\n        }\n        tree.visit(apply);\n      }\n    }\n    function apply(treeNode, arg1, arg2, arg3, arg4, arg5, arg6) {\n      var args = [arg1, arg2, arg3, arg4, arg5, arg6];\n      var x0 = args[0],\n        y0 = args[1],\n        z0 = args[2],\n        x1 = args[nDim],\n        y1 = args[nDim + 1],\n        z1 = args[nDim + 2];\n      var data = treeNode.data,\n        rj = treeNode.r,\n        r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n            y = nDim > 1 ? yi - data.y - data.vy : 0,\n            z = nDim > 2 ? zi - data.z - data.vz : 0,\n            l = x * x + y * y + z * z;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (nDim > 1 && y === 0) y = jiggle(random), l += y * y;\n            if (nDim > 2 && z === 0) z = jiggle(random), l += z * z;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            if (nDim > 1) {\n              node.vy += (y *= l) * r;\n            }\n            if (nDim > 2) {\n              node.vz += (z *= l) * r;\n            }\n            data.vx -= x * (r = 1 - r);\n            if (nDim > 1) {\n              data.vy -= y * r;\n            }\n            if (nDim > 2) {\n              data.vz -= z * r;\n            }\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r || nDim > 1 && (y0 > yi + r || y1 < yi - r) || nDim > 2 && (z0 > zi + r || z1 < zi - r);\n    }\n  }\n  function prepare(treeNode) {\n    if (treeNode.data) return treeNode.r = radii[treeNode.data.index];\n    for (var i = treeNode.r = 0; i < Math.pow(2, nDim); ++i) {\n      if (treeNode[i] && treeNode[i].r > treeNode.r) {\n        treeNode.r = treeNode[i].r;\n      }\n    }\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length,\n      node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n  force.initialize = function (_nodes, ...args) {\n    nodes = _nodes;\n    random = args.find(arg => typeof arg === 'function') || Math.random;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n  force.iterations = function (_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n  force.radius = function (_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["binarytree", "quadtree", "octree", "constant", "jiggle", "x", "d", "vx", "y", "vy", "z", "vz", "radius", "nodes", "nDim", "radii", "random", "strength", "iterations", "force", "i", "n", "length", "tree", "node", "xi", "yi", "zi", "ri", "ri2", "k", "visitAfter", "prepare", "index", "visit", "apply", "treeNode", "arg1", "arg2", "arg3", "arg4", "arg5", "arg6", "args", "x0", "y0", "z0", "x1", "y1", "z1", "data", "rj", "r", "l", "Math", "sqrt", "pow", "initialize", "Array", "_nodes", "find", "arg", "includes", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force-3d/src/collide.js"], "sourcesContent": ["import {binarytree} from \"d3-binarytree\";\nimport {quadtree} from \"d3-quadtree\";\nimport {octree} from \"d3-octree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction x(d) {\n  return d.x + d.vx;\n}\n\nfunction y(d) {\n  return d.y + d.vy;\n}\n\nfunction z(d) {\n  return d.z + d.vz;\n}\n\nexport default function(radius) {\n  var nodes,\n      nDim,\n      radii,\n      random,\n      strength = 1,\n      iterations = 1;\n\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n\n  function force() {\n    var i, n = nodes.length,\n        tree,\n        node,\n        xi,\n        yi,\n        zi,\n        ri,\n        ri2;\n\n    for (var k = 0; k < iterations; ++k) {\n      tree =\n          (nDim === 1 ? binarytree(nodes, x)\n          :(nDim === 2 ? quadtree(nodes, x, y)\n          :(nDim === 3 ? octree(nodes, x, y, z)\n          :null\n      ))).visitAfter(prepare);\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        if (nDim > 1) { yi = node.y + node.vy; }\n        if (nDim > 2) { zi = node.z + node.vz; }\n        tree.visit(apply);\n      }\n    }\n\n    function apply(treeNode, arg1, arg2, arg3, arg4, arg5, arg6) {\n      var args = [arg1, arg2, arg3, arg4, arg5, arg6];\n      var x0 = args[0],\n          y0 = args[1],\n          z0 = args[2],\n          x1 = args[nDim],\n          y1 = args[nDim+1],\n          z1 = args[nDim+2];\n\n      var data = treeNode.data, rj = treeNode.r, r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n              y = (nDim > 1 ? yi - data.y - data.vy : 0),\n              z = (nDim > 2 ? zi - data.z - data.vz : 0),\n              l = x * x + y * y + z * z;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (nDim > 1 && y === 0) y = jiggle(random), l += y * y;\n            if (nDim > 2 && z === 0) z = jiggle(random), l += z * z;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            if (nDim > 1) { node.vy += (y *= l) * r; }\n            if (nDim > 2) { node.vz += (z *= l) * r; }\n\n            data.vx -= x * (r = 1 - r);\n            if (nDim > 1) { data.vy -= y * r; }\n            if (nDim > 2) { data.vz -= z * r; }\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r\n          || (nDim > 1 && (y0 > yi + r || y1 < yi - r))\n          || (nDim > 2 && (z0 > zi + r || z1 < zi - r));\n    }\n  }\n\n  function prepare(treeNode) {\n    if (treeNode.data) return treeNode.r = radii[treeNode.data.index];\n    for (var i = treeNode.r = 0; i < Math.pow(2, nDim); ++i) {\n      if (treeNode[i] && treeNode[i].r > treeNode.r) {\n        treeNode.r = treeNode[i].r;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n\n  force.initialize = function(_nodes, ...args) {\n    nodes = _nodes;\n    random = args.find(arg => typeof arg === 'function') || Math.random;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,SAAQA,UAAU,QAAO,eAAe;AACxC,SAAQC,QAAQ,QAAO,aAAa;AACpC,SAAQC,MAAM,QAAO,WAAW;AAChC,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAEhC,SAASC,CAACA,CAACC,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACD,CAAC,GAAGC,CAAC,CAACC,EAAE;AACnB;AAEA,SAASC,CAACA,CAACF,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACE,CAAC,GAAGF,CAAC,CAACG,EAAE;AACnB;AAEA,SAASC,CAACA,CAACJ,CAAC,EAAE;EACZ,OAAOA,CAAC,CAACI,CAAC,GAAGJ,CAAC,CAACK,EAAE;AACnB;AAEA,eAAe,UAASC,MAAM,EAAE;EAC9B,IAAIC,KAAK;IACLC,IAAI;IACJC,KAAK;IACLC,MAAM;IACNC,QAAQ,GAAG,CAAC;IACZC,UAAU,GAAG,CAAC;EAElB,IAAI,OAAON,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGT,QAAQ,CAACS,MAAM,IAAI,IAAI,GAAG,CAAC,GAAG,CAACA,MAAM,CAAC;EAEjF,SAASO,KAAKA,CAAA,EAAG;IACf,IAAIC,CAAC;MAAEC,CAAC,GAAGR,KAAK,CAACS,MAAM;MACnBC,IAAI;MACJC,IAAI;MACJC,EAAE;MACFC,EAAE;MACFC,EAAE;MACFC,EAAE;MACFC,GAAG;IAEP,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,UAAU,EAAE,EAAEY,CAAC,EAAE;MACnCP,IAAI,GACA,CAACT,IAAI,KAAK,CAAC,GAAGd,UAAU,CAACa,KAAK,EAAER,CAAC,CAAC,GAChCS,IAAI,KAAK,CAAC,GAAGb,QAAQ,CAACY,KAAK,EAAER,CAAC,EAAEG,CAAC,CAAC,GAClCM,IAAI,KAAK,CAAC,GAAGZ,MAAM,CAACW,KAAK,EAAER,CAAC,EAAEG,CAAC,EAAEE,CAAC,CAAC,GACpC,IACH,EAAEqB,UAAU,CAACC,OAAO,CAAC;MAEvB,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtBI,IAAI,GAAGX,KAAK,CAACO,CAAC,CAAC;QACfQ,EAAE,GAAGb,KAAK,CAACS,IAAI,CAACS,KAAK,CAAC,EAAEJ,GAAG,GAAGD,EAAE,GAAGA,EAAE;QACrCH,EAAE,GAAGD,IAAI,CAACnB,CAAC,GAAGmB,IAAI,CAACjB,EAAE;QACrB,IAAIO,IAAI,GAAG,CAAC,EAAE;UAAEY,EAAE,GAAGF,IAAI,CAAChB,CAAC,GAAGgB,IAAI,CAACf,EAAE;QAAE;QACvC,IAAIK,IAAI,GAAG,CAAC,EAAE;UAAEa,EAAE,GAAGH,IAAI,CAACd,CAAC,GAAGc,IAAI,CAACb,EAAE;QAAE;QACvCY,IAAI,CAACW,KAAK,CAACC,KAAK,CAAC;MACnB;IACF;IAEA,SAASA,KAAKA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAE;MAC3D,IAAIC,IAAI,GAAG,CAACN,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,CAAC;MAC/C,IAAIE,EAAE,GAAGD,IAAI,CAAC,CAAC,CAAC;QACZE,EAAE,GAAGF,IAAI,CAAC,CAAC,CAAC;QACZG,EAAE,GAAGH,IAAI,CAAC,CAAC,CAAC;QACZI,EAAE,GAAGJ,IAAI,CAAC7B,IAAI,CAAC;QACfkC,EAAE,GAAGL,IAAI,CAAC7B,IAAI,GAAC,CAAC,CAAC;QACjBmC,EAAE,GAAGN,IAAI,CAAC7B,IAAI,GAAC,CAAC,CAAC;MAErB,IAAIoC,IAAI,GAAGd,QAAQ,CAACc,IAAI;QAAEC,EAAE,GAAGf,QAAQ,CAACgB,CAAC;QAAEA,CAAC,GAAGxB,EAAE,GAAGuB,EAAE;MACtD,IAAID,IAAI,EAAE;QACR,IAAIA,IAAI,CAACjB,KAAK,GAAGT,IAAI,CAACS,KAAK,EAAE;UAC3B,IAAI5B,CAAC,GAAGoB,EAAE,GAAGyB,IAAI,CAAC7C,CAAC,GAAG6C,IAAI,CAAC3C,EAAE;YACzBC,CAAC,GAAIM,IAAI,GAAG,CAAC,GAAGY,EAAE,GAAGwB,IAAI,CAAC1C,CAAC,GAAG0C,IAAI,CAACzC,EAAE,GAAG,CAAE;YAC1CC,CAAC,GAAII,IAAI,GAAG,CAAC,GAAGa,EAAE,GAAGuB,IAAI,CAACxC,CAAC,GAAGwC,IAAI,CAACvC,EAAE,GAAG,CAAE;YAC1C0C,CAAC,GAAGhD,CAAC,GAAGA,CAAC,GAAGG,CAAC,GAAGA,CAAC,GAAGE,CAAC,GAAGA,CAAC;UAC7B,IAAI2C,CAAC,GAAGD,CAAC,GAAGA,CAAC,EAAE;YACb,IAAI/C,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACY,MAAM,CAAC,EAAEqC,CAAC,IAAIhD,CAAC,GAAGA,CAAC;YAC3C,IAAIS,IAAI,GAAG,CAAC,IAAIN,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACY,MAAM,CAAC,EAAEqC,CAAC,IAAI7C,CAAC,GAAGA,CAAC;YACvD,IAAIM,IAAI,GAAG,CAAC,IAAIJ,CAAC,KAAK,CAAC,EAAEA,CAAC,GAAGN,MAAM,CAACY,MAAM,CAAC,EAAEqC,CAAC,IAAI3C,CAAC,GAAGA,CAAC;YACvD2C,CAAC,GAAG,CAACD,CAAC,IAAIC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACF,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAGpC,QAAQ;YAE3CO,IAAI,CAACjB,EAAE,IAAI,CAACF,CAAC,IAAIgD,CAAC,KAAKD,CAAC,GAAG,CAACD,EAAE,IAAIA,EAAE,KAAKtB,GAAG,GAAGsB,EAAE,CAAC,CAAC;YACnD,IAAIrC,IAAI,GAAG,CAAC,EAAE;cAAEU,IAAI,CAACf,EAAE,IAAI,CAACD,CAAC,IAAI6C,CAAC,IAAID,CAAC;YAAE;YACzC,IAAItC,IAAI,GAAG,CAAC,EAAE;cAAEU,IAAI,CAACb,EAAE,IAAI,CAACD,CAAC,IAAI2C,CAAC,IAAID,CAAC;YAAE;YAEzCF,IAAI,CAAC3C,EAAE,IAAIF,CAAC,IAAI+C,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;YAC1B,IAAItC,IAAI,GAAG,CAAC,EAAE;cAAEoC,IAAI,CAACzC,EAAE,IAAID,CAAC,GAAG4C,CAAC;YAAE;YAClC,IAAItC,IAAI,GAAG,CAAC,EAAE;cAAEoC,IAAI,CAACvC,EAAE,IAAID,CAAC,GAAG0C,CAAC;YAAE;UACpC;QACF;QACA;MACF;MACA,OAAOR,EAAE,GAAGnB,EAAE,GAAG2B,CAAC,IAAIL,EAAE,GAAGtB,EAAE,GAAG2B,CAAC,IACzBtC,IAAI,GAAG,CAAC,KAAK+B,EAAE,GAAGnB,EAAE,GAAG0B,CAAC,IAAIJ,EAAE,GAAGtB,EAAE,GAAG0B,CAAC,CAAE,IACzCtC,IAAI,GAAG,CAAC,KAAKgC,EAAE,GAAGnB,EAAE,GAAGyB,CAAC,IAAIH,EAAE,GAAGtB,EAAE,GAAGyB,CAAC,CAAE;IACnD;EACF;EAEA,SAASpB,OAAOA,CAACI,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAACc,IAAI,EAAE,OAAOd,QAAQ,CAACgB,CAAC,GAAGrC,KAAK,CAACqB,QAAQ,CAACc,IAAI,CAACjB,KAAK,CAAC;IACjE,KAAK,IAAIb,CAAC,GAAGgB,QAAQ,CAACgB,CAAC,GAAG,CAAC,EAAEhC,CAAC,GAAGkC,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE1C,IAAI,CAAC,EAAE,EAAEM,CAAC,EAAE;MACvD,IAAIgB,QAAQ,CAAChB,CAAC,CAAC,IAAIgB,QAAQ,CAAChB,CAAC,CAAC,CAACgC,CAAC,GAAGhB,QAAQ,CAACgB,CAAC,EAAE;QAC7ChB,QAAQ,CAACgB,CAAC,GAAGhB,QAAQ,CAAChB,CAAC,CAAC,CAACgC,CAAC;MAC5B;IACF;EACF;EAEA,SAASK,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAC5C,KAAK,EAAE;IACZ,IAAIO,CAAC;MAAEC,CAAC,GAAGR,KAAK,CAACS,MAAM;MAAEE,IAAI;IAC7BT,KAAK,GAAG,IAAI2C,KAAK,CAACrC,CAAC,CAAC;IACpB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAEI,IAAI,GAAGX,KAAK,CAACO,CAAC,CAAC,EAAEL,KAAK,CAACS,IAAI,CAACS,KAAK,CAAC,GAAG,CAACrB,MAAM,CAACY,IAAI,EAAEJ,CAAC,EAAEP,KAAK,CAAC;EACtF;EAEAM,KAAK,CAACsC,UAAU,GAAG,UAASE,MAAM,EAAE,GAAGhB,IAAI,EAAE;IAC3C9B,KAAK,GAAG8C,MAAM;IACd3C,MAAM,GAAG2B,IAAI,CAACiB,IAAI,CAACC,GAAG,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAIP,IAAI,CAACtC,MAAM;IACnEF,IAAI,GAAG6B,IAAI,CAACiB,IAAI,CAACC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC,IAAI,CAAC;IACrDJ,UAAU,CAAC,CAAC;EACd,CAAC;EAEDtC,KAAK,CAACD,UAAU,GAAG,UAAS6C,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAAC1C,MAAM,IAAIJ,UAAU,GAAG,CAAC6C,CAAC,EAAE5C,KAAK,IAAID,UAAU;EACjE,CAAC;EAEDC,KAAK,CAACF,QAAQ,GAAG,UAAS8C,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAAC1C,MAAM,IAAIL,QAAQ,GAAG,CAAC8C,CAAC,EAAE5C,KAAK,IAAIF,QAAQ;EAC7D,CAAC;EAEDE,KAAK,CAACP,MAAM,GAAG,UAASmD,CAAC,EAAE;IACzB,OAAOC,SAAS,CAAC1C,MAAM,IAAIV,MAAM,GAAG,OAAOmD,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAG5D,QAAQ,CAAC,CAAC4D,CAAC,CAAC,EAAEN,UAAU,CAAC,CAAC,EAAEtC,KAAK,IAAIP,MAAM;EAC/G,CAAC;EAED,OAAOO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}