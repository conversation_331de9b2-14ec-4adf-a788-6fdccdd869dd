{"ast": null, "code": "import { dot, weightedSum } from './blas1';\n/// searches along line 'pk' for a point that satifies the wolfe conditions\n/// See 'Numerical Optimization' by <PERSON><PERSON><PERSON> and <PERSON> p59-60\n/// f : objective function\n/// pk : search direction\n/// current: object containing current gradient/loss\n/// next: output: contains next gradient/loss\n/// returns a: step size taken\nexport function wolfeLineSearch(f, pk, current, next, a, c1, c2) {\n  const phi0 = current.fx;\n  const phiPrime0 = dot(current.fxprime, pk);\n  let phi = phi0;\n  let phi_old = phi0;\n  let phiPrime = phiPrime0;\n  let a0 = 0;\n  a = a || 1;\n  c1 = c1 || 1e-6;\n  c2 = c2 || 0.1;\n  function zoom(a_lo, a_high, phi_lo) {\n    for (let iteration = 0; iteration < 16; ++iteration) {\n      a = (a_lo + a_high) / 2;\n      weightedSum(next.x, 1.0, current.x, a, pk);\n      phi = next.fx = f(next.x, next.fxprime);\n      phiPrime = dot(next.fxprime, pk);\n      if (phi > phi0 + c1 * a * phiPrime0 || phi >= phi_lo) {\n        a_high = a;\n      } else {\n        if (Math.abs(phiPrime) <= -c2 * phiPrime0) {\n          return a;\n        }\n        if (phiPrime * (a_high - a_lo) >= 0) {\n          a_high = a_lo;\n        }\n        a_lo = a;\n        phi_lo = phi;\n      }\n    }\n    return 0;\n  }\n  for (let iteration = 0; iteration < 10; ++iteration) {\n    weightedSum(next.x, 1.0, current.x, a, pk);\n    phi = next.fx = f(next.x, next.fxprime);\n    phiPrime = dot(next.fxprime, pk);\n    if (phi > phi0 + c1 * a * phiPrime0 || iteration && phi >= phi_old) {\n      return zoom(a0, a, phi_old);\n    }\n    if (Math.abs(phiPrime) <= -c2 * phiPrime0) {\n      return a;\n    }\n    if (phiPrime >= 0) {\n      return zoom(a, a0, phi);\n    }\n    phi_old = phi;\n    a0 = a;\n    a *= 2;\n  }\n  return a;\n}", "map": {"version": 3, "names": ["dot", "weightedSum", "wolfeLineSearch", "f", "pk", "current", "next", "a", "c1", "c2", "phi0", "fx", "phiPrime0", "fxprime", "phi", "phi_old", "phiPrime", "a0", "zoom", "a_lo", "a_high", "phi_lo", "iteration", "x", "Math", "abs"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/venn/fmin/linesearch.ts"], "sourcesContent": ["import { dot, weightedSum } from './blas1';\n\n/// searches along line 'pk' for a point that satifies the wolfe conditions\n/// See 'Numerical Optimization' by <PERSON><PERSON><PERSON> and <PERSON> p59-60\n/// f : objective function\n/// pk : search direction\n/// current: object containing current gradient/loss\n/// next: output: contains next gradient/loss\n/// returns a: step size taken\nexport function wolfeLineSearch(f, pk, current, next, a, c1?: any, c2?: any) {\n  const phi0 = current.fx;\n  const phiPrime0 = dot(current.fxprime, pk);\n  let phi = phi0;\n  let phi_old = phi0;\n  let phiPrime = phiPrime0;\n  let a0 = 0;\n\n  a = a || 1;\n  c1 = c1 || 1e-6;\n  c2 = c2 || 0.1;\n\n  function zoom(a_lo, a_high, phi_lo) {\n    for (let iteration = 0; iteration < 16; ++iteration) {\n      a = (a_lo + a_high) / 2;\n      weightedSum(next.x, 1.0, current.x, a, pk);\n      phi = next.fx = f(next.x, next.fxprime);\n      phiPrime = dot(next.fxprime, pk);\n\n      if (phi > phi0 + c1 * a * phiPrime0 || phi >= phi_lo) {\n        a_high = a;\n      } else {\n        if (Math.abs(phiPrime) <= -c2 * phiPrime0) {\n          return a;\n        }\n\n        if (phiPrime * (a_high - a_lo) >= 0) {\n          a_high = a_lo;\n        }\n\n        a_lo = a;\n        phi_lo = phi;\n      }\n    }\n\n    return 0;\n  }\n\n  for (let iteration = 0; iteration < 10; ++iteration) {\n    weightedSum(next.x, 1.0, current.x, a, pk);\n    phi = next.fx = f(next.x, next.fxprime);\n    phiPrime = dot(next.fxprime, pk);\n    if (phi > phi0 + c1 * a * phiPrime0 || (iteration && phi >= phi_old)) {\n      return zoom(a0, a, phi_old);\n    }\n\n    if (Math.abs(phiPrime) <= -c2 * phiPrime0) {\n      return a;\n    }\n\n    if (phiPrime >= 0) {\n      return zoom(a, a0, phi);\n    }\n\n    phi_old = phi;\n    a0 = a;\n    a *= 2;\n  }\n\n  return a;\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,WAAW,QAAQ,SAAS;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAM,SAAUC,eAAeA,CAACC,CAAC,EAAEC,EAAE,EAAEC,OAAO,EAAEC,IAAI,EAAEC,CAAC,EAAEC,EAAQ,EAAEC,EAAQ;EACzE,MAAMC,IAAI,GAAGL,OAAO,CAACM,EAAE;EACvB,MAAMC,SAAS,GAAGZ,GAAG,CAACK,OAAO,CAACQ,OAAO,EAAET,EAAE,CAAC;EAC1C,IAAIU,GAAG,GAAGJ,IAAI;EACd,IAAIK,OAAO,GAAGL,IAAI;EAClB,IAAIM,QAAQ,GAAGJ,SAAS;EACxB,IAAIK,EAAE,GAAG,CAAC;EAEVV,CAAC,GAAGA,CAAC,IAAI,CAAC;EACVC,EAAE,GAAGA,EAAE,IAAI,IAAI;EACfC,EAAE,GAAGA,EAAE,IAAI,GAAG;EAEd,SAASS,IAAIA,CAACC,IAAI,EAAEC,MAAM,EAAEC,MAAM;IAChC,KAAK,IAAIC,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG,EAAE,EAAE,EAAEA,SAAS,EAAE;MACnDf,CAAC,GAAG,CAACY,IAAI,GAAGC,MAAM,IAAI,CAAC;MACvBnB,WAAW,CAACK,IAAI,CAACiB,CAAC,EAAE,GAAG,EAAElB,OAAO,CAACkB,CAAC,EAAEhB,CAAC,EAAEH,EAAE,CAAC;MAC1CU,GAAG,GAAGR,IAAI,CAACK,EAAE,GAAGR,CAAC,CAACG,IAAI,CAACiB,CAAC,EAAEjB,IAAI,CAACO,OAAO,CAAC;MACvCG,QAAQ,GAAGhB,GAAG,CAACM,IAAI,CAACO,OAAO,EAAET,EAAE,CAAC;MAEhC,IAAIU,GAAG,GAAGJ,IAAI,GAAGF,EAAE,GAAGD,CAAC,GAAGK,SAAS,IAAIE,GAAG,IAAIO,MAAM,EAAE;QACpDD,MAAM,GAAGb,CAAC;OACX,MAAM;QACL,IAAIiB,IAAI,CAACC,GAAG,CAACT,QAAQ,CAAC,IAAI,CAACP,EAAE,GAAGG,SAAS,EAAE;UACzC,OAAOL,CAAC;;QAGV,IAAIS,QAAQ,IAAII,MAAM,GAAGD,IAAI,CAAC,IAAI,CAAC,EAAE;UACnCC,MAAM,GAAGD,IAAI;;QAGfA,IAAI,GAAGZ,CAAC;QACRc,MAAM,GAAGP,GAAG;;;IAIhB,OAAO,CAAC;EACV;EAEA,KAAK,IAAIQ,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAG,EAAE,EAAE,EAAEA,SAAS,EAAE;IACnDrB,WAAW,CAACK,IAAI,CAACiB,CAAC,EAAE,GAAG,EAAElB,OAAO,CAACkB,CAAC,EAAEhB,CAAC,EAAEH,EAAE,CAAC;IAC1CU,GAAG,GAAGR,IAAI,CAACK,EAAE,GAAGR,CAAC,CAACG,IAAI,CAACiB,CAAC,EAAEjB,IAAI,CAACO,OAAO,CAAC;IACvCG,QAAQ,GAAGhB,GAAG,CAACM,IAAI,CAACO,OAAO,EAAET,EAAE,CAAC;IAChC,IAAIU,GAAG,GAAGJ,IAAI,GAAGF,EAAE,GAAGD,CAAC,GAAGK,SAAS,IAAKU,SAAS,IAAIR,GAAG,IAAIC,OAAQ,EAAE;MACpE,OAAOG,IAAI,CAACD,EAAE,EAAEV,CAAC,EAAEQ,OAAO,CAAC;;IAG7B,IAAIS,IAAI,CAACC,GAAG,CAACT,QAAQ,CAAC,IAAI,CAACP,EAAE,GAAGG,SAAS,EAAE;MACzC,OAAOL,CAAC;;IAGV,IAAIS,QAAQ,IAAI,CAAC,EAAE;MACjB,OAAOE,IAAI,CAACX,CAAC,EAAEU,EAAE,EAAEH,GAAG,CAAC;;IAGzBC,OAAO,GAAGD,GAAG;IACbG,EAAE,GAAGV,CAAC;IACNA,CAAC,IAAI,CAAC;;EAGR,OAAOA,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}