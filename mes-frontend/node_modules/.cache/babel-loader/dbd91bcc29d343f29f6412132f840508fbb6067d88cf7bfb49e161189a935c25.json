{"ast": null, "code": "var flatten = require('./flatten'),\n  overRest = require('./_overRest'),\n  setToString = require('./_setToString');\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\nmodule.exports = flatRest;", "map": {"version": 3, "names": ["flatten", "require", "overRest", "setToString", "flatRest", "func", "undefined", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_flatRest.js"], "sourcesContent": ["var flatten = require('./flatten'),\n    overRest = require('./_overRest'),\n    setToString = require('./_setToString');\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\n\nmodule.exports = flatRest;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;EAC9BC,QAAQ,GAAGD,OAAO,CAAC,aAAa,CAAC;EACjCE,WAAW,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,IAAI,EAAE;EACtB,OAAOF,WAAW,CAACD,QAAQ,CAACG,IAAI,EAAEC,SAAS,EAAEN,OAAO,CAAC,EAAEK,IAAI,GAAG,EAAE,CAAC;AACnE;AAEAE,MAAM,CAACC,OAAO,GAAGJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}