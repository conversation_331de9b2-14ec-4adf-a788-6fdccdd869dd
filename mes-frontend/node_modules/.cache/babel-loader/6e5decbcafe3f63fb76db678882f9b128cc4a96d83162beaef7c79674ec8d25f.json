{"ast": null, "code": "import isArray from './is-array';\n/**\n * Flattens `array` a single level deep.\n *\n * @param {Array} arr The array to flatten.\n * @param {Array} result The array to return.\n * @return {Array} Returns the new flattened array.\n * @example\n *\n * flattenDeep([1, [2, [3, [4]], 5]]);  // => [1, 2, 3, 4, 5]\n */\nvar flattenDeep = function (arr, result) {\n  if (result === void 0) {\n    result = [];\n  }\n  if (!isArray(arr)) {\n    result.push(arr);\n  } else {\n    for (var i = 0; i < arr.length; i += 1) {\n      flattenDeep(arr[i], result);\n    }\n  }\n  return result;\n};\nexport default flattenDeep;", "map": {"version": 3, "names": ["isArray", "flattenDeep", "arr", "result", "push", "i", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/flatten-deep.ts"], "sourcesContent": ["import isArray from './is-array';\n\n/**\n * Flattens `array` a single level deep.\n *\n * @param {Array} arr The array to flatten.\n * @param {Array} result The array to return.\n * @return {Array} Returns the new flattened array.\n * @example\n *\n * flattenDeep([1, [2, [3, [4]], 5]]);  // => [1, 2, 3, 4, 5]\n */\nconst flattenDeep = function(arr: any[], result: any[] = []): any[] {\n  if (!isArray(arr)) {\n    result.push(arr);\n  } else {\n    for (let i = 0; i < arr.length; i += 1) {\n      flattenDeep(arr[i], result);\n    }\n  }\n  return result;\n};\n\nexport default flattenDeep;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,YAAY;AAEhC;;;;;;;;;;AAUA,IAAMC,WAAW,GAAG,SAAAA,CAASC,GAAU,EAAEC,MAAkB;EAAlB,IAAAA,MAAA;IAAAA,MAAA,KAAkB;EAAA;EACzD,IAAI,CAACH,OAAO,CAACE,GAAG,CAAC,EAAE;IACjBC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC;GACjB,MAAM;IACL,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACtCJ,WAAW,CAACC,GAAG,CAACG,CAAC,CAAC,EAAEF,MAAM,CAAC;;;EAG/B,OAAOA,MAAM;AACf,CAAC;AAED,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}