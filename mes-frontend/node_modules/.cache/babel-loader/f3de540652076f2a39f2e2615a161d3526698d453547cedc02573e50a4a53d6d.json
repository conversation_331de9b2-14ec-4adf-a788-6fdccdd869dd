{"ast": null, "code": "import filter from './filter';\nimport contains from './contains';\n/**\n * Flattens `array` a single level deep.\n *\n * @param {Array} arr The array to inspect.\n * @param {Array} values The values to exclude.\n * @return {Array} Returns the new array of filtered values.\n * @example\n * difference([2, 1], [2, 3]);  // => [1]\n */\nvar difference = function (arr, values) {\n  if (values === void 0) {\n    values = [];\n  }\n  return filter(arr, function (value) {\n    return !contains(values, value);\n  });\n};\nexport default difference;", "map": {"version": 3, "names": ["filter", "contains", "difference", "arr", "values", "value"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/difference.ts"], "sourcesContent": ["import filter from './filter';\nimport contains from './contains';\n\n/**\n * Flattens `array` a single level deep.\n *\n * @param {Array} arr The array to inspect.\n * @param {Array} values The values to exclude.\n * @return {Array} Returns the new array of filtered values.\n * @example\n * difference([2, 1], [2, 3]);  // => [1]\n */\nconst difference = function<T>(arr: T[], values: T[] = []): T[] {\n  return filter(arr, (value: any) => !contains(values, value));\n};\n\nexport default difference;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AAEjC;;;;;;;;;AASA,IAAMC,UAAU,GAAG,SAAAA,CAAYC,GAAQ,EAAEC,MAAgB;EAAhB,IAAAA,MAAA;IAAAA,MAAA,KAAgB;EAAA;EACvD,OAAOJ,MAAM,CAACG,GAAG,EAAE,UAACE,KAAU;IAAK,QAACJ,QAAQ,CAACG,MAAM,EAAEC,KAAK,CAAC;EAAxB,CAAwB,CAAC;AAC9D,CAAC;AAED,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}