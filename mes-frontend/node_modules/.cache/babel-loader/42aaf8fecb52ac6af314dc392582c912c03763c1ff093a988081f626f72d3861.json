{"ast": null, "code": "var _jsxFileName = \"/root/mes-system/mes-frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport AppRoutes from './routes';\nimport './App.css';\n\n// Ant Design 主题配置\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = {\n  token: {\n    colorPrimary: '#1890ff',\n    borderRadius: 6,\n    colorBgContainer: '#ffffff'\n  },\n  components: {\n    Layout: {\n      headerBg: '#001529',\n      siderBg: '#001529'\n    },\n    Menu: {\n      darkItemBg: '#001529',\n      darkSubMenuItemBg: '#000c17',\n      darkItemSelectedBg: '#1890ff'\n    }\n  }\n};\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    theme: theme,\n    children: /*#__PURE__*/_jsxDEV(BrowserRouter, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 31,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "AppRoutes", "jsxDEV", "_jsxDEV", "theme", "token", "colorPrimary", "borderRadius", "colorBgContainer", "components", "Layout", "headerBg", "siderBg", "<PERSON><PERSON>", "darkItemBg", "darkSubMenuItemBg", "darkItemSelectedBg", "App", "locale", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/root/mes-system/mes-frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport AppRoutes from './routes';\nimport ErrorBoundary from './components/Common/ErrorBoundary';\nimport './App.css';\n\n// Ant Design 主题配置\nconst theme = {\n  token: {\n    colorPrimary: '#1890ff',\n    borderRadius: 6,\n    colorBgContainer: '#ffffff',\n  },\n  components: {\n    Layout: {\n      headerBg: '#001529',\n      siderBg: '#001529',\n    },\n    Menu: {\n      darkItemBg: '#001529',\n      darkSubMenuItemBg: '#000c17',\n      darkItemSelectedBg: '#1890ff',\n    },\n  },\n};\n\nconst App: React.FC = () => {\n  return (\n    <ConfigProvider\n      locale={zhCN}\n      theme={theme}\n    >\n      <BrowserRouter>\n        <div className=\"App\">\n          <AppRoutes />\n        </div>\n      </BrowserRouter>\n    </ConfigProvider>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,SAAS,MAAM,UAAU;AAEhC,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,KAAK,GAAG;EACZC,KAAK,EAAE;IACLC,YAAY,EAAE,SAAS;IACvBC,YAAY,EAAE,CAAC;IACfC,gBAAgB,EAAE;EACpB,CAAC;EACDC,UAAU,EAAE;IACVC,MAAM,EAAE;MACNC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE;IACX,CAAC;IACDC,IAAI,EAAE;MACJC,UAAU,EAAE,SAAS;MACrBC,iBAAiB,EAAE,SAAS;MAC5BC,kBAAkB,EAAE;IACtB;EACF;AACF,CAAC;AAED,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEd,OAAA,CAACJ,cAAc;IACbmB,MAAM,EAAElB,IAAK;IACbI,KAAK,EAAEA,KAAM;IAAAe,QAAA,eAEbhB,OAAA,CAACL,aAAa;MAAAqB,QAAA,eACZhB,OAAA;QAAKiB,SAAS,EAAC,KAAK;QAAAD,QAAA,eAClBhB,OAAA,CAACF,SAAS;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAErB,CAAC;AAACC,EAAA,GAbIR,GAAa;AAenB,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}