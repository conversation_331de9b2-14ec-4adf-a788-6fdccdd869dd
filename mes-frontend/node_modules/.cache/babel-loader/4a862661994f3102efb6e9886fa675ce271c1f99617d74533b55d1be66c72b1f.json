{"ast": null, "code": "var basePickBy = require('./_basePickBy'),\n  hasIn = require('./hasIn');\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function (value, path) {\n    return hasIn(object, path);\n  });\n}\nmodule.exports = basePick;", "map": {"version": 3, "names": ["basePickBy", "require", "hasIn", "base<PERSON>ick", "object", "paths", "value", "path", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_basePick.js"], "sourcesContent": ["var basePickBy = require('./_basePickBy'),\n    hasIn = require('./hasIn');\n\n/**\n * The base implementation of `_.pick` without support for individual\n * property identifiers.\n *\n * @private\n * @param {Object} object The source object.\n * @param {string[]} paths The property paths to pick.\n * @returns {Object} Returns the new object.\n */\nfunction basePick(object, paths) {\n  return basePickBy(object, paths, function(value, path) {\n    return hasIn(object, path);\n  });\n}\n\nmodule.exports = basePick;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAC/B,OAAOL,UAAU,CAACI,MAAM,EAAEC,KAAK,EAAE,UAASC,KAAK,EAAEC,IAAI,EAAE;IACrD,OAAOL,KAAK,CAACE,MAAM,EAAEG,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ;AAEAC,MAAM,CAACC,OAAO,GAAGN,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}