{"ast": null, "code": "import { Sunburst } from \"./mark/sunburst\";\nimport { DrillDown } from \"./interaction/drillDown\";\nexport function plotlib() {\n  return {\n    \"interaction.drillDown\": DrillDown,\n    \"mark.sunburst\": Sunburst\n  };\n}", "map": {"version": 3, "names": ["Sunburst", "DrillDown", "plotlib"], "sources": ["lib.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,SAAS,QAAQ,yBAAyB;AAEnD,OAAM,SAAUC,OAAOA,CAAA;EACrB,OAAO;IACL,uBAAuB,EAAED,SAAS;IAClC,eAAe,EAAED;GACT;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}