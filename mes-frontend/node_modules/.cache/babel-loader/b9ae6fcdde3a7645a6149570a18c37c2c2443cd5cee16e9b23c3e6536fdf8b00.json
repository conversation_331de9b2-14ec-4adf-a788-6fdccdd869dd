{"ast": null, "code": "import { paramsCount } from '../parser/params-count';\n/**\n * Iterates an array to check if it's an actual `PathArray`.\n */\nexport function isPathArray(path) {\n  return Array.isArray(path) && path.every(function (seg) {\n    var lk = seg[0].toLowerCase();\n    return paramsCount[lk] === seg.length - 1 && 'achlmqstvz'.includes(lk);\n  });\n}", "map": {"version": 3, "names": ["paramsCount", "isPathArray", "path", "Array", "isArray", "every", "seg", "lk", "toLowerCase", "length", "includes"], "sources": ["path/util/is-path-array.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,WAAW,QAAQ,wBAAwB;AAGpD;;;AAGA,OAAM,SAAUC,WAAWA,CAACC,IAAwB;EAClD,OACEC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,IACnBA,IAAI,CAACG,KAAK,CAAC,UAACC,GAAG;IACb,IAAMC,EAAE,GAAGD,GAAG,CAAC,CAAC,CAAC,CAACE,WAAW,EAAE;IAC/B,OAAOR,WAAW,CAACO,EAAE,CAAC,KAAKD,GAAG,CAACG,MAAM,GAAG,CAAC,IAAI,YAAY,CAACC,QAAQ,CAACH,EAAE,CAAC;EACxE,CAAC,CAAC;AAEN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}