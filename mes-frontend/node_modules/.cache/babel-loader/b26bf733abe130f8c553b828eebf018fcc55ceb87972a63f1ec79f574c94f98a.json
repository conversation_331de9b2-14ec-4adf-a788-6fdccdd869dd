{"ast": null, "code": "export { default as quadtree } from \"./quadtree.js\";", "map": {"version": 3, "names": ["default", "quadtree"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-quadtree/src/index.js"], "sourcesContent": ["export {default as quadtree} from \"./quadtree.js\";\n"], "mappings": "AAAA,SAAQA,OAAO,IAAIC,QAAQ,QAAO,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}