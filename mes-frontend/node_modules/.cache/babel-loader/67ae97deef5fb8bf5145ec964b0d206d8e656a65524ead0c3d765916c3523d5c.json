{"ast": null, "code": "export { Image } from '@antv/g';", "map": {"version": 3, "names": ["Image"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Image.ts"], "sourcesContent": ["import type { ImageStyleProps as GImageStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Image } from '@antv/g';\nexport type ImageStyleProps = OmitConflictStyleProps<GImageStyleProps>;\n"], "mappings": "AAGA,SAASA,KAAK,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}