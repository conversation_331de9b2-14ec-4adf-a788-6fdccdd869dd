{"ast": null, "code": "import axios from 'axios';\nimport { message } from 'antd';\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://192.168.2.11:3000/api',\n  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求重试配置\nconst MAX_RETRY_COUNT = 3;\nconst RETRY_DELAY = 1000;\n\n// 重试函数\nconst retryRequest = async (error, retryCount = 0) => {\n  if (retryCount >= MAX_RETRY_COUNT) {\n    throw error;\n  }\n\n  // 只对网络错误或5xx错误进行重试\n  if (!error.response || error.response.status >= 500 && error.response.status < 600) {\n    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retryCount + 1)));\n    return api.request(error.config);\n  }\n  throw error;\n};\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 添加认证token\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  // 统一错误处理\n  if (error.response) {\n    const {\n      status,\n      data\n    } = error.response;\n    switch (status) {\n      case 401:\n        message.error('登录已过期，请重新登录');\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        window.location.href = '/login';\n        break;\n      case 403:\n        message.error('没有权限访问此资源');\n        break;\n      case 404:\n        message.error('请求的资源不存在');\n        break;\n      case 500:\n        message.error('服务器内部错误');\n        break;\n      default:\n        message.error((data === null || data === void 0 ? void 0 : data.message) || '请求失败');\n    }\n  } else if (error.request) {\n    message.error('网络连接失败，请检查网络');\n  } else {\n    message.error('请求配置错误');\n  }\n  return Promise.reject(error);\n});\n\n// 通用API方法\nexport const apiRequest = {\n  get: (url, params) => api.get(url, {\n    params\n  }).then(res => res.data),\n  post: (url, data) => api.post(url, data).then(res => res.data),\n  put: (url, data) => api.put(url, data).then(res => res.data),\n  delete: url => api.delete(url).then(res => res.data),\n  patch: (url, data) => api.patch(url, data).then(res => res.data)\n};\n\n// 健康检查\nexport const healthCheck = () => apiRequest.get('/health');\nexport default api;", "map": {"version": 3, "names": ["axios", "message", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "parseInt", "REACT_APP_REQUEST_TIMEOUT", "headers", "MAX_RETRY_COUNT", "RETRY_DELAY", "retryRequest", "error", "retryCount", "response", "status", "Promise", "resolve", "setTimeout", "request", "config", "interceptors", "use", "token", "localStorage", "getItem", "Authorization", "reject", "data", "removeItem", "window", "location", "href", "apiRequest", "get", "url", "params", "then", "res", "post", "put", "delete", "patch", "healthCheck"], "sources": ["/root/mes-system/mes-frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\nimport { message, notification } from 'antd';\nimport { ApiResponse } from '../types';\n\n// 创建axios实例\nconst api: AxiosInstance = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://192.168.2.11:3000/api',\n  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求重试配置\nconst MAX_RETRY_COUNT = 3;\nconst RETRY_DELAY = 1000;\n\n// 重试函数\nconst retryRequest = async (error: AxiosError, retryCount = 0): Promise<any> => {\n  if (retryCount >= MAX_RETRY_COUNT) {\n    throw error;\n  }\n\n  // 只对网络错误或5xx错误进行重试\n  if (!error.response || (error.response.status >= 500 && error.response.status < 600)) {\n    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retryCount + 1)));\n    return api.request(error.config!);\n  }\n\n  throw error;\n};\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 添加认证token\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response;\n  },\n  (error) => {\n    // 统一错误处理\n    if (error.response) {\n      const { status, data } = error.response;\n      \n      switch (status) {\n        case 401:\n          message.error('登录已过期，请重新登录');\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          window.location.href = '/login';\n          break;\n        case 403:\n          message.error('没有权限访问此资源');\n          break;\n        case 404:\n          message.error('请求的资源不存在');\n          break;\n        case 500:\n          message.error('服务器内部错误');\n          break;\n        default:\n          message.error(data?.message || '请求失败');\n      }\n    } else if (error.request) {\n      message.error('网络连接失败，请检查网络');\n    } else {\n      message.error('请求配置错误');\n    }\n    \n    return Promise.reject(error);\n  }\n);\n\n// 通用API方法\nexport const apiRequest = {\n  get: <T>(url: string, params?: any): Promise<ApiResponse<T>> =>\n    api.get(url, { params }).then(res => res.data),\n    \n  post: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>\n    api.post(url, data).then(res => res.data),\n    \n  put: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>\n    api.put(url, data).then(res => res.data),\n    \n  delete: <T>(url: string): Promise<ApiResponse<T>> =>\n    api.delete(url).then(res => res.data),\n    \n  patch: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>\n    api.patch(url, data).then(res => res.data),\n};\n\n// 健康检查\nexport const healthCheck = () => apiRequest.get('/health');\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAoD,OAAO;AACvE,SAASC,OAAO,QAAsB,MAAM;AAG5C;AACA,MAAMC,GAAkB,GAAGF,KAAK,CAACG,MAAM,CAAC;EACtCC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;EACxEC,OAAO,EAAEC,QAAQ,CAACJ,OAAO,CAACC,GAAG,CAACI,yBAAyB,IAAI,OAAO,CAAC;EACnEC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,WAAW,GAAG,IAAI;;AAExB;AACA,MAAMC,YAAY,GAAG,MAAAA,CAAOC,KAAiB,EAAEC,UAAU,GAAG,CAAC,KAAmB;EAC9E,IAAIA,UAAU,IAAIJ,eAAe,EAAE;IACjC,MAAMG,KAAK;EACb;;EAEA;EACA,IAAI,CAACA,KAAK,CAACE,QAAQ,IAAKF,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI,GAAG,IAAIH,KAAK,CAACE,QAAQ,CAACC,MAAM,GAAG,GAAI,EAAE;IACpF,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEP,WAAW,IAAIG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IACjF,OAAOd,GAAG,CAACoB,OAAO,CAACP,KAAK,CAACQ,MAAO,CAAC;EACnC;EAEA,MAAMR,KAAK;AACb,CAAC;;AAED;AACAb,GAAG,CAACsB,YAAY,CAACF,OAAO,CAACG,GAAG,CACzBF,MAAM,IAAK;EACV;EACA,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTH,MAAM,CAACZ,OAAO,CAACkB,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOH,MAAM;AACf,CAAC,EACAR,KAAK,IAAK;EACT,OAAOI,OAAO,CAACW,MAAM,CAACf,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACsB,YAAY,CAACP,QAAQ,CAACQ,GAAG,CAC1BR,QAAuB,IAAK;EAC3B,OAAOA,QAAQ;AACjB,CAAC,EACAF,KAAK,IAAK;EACT;EACA,IAAIA,KAAK,CAACE,QAAQ,EAAE;IAClB,MAAM;MAAEC,MAAM;MAAEa;IAAK,CAAC,GAAGhB,KAAK,CAACE,QAAQ;IAEvC,QAAQC,MAAM;MACZ,KAAK,GAAG;QACNjB,OAAO,CAACc,KAAK,CAAC,aAAa,CAAC;QAC5BY,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;QAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;QAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC/B;MACF,KAAK,GAAG;QACNlC,OAAO,CAACc,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF,KAAK,GAAG;QACNd,OAAO,CAACc,KAAK,CAAC,UAAU,CAAC;QACzB;MACF,KAAK,GAAG;QACNd,OAAO,CAACc,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;QACEd,OAAO,CAACc,KAAK,CAAC,CAAAgB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE9B,OAAO,KAAI,MAAM,CAAC;IAC1C;EACF,CAAC,MAAM,IAAIc,KAAK,CAACO,OAAO,EAAE;IACxBrB,OAAO,CAACc,KAAK,CAAC,cAAc,CAAC;EAC/B,CAAC,MAAM;IACLd,OAAO,CAACc,KAAK,CAAC,QAAQ,CAAC;EACzB;EAEA,OAAOI,OAAO,CAACW,MAAM,CAACf,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAMqB,UAAU,GAAG;EACxBC,GAAG,EAAEA,CAAIC,GAAW,EAAEC,MAAY,KAChCrC,GAAG,CAACmC,GAAG,CAACC,GAAG,EAAE;IAAEC;EAAO,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACV,IAAI,CAAC;EAEhDW,IAAI,EAAEA,CAAIJ,GAAW,EAAEP,IAAU,KAC/B7B,GAAG,CAACwC,IAAI,CAACJ,GAAG,EAAEP,IAAI,CAAC,CAACS,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACV,IAAI,CAAC;EAE3CY,GAAG,EAAEA,CAAIL,GAAW,EAAEP,IAAU,KAC9B7B,GAAG,CAACyC,GAAG,CAACL,GAAG,EAAEP,IAAI,CAAC,CAACS,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACV,IAAI,CAAC;EAE1Ca,MAAM,EAAMN,GAAW,IACrBpC,GAAG,CAAC0C,MAAM,CAACN,GAAG,CAAC,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACV,IAAI,CAAC;EAEvCc,KAAK,EAAEA,CAAIP,GAAW,EAAEP,IAAU,KAChC7B,GAAG,CAAC2C,KAAK,CAACP,GAAG,EAAEP,IAAI,CAAC,CAACS,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACV,IAAI;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMe,WAAW,GAAGA,CAAA,KAAMV,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC;AAE1D,eAAenC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}