{"ast": null, "code": "export {};", "map": {"version": 3, "names": [], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/core/types.ts"], "sourcesContent": ["/* global KeyframeAnimationOptions */\nimport type { GenericAnimation, StandardAnimationOption } from '../animation';\nimport type { DisplayObjectConfig } from '../shapes';\nimport type { Callable } from '../types';\n\nexport interface ComponentStyleProps<T extends Record<string, any>> {\n  layout?: T extends { layout: infer L } ? L : Record<string, any>;\n  events?: T extends { events: infer E } ? E : Record<string, any>;\n  style?: T extends { style: infer S } ? S : Record<string, any>;\n  animation?: GenericAnimation | StandardAnimationOption;\n  interactions?: T extends { interaction: infer I } ? I : Record<string, any>;\n  [key: string]: any;\n}\n\nexport type ComponentOptions<T> = DisplayObjectConfig<T>;\n\n/** add prefix for object property key  */\nexport type PrefixStyleProps<T extends Record<string, any>, P extends string> = {\n  [K in keyof T as K extends `show${infer S}`\n    ? `show${Capitalize<P>}${S}`\n    : K extends string\n    ? `${P}${Capitalize<K>}`\n    : never]: T[K];\n};\n\nexport type CallableStyleProps<T extends Record<string, any>, P extends any[]> = {\n  [K in keyof T]: Callable<T[K], P>;\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}