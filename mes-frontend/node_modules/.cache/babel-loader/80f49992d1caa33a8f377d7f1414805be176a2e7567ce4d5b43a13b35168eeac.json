{"ast": null, "code": "import pointEqual from \"../pointEqual.js\";\nimport { epsilon } from \"../math.js\";\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\nexport default function (segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n    clip = [],\n    i,\n    n;\n  segments.forEach(function (segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n,\n      p0 = segment[0],\n      p1 = segment[n],\n      x;\n    if (pointEqual(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * epsilon;\n    }\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n  if (!subject.length) return;\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n  var start = subject[0],\n    points,\n    point;\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n      isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n    i = 0,\n    a = array[0],\n    b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}", "map": {"version": 3, "names": ["pointEqual", "epsilon", "Intersection", "point", "points", "other", "entry", "x", "z", "o", "e", "v", "n", "p", "segments", "compareIntersection", "startInside", "interpolate", "stream", "subject", "clip", "i", "for<PERSON>ach", "segment", "length", "p0", "p1", "lineStart", "lineEnd", "push", "sort", "link", "start", "current", "isSubject", "array", "a", "b"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-geo/src/clip/rejoin.js"], "sourcesContent": ["import pointEqual from \"../pointEqual.js\";\nimport {epsilon} from \"../math.js\";\n\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\nexport default function(segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n      clip = [],\n      i,\n      n;\n\n  segments.forEach(function(segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n, p0 = segment[0], p1 = segment[n], x;\n\n    if (pointEqual(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * epsilon;\n    }\n\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n\n  if (!subject.length) return;\n\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n\n  var start = subject[0],\n      points,\n      point;\n\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n        isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\n\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n      i = 0,\n      a = array[0],\n      b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,SAAQC,OAAO,QAAO,YAAY;AAElC,SAASC,YAAYA,CAACC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACjD,IAAI,CAACC,CAAC,GAAGJ,KAAK;EACd,IAAI,CAACK,CAAC,GAAGJ,MAAM;EACf,IAAI,CAACK,CAAC,GAAGJ,KAAK,CAAC,CAAC;EAChB,IAAI,CAACK,CAAC,GAAGJ,KAAK,CAAC,CAAC;EAChB,IAAI,CAACK,CAAC,GAAG,KAAK,CAAC,CAAC;EAChB,IAAI,CAACC,CAAC,GAAG,IAAI,CAACC,CAAC,GAAG,IAAI,CAAC,CAAC;AAC1B;;AAEA;AACA;AACA;AACA,eAAe,UAASC,QAAQ,EAAEC,mBAAmB,EAAEC,WAAW,EAAEC,WAAW,EAAEC,MAAM,EAAE;EACvF,IAAIC,OAAO,GAAG,EAAE;IACZC,IAAI,GAAG,EAAE;IACTC,CAAC;IACDT,CAAC;EAELE,QAAQ,CAACQ,OAAO,CAAC,UAASC,OAAO,EAAE;IACjC,IAAI,CAACX,CAAC,GAAGW,OAAO,CAACC,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACnC,IAAIZ,CAAC;MAAEa,EAAE,GAAGF,OAAO,CAAC,CAAC,CAAC;MAAEG,EAAE,GAAGH,OAAO,CAACX,CAAC,CAAC;MAAEL,CAAC;IAE1C,IAAIP,UAAU,CAACyB,EAAE,EAAEC,EAAE,CAAC,EAAE;MACtB,IAAI,CAACD,EAAE,CAAC,CAAC,CAAC,IAAI,CAACC,EAAE,CAAC,CAAC,CAAC,EAAE;QACpBR,MAAM,CAACS,SAAS,CAAC,CAAC;QAClB,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,CAAC,EAAE,EAAES,CAAC,EAAEH,MAAM,CAACf,KAAK,CAAC,CAACsB,EAAE,GAAGF,OAAO,CAACF,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEI,EAAE,CAAC,CAAC,CAAC,CAAC;QACjEP,MAAM,CAACU,OAAO,CAAC,CAAC;QAChB;MACF;MACA;MACAF,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGzB,OAAO;IACtB;IAEAkB,OAAO,CAACU,IAAI,CAACtB,CAAC,GAAG,IAAIL,YAAY,CAACuB,EAAE,EAAEF,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;IAC3DH,IAAI,CAACS,IAAI,CAACtB,CAAC,CAACE,CAAC,GAAG,IAAIP,YAAY,CAACuB,EAAE,EAAE,IAAI,EAAElB,CAAC,EAAE,KAAK,CAAC,CAAC;IACrDY,OAAO,CAACU,IAAI,CAACtB,CAAC,GAAG,IAAIL,YAAY,CAACwB,EAAE,EAAEH,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;IAC5DH,IAAI,CAACS,IAAI,CAACtB,CAAC,CAACE,CAAC,GAAG,IAAIP,YAAY,CAACwB,EAAE,EAAE,IAAI,EAAEnB,CAAC,EAAE,IAAI,CAAC,CAAC;EACtD,CAAC,CAAC;EAEF,IAAI,CAACY,OAAO,CAACK,MAAM,EAAE;EAErBJ,IAAI,CAACU,IAAI,CAACf,mBAAmB,CAAC;EAC9BgB,IAAI,CAACZ,OAAO,CAAC;EACbY,IAAI,CAACX,IAAI,CAAC;EAEV,KAAKC,CAAC,GAAG,CAAC,EAAET,CAAC,GAAGQ,IAAI,CAACI,MAAM,EAAEH,CAAC,GAAGT,CAAC,EAAE,EAAES,CAAC,EAAE;IACvCD,IAAI,CAACC,CAAC,CAAC,CAACX,CAAC,GAAGM,WAAW,GAAG,CAACA,WAAW;EACxC;EAEA,IAAIgB,KAAK,GAAGb,OAAO,CAAC,CAAC,CAAC;IAClBf,MAAM;IACND,KAAK;EAET,OAAO,CAAC,EAAE;IACR;IACA,IAAI8B,OAAO,GAAGD,KAAK;MACfE,SAAS,GAAG,IAAI;IACpB,OAAOD,OAAO,CAACtB,CAAC,EAAE,IAAI,CAACsB,OAAO,GAAGA,OAAO,CAACrB,CAAC,MAAMoB,KAAK,EAAE;IACvD5B,MAAM,GAAG6B,OAAO,CAACzB,CAAC;IAClBU,MAAM,CAACS,SAAS,CAAC,CAAC;IAClB,GAAG;MACDM,OAAO,CAACtB,CAAC,GAAGsB,OAAO,CAACxB,CAAC,CAACE,CAAC,GAAG,IAAI;MAC9B,IAAIsB,OAAO,CAACvB,CAAC,EAAE;QACb,IAAIwB,SAAS,EAAE;UACb,KAAKb,CAAC,GAAG,CAAC,EAAET,CAAC,GAAGR,MAAM,CAACoB,MAAM,EAAEH,CAAC,GAAGT,CAAC,EAAE,EAAES,CAAC,EAAEH,MAAM,CAACf,KAAK,CAAC,CAACA,KAAK,GAAGC,MAAM,CAACiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAElB,KAAK,CAAC,CAAC,CAAC,CAAC;QAC3F,CAAC,MAAM;UACLc,WAAW,CAACgB,OAAO,CAAC1B,CAAC,EAAE0B,OAAO,CAACrB,CAAC,CAACL,CAAC,EAAE,CAAC,EAAEW,MAAM,CAAC;QAChD;QACAe,OAAO,GAAGA,OAAO,CAACrB,CAAC;MACrB,CAAC,MAAM;QACL,IAAIsB,SAAS,EAAE;UACb9B,MAAM,GAAG6B,OAAO,CAACpB,CAAC,CAACL,CAAC;UACpB,KAAKa,CAAC,GAAGjB,MAAM,CAACoB,MAAM,GAAG,CAAC,EAAEH,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAEH,MAAM,CAACf,KAAK,CAAC,CAACA,KAAK,GAAGC,MAAM,CAACiB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAElB,KAAK,CAAC,CAAC,CAAC,CAAC;QACzF,CAAC,MAAM;UACLc,WAAW,CAACgB,OAAO,CAAC1B,CAAC,EAAE0B,OAAO,CAACpB,CAAC,CAACN,CAAC,EAAE,CAAC,CAAC,EAAEW,MAAM,CAAC;QACjD;QACAe,OAAO,GAAGA,OAAO,CAACpB,CAAC;MACrB;MACAoB,OAAO,GAAGA,OAAO,CAACxB,CAAC;MACnBL,MAAM,GAAG6B,OAAO,CAACzB,CAAC;MAClB0B,SAAS,GAAG,CAACA,SAAS;IACxB,CAAC,QAAQ,CAACD,OAAO,CAACtB,CAAC;IACnBO,MAAM,CAACU,OAAO,CAAC,CAAC;EAClB;AACF;AAEA,SAASG,IAAIA,CAACI,KAAK,EAAE;EACnB,IAAI,EAAEvB,CAAC,GAAGuB,KAAK,CAACX,MAAM,CAAC,EAAE;EACzB,IAAIZ,CAAC;IACDS,CAAC,GAAG,CAAC;IACLe,CAAC,GAAGD,KAAK,CAAC,CAAC,CAAC;IACZE,CAAC;EACL,OAAO,EAAEhB,CAAC,GAAGT,CAAC,EAAE;IACdwB,CAAC,CAACxB,CAAC,GAAGyB,CAAC,GAAGF,KAAK,CAACd,CAAC,CAAC;IAClBgB,CAAC,CAACxB,CAAC,GAAGuB,CAAC;IACPA,CAAC,GAAGC,CAAC;EACP;EACAD,CAAC,CAACxB,CAAC,GAAGyB,CAAC,GAAGF,KAAK,CAAC,CAAC,CAAC;EAClBE,CAAC,CAACxB,CAAC,GAAGuB,CAAC;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}