{"ast": null, "code": "export { Ellipse } from '@antv/g';", "map": {"version": 3, "names": ["Ellipse"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Ellipse.ts"], "sourcesContent": ["import type { EllipseStyleProps as GEllipseStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Ellipse } from '@antv/g';\nexport type EllipseStyleProps = OmitConflictStyleProps<GEllipseStyleProps>;\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}