{"ast": null, "code": "/**\n * 只要有一个满足条件就返回 true\n * @param arr\n * @param func\n */\nvar some = function (arr, func) {\n  for (var i = 0; i < arr.length; i++) {\n    if (func(arr[i], i)) return true;\n  }\n  return false;\n};\nexport default some;", "map": {"version": 3, "names": ["some", "arr", "func", "i", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/some.ts"], "sourcesContent": ["/**\n * 只要有一个满足条件就返回 true\n * @param arr\n * @param func\n */\nconst some = function <T>(arr: T[], func: (v: T, idx?: number) => any): boolean {\n  for (let i = 0; i < arr.length; i ++) {\n    if (func(arr[i], i)) return true;\n  }\n\n  return false;\n};\n\nexport default some;\n"], "mappings": "AAAA;;;;;AAKA,IAAMA,IAAI,GAAG,SAAAA,CAAaC,GAAQ,EAAEC,IAAiC;EACnE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAG,EAAE;IACpC,IAAID,IAAI,CAACD,GAAG,CAACE,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE,OAAO,IAAI;;EAGlC,OAAO,KAAK;AACd,CAAC;AAED,eAAeH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}