{"ast": null, "code": "export function constant(x) {\n  return function () {\n    return x;\n  };\n}", "map": {"version": 3, "names": ["constant", "x"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/d3-sankey/constant.ts"], "sourcesContent": ["export function constant(x: any) {\n  return function () {\n    return x;\n  };\n}\n"], "mappings": "AAAA,OAAM,SAAUA,QAAQA,CAACC,CAAM;EAC7B,OAAO;IACL,OAAOA,CAAC;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}