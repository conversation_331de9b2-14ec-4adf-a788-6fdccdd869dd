{"ast": null, "code": "export default function () {\n  var data = [];\n  this.visit(function (node) {\n    if (!node.length) do data.push(node.data); while (node = node.next);\n  });\n  return data;\n}", "map": {"version": 3, "names": ["data", "visit", "node", "length", "push", "next"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-octree/src/data.js"], "sourcesContent": ["export default function() {\n  var data = [];\n  this.visit(function(node) {\n    if (!node.length) do data.push(node.data); while (node = node.next)\n  });\n  return data;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,IAAIA,IAAI,GAAG,EAAE;EACb,IAAI,CAACC,KAAK,CAAC,UAASC,IAAI,EAAE;IACxB,IAAI,CAACA,IAAI,CAACC,MAAM,EAAE,GAAGH,IAAI,CAACI,IAAI,CAACF,IAAI,CAACF,IAAI,CAAC,CAAC,QAAQE,IAAI,GAAGA,IAAI,CAACG,IAAI;EACpE,CAAC,CAAC;EACF,OAAOL,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}