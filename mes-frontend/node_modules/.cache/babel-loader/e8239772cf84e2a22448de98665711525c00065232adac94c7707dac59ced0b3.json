{"ast": null, "code": "'use strict';\n\nimport { twoProduct } from './two-product';\nimport { fastTwoSum as twoSum } from './two-sum';\nexport function scaleLinearExpansion(e, scale) {\n  const n = e.length;\n  if (n === 1) {\n    const ts = twoProduct(e[0], scale);\n    if (ts[0]) {\n      return ts;\n    }\n    return [ts[1]];\n  }\n  const g = new Array(2 * n);\n  const q = [0.1, 0.1];\n  const t = [0.1, 0.1];\n  let count = 0;\n  twoProduct(e[0], scale, q);\n  if (q[0]) {\n    g[count++] = q[0];\n  }\n  for (let i = 1; i < n; ++i) {\n    twoProduct(e[i], scale, t);\n    const pq = q[1];\n    twoSum(pq, t[0], q);\n    if (q[0]) {\n      g[count++] = q[0];\n    }\n    const a = t[1];\n    const b = q[1];\n    const x = a + b;\n    const bv = x - a;\n    const y = b - bv;\n    q[1] = x;\n    if (y) {\n      g[count++] = y;\n    }\n  }\n  if (q[1]) {\n    g[count++] = q[1];\n  }\n  if (count === 0) {\n    g[count++] = 0.0;\n  }\n  g.length = count;\n  return g;\n}", "map": {"version": 3, "names": ["twoProduct", "fastTwoSum", "twoSum", "scaleLinearExpansion", "e", "scale", "n", "length", "ts", "g", "Array", "q", "t", "count", "i", "pq", "a", "b", "x", "bv", "y"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g6/src/plugins/hull/hull/robust-scale.ts"], "sourcesContent": ["'use strict';\n\nimport { twoProduct } from './two-product';\nimport { fastTwoSum as twoSum } from './two-sum';\n\nexport function scaleLinearExpansion(e: number[], scale: number): number[] {\n  const n = e.length;\n\n  if (n === 1) {\n    const ts = twoProduct(e[0], scale);\n    if (ts[0]) {\n      return ts;\n    }\n    return [ts[1]];\n  }\n\n  const g = new Array(2 * n);\n  const q: [number, number] = [0.1, 0.1];\n  const t: [number, number] = [0.1, 0.1];\n  let count = 0;\n\n  twoProduct(e[0], scale, q);\n  if (q[0]) {\n    g[count++] = q[0];\n  }\n\n  for (let i = 1; i < n; ++i) {\n    twoProduct(e[i], scale, t);\n    const pq = q[1];\n    twoSum(pq, t[0], q);\n    if (q[0]) {\n      g[count++] = q[0];\n    }\n    const a = t[1];\n    const b = q[1];\n    const x = a + b;\n    const bv = x - a;\n    const y = b - bv;\n    q[1] = x;\n    if (y) {\n      g[count++] = y;\n    }\n  }\n\n  if (q[1]) {\n    g[count++] = q[1];\n  }\n  if (count === 0) {\n    g[count++] = 0.0;\n  }\n  g.length = count;\n  return g;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,IAAIC,MAAM,QAAQ,WAAW;AAEhD,OAAM,SAAUC,oBAAoBA,CAACC,CAAW,EAAEC,KAAa;EAC7D,MAAMC,CAAC,GAAGF,CAAC,CAACG,MAAM;EAElB,IAAID,CAAC,KAAK,CAAC,EAAE;IACX,MAAME,EAAE,GAAGR,UAAU,CAACI,CAAC,CAAC,CAAC,CAAC,EAAEC,KAAK,CAAC;IAClC,IAAIG,EAAE,CAAC,CAAC,CAAC,EAAE;MACT,OAAOA,EAAE;IACX;IACA,OAAO,CAACA,EAAE,CAAC,CAAC,CAAC,CAAC;EAChB;EAEA,MAAMC,CAAC,GAAG,IAAIC,KAAK,CAAC,CAAC,GAAGJ,CAAC,CAAC;EAC1B,MAAMK,CAAC,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC;EACtC,MAAMC,CAAC,GAAqB,CAAC,GAAG,EAAE,GAAG,CAAC;EACtC,IAAIC,KAAK,GAAG,CAAC;EAEbb,UAAU,CAACI,CAAC,CAAC,CAAC,CAAC,EAAEC,KAAK,EAAEM,CAAC,CAAC;EAC1B,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;IACRF,CAAC,CAACI,KAAK,EAAE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;EACnB;EAEA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,CAAC,EAAE,EAAEQ,CAAC,EAAE;IAC1Bd,UAAU,CAACI,CAAC,CAACU,CAAC,CAAC,EAAET,KAAK,EAAEO,CAAC,CAAC;IAC1B,MAAMG,EAAE,GAAGJ,CAAC,CAAC,CAAC,CAAC;IACfT,MAAM,CAACa,EAAE,EAAEH,CAAC,CAAC,CAAC,CAAC,EAAED,CAAC,CAAC;IACnB,IAAIA,CAAC,CAAC,CAAC,CAAC,EAAE;MACRF,CAAC,CAACI,KAAK,EAAE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;IACnB;IACA,MAAMK,CAAC,GAAGJ,CAAC,CAAC,CAAC,CAAC;IACd,MAAMK,CAAC,GAAGN,CAAC,CAAC,CAAC,CAAC;IACd,MAAMO,CAAC,GAAGF,CAAC,GAAGC,CAAC;IACf,MAAME,EAAE,GAAGD,CAAC,GAAGF,CAAC;IAChB,MAAMI,CAAC,GAAGH,CAAC,GAAGE,EAAE;IAChBR,CAAC,CAAC,CAAC,CAAC,GAAGO,CAAC;IACR,IAAIE,CAAC,EAAE;MACLX,CAAC,CAACI,KAAK,EAAE,CAAC,GAAGO,CAAC;IAChB;EACF;EAEA,IAAIT,CAAC,CAAC,CAAC,CAAC,EAAE;IACRF,CAAC,CAACI,KAAK,EAAE,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC;EACnB;EACA,IAAIE,KAAK,KAAK,CAAC,EAAE;IACfJ,CAAC,CAACI,KAAK,EAAE,CAAC,GAAG,GAAG;EAClB;EACAJ,CAAC,CAACF,MAAM,GAAGM,KAAK;EAChB,OAAOJ,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}