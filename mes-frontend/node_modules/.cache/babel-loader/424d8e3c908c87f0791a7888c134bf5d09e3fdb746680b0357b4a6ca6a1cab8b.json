{"ast": null, "code": "var fixedBase = function (v, base) {\n  var str = base.toString();\n  var index = str.indexOf('.');\n  if (index === -1) {\n    return Math.round(v);\n  }\n  var length = str.substr(index + 1).length;\n  if (length > 20) {\n    length = 20;\n  }\n  return parseFloat(v.toFixed(length));\n};\nexport default fixedBase;", "map": {"version": 3, "names": ["fixedBase", "v", "base", "str", "toString", "index", "indexOf", "Math", "round", "length", "substr", "parseFloat", "toFixed"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/fixed-base.ts"], "sourcesContent": ["const fixedBase = function(v: number, base: number | string): number {\n  const str = base.toString();\n  const index = str.indexOf('.');\n  if (index === -1) {\n    return Math.round(v);\n  }\n  let length = str.substr(index + 1).length;\n  if (length > 20) {\n    length = 20;\n  }\n  return parseFloat(v.toFixed(length));\n};\n\nexport default fixedBase;\n"], "mappings": "AAAA,IAAMA,SAAS,GAAG,SAAAA,CAASC,CAAS,EAAEC,IAAqB;EACzD,IAAMC,GAAG,GAAGD,IAAI,CAACE,QAAQ,EAAE;EAC3B,IAAMC,KAAK,GAAGF,GAAG,CAACG,OAAO,CAAC,GAAG,CAAC;EAC9B,IAAID,KAAK,KAAK,CAAC,CAAC,EAAE;IAChB,OAAOE,IAAI,CAACC,KAAK,CAACP,CAAC,CAAC;;EAEtB,IAAIQ,MAAM,GAAGN,GAAG,CAACO,MAAM,CAACL,KAAK,GAAG,CAAC,CAAC,CAACI,MAAM;EACzC,IAAIA,MAAM,GAAG,EAAE,EAAE;IACfA,MAAM,GAAG,EAAE;;EAEb,OAAOE,UAAU,CAACV,CAAC,CAACW,OAAO,CAACH,MAAM,CAAC,CAAC;AACtC,CAAC;AAED,eAAeT,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}