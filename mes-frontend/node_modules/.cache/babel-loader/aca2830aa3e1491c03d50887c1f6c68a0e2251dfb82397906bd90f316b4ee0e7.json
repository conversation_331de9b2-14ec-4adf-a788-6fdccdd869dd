{"ast": null, "code": "export default function (d) {\n  const x = +this._x.call(null, d);\n  return add(this.cover(x), x, d);\n}\nfunction add(tree, x, d) {\n  if (isNaN(x)) return tree; // ignore invalid points\n\n  var parent,\n    node = tree._root,\n    leaf = {\n      data: d\n    },\n    x0 = tree._x0,\n    x1 = tree._x1,\n    xm,\n    xp,\n    right,\n    i,\n    j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (parent = node, !(node = node[i = +right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  if (x === xp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(2) : tree._root = new Array(2);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n  } while ((i = +right) === (j = +(xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\nexport function addAll(data) {\n  if (!Array.isArray(data)) data = Array.from(data);\n  const n = data.length;\n  const xz = new Float64Array(n);\n  let x0 = Infinity,\n    x1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (let i = 0, x; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, data[i]))) continue;\n    xz[i] = x;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0).cover(x1);\n\n  // Add the new points.\n  for (let i = 0; i < n; ++i) {\n    add(this, xz[i], data[i]);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["d", "x", "_x", "call", "add", "cover", "tree", "isNaN", "parent", "node", "_root", "leaf", "data", "x0", "_x0", "x1", "_x1", "xm", "xp", "right", "i", "j", "length", "next", "Array", "addAll", "isArray", "from", "n", "xz", "Float64Array", "Infinity"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-binarytree/src/add.js"], "sourcesContent": ["export default function(d) {\n  const x = +this._x.call(null, d);\n  return add(this.cover(x), x, d);\n}\n\nfunction add(tree, x, d) {\n  if (isNaN(x)) return tree; // ignore invalid points\n\n  var parent,\n      node = tree._root,\n      leaf = {data: d},\n      x0 = tree._x0,\n      x1 = tree._x1,\n      xm,\n      xp,\n      right,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (parent = node, !(node = node[i = +right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  if (x === xp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(2) : tree._root = new Array(2);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n  } while ((i = +right) === (j = +(xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\n\nexport function addAll(data) {\n  if (!Array.isArray(data)) data = Array.from(data);\n  const n = data.length;\n  const xz = new Float64Array(n);\n  let x0 = Infinity,\n      x1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (let i = 0, x; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, data[i]))) continue;\n    xz[i] = x;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0).cover(x1);\n\n  // Add the new points.\n  for (let i = 0; i < n; ++i) {\n    add(this, xz[i], data[i]);\n  }\n\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,MAAMC,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEH,CAAC,CAAC;EAChC,OAAOI,GAAG,CAAC,IAAI,CAACC,KAAK,CAACJ,CAAC,CAAC,EAAEA,CAAC,EAAED,CAAC,CAAC;AACjC;AAEA,SAASI,GAAGA,CAACE,IAAI,EAAEL,CAAC,EAAED,CAAC,EAAE;EACvB,IAAIO,KAAK,CAACN,CAAC,CAAC,EAAE,OAAOK,IAAI,CAAC,CAAC;;EAE3B,IAAIE,MAAM;IACNC,IAAI,GAAGH,IAAI,CAACI,KAAK;IACjBC,IAAI,GAAG;MAACC,IAAI,EAAEZ;IAAC,CAAC;IAChBa,EAAE,GAAGP,IAAI,CAACQ,GAAG;IACbC,EAAE,GAAGT,IAAI,CAACU,GAAG;IACbC,EAAE;IACFC,EAAE;IACFC,KAAK;IACLC,CAAC;IACDC,CAAC;;EAEL;EACA,IAAI,CAACZ,IAAI,EAAE,OAAOH,IAAI,CAACI,KAAK,GAAGC,IAAI,EAAEL,IAAI;;EAEzC;EACA,OAAOG,IAAI,CAACa,MAAM,EAAE;IAClB,IAAIH,KAAK,GAAGlB,CAAC,KAAKgB,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC,CAAC,EAAEF,EAAE,GAAGI,EAAE,CAAC,KAAMF,EAAE,GAAGE,EAAE;IAC5D,IAAIT,MAAM,GAAGC,IAAI,EAAE,EAAEA,IAAI,GAAGA,IAAI,CAACW,CAAC,GAAG,CAACD,KAAK,CAAC,CAAC,EAAE,OAAOX,MAAM,CAACY,CAAC,CAAC,GAAGT,IAAI,EAAEL,IAAI;EAC9E;;EAEA;EACAY,EAAE,GAAG,CAACZ,IAAI,CAACJ,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEM,IAAI,CAACG,IAAI,CAAC;EACnC,IAAIX,CAAC,KAAKiB,EAAE,EAAE,OAAOP,IAAI,CAACY,IAAI,GAAGd,IAAI,EAAED,MAAM,GAAGA,MAAM,CAACY,CAAC,CAAC,GAAGT,IAAI,GAAGL,IAAI,CAACI,KAAK,GAAGC,IAAI,EAAEL,IAAI;;EAE1F;EACA,GAAG;IACDE,MAAM,GAAGA,MAAM,GAAGA,MAAM,CAACY,CAAC,CAAC,GAAG,IAAII,KAAK,CAAC,CAAC,CAAC,GAAGlB,IAAI,CAACI,KAAK,GAAG,IAAIc,KAAK,CAAC,CAAC,CAAC;IACtE,IAAIL,KAAK,GAAGlB,CAAC,KAAKgB,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC,CAAC,EAAEF,EAAE,GAAGI,EAAE,CAAC,KAAMF,EAAE,GAAGE,EAAE;EAC9D,CAAC,QAAQ,CAACG,CAAC,GAAG,CAACD,KAAK,OAAOE,CAAC,GAAG,EAAEH,EAAE,IAAID,EAAE,CAAC,CAAC;EAC3C,OAAOT,MAAM,CAACa,CAAC,CAAC,GAAGZ,IAAI,EAAED,MAAM,CAACY,CAAC,CAAC,GAAGT,IAAI,EAAEL,IAAI;AACjD;AAEA,OAAO,SAASmB,MAAMA,CAACb,IAAI,EAAE;EAC3B,IAAI,CAACY,KAAK,CAACE,OAAO,CAACd,IAAI,CAAC,EAAEA,IAAI,GAAGY,KAAK,CAACG,IAAI,CAACf,IAAI,CAAC;EACjD,MAAMgB,CAAC,GAAGhB,IAAI,CAACU,MAAM;EACrB,MAAMO,EAAE,GAAG,IAAIC,YAAY,CAACF,CAAC,CAAC;EAC9B,IAAIf,EAAE,GAAGkB,QAAQ;IACbhB,EAAE,GAAG,CAACgB,QAAQ;;EAElB;EACA,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEnB,CAAC,EAAEmB,CAAC,GAAGQ,CAAC,EAAE,EAAER,CAAC,EAAE;IAC7B,IAAIb,KAAK,CAACN,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAES,IAAI,CAACQ,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7CS,EAAE,CAACT,CAAC,CAAC,GAAGnB,CAAC;IACT,IAAIA,CAAC,GAAGY,EAAE,EAAEA,EAAE,GAAGZ,CAAC;IAClB,IAAIA,CAAC,GAAGc,EAAE,EAAEA,EAAE,GAAGd,CAAC;EACpB;;EAEA;EACA,IAAIY,EAAE,GAAGE,EAAE,EAAE,OAAO,IAAI;;EAExB;EACA,IAAI,CAACV,KAAK,CAACQ,EAAE,CAAC,CAACR,KAAK,CAACU,EAAE,CAAC;;EAExB;EACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,CAAC,EAAE,EAAER,CAAC,EAAE;IAC1BhB,GAAG,CAAC,IAAI,EAAEyB,EAAE,CAACT,CAAC,CAAC,EAAER,IAAI,CAACQ,CAAC,CAAC,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}