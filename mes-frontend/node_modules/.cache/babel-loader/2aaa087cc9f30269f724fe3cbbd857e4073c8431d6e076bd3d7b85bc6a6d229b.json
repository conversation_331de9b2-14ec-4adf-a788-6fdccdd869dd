{"ast": null, "code": "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { isFunction } from '@antv/util';\nimport { fadeOut, onAnimateFinished, onAnimatesFinished, transition, transitionShape } from '../../../animation';\nimport { add, ellipsisIt, getCallbackValue, hide, inRange, percentTransform, radToDeg, renderExtDo, scale, select, show, splitStyle, subStyleProps, wrapIt } from '../../../util';\nimport { CLASS_NAMES } from '../constant';\nimport { processOverlap } from '../overlap';\nimport { getFactor } from '../utils';\nimport { getValuePos } from './line';\nimport { filterExec, getCallbackStyle, getLabelVector, getLineTangentVector } from './utils';\nfunction angleNormalizer(angle) {\n  var normalizedAngle = angle;\n  while (normalizedAngle < 0) normalizedAngle += 360;\n  return Math.round(normalizedAngle % 360);\n}\nfunction getAngle(v1, v2) {\n  var _a = __read(v1, 2),\n    x1 = _a[0],\n    y1 = _a[1];\n  var _b = __read(v2, 2),\n    x2 = _b[0],\n    y2 = _b[1];\n  var _c = __read([x1 * x2 + y1 * y2, x1 * y2 - y1 * x2], 2),\n    dot = _c[0],\n    det = _c[1];\n  return Math.atan2(det, dot);\n}\n/** to correct label rotation to avoid inverted character */\nfunction correctLabelRotation(_rotate) {\n  var rotate = (_rotate + 360) % 180;\n  if (!inRange(rotate, -90, 90)) rotate += 180;\n  return rotate;\n}\n/** get rotation from preset or layout */\nfunction getLabelRotation(datum, label, attr) {\n  var _a;\n  var labelAlign = attr.labelAlign;\n  // if label rotate is set, use it\n  var customRotate = (_a = label.style.transform) === null || _a === void 0 ? void 0 : _a.includes('rotate');\n  if (customRotate) return label.getLocalEulerAngles();\n  var rotate = 0;\n  var labelVector = getLabelVector(datum.value, attr);\n  var tangentVector = getLineTangentVector(datum.value, attr);\n  if (labelAlign === 'horizontal') return 0;\n  if (labelAlign === 'perpendicular') rotate = getAngle([1, 0], labelVector);else rotate = getAngle([tangentVector[0] < 0 ? -1 : 1, 0], tangentVector);\n  return correctLabelRotation(radToDeg(rotate));\n}\n/** get the label align according to its tick and label angle  */\nfunction getLabelStyle(value, rotate, attr) {\n  var type = attr.type,\n    labelAlign = attr.labelAlign;\n  var labelVector = getLabelVector(value, attr);\n  var labelAngle = angleNormalizer(rotate);\n  var tickAngle = angleNormalizer(radToDeg(getAngle([1, 0], labelVector)));\n  var textAlign = 'center';\n  var textBaseline = 'middle';\n  if (type === 'linear') {\n    // tick 和 label 均为水平或垂直时，做快速判断\n    if ([90, 270].includes(tickAngle) && labelAngle === 0) {\n      textAlign = 'center';\n      textBaseline = labelVector[1] === 1 ? 'top' : 'bottom';\n    } else if (!(tickAngle % 180) && [90, 270].includes(labelAngle)) {\n      textAlign = 'center';\n    }\n    // 根据 tick 和 label 的角度，判断 label 的对齐方式\n    else if (tickAngle === 0) {\n      if (inRange(labelAngle, 0, 90, false, true)) {\n        textAlign = 'start';\n      } else if (inRange(labelAngle, 0, 90) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'start';\n      }\n    } else if (tickAngle === 90) {\n      if (inRange(labelAngle, 0, 90, false, true)) {\n        textAlign = 'start';\n      } else if (inRange(labelAngle, 90, 180) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'end';\n      }\n    } else if (tickAngle === 270) {\n      if (inRange(labelAngle, 0, 90, false, true)) {\n        textAlign = 'end';\n      } else if (inRange(labelAngle, 90, 180) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'start';\n      }\n    } else if (tickAngle === 180) {\n      if (labelAngle === 90) {\n        textAlign = 'start';\n      } else if (inRange(labelAngle, 0, 90) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'end';\n      }\n    }\n    /**\n     * todo tick 倾斜时的判断逻辑，该情况下坐标轴非垂直或水平\n     */\n  } else {\n    // 弧线坐标轴 label 的对齐方式判断逻辑\n    if (labelAlign === 'parallel') {\n      if (inRange(tickAngle, 0, 180, true)) {\n        textBaseline = 'top';\n      } else {\n        textBaseline = 'bottom';\n      }\n    } else if (labelAlign === 'horizontal') {\n      if (inRange(tickAngle, 90, 270, false)) {\n        textAlign = 'end';\n      } else if (inRange(tickAngle, 270, 360, false) || inRange(tickAngle, 0, 90)) {\n        textAlign = 'start';\n      }\n    } else if (labelAlign === 'perpendicular') {\n      if (inRange(tickAngle, 90, 270)) {\n        textAlign = 'end';\n      } else {\n        textAlign = 'start';\n      }\n    }\n  }\n  return {\n    textAlign: textAlign,\n    textBaseline: textBaseline\n  };\n}\nfunction setRotateAndAdjustLabelAlign(rotate, group, attr) {\n  group.setLocalEulerAngles(rotate);\n  var value = group.__data__.value;\n  var textStyle = getLabelStyle(value, rotate, attr);\n  var label = group.querySelector(CLASS_NAMES.labelItem.class);\n  if (label) applyTextStyle(label, textStyle);\n}\nfunction getLabelPos(datum, data, attr) {\n  var showTick = attr.showTick,\n    tickLength = attr.tickLength,\n    tickDirection = attr.tickDirection,\n    labelDirection = attr.labelDirection,\n    labelSpacing = attr.labelSpacing;\n  var index = data.indexOf(datum);\n  var finalLabelSpacing = getCallbackValue(labelSpacing, [datum, index, data]);\n  var _a = __read([getLabelVector(datum.value, attr), getFactor(labelDirection, tickDirection)], 2),\n    labelVector = _a[0],\n    unionFactor = _a[1];\n  var extraLength = unionFactor === 1 ? getCallbackValue(showTick ? tickLength : 0, [datum, index, data]) : 0;\n  var _b = __read(add(scale(labelVector, finalLabelSpacing + extraLength), getValuePos(datum.value, attr)), 2),\n    x = _b[0],\n    y = _b[1];\n  return {\n    x: x,\n    y: y\n  };\n}\nfunction formatter(datum, index, data, attr) {\n  var labelFormatter = attr.labelFormatter;\n  var element = isFunction(labelFormatter) ? function () {\n    return renderExtDo(getCallbackValue(labelFormatter, [datum, index, data, getLabelVector(datum.value, attr)]));\n  } : function () {\n    return renderExtDo(datum.label || '');\n  };\n  return element;\n}\nfunction applyTextStyle(node, style) {\n  if (node.nodeName === 'text') node.attr(style);\n}\nfunction overlapHandler(attr, main) {\n  processOverlap(this.node().childNodes, attr, main, {\n    hide: hide,\n    show: show,\n    rotate: function (label, angle) {\n      setRotateAndAdjustLabelAlign(+angle, label, attr);\n    },\n    ellipsis: function (label, len, suffix) {\n      label && ellipsisIt(label, len || Infinity, suffix);\n    },\n    wrap: function (label, width, lines) {\n      label && wrapIt(label, width, lines);\n    },\n    getTextShape: function (label) {\n      return label.querySelector('text');\n    }\n  });\n}\nfunction renderLabel(container, datum, data, style, attr) {\n  var index = data.indexOf(datum);\n  var label = select(container).append(formatter(datum, index, data, attr)).attr('className', CLASS_NAMES.labelItem.name).node();\n  var _a = __read(splitStyle(getCallbackStyle(style, [datum, index, data])), 2),\n    labelStyle = _a[0],\n    _b = _a[1],\n    transform = _b.transform,\n    groupStyle = __rest(_b, [\"transform\"]);\n  percentTransform(label, transform);\n  var rotate = getLabelRotation(datum, label, attr);\n  if (!label.getLocalEulerAngles()) {\n    label.setLocalEulerAngles(rotate);\n  }\n  applyTextStyle(label, __assign(__assign({}, getLabelStyle(datum.value, rotate, attr)), labelStyle));\n  container.attr(groupStyle);\n  return label;\n}\nexport function renderLabels(container, data, attr, animate, main) {\n  var finalData = filterExec(data, attr.labelFilter);\n  var style = subStyleProps(attr, 'label');\n  var _exit;\n  var transitions = container.selectAll(CLASS_NAMES.label.class).data(finalData, function (d, i) {\n    return i;\n  }).join(function (enter) {\n    return enter.append('g').attr('className', CLASS_NAMES.label.name).transition(function (datum) {\n      renderLabel(this, datum, data, style, attr);\n      var _a = getLabelPos(datum, data, attr),\n        x = _a.x,\n        y = _a.y;\n      // .axis-label\n      this.style.transform = \"translate(\".concat(x, \", \").concat(y, \")\");\n      return null;\n    });\n  }, function (update) {\n    return update.transition(function (datum) {\n      var prevLabel = this.querySelector(CLASS_NAMES.labelItem.class);\n      var label = renderLabel(this, datum, data, style, attr);\n      var shapeAnimation = transitionShape(prevLabel, label, animate.update);\n      var _a = getLabelPos(datum, data, attr),\n        x = _a.x,\n        y = _a.y;\n      var animation = transition(this, {\n        transform: \"translate(\".concat(x, \", \").concat(y, \")\")\n      }, animate.update);\n      return __spreadArray(__spreadArray([], __read(shapeAnimation), false), [animation], false);\n      // return [animation];\n    });\n  }, function (exit) {\n    _exit = exit;\n    exit.transition(function () {\n      var _this = this;\n      var animation = fadeOut(this.childNodes[0], animate.exit);\n      onAnimateFinished(animation, function () {\n        return select(_this).remove();\n      });\n      return animation;\n    });\n    return _exit;\n  }).transitions();\n  // handle overlapping after transitions finished\n  onAnimatesFinished(transitions, function () {\n    overlapHandler.call(container, attr, main);\n  });\n  return transitions;\n}", "map": {"version": 3, "names": ["isFunction", "fadeOut", "onAnimateFinished", "onAnimatesFinished", "transition", "transitionShape", "add", "ellipsisIt", "getCallbackValue", "hide", "inRange", "percentTransform", "radToDeg", "renderExtDo", "scale", "select", "show", "splitStyle", "subStyleProps", "wrapIt", "CLASS_NAMES", "processOverlap", "getFactor", "getValuePos", "filterExec", "getCallbackStyle", "getLabelVector", "getLineTangentVector", "angleNormalizer", "angle", "normalizedAngle", "Math", "round", "getAngle", "v1", "v2", "_a", "__read", "x1", "y1", "_b", "x2", "y2", "_c", "dot", "det", "atan2", "correctLabelRotation", "_rotate", "rotate", "getLabelRotation", "datum", "label", "attr", "labelAlign", "customRotate", "style", "transform", "includes", "getLocalEulerAngles", "labelVector", "value", "tangentVector", "getLabelStyle", "type", "labelAngle", "tickAngle", "textAlign", "textBaseline", "setRotateAndAdjustLabelAlign", "group", "setLocalEulerAngles", "__data__", "textStyle", "querySelector", "labelItem", "class", "applyTextStyle", "getLabelPos", "data", "showTick", "tick<PERSON><PERSON>th", "tickDirection", "labelDirection", "labelSpacing", "index", "indexOf", "finalLabelSpacing", "unionFactor", "extraLength", "x", "y", "formatter", "labelFormatter", "element", "node", "nodeName", "<PERSON><PERSON><PERSON><PERSON>", "main", "childNodes", "ellipsis", "len", "suffix", "Infinity", "wrap", "width", "lines", "getTextShape", "renderLabel", "container", "append", "name", "labelStyle", "groupStyle", "__rest", "__assign", "renderLabels", "animate", "finalData", "labelFilter", "_exit", "transitions", "selectAll", "d", "i", "join", "enter", "concat", "update", "prevLabel", "shapeAnimation", "animation", "__spread<PERSON><PERSON>y", "exit", "_this", "remove", "call"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/guides/labels.ts"], "sourcesContent": ["import type { IAnimation } from '@antv/g';\nimport { flatten, get, isFunction } from '@antv/util';\nimport type { StandardAnimationOption } from '../../../animation';\nimport { fadeOut, onAnimateFinished, onAnimatesFinished, transition, transitionShape } from '../../../animation';\nimport type { DisplayObject, TextStyleProps } from '../../../shapes';\nimport { Text } from '../../../shapes';\nimport type { Vector2 } from '../../../types';\nimport type { Selection, _Element } from '../../../util';\nimport {\n  add,\n  defined,\n  ellipsisIt,\n  getCallbackValue,\n  hide,\n  inRange,\n  percentTransform,\n  radToDeg,\n  renderExtDo,\n  scale,\n  select,\n  show,\n  splitStyle,\n  subStyleProps,\n  wrapIt,\n} from '../../../util';\nimport { CLASS_NAMES } from '../constant';\nimport { processOverlap } from '../overlap';\nimport type { AxisDatum, AxisLabelStyleProps, AxisStyleProps } from '../types';\nimport { getFactor } from '../utils';\nimport { getValuePos } from './line';\nimport { filterExec, getCallbackStyle, getLabelVector, getLineTangentVector } from './utils';\n\nfunction angleNormalizer(angle: number) {\n  let normalizedAngle = angle;\n  while (normalizedAngle < 0) normalizedAngle += 360;\n  return Math.round(normalizedAngle % 360);\n}\n\nfunction getAngle(v1: Vector2, v2: Vector2) {\n  const [x1, y1] = v1;\n  const [x2, y2] = v2;\n  const [dot, det] = [x1 * x2 + y1 * y2, x1 * y2 - y1 * x2];\n  return Math.atan2(det, dot);\n}\n\n/** to correct label rotation to avoid inverted character */\nfunction correctLabelRotation(_rotate: number) {\n  let rotate = (_rotate + 360) % 180;\n  if (!inRange(rotate, -90, 90)) rotate += 180;\n  return rotate;\n}\n\n/** get rotation from preset or layout */\nfunction getLabelRotation(datum: AxisDatum, label: DisplayObject, attr: Required<AxisStyleProps>) {\n  const { labelAlign } = attr;\n  // if label rotate is set, use it\n  const customRotate = label.style.transform?.includes('rotate');\n  if (customRotate) return label.getLocalEulerAngles();\n  let rotate = 0;\n  const labelVector = getLabelVector(datum.value, attr);\n  const tangentVector = getLineTangentVector(datum.value, attr);\n  if (labelAlign === 'horizontal') return 0;\n  if (labelAlign === 'perpendicular') rotate = getAngle([1, 0], labelVector);\n  else rotate = getAngle([tangentVector[0] < 0 ? -1 : 1, 0], tangentVector);\n  return correctLabelRotation(radToDeg(rotate));\n}\n\n/** get the label align according to its tick and label angle  */\nfunction getLabelStyle(\n  value: number,\n  rotate: number,\n  attr: Required<AxisStyleProps>\n): Pick<TextStyleProps, 'textAlign' | 'textBaseline'> {\n  const { type, labelAlign } = attr;\n  const labelVector = getLabelVector(value, attr);\n  const labelAngle = angleNormalizer(rotate);\n  const tickAngle = angleNormalizer(radToDeg(getAngle([1, 0], labelVector)));\n\n  let textAlign: TextStyleProps['textAlign'] = 'center';\n  let textBaseline: TextStyleProps['textBaseline'] = 'middle';\n  if (type === 'linear') {\n    // tick 和 label 均为水平或垂直时，做快速判断\n    if ([90, 270].includes(tickAngle) && labelAngle === 0) {\n      textAlign = 'center';\n      textBaseline = labelVector[1] === 1 ? 'top' : 'bottom';\n    } else if (!(tickAngle % 180) && [90, 270].includes(labelAngle)) {\n      textAlign = 'center';\n    }\n    // 根据 tick 和 label 的角度，判断 label 的对齐方式\n    else if (tickAngle === 0) {\n      if (inRange(labelAngle, 0, 90, false, true)) {\n        textAlign = 'start';\n      } else if (inRange(labelAngle, 0, 90) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'start';\n      }\n    } else if (tickAngle === 90) {\n      if (inRange(labelAngle, 0, 90, false, true)) {\n        textAlign = 'start';\n      } else if (inRange(labelAngle, 90, 180) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'end';\n      }\n    } else if (tickAngle === 270) {\n      if (inRange(labelAngle, 0, 90, false, true)) {\n        textAlign = 'end';\n      } else if (inRange(labelAngle, 90, 180) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'start';\n      }\n    } else if (tickAngle === 180) {\n      if (labelAngle === 90) {\n        textAlign = 'start';\n      } else if (inRange(labelAngle, 0, 90) || inRange(labelAngle, 270, 360)) {\n        textAlign = 'end';\n      }\n    }\n    /**\n     * todo tick 倾斜时的判断逻辑，该情况下坐标轴非垂直或水平\n     */\n  } else {\n    // 弧线坐标轴 label 的对齐方式判断逻辑\n    if (labelAlign === 'parallel') {\n      if (inRange(tickAngle, 0, 180, true)) {\n        textBaseline = 'top';\n      } else {\n        textBaseline = 'bottom';\n      }\n    } else if (labelAlign === 'horizontal') {\n      if (inRange(tickAngle, 90, 270, false)) {\n        textAlign = 'end';\n      } else if (inRange(tickAngle, 270, 360, false) || inRange(tickAngle, 0, 90)) {\n        textAlign = 'start';\n      }\n    } else if (labelAlign === 'perpendicular') {\n      if (inRange(tickAngle, 90, 270)) {\n        textAlign = 'end';\n      } else {\n        textAlign = 'start';\n      }\n    }\n  }\n  return { textAlign, textBaseline };\n}\n\nfunction setRotateAndAdjustLabelAlign(rotate: number, group: _Element, attr: Required<AxisStyleProps>) {\n  group.setLocalEulerAngles(rotate);\n  const { value } = group.__data__;\n  const textStyle = getLabelStyle(value, rotate, attr);\n  const label = group.querySelector<DisplayObject>(CLASS_NAMES.labelItem.class);\n  if (label) applyTextStyle(label, textStyle);\n}\n\nfunction getLabelPos(datum: AxisDatum, data: AxisDatum[], attr: Required<AxisStyleProps>) {\n  const { showTick, tickLength, tickDirection, labelDirection, labelSpacing } = attr;\n  const index = data.indexOf(datum);\n  const finalLabelSpacing = getCallbackValue<number>(labelSpacing, [datum, index, data]);\n  const [labelVector, unionFactor] = [getLabelVector(datum.value, attr), getFactor(labelDirection!, tickDirection!)];\n  const extraLength = unionFactor === 1 ? getCallbackValue<number>(showTick ? tickLength : 0, [datum, index, data]) : 0;\n  const [x, y] = add(scale(labelVector, finalLabelSpacing + extraLength), getValuePos(datum.value, attr));\n  return { x, y };\n}\n\nfunction formatter(datum: AxisDatum, index: number, data: AxisDatum[], attr: Required<AxisStyleProps>) {\n  const { labelFormatter } = attr;\n  const element = isFunction(labelFormatter)\n    ? () => renderExtDo(getCallbackValue(labelFormatter, [datum, index, data, getLabelVector(datum.value, attr)]))\n    : () => renderExtDo(datum.label || '');\n  return element;\n}\n\nfunction applyTextStyle(node: DisplayObject, style: Partial<TextStyleProps>) {\n  if (node.nodeName === 'text') node.attr(style);\n}\n\nfunction overlapHandler(attr: Required<AxisStyleProps>, main: DisplayObject) {\n  processOverlap(this.node().childNodes as DisplayObject[], attr, main, {\n    hide,\n    show,\n    rotate: (label, angle) => {\n      setRotateAndAdjustLabelAlign(+angle, label, attr);\n    },\n    ellipsis: (label, len, suffix) => {\n      label && ellipsisIt(label, len || Infinity, suffix);\n    },\n    wrap: (label, width, lines) => {\n      label && wrapIt(label, width, lines);\n    },\n    getTextShape: (label) => label.querySelector<DisplayObject>('text') as Text,\n  });\n}\n\nfunction renderLabel(\n  container: DisplayObject,\n  datum: any,\n  data: any[],\n  style: any,\n  attr: Required<AxisStyleProps>\n): DisplayObject {\n  const index = data.indexOf(datum);\n  const label = select(container)\n    .append(formatter(datum, index, data, attr))\n    .attr('className', CLASS_NAMES.labelItem.name)\n    .node();\n  const [labelStyle, { transform, ...groupStyle }] = splitStyle(getCallbackStyle(style, [datum, index, data]));\n\n  percentTransform(label, transform);\n  const rotate = getLabelRotation(datum, label, attr);\n  if (!label.getLocalEulerAngles()) {\n    label.setLocalEulerAngles(rotate);\n  }\n\n  applyTextStyle(label, {\n    ...getLabelStyle(datum.value, rotate, attr),\n    ...labelStyle,\n  });\n  container.attr(groupStyle);\n  return label;\n}\n\nexport function renderLabels(\n  container: Selection,\n  data: AxisDatum[],\n  attr: Required<AxisStyleProps>,\n  animate: StandardAnimationOption,\n  main: DisplayObject\n) {\n  const finalData = filterExec(data, attr.labelFilter);\n  const style = subStyleProps<AxisLabelStyleProps>(attr, 'label');\n  let _exit!: Selection<AxisDatum>;\n  const transitions = container\n    .selectAll(CLASS_NAMES.label.class)\n    .data(finalData, (d, i) => i)\n    .join(\n      (enter) =>\n        enter\n          .append('g')\n          .attr('className', CLASS_NAMES.label.name)\n          .transition(function (datum) {\n            renderLabel(this, datum, data, style, attr);\n            const { x, y } = getLabelPos(datum, data, attr);\n            // .axis-label\n            this.style.transform = `translate(${x}, ${y})`;\n            return null;\n          }),\n      (update) =>\n        update.transition(function (datum) {\n          const prevLabel = this.querySelector(CLASS_NAMES.labelItem.class);\n          const label = renderLabel(this, datum, data, style, attr);\n          const shapeAnimation = transitionShape(prevLabel, label, animate.update);\n          const { x, y } = getLabelPos(datum, data, attr);\n          const animation = transition(this, { transform: `translate(${x}, ${y})` }, animate.update);\n          return [...shapeAnimation, animation];\n          // return [animation];\n        }),\n      (exit) => {\n        _exit = exit;\n        exit.transition(function () {\n          const animation = fadeOut(this.childNodes[0], animate.exit);\n          onAnimateFinished(animation, () => select(this).remove());\n          return animation;\n        });\n        return _exit;\n      }\n    )\n    .transitions();\n  // handle overlapping after transitions finished\n  onAnimatesFinished(transitions, () => {\n    overlapHandler.call(container, attr, main);\n  });\n  return transitions;\n}\n"], "mappings": ";AACA,SAAuBA,UAAU,QAAQ,YAAY;AAErD,SAASC,OAAO,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,eAAe,QAAQ,oBAAoB;AAKhH,SACEC,GAAG,EAEHC,UAAU,EACVC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,EACPC,gBAAgB,EAChBC,QAAQ,EACRC,WAAW,EACXC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,UAAU,EACVC,aAAa,EACbC,MAAM,QACD,eAAe;AACtB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,cAAc,QAAQ,YAAY;AAE3C,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,WAAW,QAAQ,QAAQ;AACpC,SAASC,UAAU,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,oBAAoB,QAAQ,SAAS;AAE5F,SAASC,eAAeA,CAACC,KAAa;EACpC,IAAIC,eAAe,GAAGD,KAAK;EAC3B,OAAOC,eAAe,GAAG,CAAC,EAAEA,eAAe,IAAI,GAAG;EAClD,OAAOC,IAAI,CAACC,KAAK,CAACF,eAAe,GAAG,GAAG,CAAC;AAC1C;AAEA,SAASG,QAAQA,CAACC,EAAW,EAAEC,EAAW;EAClC,IAAAC,EAAA,GAAAC,MAAA,CAAWH,EAAE;IAAZI,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA,GAAM;EACb,IAAAI,EAAA,GAAAH,MAAA,CAAWF,EAAE;IAAZM,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAM;EACb,IAAAG,EAAA,GAAAN,MAAA,CAAa,CAACC,EAAE,GAAGG,EAAE,GAAGF,EAAE,GAAGG,EAAE,EAAEJ,EAAE,GAAGI,EAAE,GAAGH,EAAE,GAAGE,EAAE,CAAC;IAAlDG,GAAG,GAAAD,EAAA;IAAEE,GAAG,GAAAF,EAAA,GAA0C;EACzD,OAAOZ,IAAI,CAACe,KAAK,CAACD,GAAG,EAAED,GAAG,CAAC;AAC7B;AAEA;AACA,SAASG,oBAAoBA,CAACC,OAAe;EAC3C,IAAIC,MAAM,GAAG,CAACD,OAAO,GAAG,GAAG,IAAI,GAAG;EAClC,IAAI,CAACtC,OAAO,CAACuC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAEA,MAAM,IAAI,GAAG;EAC5C,OAAOA,MAAM;AACf;AAEA;AACA,SAASC,gBAAgBA,CAACC,KAAgB,EAAEC,KAAoB,EAAEC,IAA8B;;EACtF,IAAAC,UAAU,GAAKD,IAAI,CAAAC,UAAT;EAClB;EACA,IAAMC,YAAY,GAAG,CAAAnB,EAAA,GAAAgB,KAAK,CAACI,KAAK,CAACC,SAAS,cAAArB,EAAA,uBAAAA,EAAA,CAAEsB,QAAQ,CAAC,QAAQ,CAAC;EAC9D,IAAIH,YAAY,EAAE,OAAOH,KAAK,CAACO,mBAAmB,EAAE;EACpD,IAAIV,MAAM,GAAG,CAAC;EACd,IAAMW,WAAW,GAAGlC,cAAc,CAACyB,KAAK,CAACU,KAAK,EAAER,IAAI,CAAC;EACrD,IAAMS,aAAa,GAAGnC,oBAAoB,CAACwB,KAAK,CAACU,KAAK,EAAER,IAAI,CAAC;EAC7D,IAAIC,UAAU,KAAK,YAAY,EAAE,OAAO,CAAC;EACzC,IAAIA,UAAU,KAAK,eAAe,EAAEL,MAAM,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE2B,WAAW,CAAC,CAAC,KACtEX,MAAM,GAAGhB,QAAQ,CAAC,CAAC6B,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,EAAEA,aAAa,CAAC;EACzE,OAAOf,oBAAoB,CAACnC,QAAQ,CAACqC,MAAM,CAAC,CAAC;AAC/C;AAEA;AACA,SAASc,aAAaA,CACpBF,KAAa,EACbZ,MAAc,EACdI,IAA8B;EAEtB,IAAAW,IAAI,GAAiBX,IAAI,CAAAW,IAArB;IAAEV,UAAU,GAAKD,IAAI,CAAAC,UAAT;EACxB,IAAMM,WAAW,GAAGlC,cAAc,CAACmC,KAAK,EAAER,IAAI,CAAC;EAC/C,IAAMY,UAAU,GAAGrC,eAAe,CAACqB,MAAM,CAAC;EAC1C,IAAMiB,SAAS,GAAGtC,eAAe,CAAChB,QAAQ,CAACqB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE2B,WAAW,CAAC,CAAC,CAAC;EAE1E,IAAIO,SAAS,GAAgC,QAAQ;EACrD,IAAIC,YAAY,GAAmC,QAAQ;EAC3D,IAAIJ,IAAI,KAAK,QAAQ,EAAE;IACrB;IACA,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAACN,QAAQ,CAACQ,SAAS,CAAC,IAAID,UAAU,KAAK,CAAC,EAAE;MACrDE,SAAS,GAAG,QAAQ;MACpBC,YAAY,GAAGR,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;IACxD,CAAC,MAAM,IAAI,EAAEM,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAACR,QAAQ,CAACO,UAAU,CAAC,EAAE;MAC/DE,SAAS,GAAG,QAAQ;IACtB;IACA;IAAA,KACK,IAAID,SAAS,KAAK,CAAC,EAAE;MACxB,IAAIxD,OAAO,CAACuD,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE;QAC3CE,SAAS,GAAG,OAAO;MACrB,CAAC,MAAM,IAAIzD,OAAO,CAACuD,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,IAAIvD,OAAO,CAACuD,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;QACtEE,SAAS,GAAG,OAAO;MACrB;IACF,CAAC,MAAM,IAAID,SAAS,KAAK,EAAE,EAAE;MAC3B,IAAIxD,OAAO,CAACuD,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE;QAC3CE,SAAS,GAAG,OAAO;MACrB,CAAC,MAAM,IAAIzD,OAAO,CAACuD,UAAU,EAAE,EAAE,EAAE,GAAG,CAAC,IAAIvD,OAAO,CAACuD,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;QACxEE,SAAS,GAAG,KAAK;MACnB;IACF,CAAC,MAAM,IAAID,SAAS,KAAK,GAAG,EAAE;MAC5B,IAAIxD,OAAO,CAACuD,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE;QAC3CE,SAAS,GAAG,KAAK;MACnB,CAAC,MAAM,IAAIzD,OAAO,CAACuD,UAAU,EAAE,EAAE,EAAE,GAAG,CAAC,IAAIvD,OAAO,CAACuD,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;QACxEE,SAAS,GAAG,OAAO;MACrB;IACF,CAAC,MAAM,IAAID,SAAS,KAAK,GAAG,EAAE;MAC5B,IAAID,UAAU,KAAK,EAAE,EAAE;QACrBE,SAAS,GAAG,OAAO;MACrB,CAAC,MAAM,IAAIzD,OAAO,CAACuD,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,IAAIvD,OAAO,CAACuD,UAAU,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;QACtEE,SAAS,GAAG,KAAK;MACnB;IACF;IACA;;;EAGF,CAAC,MAAM;IACL;IACA,IAAIb,UAAU,KAAK,UAAU,EAAE;MAC7B,IAAI5C,OAAO,CAACwD,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,EAAE;QACpCE,YAAY,GAAG,KAAK;MACtB,CAAC,MAAM;QACLA,YAAY,GAAG,QAAQ;MACzB;IACF,CAAC,MAAM,IAAId,UAAU,KAAK,YAAY,EAAE;MACtC,IAAI5C,OAAO,CAACwD,SAAS,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,EAAE;QACtCC,SAAS,GAAG,KAAK;MACnB,CAAC,MAAM,IAAIzD,OAAO,CAACwD,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,IAAIxD,OAAO,CAACwD,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;QAC3EC,SAAS,GAAG,OAAO;MACrB;IACF,CAAC,MAAM,IAAIb,UAAU,KAAK,eAAe,EAAE;MACzC,IAAI5C,OAAO,CAACwD,SAAS,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE;QAC/BC,SAAS,GAAG,KAAK;MACnB,CAAC,MAAM;QACLA,SAAS,GAAG,OAAO;MACrB;IACF;EACF;EACA,OAAO;IAAEA,SAAS,EAAAA,SAAA;IAAEC,YAAY,EAAAA;EAAA,CAAE;AACpC;AAEA,SAASC,4BAA4BA,CAACpB,MAAc,EAAEqB,KAAe,EAAEjB,IAA8B;EACnGiB,KAAK,CAACC,mBAAmB,CAACtB,MAAM,CAAC;EACzB,IAAAY,KAAK,GAAKS,KAAK,CAACE,QAAQ,CAAAX,KAAnB;EACb,IAAMY,SAAS,GAAGV,aAAa,CAACF,KAAK,EAAEZ,MAAM,EAAEI,IAAI,CAAC;EACpD,IAAMD,KAAK,GAAGkB,KAAK,CAACI,aAAa,CAAgBtD,WAAW,CAACuD,SAAS,CAACC,KAAK,CAAC;EAC7E,IAAIxB,KAAK,EAAEyB,cAAc,CAACzB,KAAK,EAAEqB,SAAS,CAAC;AAC7C;AAEA,SAASK,WAAWA,CAAC3B,KAAgB,EAAE4B,IAAiB,EAAE1B,IAA8B;EAC9E,IAAA2B,QAAQ,GAA8D3B,IAAI,CAAA2B,QAAlE;IAAEC,UAAU,GAAkD5B,IAAI,CAAA4B,UAAtD;IAAEC,aAAa,GAAmC7B,IAAI,CAAA6B,aAAvC;IAAEC,cAAc,GAAmB9B,IAAI,CAAA8B,cAAvB;IAAEC,YAAY,GAAK/B,IAAI,CAAA+B,YAAT;EACzE,IAAMC,KAAK,GAAGN,IAAI,CAACO,OAAO,CAACnC,KAAK,CAAC;EACjC,IAAMoC,iBAAiB,GAAG/E,gBAAgB,CAAS4E,YAAY,EAAE,CAACjC,KAAK,EAAEkC,KAAK,EAAEN,IAAI,CAAC,CAAC;EAChF,IAAA3C,EAAA,GAAAC,MAAA,CAA6B,CAACX,cAAc,CAACyB,KAAK,CAACU,KAAK,EAAER,IAAI,CAAC,EAAE/B,SAAS,CAAC6D,cAAe,EAAED,aAAc,CAAC,CAAC;IAA3GtB,WAAW,GAAAxB,EAAA;IAAEoD,WAAW,GAAApD,EAAA,GAAmF;EAClH,IAAMqD,WAAW,GAAGD,WAAW,KAAK,CAAC,GAAGhF,gBAAgB,CAASwE,QAAQ,GAAGC,UAAU,GAAG,CAAC,EAAE,CAAC9B,KAAK,EAAEkC,KAAK,EAAEN,IAAI,CAAC,CAAC,GAAG,CAAC;EAC/G,IAAAvC,EAAA,GAAAH,MAAA,CAAS/B,GAAG,CAACQ,KAAK,CAAC8C,WAAW,EAAE2B,iBAAiB,GAAGE,WAAW,CAAC,EAAElE,WAAW,CAAC4B,KAAK,CAACU,KAAK,EAAER,IAAI,CAAC,CAAC;IAAhGqC,CAAC,GAAAlD,EAAA;IAAEmD,CAAC,GAAAnD,EAAA,GAA4F;EACvG,OAAO;IAAEkD,CAAC,EAAAA,CAAA;IAAEC,CAAC,EAAAA;EAAA,CAAE;AACjB;AAEA,SAASC,SAASA,CAACzC,KAAgB,EAAEkC,KAAa,EAAEN,IAAiB,EAAE1B,IAA8B;EAC3F,IAAAwC,cAAc,GAAKxC,IAAI,CAAAwC,cAAT;EACtB,IAAMC,OAAO,GAAG9F,UAAU,CAAC6F,cAAc,CAAC,GACtC;IAAM,OAAAhF,WAAW,CAACL,gBAAgB,CAACqF,cAAc,EAAE,CAAC1C,KAAK,EAAEkC,KAAK,EAAEN,IAAI,EAAErD,cAAc,CAACyB,KAAK,CAACU,KAAK,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;EAAtG,CAAsG,GAC5G;IAAM,OAAAxC,WAAW,CAACsC,KAAK,CAACC,KAAK,IAAI,EAAE,CAAC;EAA9B,CAA8B;EACxC,OAAO0C,OAAO;AAChB;AAEA,SAASjB,cAAcA,CAACkB,IAAmB,EAAEvC,KAA8B;EACzE,IAAIuC,IAAI,CAACC,QAAQ,KAAK,MAAM,EAAED,IAAI,CAAC1C,IAAI,CAACG,KAAK,CAAC;AAChD;AAEA,SAASyC,cAAcA,CAAC5C,IAA8B,EAAE6C,IAAmB;EACzE7E,cAAc,CAAC,IAAI,CAAC0E,IAAI,EAAE,CAACI,UAA6B,EAAE9C,IAAI,EAAE6C,IAAI,EAAE;IACpEzF,IAAI,EAAAA,IAAA;IACJO,IAAI,EAAAA,IAAA;IACJiC,MAAM,EAAE,SAAAA,CAACG,KAAK,EAAEvB,KAAK;MACnBwC,4BAA4B,CAAC,CAACxC,KAAK,EAAEuB,KAAK,EAAEC,IAAI,CAAC;IACnD,CAAC;IACD+C,QAAQ,EAAE,SAAAA,CAAChD,KAAK,EAAEiD,GAAG,EAAEC,MAAM;MAC3BlD,KAAK,IAAI7C,UAAU,CAAC6C,KAAK,EAAEiD,GAAG,IAAIE,QAAQ,EAAED,MAAM,CAAC;IACrD,CAAC;IACDE,IAAI,EAAE,SAAAA,CAACpD,KAAK,EAAEqD,KAAK,EAAEC,KAAK;MACxBtD,KAAK,IAAIjC,MAAM,CAACiC,KAAK,EAAEqD,KAAK,EAAEC,KAAK,CAAC;IACtC,CAAC;IACDC,YAAY,EAAE,SAAAA,CAACvD,KAAK;MAAK,OAAAA,KAAK,CAACsB,aAAa,CAAgB,MAAM,CAAS;IAAlD;GAC1B,CAAC;AACJ;AAEA,SAASkC,WAAWA,CAClBC,SAAwB,EACxB1D,KAAU,EACV4B,IAAW,EACXvB,KAAU,EACVH,IAA8B;EAE9B,IAAMgC,KAAK,GAAGN,IAAI,CAACO,OAAO,CAACnC,KAAK,CAAC;EACjC,IAAMC,KAAK,GAAGrC,MAAM,CAAC8F,SAAS,CAAC,CAC5BC,MAAM,CAAClB,SAAS,CAACzC,KAAK,EAAEkC,KAAK,EAAEN,IAAI,EAAE1B,IAAI,CAAC,CAAC,CAC3CA,IAAI,CAAC,WAAW,EAAEjC,WAAW,CAACuD,SAAS,CAACoC,IAAI,CAAC,CAC7ChB,IAAI,EAAE;EACH,IAAA3D,EAAA,GAAAC,MAAA,CAA6CpB,UAAU,CAACQ,gBAAgB,CAAC+B,KAAK,EAAE,CAACL,KAAK,EAAEkC,KAAK,EAAEN,IAAI,CAAC,CAAC,CAAC;IAArGiC,UAAU,GAAA5E,EAAA;IAAAI,EAAA,GAAAJ,EAAA;IAAIqB,SAAS,GAAAjB,EAAA,CAAAiB,SAAA;IAAKwD,UAAU,GAAAC,MAAA,CAAA1E,EAAA,EAA1B,aAA4B,CAA6D;EAE5G7B,gBAAgB,CAACyC,KAAK,EAAEK,SAAS,CAAC;EAClC,IAAMR,MAAM,GAAGC,gBAAgB,CAACC,KAAK,EAAEC,KAAK,EAAEC,IAAI,CAAC;EACnD,IAAI,CAACD,KAAK,CAACO,mBAAmB,EAAE,EAAE;IAChCP,KAAK,CAACmB,mBAAmB,CAACtB,MAAM,CAAC;EACnC;EAEA4B,cAAc,CAACzB,KAAK,EAAA+D,QAAA,CAAAA,QAAA,KACfpD,aAAa,CAACZ,KAAK,CAACU,KAAK,EAAEZ,MAAM,EAAEI,IAAI,CAAC,GACxC2D,UAAU,EACb;EACFH,SAAS,CAACxD,IAAI,CAAC4D,UAAU,CAAC;EAC1B,OAAO7D,KAAK;AACd;AAEA,OAAM,SAAUgE,YAAYA,CAC1BP,SAAoB,EACpB9B,IAAiB,EACjB1B,IAA8B,EAC9BgE,OAAgC,EAChCnB,IAAmB;EAEnB,IAAMoB,SAAS,GAAG9F,UAAU,CAACuD,IAAI,EAAE1B,IAAI,CAACkE,WAAW,CAAC;EACpD,IAAM/D,KAAK,GAAGtC,aAAa,CAAsBmC,IAAI,EAAE,OAAO,CAAC;EAC/D,IAAImE,KAA4B;EAChC,IAAMC,WAAW,GAAGZ,SAAS,CAC1Ba,SAAS,CAACtG,WAAW,CAACgC,KAAK,CAACwB,KAAK,CAAC,CAClCG,IAAI,CAACuC,SAAS,EAAE,UAACK,CAAC,EAAEC,CAAC;IAAK,OAAAA,CAAC;EAAD,CAAC,CAAC,CAC5BC,IAAI,CACH,UAACC,KAAK;IACJ,OAAAA,KAAK,CACFhB,MAAM,CAAC,GAAG,CAAC,CACXzD,IAAI,CAAC,WAAW,EAAEjC,WAAW,CAACgC,KAAK,CAAC2D,IAAI,CAAC,CACzC3G,UAAU,CAAC,UAAU+C,KAAK;MACzByD,WAAW,CAAC,IAAI,EAAEzD,KAAK,EAAE4B,IAAI,EAAEvB,KAAK,EAAEH,IAAI,CAAC;MACrC,IAAAjB,EAAA,GAAW0C,WAAW,CAAC3B,KAAK,EAAE4B,IAAI,EAAE1B,IAAI,CAAC;QAAvCqC,CAAC,GAAAtD,EAAA,CAAAsD,CAAA;QAAEC,CAAC,GAAAvD,EAAA,CAAAuD,CAAmC;MAC/C;MACA,IAAI,CAACnC,KAAK,CAACC,SAAS,GAAG,aAAAsE,MAAA,CAAarC,CAAC,QAAAqC,MAAA,CAAKpC,CAAC,MAAG;MAC9C,OAAO,IAAI;IACb,CAAC,CAAC;EATJ,CASI,EACN,UAACqC,MAAM;IACL,OAAAA,MAAM,CAAC5H,UAAU,CAAC,UAAU+C,KAAK;MAC/B,IAAM8E,SAAS,GAAG,IAAI,CAACvD,aAAa,CAACtD,WAAW,CAACuD,SAAS,CAACC,KAAK,CAAC;MACjE,IAAMxB,KAAK,GAAGwD,WAAW,CAAC,IAAI,EAAEzD,KAAK,EAAE4B,IAAI,EAAEvB,KAAK,EAAEH,IAAI,CAAC;MACzD,IAAM6E,cAAc,GAAG7H,eAAe,CAAC4H,SAAS,EAAE7E,KAAK,EAAEiE,OAAO,CAACW,MAAM,CAAC;MAClE,IAAA5F,EAAA,GAAW0C,WAAW,CAAC3B,KAAK,EAAE4B,IAAI,EAAE1B,IAAI,CAAC;QAAvCqC,CAAC,GAAAtD,EAAA,CAAAsD,CAAA;QAAEC,CAAC,GAAAvD,EAAA,CAAAuD,CAAmC;MAC/C,IAAMwC,SAAS,GAAG/H,UAAU,CAAC,IAAI,EAAE;QAAEqD,SAAS,EAAE,aAAAsE,MAAA,CAAarC,CAAC,QAAAqC,MAAA,CAAKpC,CAAC;MAAG,CAAE,EAAE0B,OAAO,CAACW,MAAM,CAAC;MAC1F,OAAAI,aAAA,CAAAA,aAAA,KAAA/F,MAAA,CAAW6F,cAAc,YAAEC,SAAS;MACpC;IACF,CAAC,CAAC;EARF,CAQE,EACJ,UAACE,IAAI;IACHb,KAAK,GAAGa,IAAI;IACZA,IAAI,CAACjI,UAAU,CAAC;MAAA,IAAAkI,KAAA;MACd,IAAMH,SAAS,GAAGlI,OAAO,CAAC,IAAI,CAACkG,UAAU,CAAC,CAAC,CAAC,EAAEkB,OAAO,CAACgB,IAAI,CAAC;MAC3DnI,iBAAiB,CAACiI,SAAS,EAAE;QAAM,OAAApH,MAAM,CAACuH,KAAI,CAAC,CAACC,MAAM,EAAE;MAArB,CAAqB,CAAC;MACzD,OAAOJ,SAAS;IAClB,CAAC,CAAC;IACF,OAAOX,KAAK;EACd,CAAC,CACF,CACAC,WAAW,EAAE;EAChB;EACAtH,kBAAkB,CAACsH,WAAW,EAAE;IAC9BxB,cAAc,CAACuC,IAAI,CAAC3B,SAAS,EAAExD,IAAI,EAAE6C,IAAI,CAAC;EAC5C,CAAC,CAAC;EACF,OAAOuB,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}