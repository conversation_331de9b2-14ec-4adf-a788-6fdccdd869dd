{"ast": null, "code": "export default function (_) {\n  return arguments.length ? this.cover(+_[0][0]).cover(+_[1][0]) : isNaN(this._x0) ? undefined : [[this._x0], [this._x1]];\n}", "map": {"version": 3, "names": ["_", "arguments", "length", "cover", "isNaN", "_x0", "undefined", "_x1"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-binarytree/src/extent.js"], "sourcesContent": ["export default function(_) {\n  return arguments.length\n      ? this.cover(+_[0][0]).cover(+_[1][0])\n      : isNaN(this._x0) ? undefined : [[this._x0], [this._x1]];\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,OAAOC,SAAS,CAACC,MAAM,GACjB,IAAI,CAACC,KAAK,CAAC,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GACpCI,KAAK,CAAC,IAAI,CAACC,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC,CAAC,IAAI,CAACD,GAAG,CAAC,EAAE,CAAC,IAAI,CAACE,GAAG,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}