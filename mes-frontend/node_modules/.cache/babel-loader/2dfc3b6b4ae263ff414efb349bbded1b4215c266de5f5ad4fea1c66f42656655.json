{"ast": null, "code": "import { __assign, __extends, __read, __spreadArray } from \"tslib\";\nimport { Component } from '../../core';\nimport { Group, Rect } from '../../shapes';\nimport { deepAssign, parseSeriesAttr, subStyleProps } from '../../util';\nimport { Backward, ChartType, Forward, PlayPause, Reset, SelectionType, SpeedSelect, Split } from './icons';\nvar componentsMap = {\n  reset: Reset,\n  speed: SpeedSelect,\n  backward: Backward,\n  playPause: PlayPause,\n  forward: Forward,\n  selectionType: SelectionType,\n  chartType: ChartType,\n  split: Split\n};\nvar Controller = /** @class */function (_super) {\n  __extends(Controller, _super);\n  function Controller(options) {\n    var _this = _super.call(this, deepAssign({}, Controller.defaultOptions, options)) || this;\n    _this.background = _this.appendChild(new Rect({}));\n    _this.functions = _this.appendChild(new Group({}));\n    return _this;\n  }\n  Object.defineProperty(Controller.prototype, \"padding\", {\n    get: function () {\n      return parseSeriesAttr(this.attributes.padding);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Controller.prototype.renderBackground = function () {\n    var _a = this.style,\n      x = _a.x,\n      y = _a.y,\n      width = _a.width,\n      height = _a.height;\n    var backgroundStyle = subStyleProps(this.attributes, 'background');\n    this.background.attr(__assign({\n      x: x,\n      y: y,\n      width: width,\n      height: height\n    }, backgroundStyle));\n  };\n  Controller.prototype.renderFunctions = function () {\n    var _this = this;\n    var _a;\n    var _b = this.attributes,\n      functions = _b.functions,\n      iconSize = _b.iconSize,\n      iconSpacing = _b.iconSpacing,\n      x = _b.x,\n      y = _b.y,\n      width = _b.width,\n      height = _b.height,\n      align = _b.align;\n    var _c = __read(this.padding, 4),\n      right = _c[1],\n      left = _c[3];\n    var components = functions.reduce(function (prev, curr) {\n      if (prev.length && curr.length) {\n        return prev.concat.apply(prev, __spreadArray(['split'], __read(curr), false));\n      }\n      return prev.concat.apply(prev, __spreadArray([], __read(curr), false));\n    }, []);\n    var componentsWidth = components.length * (iconSize + iconSpacing) - iconSpacing;\n    var xOffset = {\n      left: left + iconSize / 2,\n      center: (width - componentsWidth) / 2 + iconSize / 2,\n      right: width - componentsWidth - left - right + iconSize / 2\n    }[align] || 0;\n    (_a = this.speedSelect) === null || _a === void 0 ? void 0 : _a.destroy();\n    this.functions.removeChildren();\n    components.forEach(function (name, index) {\n      var _a;\n      var Ctor = componentsMap[name];\n      var style = {\n        x: x + index * (iconSize + iconSpacing) + xOffset,\n        y: y + height / 2,\n        size: iconSize\n      };\n      if (Ctor === SpeedSelect) {\n        style.speed = _this.attributes.speed;\n        style.onSelect = function (value) {\n          return _this.handleFunctionChange(name, {\n            value: value\n          });\n        };\n      } else if ([PlayPause, SelectionType, ChartType].includes(Ctor)) {\n        style.onChange = function (value) {\n          return _this.handleFunctionChange(name, {\n            value: value\n          });\n        };\n        if (Ctor === PlayPause) style.type = _this.attributes.state === 'play' ? 'pause' : 'play';\n        if (Ctor === SelectionType) style.type = _this.attributes.selectionType === 'range' ? 'value' : 'range';\n        if (Ctor === ChartType) style.type = _this.attributes.chartType === 'line' ? 'column' : 'line';\n      } else {\n        // IconBase\n        style.onClick = function () {\n          return _this.handleFunctionChange(name, {\n            value: name\n          });\n        };\n      }\n      if (Ctor === SpeedSelect) {\n        // SpeedSelect 直接插入到 canvas\n        var canvas = (_a = _this.ownerDocument) === null || _a === void 0 ? void 0 : _a.defaultView;\n        if (canvas) {\n          _this.speedSelect = new Ctor({\n            style: __assign(__assign({}, style), {\n              zIndex: 100\n            })\n          });\n          canvas.appendChild(_this.speedSelect);\n        }\n      } else {\n        _this.functions.appendChild(new Ctor({\n          style: style\n        }));\n      }\n    });\n  };\n  Controller.prototype.disconnectedCallback = function () {\n    var _a;\n    _super.prototype.disconnectedCallback.call(this);\n    (_a = this.speedSelect) === null || _a === void 0 ? void 0 : _a.destroy();\n  };\n  Controller.prototype.render = function () {\n    this.renderBackground();\n    this.renderFunctions();\n  };\n  Controller.prototype.handleFunctionChange = function (name, value) {\n    var onChange = this.attributes.onChange;\n    onChange === null || onChange === void 0 ? void 0 : onChange(name, value);\n  };\n  Controller.defaultOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      width: 300,\n      height: 40,\n      padding: 0,\n      align: 'center',\n      iconSize: 25,\n      iconSpacing: 0,\n      speed: 1,\n      state: 'pause',\n      chartType: 'line',\n      selectionType: 'range',\n      backgroundFill: '#fbfdff',\n      backgroundStroke: '#ebedf0',\n      functions: [['reset', 'speed'], ['backward', 'playPause', 'forward'], ['selectionType', 'chartType']]\n    }\n  };\n  return Controller;\n}(Component);\nexport { Controller };", "map": {"version": 3, "names": ["Component", "Group", "Rect", "deepAssign", "parseSeriesAttr", "subStyleProps", "Backward", "ChartType", "Forward", "PlayPause", "Reset", "SelectionType", "SpeedSelect", "Split", "componentsMap", "reset", "speed", "backward", "playPause", "forward", "selectionType", "chartType", "split", "Controller", "_super", "__extends", "options", "_this", "call", "defaultOptions", "background", "append<PERSON><PERSON><PERSON>", "functions", "Object", "defineProperty", "prototype", "get", "attributes", "padding", "renderBackground", "_a", "style", "x", "y", "width", "height", "backgroundStyle", "attr", "__assign", "renderFunctions", "_b", "iconSize", "iconSpacing", "align", "_c", "__read", "right", "left", "components", "reduce", "prev", "curr", "length", "concat", "apply", "__spread<PERSON><PERSON>y", "componentsWidth", "xOffset", "center", "speedSelect", "destroy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "name", "index", "Ctor", "size", "onSelect", "value", "handleFunctionChange", "includes", "onChange", "type", "state", "onClick", "canvas", "ownerDocument", "defaultView", "zIndex", "disconnectedCallback", "render", "backgroundFill", "backgroundStroke"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/timebar/controller.ts"], "sourcesContent": ["import type { Canvas } from '@antv/g';\nimport { Component } from '../../core';\nimport { DisplayObject, Group, Rect } from '../../shapes';\nimport { deepAssign, parseSeriesAttr, subStyleProps } from '../../util';\nimport { Backward, ChartType, Forward, PlayPause, Reset, SelectionType, SpeedSelect, Split } from './icons';\nimport type { ControllerOptions, ControllerStyleProps, Functions } from './types';\n\nconst componentsMap: Record<Functions | 'split', { new (...args: any): DisplayObject }> = {\n  reset: Reset,\n  speed: SpeedSelect,\n  backward: Backward,\n  playPause: PlayPause,\n  forward: Forward,\n  selectionType: SelectionType,\n  chartType: ChartType,\n  split: Split,\n} as const;\n\nexport class Controller extends Component<ControllerStyleProps> {\n  static defaultOptions: ControllerOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      width: 300,\n      height: 40,\n      padding: 0,\n      align: 'center',\n      iconSize: 25,\n      iconSpacing: 0,\n      speed: 1,\n      state: 'pause',\n      chartType: 'line',\n      selectionType: 'range',\n      backgroundFill: '#fbfdff',\n      backgroundStroke: '#ebedf0',\n      functions: [\n        ['reset', 'speed'],\n        ['backward', 'playPause', 'forward'],\n        ['selectionType', 'chartType'],\n      ],\n    },\n  };\n\n  private background = this.appendChild(new Rect({}));\n\n  private functions = this.appendChild(new Group({}));\n\n  private speedSelect!: SpeedSelect;\n\n  private get padding() {\n    return parseSeriesAttr(this.attributes.padding);\n  }\n\n  private renderBackground() {\n    const { x, y, width, height } = this.style;\n    const backgroundStyle = subStyleProps(this.attributes, 'background');\n    this.background.attr({ x, y, width, height, ...backgroundStyle });\n  }\n\n  private renderFunctions() {\n    const { functions, iconSize, iconSpacing, x, y, width, height, align } = this.attributes;\n\n    const {\n      padding: [, right, , left],\n    } = this;\n\n    const components = functions.reduce((prev, curr) => {\n      if (prev.length && curr.length) {\n        return prev.concat('split', ...curr);\n      }\n      return prev.concat(...curr);\n    }, [] as (keyof typeof componentsMap)[]);\n\n    const componentsWidth = components.length * (iconSize + iconSpacing) - iconSpacing;\n    const xOffset =\n      {\n        left: left + iconSize / 2,\n        center: (width - componentsWidth) / 2 + iconSize / 2,\n        right: width - componentsWidth - left - right + iconSize / 2,\n      }[align] || 0;\n\n    this.speedSelect?.destroy();\n    this.functions.removeChildren();\n    components.forEach((name, index) => {\n      const Ctor = componentsMap[name];\n      const style: Record<string, any> = {\n        x: x + index * (iconSize + iconSpacing) + xOffset,\n        y: y + height / 2,\n        size: iconSize,\n      };\n\n      if (Ctor === SpeedSelect) {\n        style.speed = this.attributes.speed;\n        style.onSelect = (value: any) => this.handleFunctionChange(name, { value });\n      } else if (([PlayPause, SelectionType, ChartType] as any).includes(Ctor)) {\n        style.onChange = (value: any) => this.handleFunctionChange(name, { value });\n        if (Ctor === PlayPause) style.type = this.attributes.state === 'play' ? 'pause' : 'play';\n        if (Ctor === SelectionType) style.type = this.attributes.selectionType === 'range' ? 'value' : 'range';\n        if (Ctor === ChartType) style.type = this.attributes.chartType === 'line' ? 'column' : 'line';\n      } else {\n        // IconBase\n        style.onClick = () => this.handleFunctionChange(name, { value: name });\n      }\n\n      if (Ctor === SpeedSelect) {\n        // SpeedSelect 直接插入到 canvas\n        const canvas = this.ownerDocument?.defaultView;\n        if (canvas) {\n          this.speedSelect = new Ctor({ style: { ...style, zIndex: 100 } }) as SpeedSelect;\n          (canvas as unknown as Canvas).appendChild(this.speedSelect);\n        }\n      } else {\n        this.functions.appendChild(new Ctor({ style }));\n      }\n    });\n  }\n\n  constructor(options: ControllerOptions) {\n    super(deepAssign({}, Controller.defaultOptions, options));\n  }\n\n  disconnectedCallback(): void {\n    super.disconnectedCallback();\n    this.speedSelect?.destroy();\n  }\n\n  render() {\n    this.renderBackground();\n    this.renderFunctions();\n  }\n\n  handleFunctionChange(name: string, value: any) {\n    const { onChange } = this.attributes;\n    onChange?.(name as Functions, value);\n  }\n}\n"], "mappings": ";AACA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAAwBC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AACzD,SAASC,UAAU,EAAEC,eAAe,EAAEC,aAAa,QAAQ,YAAY;AACvE,SAASC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,EAAEC,SAAS,EAAEC,KAAK,EAAEC,aAAa,EAAEC,WAAW,EAAEC,KAAK,QAAQ,SAAS;AAG3G,IAAMC,aAAa,GAAuE;EACxFC,KAAK,EAAEL,KAAK;EACZM,KAAK,EAAEJ,WAAW;EAClBK,QAAQ,EAAEX,QAAQ;EAClBY,SAAS,EAAET,SAAS;EACpBU,OAAO,EAAEX,OAAO;EAChBY,aAAa,EAAET,aAAa;EAC5BU,SAAS,EAAEd,SAAS;EACpBe,KAAK,EAAET;CACC;AAEV,IAAAU,UAAA,0BAAAC,MAAA;EAAgCC,SAAA,CAAAF,UAAA,EAAAC,MAAA;EAmG9B,SAAAD,WAAYG,OAA0B;IACpC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACzB,UAAU,CAAC,EAAE,EAAEoB,UAAU,CAACM,cAAc,EAAEH,OAAO,CAAC,CAAC;IA3EnDC,KAAA,CAAAG,UAAU,GAAGH,KAAI,CAACI,WAAW,CAAC,IAAI7B,IAAI,CAAC,EAAE,CAAC,CAAC;IAE3CyB,KAAA,CAAAK,SAAS,GAAGL,KAAI,CAACI,WAAW,CAAC,IAAI9B,KAAK,CAAC,EAAE,CAAC,CAAC;;EA0EnD;EAtEAgC,MAAA,CAAAC,cAAA,CAAYX,UAAA,CAAAY,SAAA,WAAO;SAAnB,SAAAC,CAAA;MACE,OAAOhC,eAAe,CAAC,IAAI,CAACiC,UAAU,CAACC,OAAO,CAAC;IACjD,CAAC;;;;EAEOf,UAAA,CAAAY,SAAA,CAAAI,gBAAgB,GAAxB;IACQ,IAAAC,EAAA,GAA0B,IAAI,CAACC,KAAK;MAAlCC,CAAC,GAAAF,EAAA,CAAAE,CAAA;MAAEC,CAAC,GAAAH,EAAA,CAAAG,CAAA;MAAEC,KAAK,GAAAJ,EAAA,CAAAI,KAAA;MAAEC,MAAM,GAAAL,EAAA,CAAAK,MAAe;IAC1C,IAAMC,eAAe,GAAGzC,aAAa,CAAC,IAAI,CAACgC,UAAU,EAAE,YAAY,CAAC;IACpE,IAAI,CAACP,UAAU,CAACiB,IAAI,CAAAC,QAAA;MAAGN,CAAC,EAAAA,CAAA;MAAEC,CAAC,EAAAA,CAAA;MAAEC,KAAK,EAAAA,KAAA;MAAEC,MAAM,EAAAA;IAAA,GAAKC,eAAe,EAAG;EACnE,CAAC;EAEOvB,UAAA,CAAAY,SAAA,CAAAc,eAAe,GAAvB;IAAA,IAAAtB,KAAA;;IACQ,IAAAuB,EAAA,GAAmE,IAAI,CAACb,UAAU;MAAhFL,SAAS,GAAAkB,EAAA,CAAAlB,SAAA;MAAEmB,QAAQ,GAAAD,EAAA,CAAAC,QAAA;MAAEC,WAAW,GAAAF,EAAA,CAAAE,WAAA;MAAEV,CAAC,GAAAQ,EAAA,CAAAR,CAAA;MAAEC,CAAC,GAAAO,EAAA,CAAAP,CAAA;MAAEC,KAAK,GAAAM,EAAA,CAAAN,KAAA;MAAEC,MAAM,GAAAK,EAAA,CAAAL,MAAA;MAAEQ,KAAK,GAAAH,EAAA,CAAAG,KAAoB;IAGtF,IAAAC,EAAA,GAAAC,MAAA,CACE,IAAI,CAAAjB,OAAA,IADoB;MAAdkB,KAAK,GAAAF,EAAA;MAAIG,IAAI,GAAAH,EAAA,GAAC;IAG5B,IAAMI,UAAU,GAAG1B,SAAS,CAAC2B,MAAM,CAAC,UAACC,IAAI,EAAEC,IAAI;MAC7C,IAAID,IAAI,CAACE,MAAM,IAAID,IAAI,CAACC,MAAM,EAAE;QAC9B,OAAOF,IAAI,CAACG,MAAM,CAAAC,KAAA,CAAXJ,IAAI,EAAAK,aAAA,EAAQ,OAAO,GAAAV,MAAA,CAAKM,IAAI;MACrC;MACA,OAAOD,IAAI,CAACG,MAAM,CAAAC,KAAA,CAAXJ,IAAI,EAAAK,aAAA,KAAAV,MAAA,CAAWM,IAAI;IAC5B,CAAC,EAAE,EAAoC,CAAC;IAExC,IAAMK,eAAe,GAAGR,UAAU,CAACI,MAAM,IAAIX,QAAQ,GAAGC,WAAW,CAAC,GAAGA,WAAW;IAClF,IAAMe,OAAO,GACX;MACEV,IAAI,EAAEA,IAAI,GAAGN,QAAQ,GAAG,CAAC;MACzBiB,MAAM,EAAE,CAACxB,KAAK,GAAGsB,eAAe,IAAI,CAAC,GAAGf,QAAQ,GAAG,CAAC;MACpDK,KAAK,EAAEZ,KAAK,GAAGsB,eAAe,GAAGT,IAAI,GAAGD,KAAK,GAAGL,QAAQ,GAAG;KAC5D,CAACE,KAAK,CAAC,IAAI,CAAC;IAEf,CAAAb,EAAA,OAAI,CAAC6B,WAAW,cAAA7B,EAAA,uBAAAA,EAAA,CAAE8B,OAAO,EAAE;IAC3B,IAAI,CAACtC,SAAS,CAACuC,cAAc,EAAE;IAC/Bb,UAAU,CAACc,OAAO,CAAC,UAACC,IAAI,EAAEC,KAAK;;MAC7B,IAAMC,IAAI,GAAG7D,aAAa,CAAC2D,IAAI,CAAC;MAChC,IAAMhC,KAAK,GAAwB;QACjCC,CAAC,EAAEA,CAAC,GAAGgC,KAAK,IAAIvB,QAAQ,GAAGC,WAAW,CAAC,GAAGe,OAAO;QACjDxB,CAAC,EAAEA,CAAC,GAAGE,MAAM,GAAG,CAAC;QACjB+B,IAAI,EAAEzB;OACP;MAED,IAAIwB,IAAI,KAAK/D,WAAW,EAAE;QACxB6B,KAAK,CAACzB,KAAK,GAAGW,KAAI,CAACU,UAAU,CAACrB,KAAK;QACnCyB,KAAK,CAACoC,QAAQ,GAAG,UAACC,KAAU;UAAK,OAAAnD,KAAI,CAACoD,oBAAoB,CAACN,IAAI,EAAE;YAAEK,KAAK,EAAAA;UAAA,CAAE,CAAC;QAA1C,CAA0C;MAC7E,CAAC,MAAM,IAAK,CAACrE,SAAS,EAAEE,aAAa,EAAEJ,SAAS,CAAS,CAACyE,QAAQ,CAACL,IAAI,CAAC,EAAE;QACxElC,KAAK,CAACwC,QAAQ,GAAG,UAACH,KAAU;UAAK,OAAAnD,KAAI,CAACoD,oBAAoB,CAACN,IAAI,EAAE;YAAEK,KAAK,EAAAA;UAAA,CAAE,CAAC;QAA1C,CAA0C;QAC3E,IAAIH,IAAI,KAAKlE,SAAS,EAAEgC,KAAK,CAACyC,IAAI,GAAGvD,KAAI,CAACU,UAAU,CAAC8C,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,MAAM;QACxF,IAAIR,IAAI,KAAKhE,aAAa,EAAE8B,KAAK,CAACyC,IAAI,GAAGvD,KAAI,CAACU,UAAU,CAACjB,aAAa,KAAK,OAAO,GAAG,OAAO,GAAG,OAAO;QACtG,IAAIuD,IAAI,KAAKpE,SAAS,EAAEkC,KAAK,CAACyC,IAAI,GAAGvD,KAAI,CAACU,UAAU,CAAChB,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,MAAM;MAC/F,CAAC,MAAM;QACL;QACAoB,KAAK,CAAC2C,OAAO,GAAG;UAAM,OAAAzD,KAAI,CAACoD,oBAAoB,CAACN,IAAI,EAAE;YAAEK,KAAK,EAAEL;UAAI,CAAE,CAAC;QAAhD,CAAgD;MACxE;MAEA,IAAIE,IAAI,KAAK/D,WAAW,EAAE;QACxB;QACA,IAAMyE,MAAM,GAAG,CAAA7C,EAAA,GAAAb,KAAI,CAAC2D,aAAa,cAAA9C,EAAA,uBAAAA,EAAA,CAAE+C,WAAW;QAC9C,IAAIF,MAAM,EAAE;UACV1D,KAAI,CAAC0C,WAAW,GAAG,IAAIM,IAAI,CAAC;YAAElC,KAAK,EAAAO,QAAA,CAAAA,QAAA,KAAOP,KAAK;cAAE+C,MAAM,EAAE;YAAG;UAAE,CAAE,CAAgB;UAC/EH,MAA4B,CAACtD,WAAW,CAACJ,KAAI,CAAC0C,WAAW,CAAC;QAC7D;MACF,CAAC,MAAM;QACL1C,KAAI,CAACK,SAAS,CAACD,WAAW,CAAC,IAAI4C,IAAI,CAAC;UAAElC,KAAK,EAAAA;QAAA,CAAE,CAAC,CAAC;MACjD;IACF,CAAC,CAAC;EACJ,CAAC;EAMDlB,UAAA,CAAAY,SAAA,CAAAsD,oBAAoB,GAApB;;IACEjE,MAAA,CAAAW,SAAK,CAACsD,oBAAoB,CAAA7D,IAAA,MAAE;IAC5B,CAAAY,EAAA,OAAI,CAAC6B,WAAW,cAAA7B,EAAA,uBAAAA,EAAA,CAAE8B,OAAO,EAAE;EAC7B,CAAC;EAED/C,UAAA,CAAAY,SAAA,CAAAuD,MAAM,GAAN;IACE,IAAI,CAACnD,gBAAgB,EAAE;IACvB,IAAI,CAACU,eAAe,EAAE;EACxB,CAAC;EAED1B,UAAA,CAAAY,SAAA,CAAA4C,oBAAoB,GAApB,UAAqBN,IAAY,EAAEK,KAAU;IACnC,IAAAG,QAAQ,GAAK,IAAI,CAAC5C,UAAU,CAAA4C,QAApB;IAChBA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGR,IAAiB,EAAEK,KAAK,CAAC;EACtC,CAAC;EAnHMvD,UAAA,CAAAM,cAAc,GAAsB;IACzCY,KAAK,EAAE;MACLC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAE,GAAG;MACVC,MAAM,EAAE,EAAE;MACVP,OAAO,EAAE,CAAC;MACVe,KAAK,EAAE,QAAQ;MACfF,QAAQ,EAAE,EAAE;MACZC,WAAW,EAAE,CAAC;MACdpC,KAAK,EAAE,CAAC;MACRmE,KAAK,EAAE,OAAO;MACd9D,SAAS,EAAE,MAAM;MACjBD,aAAa,EAAE,OAAO;MACtBuE,cAAc,EAAE,SAAS;MACzBC,gBAAgB,EAAE,SAAS;MAC3B5D,SAAS,EAAE,CACT,CAAC,OAAO,EAAE,OAAO,CAAC,EAClB,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,EACpC,CAAC,eAAe,EAAE,WAAW,CAAC;;GAGnC;EA8FH,OAAAT,UAAC;CAAA,CArH+BvB,SAAS;SAA5BuB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}