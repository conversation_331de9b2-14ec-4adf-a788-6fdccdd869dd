{"ast": null, "code": "import { greedyFAS } from './greedy-fas';\nconst run = (g, acyclicer) => {\n  const weightFn = g => {\n    return e => e.data.weight || 1;\n  };\n  const fas = acyclicer === 'greedy' ? greedyFAS(g, weightFn(g)) : dfsFAS(g);\n  fas === null || fas === void 0 ? void 0 : fas.forEach(e => {\n    const label = e.data;\n    g.removeEdge(e.id);\n    label.forwardName = e.data.name;\n    label.reversed = true;\n    g.addEdge({\n      id: e.id,\n      source: e.target,\n      target: e.source,\n      data: Object.assign({}, label)\n    });\n  });\n};\nconst dfsFAS = g => {\n  const fas = [];\n  const stack = {};\n  const visited = {};\n  const dfs = v => {\n    if (visited[v]) {\n      return;\n    }\n    visited[v] = true;\n    stack[v] = true;\n    g.getRelatedEdges(v, 'out').forEach(e => {\n      if (stack[e.target]) {\n        fas.push(e);\n      } else {\n        dfs(e.target);\n      }\n    });\n    delete stack[v];\n  };\n  g.getAllNodes().forEach(n => dfs(n.id));\n  return fas;\n};\nconst undo = g => {\n  g.getAllEdges().forEach(e => {\n    const label = e.data;\n    if (label.reversed) {\n      g.removeEdge(e.id);\n      const forwardName = label.forwardName;\n      delete label.reversed;\n      delete label.forwardName;\n      g.addEdge({\n        id: e.id,\n        source: e.target,\n        target: e.source,\n        data: Object.assign(Object.assign({}, label), {\n          forwardName\n        })\n      });\n    }\n  });\n};\nexport { run, undo };", "map": {"version": 3, "names": ["greedyFAS", "run", "g", "acyclicer", "weightFn", "e", "data", "weight", "fas", "dfsFAS", "for<PERSON>ach", "label", "removeEdge", "id", "<PERSON><PERSON><PERSON>", "name", "reversed", "addEdge", "source", "target", "Object", "assign", "stack", "visited", "dfs", "v", "getRelatedEdges", "push", "getAllNodes", "n", "undo", "getAllEdges"], "sources": ["../../src/antv-dagre/acyclic.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,SAAS,QAAQ,cAAc;AAExC,MAAMC,GAAG,GAAGA,CAACC,CAAQ,EAAEC,SAAiB,KAAI;EAC1C,MAAMC,QAAQ,GAAIF,CAAQ,IAAI;IAC5B,OAAQG,CAAiB,IAAKA,CAAC,CAACC,IAAI,CAACC,MAAM,IAAI,CAAC;EAClD,CAAC;EACD,MAAMC,GAAG,GAAGL,SAAS,KAAK,QAAQ,GAAGH,SAAS,CAACE,CAAC,EAAEE,QAAQ,CAACF,CAAC,CAAC,CAAC,GAAGO,MAAM,CAACP,CAAC,CAAC;EAC1EM,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEE,OAAO,CAAEL,CAAiB,IAAI;IACjC,MAAMM,KAAK,GAAGN,CAAC,CAACC,IAAI;IACpBJ,CAAC,CAACU,UAAU,CAACP,CAAC,CAACQ,EAAE,CAAC;IAClBF,KAAK,CAACG,WAAW,GAAGT,CAAC,CAACC,IAAI,CAACS,IAAI;IAC/BJ,KAAK,CAACK,QAAQ,GAAG,IAAI;IACrBd,CAAC,CAACe,OAAO,CAAC;MACRJ,EAAE,EAAER,CAAC,CAACQ,EAAE;MACRK,MAAM,EAAEb,CAAC,CAACc,MAAM;MAChBA,MAAM,EAAEd,CAAC,CAACa,MAAM;MAChBZ,IAAI,EAAAc,MAAA,CAAAC,MAAA,KACCV,KAAK;KAEX,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAED,MAAMF,MAAM,GAAIP,CAAQ,IAAI;EAC1B,MAAMM,GAAG,GAAqB,EAAE;EAChC,MAAMc,KAAK,GAAwB,EAAE;EACrC,MAAMC,OAAO,GAAwB,EAAE;EAEvC,MAAMC,GAAG,GAAIC,CAAK,IAAI;IACpB,IAAIF,OAAO,CAACE,CAAC,CAAC,EAAE;MACd;;IAEFF,OAAO,CAACE,CAAC,CAAC,GAAG,IAAI;IACjBH,KAAK,CAACG,CAAC,CAAC,GAAG,IAAI;IACfvB,CAAC,CAACwB,eAAe,CAACD,CAAC,EAAE,KAAK,CAAC,CAACf,OAAO,CAAEL,CAAC,IAAI;MACxC,IAAIiB,KAAK,CAACjB,CAAC,CAACc,MAAM,CAAC,EAAE;QACnBX,GAAG,CAACmB,IAAI,CAACtB,CAAC,CAAC;OACZ,MAAM;QACLmB,GAAG,CAACnB,CAAC,CAACc,MAAM,CAAC;;IAEjB,CAAC,CAAC;IACF,OAAOG,KAAK,CAACG,CAAC,CAAC;EACjB,CAAC;EAEDvB,CAAC,CAAC0B,WAAW,EAAE,CAAClB,OAAO,CAAEmB,CAAC,IAAKL,GAAG,CAACK,CAAC,CAAChB,EAAE,CAAC,CAAC;EACzC,OAAOL,GAAG;AACZ,CAAC;AAED,MAAMsB,IAAI,GAAI5B,CAAQ,IAAI;EACxBA,CAAC,CAAC6B,WAAW,EAAE,CAACrB,OAAO,CAAEL,CAAC,IAAI;IAC5B,MAAMM,KAAK,GAAGN,CAAC,CAACC,IAAI;IACpB,IAAIK,KAAK,CAACK,QAAQ,EAAE;MAClBd,CAAC,CAACU,UAAU,CAACP,CAAC,CAACQ,EAAE,CAAC;MAElB,MAAMC,WAAW,GAAGH,KAAK,CAACG,WAAW;MACrC,OAAOH,KAAK,CAACK,QAAQ;MACrB,OAAOL,KAAK,CAACG,WAAW;MACxBZ,CAAC,CAACe,OAAO,CAAC;QACRJ,EAAE,EAAER,CAAC,CAACQ,EAAE;QACRK,MAAM,EAAEb,CAAC,CAACc,MAAM;QAChBA,MAAM,EAAEd,CAAC,CAACa,MAAM;QAChBZ,IAAI,EAAAc,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAOV,KAAK;UAAEG;QAAW;OAC9B,CAAC;;EAEN,CAAC,CAAC;AACJ,CAAC;AAED,SAASb,GAAG,EAAE6B,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}