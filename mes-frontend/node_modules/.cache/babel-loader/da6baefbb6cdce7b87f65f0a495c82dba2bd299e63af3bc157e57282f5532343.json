{"ast": null, "code": "import { __read } from \"tslib\";\nfunction search(array, value) {\n  for (var i = 1; i < array.length; i += 1) {\n    var st = array[i - 1];\n    var end = array[i];\n    if (value >= st && value <= end) {\n      return [st, end];\n    }\n  }\n  return [value, value];\n}\nexport function getBlockColor(partition, color, orientation) {\n  var colors = Array.from(color);\n  var count = partition.length;\n  return new Array(count).fill(0).reduce(function (r, v, idx) {\n    var c = colors[idx % colors.length];\n    return r += \" \".concat(partition[idx], \":\").concat(c).concat(idx < count - 1 ? \" \".concat(partition[idx + 1], \":\").concat(c) : '');\n  }, \"l(\".concat(orientation === 'horizontal' ? '0' : '270', \")\"));\n}\nexport function getNextTickValue(ticks, value) {\n  var _a = __read(search(ticks, value), 2),\n    v1 = _a[0],\n    v2 = _a[1];\n  return {\n    tick: value > (v1 + v2) / 2 ? v2 : v1,\n    range: [v1, v2]\n  };\n}", "map": {"version": 3, "names": ["search", "array", "value", "i", "length", "st", "end", "getBlockColor", "partition", "color", "orientation", "colors", "Array", "from", "count", "fill", "reduce", "r", "v", "idx", "c", "concat", "getNextTickValue", "ticks", "_a", "__read", "v1", "v2", "tick", "range"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/legend/continuous/utils.ts"], "sourcesContent": ["function search(array: number[], value: number) {\n  for (let i = 1; i < array.length; i += 1) {\n    const st = array[i - 1];\n    const end = array[i];\n    if (value >= st && value <= end) {\n      return [st, end];\n    }\n  }\n  return [value, value];\n}\n\nexport function getBlockColor(partition: number[], color: string[], orientation: string) {\n  const colors = Array.from(color);\n  const count = partition.length;\n\n  return new Array(count).fill(0).reduce((r, v, idx) => {\n    const c = colors[idx % colors.length];\n    return (r += ` ${partition[idx]}:${c}${idx < count - 1 ? ` ${partition[idx + 1]}:${c}` : ''}`);\n  }, `l(${orientation === 'horizontal' ? '0' : '270'})`);\n}\n\nexport function getNextTickValue(ticks: number[], value: number) {\n  const [v1, v2] = search(ticks, value);\n  return { tick: value > (v1 + v2) / 2 ? v2 : v1, range: [v1, v2] };\n}\n"], "mappings": ";AAAA,SAASA,MAAMA,CAACC,KAAe,EAAEC,KAAa;EAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACxC,IAAME,EAAE,GAAGJ,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC;IACvB,IAAMG,GAAG,GAAGL,KAAK,CAACE,CAAC,CAAC;IACpB,IAAID,KAAK,IAAIG,EAAE,IAAIH,KAAK,IAAII,GAAG,EAAE;MAC/B,OAAO,CAACD,EAAE,EAAEC,GAAG,CAAC;IAClB;EACF;EACA,OAAO,CAACJ,KAAK,EAAEA,KAAK,CAAC;AACvB;AAEA,OAAM,SAAUK,aAAaA,CAACC,SAAmB,EAAEC,KAAe,EAAEC,WAAmB;EACrF,IAAMC,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACJ,KAAK,CAAC;EAChC,IAAMK,KAAK,GAAGN,SAAS,CAACJ,MAAM;EAE9B,OAAO,IAAIQ,KAAK,CAACE,KAAK,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAEC,GAAG;IAC/C,IAAMC,CAAC,GAAGT,MAAM,CAACQ,GAAG,GAAGR,MAAM,CAACP,MAAM,CAAC;IACrC,OAAQa,CAAC,IAAI,IAAAI,MAAA,CAAIb,SAAS,CAACW,GAAG,CAAC,OAAAE,MAAA,CAAID,CAAC,EAAAC,MAAA,CAAGF,GAAG,GAAGL,KAAK,GAAG,CAAC,GAAG,IAAAO,MAAA,CAAIb,SAAS,CAACW,GAAG,GAAG,CAAC,CAAC,OAAAE,MAAA,CAAID,CAAC,CAAE,GAAG,EAAE,CAAE;EAC/F,CAAC,EAAE,KAAAC,MAAA,CAAKX,WAAW,KAAK,YAAY,GAAG,GAAG,GAAG,KAAK,MAAG,CAAC;AACxD;AAEA,OAAM,SAAUY,gBAAgBA,CAACC,KAAe,EAAErB,KAAa;EACvD,IAAAsB,EAAA,GAAAC,MAAA,CAAWzB,MAAM,CAACuB,KAAK,EAAErB,KAAK,CAAC;IAA9BwB,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA,GAAwB;EACrC,OAAO;IAAEI,IAAI,EAAE1B,KAAK,GAAG,CAACwB,EAAE,GAAGC,EAAE,IAAI,CAAC,GAAGA,EAAE,GAAGD,EAAE;IAAEG,KAAK,EAAE,CAACH,EAAE,EAAEC,EAAE;EAAC,CAAE;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}