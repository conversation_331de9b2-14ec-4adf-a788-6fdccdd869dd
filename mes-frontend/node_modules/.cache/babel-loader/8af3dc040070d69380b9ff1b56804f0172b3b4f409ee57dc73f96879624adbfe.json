{"ast": null, "code": "import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nfunction index(d) {\n  return d.index;\n}\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\nexport default function (links) {\n  var id = index,\n    strength = defaultStrength,\n    strengths,\n    distance = constant(30),\n    distances,\n    nodes,\n    nDim,\n    count,\n    bias,\n    random,\n    iterations = 1;\n  if (links == null) links = [];\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x = 0, y = 0, z = 0, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        if (nDim > 1) {\n          y = target.y + target.vy - source.y - source.vy || jiggle(random);\n        }\n        if (nDim > 2) {\n          z = target.z + target.vz - source.z - source.vz || jiggle(random);\n        }\n        l = Math.sqrt(x * x + y * y + z * z);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l, z *= l;\n        target.vx -= x * (b = bias[i]);\n        if (nDim > 1) {\n          target.vy -= y * b;\n        }\n        if (nDim > 2) {\n          target.vz -= z * b;\n        }\n        source.vx += x * (b = 1 - b);\n        if (nDim > 1) {\n          source.vy += y * b;\n        }\n        if (nDim > 2) {\n          source.vz += z * b;\n        }\n      }\n    }\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length,\n      m = links.length,\n      nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n      link;\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n  function initializeStrength() {\n    if (!nodes) return;\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n  function initializeDistance() {\n    if (!nodes) return;\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n  force.initialize = function (_nodes, ...args) {\n    nodes = _nodes;\n    random = args.find(arg => typeof arg === 'function') || Math.random;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n  force.links = function (_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n  force.id = function (_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n  force.iterations = function (_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n  force.distance = function (_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["constant", "jiggle", "index", "d", "find", "nodeById", "nodeId", "node", "get", "Error", "links", "id", "strength", "defaultStrength", "strengths", "distance", "distances", "nodes", "nDim", "count", "bias", "random", "iterations", "link", "Math", "min", "source", "target", "force", "alpha", "k", "n", "length", "i", "x", "y", "z", "l", "b", "vx", "vy", "vz", "sqrt", "initialize", "m", "Map", "map", "Array", "initializeStrength", "initializeDistance", "_nodes", "args", "arg", "includes", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force-3d/src/link.js"], "sourcesContent": ["import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction index(d) {\n  return d.index;\n}\n\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\n\nexport default function(links) {\n  var id = index,\n      strength = defaultStrength,\n      strengths,\n      distance = constant(30),\n      distances,\n      nodes,\n      nDim,\n      count,\n      bias,\n      random,\n      iterations = 1;\n\n  if (links == null) links = [];\n\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x = 0, y = 0, z = 0, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        if (nDim > 1) { y = target.y + target.vy - source.y - source.vy || jiggle(random); }\n        if (nDim > 2) { z = target.z + target.vz - source.z - source.vz || jiggle(random); }\n        l = Math.sqrt(x * x + y * y + z * z);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l, z *= l;\n\n        target.vx -= x * (b = bias[i]);\n        if (nDim > 1) { target.vy -= y * b; }\n        if (nDim > 2) { target.vz -= z * b; }\n\n        source.vx += x * (b = 1 - b);\n        if (nDim > 1) { source.vy += y * b; }\n        if (nDim > 2) { source.vz += z * b; }\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n\n    var i,\n        n = nodes.length,\n        m = links.length,\n        nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n        link;\n\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n\n  function initializeStrength() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n\n  function initializeDistance() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n\n  force.initialize = function(_nodes, ...args) {\n    nodes = _nodes;\n    random = args.find(arg => typeof arg === 'function') || Math.random;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n\n  force.links = function(_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n\n  force.id = function(_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n\n  force.distance = function(_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,MAAM,MAAM,aAAa;AAEhC,SAASC,KAAKA,CAACC,CAAC,EAAE;EAChB,OAAOA,CAAC,CAACD,KAAK;AAChB;AAEA,SAASE,IAAIA,CAACC,QAAQ,EAAEC,MAAM,EAAE;EAC9B,IAAIC,IAAI,GAAGF,QAAQ,CAACG,GAAG,CAACF,MAAM,CAAC;EAC/B,IAAI,CAACC,IAAI,EAAE,MAAM,IAAIE,KAAK,CAAC,kBAAkB,GAAGH,MAAM,CAAC;EACvD,OAAOC,IAAI;AACb;AAEA,eAAe,UAASG,KAAK,EAAE;EAC7B,IAAIC,EAAE,GAAGT,KAAK;IACVU,QAAQ,GAAGC,eAAe;IAC1BC,SAAS;IACTC,QAAQ,GAAGf,QAAQ,CAAC,EAAE,CAAC;IACvBgB,SAAS;IACTC,KAAK;IACLC,IAAI;IACJC,KAAK;IACLC,IAAI;IACJC,MAAM;IACNC,UAAU,GAAG,CAAC;EAElB,IAAIZ,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;EAE7B,SAASG,eAAeA,CAACU,IAAI,EAAE;IAC7B,OAAO,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACN,KAAK,CAACI,IAAI,CAACG,MAAM,CAACxB,KAAK,CAAC,EAAEiB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACzB,KAAK,CAAC,CAAC;EACzE;EAEA,SAAS0B,KAAKA,CAACC,KAAK,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGrB,KAAK,CAACsB,MAAM,EAAEF,CAAC,GAAGR,UAAU,EAAE,EAAEQ,CAAC,EAAE;MACrD,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEV,IAAI,EAAEG,MAAM,EAAEC,MAAM,EAAEO,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEL,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;QAC3EV,IAAI,GAAGb,KAAK,CAACuB,CAAC,CAAC,EAAEP,MAAM,GAAGH,IAAI,CAACG,MAAM,EAAEC,MAAM,GAAGJ,IAAI,CAACI,MAAM;QAC3DO,CAAC,GAAGP,MAAM,CAACO,CAAC,GAAGP,MAAM,CAACY,EAAE,GAAGb,MAAM,CAACQ,CAAC,GAAGR,MAAM,CAACa,EAAE,IAAItC,MAAM,CAACoB,MAAM,CAAC;QACjE,IAAIH,IAAI,GAAG,CAAC,EAAE;UAAEiB,CAAC,GAAGR,MAAM,CAACQ,CAAC,GAAGR,MAAM,CAACa,EAAE,GAAGd,MAAM,CAACS,CAAC,GAAGT,MAAM,CAACc,EAAE,IAAIvC,MAAM,CAACoB,MAAM,CAAC;QAAE;QACnF,IAAIH,IAAI,GAAG,CAAC,EAAE;UAAEkB,CAAC,GAAGT,MAAM,CAACS,CAAC,GAAGT,MAAM,CAACc,EAAE,GAAGf,MAAM,CAACU,CAAC,GAAGV,MAAM,CAACe,EAAE,IAAIxC,MAAM,CAACoB,MAAM,CAAC;QAAE;QACnFgB,CAAC,GAAGb,IAAI,CAACkB,IAAI,CAACR,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;QACpCC,CAAC,GAAG,CAACA,CAAC,GAAGrB,SAAS,CAACiB,CAAC,CAAC,IAAII,CAAC,GAAGR,KAAK,GAAGf,SAAS,CAACmB,CAAC,CAAC;QACjDC,CAAC,IAAIG,CAAC,EAAEF,CAAC,IAAIE,CAAC,EAAED,CAAC,IAAIC,CAAC;QAEtBV,MAAM,CAACY,EAAE,IAAIL,CAAC,IAAII,CAAC,GAAGlB,IAAI,CAACa,CAAC,CAAC,CAAC;QAC9B,IAAIf,IAAI,GAAG,CAAC,EAAE;UAAES,MAAM,CAACa,EAAE,IAAIL,CAAC,GAAGG,CAAC;QAAE;QACpC,IAAIpB,IAAI,GAAG,CAAC,EAAE;UAAES,MAAM,CAACc,EAAE,IAAIL,CAAC,GAAGE,CAAC;QAAE;QAEpCZ,MAAM,CAACa,EAAE,IAAIL,CAAC,IAAII,CAAC,GAAG,CAAC,GAAGA,CAAC,CAAC;QAC5B,IAAIpB,IAAI,GAAG,CAAC,EAAE;UAAEQ,MAAM,CAACc,EAAE,IAAIL,CAAC,GAAGG,CAAC;QAAE;QACpC,IAAIpB,IAAI,GAAG,CAAC,EAAE;UAAEQ,MAAM,CAACe,EAAE,IAAIL,CAAC,GAAGE,CAAC;QAAE;MACtC;IACF;EACF;EAEA,SAASK,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAC1B,KAAK,EAAE;IAEZ,IAAIgB,CAAC;MACDF,CAAC,GAAGd,KAAK,CAACe,MAAM;MAChBY,CAAC,GAAGlC,KAAK,CAACsB,MAAM;MAChB3B,QAAQ,GAAG,IAAIwC,GAAG,CAAC5B,KAAK,CAAC6B,GAAG,CAAC,CAAC3C,CAAC,EAAE8B,CAAC,KAAK,CAACtB,EAAE,CAACR,CAAC,EAAE8B,CAAC,EAAEhB,KAAK,CAAC,EAAEd,CAAC,CAAC,CAAC,CAAC;MAC7DoB,IAAI;IAER,KAAKU,CAAC,GAAG,CAAC,EAAEd,KAAK,GAAG,IAAI4B,KAAK,CAAChB,CAAC,CAAC,EAAEE,CAAC,GAAGW,CAAC,EAAE,EAAEX,CAAC,EAAE;MAC5CV,IAAI,GAAGb,KAAK,CAACuB,CAAC,CAAC,EAAEV,IAAI,CAACrB,KAAK,GAAG+B,CAAC;MAC/B,IAAI,OAAOV,IAAI,CAACG,MAAM,KAAK,QAAQ,EAAEH,IAAI,CAACG,MAAM,GAAGtB,IAAI,CAACC,QAAQ,EAAEkB,IAAI,CAACG,MAAM,CAAC;MAC9E,IAAI,OAAOH,IAAI,CAACI,MAAM,KAAK,QAAQ,EAAEJ,IAAI,CAACI,MAAM,GAAGvB,IAAI,CAACC,QAAQ,EAAEkB,IAAI,CAACI,MAAM,CAAC;MAC9ER,KAAK,CAACI,IAAI,CAACG,MAAM,CAACxB,KAAK,CAAC,GAAG,CAACiB,KAAK,CAACI,IAAI,CAACG,MAAM,CAACxB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;MAC9DiB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACzB,KAAK,CAAC,GAAG,CAACiB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACzB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;IAChE;IAEA,KAAK+B,CAAC,GAAG,CAAC,EAAEb,IAAI,GAAG,IAAI2B,KAAK,CAACH,CAAC,CAAC,EAAEX,CAAC,GAAGW,CAAC,EAAE,EAAEX,CAAC,EAAE;MAC3CV,IAAI,GAAGb,KAAK,CAACuB,CAAC,CAAC,EAAEb,IAAI,CAACa,CAAC,CAAC,GAAGd,KAAK,CAACI,IAAI,CAACG,MAAM,CAACxB,KAAK,CAAC,IAAIiB,KAAK,CAACI,IAAI,CAACG,MAAM,CAACxB,KAAK,CAAC,GAAGiB,KAAK,CAACI,IAAI,CAACI,MAAM,CAACzB,KAAK,CAAC,CAAC;IAC7G;IAEAY,SAAS,GAAG,IAAIiC,KAAK,CAACH,CAAC,CAAC,EAAEI,kBAAkB,CAAC,CAAC;IAC9ChC,SAAS,GAAG,IAAI+B,KAAK,CAACH,CAAC,CAAC,EAAEK,kBAAkB,CAAC,CAAC;EAChD;EAEA,SAASD,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAC/B,KAAK,EAAE;IAEZ,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAGrB,KAAK,CAACsB,MAAM,EAAEC,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MAC5CnB,SAAS,CAACmB,CAAC,CAAC,GAAG,CAACrB,QAAQ,CAACF,KAAK,CAACuB,CAAC,CAAC,EAAEA,CAAC,EAAEvB,KAAK,CAAC;IAC9C;EACF;EAEA,SAASuC,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAAChC,KAAK,EAAE;IAEZ,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAGrB,KAAK,CAACsB,MAAM,EAAEC,CAAC,GAAGF,CAAC,EAAE,EAAEE,CAAC,EAAE;MAC5CjB,SAAS,CAACiB,CAAC,CAAC,GAAG,CAAClB,QAAQ,CAACL,KAAK,CAACuB,CAAC,CAAC,EAAEA,CAAC,EAAEvB,KAAK,CAAC;IAC9C;EACF;EAEAkB,KAAK,CAACe,UAAU,GAAG,UAASO,MAAM,EAAE,GAAGC,IAAI,EAAE;IAC3ClC,KAAK,GAAGiC,MAAM;IACd7B,MAAM,GAAG8B,IAAI,CAAC/C,IAAI,CAACgD,GAAG,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI5B,IAAI,CAACH,MAAM;IACnEH,IAAI,GAAGiC,IAAI,CAAC/C,IAAI,CAACgD,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC,IAAI,CAAC;IACrDT,UAAU,CAAC,CAAC;EACd,CAAC;EAEDf,KAAK,CAAClB,KAAK,GAAG,UAAS4C,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACvB,MAAM,IAAItB,KAAK,GAAG4C,CAAC,EAAEX,UAAU,CAAC,CAAC,EAAEf,KAAK,IAAIlB,KAAK;EACpE,CAAC;EAEDkB,KAAK,CAACjB,EAAE,GAAG,UAAS2C,CAAC,EAAE;IACrB,OAAOC,SAAS,CAACvB,MAAM,IAAIrB,EAAE,GAAG2C,CAAC,EAAE1B,KAAK,IAAIjB,EAAE;EAChD,CAAC;EAEDiB,KAAK,CAACN,UAAU,GAAG,UAASgC,CAAC,EAAE;IAC7B,OAAOC,SAAS,CAACvB,MAAM,IAAIV,UAAU,GAAG,CAACgC,CAAC,EAAE1B,KAAK,IAAIN,UAAU;EACjE,CAAC;EAEDM,KAAK,CAAChB,QAAQ,GAAG,UAAS0C,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACvB,MAAM,IAAIpB,QAAQ,GAAG,OAAO0C,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGtD,QAAQ,CAAC,CAACsD,CAAC,CAAC,EAAEN,kBAAkB,CAAC,CAAC,EAAEpB,KAAK,IAAIhB,QAAQ;EAC3H,CAAC;EAEDgB,KAAK,CAACb,QAAQ,GAAG,UAASuC,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACvB,MAAM,IAAIjB,QAAQ,GAAG,OAAOuC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGtD,QAAQ,CAAC,CAACsD,CAAC,CAAC,EAAEL,kBAAkB,CAAC,CAAC,EAAErB,KAAK,IAAIb,QAAQ;EAC3H,CAAC;EAED,OAAOa,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}