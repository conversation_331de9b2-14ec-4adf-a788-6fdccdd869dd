{"ast": null, "code": "import { bisect, conjugateGradient, nelderMead, norm2, scale, zeros, zerosM } from './fmin';\nimport { circleCircleIntersection, circleOverlap, distance, intersectionArea } from './circleintersection';\n/** given a list of set objects, and their corresponding overlaps.\nupdates the (x, y, radius) attribute on each set such that their positions\nroughly correspond to the desired overlaps */\nexport function venn(areas, parameters) {\n  parameters = parameters || {};\n  parameters.maxIterations = parameters.maxIterations || 500;\n  const initialLayout = parameters.initialLayout || bestInitialLayout;\n  const loss = parameters.lossFunction || lossFunction;\n  // add in missing pairwise areas as having 0 size\n  areas = addMissingAreas(areas);\n  // initial layout is done greedily\n  const circles = initialLayout(areas, parameters);\n  // transform x/y coordinates to a vector to optimize\n  const initial = [],\n    setids = [];\n  let setid;\n  for (setid in circles) {\n    // eslint-disable-next-line\n    if (circles.hasOwnProperty(setid)) {\n      initial.push(circles[setid].x);\n      initial.push(circles[setid].y);\n      setids.push(setid);\n    }\n  }\n  // optimize initial layout from our loss function\n  const solution = nelderMead(function (values) {\n    const current = {};\n    for (let i = 0; i < setids.length; ++i) {\n      const setid = setids[i];\n      current[setid] = {\n        x: values[2 * i],\n        y: values[2 * i + 1],\n        radius: circles[setid].radius\n      };\n    }\n    return loss(current, areas);\n  }, initial, parameters);\n  // transform solution vector back to x/y points\n  const positions = solution.x;\n  for (let i = 0; i < setids.length; ++i) {\n    setid = setids[i];\n    circles[setid].x = positions[2 * i];\n    circles[setid].y = positions[2 * i + 1];\n  }\n  return circles;\n}\nconst SMALL = 1e-10;\n/** Returns the distance necessary for two circles of radius r1 + r2 to\nhave the overlap area 'overlap' */\nexport function distanceFromIntersectArea(r1, r2, overlap) {\n  // handle complete overlapped circles\n  if (Math.min(r1, r2) * Math.min(r1, r2) * Math.PI <= overlap + SMALL) {\n    return Math.abs(r1 - r2);\n  }\n  return bisect(function (distance) {\n    return circleOverlap(r1, r2, distance) - overlap;\n  }, 0, r1 + r2);\n}\n/** Missing pair-wise intersection area data can cause problems:\n treating as an unknown means that sets will be laid out overlapping,\n which isn't what people expect. To reflect that we want disjoint sets\n here, set the overlap to 0 for all missing pairwise set intersections */\nfunction addMissingAreas(areas) {\n  areas = areas.slice();\n  // two circle intersections that aren't defined\n  const ids = [],\n    pairs = {};\n  let i, j, a, b;\n  for (i = 0; i < areas.length; ++i) {\n    const area = areas[i];\n    if (area.sets.length == 1) {\n      ids.push(area.sets[0]);\n    } else if (area.sets.length == 2) {\n      a = area.sets[0];\n      b = area.sets[1];\n      // @ts-ignore\n      pairs[[a, b]] = true;\n      // @ts-ignore\n      pairs[[b, a]] = true;\n    }\n  }\n  ids.sort((a, b) => {\n    return a > b ? 1 : -1;\n  });\n  for (i = 0; i < ids.length; ++i) {\n    a = ids[i];\n    for (j = i + 1; j < ids.length; ++j) {\n      b = ids[j];\n      // @ts-ignore\n      if (!([a, b] in pairs)) {\n        areas.push({\n          sets: [a, b],\n          size: 0\n        });\n      }\n    }\n  }\n  return areas;\n}\n/// Returns two matrices, one of the euclidean distances between the sets\n/// and the other indicating if there are subset or disjoint set relationships\nexport function getDistanceMatrices(areas, sets, setids) {\n  // initialize an empty distance matrix between all the points\n  const distances = zerosM(sets.length, sets.length),\n    constraints = zerosM(sets.length, sets.length);\n  // compute required distances between all the sets such that\n  // the areas match\n  areas.filter(function (x) {\n    return x.sets.length == 2;\n  }).map(function (current) {\n    const left = setids[current.sets[0]],\n      right = setids[current.sets[1]],\n      r1 = Math.sqrt(sets[left].size / Math.PI),\n      r2 = Math.sqrt(sets[right].size / Math.PI),\n      distance = distanceFromIntersectArea(r1, r2, current.size);\n    distances[left][right] = distances[right][left] = distance;\n    // also update constraints to indicate if its a subset or disjoint\n    // relationship\n    let c = 0;\n    if (current.size + 1e-10 >= Math.min(sets[left].size, sets[right].size)) {\n      c = 1;\n    } else if (current.size <= 1e-10) {\n      c = -1;\n    }\n    constraints[left][right] = constraints[right][left] = c;\n  });\n  return {\n    distances: distances,\n    constraints: constraints\n  };\n}\n/// computes the gradient and loss simulatenously for our constrained MDS optimizer\nfunction constrainedMDSGradient(x, fxprime, distances, constraints) {\n  let loss = 0,\n    i;\n  for (i = 0; i < fxprime.length; ++i) {\n    fxprime[i] = 0;\n  }\n  for (i = 0; i < distances.length; ++i) {\n    const xi = x[2 * i],\n      yi = x[2 * i + 1];\n    for (let j = i + 1; j < distances.length; ++j) {\n      const xj = x[2 * j],\n        yj = x[2 * j + 1],\n        dij = distances[i][j],\n        constraint = constraints[i][j];\n      const squaredDistance = (xj - xi) * (xj - xi) + (yj - yi) * (yj - yi),\n        distance = Math.sqrt(squaredDistance),\n        delta = squaredDistance - dij * dij;\n      if (constraint > 0 && distance <= dij || constraint < 0 && distance >= dij) {\n        continue;\n      }\n      loss += 2 * delta * delta;\n      fxprime[2 * i] += 4 * delta * (xi - xj);\n      fxprime[2 * i + 1] += 4 * delta * (yi - yj);\n      fxprime[2 * j] += 4 * delta * (xj - xi);\n      fxprime[2 * j + 1] += 4 * delta * (yj - yi);\n    }\n  }\n  return loss;\n}\n/// takes the best working variant of either constrained MDS or greedy\nexport function bestInitialLayout(areas, params) {\n  let initial = greedyLayout(areas, params);\n  const loss = params.lossFunction || lossFunction;\n  // greedylayout is sufficient for all 2/3 circle cases. try out\n  // constrained MDS for higher order problems, take its output\n  // if it outperforms. (greedy is aesthetically better on 2/3 circles\n  // since it axis aligns)\n  if (areas.length >= 8) {\n    const constrained = constrainedMDSLayout(areas, params),\n      constrainedLoss = loss(constrained, areas),\n      greedyLoss = loss(initial, areas);\n    if (constrainedLoss + 1e-8 < greedyLoss) {\n      initial = constrained;\n    }\n  }\n  return initial;\n}\n/// use the constrained MDS variant to generate an initial layout\nexport function constrainedMDSLayout(areas, params) {\n  params = params || {};\n  const restarts = params.restarts || 10;\n  // bidirectionally map sets to a rowid  (so we can create a matrix)\n  const sets = [],\n    setids = {};\n  let i;\n  for (i = 0; i < areas.length; ++i) {\n    const area = areas[i];\n    if (area.sets.length == 1) {\n      setids[area.sets[0]] = sets.length;\n      sets.push(area);\n    }\n  }\n  const matrices = getDistanceMatrices(areas, sets, setids);\n  let distances = matrices.distances;\n  const constraints = matrices.constraints;\n  // keep distances bounded, things get messed up otherwise.\n  // TODO: proper preconditioner?\n  const norm = norm2(distances.map(norm2)) / distances.length;\n  distances = distances.map(function (row) {\n    return row.map(function (value) {\n      return value / norm;\n    });\n  });\n  const obj = function (x, fxprime) {\n    return constrainedMDSGradient(x, fxprime, distances, constraints);\n  };\n  let best, current;\n  for (i = 0; i < restarts; ++i) {\n    const initial = zeros(distances.length * 2).map(Math.random);\n    current = conjugateGradient(obj, initial, params);\n    if (!best || current.fx < best.fx) {\n      best = current;\n    }\n  }\n  const positions = best.x;\n  // translate rows back to (x,y,radius) coordinates\n  const circles = {};\n  for (i = 0; i < sets.length; ++i) {\n    const set = sets[i];\n    circles[set.sets[0]] = {\n      x: positions[2 * i] * norm,\n      y: positions[2 * i + 1] * norm,\n      radius: Math.sqrt(set.size / Math.PI)\n    };\n  }\n  if (params.history) {\n    for (i = 0; i < params.history.length; ++i) {\n      scale(params.history[i].x, norm);\n    }\n  }\n  return circles;\n}\n/** Lays out a Venn diagram greedily, going from most overlapped sets to\nleast overlapped, attempting to position each new set such that the\noverlapping areas to already positioned sets are basically right */\nexport function greedyLayout(areas, params) {\n  const loss = params && params.lossFunction ? params.lossFunction : lossFunction;\n  // define a circle for each set\n  const circles = {},\n    setOverlaps = {};\n  let set;\n  for (let i = 0; i < areas.length; ++i) {\n    const area = areas[i];\n    if (area.sets.length == 1) {\n      set = area.sets[0];\n      circles[set] = {\n        x: 1e10,\n        y: 1e10,\n        // rowid: circles.length, // fix to ->\n        rowid: Object.keys(circles).length,\n        size: area.size,\n        radius: Math.sqrt(area.size / Math.PI)\n      };\n      setOverlaps[set] = [];\n    }\n  }\n  areas = areas.filter(function (a) {\n    return a.sets.length == 2;\n  });\n  // map each set to a list of all the other sets that overlap it\n  for (let i = 0; i < areas.length; ++i) {\n    const current = areas[i];\n    // eslint-disable-next-line\n    let weight = current.hasOwnProperty('weight') ? current.weight : 1.0;\n    const left = current.sets[0],\n      right = current.sets[1];\n    // completely overlapped circles shouldn't be positioned early here\n    if (current.size + SMALL >= Math.min(circles[left].size, circles[right].size)) {\n      weight = 0;\n    }\n    setOverlaps[left].push({\n      set: right,\n      size: current.size,\n      weight: weight\n    });\n    setOverlaps[right].push({\n      set: left,\n      size: current.size,\n      weight: weight\n    });\n  }\n  // get list of most overlapped sets\n  const mostOverlapped = [];\n  for (set in setOverlaps) {\n    // eslint-disable-next-line\n    if (setOverlaps.hasOwnProperty(set)) {\n      let size = 0;\n      for (let i = 0; i < setOverlaps[set].length; ++i) {\n        size += setOverlaps[set][i].size * setOverlaps[set][i].weight;\n      }\n      mostOverlapped.push({\n        set: set,\n        size: size\n      });\n    }\n  }\n  // sort by size desc\n  function sortOrder(a, b) {\n    return b.size - a.size;\n  }\n  mostOverlapped.sort(sortOrder);\n  // keep track of what sets have been laid out\n  const positioned = {};\n  function isPositioned(element) {\n    return element.set in positioned;\n  }\n  // adds a point to the output\n  function positionSet(point, index) {\n    circles[index].x = point.x;\n    circles[index].y = point.y;\n    positioned[index] = true;\n  }\n  // add most overlapped set at (0,0)\n  positionSet({\n    x: 0,\n    y: 0\n  }, mostOverlapped[0].set);\n  // get distances between all points. TODO, necessary?\n  // answer: probably not\n  // var distances = venn.getDistanceMatrices(circles, areas).distances;\n  for (let i = 1; i < mostOverlapped.length; ++i) {\n    const setIndex = mostOverlapped[i].set,\n      overlap = setOverlaps[setIndex].filter(isPositioned);\n    set = circles[setIndex];\n    overlap.sort(sortOrder);\n    if (overlap.length === 0) {\n      // this shouldn't happen anymore with addMissingAreas\n      throw 'ERROR: missing pairwise overlap information';\n    }\n    const points = [];\n    for (let j = 0; j < overlap.length; ++j) {\n      // get appropriate distance from most overlapped already added set\n      const p1 = circles[overlap[j].set],\n        d1 = distanceFromIntersectArea(set.radius, p1.radius, overlap[j].size);\n      // sample positions at 90 degrees for maximum aesthetics\n      points.push({\n        x: p1.x + d1,\n        y: p1.y\n      });\n      points.push({\n        x: p1.x - d1,\n        y: p1.y\n      });\n      points.push({\n        y: p1.y + d1,\n        x: p1.x\n      });\n      points.push({\n        y: p1.y - d1,\n        x: p1.x\n      });\n      // if we have at least 2 overlaps, then figure out where the\n      // set should be positioned analytically and try those too\n      for (let k = j + 1; k < overlap.length; ++k) {\n        const p2 = circles[overlap[k].set],\n          d2 = distanceFromIntersectArea(set.radius, p2.radius, overlap[k].size);\n        const extraPoints = circleCircleIntersection({\n          x: p1.x,\n          y: p1.y,\n          radius: d1\n        }, {\n          x: p2.x,\n          y: p2.y,\n          radius: d2\n        });\n        for (let l = 0; l < extraPoints.length; ++l) {\n          points.push(extraPoints[l]);\n        }\n      }\n    }\n    // we have some candidate positions for the set, examine loss\n    // at each position to figure out where to put it at\n    let bestLoss = 1e50,\n      bestPoint = points[0];\n    for (let j = 0; j < points.length; ++j) {\n      circles[setIndex].x = points[j].x;\n      circles[setIndex].y = points[j].y;\n      const localLoss = loss(circles, areas);\n      if (localLoss < bestLoss) {\n        bestLoss = localLoss;\n        bestPoint = points[j];\n      }\n    }\n    positionSet(bestPoint, setIndex);\n  }\n  return circles;\n}\n/** Given a bunch of sets, and the desired overlaps between these sets - computes\nthe distance from the actual overlaps to the desired overlaps. Note that\nthis method ignores overlaps of more than 2 circles */\nexport function lossFunction(sets, overlaps) {\n  let output = 0;\n  function getCircles(indices) {\n    return indices.map(function (i) {\n      return sets[i];\n    });\n  }\n  for (let i = 0; i < overlaps.length; ++i) {\n    const area = overlaps[i];\n    let overlap;\n    if (area.sets.length == 1) {\n      continue;\n    } else if (area.sets.length == 2) {\n      const left = sets[area.sets[0]],\n        right = sets[area.sets[1]];\n      overlap = circleOverlap(left.radius, right.radius, distance(left, right));\n    } else {\n      overlap = intersectionArea(getCircles(area.sets));\n    }\n    // eslint-disable-next-line\n    const weight = area.hasOwnProperty('weight') ? area.weight : 1.0;\n    output += weight * (overlap - area.size) * (overlap - area.size);\n  }\n  return output;\n}\n// orientates a bunch of circles to point in orientation\nfunction orientateCircles(circles, orientation, orientationOrder) {\n  if (orientationOrder === null) {\n    circles.sort(function (a, b) {\n      return b.radius - a.radius;\n    });\n  } else {\n    circles.sort(orientationOrder);\n  }\n  let i;\n  // shift circles so largest circle is at (0, 0)\n  if (circles.length > 0) {\n    const largestX = circles[0].x,\n      largestY = circles[0].y;\n    for (i = 0; i < circles.length; ++i) {\n      circles[i].x -= largestX;\n      circles[i].y -= largestY;\n    }\n  }\n  if (circles.length == 2) {\n    // if the second circle is a subset of the first, arrange so that\n    // it is off to one side. hack for https://github.com/benfred/venn.js/issues/120\n    const dist = distance(circles[0], circles[1]);\n    if (dist < Math.abs(circles[1].radius - circles[0].radius)) {\n      circles[1].x = circles[0].x + circles[0].radius - circles[1].radius - 1e-10;\n      circles[1].y = circles[0].y;\n    }\n  }\n  // rotate circles so that second largest is at an angle of 'orientation'\n  // from largest\n  if (circles.length > 1) {\n    const rotation = Math.atan2(circles[1].x, circles[1].y) - orientation;\n    let x, y;\n    const c = Math.cos(rotation),\n      s = Math.sin(rotation);\n    for (i = 0; i < circles.length; ++i) {\n      x = circles[i].x;\n      y = circles[i].y;\n      circles[i].x = c * x - s * y;\n      circles[i].y = s * x + c * y;\n    }\n  }\n  // mirror solution if third solution is above plane specified by\n  // first two circles\n  if (circles.length > 2) {\n    let angle = Math.atan2(circles[2].x, circles[2].y) - orientation;\n    while (angle < 0) {\n      angle += 2 * Math.PI;\n    }\n    while (angle > 2 * Math.PI) {\n      angle -= 2 * Math.PI;\n    }\n    if (angle > Math.PI) {\n      const slope = circles[1].y / (1e-10 + circles[1].x);\n      for (i = 0; i < circles.length; ++i) {\n        const d = (circles[i].x + slope * circles[i].y) / (1 + slope * slope);\n        circles[i].x = 2 * d - circles[i].x;\n        circles[i].y = 2 * d * slope - circles[i].y;\n      }\n    }\n  }\n}\nexport function disjointCluster(circles) {\n  // union-find clustering to get disjoint sets\n  circles.map(function (circle) {\n    circle.parent = circle;\n  });\n  // path compression step in union find\n  function find(circle) {\n    if (circle.parent !== circle) {\n      circle.parent = find(circle.parent);\n    }\n    return circle.parent;\n  }\n  function union(x, y) {\n    const xRoot = find(x),\n      yRoot = find(y);\n    xRoot.parent = yRoot;\n  }\n  // get the union of all overlapping sets\n  for (let i = 0; i < circles.length; ++i) {\n    for (let j = i + 1; j < circles.length; ++j) {\n      const maxDistance = circles[i].radius + circles[j].radius;\n      if (distance(circles[i], circles[j]) + 1e-10 < maxDistance) {\n        union(circles[j], circles[i]);\n      }\n    }\n  }\n  // find all the disjoint clusters and group them together\n  const disjointClusters = {};\n  let setid;\n  for (let i = 0; i < circles.length; ++i) {\n    setid = find(circles[i]).parent.setid;\n    if (!(setid in disjointClusters)) {\n      disjointClusters[setid] = [];\n    }\n    disjointClusters[setid].push(circles[i]);\n  }\n  // cleanup bookkeeping\n  circles.map(function (circle) {\n    delete circle.parent;\n  });\n  // return in more usable form\n  const ret = [];\n  for (setid in disjointClusters) {\n    // eslint-disable-next-line\n    if (disjointClusters.hasOwnProperty(setid)) {\n      ret.push(disjointClusters[setid]);\n    }\n  }\n  return ret;\n}\nfunction getBoundingBox(circles) {\n  const minMax = function (d) {\n    const hi = Math.max.apply(null, circles.map(function (c) {\n        return c[d] + c.radius;\n      })),\n      lo = Math.min.apply(null, circles.map(function (c) {\n        return c[d] - c.radius;\n      }));\n    return {\n      max: hi,\n      min: lo\n    };\n  };\n  return {\n    xRange: minMax('x'),\n    yRange: minMax('y')\n  };\n}\nexport function normalizeSolution(solution, orientation, orientationOrder) {\n  if (orientation === null) {\n    orientation = Math.PI / 2;\n  }\n  // work with a list instead of a dictionary, and take a copy so we\n  // don't mutate input\n  let circles = [],\n    i,\n    setid;\n  for (setid in solution) {\n    // eslint-disable-next-line\n    if (solution.hasOwnProperty(setid)) {\n      const previous = solution[setid];\n      circles.push({\n        x: previous.x,\n        y: previous.y,\n        radius: previous.radius,\n        setid: setid\n      });\n    }\n  }\n  // get all the disjoint clusters\n  const clusters = disjointCluster(circles);\n  // orientate all disjoint sets, get sizes\n  for (i = 0; i < clusters.length; ++i) {\n    orientateCircles(clusters[i], orientation, orientationOrder);\n    const bounds = getBoundingBox(clusters[i]);\n    clusters[i].size = (bounds.xRange.max - bounds.xRange.min) * (bounds.yRange.max - bounds.yRange.min);\n    clusters[i].bounds = bounds;\n  }\n  clusters.sort(function (a, b) {\n    return b.size - a.size;\n  });\n  // orientate the largest at 0,0, and get the bounds\n  circles = clusters[0];\n  // @ts-ignore fixme 从逻辑上看似乎是不对的，后续看看\n  let returnBounds = circles.bounds;\n  const spacing = (returnBounds.xRange.max - returnBounds.xRange.min) / 50;\n  function addCluster(cluster, right, bottom) {\n    if (!cluster) return;\n    const bounds = cluster.bounds;\n    let xOffset, yOffset, centreing;\n    if (right) {\n      xOffset = returnBounds.xRange.max - bounds.xRange.min + spacing;\n    } else {\n      xOffset = returnBounds.xRange.max - bounds.xRange.max;\n      centreing = (bounds.xRange.max - bounds.xRange.min) / 2 - (returnBounds.xRange.max - returnBounds.xRange.min) / 2;\n      if (centreing < 0) xOffset += centreing;\n    }\n    if (bottom) {\n      yOffset = returnBounds.yRange.max - bounds.yRange.min + spacing;\n    } else {\n      yOffset = returnBounds.yRange.max - bounds.yRange.max;\n      centreing = (bounds.yRange.max - bounds.yRange.min) / 2 - (returnBounds.yRange.max - returnBounds.yRange.min) / 2;\n      if (centreing < 0) yOffset += centreing;\n    }\n    for (let j = 0; j < cluster.length; ++j) {\n      cluster[j].x += xOffset;\n      cluster[j].y += yOffset;\n      circles.push(cluster[j]);\n    }\n  }\n  let index = 1;\n  while (index < clusters.length) {\n    addCluster(clusters[index], true, false);\n    addCluster(clusters[index + 1], false, true);\n    addCluster(clusters[index + 2], true, true);\n    index += 3;\n    // have one cluster (in top left). lay out next three relative\n    // to it in a grid\n    returnBounds = getBoundingBox(circles);\n  }\n  // convert back to solution form\n  const ret = {};\n  for (i = 0; i < circles.length; ++i) {\n    ret[circles[i].setid] = circles[i];\n  }\n  return ret;\n}\n/** Scales a solution from venn.venn or venn.greedyLayout such that it fits in\na rectangle of width/height - with padding around the borders. also\ncenters the diagram in the available space at the same time */\nexport function scaleSolution(solution, width, height, padding) {\n  const circles = [],\n    setids = [];\n  for (const setid in solution) {\n    // eslint-disable-next-line\n    if (solution.hasOwnProperty(setid)) {\n      setids.push(setid);\n      circles.push(solution[setid]);\n    }\n  }\n  width -= 2 * padding;\n  height -= 2 * padding;\n  const bounds = getBoundingBox(circles),\n    xRange = bounds.xRange,\n    yRange = bounds.yRange;\n  if (xRange.max == xRange.min || yRange.max == yRange.min) {\n    console.log('not scaling solution: zero size detected');\n    return solution;\n  }\n  const xScaling = width / (xRange.max - xRange.min),\n    yScaling = height / (yRange.max - yRange.min),\n    scaling = Math.min(yScaling, xScaling),\n    // while we're at it, center the diagram too\n    xOffset = (width - (xRange.max - xRange.min) * scaling) / 2,\n    yOffset = (height - (yRange.max - yRange.min) * scaling) / 2;\n  const scaled = {};\n  for (let i = 0; i < circles.length; ++i) {\n    const circle = circles[i];\n    scaled[setids[i]] = {\n      radius: scaling * circle.radius,\n      x: padding + xOffset + (circle.x - xRange.min) * scaling,\n      y: padding + yOffset + (circle.y - yRange.min) * scaling\n    };\n  }\n  return scaled;\n}", "map": {"version": 3, "names": ["bisect", "conjugateGradient", "nelderMead", "norm2", "scale", "zeros", "zerosM", "circleCircleIntersection", "circleOverlap", "distance", "intersectionArea", "venn", "areas", "parameters", "maxIterations", "initialLayout", "bestInitialLayout", "loss", "lossFunction", "addMissingAreas", "circles", "initial", "setids", "setid", "hasOwnProperty", "push", "x", "y", "solution", "values", "current", "i", "length", "radius", "positions", "SMALL", "distanceFromIntersectArea", "r1", "r2", "overlap", "Math", "min", "PI", "abs", "slice", "ids", "pairs", "j", "a", "b", "area", "sets", "sort", "size", "getDistanceMatrices", "distances", "constraints", "filter", "map", "left", "right", "sqrt", "c", "constrainedMDSGradient", "fxprime", "xi", "yi", "xj", "yj", "dij", "constraint", "squaredDistance", "delta", "params", "greedyLayout", "constrained", "constrainedMDSLayout", "constrainedLoss", "<PERSON><PERSON><PERSON>", "restarts", "matrices", "norm", "row", "value", "obj", "best", "random", "fx", "set", "history", "setOverlaps", "rowid", "Object", "keys", "weight", "mostOverlapped", "sortOrder", "positioned", "isPositioned", "element", "positionSet", "point", "index", "setIndex", "points", "p1", "d1", "k", "p2", "d2", "extraPoints", "l", "bestLoss", "bestPoint", "localLoss", "overlaps", "output", "getCircles", "indices", "orientateCircles", "orientation", "orientationOrder", "largestX", "largestY", "dist", "rotation", "atan2", "cos", "s", "sin", "angle", "slope", "d", "disjointCluster", "circle", "parent", "find", "union", "xRoot", "yRoot", "maxDistance", "disjointClusters", "ret", "getBoundingBox", "minMax", "hi", "max", "apply", "lo", "xRange", "y<PERSON><PERSON><PERSON>", "normalizeSolution", "previous", "clusters", "bounds", "returnBounds", "spacing", "addCluster", "cluster", "bottom", "xOffset", "yOffset", "centreing", "scaleSolution", "width", "height", "padding", "console", "log", "xScaling", "yScaling", "scaling", "scaled"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/venn/layout.ts"], "sourcesContent": ["import {\n  bisect,\n  conjugateGradient,\n  nelderMead,\n  norm2,\n  scale,\n  zeros,\n  zerosM,\n} from './fmin';\nimport {\n  circleCircleIntersection,\n  circleOverlap,\n  distance,\n  intersectionArea,\n} from './circleintersection';\n\n/** given a list of set objects, and their corresponding overlaps.\nupdates the (x, y, radius) attribute on each set such that their positions\nroughly correspond to the desired overlaps */\nexport function venn(areas, parameters?: any) {\n  parameters = parameters || {};\n  parameters.maxIterations = parameters.maxIterations || 500;\n  const initialLayout = parameters.initialLayout || bestInitialLayout;\n  const loss = parameters.lossFunction || lossFunction;\n\n  // add in missing pairwise areas as having 0 size\n  areas = addMissingAreas(areas);\n\n  // initial layout is done greedily\n  const circles = initialLayout(areas, parameters);\n\n  // transform x/y coordinates to a vector to optimize\n  const initial = [],\n    setids = [];\n  let setid;\n  for (setid in circles) {\n    // eslint-disable-next-line\n    if (circles.hasOwnProperty(setid)) {\n      initial.push(circles[setid].x);\n      initial.push(circles[setid].y);\n      setids.push(setid);\n    }\n  }\n\n  // optimize initial layout from our loss function\n  const solution = nelderMead(\n    function (values) {\n      const current = {};\n      for (let i = 0; i < setids.length; ++i) {\n        const setid = setids[i];\n        current[setid] = {\n          x: values[2 * i],\n          y: values[2 * i + 1],\n          radius: circles[setid].radius,\n        };\n      }\n      return loss(current, areas);\n    },\n    initial,\n    parameters,\n  );\n\n  // transform solution vector back to x/y points\n  const positions = solution.x;\n  for (let i = 0; i < setids.length; ++i) {\n    setid = setids[i];\n    circles[setid].x = positions[2 * i];\n    circles[setid].y = positions[2 * i + 1];\n  }\n\n  return circles;\n}\n\nconst SMALL = 1e-10;\n\n/** Returns the distance necessary for two circles of radius r1 + r2 to\nhave the overlap area 'overlap' */\nexport function distanceFromIntersectArea(r1, r2, overlap) {\n  // handle complete overlapped circles\n  if (Math.min(r1, r2) * Math.min(r1, r2) * Math.PI <= overlap + SMALL) {\n    return Math.abs(r1 - r2);\n  }\n\n  return bisect(\n    function (distance) {\n      return circleOverlap(r1, r2, distance) - overlap;\n    },\n    0,\n    r1 + r2,\n  );\n}\n\n/** Missing pair-wise intersection area data can cause problems:\n treating as an unknown means that sets will be laid out overlapping,\n which isn't what people expect. To reflect that we want disjoint sets\n here, set the overlap to 0 for all missing pairwise set intersections */\nfunction addMissingAreas(areas) {\n  areas = areas.slice();\n\n  // two circle intersections that aren't defined\n  const ids: number[] = [],\n    pairs: any = {};\n  let i, j, a, b;\n  for (i = 0; i < areas.length; ++i) {\n    const area = areas[i];\n    if (area.sets.length == 1) {\n      ids.push(area.sets[0]);\n    } else if (area.sets.length == 2) {\n      a = area.sets[0];\n      b = area.sets[1];\n      // @ts-ignore\n      pairs[[a, b]] = true;\n      // @ts-ignore\n      pairs[[b, a]] = true;\n    }\n  }\n  ids.sort((a, b) => {\n    return a > b ? 1 : -1;\n  });\n\n  for (i = 0; i < ids.length; ++i) {\n    a = ids[i];\n    for (j = i + 1; j < ids.length; ++j) {\n      b = ids[j];\n      // @ts-ignore\n      if (!([a, b] in pairs)) {\n        areas.push({ sets: [a, b], size: 0 });\n      }\n    }\n  }\n  return areas;\n}\n\n/// Returns two matrices, one of the euclidean distances between the sets\n/// and the other indicating if there are subset or disjoint set relationships\nexport function getDistanceMatrices(areas, sets, setids) {\n  // initialize an empty distance matrix between all the points\n  const distances = zerosM(sets.length, sets.length),\n    constraints = zerosM(sets.length, sets.length);\n\n  // compute required distances between all the sets such that\n  // the areas match\n  areas\n    .filter(function (x) {\n      return x.sets.length == 2;\n    })\n    .map(function (current) {\n      const left = setids[current.sets[0]],\n        right = setids[current.sets[1]],\n        r1 = Math.sqrt(sets[left].size / Math.PI),\n        r2 = Math.sqrt(sets[right].size / Math.PI),\n        distance = distanceFromIntersectArea(r1, r2, current.size);\n\n      distances[left][right] = distances[right][left] = distance;\n\n      // also update constraints to indicate if its a subset or disjoint\n      // relationship\n      let c = 0;\n      if (current.size + 1e-10 >= Math.min(sets[left].size, sets[right].size)) {\n        c = 1;\n      } else if (current.size <= 1e-10) {\n        c = -1;\n      }\n      constraints[left][right] = constraints[right][left] = c;\n    });\n\n  return { distances: distances, constraints: constraints };\n}\n\n/// computes the gradient and loss simulatenously for our constrained MDS optimizer\nfunction constrainedMDSGradient(x, fxprime, distances, constraints) {\n  let loss = 0,\n    i;\n  for (i = 0; i < fxprime.length; ++i) {\n    fxprime[i] = 0;\n  }\n\n  for (i = 0; i < distances.length; ++i) {\n    const xi = x[2 * i],\n      yi = x[2 * i + 1];\n    for (let j = i + 1; j < distances.length; ++j) {\n      const xj = x[2 * j],\n        yj = x[2 * j + 1],\n        dij = distances[i][j],\n        constraint = constraints[i][j];\n\n      const squaredDistance = (xj - xi) * (xj - xi) + (yj - yi) * (yj - yi),\n        distance = Math.sqrt(squaredDistance),\n        delta = squaredDistance - dij * dij;\n\n      if (\n        (constraint > 0 && distance <= dij) ||\n        (constraint < 0 && distance >= dij)\n      ) {\n        continue;\n      }\n\n      loss += 2 * delta * delta;\n\n      fxprime[2 * i] += 4 * delta * (xi - xj);\n      fxprime[2 * i + 1] += 4 * delta * (yi - yj);\n\n      fxprime[2 * j] += 4 * delta * (xj - xi);\n      fxprime[2 * j + 1] += 4 * delta * (yj - yi);\n    }\n  }\n  return loss;\n}\n\n/// takes the best working variant of either constrained MDS or greedy\nexport function bestInitialLayout(areas, params) {\n  let initial = greedyLayout(areas, params);\n  const loss = params.lossFunction || lossFunction;\n\n  // greedylayout is sufficient for all 2/3 circle cases. try out\n  // constrained MDS for higher order problems, take its output\n  // if it outperforms. (greedy is aesthetically better on 2/3 circles\n  // since it axis aligns)\n  if (areas.length >= 8) {\n    const constrained = constrainedMDSLayout(areas, params),\n      constrainedLoss = loss(constrained, areas),\n      greedyLoss = loss(initial, areas);\n\n    if (constrainedLoss + 1e-8 < greedyLoss) {\n      initial = constrained;\n    }\n  }\n  return initial;\n}\n\n/// use the constrained MDS variant to generate an initial layout\nexport function constrainedMDSLayout(areas, params) {\n  params = params || {};\n  const restarts = params.restarts || 10;\n\n  // bidirectionally map sets to a rowid  (so we can create a matrix)\n  const sets = [],\n    setids = {};\n  let i;\n  for (i = 0; i < areas.length; ++i) {\n    const area = areas[i];\n    if (area.sets.length == 1) {\n      setids[area.sets[0]] = sets.length;\n      sets.push(area);\n    }\n  }\n\n  const matrices = getDistanceMatrices(areas, sets, setids);\n  let distances = matrices.distances;\n  const constraints = matrices.constraints;\n\n  // keep distances bounded, things get messed up otherwise.\n  // TODO: proper preconditioner?\n  const norm = norm2(distances.map(norm2)) / distances.length;\n  distances = distances.map(function (row) {\n    return row.map(function (value) {\n      return value / norm;\n    });\n  });\n\n  const obj = function (x, fxprime) {\n    return constrainedMDSGradient(x, fxprime, distances, constraints);\n  };\n\n  let best, current;\n  for (i = 0; i < restarts; ++i) {\n    const initial = zeros(distances.length * 2).map(Math.random);\n\n    current = conjugateGradient(obj, initial, params);\n    if (!best || current.fx < best.fx) {\n      best = current;\n    }\n  }\n  const positions = best.x;\n\n  // translate rows back to (x,y,radius) coordinates\n  const circles = {};\n  for (i = 0; i < sets.length; ++i) {\n    const set = sets[i];\n    circles[set.sets[0]] = {\n      x: positions[2 * i] * norm,\n      y: positions[2 * i + 1] * norm,\n      radius: Math.sqrt(set.size / Math.PI),\n    };\n  }\n\n  if (params.history) {\n    for (i = 0; i < params.history.length; ++i) {\n      scale(params.history[i].x, norm);\n    }\n  }\n  return circles;\n}\n\n/** Lays out a Venn diagram greedily, going from most overlapped sets to\nleast overlapped, attempting to position each new set such that the\noverlapping areas to already positioned sets are basically right */\nexport function greedyLayout(areas, params) {\n  const loss =\n    params && params.lossFunction ? params.lossFunction : lossFunction;\n  // define a circle for each set\n  const circles = {},\n    setOverlaps = {};\n  let set;\n  for (let i = 0; i < areas.length; ++i) {\n    const area = areas[i];\n    if (area.sets.length == 1) {\n      set = area.sets[0];\n      circles[set] = {\n        x: 1e10,\n        y: 1e10,\n        // rowid: circles.length, // fix to ->\n        rowid: Object.keys(circles).length,\n        size: area.size,\n        radius: Math.sqrt(area.size / Math.PI),\n      };\n      setOverlaps[set] = [];\n    }\n  }\n  areas = areas.filter(function (a) {\n    return a.sets.length == 2;\n  });\n\n  // map each set to a list of all the other sets that overlap it\n  for (let i = 0; i < areas.length; ++i) {\n    const current = areas[i];\n    // eslint-disable-next-line\n    let weight = current.hasOwnProperty('weight') ? current.weight : 1.0;\n    const left = current.sets[0],\n      right = current.sets[1];\n\n    // completely overlapped circles shouldn't be positioned early here\n    if (\n      current.size + SMALL >=\n      Math.min(circles[left].size, circles[right].size)\n    ) {\n      weight = 0;\n    }\n\n    setOverlaps[left].push({ set: right, size: current.size, weight: weight });\n    setOverlaps[right].push({ set: left, size: current.size, weight: weight });\n  }\n\n  // get list of most overlapped sets\n  const mostOverlapped = [];\n  for (set in setOverlaps) {\n    // eslint-disable-next-line\n    if (setOverlaps.hasOwnProperty(set)) {\n      let size = 0;\n      for (let i = 0; i < setOverlaps[set].length; ++i) {\n        size += setOverlaps[set][i].size * setOverlaps[set][i].weight;\n      }\n\n      mostOverlapped.push({ set: set, size: size });\n    }\n  }\n\n  // sort by size desc\n  function sortOrder(a, b) {\n    return b.size - a.size;\n  }\n  mostOverlapped.sort(sortOrder);\n\n  // keep track of what sets have been laid out\n  const positioned = {};\n  function isPositioned(element) {\n    return element.set in positioned;\n  }\n\n  // adds a point to the output\n  function positionSet(point, index) {\n    circles[index].x = point.x;\n    circles[index].y = point.y;\n    positioned[index] = true;\n  }\n\n  // add most overlapped set at (0,0)\n  positionSet({ x: 0, y: 0 }, mostOverlapped[0].set);\n\n  // get distances between all points. TODO, necessary?\n  // answer: probably not\n  // var distances = venn.getDistanceMatrices(circles, areas).distances;\n  for (let i = 1; i < mostOverlapped.length; ++i) {\n    const setIndex = mostOverlapped[i].set,\n      overlap = setOverlaps[setIndex].filter(isPositioned);\n    set = circles[setIndex];\n    overlap.sort(sortOrder);\n\n    if (overlap.length === 0) {\n      // this shouldn't happen anymore with addMissingAreas\n      throw 'ERROR: missing pairwise overlap information';\n    }\n\n    const points = [];\n    for (let j = 0; j < overlap.length; ++j) {\n      // get appropriate distance from most overlapped already added set\n      const p1 = circles[overlap[j].set],\n        d1 = distanceFromIntersectArea(set.radius, p1.radius, overlap[j].size);\n\n      // sample positions at 90 degrees for maximum aesthetics\n      points.push({ x: p1.x + d1, y: p1.y });\n      points.push({ x: p1.x - d1, y: p1.y });\n      points.push({ y: p1.y + d1, x: p1.x });\n      points.push({ y: p1.y - d1, x: p1.x });\n\n      // if we have at least 2 overlaps, then figure out where the\n      // set should be positioned analytically and try those too\n      for (let k = j + 1; k < overlap.length; ++k) {\n        const p2 = circles[overlap[k].set],\n          d2 = distanceFromIntersectArea(\n            set.radius,\n            p2.radius,\n            overlap[k].size,\n          );\n\n        const extraPoints = circleCircleIntersection(\n          { x: p1.x, y: p1.y, radius: d1 },\n          { x: p2.x, y: p2.y, radius: d2 },\n        );\n\n        for (let l = 0; l < extraPoints.length; ++l) {\n          points.push(extraPoints[l]);\n        }\n      }\n    }\n\n    // we have some candidate positions for the set, examine loss\n    // at each position to figure out where to put it at\n    let bestLoss = 1e50,\n      bestPoint = points[0];\n    for (let j = 0; j < points.length; ++j) {\n      circles[setIndex].x = points[j].x;\n      circles[setIndex].y = points[j].y;\n      const localLoss = loss(circles, areas);\n      if (localLoss < bestLoss) {\n        bestLoss = localLoss;\n        bestPoint = points[j];\n      }\n    }\n\n    positionSet(bestPoint, setIndex);\n  }\n\n  return circles;\n}\n\n/** Given a bunch of sets, and the desired overlaps between these sets - computes\nthe distance from the actual overlaps to the desired overlaps. Note that\nthis method ignores overlaps of more than 2 circles */\nexport function lossFunction(sets, overlaps) {\n  let output = 0;\n\n  function getCircles(indices) {\n    return indices.map(function (i) {\n      return sets[i];\n    });\n  }\n\n  for (let i = 0; i < overlaps.length; ++i) {\n    const area = overlaps[i];\n    let overlap;\n    if (area.sets.length == 1) {\n      continue;\n    } else if (area.sets.length == 2) {\n      const left = sets[area.sets[0]],\n        right = sets[area.sets[1]];\n      overlap = circleOverlap(left.radius, right.radius, distance(left, right));\n    } else {\n      overlap = intersectionArea(getCircles(area.sets));\n    }\n\n    // eslint-disable-next-line\n    const weight = area.hasOwnProperty('weight') ? area.weight : 1.0;\n    output += weight * (overlap - area.size) * (overlap - area.size);\n  }\n\n  return output;\n}\n\n// orientates a bunch of circles to point in orientation\nfunction orientateCircles(circles, orientation, orientationOrder) {\n  if (orientationOrder === null) {\n    circles.sort(function (a, b) {\n      return b.radius - a.radius;\n    });\n  } else {\n    circles.sort(orientationOrder);\n  }\n\n  let i;\n  // shift circles so largest circle is at (0, 0)\n  if (circles.length > 0) {\n    const largestX = circles[0].x,\n      largestY = circles[0].y;\n\n    for (i = 0; i < circles.length; ++i) {\n      circles[i].x -= largestX;\n      circles[i].y -= largestY;\n    }\n  }\n\n  if (circles.length == 2) {\n    // if the second circle is a subset of the first, arrange so that\n    // it is off to one side. hack for https://github.com/benfred/venn.js/issues/120\n    const dist = distance(circles[0], circles[1]);\n    if (dist < Math.abs(circles[1].radius - circles[0].radius)) {\n      circles[1].x =\n        circles[0].x + circles[0].radius - circles[1].radius - 1e-10;\n      circles[1].y = circles[0].y;\n    }\n  }\n\n  // rotate circles so that second largest is at an angle of 'orientation'\n  // from largest\n  if (circles.length > 1) {\n    const rotation = Math.atan2(circles[1].x, circles[1].y) - orientation;\n    let x, y;\n    const c = Math.cos(rotation),\n      s = Math.sin(rotation);\n    for (i = 0; i < circles.length; ++i) {\n      x = circles[i].x;\n      y = circles[i].y;\n      circles[i].x = c * x - s * y;\n      circles[i].y = s * x + c * y;\n    }\n  }\n\n  // mirror solution if third solution is above plane specified by\n  // first two circles\n  if (circles.length > 2) {\n    let angle = Math.atan2(circles[2].x, circles[2].y) - orientation;\n    while (angle < 0) {\n      angle += 2 * Math.PI;\n    }\n    while (angle > 2 * Math.PI) {\n      angle -= 2 * Math.PI;\n    }\n    if (angle > Math.PI) {\n      const slope = circles[1].y / (1e-10 + circles[1].x);\n      for (i = 0; i < circles.length; ++i) {\n        const d = (circles[i].x + slope * circles[i].y) / (1 + slope * slope);\n        circles[i].x = 2 * d - circles[i].x;\n        circles[i].y = 2 * d * slope - circles[i].y;\n      }\n    }\n  }\n}\n\nexport function disjointCluster(circles) {\n  // union-find clustering to get disjoint sets\n  circles.map(function (circle) {\n    circle.parent = circle;\n  });\n\n  // path compression step in union find\n  function find(circle) {\n    if (circle.parent !== circle) {\n      circle.parent = find(circle.parent);\n    }\n    return circle.parent;\n  }\n\n  function union(x, y) {\n    const xRoot = find(x),\n      yRoot = find(y);\n    xRoot.parent = yRoot;\n  }\n\n  // get the union of all overlapping sets\n  for (let i = 0; i < circles.length; ++i) {\n    for (let j = i + 1; j < circles.length; ++j) {\n      const maxDistance = circles[i].radius + circles[j].radius;\n      if (distance(circles[i], circles[j]) + 1e-10 < maxDistance) {\n        union(circles[j], circles[i]);\n      }\n    }\n  }\n\n  // find all the disjoint clusters and group them together\n  const disjointClusters = {};\n  let setid;\n  for (let i = 0; i < circles.length; ++i) {\n    setid = find(circles[i]).parent.setid;\n    if (!(setid in disjointClusters)) {\n      disjointClusters[setid] = [];\n    }\n    disjointClusters[setid].push(circles[i]);\n  }\n\n  // cleanup bookkeeping\n  circles.map(function (circle) {\n    delete circle.parent;\n  });\n\n  // return in more usable form\n  const ret = [];\n  for (setid in disjointClusters) {\n    // eslint-disable-next-line\n    if (disjointClusters.hasOwnProperty(setid)) {\n      ret.push(disjointClusters[setid]);\n    }\n  }\n  return ret;\n}\n\nfunction getBoundingBox(circles) {\n  const minMax = function (d) {\n    const hi = Math.max.apply(\n        null,\n        circles.map(function (c) {\n          return c[d] + c.radius;\n        }),\n      ),\n      lo = Math.min.apply(\n        null,\n        circles.map(function (c) {\n          return c[d] - c.radius;\n        }),\n      );\n    return { max: hi, min: lo };\n  };\n\n  return { xRange: minMax('x'), yRange: minMax('y') };\n}\n\nexport function normalizeSolution(solution, orientation, orientationOrder) {\n  if (orientation === null) {\n    orientation = Math.PI / 2;\n  }\n\n  // work with a list instead of a dictionary, and take a copy so we\n  // don't mutate input\n  let circles = [],\n    i,\n    setid;\n  for (setid in solution) {\n    // eslint-disable-next-line\n    if (solution.hasOwnProperty(setid)) {\n      const previous = solution[setid];\n      circles.push({\n        x: previous.x,\n        y: previous.y,\n        radius: previous.radius,\n        setid: setid,\n      });\n    }\n  }\n\n  // get all the disjoint clusters\n  const clusters = disjointCluster(circles);\n\n  // orientate all disjoint sets, get sizes\n  for (i = 0; i < clusters.length; ++i) {\n    orientateCircles(clusters[i], orientation, orientationOrder);\n    const bounds = getBoundingBox(clusters[i]);\n    clusters[i].size =\n      (bounds.xRange.max - bounds.xRange.min) *\n      (bounds.yRange.max - bounds.yRange.min);\n    clusters[i].bounds = bounds;\n  }\n  clusters.sort(function (a, b) {\n    return b.size - a.size;\n  });\n\n  // orientate the largest at 0,0, and get the bounds\n  circles = clusters[0];\n  // @ts-ignore fixme 从逻辑上看似乎是不对的，后续看看\n  let returnBounds = circles.bounds;\n\n  const spacing = (returnBounds.xRange.max - returnBounds.xRange.min) / 50;\n\n  function addCluster(cluster, right, bottom) {\n    if (!cluster) return;\n\n    const bounds = cluster.bounds;\n    let xOffset, yOffset, centreing;\n\n    if (right) {\n      xOffset = returnBounds.xRange.max - bounds.xRange.min + spacing;\n    } else {\n      xOffset = returnBounds.xRange.max - bounds.xRange.max;\n      centreing =\n        (bounds.xRange.max - bounds.xRange.min) / 2 -\n        (returnBounds.xRange.max - returnBounds.xRange.min) / 2;\n      if (centreing < 0) xOffset += centreing;\n    }\n\n    if (bottom) {\n      yOffset = returnBounds.yRange.max - bounds.yRange.min + spacing;\n    } else {\n      yOffset = returnBounds.yRange.max - bounds.yRange.max;\n      centreing =\n        (bounds.yRange.max - bounds.yRange.min) / 2 -\n        (returnBounds.yRange.max - returnBounds.yRange.min) / 2;\n      if (centreing < 0) yOffset += centreing;\n    }\n\n    for (let j = 0; j < cluster.length; ++j) {\n      cluster[j].x += xOffset;\n      cluster[j].y += yOffset;\n      circles.push(cluster[j]);\n    }\n  }\n\n  let index = 1;\n  while (index < clusters.length) {\n    addCluster(clusters[index], true, false);\n    addCluster(clusters[index + 1], false, true);\n    addCluster(clusters[index + 2], true, true);\n    index += 3;\n\n    // have one cluster (in top left). lay out next three relative\n    // to it in a grid\n    returnBounds = getBoundingBox(circles);\n  }\n\n  // convert back to solution form\n  const ret = {};\n  for (i = 0; i < circles.length; ++i) {\n    ret[circles[i].setid] = circles[i];\n  }\n  return ret;\n}\n\n/** Scales a solution from venn.venn or venn.greedyLayout such that it fits in\na rectangle of width/height - with padding around the borders. also\ncenters the diagram in the available space at the same time */\nexport function scaleSolution(solution, width, height, padding) {\n  const circles = [],\n    setids = [];\n  for (const setid in solution) {\n    // eslint-disable-next-line\n    if (solution.hasOwnProperty(setid)) {\n      setids.push(setid);\n      circles.push(solution[setid]);\n    }\n  }\n\n  width -= 2 * padding;\n  height -= 2 * padding;\n\n  const bounds = getBoundingBox(circles),\n    xRange = bounds.xRange,\n    yRange = bounds.yRange;\n\n  if (xRange.max == xRange.min || yRange.max == yRange.min) {\n    console.log('not scaling solution: zero size detected');\n    return solution;\n  }\n\n  const xScaling = width / (xRange.max - xRange.min),\n    yScaling = height / (yRange.max - yRange.min),\n    scaling = Math.min(yScaling, xScaling),\n    // while we're at it, center the diagram too\n    xOffset = (width - (xRange.max - xRange.min) * scaling) / 2,\n    yOffset = (height - (yRange.max - yRange.min) * scaling) / 2;\n\n  const scaled = {};\n  for (let i = 0; i < circles.length; ++i) {\n    const circle = circles[i];\n    scaled[setids[i]] = {\n      radius: scaling * circle.radius,\n      x: padding + xOffset + (circle.x - xRange.min) * scaling,\n      y: padding + yOffset + (circle.y - yRange.min) * scaling,\n    };\n  }\n\n  return scaled;\n}\n"], "mappings": "AAAA,SACEA,MAAM,EACNC,iBAAiB,EACjBC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,MAAM,QACD,QAAQ;AACf,SACEC,wBAAwB,EACxBC,aAAa,EACbC,QAAQ,EACRC,gBAAgB,QACX,sBAAsB;AAE7B;;;AAGA,OAAM,SAAUC,IAAIA,CAACC,KAAK,EAAEC,UAAgB;EAC1CA,UAAU,GAAGA,UAAU,IAAI,EAAE;EAC7BA,UAAU,CAACC,aAAa,GAAGD,UAAU,CAACC,aAAa,IAAI,GAAG;EAC1D,MAAMC,aAAa,GAAGF,UAAU,CAACE,aAAa,IAAIC,iBAAiB;EACnE,MAAMC,IAAI,GAAGJ,UAAU,CAACK,YAAY,IAAIA,YAAY;EAEpD;EACAN,KAAK,GAAGO,eAAe,CAACP,KAAK,CAAC;EAE9B;EACA,MAAMQ,OAAO,GAAGL,aAAa,CAACH,KAAK,EAAEC,UAAU,CAAC;EAEhD;EACA,MAAMQ,OAAO,GAAG,EAAE;IAChBC,MAAM,GAAG,EAAE;EACb,IAAIC,KAAK;EACT,KAAKA,KAAK,IAAIH,OAAO,EAAE;IACrB;IACA,IAAIA,OAAO,CAACI,cAAc,CAACD,KAAK,CAAC,EAAE;MACjCF,OAAO,CAACI,IAAI,CAACL,OAAO,CAACG,KAAK,CAAC,CAACG,CAAC,CAAC;MAC9BL,OAAO,CAACI,IAAI,CAACL,OAAO,CAACG,KAAK,CAAC,CAACI,CAAC,CAAC;MAC9BL,MAAM,CAACG,IAAI,CAACF,KAAK,CAAC;;;EAItB;EACA,MAAMK,QAAQ,GAAG1B,UAAU,CACzB,UAAU2B,MAAM;IACd,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACU,MAAM,EAAE,EAAED,CAAC,EAAE;MACtC,MAAMR,KAAK,GAAGD,MAAM,CAACS,CAAC,CAAC;MACvBD,OAAO,CAACP,KAAK,CAAC,GAAG;QACfG,CAAC,EAAEG,MAAM,CAAC,CAAC,GAAGE,CAAC,CAAC;QAChBJ,CAAC,EAAEE,MAAM,CAAC,CAAC,GAAGE,CAAC,GAAG,CAAC,CAAC;QACpBE,MAAM,EAAEb,OAAO,CAACG,KAAK,CAAC,CAACU;OACxB;;IAEH,OAAOhB,IAAI,CAACa,OAAO,EAAElB,KAAK,CAAC;EAC7B,CAAC,EACDS,OAAO,EACPR,UAAU,CACX;EAED;EACA,MAAMqB,SAAS,GAAGN,QAAQ,CAACF,CAAC;EAC5B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACU,MAAM,EAAE,EAAED,CAAC,EAAE;IACtCR,KAAK,GAAGD,MAAM,CAACS,CAAC,CAAC;IACjBX,OAAO,CAACG,KAAK,CAAC,CAACG,CAAC,GAAGQ,SAAS,CAAC,CAAC,GAAGH,CAAC,CAAC;IACnCX,OAAO,CAACG,KAAK,CAAC,CAACI,CAAC,GAAGO,SAAS,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAC;;EAGzC,OAAOX,OAAO;AAChB;AAEA,MAAMe,KAAK,GAAG,KAAK;AAEnB;;AAEA,OAAM,SAAUC,yBAAyBA,CAACC,EAAE,EAAEC,EAAE,EAAEC,OAAO;EACvD;EACA,IAAIC,IAAI,CAACC,GAAG,CAACJ,EAAE,EAAEC,EAAE,CAAC,GAAGE,IAAI,CAACC,GAAG,CAACJ,EAAE,EAAEC,EAAE,CAAC,GAAGE,IAAI,CAACE,EAAE,IAAIH,OAAO,GAAGJ,KAAK,EAAE;IACpE,OAAOK,IAAI,CAACG,GAAG,CAACN,EAAE,GAAGC,EAAE,CAAC;;EAG1B,OAAOtC,MAAM,CACX,UAAUS,QAAQ;IAChB,OAAOD,aAAa,CAAC6B,EAAE,EAAEC,EAAE,EAAE7B,QAAQ,CAAC,GAAG8B,OAAO;EAClD,CAAC,EACD,CAAC,EACDF,EAAE,GAAGC,EAAE,CACR;AACH;AAEA;;;;AAIA,SAASnB,eAAeA,CAACP,KAAK;EAC5BA,KAAK,GAAGA,KAAK,CAACgC,KAAK,EAAE;EAErB;EACA,MAAMC,GAAG,GAAa,EAAE;IACtBC,KAAK,GAAQ,EAAE;EACjB,IAAIf,CAAC,EAAEgB,CAAC,EAAEC,CAAC,EAAEC,CAAC;EACd,KAAKlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,KAAK,CAACoB,MAAM,EAAE,EAAED,CAAC,EAAE;IACjC,MAAMmB,IAAI,GAAGtC,KAAK,CAACmB,CAAC,CAAC;IACrB,IAAImB,IAAI,CAACC,IAAI,CAACnB,MAAM,IAAI,CAAC,EAAE;MACzBa,GAAG,CAACpB,IAAI,CAACyB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;KACvB,MAAM,IAAID,IAAI,CAACC,IAAI,CAACnB,MAAM,IAAI,CAAC,EAAE;MAChCgB,CAAC,GAAGE,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAChBF,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAChB;MACAL,KAAK,CAAC,CAACE,CAAC,EAAEC,CAAC,CAAC,CAAC,GAAG,IAAI;MACpB;MACAH,KAAK,CAAC,CAACG,CAAC,EAAED,CAAC,CAAC,CAAC,GAAG,IAAI;;;EAGxBH,GAAG,CAACO,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,KAAI;IAChB,OAAOD,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EACvB,CAAC,CAAC;EAEF,KAAKlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,GAAG,CAACb,MAAM,EAAE,EAAED,CAAC,EAAE;IAC/BiB,CAAC,GAAGH,GAAG,CAACd,CAAC,CAAC;IACV,KAAKgB,CAAC,GAAGhB,CAAC,GAAG,CAAC,EAAEgB,CAAC,GAAGF,GAAG,CAACb,MAAM,EAAE,EAAEe,CAAC,EAAE;MACnCE,CAAC,GAAGJ,GAAG,CAACE,CAAC,CAAC;MACV;MACA,IAAI,EAAE,CAACC,CAAC,EAAEC,CAAC,CAAC,IAAIH,KAAK,CAAC,EAAE;QACtBlC,KAAK,CAACa,IAAI,CAAC;UAAE0B,IAAI,EAAE,CAACH,CAAC,EAAEC,CAAC,CAAC;UAAEI,IAAI,EAAE;QAAC,CAAE,CAAC;;;;EAI3C,OAAOzC,KAAK;AACd;AAEA;AACA;AACA,OAAM,SAAU0C,mBAAmBA,CAAC1C,KAAK,EAAEuC,IAAI,EAAE7B,MAAM;EACrD;EACA,MAAMiC,SAAS,GAAGjD,MAAM,CAAC6C,IAAI,CAACnB,MAAM,EAAEmB,IAAI,CAACnB,MAAM,CAAC;IAChDwB,WAAW,GAAGlD,MAAM,CAAC6C,IAAI,CAACnB,MAAM,EAAEmB,IAAI,CAACnB,MAAM,CAAC;EAEhD;EACA;EACApB,KAAK,CACF6C,MAAM,CAAC,UAAU/B,CAAC;IACjB,OAAOA,CAAC,CAACyB,IAAI,CAACnB,MAAM,IAAI,CAAC;EAC3B,CAAC,CAAC,CACD0B,GAAG,CAAC,UAAU5B,OAAO;IACpB,MAAM6B,IAAI,GAAGrC,MAAM,CAACQ,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,CAAC;MAClCS,KAAK,GAAGtC,MAAM,CAACQ,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC,CAAC;MAC/Bd,EAAE,GAAGG,IAAI,CAACqB,IAAI,CAACV,IAAI,CAACQ,IAAI,CAAC,CAACN,IAAI,GAAGb,IAAI,CAACE,EAAE,CAAC;MACzCJ,EAAE,GAAGE,IAAI,CAACqB,IAAI,CAACV,IAAI,CAACS,KAAK,CAAC,CAACP,IAAI,GAAGb,IAAI,CAACE,EAAE,CAAC;MAC1CjC,QAAQ,GAAG2B,yBAAyB,CAACC,EAAE,EAAEC,EAAE,EAAER,OAAO,CAACuB,IAAI,CAAC;IAE5DE,SAAS,CAACI,IAAI,CAAC,CAACC,KAAK,CAAC,GAAGL,SAAS,CAACK,KAAK,CAAC,CAACD,IAAI,CAAC,GAAGlD,QAAQ;IAE1D;IACA;IACA,IAAIqD,CAAC,GAAG,CAAC;IACT,IAAIhC,OAAO,CAACuB,IAAI,GAAG,KAAK,IAAIb,IAAI,CAACC,GAAG,CAACU,IAAI,CAACQ,IAAI,CAAC,CAACN,IAAI,EAAEF,IAAI,CAACS,KAAK,CAAC,CAACP,IAAI,CAAC,EAAE;MACvES,CAAC,GAAG,CAAC;KACN,MAAM,IAAIhC,OAAO,CAACuB,IAAI,IAAI,KAAK,EAAE;MAChCS,CAAC,GAAG,CAAC,CAAC;;IAERN,WAAW,CAACG,IAAI,CAAC,CAACC,KAAK,CAAC,GAAGJ,WAAW,CAACI,KAAK,CAAC,CAACD,IAAI,CAAC,GAAGG,CAAC;EACzD,CAAC,CAAC;EAEJ,OAAO;IAAEP,SAAS,EAAEA,SAAS;IAAEC,WAAW,EAAEA;EAAW,CAAE;AAC3D;AAEA;AACA,SAASO,sBAAsBA,CAACrC,CAAC,EAAEsC,OAAO,EAAET,SAAS,EAAEC,WAAW;EAChE,IAAIvC,IAAI,GAAG,CAAC;IACVc,CAAC;EACH,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiC,OAAO,CAAChC,MAAM,EAAE,EAAED,CAAC,EAAE;IACnCiC,OAAO,CAACjC,CAAC,CAAC,GAAG,CAAC;;EAGhB,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwB,SAAS,CAACvB,MAAM,EAAE,EAAED,CAAC,EAAE;IACrC,MAAMkC,EAAE,GAAGvC,CAAC,CAAC,CAAC,GAAGK,CAAC,CAAC;MACjBmC,EAAE,GAAGxC,CAAC,CAAC,CAAC,GAAGK,CAAC,GAAG,CAAC,CAAC;IACnB,KAAK,IAAIgB,CAAC,GAAGhB,CAAC,GAAG,CAAC,EAAEgB,CAAC,GAAGQ,SAAS,CAACvB,MAAM,EAAE,EAAEe,CAAC,EAAE;MAC7C,MAAMoB,EAAE,GAAGzC,CAAC,CAAC,CAAC,GAAGqB,CAAC,CAAC;QACjBqB,EAAE,GAAG1C,CAAC,CAAC,CAAC,GAAGqB,CAAC,GAAG,CAAC,CAAC;QACjBsB,GAAG,GAAGd,SAAS,CAACxB,CAAC,CAAC,CAACgB,CAAC,CAAC;QACrBuB,UAAU,GAAGd,WAAW,CAACzB,CAAC,CAAC,CAACgB,CAAC,CAAC;MAEhC,MAAMwB,eAAe,GAAG,CAACJ,EAAE,GAAGF,EAAE,KAAKE,EAAE,GAAGF,EAAE,CAAC,GAAG,CAACG,EAAE,GAAGF,EAAE,KAAKE,EAAE,GAAGF,EAAE,CAAC;QACnEzD,QAAQ,GAAG+B,IAAI,CAACqB,IAAI,CAACU,eAAe,CAAC;QACrCC,KAAK,GAAGD,eAAe,GAAGF,GAAG,GAAGA,GAAG;MAErC,IACGC,UAAU,GAAG,CAAC,IAAI7D,QAAQ,IAAI4D,GAAG,IACjCC,UAAU,GAAG,CAAC,IAAI7D,QAAQ,IAAI4D,GAAI,EACnC;QACA;;MAGFpD,IAAI,IAAI,CAAC,GAAGuD,KAAK,GAAGA,KAAK;MAEzBR,OAAO,CAAC,CAAC,GAAGjC,CAAC,CAAC,IAAI,CAAC,GAAGyC,KAAK,IAAIP,EAAE,GAAGE,EAAE,CAAC;MACvCH,OAAO,CAAC,CAAC,GAAGjC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGyC,KAAK,IAAIN,EAAE,GAAGE,EAAE,CAAC;MAE3CJ,OAAO,CAAC,CAAC,GAAGjB,CAAC,CAAC,IAAI,CAAC,GAAGyB,KAAK,IAAIL,EAAE,GAAGF,EAAE,CAAC;MACvCD,OAAO,CAAC,CAAC,GAAGjB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGyB,KAAK,IAAIJ,EAAE,GAAGF,EAAE,CAAC;;;EAG/C,OAAOjD,IAAI;AACb;AAEA;AACA,OAAM,SAAUD,iBAAiBA,CAACJ,KAAK,EAAE6D,MAAM;EAC7C,IAAIpD,OAAO,GAAGqD,YAAY,CAAC9D,KAAK,EAAE6D,MAAM,CAAC;EACzC,MAAMxD,IAAI,GAAGwD,MAAM,CAACvD,YAAY,IAAIA,YAAY;EAEhD;EACA;EACA;EACA;EACA,IAAIN,KAAK,CAACoB,MAAM,IAAI,CAAC,EAAE;IACrB,MAAM2C,WAAW,GAAGC,oBAAoB,CAAChE,KAAK,EAAE6D,MAAM,CAAC;MACrDI,eAAe,GAAG5D,IAAI,CAAC0D,WAAW,EAAE/D,KAAK,CAAC;MAC1CkE,UAAU,GAAG7D,IAAI,CAACI,OAAO,EAAET,KAAK,CAAC;IAEnC,IAAIiE,eAAe,GAAG,IAAI,GAAGC,UAAU,EAAE;MACvCzD,OAAO,GAAGsD,WAAW;;;EAGzB,OAAOtD,OAAO;AAChB;AAEA;AACA,OAAM,SAAUuD,oBAAoBA,CAAChE,KAAK,EAAE6D,MAAM;EAChDA,MAAM,GAAGA,MAAM,IAAI,EAAE;EACrB,MAAMM,QAAQ,GAAGN,MAAM,CAACM,QAAQ,IAAI,EAAE;EAEtC;EACA,MAAM5B,IAAI,GAAG,EAAE;IACb7B,MAAM,GAAG,EAAE;EACb,IAAIS,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,KAAK,CAACoB,MAAM,EAAE,EAAED,CAAC,EAAE;IACjC,MAAMmB,IAAI,GAAGtC,KAAK,CAACmB,CAAC,CAAC;IACrB,IAAImB,IAAI,CAACC,IAAI,CAACnB,MAAM,IAAI,CAAC,EAAE;MACzBV,MAAM,CAAC4B,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAGA,IAAI,CAACnB,MAAM;MAClCmB,IAAI,CAAC1B,IAAI,CAACyB,IAAI,CAAC;;;EAInB,MAAM8B,QAAQ,GAAG1B,mBAAmB,CAAC1C,KAAK,EAAEuC,IAAI,EAAE7B,MAAM,CAAC;EACzD,IAAIiC,SAAS,GAAGyB,QAAQ,CAACzB,SAAS;EAClC,MAAMC,WAAW,GAAGwB,QAAQ,CAACxB,WAAW;EAExC;EACA;EACA,MAAMyB,IAAI,GAAG9E,KAAK,CAACoD,SAAS,CAACG,GAAG,CAACvD,KAAK,CAAC,CAAC,GAAGoD,SAAS,CAACvB,MAAM;EAC3DuB,SAAS,GAAGA,SAAS,CAACG,GAAG,CAAC,UAAUwB,GAAG;IACrC,OAAOA,GAAG,CAACxB,GAAG,CAAC,UAAUyB,KAAK;MAC5B,OAAOA,KAAK,GAAGF,IAAI;IACrB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAMG,GAAG,GAAG,SAAAA,CAAU1D,CAAC,EAAEsC,OAAO;IAC9B,OAAOD,sBAAsB,CAACrC,CAAC,EAAEsC,OAAO,EAAET,SAAS,EAAEC,WAAW,CAAC;EACnE,CAAC;EAED,IAAI6B,IAAI,EAAEvD,OAAO;EACjB,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgD,QAAQ,EAAE,EAAEhD,CAAC,EAAE;IAC7B,MAAMV,OAAO,GAAGhB,KAAK,CAACkD,SAAS,CAACvB,MAAM,GAAG,CAAC,CAAC,CAAC0B,GAAG,CAAClB,IAAI,CAAC8C,MAAM,CAAC;IAE5DxD,OAAO,GAAG7B,iBAAiB,CAACmF,GAAG,EAAE/D,OAAO,EAAEoD,MAAM,CAAC;IACjD,IAAI,CAACY,IAAI,IAAIvD,OAAO,CAACyD,EAAE,GAAGF,IAAI,CAACE,EAAE,EAAE;MACjCF,IAAI,GAAGvD,OAAO;;;EAGlB,MAAMI,SAAS,GAAGmD,IAAI,CAAC3D,CAAC;EAExB;EACA,MAAMN,OAAO,GAAG,EAAE;EAClB,KAAKW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,IAAI,CAACnB,MAAM,EAAE,EAAED,CAAC,EAAE;IAChC,MAAMyD,GAAG,GAAGrC,IAAI,CAACpB,CAAC,CAAC;IACnBX,OAAO,CAACoE,GAAG,CAACrC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG;MACrBzB,CAAC,EAAEQ,SAAS,CAAC,CAAC,GAAGH,CAAC,CAAC,GAAGkD,IAAI;MAC1BtD,CAAC,EAAEO,SAAS,CAAC,CAAC,GAAGH,CAAC,GAAG,CAAC,CAAC,GAAGkD,IAAI;MAC9BhD,MAAM,EAAEO,IAAI,CAACqB,IAAI,CAAC2B,GAAG,CAACnC,IAAI,GAAGb,IAAI,CAACE,EAAE;KACrC;;EAGH,IAAI+B,MAAM,CAACgB,OAAO,EAAE;IAClB,KAAK1D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0C,MAAM,CAACgB,OAAO,CAACzD,MAAM,EAAE,EAAED,CAAC,EAAE;MAC1C3B,KAAK,CAACqE,MAAM,CAACgB,OAAO,CAAC1D,CAAC,CAAC,CAACL,CAAC,EAAEuD,IAAI,CAAC;;;EAGpC,OAAO7D,OAAO;AAChB;AAEA;;;AAGA,OAAM,SAAUsD,YAAYA,CAAC9D,KAAK,EAAE6D,MAAM;EACxC,MAAMxD,IAAI,GACRwD,MAAM,IAAIA,MAAM,CAACvD,YAAY,GAAGuD,MAAM,CAACvD,YAAY,GAAGA,YAAY;EACpE;EACA,MAAME,OAAO,GAAG,EAAE;IAChBsE,WAAW,GAAG,EAAE;EAClB,IAAIF,GAAG;EACP,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,KAAK,CAACoB,MAAM,EAAE,EAAED,CAAC,EAAE;IACrC,MAAMmB,IAAI,GAAGtC,KAAK,CAACmB,CAAC,CAAC;IACrB,IAAImB,IAAI,CAACC,IAAI,CAACnB,MAAM,IAAI,CAAC,EAAE;MACzBwD,GAAG,GAAGtC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAClB/B,OAAO,CAACoE,GAAG,CAAC,GAAG;QACb9D,CAAC,EAAE,IAAI;QACPC,CAAC,EAAE,IAAI;QACP;QACAgE,KAAK,EAAEC,MAAM,CAACC,IAAI,CAACzE,OAAO,CAAC,CAACY,MAAM;QAClCqB,IAAI,EAAEH,IAAI,CAACG,IAAI;QACfpB,MAAM,EAAEO,IAAI,CAACqB,IAAI,CAACX,IAAI,CAACG,IAAI,GAAGb,IAAI,CAACE,EAAE;OACtC;MACDgD,WAAW,CAACF,GAAG,CAAC,GAAG,EAAE;;;EAGzB5E,KAAK,GAAGA,KAAK,CAAC6C,MAAM,CAAC,UAAUT,CAAC;IAC9B,OAAOA,CAAC,CAACG,IAAI,CAACnB,MAAM,IAAI,CAAC;EAC3B,CAAC,CAAC;EAEF;EACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,KAAK,CAACoB,MAAM,EAAE,EAAED,CAAC,EAAE;IACrC,MAAMD,OAAO,GAAGlB,KAAK,CAACmB,CAAC,CAAC;IACxB;IACA,IAAI+D,MAAM,GAAGhE,OAAO,CAACN,cAAc,CAAC,QAAQ,CAAC,GAAGM,OAAO,CAACgE,MAAM,GAAG,GAAG;IACpE,MAAMnC,IAAI,GAAG7B,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC;MAC1BS,KAAK,GAAG9B,OAAO,CAACqB,IAAI,CAAC,CAAC,CAAC;IAEzB;IACA,IACErB,OAAO,CAACuB,IAAI,GAAGlB,KAAK,IACpBK,IAAI,CAACC,GAAG,CAACrB,OAAO,CAACuC,IAAI,CAAC,CAACN,IAAI,EAAEjC,OAAO,CAACwC,KAAK,CAAC,CAACP,IAAI,CAAC,EACjD;MACAyC,MAAM,GAAG,CAAC;;IAGZJ,WAAW,CAAC/B,IAAI,CAAC,CAAClC,IAAI,CAAC;MAAE+D,GAAG,EAAE5B,KAAK;MAAEP,IAAI,EAAEvB,OAAO,CAACuB,IAAI;MAAEyC,MAAM,EAAEA;IAAM,CAAE,CAAC;IAC1EJ,WAAW,CAAC9B,KAAK,CAAC,CAACnC,IAAI,CAAC;MAAE+D,GAAG,EAAE7B,IAAI;MAAEN,IAAI,EAAEvB,OAAO,CAACuB,IAAI;MAAEyC,MAAM,EAAEA;IAAM,CAAE,CAAC;;EAG5E;EACA,MAAMC,cAAc,GAAG,EAAE;EACzB,KAAKP,GAAG,IAAIE,WAAW,EAAE;IACvB;IACA,IAAIA,WAAW,CAAClE,cAAc,CAACgE,GAAG,CAAC,EAAE;MACnC,IAAInC,IAAI,GAAG,CAAC;MACZ,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2D,WAAW,CAACF,GAAG,CAAC,CAACxD,MAAM,EAAE,EAAED,CAAC,EAAE;QAChDsB,IAAI,IAAIqC,WAAW,CAACF,GAAG,CAAC,CAACzD,CAAC,CAAC,CAACsB,IAAI,GAAGqC,WAAW,CAACF,GAAG,CAAC,CAACzD,CAAC,CAAC,CAAC+D,MAAM;;MAG/DC,cAAc,CAACtE,IAAI,CAAC;QAAE+D,GAAG,EAAEA,GAAG;QAAEnC,IAAI,EAAEA;MAAI,CAAE,CAAC;;;EAIjD;EACA,SAAS2C,SAASA,CAAChD,CAAC,EAAEC,CAAC;IACrB,OAAOA,CAAC,CAACI,IAAI,GAAGL,CAAC,CAACK,IAAI;EACxB;EACA0C,cAAc,CAAC3C,IAAI,CAAC4C,SAAS,CAAC;EAE9B;EACA,MAAMC,UAAU,GAAG,EAAE;EACrB,SAASC,YAAYA,CAACC,OAAO;IAC3B,OAAOA,OAAO,CAACX,GAAG,IAAIS,UAAU;EAClC;EAEA;EACA,SAASG,WAAWA,CAACC,KAAK,EAAEC,KAAK;IAC/BlF,OAAO,CAACkF,KAAK,CAAC,CAAC5E,CAAC,GAAG2E,KAAK,CAAC3E,CAAC;IAC1BN,OAAO,CAACkF,KAAK,CAAC,CAAC3E,CAAC,GAAG0E,KAAK,CAAC1E,CAAC;IAC1BsE,UAAU,CAACK,KAAK,CAAC,GAAG,IAAI;EAC1B;EAEA;EACAF,WAAW,CAAC;IAAE1E,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAC,CAAE,EAAEoE,cAAc,CAAC,CAAC,CAAC,CAACP,GAAG,CAAC;EAElD;EACA;EACA;EACA,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,cAAc,CAAC/D,MAAM,EAAE,EAAED,CAAC,EAAE;IAC9C,MAAMwE,QAAQ,GAAGR,cAAc,CAAChE,CAAC,CAAC,CAACyD,GAAG;MACpCjD,OAAO,GAAGmD,WAAW,CAACa,QAAQ,CAAC,CAAC9C,MAAM,CAACyC,YAAY,CAAC;IACtDV,GAAG,GAAGpE,OAAO,CAACmF,QAAQ,CAAC;IACvBhE,OAAO,CAACa,IAAI,CAAC4C,SAAS,CAAC;IAEvB,IAAIzD,OAAO,CAACP,MAAM,KAAK,CAAC,EAAE;MACxB;MACA,MAAM,6CAA6C;;IAGrD,MAAMwE,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,OAAO,CAACP,MAAM,EAAE,EAAEe,CAAC,EAAE;MACvC;MACA,MAAM0D,EAAE,GAAGrF,OAAO,CAACmB,OAAO,CAACQ,CAAC,CAAC,CAACyC,GAAG,CAAC;QAChCkB,EAAE,GAAGtE,yBAAyB,CAACoD,GAAG,CAACvD,MAAM,EAAEwE,EAAE,CAACxE,MAAM,EAAEM,OAAO,CAACQ,CAAC,CAAC,CAACM,IAAI,CAAC;MAExE;MACAmD,MAAM,CAAC/E,IAAI,CAAC;QAAEC,CAAC,EAAE+E,EAAE,CAAC/E,CAAC,GAAGgF,EAAE;QAAE/E,CAAC,EAAE8E,EAAE,CAAC9E;MAAC,CAAE,CAAC;MACtC6E,MAAM,CAAC/E,IAAI,CAAC;QAAEC,CAAC,EAAE+E,EAAE,CAAC/E,CAAC,GAAGgF,EAAE;QAAE/E,CAAC,EAAE8E,EAAE,CAAC9E;MAAC,CAAE,CAAC;MACtC6E,MAAM,CAAC/E,IAAI,CAAC;QAAEE,CAAC,EAAE8E,EAAE,CAAC9E,CAAC,GAAG+E,EAAE;QAAEhF,CAAC,EAAE+E,EAAE,CAAC/E;MAAC,CAAE,CAAC;MACtC8E,MAAM,CAAC/E,IAAI,CAAC;QAAEE,CAAC,EAAE8E,EAAE,CAAC9E,CAAC,GAAG+E,EAAE;QAAEhF,CAAC,EAAE+E,EAAE,CAAC/E;MAAC,CAAE,CAAC;MAEtC;MACA;MACA,KAAK,IAAIiF,CAAC,GAAG5D,CAAC,GAAG,CAAC,EAAE4D,CAAC,GAAGpE,OAAO,CAACP,MAAM,EAAE,EAAE2E,CAAC,EAAE;QAC3C,MAAMC,EAAE,GAAGxF,OAAO,CAACmB,OAAO,CAACoE,CAAC,CAAC,CAACnB,GAAG,CAAC;UAChCqB,EAAE,GAAGzE,yBAAyB,CAC5BoD,GAAG,CAACvD,MAAM,EACV2E,EAAE,CAAC3E,MAAM,EACTM,OAAO,CAACoE,CAAC,CAAC,CAACtD,IAAI,CAChB;QAEH,MAAMyD,WAAW,GAAGvG,wBAAwB,CAC1C;UAAEmB,CAAC,EAAE+E,EAAE,CAAC/E,CAAC;UAAEC,CAAC,EAAE8E,EAAE,CAAC9E,CAAC;UAAEM,MAAM,EAAEyE;QAAE,CAAE,EAChC;UAAEhF,CAAC,EAAEkF,EAAE,CAAClF,CAAC;UAAEC,CAAC,EAAEiF,EAAE,CAACjF,CAAC;UAAEM,MAAM,EAAE4E;QAAE,CAAE,CACjC;QAED,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,WAAW,CAAC9E,MAAM,EAAE,EAAE+E,CAAC,EAAE;UAC3CP,MAAM,CAAC/E,IAAI,CAACqF,WAAW,CAACC,CAAC,CAAC,CAAC;;;;IAKjC;IACA;IACA,IAAIC,QAAQ,GAAG,IAAI;MACjBC,SAAS,GAAGT,MAAM,CAAC,CAAC,CAAC;IACvB,KAAK,IAAIzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyD,MAAM,CAACxE,MAAM,EAAE,EAAEe,CAAC,EAAE;MACtC3B,OAAO,CAACmF,QAAQ,CAAC,CAAC7E,CAAC,GAAG8E,MAAM,CAACzD,CAAC,CAAC,CAACrB,CAAC;MACjCN,OAAO,CAACmF,QAAQ,CAAC,CAAC5E,CAAC,GAAG6E,MAAM,CAACzD,CAAC,CAAC,CAACpB,CAAC;MACjC,MAAMuF,SAAS,GAAGjG,IAAI,CAACG,OAAO,EAAER,KAAK,CAAC;MACtC,IAAIsG,SAAS,GAAGF,QAAQ,EAAE;QACxBA,QAAQ,GAAGE,SAAS;QACpBD,SAAS,GAAGT,MAAM,CAACzD,CAAC,CAAC;;;IAIzBqD,WAAW,CAACa,SAAS,EAAEV,QAAQ,CAAC;;EAGlC,OAAOnF,OAAO;AAChB;AAEA;;;AAGA,OAAM,SAAUF,YAAYA,CAACiC,IAAI,EAAEgE,QAAQ;EACzC,IAAIC,MAAM,GAAG,CAAC;EAEd,SAASC,UAAUA,CAACC,OAAO;IACzB,OAAOA,OAAO,CAAC5D,GAAG,CAAC,UAAU3B,CAAC;MAC5B,OAAOoB,IAAI,CAACpB,CAAC,CAAC;IAChB,CAAC,CAAC;EACJ;EAEA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,QAAQ,CAACnF,MAAM,EAAE,EAAED,CAAC,EAAE;IACxC,MAAMmB,IAAI,GAAGiE,QAAQ,CAACpF,CAAC,CAAC;IACxB,IAAIQ,OAAO;IACX,IAAIW,IAAI,CAACC,IAAI,CAACnB,MAAM,IAAI,CAAC,EAAE;MACzB;KACD,MAAM,IAAIkB,IAAI,CAACC,IAAI,CAACnB,MAAM,IAAI,CAAC,EAAE;MAChC,MAAM2B,IAAI,GAAGR,IAAI,CAACD,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7BS,KAAK,GAAGT,IAAI,CAACD,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC;MAC5BZ,OAAO,GAAG/B,aAAa,CAACmD,IAAI,CAAC1B,MAAM,EAAE2B,KAAK,CAAC3B,MAAM,EAAExB,QAAQ,CAACkD,IAAI,EAAEC,KAAK,CAAC,CAAC;KAC1E,MAAM;MACLrB,OAAO,GAAG7B,gBAAgB,CAAC2G,UAAU,CAACnE,IAAI,CAACC,IAAI,CAAC,CAAC;;IAGnD;IACA,MAAM2C,MAAM,GAAG5C,IAAI,CAAC1B,cAAc,CAAC,QAAQ,CAAC,GAAG0B,IAAI,CAAC4C,MAAM,GAAG,GAAG;IAChEsB,MAAM,IAAItB,MAAM,IAAIvD,OAAO,GAAGW,IAAI,CAACG,IAAI,CAAC,IAAId,OAAO,GAAGW,IAAI,CAACG,IAAI,CAAC;;EAGlE,OAAO+D,MAAM;AACf;AAEA;AACA,SAASG,gBAAgBA,CAACnG,OAAO,EAAEoG,WAAW,EAAEC,gBAAgB;EAC9D,IAAIA,gBAAgB,KAAK,IAAI,EAAE;IAC7BrG,OAAO,CAACgC,IAAI,CAAC,UAAUJ,CAAC,EAAEC,CAAC;MACzB,OAAOA,CAAC,CAAChB,MAAM,GAAGe,CAAC,CAACf,MAAM;IAC5B,CAAC,CAAC;GACH,MAAM;IACLb,OAAO,CAACgC,IAAI,CAACqE,gBAAgB,CAAC;;EAGhC,IAAI1F,CAAC;EACL;EACA,IAAIX,OAAO,CAACY,MAAM,GAAG,CAAC,EAAE;IACtB,MAAM0F,QAAQ,GAAGtG,OAAO,CAAC,CAAC,CAAC,CAACM,CAAC;MAC3BiG,QAAQ,GAAGvG,OAAO,CAAC,CAAC,CAAC,CAACO,CAAC;IAEzB,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;MACnCX,OAAO,CAACW,CAAC,CAAC,CAACL,CAAC,IAAIgG,QAAQ;MACxBtG,OAAO,CAACW,CAAC,CAAC,CAACJ,CAAC,IAAIgG,QAAQ;;;EAI5B,IAAIvG,OAAO,CAACY,MAAM,IAAI,CAAC,EAAE;IACvB;IACA;IACA,MAAM4F,IAAI,GAAGnH,QAAQ,CAACW,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,CAAC;IAC7C,IAAIwG,IAAI,GAAGpF,IAAI,CAACG,GAAG,CAACvB,OAAO,CAAC,CAAC,CAAC,CAACa,MAAM,GAAGb,OAAO,CAAC,CAAC,CAAC,CAACa,MAAM,CAAC,EAAE;MAC1Db,OAAO,CAAC,CAAC,CAAC,CAACM,CAAC,GACVN,OAAO,CAAC,CAAC,CAAC,CAACM,CAAC,GAAGN,OAAO,CAAC,CAAC,CAAC,CAACa,MAAM,GAAGb,OAAO,CAAC,CAAC,CAAC,CAACa,MAAM,GAAG,KAAK;MAC9Db,OAAO,CAAC,CAAC,CAAC,CAACO,CAAC,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACO,CAAC;;;EAI/B;EACA;EACA,IAAIP,OAAO,CAACY,MAAM,GAAG,CAAC,EAAE;IACtB,MAAM6F,QAAQ,GAAGrF,IAAI,CAACsF,KAAK,CAAC1G,OAAO,CAAC,CAAC,CAAC,CAACM,CAAC,EAAEN,OAAO,CAAC,CAAC,CAAC,CAACO,CAAC,CAAC,GAAG6F,WAAW;IACrE,IAAI9F,CAAC,EAAEC,CAAC;IACR,MAAMmC,CAAC,GAAGtB,IAAI,CAACuF,GAAG,CAACF,QAAQ,CAAC;MAC1BG,CAAC,GAAGxF,IAAI,CAACyF,GAAG,CAACJ,QAAQ,CAAC;IACxB,KAAK9F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;MACnCL,CAAC,GAAGN,OAAO,CAACW,CAAC,CAAC,CAACL,CAAC;MAChBC,CAAC,GAAGP,OAAO,CAACW,CAAC,CAAC,CAACJ,CAAC;MAChBP,OAAO,CAACW,CAAC,CAAC,CAACL,CAAC,GAAGoC,CAAC,GAAGpC,CAAC,GAAGsG,CAAC,GAAGrG,CAAC;MAC5BP,OAAO,CAACW,CAAC,CAAC,CAACJ,CAAC,GAAGqG,CAAC,GAAGtG,CAAC,GAAGoC,CAAC,GAAGnC,CAAC;;;EAIhC;EACA;EACA,IAAIP,OAAO,CAACY,MAAM,GAAG,CAAC,EAAE;IACtB,IAAIkG,KAAK,GAAG1F,IAAI,CAACsF,KAAK,CAAC1G,OAAO,CAAC,CAAC,CAAC,CAACM,CAAC,EAAEN,OAAO,CAAC,CAAC,CAAC,CAACO,CAAC,CAAC,GAAG6F,WAAW;IAChE,OAAOU,KAAK,GAAG,CAAC,EAAE;MAChBA,KAAK,IAAI,CAAC,GAAG1F,IAAI,CAACE,EAAE;;IAEtB,OAAOwF,KAAK,GAAG,CAAC,GAAG1F,IAAI,CAACE,EAAE,EAAE;MAC1BwF,KAAK,IAAI,CAAC,GAAG1F,IAAI,CAACE,EAAE;;IAEtB,IAAIwF,KAAK,GAAG1F,IAAI,CAACE,EAAE,EAAE;MACnB,MAAMyF,KAAK,GAAG/G,OAAO,CAAC,CAAC,CAAC,CAACO,CAAC,IAAI,KAAK,GAAGP,OAAO,CAAC,CAAC,CAAC,CAACM,CAAC,CAAC;MACnD,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;QACnC,MAAMqG,CAAC,GAAG,CAAChH,OAAO,CAACW,CAAC,CAAC,CAACL,CAAC,GAAGyG,KAAK,GAAG/G,OAAO,CAACW,CAAC,CAAC,CAACJ,CAAC,KAAK,CAAC,GAAGwG,KAAK,GAAGA,KAAK,CAAC;QACrE/G,OAAO,CAACW,CAAC,CAAC,CAACL,CAAC,GAAG,CAAC,GAAG0G,CAAC,GAAGhH,OAAO,CAACW,CAAC,CAAC,CAACL,CAAC;QACnCN,OAAO,CAACW,CAAC,CAAC,CAACJ,CAAC,GAAG,CAAC,GAAGyG,CAAC,GAAGD,KAAK,GAAG/G,OAAO,CAACW,CAAC,CAAC,CAACJ,CAAC;;;;AAInD;AAEA,OAAM,SAAU0G,eAAeA,CAACjH,OAAO;EACrC;EACAA,OAAO,CAACsC,GAAG,CAAC,UAAU4E,MAAM;IAC1BA,MAAM,CAACC,MAAM,GAAGD,MAAM;EACxB,CAAC,CAAC;EAEF;EACA,SAASE,IAAIA,CAACF,MAAM;IAClB,IAAIA,MAAM,CAACC,MAAM,KAAKD,MAAM,EAAE;MAC5BA,MAAM,CAACC,MAAM,GAAGC,IAAI,CAACF,MAAM,CAACC,MAAM,CAAC;;IAErC,OAAOD,MAAM,CAACC,MAAM;EACtB;EAEA,SAASE,KAAKA,CAAC/G,CAAC,EAAEC,CAAC;IACjB,MAAM+G,KAAK,GAAGF,IAAI,CAAC9G,CAAC,CAAC;MACnBiH,KAAK,GAAGH,IAAI,CAAC7G,CAAC,CAAC;IACjB+G,KAAK,CAACH,MAAM,GAAGI,KAAK;EACtB;EAEA;EACA,KAAK,IAAI5G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;IACvC,KAAK,IAAIgB,CAAC,GAAGhB,CAAC,GAAG,CAAC,EAAEgB,CAAC,GAAG3B,OAAO,CAACY,MAAM,EAAE,EAAEe,CAAC,EAAE;MAC3C,MAAM6F,WAAW,GAAGxH,OAAO,CAACW,CAAC,CAAC,CAACE,MAAM,GAAGb,OAAO,CAAC2B,CAAC,CAAC,CAACd,MAAM;MACzD,IAAIxB,QAAQ,CAACW,OAAO,CAACW,CAAC,CAAC,EAAEX,OAAO,CAAC2B,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG6F,WAAW,EAAE;QAC1DH,KAAK,CAACrH,OAAO,CAAC2B,CAAC,CAAC,EAAE3B,OAAO,CAACW,CAAC,CAAC,CAAC;;;;EAKnC;EACA,MAAM8G,gBAAgB,GAAG,EAAE;EAC3B,IAAItH,KAAK;EACT,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;IACvCR,KAAK,GAAGiH,IAAI,CAACpH,OAAO,CAACW,CAAC,CAAC,CAAC,CAACwG,MAAM,CAAChH,KAAK;IACrC,IAAI,EAAEA,KAAK,IAAIsH,gBAAgB,CAAC,EAAE;MAChCA,gBAAgB,CAACtH,KAAK,CAAC,GAAG,EAAE;;IAE9BsH,gBAAgB,CAACtH,KAAK,CAAC,CAACE,IAAI,CAACL,OAAO,CAACW,CAAC,CAAC,CAAC;;EAG1C;EACAX,OAAO,CAACsC,GAAG,CAAC,UAAU4E,MAAM;IAC1B,OAAOA,MAAM,CAACC,MAAM;EACtB,CAAC,CAAC;EAEF;EACA,MAAMO,GAAG,GAAG,EAAE;EACd,KAAKvH,KAAK,IAAIsH,gBAAgB,EAAE;IAC9B;IACA,IAAIA,gBAAgB,CAACrH,cAAc,CAACD,KAAK,CAAC,EAAE;MAC1CuH,GAAG,CAACrH,IAAI,CAACoH,gBAAgB,CAACtH,KAAK,CAAC,CAAC;;;EAGrC,OAAOuH,GAAG;AACZ;AAEA,SAASC,cAAcA,CAAC3H,OAAO;EAC7B,MAAM4H,MAAM,GAAG,SAAAA,CAAUZ,CAAC;IACxB,MAAMa,EAAE,GAAGzG,IAAI,CAAC0G,GAAG,CAACC,KAAK,CACrB,IAAI,EACJ/H,OAAO,CAACsC,GAAG,CAAC,UAAUI,CAAC;QACrB,OAAOA,CAAC,CAACsE,CAAC,CAAC,GAAGtE,CAAC,CAAC7B,MAAM;MACxB,CAAC,CAAC,CACH;MACDmH,EAAE,GAAG5G,IAAI,CAACC,GAAG,CAAC0G,KAAK,CACjB,IAAI,EACJ/H,OAAO,CAACsC,GAAG,CAAC,UAAUI,CAAC;QACrB,OAAOA,CAAC,CAACsE,CAAC,CAAC,GAAGtE,CAAC,CAAC7B,MAAM;MACxB,CAAC,CAAC,CACH;IACH,OAAO;MAAEiH,GAAG,EAAED,EAAE;MAAExG,GAAG,EAAE2G;IAAE,CAAE;EAC7B,CAAC;EAED,OAAO;IAAEC,MAAM,EAAEL,MAAM,CAAC,GAAG,CAAC;IAAEM,MAAM,EAAEN,MAAM,CAAC,GAAG;EAAC,CAAE;AACrD;AAEA,OAAM,SAAUO,iBAAiBA,CAAC3H,QAAQ,EAAE4F,WAAW,EAAEC,gBAAgB;EACvE,IAAID,WAAW,KAAK,IAAI,EAAE;IACxBA,WAAW,GAAGhF,IAAI,CAACE,EAAE,GAAG,CAAC;;EAG3B;EACA;EACA,IAAItB,OAAO,GAAG,EAAE;IACdW,CAAC;IACDR,KAAK;EACP,KAAKA,KAAK,IAAIK,QAAQ,EAAE;IACtB;IACA,IAAIA,QAAQ,CAACJ,cAAc,CAACD,KAAK,CAAC,EAAE;MAClC,MAAMiI,QAAQ,GAAG5H,QAAQ,CAACL,KAAK,CAAC;MAChCH,OAAO,CAACK,IAAI,CAAC;QACXC,CAAC,EAAE8H,QAAQ,CAAC9H,CAAC;QACbC,CAAC,EAAE6H,QAAQ,CAAC7H,CAAC;QACbM,MAAM,EAAEuH,QAAQ,CAACvH,MAAM;QACvBV,KAAK,EAAEA;OACR,CAAC;;;EAIN;EACA,MAAMkI,QAAQ,GAAGpB,eAAe,CAACjH,OAAO,CAAC;EAEzC;EACA,KAAKW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0H,QAAQ,CAACzH,MAAM,EAAE,EAAED,CAAC,EAAE;IACpCwF,gBAAgB,CAACkC,QAAQ,CAAC1H,CAAC,CAAC,EAAEyF,WAAW,EAAEC,gBAAgB,CAAC;IAC5D,MAAMiC,MAAM,GAAGX,cAAc,CAACU,QAAQ,CAAC1H,CAAC,CAAC,CAAC;IAC1C0H,QAAQ,CAAC1H,CAAC,CAAC,CAACsB,IAAI,GACd,CAACqG,MAAM,CAACL,MAAM,CAACH,GAAG,GAAGQ,MAAM,CAACL,MAAM,CAAC5G,GAAG,KACrCiH,MAAM,CAACJ,MAAM,CAACJ,GAAG,GAAGQ,MAAM,CAACJ,MAAM,CAAC7G,GAAG,CAAC;IACzCgH,QAAQ,CAAC1H,CAAC,CAAC,CAAC2H,MAAM,GAAGA,MAAM;;EAE7BD,QAAQ,CAACrG,IAAI,CAAC,UAAUJ,CAAC,EAAEC,CAAC;IAC1B,OAAOA,CAAC,CAACI,IAAI,GAAGL,CAAC,CAACK,IAAI;EACxB,CAAC,CAAC;EAEF;EACAjC,OAAO,GAAGqI,QAAQ,CAAC,CAAC,CAAC;EACrB;EACA,IAAIE,YAAY,GAAGvI,OAAO,CAACsI,MAAM;EAEjC,MAAME,OAAO,GAAG,CAACD,YAAY,CAACN,MAAM,CAACH,GAAG,GAAGS,YAAY,CAACN,MAAM,CAAC5G,GAAG,IAAI,EAAE;EAExE,SAASoH,UAAUA,CAACC,OAAO,EAAElG,KAAK,EAAEmG,MAAM;IACxC,IAAI,CAACD,OAAO,EAAE;IAEd,MAAMJ,MAAM,GAAGI,OAAO,CAACJ,MAAM;IAC7B,IAAIM,OAAO,EAAEC,OAAO,EAAEC,SAAS;IAE/B,IAAItG,KAAK,EAAE;MACToG,OAAO,GAAGL,YAAY,CAACN,MAAM,CAACH,GAAG,GAAGQ,MAAM,CAACL,MAAM,CAAC5G,GAAG,GAAGmH,OAAO;KAChE,MAAM;MACLI,OAAO,GAAGL,YAAY,CAACN,MAAM,CAACH,GAAG,GAAGQ,MAAM,CAACL,MAAM,CAACH,GAAG;MACrDgB,SAAS,GACP,CAACR,MAAM,CAACL,MAAM,CAACH,GAAG,GAAGQ,MAAM,CAACL,MAAM,CAAC5G,GAAG,IAAI,CAAC,GAC3C,CAACkH,YAAY,CAACN,MAAM,CAACH,GAAG,GAAGS,YAAY,CAACN,MAAM,CAAC5G,GAAG,IAAI,CAAC;MACzD,IAAIyH,SAAS,GAAG,CAAC,EAAEF,OAAO,IAAIE,SAAS;;IAGzC,IAAIH,MAAM,EAAE;MACVE,OAAO,GAAGN,YAAY,CAACL,MAAM,CAACJ,GAAG,GAAGQ,MAAM,CAACJ,MAAM,CAAC7G,GAAG,GAAGmH,OAAO;KAChE,MAAM;MACLK,OAAO,GAAGN,YAAY,CAACL,MAAM,CAACJ,GAAG,GAAGQ,MAAM,CAACJ,MAAM,CAACJ,GAAG;MACrDgB,SAAS,GACP,CAACR,MAAM,CAACJ,MAAM,CAACJ,GAAG,GAAGQ,MAAM,CAACJ,MAAM,CAAC7G,GAAG,IAAI,CAAC,GAC3C,CAACkH,YAAY,CAACL,MAAM,CAACJ,GAAG,GAAGS,YAAY,CAACL,MAAM,CAAC7G,GAAG,IAAI,CAAC;MACzD,IAAIyH,SAAS,GAAG,CAAC,EAAED,OAAO,IAAIC,SAAS;;IAGzC,KAAK,IAAInH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+G,OAAO,CAAC9H,MAAM,EAAE,EAAEe,CAAC,EAAE;MACvC+G,OAAO,CAAC/G,CAAC,CAAC,CAACrB,CAAC,IAAIsI,OAAO;MACvBF,OAAO,CAAC/G,CAAC,CAAC,CAACpB,CAAC,IAAIsI,OAAO;MACvB7I,OAAO,CAACK,IAAI,CAACqI,OAAO,CAAC/G,CAAC,CAAC,CAAC;;EAE5B;EAEA,IAAIuD,KAAK,GAAG,CAAC;EACb,OAAOA,KAAK,GAAGmD,QAAQ,CAACzH,MAAM,EAAE;IAC9B6H,UAAU,CAACJ,QAAQ,CAACnD,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;IACxCuD,UAAU,CAACJ,QAAQ,CAACnD,KAAK,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC;IAC5CuD,UAAU,CAACJ,QAAQ,CAACnD,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3CA,KAAK,IAAI,CAAC;IAEV;IACA;IACAqD,YAAY,GAAGZ,cAAc,CAAC3H,OAAO,CAAC;;EAGxC;EACA,MAAM0H,GAAG,GAAG,EAAE;EACd,KAAK/G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;IACnC+G,GAAG,CAAC1H,OAAO,CAACW,CAAC,CAAC,CAACR,KAAK,CAAC,GAAGH,OAAO,CAACW,CAAC,CAAC;;EAEpC,OAAO+G,GAAG;AACZ;AAEA;;;AAGA,OAAM,SAAUqB,aAAaA,CAACvI,QAAQ,EAAEwI,KAAK,EAAEC,MAAM,EAAEC,OAAO;EAC5D,MAAMlJ,OAAO,GAAG,EAAE;IAChBE,MAAM,GAAG,EAAE;EACb,KAAK,MAAMC,KAAK,IAAIK,QAAQ,EAAE;IAC5B;IACA,IAAIA,QAAQ,CAACJ,cAAc,CAACD,KAAK,CAAC,EAAE;MAClCD,MAAM,CAACG,IAAI,CAACF,KAAK,CAAC;MAClBH,OAAO,CAACK,IAAI,CAACG,QAAQ,CAACL,KAAK,CAAC,CAAC;;;EAIjC6I,KAAK,IAAI,CAAC,GAAGE,OAAO;EACpBD,MAAM,IAAI,CAAC,GAAGC,OAAO;EAErB,MAAMZ,MAAM,GAAGX,cAAc,CAAC3H,OAAO,CAAC;IACpCiI,MAAM,GAAGK,MAAM,CAACL,MAAM;IACtBC,MAAM,GAAGI,MAAM,CAACJ,MAAM;EAExB,IAAID,MAAM,CAACH,GAAG,IAAIG,MAAM,CAAC5G,GAAG,IAAI6G,MAAM,CAACJ,GAAG,IAAII,MAAM,CAAC7G,GAAG,EAAE;IACxD8H,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,OAAO5I,QAAQ;;EAGjB,MAAM6I,QAAQ,GAAGL,KAAK,IAAIf,MAAM,CAACH,GAAG,GAAGG,MAAM,CAAC5G,GAAG,CAAC;IAChDiI,QAAQ,GAAGL,MAAM,IAAIf,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAAC7G,GAAG,CAAC;IAC7CkI,OAAO,GAAGnI,IAAI,CAACC,GAAG,CAACiI,QAAQ,EAAED,QAAQ,CAAC;IACtC;IACAT,OAAO,GAAG,CAACI,KAAK,GAAG,CAACf,MAAM,CAACH,GAAG,GAAGG,MAAM,CAAC5G,GAAG,IAAIkI,OAAO,IAAI,CAAC;IAC3DV,OAAO,GAAG,CAACI,MAAM,GAAG,CAACf,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAAC7G,GAAG,IAAIkI,OAAO,IAAI,CAAC;EAE9D,MAAMC,MAAM,GAAG,EAAE;EACjB,KAAK,IAAI7I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,OAAO,CAACY,MAAM,EAAE,EAAED,CAAC,EAAE;IACvC,MAAMuG,MAAM,GAAGlH,OAAO,CAACW,CAAC,CAAC;IACzB6I,MAAM,CAACtJ,MAAM,CAACS,CAAC,CAAC,CAAC,GAAG;MAClBE,MAAM,EAAE0I,OAAO,GAAGrC,MAAM,CAACrG,MAAM;MAC/BP,CAAC,EAAE4I,OAAO,GAAGN,OAAO,GAAG,CAAC1B,MAAM,CAAC5G,CAAC,GAAG2H,MAAM,CAAC5G,GAAG,IAAIkI,OAAO;MACxDhJ,CAAC,EAAE2I,OAAO,GAAGL,OAAO,GAAG,CAAC3B,MAAM,CAAC3G,CAAC,GAAG2H,MAAM,CAAC7G,GAAG,IAAIkI;KAClD;;EAGH,OAAOC,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}