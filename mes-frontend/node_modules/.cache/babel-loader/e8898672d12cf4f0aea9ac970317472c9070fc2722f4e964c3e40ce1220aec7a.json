{"ast": null, "code": "export { Circle } from '@antv/g';", "map": {"version": 3, "names": ["Circle"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Circle.ts"], "sourcesContent": ["import type { CircleStyleProps as GCircleStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Circle } from '@antv/g';\nexport type CircleStyleProps = OmitConflictStyleProps<GCircleStyleProps>;\n"], "mappings": "AAGA,SAASA,MAAM,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}