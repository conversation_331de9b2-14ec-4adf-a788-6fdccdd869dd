{"ast": null, "code": "export { getDefaultStyle as right } from './default';", "map": {"version": 3, "names": ["getDefaultStyle", "right"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/right.ts"], "sourcesContent": ["export { getDefaultStyle as right } from './default';\n"], "mappings": "AAAA,SAASA,eAAe,IAAIC,KAAK,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}