{"ast": null, "code": "export default function (x, y) {\n  var nodes,\n    strength = 1;\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n  function force() {\n    var i,\n      n = nodes.length,\n      node,\n      sx = 0,\n      sy = 0;\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], sx += node.x, sy += node.y;\n    }\n    for (sx = (sx / n - x) * strength, sy = (sy / n - y) * strength, i = 0; i < n; ++i) {\n      node = nodes[i], node.x -= sx, node.y -= sy;\n    }\n  }\n  force.initialize = function (_) {\n    nodes = _;\n  };\n  force.x = function (_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n  force.y = function (_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["x", "y", "nodes", "strength", "force", "i", "n", "length", "node", "sx", "sy", "initialize", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force/src/center.js"], "sourcesContent": ["export default function(x, y) {\n  var nodes, strength = 1;\n\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n\n  function force() {\n    var i,\n        n = nodes.length,\n        node,\n        sx = 0,\n        sy = 0;\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], sx += node.x, sy += node.y;\n    }\n\n    for (sx = (sx / n - x) * strength, sy = (sy / n - y) * strength, i = 0; i < n; ++i) {\n      node = nodes[i], node.x -= sx, node.y -= sy;\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAIC,KAAK;IAAEC,QAAQ,GAAG,CAAC;EAEvB,IAAIH,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EACpB,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EAEpB,SAASG,KAAKA,CAAA,EAAG;IACf,IAAIC,CAAC;MACDC,CAAC,GAAGJ,KAAK,CAACK,MAAM;MAChBC,IAAI;MACJC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IAEV,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBG,IAAI,GAAGN,KAAK,CAACG,CAAC,CAAC,EAAEI,EAAE,IAAID,IAAI,CAACR,CAAC,EAAEU,EAAE,IAAIF,IAAI,CAACP,CAAC;IAC7C;IAEA,KAAKQ,EAAE,GAAG,CAACA,EAAE,GAAGH,CAAC,GAAGN,CAAC,IAAIG,QAAQ,EAAEO,EAAE,GAAG,CAACA,EAAE,GAAGJ,CAAC,GAAGL,CAAC,IAAIE,QAAQ,EAAEE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAClFG,IAAI,GAAGN,KAAK,CAACG,CAAC,CAAC,EAAEG,IAAI,CAACR,CAAC,IAAIS,EAAE,EAAED,IAAI,CAACP,CAAC,IAAIS,EAAE;IAC7C;EACF;EAEAN,KAAK,CAACO,UAAU,GAAG,UAASC,CAAC,EAAE;IAC7BV,KAAK,GAAGU,CAAC;EACX,CAAC;EAEDR,KAAK,CAACJ,CAAC,GAAG,UAASY,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACN,MAAM,IAAIP,CAAC,GAAG,CAACY,CAAC,EAAER,KAAK,IAAIJ,CAAC;EAC/C,CAAC;EAEDI,KAAK,CAACH,CAAC,GAAG,UAASW,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACN,MAAM,IAAIN,CAAC,GAAG,CAACW,CAAC,EAAER,KAAK,IAAIH,CAAC;EAC/C,CAAC;EAEDG,KAAK,CAACD,QAAQ,GAAG,UAASS,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACN,MAAM,IAAIJ,QAAQ,GAAG,CAACS,CAAC,EAAER,KAAK,IAAID,QAAQ;EAC7D,CAAC;EAED,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}