{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\nmodule.exports = objectToString;", "map": {"version": 3, "names": ["objectProto", "Object", "prototype", "nativeObjectToString", "toString", "objectToString", "value", "call", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_objectToString.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString(value) {\n  return nativeObjectToString.call(value);\n}\n\nmodule.exports = objectToString;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAGH,WAAW,CAACI,QAAQ;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAOH,oBAAoB,CAACI,IAAI,CAACD,KAAK,CAAC;AACzC;AAEAE,MAAM,CAACC,OAAO,GAAGJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}