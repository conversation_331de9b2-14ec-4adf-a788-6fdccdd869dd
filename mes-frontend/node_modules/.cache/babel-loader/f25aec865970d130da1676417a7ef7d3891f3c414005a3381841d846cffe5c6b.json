{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = dfs;\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n  var acc = [];\n  var visited = {};\n  _.each(vs, function (v) {\n    if (!g.hasNode(v)) {\n      throw new Error(\"Graph does not have node: \" + v);\n    }\n    doDfs(g, v, order === \"post\", visited, navigation, acc);\n  });\n  return acc;\n}\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!_.has(visited, v)) {\n    visited[v] = true;\n    if (!postorder) {\n      acc.push(v);\n    }\n    _.each(navigation(v), function (w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) {\n      acc.push(v);\n    }\n  }\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "dfs", "g", "vs", "order", "isArray", "navigation", "isDirected", "successors", "neighbors", "bind", "acc", "visited", "each", "v", "hasNode", "Error", "doDfs", "postorder", "has", "push", "w"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/dfs.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = dfs;\n\n/*\n * A helper that preforms a pre- or post-order traversal on the input graph\n * and returns the nodes in the order they were visited. If the graph is\n * undirected then this algorithm will navigate using neighbors. If the graph\n * is directed then this algorithm will navigate using successors.\n *\n * Order must be one of \"pre\" or \"post\".\n */\nfunction dfs(g, vs, order) {\n  if (!_.isArray(vs)) {\n    vs = [vs];\n  }\n\n  var navigation = (g.isDirected() ? g.successors : g.neighbors).bind(g);\n\n  var acc = [];\n  var visited = {};\n  _.each(vs, function(v) {\n    if (!g.hasNode(v)) {\n      throw new Error(\"Graph does not have node: \" + v);\n    }\n\n    doDfs(g, v, order === \"post\", visited, navigation, acc);\n  });\n  return acc;\n}\n\nfunction doDfs(g, v, postorder, visited, navigation, acc) {\n  if (!_.has(visited, v)) {\n    visited[v] = true;\n\n    if (!postorder) { acc.push(v); }\n    _.each(navigation(v), function(w) {\n      doDfs(g, w, postorder, visited, navigation, acc);\n    });\n    if (postorder) { acc.push(v); }\n  }\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,GAAG;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAGA,CAACC,CAAC,EAAEC,EAAE,EAAEC,KAAK,EAAE;EACzB,IAAI,CAACP,CAAC,CAACQ,OAAO,CAACF,EAAE,CAAC,EAAE;IAClBA,EAAE,GAAG,CAACA,EAAE,CAAC;EACX;EAEA,IAAIG,UAAU,GAAG,CAACJ,CAAC,CAACK,UAAU,CAAC,CAAC,GAAGL,CAAC,CAACM,UAAU,GAAGN,CAAC,CAACO,SAAS,EAAEC,IAAI,CAACR,CAAC,CAAC;EAEtE,IAAIS,GAAG,GAAG,EAAE;EACZ,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChBf,CAAC,CAACgB,IAAI,CAACV,EAAE,EAAE,UAASW,CAAC,EAAE;IACrB,IAAI,CAACZ,CAAC,CAACa,OAAO,CAACD,CAAC,CAAC,EAAE;MACjB,MAAM,IAAIE,KAAK,CAAC,4BAA4B,GAAGF,CAAC,CAAC;IACnD;IAEAG,KAAK,CAACf,CAAC,EAAEY,CAAC,EAAEV,KAAK,KAAK,MAAM,EAAEQ,OAAO,EAAEN,UAAU,EAAEK,GAAG,CAAC;EACzD,CAAC,CAAC;EACF,OAAOA,GAAG;AACZ;AAEA,SAASM,KAAKA,CAACf,CAAC,EAAEY,CAAC,EAAEI,SAAS,EAAEN,OAAO,EAAEN,UAAU,EAAEK,GAAG,EAAE;EACxD,IAAI,CAACd,CAAC,CAACsB,GAAG,CAACP,OAAO,EAAEE,CAAC,CAAC,EAAE;IACtBF,OAAO,CAACE,CAAC,CAAC,GAAG,IAAI;IAEjB,IAAI,CAACI,SAAS,EAAE;MAAEP,GAAG,CAACS,IAAI,CAACN,CAAC,CAAC;IAAE;IAC/BjB,CAAC,CAACgB,IAAI,CAACP,UAAU,CAACQ,CAAC,CAAC,EAAE,UAASO,CAAC,EAAE;MAChCJ,KAAK,CAACf,CAAC,EAAEmB,CAAC,EAAEH,SAAS,EAAEN,OAAO,EAAEN,UAAU,EAAEK,GAAG,CAAC;IAClD,CAAC,CAAC;IACF,IAAIO,SAAS,EAAE;MAAEP,GAAG,CAACS,IAAI,CAACN,CAAC,CAAC;IAAE;EAChC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}