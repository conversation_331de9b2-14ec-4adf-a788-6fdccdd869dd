{"ast": null, "code": "import { isDigit } from './is-digit-start';\n/**\n * Validates every character of the path string,\n * every path command, negative numbers or floating point numbers.\n */\nexport function scanParam(path) {\n  var max = path.max,\n    pathValue = path.pathValue,\n    start = path.index;\n  var index = start;\n  var zeroFirst = false;\n  var hasCeiling = false;\n  var hasDecimal = false;\n  var hasDot = false;\n  var ch;\n  if (index >= max) {\n    // path.err = 'SvgPath: missed param (at pos ' + index + ')';\n    path.err = \"[path-util]: Invalid path value at index \".concat(index, \", \\\"pathValue\\\" is missing param\");\n    return;\n  }\n  ch = pathValue.charCodeAt(index);\n  if (ch === 0x2b /* + */ || ch === 0x2d /* - */) {\n    index += 1;\n    // ch = (index < max) ? pathValue.charCodeAt(index) : 0;\n    ch = pathValue.charCodeAt(index);\n  }\n  // This logic is shamelessly borrowed from Esprima\n  // https://github.com/ariya/esprimas\n  if (!isDigit(ch) && ch !== 0x2e /* . */) {\n    // path.err = 'SvgPath: param should start with 0..9 or `.` (at pos ' + index + ')';\n    path.err = \"[path-util]: Invalid path value at index \".concat(index, \", \\\"\").concat(pathValue[index], \"\\\" is not a number\");\n    return;\n  }\n  if (ch !== 0x2e /* . */) {\n    zeroFirst = ch === 0x30 /* 0 */;\n    index += 1;\n    ch = pathValue.charCodeAt(index);\n    if (zeroFirst && index < max) {\n      // decimal number starts with '0' such as '09' is illegal.\n      if (ch && isDigit(ch)) {\n        // path.err = 'SvgPath: numbers started with `0` such as `09`\n        // are illegal (at pos ' + start + ')';\n        path.err = \"[path-util]: Invalid path value at index \".concat(start, \", \\\"\").concat(pathValue[start], \"\\\" illegal number\");\n        return;\n      }\n    }\n    while (index < max && isDigit(pathValue.charCodeAt(index))) {\n      index += 1;\n      hasCeiling = true;\n    }\n    ch = pathValue.charCodeAt(index);\n  }\n  if (ch === 0x2e /* . */) {\n    hasDot = true;\n    index += 1;\n    while (isDigit(pathValue.charCodeAt(index))) {\n      index += 1;\n      hasDecimal = true;\n    }\n    ch = pathValue.charCodeAt(index);\n  }\n  if (ch === 0x65 /* e */ || ch === 0x45 /* E */) {\n    if (hasDot && !hasCeiling && !hasDecimal) {\n      path.err = \"[path-util]: Invalid path value at index \".concat(index, \", \\\"\").concat(pathValue[index], \"\\\" invalid float exponent\");\n      return;\n    }\n    index += 1;\n    ch = pathValue.charCodeAt(index);\n    if (ch === 0x2b /* + */ || ch === 0x2d /* - */) {\n      index += 1;\n    }\n    if (index < max && isDigit(pathValue.charCodeAt(index))) {\n      while (index < max && isDigit(pathValue.charCodeAt(index))) {\n        index += 1;\n      }\n    } else {\n      path.err = \"[path-util]: Invalid path value at index \".concat(index, \", \\\"\").concat(pathValue[index], \"\\\" invalid integer exponent\");\n      return;\n    }\n  }\n  path.index = index;\n  path.param = +path.pathValue.slice(start, index);\n}", "map": {"version": 3, "names": ["isDigit", "scanParam", "path", "max", "pathValue", "start", "index", "zeroFirst", "hasCeiling", "hasDecimal", "hasDot", "ch", "err", "concat", "charCodeAt", "param", "slice"], "sources": ["path/parser/scan-param.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB;AAG1C;;;;AAIA,OAAM,SAAUC,SAASA,CAACC,IAAgB;EAChC,IAAAC,GAAG,GAA8BD,IAAI,CAAAC,GAAlC;IAAEC,SAAS,GAAmBF,IAAI,CAAAE,SAAvB;IAASC,KAAK,GAAKH,IAAI,CAAAI,KAAT;EACpC,IAAIA,KAAK,GAAGD,KAAK;EACjB,IAAIE,SAAS,GAAG,KAAK;EACrB,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,EAAE;EAEN,IAAIL,KAAK,IAAIH,GAAG,EAAE;IAChB;IACAD,IAAI,CAACU,GAAG,GAAG,4CAAAC,MAAA,CAA4CP,KAAK,qCAAgC;IAC5F;EACF;EACAK,EAAE,GAAGP,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC;EAEhC,IAAIK,EAAE,KAAK,IAAI,CAAC,WAAWA,EAAE,KAAK,IAAI,CAAC,SAAS;IAC9CL,KAAK,IAAI,CAAC;IACV;IACAK,EAAE,GAAGP,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC;EAClC;EAEA;EACA;EACA,IAAI,CAACN,OAAO,CAACW,EAAE,CAAC,IAAIA,EAAE,KAAK,IAAI,CAAC,SAAS;IACvC;IACAT,IAAI,CAACU,GAAG,GAAG,4CAAAC,MAAA,CAA4CP,KAAK,UAAAO,MAAA,CAAMT,SAAS,CAACE,KAAK,CAAC,uBAAmB;IACrG;EACF;EAEA,IAAIK,EAAE,KAAK,IAAI,CAAC,SAAS;IACvBJ,SAAS,GAAGI,EAAE,KAAK,IAAI,CAAC;IACxBL,KAAK,IAAI,CAAC;IAEVK,EAAE,GAAGP,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC;IAEhC,IAAIC,SAAS,IAAID,KAAK,GAAGH,GAAG,EAAE;MAC5B;MACA,IAAIQ,EAAE,IAAIX,OAAO,CAACW,EAAE,CAAC,EAAE;QACrB;QACA;QACAT,IAAI,CAACU,GAAG,GAAG,4CAAAC,MAAA,CAA4CR,KAAK,UAAAQ,MAAA,CAAMT,SAAS,CAACC,KAAK,CAAC,sBAAkB;QACpG;MACF;IACF;IAEA,OAAOC,KAAK,GAAGH,GAAG,IAAIH,OAAO,CAACI,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC,CAAC,EAAE;MAC1DA,KAAK,IAAI,CAAC;MACVE,UAAU,GAAG,IAAI;IACnB;IAEAG,EAAE,GAAGP,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC;EAClC;EAEA,IAAIK,EAAE,KAAK,IAAI,CAAC,SAAS;IACvBD,MAAM,GAAG,IAAI;IACbJ,KAAK,IAAI,CAAC;IACV,OAAON,OAAO,CAACI,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC,CAAC,EAAE;MAC3CA,KAAK,IAAI,CAAC;MACVG,UAAU,GAAG,IAAI;IACnB;IAEAE,EAAE,GAAGP,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC;EAClC;EAEA,IAAIK,EAAE,KAAK,IAAI,CAAC,WAAWA,EAAE,KAAK,IAAI,CAAC,SAAS;IAC9C,IAAID,MAAM,IAAI,CAACF,UAAU,IAAI,CAACC,UAAU,EAAE;MACxCP,IAAI,CAACU,GAAG,GAAG,4CAAAC,MAAA,CAA4CP,KAAK,UAAAO,MAAA,CAAMT,SAAS,CAACE,KAAK,CAAC,8BAA0B;MAC5G;IACF;IAEAA,KAAK,IAAI,CAAC;IAEVK,EAAE,GAAGP,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC;IAEhC,IAAIK,EAAE,KAAK,IAAI,CAAC,WAAWA,EAAE,KAAK,IAAI,CAAC,SAAS;MAC9CL,KAAK,IAAI,CAAC;IACZ;IACA,IAAIA,KAAK,GAAGH,GAAG,IAAIH,OAAO,CAACI,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC,CAAC,EAAE;MACvD,OAAOA,KAAK,GAAGH,GAAG,IAAIH,OAAO,CAACI,SAAS,CAACU,UAAU,CAACR,KAAK,CAAC,CAAC,EAAE;QAC1DA,KAAK,IAAI,CAAC;MACZ;IACF,CAAC,MAAM;MACLJ,IAAI,CAACU,GAAG,GAAG,4CAAAC,MAAA,CAA4CP,KAAK,UAAAO,MAAA,CAAMT,SAAS,CAACE,KAAK,CAAC,gCAA4B;MAC9G;IACF;EACF;EAEAJ,IAAI,CAACI,KAAK,GAAGA,KAAK;EAClBJ,IAAI,CAACa,KAAK,GAAG,CAACb,IAAI,CAACE,SAAS,CAACY,KAAK,CAACX,KAAK,EAAEC,KAAK,CAAC;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}