{"ast": null, "code": "import { __values } from \"tslib\";\nimport { boundTest } from '../utils/test';\nexport default function rotateLabels(labels, overlapCfg, attr, utils) {\n  var e_1, _a;\n  var _b = overlapCfg.optionalAngles,\n    optionalAngles = _b === void 0 ? [0, 45, 90] : _b,\n    margin = overlapCfg.margin,\n    _c = overlapCfg.recoverWhenFailed,\n    recoverWhenFailed = _c === void 0 ? true : _c;\n  var defaultAngles = labels.map(function (label) {\n    return label.getLocalEulerAngles();\n  });\n  var runAndPassed = function () {\n    return boundTest(labels, attr, margin).length < 1;\n  };\n  var setLabelsRotate = function (angle) {\n    return labels.forEach(function (label, index) {\n      var rotate = Array.isArray(angle) ? angle[index] : angle;\n      utils.rotate(label, +rotate);\n    });\n  };\n  try {\n    for (var optionalAngles_1 = __values(optionalAngles), optionalAngles_1_1 = optionalAngles_1.next(); !optionalAngles_1_1.done; optionalAngles_1_1 = optionalAngles_1.next()) {\n      var angle = optionalAngles_1_1.value;\n      setLabelsRotate(angle);\n      if (runAndPassed()) return;\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (optionalAngles_1_1 && !optionalAngles_1_1.done && (_a = optionalAngles_1.return)) _a.call(optionalAngles_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  if (recoverWhenFailed) {\n    setLabelsRotate(defaultAngles);\n  }\n}", "map": {"version": 3, "names": ["boundTest", "<PERSON><PERSON><PERSON><PERSON>", "labels", "overlapCfg", "attr", "utils", "_b", "optionalAngles", "margin", "_c", "recoverWhenFailed", "defaultAngles", "map", "label", "getLocalEulerAngles", "runAndPassed", "length", "setLabelsRotate", "angle", "for<PERSON>ach", "index", "rotate", "Array", "isArray", "optionalAngles_1", "__values", "optionalAngles_1_1", "next", "done", "value"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/overlap/autoRotate.ts"], "sourcesContent": ["import type { DisplayObject } from '../../../shapes';\nimport { AxisStyleProps, RotateOverlapCfg } from '../types';\nimport { boundTest } from '../utils/test';\n\nexport type Utils = {\n  rotate: (label: DisplayObject, rotate: number | string) => void;\n};\n\ntype RotateType = Parameters<Utils['rotate']>[1];\n\nexport default function rotateLabels(\n  labels: DisplayObject[],\n  overlapCfg: RotateOverlapCfg,\n  attr: AxisStyleProps,\n  utils: Utils\n) {\n  const { optionalAngles = [0, 45, 90], margin, recoverWhenFailed = true } = overlapCfg;\n\n  const defaultAngles = labels.map((label) => label.getLocalEulerAngles());\n\n  const runAndPassed = () => boundTest(labels, attr, margin).length < 1;\n\n  const setLabelsRotate = (angle: RotateType | RotateType[]) =>\n    labels.forEach((label, index) => {\n      const rotate = Array.isArray(angle) ? angle[index] : angle;\n      utils.rotate(label, +rotate);\n    });\n\n  for (const angle of optionalAngles) {\n    setLabelsRotate(angle);\n    if (runAndPassed()) return;\n  }\n\n  if (recoverWhenFailed) {\n    setLabelsRotate(defaultAngles);\n  }\n}\n"], "mappings": ";AAEA,SAASA,SAAS,QAAQ,eAAe;AAQzC,eAAc,SAAUC,YAAYA,CAClCC,MAAuB,EACvBC,UAA4B,EAC5BC,IAAoB,EACpBC,KAAY;;EAEJ,IAAAC,EAAA,GAAmEH,UAAU,CAAAI,cAAjD;IAA5BA,cAAc,GAAAD,EAAA,cAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,GAAAA,EAAA;IAAEE,MAAM,GAA+BL,UAAU,CAAAK,MAAzC;IAAEC,EAAA,GAA6BN,UAAU,CAAAO,iBAAf;IAAxBA,iBAAiB,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;EAEtE,IAAME,aAAa,GAAGT,MAAM,CAACU,GAAG,CAAC,UAACC,KAAK;IAAK,OAAAA,KAAK,CAACC,mBAAmB,EAAE;EAA3B,CAA2B,CAAC;EAExE,IAAMC,YAAY,GAAG,SAAAA,CAAA;IAAM,OAAAf,SAAS,CAACE,MAAM,EAAEE,IAAI,EAAEI,MAAM,CAAC,CAACQ,MAAM,GAAG,CAAC;EAA1C,CAA0C;EAErE,IAAMC,eAAe,GAAG,SAAAA,CAACC,KAAgC;IACvD,OAAAhB,MAAM,CAACiB,OAAO,CAAC,UAACN,KAAK,EAAEO,KAAK;MAC1B,IAAMC,MAAM,GAAGC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,GAAGA,KAAK,CAACE,KAAK,CAAC,GAAGF,KAAK;MAC1Db,KAAK,CAACgB,MAAM,CAACR,KAAK,EAAE,CAACQ,MAAM,CAAC;IAC9B,CAAC,CAAC;EAHF,CAGE;;IAEJ,KAAoB,IAAAG,gBAAA,GAAAC,QAAA,CAAAlB,cAAc,GAAAmB,kBAAA,GAAAF,gBAAA,CAAAG,IAAA,KAAAD,kBAAA,CAAAE,IAAA,EAAAF,kBAAA,GAAAF,gBAAA,CAAAG,IAAA,IAAE;MAA/B,IAAMT,KAAK,GAAAQ,kBAAA,CAAAG,KAAA;MACdZ,eAAe,CAACC,KAAK,CAAC;MACtB,IAAIH,YAAY,EAAE,EAAE;IACtB;;;;;;;;;;;;EAEA,IAAIL,iBAAiB,EAAE;IACrBO,eAAe,CAACN,aAAa,CAAC;EAChC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}