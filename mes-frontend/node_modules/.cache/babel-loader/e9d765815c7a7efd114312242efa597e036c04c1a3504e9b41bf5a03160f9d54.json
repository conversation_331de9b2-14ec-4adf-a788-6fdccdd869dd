{"ast": null, "code": "export var paramsCount = {\n  a: 7,\n  c: 6,\n  h: 1,\n  l: 2,\n  m: 2,\n  r: 4,\n  q: 4,\n  s: 4,\n  t: 2,\n  v: 1,\n  z: 0\n};", "map": {"version": 3, "names": ["paramsCount", "a", "c", "h", "l", "m", "r", "q", "s", "t", "v", "z"], "sources": ["path/parser/params-count.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAO,IAAMA,WAAW,GAAG;EACzBC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}