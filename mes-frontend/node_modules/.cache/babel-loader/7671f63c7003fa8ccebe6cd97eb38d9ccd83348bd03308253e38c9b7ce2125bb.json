{"ast": null, "code": "var _ = require(\"./lodash\");\nvar util = require(\"./util\");\nvar Graph = require(\"./graphlib\").Graph;\nmodule.exports = {\n  debugOrdering: debugOrdering\n};\n\n/* istanbul ignore next */\nfunction debugOrdering(g) {\n  var layerMatrix = util.buildLayerMatrix(g);\n  var h = new Graph({\n    compound: true,\n    multigraph: true\n  }).setGraph({});\n  _.forEach(g.nodes(), function (v) {\n    h.setNode(v, {\n      label: v\n    });\n    h.setParent(v, \"layer\" + g.node(v).rank);\n  });\n  _.forEach(g.edges(), function (e) {\n    h.setEdge(e.v, e.w, {}, e.name);\n  });\n  _.forEach(layerMatrix, function (layer, i) {\n    var layerV = \"layer\" + i;\n    h.setNode(layerV, {\n      rank: \"same\"\n    });\n    _.reduce(layer, function (u, v) {\n      h.setEdge(u, v, {\n        style: \"invis\"\n      });\n      return v;\n    });\n  });\n  return h;\n}", "map": {"version": 3, "names": ["_", "require", "util", "Graph", "module", "exports", "debugOrdering", "g", "layerMatrix", "buildLayerMatrix", "h", "compound", "multigraph", "setGraph", "for<PERSON>ach", "nodes", "v", "setNode", "label", "setParent", "node", "rank", "edges", "e", "setEdge", "w", "name", "layer", "i", "layerV", "reduce", "u", "style"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/debug.js"], "sourcesContent": ["var _ = require(\"./lodash\");\nvar util = require(\"./util\");\nvar Graph = require(\"./graphlib\").Graph;\n\nmodule.exports = {\n  debugOrdering: debugOrdering\n};\n\n/* istanbul ignore next */\nfunction debugOrdering(g) {\n  var layerMatrix = util.buildLayerMatrix(g);\n\n  var h = new Graph({ compound: true, multigraph: true }).setGraph({});\n\n  _.forEach(g.nodes(), function(v) {\n    h.setNode(v, { label: v });\n    h.setParent(v, \"layer\" + g.node(v).rank);\n  });\n\n  _.forEach(g.edges(), function(e) {\n    h.setEdge(e.v, e.w, {}, e.name);\n  });\n\n  _.forEach(layerMatrix, function(layer, i) {\n    var layerV = \"layer\" + i;\n    h.setNode(layerV, { rank: \"same\" });\n    _.reduce(layer, function(u, v) {\n      h.setEdge(u, v, { style: \"invis\" });\n      return v;\n    });\n  });\n\n  return h;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAC5B,IAAIE,KAAK,GAAGF,OAAO,CAAC,YAAY,CAAC,CAACE,KAAK;AAEvCC,MAAM,CAACC,OAAO,GAAG;EACfC,aAAa,EAAEA;AACjB,CAAC;;AAED;AACA,SAASA,aAAaA,CAACC,CAAC,EAAE;EACxB,IAAIC,WAAW,GAAGN,IAAI,CAACO,gBAAgB,CAACF,CAAC,CAAC;EAE1C,IAAIG,CAAC,GAAG,IAAIP,KAAK,CAAC;IAAEQ,QAAQ,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEpEb,CAAC,CAACc,OAAO,CAACP,CAAC,CAACQ,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/BN,CAAC,CAACO,OAAO,CAACD,CAAC,EAAE;MAAEE,KAAK,EAAEF;IAAE,CAAC,CAAC;IAC1BN,CAAC,CAACS,SAAS,CAACH,CAAC,EAAE,OAAO,GAAGT,CAAC,CAACa,IAAI,CAACJ,CAAC,CAAC,CAACK,IAAI,CAAC;EAC1C,CAAC,CAAC;EAEFrB,CAAC,CAACc,OAAO,CAACP,CAAC,CAACe,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/Bb,CAAC,CAACc,OAAO,CAACD,CAAC,CAACP,CAAC,EAAEO,CAAC,CAACE,CAAC,EAAE,CAAC,CAAC,EAAEF,CAAC,CAACG,IAAI,CAAC;EACjC,CAAC,CAAC;EAEF1B,CAAC,CAACc,OAAO,CAACN,WAAW,EAAE,UAASmB,KAAK,EAAEC,CAAC,EAAE;IACxC,IAAIC,MAAM,GAAG,OAAO,GAAGD,CAAC;IACxBlB,CAAC,CAACO,OAAO,CAACY,MAAM,EAAE;MAAER,IAAI,EAAE;IAAO,CAAC,CAAC;IACnCrB,CAAC,CAAC8B,MAAM,CAACH,KAAK,EAAE,UAASI,CAAC,EAAEf,CAAC,EAAE;MAC7BN,CAAC,CAACc,OAAO,CAACO,CAAC,EAAEf,CAAC,EAAE;QAAEgB,KAAK,EAAE;MAAQ,CAAC,CAAC;MACnC,OAAOhB,CAAC;IACV,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAON,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}