{"ast": null, "code": "// deep first search with both order low for pre, lim for post\nconst dfsBothOrder = g => {\n  const result = {};\n  let lim = 0;\n  const dfs = v => {\n    const low = lim;\n    g.getChildren(v).forEach(n => dfs(n.id));\n    result[v] = {\n      low,\n      lim: lim++\n    };\n  };\n  g.getRoots().forEach(n => dfs(n.id));\n  return result;\n};\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nconst findPath = (g, postorderNums, v, w) => {\n  var _a, _b;\n  const vPath = [];\n  const wPath = [];\n  const low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  const lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  let parent;\n  let lca;\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = (_a = g.getParent(parent)) === null || _a === void 0 ? void 0 : _a.id;\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n  // Traverse from w to LCA\n  parent = w;\n  while (parent && parent !== lca) {\n    wPath.push(parent);\n    parent = (_b = g.getParent(parent)) === null || _b === void 0 ? void 0 : _b.id;\n  }\n  return {\n    lca,\n    path: vPath.concat(wPath.reverse())\n  };\n};\nexport const parentDummyChains = (g, dummyChains) => {\n  const postorderNums = dfsBothOrder(g);\n  dummyChains.forEach(startV => {\n    var _a, _b;\n    let v = startV;\n    let node = g.getNode(v);\n    const originalEdge = node.data.originalEdge;\n    if (!originalEdge) return;\n    const pathData = findPath(g, postorderNums, originalEdge.source, originalEdge.target);\n    const path = pathData.path;\n    const lca = pathData.lca;\n    let pathIdx = 0;\n    let pathV = path[pathIdx];\n    let ascending = true;\n    while (v !== originalEdge.target) {\n      node = g.getNode(v);\n      if (ascending) {\n        while (pathV !== lca && ((_a = g.getNode(pathV)) === null || _a === void 0 ? void 0 : _a.data.maxRank) < node.data.rank) {\n          pathIdx++;\n          pathV = path[pathIdx];\n        }\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n      if (!ascending) {\n        while (pathIdx < path.length - 1 && ((_b = g.getNode(path[pathIdx + 1])) === null || _b === void 0 ? void 0 : _b.data.minRank) <= node.data.rank) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n      if (g.hasNode(pathV)) {\n        g.setParent(v, pathV);\n      }\n      v = g.getSuccessors(v)[0].id;\n    }\n  });\n};", "map": {"version": 3, "names": ["dfsBothOrder", "g", "result", "lim", "dfs", "v", "low", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "n", "id", "getRoots", "<PERSON><PERSON><PERSON>", "postorderNums", "w", "vPath", "wPath", "Math", "min", "max", "parent", "lca", "_a", "getParent", "push", "_b", "path", "concat", "reverse", "parent<PERSON>ummy<PERSON><PERSON><PERSON>", "dummy<PERSON><PERSON><PERSON>", "startV", "node", "getNode", "originalEdge", "data", "pathData", "source", "target", "pathIdx", "pathV", "ascending", "maxRank", "rank", "length", "minRank", "hasNode", "setParent", "getSuccessors"], "sources": ["../../src/antv-dagre/parent-dummy-chains.ts"], "sourcesContent": [null], "mappings": "AAKA;AACA,MAAMA,YAAY,GAAIC,CAAS,IAAI;EACjC,MAAMC,MAAM,GAA0B,EAAE;EACxC,IAAIC,GAAG,GAAG,CAAC;EAEX,MAAMC,GAAG,GAAIC,CAAK,IAAI;IACpB,MAAMC,GAAG,GAAGH,GAAG;IACfF,CAAC,CAACM,WAAW,CAACF,CAAC,CAAC,CAACG,OAAO,CAAEC,CAAC,IAAKL,GAAG,CAACK,CAAC,CAACC,EAAE,CAAC,CAAC;IAC1CR,MAAM,CAACG,CAAC,CAAC,GAAG;MAAEC,GAAG;MAAEH,GAAG,EAAEA,GAAG;IAAE,CAAE;EACjC,CAAC;EACDF,CAAC,CAACU,QAAQ,EAAE,CAACH,OAAO,CAAEC,CAAC,IAAKL,GAAG,CAACK,CAAC,CAACC,EAAE,CAAC,CAAC;EAEtC,OAAOR,MAAM;AACf,CAAC;AAED;AACA;AACA,MAAMU,QAAQ,GAAGA,CACfX,CAAS,EACTY,aAAoC,EACpCR,CAAK,EACLS,CAAK,KACH;;EACF,MAAMC,KAAK,GAAS,EAAE;EACtB,MAAMC,KAAK,GAAS,EAAE;EACtB,MAAMV,GAAG,GAAGW,IAAI,CAACC,GAAG,CAACL,aAAa,CAACR,CAAC,CAAC,CAACC,GAAG,EAAEO,aAAa,CAACC,CAAC,CAAC,CAACR,GAAG,CAAC;EAChE,MAAMH,GAAG,GAAGc,IAAI,CAACE,GAAG,CAACN,aAAa,CAACR,CAAC,CAAC,CAACF,GAAG,EAAEU,aAAa,CAACC,CAAC,CAAC,CAACX,GAAG,CAAC;EAChE,IAAIiB,MAAsB;EAC1B,IAAIC,GAAmB;EAEvB;EACAD,MAAM,GAAGf,CAAC;EACV,GAAG;IACDe,MAAM,GAAG,CAAAE,EAAA,GAAArB,CAAC,CAACsB,SAAS,CAACH,MAAM,CAAC,cAAAE,EAAA,uBAAAA,EAAA,CAAEZ,EAAE;IAChCK,KAAK,CAACS,IAAI,CAACJ,MAAO,CAAC;GACpB,QACCA,MAAM,KACLP,aAAa,CAACO,MAAM,CAAC,CAACd,GAAG,GAAGA,GAAG,IAAIH,GAAG,GAAGU,aAAa,CAACO,MAAM,CAAC,CAACjB,GAAG,CAAC;EAEtEkB,GAAG,GAAGD,MAAM;EAEZ;EACAA,MAAM,GAAGN,CAAC;EACV,OAAOM,MAAM,IAAIA,MAAM,KAAKC,GAAG,EAAE;IAC/BL,KAAK,CAACQ,IAAI,CAACJ,MAAM,CAAC;IAClBA,MAAM,GAAG,CAAAK,EAAA,GAAAxB,CAAC,CAACsB,SAAS,CAACH,MAAM,CAAC,cAAAK,EAAA,uBAAAA,EAAA,CAAEf,EAAE;;EAGlC,OAAO;IAAEW,GAAG;IAAEK,IAAI,EAAEX,KAAK,CAACY,MAAM,CAACX,KAAK,CAACY,OAAO,EAAE;EAAC,CAAE;AACrD,CAAC;AAED,OAAO,MAAMC,iBAAiB,GAAGA,CAAC5B,CAAS,EAAE6B,WAAiB,KAAI;EAChE,MAAMjB,aAAa,GAAGb,YAAY,CAACC,CAAC,CAAC;EAErC6B,WAAW,CAACtB,OAAO,CAAEuB,MAAM,IAAI;;IAC7B,IAAI1B,CAAC,GAAG0B,MAAM;IACd,IAAIC,IAAI,GAAG/B,CAAC,CAACgC,OAAO,CAAC5B,CAAC,CAAE;IACxB,MAAM6B,YAAY,GAAGF,IAAI,CAACG,IAAI,CAACD,YAA8B;IAC7D,IAAI,CAACA,YAAY,EAAE;IACnB,MAAME,QAAQ,GAAGxB,QAAQ,CACvBX,CAAC,EACDY,aAAa,EACbqB,YAAY,CAACG,MAAM,EACnBH,YAAY,CAACI,MAAM,CACpB;IACD,MAAMZ,IAAI,GAAGU,QAAQ,CAACV,IAAI;IAC1B,MAAML,GAAG,GAAGe,QAAQ,CAACf,GAAG;IACxB,IAAIkB,OAAO,GAAG,CAAC;IACf,IAAIC,KAAK,GAAGd,IAAI,CAACa,OAAO,CAAE;IAC1B,IAAIE,SAAS,GAAG,IAAI;IAEpB,OAAOpC,CAAC,KAAK6B,YAAY,CAACI,MAAM,EAAE;MAChCN,IAAI,GAAG/B,CAAC,CAACgC,OAAO,CAAC5B,CAAC,CAAE;MAEpB,IAAIoC,SAAS,EAAE;QACb,OACED,KAAK,KAAKnB,GAAG,IACb,EAAAC,EAAA,GAAArB,CAAC,CAACgC,OAAO,CAACO,KAAK,CAAC,cAAAlB,EAAA,uBAAAA,EAAA,CAAEa,IAAI,CAACO,OAAQ,IAAGV,IAAI,CAACG,IAAI,CAACQ,IAAK,EACjD;UACAJ,OAAO,EAAE;UACTC,KAAK,GAAGd,IAAI,CAACa,OAAO,CAAE;;QAGxB,IAAIC,KAAK,KAAKnB,GAAG,EAAE;UACjBoB,SAAS,GAAG,KAAK;;;MAIrB,IAAI,CAACA,SAAS,EAAE;QACd,OACEF,OAAO,GAAGb,IAAI,CAACkB,MAAM,GAAG,CAAC,IACzB,EAAAnB,EAAA,GAAAxB,CAAC,CAACgC,OAAO,CAACP,IAAI,CAACa,OAAO,GAAG,CAAC,CAAE,CAAC,cAAAd,EAAA,uBAAAA,EAAA,CAAEU,IAAI,CAACU,OAAQ,KAAIb,IAAI,CAACG,IAAI,CAACQ,IAAK,EAC/D;UACAJ,OAAO,EAAE;;QAEXC,KAAK,GAAGd,IAAI,CAACa,OAAO,CAAE;;MAGxB,IAAItC,CAAC,CAAC6C,OAAO,CAACN,KAAK,CAAC,EAAE;QACpBvC,CAAC,CAAC8C,SAAS,CAAC1C,CAAC,EAAEmC,KAAK,CAAC;;MAGvBnC,CAAC,GAAGJ,CAAC,CAAC+C,aAAa,CAAC3C,CAAC,CAAE,CAAC,CAAC,CAAC,CAACK,EAAE;;EAEjC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}