{"ast": null, "code": "var _a;\nexport var POPTIP_ID = 'component-poptip';\n/**\n * 默认类名\n */\nexport var CLASS_NAME = {\n  CONTAINER: 'component-poptip',\n  ARROW: 'component-poptip-arrow',\n  TEXT: 'component-poptip-text'\n};\n/**\n * 默认 style\n */\nexport var POPTIP_STYLE = (_a = {},\n// 容器 默认 style\n_a[\".\".concat(CLASS_NAME.CONTAINER)] = {\n  visibility: 'visible',\n  position: 'absolute',\n  'background-color': 'rgba(0, 0, 0)',\n  'box-shadow': '0px 0px 10px #aeaeae',\n  'border-radius': '3px',\n  color: '#fff',\n  opacity: 0.8,\n  'font-size': '12px',\n  padding: '4px 6px',\n  display: 'flex',\n  'justify-content': 'center',\n  'align-items': 'center',\n  'z-index': 8,\n  transition: 'visibility 50ms'\n},\n// 文本内容 默认 style\n_a[\".\".concat(CLASS_NAME.TEXT)] = {\n  'text-align': 'center'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='top']\")] = {\n  transform: \"translate(-50%, -100%)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='left']\")] = {\n  transform: \"translate(-100%, -50%)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='right']\")] = {\n  transform: \"translate(0, -50%)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='bottom']\")] = {\n  transform: \"translate(-50%, 0)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='top-left']\")] = {\n  transform: \"translate(0,-100%)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='top-right']\")] = {\n  transform: \"translate(-100%,-100%)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='left-top']\")] = {\n  transform: \"translate(-100%, 0)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='left-bottom']\")] = {\n  transform: \"translate(-100%, -100%)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='right-top']\")] = {\n  transform: \"translate(0, 0)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='right-bottom']\")] = {\n  transform: \"translate(0, -100%)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='bottom-left']\")] = {\n  transform: \"translate(0, 0)\"\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='bottom-right']\")] = {\n  transform: \"translate(-100%, 0)\"\n}, _a[\".\".concat(CLASS_NAME.ARROW)] = {\n  width: '4px',\n  height: '4px',\n  transform: 'rotate(45deg)',\n  'background-color': 'rgba(0, 0, 0)',\n  position: 'absolute',\n  'z-index': -1\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='top']\")] = {\n  transform: \"translate(-50%, calc(-100% - 5px))\"\n}, _a[\"[data-position='top'] .\".concat(CLASS_NAME.ARROW)] = {\n  bottom: '-2px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='left']\")] = {\n  transform: \"translate(calc(-100% - 5px), -50%)\"\n}, _a[\"[data-position='left'] .\".concat(CLASS_NAME.ARROW)] = {\n  right: '-2px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='right']\")] = {\n  transform: \"translate(5px, -50%)\"\n}, _a[\"[data-position='right'] .\".concat(CLASS_NAME.ARROW)] = {\n  left: '-2px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='bottom']\")] = {\n  transform: \"translate(-50%, 5px)\"\n}, _a[\"[data-position='bottom'] .\".concat(CLASS_NAME.ARROW)] = {\n  top: '-2px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='top-left']\")] = {\n  transform: \"translate(0, calc(-100% - 5px))\"\n}, _a[\"[data-position='top-left'] .\".concat(CLASS_NAME.ARROW)] = {\n  left: '10px',\n  bottom: '-2px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='top-right']\")] = {\n  transform: \"translate(-100%, calc(-100% - 5px))\"\n}, _a[\"[data-position='top-right'] .\".concat(CLASS_NAME.ARROW)] = {\n  right: '10px',\n  bottom: '-2px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='left-top']\")] = {\n  transform: \"translate(calc(-100% - 5px), 0)\"\n}, _a[\"[data-position='left-top'] .\".concat(CLASS_NAME.ARROW)] = {\n  right: '-2px',\n  top: '8px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='left-bottom']\")] = {\n  transform: \"translate(calc(-100% - 5px), -100%)\"\n}, _a[\"[data-position='left-bottom'] .\".concat(CLASS_NAME.ARROW)] = {\n  right: '-2px',\n  bottom: '8px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='right-top']\")] = {\n  transform: \"translate(5px, 0)\"\n}, _a[\"[data-position='right-top'] .\".concat(CLASS_NAME.ARROW)] = {\n  left: '-2px',\n  top: '8px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='right-bottom']\")] = {\n  transform: \"translate(5px, -100%)\"\n}, _a[\"[data-position='right-bottom'] .\".concat(CLASS_NAME.ARROW)] = {\n  left: '-2px',\n  bottom: '8px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='bottom-left']\")] = {\n  transform: \"translate(0, 5px)\"\n}, _a[\"[data-position='bottom-left'] .\".concat(CLASS_NAME.ARROW)] = {\n  top: '-2px',\n  left: '8px'\n}, _a[\".\".concat(CLASS_NAME.CONTAINER, \"[data-position='bottom-right']\")] = {\n  transform: \"translate(-100%, 5px)\"\n}, _a[\"[data-position='bottom-right'] .\".concat(CLASS_NAME.ARROW)] = {\n  top: '-2px',\n  right: '8px'\n}, _a);", "map": {"version": 3, "names": ["POPTIP_ID", "CLASS_NAME", "CONTAINER", "ARROW", "TEXT", "POPTIP_STYLE", "_a", "concat", "visibility", "position", "color", "opacity", "padding", "display", "transition", "transform", "width", "height", "bottom", "right", "left", "top"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/poptip/constant.ts"], "sourcesContent": ["export const POPTIP_ID = 'component-poptip';\n\n/**\n * 默认类名\n */\nexport const CLASS_NAME = {\n  CONTAINER: 'component-poptip',\n  ARROW: 'component-poptip-arrow',\n  TEXT: 'component-poptip-text',\n};\n\n/**\n * 默认 style\n */\nexport const POPTIP_STYLE = {\n  // 容器 默认 style\n  [`.${CLASS_NAME.CONTAINER}`]: {\n    visibility: 'visible',\n    position: 'absolute',\n    'background-color': 'rgba(0, 0, 0)',\n    'box-shadow': '0px 0px 10px #aeaeae',\n    'border-radius': '3px',\n    color: '#fff',\n    opacity: 0.8,\n    'font-size': '12px',\n    padding: '4px 6px',\n    display: 'flex',\n    'justify-content': 'center',\n    'align-items': 'center',\n    'z-index': 8,\n    transition: 'visibility 50ms',\n  },\n  // 文本内容 默认 style\n  [`.${CLASS_NAME.TEXT}`]: {\n    'text-align': 'center',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='top']`]: {\n    transform: `translate(-50%, -100%)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='left']`]: {\n    transform: `translate(-100%, -50%)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='right']`]: {\n    transform: `translate(0, -50%)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='bottom']`]: {\n    transform: `translate(-50%, 0)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='top-left']`]: {\n    transform: `translate(0,-100%)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='top-right']`]: {\n    transform: `translate(-100%,-100%)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='left-top']`]: {\n    transform: `translate(-100%, 0)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='left-bottom']`]: {\n    transform: `translate(-100%, -100%)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='right-top']`]: {\n    transform: `translate(0, 0)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='right-bottom']`]: {\n    transform: `translate(0, -100%)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='bottom-left']`]: {\n    transform: `translate(0, 0)`,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='bottom-right']`]: {\n    transform: `translate(-100%, 0)`,\n  },\n  [`.${CLASS_NAME.ARROW}`]: {\n    width: '4px',\n    height: '4px',\n    transform: 'rotate(45deg)',\n    'background-color': 'rgba(0, 0, 0)',\n    position: 'absolute',\n    'z-index': -1,\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='top']`]: {\n    transform: `translate(-50%, calc(-100% - 5px))`,\n  },\n  [`[data-position='top'] .${CLASS_NAME.ARROW}`]: {\n    bottom: '-2px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='left']`]: {\n    transform: `translate(calc(-100% - 5px), -50%)`,\n  },\n  [`[data-position='left'] .${CLASS_NAME.ARROW}`]: {\n    right: '-2px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='right']`]: {\n    transform: `translate(5px, -50%)`,\n  },\n  [`[data-position='right'] .${CLASS_NAME.ARROW}`]: {\n    left: '-2px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='bottom']`]: {\n    transform: `translate(-50%, 5px)`,\n  },\n  [`[data-position='bottom'] .${CLASS_NAME.ARROW}`]: {\n    top: '-2px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='top-left']`]: {\n    transform: `translate(0, calc(-100% - 5px))`,\n  },\n  [`[data-position='top-left'] .${CLASS_NAME.ARROW}`]: {\n    left: '10px',\n    bottom: '-2px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='top-right']`]: {\n    transform: `translate(-100%, calc(-100% - 5px))`,\n  },\n  [`[data-position='top-right'] .${CLASS_NAME.ARROW}`]: {\n    right: '10px',\n    bottom: '-2px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='left-top']`]: {\n    transform: `translate(calc(-100% - 5px), 0)`,\n  },\n  [`[data-position='left-top'] .${CLASS_NAME.ARROW}`]: {\n    right: '-2px',\n    top: '8px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='left-bottom']`]: {\n    transform: `translate(calc(-100% - 5px), -100%)`,\n  },\n  [`[data-position='left-bottom'] .${CLASS_NAME.ARROW}`]: {\n    right: '-2px',\n    bottom: '8px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='right-top']`]: {\n    transform: `translate(5px, 0)`,\n  },\n  [`[data-position='right-top'] .${CLASS_NAME.ARROW}`]: {\n    left: '-2px',\n    top: '8px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='right-bottom']`]: {\n    transform: `translate(5px, -100%)`,\n  },\n  [`[data-position='right-bottom'] .${CLASS_NAME.ARROW}`]: {\n    left: '-2px',\n    bottom: '8px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='bottom-left']`]: {\n    transform: `translate(0, 5px)`,\n  },\n  [`[data-position='bottom-left'] .${CLASS_NAME.ARROW}`]: {\n    top: '-2px',\n    left: '8px',\n  },\n  [`.${CLASS_NAME.CONTAINER}[data-position='bottom-right']`]: {\n    transform: `translate(-100%, 5px)`,\n  },\n  [`[data-position='bottom-right'] .${CLASS_NAME.ARROW}`]: {\n    top: '-2px',\n    right: '8px',\n  },\n};\n"], "mappings": ";AAAA,OAAO,IAAMA,SAAS,GAAG,kBAAkB;AAE3C;;;AAGA,OAAO,IAAMC,UAAU,GAAG;EACxBC,SAAS,EAAE,kBAAkB;EAC7BC,KAAK,EAAE,wBAAwB;EAC/BC,IAAI,EAAE;CACP;AAED;;;AAGA,OAAO,IAAMC,YAAY,IAAAC,EAAA;AACvB;AACAA,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,CAAE,IAAG;EAC5BM,UAAU,EAAE,SAAS;EACrBC,QAAQ,EAAE,UAAU;EACpB,kBAAkB,EAAE,eAAe;EACnC,YAAY,EAAE,sBAAsB;EACpC,eAAe,EAAE,KAAK;EACtBC,KAAK,EAAE,MAAM;EACbC,OAAO,EAAE,GAAG;EACZ,WAAW,EAAE,MAAM;EACnBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,MAAM;EACf,iBAAiB,EAAE,QAAQ;EAC3B,aAAa,EAAE,QAAQ;EACvB,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE;CACb;AACD;AACAR,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACG,IAAI,CAAE,IAAG;EACvB,YAAY,EAAE;CACf,EACDE,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,0BAAuB,IAAG;EACjDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,2BAAwB,IAAG;EAClDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,4BAAyB,IAAG;EACnDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,6BAA0B,IAAG;EACpDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,+BAA4B,IAAG;EACtDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,gCAA6B,IAAG;EACvDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,+BAA4B,IAAG;EACtDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,kCAA+B,IAAG;EACzDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,gCAA6B,IAAG;EACvDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,mCAAgC,IAAG;EAC1Da,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,kCAA+B,IAAG;EACzDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,mCAAgC,IAAG;EAC1Da,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACE,KAAK,CAAE,IAAG;EACxBa,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE,KAAK;EACbF,SAAS,EAAE,eAAe;EAC1B,kBAAkB,EAAE,eAAe;EACnCN,QAAQ,EAAE,UAAU;EACpB,SAAS,EAAE,CAAC;CACb,EACDH,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,0BAAuB,IAAG;EACjDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,0BAAAC,MAAA,CAA0BN,UAAU,CAACE,KAAK,CAAE,IAAG;EAC9Ce,MAAM,EAAE;CACT,EACDZ,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,2BAAwB,IAAG;EAClDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,2BAAAC,MAAA,CAA2BN,UAAU,CAACE,KAAK,CAAE,IAAG;EAC/CgB,KAAK,EAAE;CACR,EACDb,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,4BAAyB,IAAG;EACnDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,4BAAAC,MAAA,CAA4BN,UAAU,CAACE,KAAK,CAAE,IAAG;EAChDiB,IAAI,EAAE;CACP,EACDd,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,6BAA0B,IAAG;EACpDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,6BAAAC,MAAA,CAA6BN,UAAU,CAACE,KAAK,CAAE,IAAG;EACjDkB,GAAG,EAAE;CACN,EACDf,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,+BAA4B,IAAG;EACtDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,+BAAAC,MAAA,CAA+BN,UAAU,CAACE,KAAK,CAAE,IAAG;EACnDiB,IAAI,EAAE,MAAM;EACZF,MAAM,EAAE;CACT,EACDZ,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,gCAA6B,IAAG;EACvDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,gCAAAC,MAAA,CAAgCN,UAAU,CAACE,KAAK,CAAE,IAAG;EACpDgB,KAAK,EAAE,MAAM;EACbD,MAAM,EAAE;CACT,EACDZ,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,+BAA4B,IAAG;EACtDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,+BAAAC,MAAA,CAA+BN,UAAU,CAACE,KAAK,CAAE,IAAG;EACnDgB,KAAK,EAAE,MAAM;EACbE,GAAG,EAAE;CACN,EACDf,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,kCAA+B,IAAG;EACzDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,kCAAAC,MAAA,CAAkCN,UAAU,CAACE,KAAK,CAAE,IAAG;EACtDgB,KAAK,EAAE,MAAM;EACbD,MAAM,EAAE;CACT,EACDZ,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,gCAA6B,IAAG;EACvDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,gCAAAC,MAAA,CAAgCN,UAAU,CAACE,KAAK,CAAE,IAAG;EACpDiB,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE;CACN,EACDf,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,mCAAgC,IAAG;EAC1Da,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,mCAAAC,MAAA,CAAmCN,UAAU,CAACE,KAAK,CAAE,IAAG;EACvDiB,IAAI,EAAE,MAAM;EACZF,MAAM,EAAE;CACT,EACDZ,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,kCAA+B,IAAG;EACzDa,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,kCAAAC,MAAA,CAAkCN,UAAU,CAACE,KAAK,CAAE,IAAG;EACtDkB,GAAG,EAAE,MAAM;EACXD,IAAI,EAAE;CACP,EACDd,EAAA,CAAC,IAAAC,MAAA,CAAIN,UAAU,CAACC,SAAS,mCAAgC,IAAG;EAC1Da,SAAS,EAAE;CACZ,EACDT,EAAA,CAAC,mCAAAC,MAAA,CAAmCN,UAAU,CAACE,KAAK,CAAE,IAAG;EACvDkB,GAAG,EAAE,MAAM;EACXF,KAAK,EAAE;CACR,E,GACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}