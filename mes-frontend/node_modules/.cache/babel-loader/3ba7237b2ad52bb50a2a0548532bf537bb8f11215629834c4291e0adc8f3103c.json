{"ast": null, "code": "export default function (value) {\n  return this.eachAfter(function (node) {\n    var sum = +value(node.data) || 0,\n      children = node.children,\n      i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}", "map": {"version": 3, "names": ["value", "eachAfter", "node", "sum", "data", "children", "i", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/hierarchy/sum.js"], "sourcesContent": ["export default function(value) {\n  return this.eachAfter(function(node) {\n    var sum = +value(node.data) || 0,\n        children = node.children,\n        i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}\n"], "mappings": "AAAA,eAAe,UAASA,KAAK,EAAE;EAC7B,OAAO,IAAI,CAACC,SAAS,CAAC,UAASC,IAAI,EAAE;IACnC,IAAIC,GAAG,GAAG,CAACH,KAAK,CAACE,IAAI,CAACE,IAAI,CAAC,IAAI,CAAC;MAC5BC,QAAQ,GAAGH,IAAI,CAACG,QAAQ;MACxBC,CAAC,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,MAAM;IACnC,OAAO,EAAED,CAAC,IAAI,CAAC,EAAEH,GAAG,IAAIE,QAAQ,CAACC,CAAC,CAAC,CAACN,KAAK;IACzCE,IAAI,CAACF,KAAK,GAAGG,GAAG;EAClB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}