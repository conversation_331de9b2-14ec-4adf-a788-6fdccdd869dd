{"ast": null, "code": "const filterOutLinks = (k, v) => {\n  if (k !== 'next' && k !== 'prev') {\n    return v;\n  }\n};\nconst unlink = entry => {\n  entry.prev.next = entry.next;\n  entry.next.prev = entry.prev;\n  delete entry.next;\n  delete entry.prev;\n};\nexport default class List {\n  constructor() {\n    const shortcut = {};\n    shortcut.prev = shortcut;\n    shortcut.next = shortcut.prev;\n    this.shortcut = shortcut;\n  }\n  dequeue() {\n    const shortcut = this.shortcut;\n    const entry = shortcut.prev;\n    if (entry && entry !== shortcut) {\n      unlink(entry);\n      return entry;\n    }\n  }\n  enqueue(entry) {\n    const shortcut = this.shortcut;\n    if (entry.prev && entry.next) {\n      unlink(entry);\n    }\n    entry.next = shortcut.next;\n    shortcut.next.prev = entry;\n    shortcut.next = entry;\n    entry.prev = shortcut;\n  }\n  toString() {\n    const strs = [];\n    const sentinel = this.shortcut;\n    let curr = sentinel.prev;\n    while (curr !== sentinel) {\n      strs.push(JSON.stringify(curr, filterOutLinks));\n      curr = curr === null || curr === void 0 ? void 0 : curr.prev;\n    }\n    return `[${strs.join(', ')}]`;\n  }\n}", "map": {"version": 3, "names": ["filterOutLinks", "k", "v", "unlink", "entry", "prev", "next", "List", "constructor", "shortcut", "dequeue", "enqueue", "toString", "strs", "sentinel", "curr", "push", "JSON", "stringify", "join"], "sources": ["../../../src/antv-dagre/data/list.ts"], "sourcesContent": [null], "mappings": "AAAA,MAAMA,cAAc,GAAGA,CAACC,CAAS,EAAEC,CAAS,KAAI;EAC9C,IAAID,CAAC,KAAK,MAAM,IAAIA,CAAC,KAAK,MAAM,EAAE;IAChC,OAAOC,CAAC;;AAEZ,CAAC;AAOD,MAAMC,MAAM,GAAOC,KAAoB,IAAI;EACzCA,KAAK,CAACC,IAAK,CAACC,IAAI,GAAGF,KAAK,CAACE,IAAI;EAC7BF,KAAK,CAACE,IAAK,CAACD,IAAI,GAAGD,KAAK,CAACC,IAAI;EAC7B,OAAOD,KAAK,CAACE,IAAI;EACjB,OAAOF,KAAK,CAACC,IAAI;AACnB,CAAC;AAED,eAAc,MAAOE,IAAI;EAIvBC,YAAA;IACE,MAAMC,QAAQ,GAAoB,EAAE;IACpCA,QAAQ,CAACJ,IAAI,GAAGI,QAAQ;IACxBA,QAAQ,CAACH,IAAI,GAAGG,QAAQ,CAACJ,IAAI;IAC7B,IAAI,CAACI,QAAQ,GAAGA,QAAQ;EAC1B;EAEOC,OAAOA,CAAA;IACZ,MAAMD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAML,KAAK,GAAGK,QAAQ,CAACJ,IAAI;IAC3B,IAAID,KAAK,IAAIA,KAAK,KAAKK,QAAQ,EAAE;MAC/BN,MAAM,CAACC,KAAK,CAAC;MACb,OAAOA,KAAK;;EAEhB;EAEOO,OAAOA,CAACP,KAAoB;IACjC,MAAMK,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAIL,KAAK,CAACC,IAAI,IAAID,KAAK,CAACE,IAAI,EAAE;MAC5BH,MAAM,CAACC,KAAK,CAAC;;IAEfA,KAAK,CAACE,IAAI,GAAGG,QAAQ,CAACH,IAAI;IAC1BG,QAAQ,CAACH,IAAK,CAACD,IAAI,GAAGD,KAAK;IAC3BK,QAAQ,CAACH,IAAI,GAAGF,KAAK;IACrBA,KAAK,CAACC,IAAI,GAAGI,QAAQ;EACvB;EAEOG,QAAQA,CAAA;IACb,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACL,QAAQ;IAC9B,IAAIM,IAAI,GAAGD,QAAQ,CAACT,IAAI;IACxB,OAAOU,IAAI,KAAKD,QAAQ,EAAE;MACxBD,IAAI,CAACG,IAAI,CAACC,IAAI,CAACC,SAAS,CAACH,IAAI,EAAEf,cAAc,CAAC,CAAC;MAC/Ce,IAAI,GAAGA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEV,IAAI;;IAEnB,OAAO,IAAIQ,IAAI,CAACM,IAAI,CAAC,IAAI,CAAC,GAAG;EAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}