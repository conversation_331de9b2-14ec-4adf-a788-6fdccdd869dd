{"ast": null, "code": "export * from './test';\nexport function getFactor() {\n  var args = [];\n  for (var _i = 0; _i < arguments.length; _i++) {\n    args[_i] = arguments[_i];\n  }\n  var fn = function (str) {\n    return str === 'positive' ? -1 : 1;\n  };\n  return args.reduce(function (acc, cur) {\n    return acc * fn(cur);\n  }, 1);\n}", "map": {"version": 3, "names": ["getFactor", "args", "_i", "arguments", "length", "fn", "str", "reduce", "acc", "cur"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/utils/index.ts"], "sourcesContent": ["import type { VerticalFactor, Direction } from '../types';\n\nexport * from './test';\n\nexport function getFactor(...args: Direction[]): VerticalFactor {\n  const fn = (str: (typeof args)[number]): VerticalFactor => (str === 'positive' ? -1 : 1);\n  return args.reduce((acc, cur) => acc * fn(cur), 1) as unknown as VerticalFactor;\n}\n"], "mappings": "AAEA,cAAc,QAAQ;AAEtB,OAAM,SAAUA,SAASA,CAAA;EAAC,IAAAC,IAAA;OAAA,IAAAC,EAAA,IAAoB,EAApBA,EAAA,GAAAC,SAAA,CAAAC,MAAoB,EAApBF,EAAA,EAAoB;IAApBD,IAAA,CAAAC,EAAA,IAAAC,SAAA,CAAAD,EAAA;;EACxB,IAAMG,EAAE,GAAG,SAAAA,CAACC,GAA0B;IAAqB,OAACA,GAAG,KAAK,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;EAA5B,CAA6B;EACxF,OAAOL,IAAI,CAACM,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG;IAAK,OAAAD,GAAG,GAAGH,EAAE,CAACI,GAAG,CAAC;EAAb,CAAa,EAAE,CAAC,CAA8B;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}