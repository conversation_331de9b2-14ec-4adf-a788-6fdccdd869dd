{"ast": null, "code": "export default function (parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n    node,\n    i = -1,\n    n = nodes.length,\n    k = parent.value && (y1 - y0) / parent.value;\n  while (++i < n) {\n    node = nodes[i], node.x0 = x0, node.x1 = x1;\n    node.y0 = y0, node.y1 = y0 += node.value * k;\n  }\n}", "map": {"version": 3, "names": ["parent", "x0", "y0", "x1", "y1", "nodes", "children", "node", "i", "n", "length", "k", "value"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/treemap/slice.js"], "sourcesContent": ["export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (y1 - y0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.x0 = x0, node.x1 = x1;\n    node.y0 = y0, node.y1 = y0 += node.value * k;\n  }\n}\n"], "mappings": "AAAA,eAAe,UAASA,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC9C,IAAIC,KAAK,GAAGL,MAAM,CAACM,QAAQ;IACvBC,IAAI;IACJC,CAAC,GAAG,CAAC,CAAC;IACNC,CAAC,GAAGJ,KAAK,CAACK,MAAM;IAChBC,CAAC,GAAGX,MAAM,CAACY,KAAK,IAAI,CAACR,EAAE,GAAGF,EAAE,IAAIF,MAAM,CAACY,KAAK;EAEhD,OAAO,EAAEJ,CAAC,GAAGC,CAAC,EAAE;IACdF,IAAI,GAAGF,KAAK,CAACG,CAAC,CAAC,EAAED,IAAI,CAACN,EAAE,GAAGA,EAAE,EAAEM,IAAI,CAACJ,EAAE,GAAGA,EAAE;IAC3CI,IAAI,CAACL,EAAE,GAAGA,EAAE,EAAEK,IAAI,CAACH,EAAE,GAAGF,EAAE,IAAIK,IAAI,CAACK,KAAK,GAAGD,CAAC;EAC9C;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}