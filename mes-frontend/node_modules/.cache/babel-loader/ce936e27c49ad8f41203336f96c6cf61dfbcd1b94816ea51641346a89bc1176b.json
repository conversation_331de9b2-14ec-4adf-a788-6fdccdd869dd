{"ast": null, "code": "/* eslint \"no-console\": off */\n\n\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar Graph = require(\"./graphlib\").Graph;\nmodule.exports = {\n  addDummyNode: addDummyNode,\n  simplify: simplify,\n  asNonCompoundGraph: asNonCompoundGraph,\n  successorWeights: successorWeights,\n  predecessorWeights: predecessorWeights,\n  intersectRect: intersectRect,\n  buildLayerMatrix: buildLayerMatrix,\n  normalizeRanks: normalizeRanks,\n  removeEmptyRanks: removeEmptyRanks,\n  addBorderNode: addBorderNode,\n  maxRank: maxRank,\n  partition: partition,\n  time: time,\n  notime: notime\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    simplified.setNode(v, g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || {\n      weight: 0,\n      minlen: 1\n    };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen)\n    });\n  });\n  return simplified;\n}\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({\n    multigraph: g.isMultigraph()\n  }).setGraph(g.graph());\n  _.forEach(g.nodes(), function (v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function (e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function (e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function (v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function (e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n  if (!dx && !dy) {\n    throw new Error(\"Not possible to find intersection inside of the rectangle\");\n  }\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = h * dx / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = w * dy / dx;\n  }\n  return {\n    x: x + sx,\n    y: y + sy\n  };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function () {\n    return [];\n  });\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(_.map(g.nodes(), function (v) {\n    return g.node(v).rank;\n  }));\n  _.forEach(g.nodes(), function (v) {\n    var node = g.node(v);\n    if (_.has(node, \"rank\")) {\n      node.rank -= min;\n    }\n  });\n}\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(_.map(g.nodes(), function (v) {\n    return g.node(v).rank;\n  }));\n  var layers = [];\n  _.forEach(g.nodes(), function (v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function (vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function (v) {\n        g.node(v).rank += delta;\n      });\n    }\n  });\n}\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, \"border\", node, prefix);\n}\nfunction maxRank(g) {\n  return _.max(_.map(g.nodes(), function (v) {\n    var rank = g.node(v).rank;\n    if (!_.isUndefined(rank)) {\n      return rank;\n    }\n  }));\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = {\n    lhs: [],\n    rhs: []\n  };\n  _.forEach(collection, function (value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + \" time: \" + (_.now() - start) + \"ms\");\n  }\n}\nfunction notime(name, fn) {\n  return fn();\n}", "map": {"version": 3, "names": ["_", "require", "Graph", "module", "exports", "addDummyNode", "simplify", "asNonCompoundGraph", "successorWeights", "predecessorWeights", "intersectRect", "buildLayerMatrix", "normalizeRanks", "removeEmptyRanks", "addBorderNode", "maxRank", "partition", "time", "notime", "g", "type", "attrs", "name", "v", "uniqueId", "hasNode", "dummy", "setNode", "simplified", "setGraph", "graph", "for<PERSON>ach", "nodes", "node", "edges", "e", "simpleLabel", "edge", "w", "weight", "minlen", "label", "setEdge", "Math", "max", "multigraph", "isMultigraph", "children", "length", "weightMap", "map", "sucs", "outEdges", "zipObject", "preds", "inEdges", "rect", "point", "x", "y", "dx", "dy", "width", "h", "height", "Error", "sx", "sy", "abs", "layering", "range", "rank", "isUndefined", "order", "min", "has", "offset", "layers", "push", "delta", "nodeRankFactor", "vs", "i", "prefix", "arguments", "collection", "fn", "result", "lhs", "rhs", "value", "start", "now", "console", "log"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/util.js"], "sourcesContent": ["/* eslint \"no-console\": off */\n\n\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar Graph = require(\"./graphlib\").Graph;\n\nmodule.exports = {\n  addDummyNode: addDummyNode,\n  simplify: simplify,\n  asNonCompoundGraph: asNonCompoundGraph,\n  successorWeights: successorWeights,\n  predecessorWeights: predecessorWeights,\n  intersectRect: intersectRect,\n  buildLayerMatrix: buildLayerMatrix,\n  normalizeRanks: normalizeRanks,\n  removeEmptyRanks: removeEmptyRanks,\n  addBorderNode: addBorderNode,\n  maxRank: maxRank,\n  partition: partition,\n  time: time,\n  notime: notime\n};\n\n/*\n * Adds a dummy node to the graph and return v.\n */\nfunction addDummyNode(g, type, attrs, name) {\n  var v;\n  do {\n    v = _.uniqueId(name);\n  } while (g.hasNode(v));\n\n  attrs.dummy = type;\n  g.setNode(v, attrs);\n  return v;\n}\n\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nfunction simplify(g) {\n  var simplified = new Graph().setGraph(g.graph());\n  _.forEach(g.nodes(), function(v) { simplified.setNode(v, g.node(v)); });\n  _.forEach(g.edges(), function(e) {\n    var simpleLabel = simplified.edge(e.v, e.w) || { weight: 0, minlen: 1 };\n    var label = g.edge(e);\n    simplified.setEdge(e.v, e.w, {\n      weight: simpleLabel.weight + label.weight,\n      minlen: Math.max(simpleLabel.minlen, label.minlen)\n    });\n  });\n  return simplified;\n}\n\nfunction asNonCompoundGraph(g) {\n  var simplified = new Graph({ multigraph: g.isMultigraph() }).setGraph(g.graph());\n  _.forEach(g.nodes(), function(v) {\n    if (!g.children(v).length) {\n      simplified.setNode(v, g.node(v));\n    }\n  });\n  _.forEach(g.edges(), function(e) {\n    simplified.setEdge(e, g.edge(e));\n  });\n  return simplified;\n}\n\nfunction successorWeights(g) {\n  var weightMap = _.map(g.nodes(), function(v) {\n    var sucs = {};\n    _.forEach(g.outEdges(v), function(e) {\n      sucs[e.w] = (sucs[e.w] || 0) + g.edge(e).weight;\n    });\n    return sucs;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\nfunction predecessorWeights(g) {\n  var weightMap = _.map(g.nodes(), function(v) {\n    var preds = {};\n    _.forEach(g.inEdges(v), function(e) {\n      preds[e.v] = (preds[e.v] || 0) + g.edge(e).weight;\n    });\n    return preds;\n  });\n  return _.zipObject(g.nodes(), weightMap);\n}\n\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nfunction intersectRect(rect, point) {\n  var x = rect.x;\n  var y = rect.y;\n\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  var dx = point.x - x;\n  var dy = point.y - y;\n  var w = rect.width / 2;\n  var h = rect.height / 2;\n\n  if (!dx && !dy) {\n    throw new Error(\"Not possible to find intersection inside of the rectangle\");\n  }\n\n  var sx, sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = h * dx / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = w * dy / dx;\n  }\n\n  return { x: x + sx, y: y + sy };\n}\n\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * function will produce a matrix with the ids of each node.\n */\nfunction buildLayerMatrix(g) {\n  var layering = _.map(_.range(maxRank(g) + 1), function() { return []; });\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    var rank = node.rank;\n    if (!_.isUndefined(rank)) {\n      layering[rank][node.order] = v;\n    }\n  });\n  return layering;\n}\n\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nfunction normalizeRanks(g) {\n  var min = _.min(_.map(g.nodes(), function(v) { return g.node(v).rank; }));\n  _.forEach(g.nodes(), function(v) {\n    var node = g.node(v);\n    if (_.has(node, \"rank\")) {\n      node.rank -= min;\n    }\n  });\n}\n\nfunction removeEmptyRanks(g) {\n  // Ranks may not start at 0, so we need to offset them\n  var offset = _.min(_.map(g.nodes(), function(v) { return g.node(v).rank; }));\n\n  var layers = [];\n  _.forEach(g.nodes(), function(v) {\n    var rank = g.node(v).rank - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v);\n  });\n\n  var delta = 0;\n  var nodeRankFactor = g.graph().nodeRankFactor;\n  _.forEach(layers, function(vs, i) {\n    if (_.isUndefined(vs) && i % nodeRankFactor !== 0) {\n      --delta;\n    } else if (delta) {\n      _.forEach(vs, function(v) { g.node(v).rank += delta; });\n    }\n  });\n}\n\nfunction addBorderNode(g, prefix, rank, order) {\n  var node = {\n    width: 0,\n    height: 0\n  };\n  if (arguments.length >= 4) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, \"border\", node, prefix);\n}\n\nfunction maxRank(g) {\n  return _.max(_.map(g.nodes(), function(v) {\n    var rank = g.node(v).rank;\n    if (!_.isUndefined(rank)) {\n      return rank;\n    }\n  }));\n}\n\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * function returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nfunction partition(collection, fn) {\n  var result = { lhs: [], rhs: [] };\n  _.forEach(collection, function(value) {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n}\n\n/*\n * Returns a new function that wraps `fn` with a timer. The wrapper logs the\n * time it takes to execute the function.\n */\nfunction time(name, fn) {\n  var start = _.now();\n  try {\n    return fn();\n  } finally {\n    console.log(name + \" time: \" + (_.now() - start) + \"ms\");\n  }\n}\n\nfunction notime(name, fn) {\n  return fn();\n}\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,KAAK,GAAGD,OAAO,CAAC,YAAY,CAAC,CAACC,KAAK;AAEvCC,MAAM,CAACC,OAAO,GAAG;EACfC,YAAY,EAAEA,YAAY;EAC1BC,QAAQ,EAAEA,QAAQ;EAClBC,kBAAkB,EAAEA,kBAAkB;EACtCC,gBAAgB,EAAEA,gBAAgB;EAClCC,kBAAkB,EAAEA,kBAAkB;EACtCC,aAAa,EAAEA,aAAa;EAC5BC,gBAAgB,EAAEA,gBAAgB;EAClCC,cAAc,EAAEA,cAAc;EAC9BC,gBAAgB,EAAEA,gBAAgB;EAClCC,aAAa,EAAEA,aAAa;EAC5BC,OAAO,EAAEA,OAAO;EAChBC,SAAS,EAAEA,SAAS;EACpBC,IAAI,EAAEA,IAAI;EACVC,MAAM,EAAEA;AACV,CAAC;;AAED;AACA;AACA;AACA,SAASb,YAAYA,CAACc,CAAC,EAAEC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC1C,IAAIC,CAAC;EACL,GAAG;IACDA,CAAC,GAAGvB,CAAC,CAACwB,QAAQ,CAACF,IAAI,CAAC;EACtB,CAAC,QAAQH,CAAC,CAACM,OAAO,CAACF,CAAC,CAAC;EAErBF,KAAK,CAACK,KAAK,GAAGN,IAAI;EAClBD,CAAC,CAACQ,OAAO,CAACJ,CAAC,EAAEF,KAAK,CAAC;EACnB,OAAOE,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA,SAASjB,QAAQA,CAACa,CAAC,EAAE;EACnB,IAAIS,UAAU,GAAG,IAAI1B,KAAK,CAAC,CAAC,CAAC2B,QAAQ,CAACV,CAAC,CAACW,KAAK,CAAC,CAAC,CAAC;EAChD9B,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAAEK,UAAU,CAACD,OAAO,CAACJ,CAAC,EAAEJ,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;EACvEvB,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACe,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,WAAW,GAAGR,UAAU,CAACS,IAAI,CAACF,CAAC,CAACZ,CAAC,EAAEY,CAAC,CAACG,CAAC,CAAC,IAAI;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;IACvE,IAAIC,KAAK,GAAGtB,CAAC,CAACkB,IAAI,CAACF,CAAC,CAAC;IACrBP,UAAU,CAACc,OAAO,CAACP,CAAC,CAACZ,CAAC,EAAEY,CAAC,CAACG,CAAC,EAAE;MAC3BC,MAAM,EAAEH,WAAW,CAACG,MAAM,GAAGE,KAAK,CAACF,MAAM;MACzCC,MAAM,EAAEG,IAAI,CAACC,GAAG,CAACR,WAAW,CAACI,MAAM,EAAEC,KAAK,CAACD,MAAM;IACnD,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOZ,UAAU;AACnB;AAEA,SAASrB,kBAAkBA,CAACY,CAAC,EAAE;EAC7B,IAAIS,UAAU,GAAG,IAAI1B,KAAK,CAAC;IAAE2C,UAAU,EAAE1B,CAAC,CAAC2B,YAAY,CAAC;EAAE,CAAC,CAAC,CAACjB,QAAQ,CAACV,CAAC,CAACW,KAAK,CAAC,CAAC,CAAC;EAChF9B,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAC/B,IAAI,CAACJ,CAAC,CAAC4B,QAAQ,CAACxB,CAAC,CAAC,CAACyB,MAAM,EAAE;MACzBpB,UAAU,CAACD,OAAO,CAACJ,CAAC,EAAEJ,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC,CAAC;IAClC;EACF,CAAC,CAAC;EACFvB,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACe,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/BP,UAAU,CAACc,OAAO,CAACP,CAAC,EAAEhB,CAAC,CAACkB,IAAI,CAACF,CAAC,CAAC,CAAC;EAClC,CAAC,CAAC;EACF,OAAOP,UAAU;AACnB;AAEA,SAASpB,gBAAgBA,CAACW,CAAC,EAAE;EAC3B,IAAI8B,SAAS,GAAGjD,CAAC,CAACkD,GAAG,CAAC/B,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAC3C,IAAI4B,IAAI,GAAG,CAAC,CAAC;IACbnD,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACiC,QAAQ,CAAC7B,CAAC,CAAC,EAAE,UAASY,CAAC,EAAE;MACnCgB,IAAI,CAAChB,CAAC,CAACG,CAAC,CAAC,GAAG,CAACa,IAAI,CAAChB,CAAC,CAACG,CAAC,CAAC,IAAI,CAAC,IAAInB,CAAC,CAACkB,IAAI,CAACF,CAAC,CAAC,CAACI,MAAM;IACjD,CAAC,CAAC;IACF,OAAOY,IAAI;EACb,CAAC,CAAC;EACF,OAAOnD,CAAC,CAACqD,SAAS,CAAClC,CAAC,CAACa,KAAK,CAAC,CAAC,EAAEiB,SAAS,CAAC;AAC1C;AAEA,SAASxC,kBAAkBA,CAACU,CAAC,EAAE;EAC7B,IAAI8B,SAAS,GAAGjD,CAAC,CAACkD,GAAG,CAAC/B,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAC3C,IAAI+B,KAAK,GAAG,CAAC,CAAC;IACdtD,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACoC,OAAO,CAAChC,CAAC,CAAC,EAAE,UAASY,CAAC,EAAE;MAClCmB,KAAK,CAACnB,CAAC,CAACZ,CAAC,CAAC,GAAG,CAAC+B,KAAK,CAACnB,CAAC,CAACZ,CAAC,CAAC,IAAI,CAAC,IAAIJ,CAAC,CAACkB,IAAI,CAACF,CAAC,CAAC,CAACI,MAAM;IACnD,CAAC,CAAC;IACF,OAAOe,KAAK;EACd,CAAC,CAAC;EACF,OAAOtD,CAAC,CAACqD,SAAS,CAAClC,CAAC,CAACa,KAAK,CAAC,CAAC,EAAEiB,SAAS,CAAC;AAC1C;;AAEA;AACA;AACA;AACA;AACA,SAASvC,aAAaA,CAAC8C,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAIC,CAAC,GAAGF,IAAI,CAACE,CAAC;EACd,IAAIC,CAAC,GAAGH,IAAI,CAACG,CAAC;;EAEd;EACA;EACA,IAAIC,EAAE,GAAGH,KAAK,CAACC,CAAC,GAAGA,CAAC;EACpB,IAAIG,EAAE,GAAGJ,KAAK,CAACE,CAAC,GAAGA,CAAC;EACpB,IAAIrB,CAAC,GAAGkB,IAAI,CAACM,KAAK,GAAG,CAAC;EACtB,IAAIC,CAAC,GAAGP,IAAI,CAACQ,MAAM,GAAG,CAAC;EAEvB,IAAI,CAACJ,EAAE,IAAI,CAACC,EAAE,EAAE;IACd,MAAM,IAAII,KAAK,CAAC,2DAA2D,CAAC;EAC9E;EAEA,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIxB,IAAI,CAACyB,GAAG,CAACP,EAAE,CAAC,GAAGvB,CAAC,GAAGK,IAAI,CAACyB,GAAG,CAACR,EAAE,CAAC,GAAGG,CAAC,EAAE;IACvC;IACA,IAAIF,EAAE,GAAG,CAAC,EAAE;MACVE,CAAC,GAAG,CAACA,CAAC;IACR;IACAG,EAAE,GAAGH,CAAC,GAAGH,EAAE,GAAGC,EAAE;IAChBM,EAAE,GAAGJ,CAAC;EACR,CAAC,MAAM;IACL;IACA,IAAIH,EAAE,GAAG,CAAC,EAAE;MACVtB,CAAC,GAAG,CAACA,CAAC;IACR;IACA4B,EAAE,GAAG5B,CAAC;IACN6B,EAAE,GAAG7B,CAAC,GAAGuB,EAAE,GAAGD,EAAE;EAClB;EAEA,OAAO;IAAEF,CAAC,EAAEA,CAAC,GAAGQ,EAAE;IAAEP,CAAC,EAAEA,CAAC,GAAGQ;EAAG,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA,SAASxD,gBAAgBA,CAACQ,CAAC,EAAE;EAC3B,IAAIkD,QAAQ,GAAGrE,CAAC,CAACkD,GAAG,CAAClD,CAAC,CAACsE,KAAK,CAACvD,OAAO,CAACI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,YAAW;IAAE,OAAO,EAAE;EAAE,CAAC,CAAC;EACxEnB,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAC/B,IAAIU,IAAI,GAAGd,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC;IACpB,IAAIgD,IAAI,GAAGtC,IAAI,CAACsC,IAAI;IACpB,IAAI,CAACvE,CAAC,CAACwE,WAAW,CAACD,IAAI,CAAC,EAAE;MACxBF,QAAQ,CAACE,IAAI,CAAC,CAACtC,IAAI,CAACwC,KAAK,CAAC,GAAGlD,CAAC;IAChC;EACF,CAAC,CAAC;EACF,OAAO8C,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA,SAASzD,cAAcA,CAACO,CAAC,EAAE;EACzB,IAAIuD,GAAG,GAAG1E,CAAC,CAAC0E,GAAG,CAAC1E,CAAC,CAACkD,GAAG,CAAC/B,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAAE,OAAOJ,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC,CAACgD,IAAI;EAAE,CAAC,CAAC,CAAC;EACzEvE,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAC/B,IAAIU,IAAI,GAAGd,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC;IACpB,IAAIvB,CAAC,CAAC2E,GAAG,CAAC1C,IAAI,EAAE,MAAM,CAAC,EAAE;MACvBA,IAAI,CAACsC,IAAI,IAAIG,GAAG;IAClB;EACF,CAAC,CAAC;AACJ;AAEA,SAAS7D,gBAAgBA,CAACM,CAAC,EAAE;EAC3B;EACA,IAAIyD,MAAM,GAAG5E,CAAC,CAAC0E,GAAG,CAAC1E,CAAC,CAACkD,GAAG,CAAC/B,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAAE,OAAOJ,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC,CAACgD,IAAI;EAAE,CAAC,CAAC,CAAC;EAE5E,IAAIM,MAAM,GAAG,EAAE;EACf7E,CAAC,CAAC+B,OAAO,CAACZ,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IAC/B,IAAIgD,IAAI,GAAGpD,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC,CAACgD,IAAI,GAAGK,MAAM;IAClC,IAAI,CAACC,MAAM,CAACN,IAAI,CAAC,EAAE;MACjBM,MAAM,CAACN,IAAI,CAAC,GAAG,EAAE;IACnB;IACAM,MAAM,CAACN,IAAI,CAAC,CAACO,IAAI,CAACvD,CAAC,CAAC;EACtB,CAAC,CAAC;EAEF,IAAIwD,KAAK,GAAG,CAAC;EACb,IAAIC,cAAc,GAAG7D,CAAC,CAACW,KAAK,CAAC,CAAC,CAACkD,cAAc;EAC7ChF,CAAC,CAAC+B,OAAO,CAAC8C,MAAM,EAAE,UAASI,EAAE,EAAEC,CAAC,EAAE;IAChC,IAAIlF,CAAC,CAACwE,WAAW,CAACS,EAAE,CAAC,IAAIC,CAAC,GAAGF,cAAc,KAAK,CAAC,EAAE;MACjD,EAAED,KAAK;IACT,CAAC,MAAM,IAAIA,KAAK,EAAE;MAChB/E,CAAC,CAAC+B,OAAO,CAACkD,EAAE,EAAE,UAAS1D,CAAC,EAAE;QAAEJ,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC,CAACgD,IAAI,IAAIQ,KAAK;MAAE,CAAC,CAAC;IACzD;EACF,CAAC,CAAC;AACJ;AAEA,SAASjE,aAAaA,CAACK,CAAC,EAAEgE,MAAM,EAAEZ,IAAI,EAAEE,KAAK,EAAE;EAC7C,IAAIxC,IAAI,GAAG;IACT6B,KAAK,EAAE,CAAC;IACRE,MAAM,EAAE;EACV,CAAC;EACD,IAAIoB,SAAS,CAACpC,MAAM,IAAI,CAAC,EAAE;IACzBf,IAAI,CAACsC,IAAI,GAAGA,IAAI;IAChBtC,IAAI,CAACwC,KAAK,GAAGA,KAAK;EACpB;EACA,OAAOpE,YAAY,CAACc,CAAC,EAAE,QAAQ,EAAEc,IAAI,EAAEkD,MAAM,CAAC;AAChD;AAEA,SAASpE,OAAOA,CAACI,CAAC,EAAE;EAClB,OAAOnB,CAAC,CAAC4C,GAAG,CAAC5C,CAAC,CAACkD,GAAG,CAAC/B,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAAST,CAAC,EAAE;IACxC,IAAIgD,IAAI,GAAGpD,CAAC,CAACc,IAAI,CAACV,CAAC,CAAC,CAACgD,IAAI;IACzB,IAAI,CAACvE,CAAC,CAACwE,WAAW,CAACD,IAAI,CAAC,EAAE;MACxB,OAAOA,IAAI;IACb;EACF,CAAC,CAAC,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASvD,SAASA,CAACqE,UAAU,EAAEC,EAAE,EAAE;EACjC,IAAIC,MAAM,GAAG;IAAEC,GAAG,EAAE,EAAE;IAAEC,GAAG,EAAE;EAAG,CAAC;EACjCzF,CAAC,CAAC+B,OAAO,CAACsD,UAAU,EAAE,UAASK,KAAK,EAAE;IACpC,IAAIJ,EAAE,CAACI,KAAK,CAAC,EAAE;MACbH,MAAM,CAACC,GAAG,CAACV,IAAI,CAACY,KAAK,CAAC;IACxB,CAAC,MAAM;MACLH,MAAM,CAACE,GAAG,CAACX,IAAI,CAACY,KAAK,CAAC;IACxB;EACF,CAAC,CAAC;EACF,OAAOH,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,SAAStE,IAAIA,CAACK,IAAI,EAAEgE,EAAE,EAAE;EACtB,IAAIK,KAAK,GAAG3F,CAAC,CAAC4F,GAAG,CAAC,CAAC;EACnB,IAAI;IACF,OAAON,EAAE,CAAC,CAAC;EACb,CAAC,SAAS;IACRO,OAAO,CAACC,GAAG,CAACxE,IAAI,GAAG,SAAS,IAAItB,CAAC,CAAC4F,GAAG,CAAC,CAAC,GAAGD,KAAK,CAAC,GAAG,IAAI,CAAC;EAC1D;AACF;AAEA,SAASzE,MAAMA,CAACI,IAAI,EAAEgE,EAAE,EAAE;EACxB,OAAOA,EAAE,CAAC,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}