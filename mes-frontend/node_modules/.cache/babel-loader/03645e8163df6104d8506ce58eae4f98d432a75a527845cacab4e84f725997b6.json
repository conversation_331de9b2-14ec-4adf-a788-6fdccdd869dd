{"ast": null, "code": "import { __assign, __extends, __read, __rest, __spreadArray } from \"tslib\";\nimport { clamp, omit } from '@antv/util';\nimport { Component } from '../../core';\nimport { Group, Path, Rect } from '../../shapes';\nimport { deepAssign, parseSeriesAttr, select, subStyleProps } from '../../util';\nimport { Indicator } from '../indicator';\nimport { Select } from '../select';\nvar IconBase = /** @class */function (_super) {\n  __extends(IconBase, _super);\n  function IconBase(options) {\n    var _this = _super.call(this, deepAssign({}, {\n      style: {\n        backgroundOpacity: IconBase.backgroundOpacities.default\n      }\n    }, IconBase.defaultOptions, options)) || this;\n    /** hover 时是否显示背景 */\n    _this.showBackground = true;\n    _this.background = _this.appendChild(new Rect({}));\n    _this.icon = _this.appendChild(new Group({}));\n    return _this;\n  }\n  Object.defineProperty(IconBase.prototype, \"label\", {\n    get: function () {\n      return 'BaseIcon';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(IconBase.prototype, \"lineWidth\", {\n    get: function () {\n      return Math.log10(this.attributes.size);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(IconBase.prototype, \"padding\", {\n    get: function () {\n      return parseSeriesAttr(this.attributes.size / 5);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(IconBase.prototype, \"iconSize\", {\n    get: function () {\n      var size = this.attributes.size;\n      var _a = __read(this.padding, 4),\n        top = _a[0],\n        right = _a[1],\n        bottom = _a[2],\n        left = _a[3];\n      return Math.max(size - Math.max(left + right, top + bottom), this.lineWidth * 2 + 1);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  IconBase.prototype.renderBackground = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      size = _a.size;\n    var halfSize = size / 2;\n    var backgroundStyle = subStyleProps(this.attributes, 'background');\n    this.background.attr(__assign({\n      x: x - halfSize,\n      y: y - halfSize,\n      width: size,\n      height: size\n    }, backgroundStyle));\n  };\n  IconBase.prototype.showIndicator = function () {\n    if (!this.label) return;\n    var size = this.attributes.size;\n    var _a = this.background.getBBox(),\n      x = _a.x,\n      y = _a.y;\n    this.indicator.update({\n      x: x + size / 2,\n      y: y - 5,\n      labelText: this.label,\n      visibility: 'visible'\n    });\n  };\n  IconBase.prototype.hideIndicator = function () {\n    this.indicator.update({\n      visibility: 'hidden'\n    });\n  };\n  IconBase.prototype.connectedCallback = function () {\n    var _a;\n    _super.prototype.connectedCallback.call(this);\n    // indicator 脱离文档流，需要手动添加到 canvas\n    var size = this.attributes.size;\n    var _b = this.background.getBBox(),\n      x = _b.x,\n      y = _b.y;\n    var canvas = (_a = this.ownerDocument) === null || _a === void 0 ? void 0 : _a.defaultView;\n    if (canvas) {\n      this.indicator = canvas.appendChild(new Indicator({\n        style: {\n          x: x + size / 2,\n          y: y - size / 2,\n          visibility: 'hidden',\n          position: 'top',\n          radius: 3,\n          zIndex: 100\n        }\n      }));\n    }\n  };\n  IconBase.prototype.disconnectedCallback = function () {\n    this.indicator.destroy();\n  };\n  IconBase.prototype.render = function () {\n    this.renderIcon();\n    if (this.showBackground) this.renderBackground();\n  };\n  IconBase.prototype.bindEvents = function () {\n    var _this = this;\n    var onClick = this.attributes.onClick;\n    this.addEventListener('click', function () {\n      onClick === null || onClick === void 0 ? void 0 : onClick(_this);\n    });\n    if (this.showBackground) {\n      var resetBackground_1 = function () {\n        return _this.background.attr({\n          opacity: IconBase.backgroundOpacities.default\n        });\n      };\n      var hoverBackground_1 = function () {\n        return _this.background.attr({\n          opacity: IconBase.backgroundOpacities.hover\n        });\n      };\n      var activeBackground_1 = function () {\n        return _this.background.attr({\n          opacity: IconBase.backgroundOpacities.active\n        });\n      };\n      this.addEventListener('pointerenter', function () {\n        hoverBackground_1();\n        _this.showIndicator();\n      });\n      this.addEventListener('pointerleave', function () {\n        resetBackground_1();\n        _this.hideIndicator();\n      });\n      this.addEventListener('pointerdown', function () {\n        activeBackground_1();\n      });\n      this.addEventListener('pointerup', function () {\n        resetBackground_1();\n      });\n    }\n  };\n  IconBase.tag = 'IconBase';\n  IconBase.defaultOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      size: 10,\n      color: '#565758',\n      backgroundRadius: 4,\n      backgroundFill: '#e2e2e2'\n    }\n  };\n  IconBase.backgroundOpacities = {\n    default: 0,\n    hover: 0.8,\n    active: 1\n  };\n  return IconBase;\n}(Component);\nexport { IconBase };\nvar arrow = function (size, color) {\n  if (color === void 0) {\n    color = '#565758';\n  }\n  return new Path({\n    style: {\n      fill: color,\n      d: \"M \".concat(size, \",\").concat(size, \" L -\").concat(size, \",0 L \").concat(size, \",-\").concat(size, \" Z\"),\n      transformOrigin: 'center'\n    }\n  });\n};\n/** 重置 */\nvar Reset = /** @class */function (_super) {\n  __extends(Reset, _super);\n  function Reset() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Reset.prototype.arcPath = function (cx, cy, radius) {\n    var _a = __read([radius, radius], 2),\n      rx = _a[0],\n      ry = _a[1];\n    var getPosByAngle = function (angle) {\n      return [cx + radius * Math.cos(angle), cy + radius * Math.sin(angle)];\n    };\n    var _b = __read(getPosByAngle(-5 / 4 * Math.PI), 2),\n      x1 = _b[0],\n      y1 = _b[1];\n    var _c = __read(getPosByAngle(1 / 4 * Math.PI), 2),\n      x2 = _c[0],\n      y2 = _c[1];\n    return \"M\".concat(x1, \",\").concat(y1, \",A\").concat(rx, \",\").concat(ry, \",0,1,1,\").concat(x2, \",\").concat(y2);\n  };\n  Object.defineProperty(Reset.prototype, \"label\", {\n    get: function () {\n      return '重置';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Reset.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var size = this.iconSize;\n    var lineWidth = this.lineWidth;\n    var arrowSize = lineWidth + 0.5;\n    select(this.icon).maybeAppend('.reset', 'path').styles({\n      stroke: color,\n      lineWidth: lineWidth,\n      d: this.arcPath(x, y, size / 2 - lineWidth),\n      markerStart: arrow(arrowSize, color)\n    });\n  };\n  return Reset;\n}(IconBase);\nexport { Reset };\n/** 快退 */\nvar Backward = /** @class */function (_super) {\n  __extends(Backward, _super);\n  function Backward() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(Backward.prototype, \"label\", {\n    get: function () {\n      return '快退';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Backward.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var size = this.iconSize;\n    var deltaX = size / 2;\n    var deltaY = size / 2 / Math.pow(3, 0.5);\n    var points = [[x, y], [x, y - deltaY], [x - deltaX, y], [x, y + deltaY], [x, y], [x + deltaX, y - deltaY], [x + deltaX, y + deltaY], [x, y]];\n    select(this.icon).maybeAppend('.backward', 'polygon').styles({\n      points: points,\n      fill: color\n    });\n  };\n  return Backward;\n}(IconBase);\nexport { Backward };\n/** 快进 */\nvar Forward = /** @class */function (_super) {\n  __extends(Forward, _super);\n  function Forward() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(Forward.prototype, \"label\", {\n    get: function () {\n      return '快进';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Forward.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var size = this.iconSize;\n    var deltaX = size / 2;\n    var deltaY = size / 2 / Math.pow(3, 0.5);\n    var points = [[x, y], [x, y - deltaY], [x + deltaX, y], [x, y + deltaY], [x, y], [x - deltaX, y - deltaY], [x - deltaX, y + deltaY], [x, y]];\n    select(this.icon).maybeAppend('.forward', 'polygon').styles({\n      points: points,\n      fill: color\n    });\n  };\n  return Forward;\n}(IconBase);\nexport { Forward };\nvar Play = /** @class */function (_super) {\n  __extends(Play, _super);\n  function Play() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(Play.prototype, \"label\", {\n    get: function () {\n      return '播放';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Play.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var size = this.iconSize;\n    var deltaX = size / 3 * Math.pow(3, 0.5) * 0.8;\n    var points = [[x + deltaX, y], [x - deltaX / 2, y - size / 2 * 0.8], [x - deltaX / 2, y + size / 2 * 0.8], [x + deltaX, y]];\n    select(this.icon).maybeAppend('.play', 'polygon').styles({\n      points: points,\n      fill: color\n    });\n  };\n  return Play;\n}(IconBase);\nexport { Play };\nvar Pause = /** @class */function (_super) {\n  __extends(Pause, _super);\n  function Pause() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(Pause.prototype, \"label\", {\n    get: function () {\n      return '暂停';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Pause.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var size = this.iconSize;\n    var deltaX = size / 3;\n    var points = [[x - deltaX, y - size / 2], [x - deltaX, y + size / 2], [x - deltaX / 2, y + size / 2], [x - deltaX / 2, y - size / 2], [x - deltaX, y - size / 2], [x + deltaX / 2, y - size / 2], [x + deltaX / 2, y + size / 2], [x + deltaX, y + size / 2], [x + deltaX, y - size / 2]];\n    select(this.icon).maybeAppend('.pause', 'polygon').styles({\n      points: points,\n      fill: color\n    });\n  };\n  return Pause;\n}(IconBase);\nexport { Pause };\n/** 时间范围 */\nvar Range = /** @class */function (_super) {\n  __extends(Range, _super);\n  function Range() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(Range.prototype, \"label\", {\n    get: function () {\n      return '范围时间';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Range.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var _b = this,\n      size = _b.iconSize,\n      lineWidth = _b.lineWidth;\n    var gap = lineWidth;\n    select(this.icon).maybeAppend('.left-line', 'line').styles({\n      x1: x - size / 2,\n      y1: y - size / 2,\n      x2: x - size / 2,\n      y2: y + size / 2,\n      stroke: color,\n      lineWidth: lineWidth\n    });\n    select(this.icon).maybeAppend('.right-line', 'line').styles({\n      x1: x + size / 2,\n      y1: y - size / 2,\n      x2: x + size / 2,\n      y2: y + size / 2,\n      stroke: color,\n      lineWidth: lineWidth\n    });\n    select(this.icon).maybeAppend('.left-arrow', 'line').styles({\n      x1: x,\n      y1: y,\n      x2: x - size / 2 + gap * 2,\n      y2: y,\n      stroke: color,\n      lineWidth: lineWidth,\n      markerEnd: arrow(lineWidth * 2, color)\n    });\n    select(this.icon).maybeAppend('.right-arrow', 'line').styles({\n      x1: x,\n      y1: y,\n      x2: x + size / 2 - gap * 2,\n      y2: y,\n      stroke: color,\n      lineWidth: lineWidth,\n      markerEnd: arrow(lineWidth * 2, color)\n    });\n  };\n  return Range;\n}(IconBase);\nexport { Range };\n/** 值范围 */\nvar Value = /** @class */function (_super) {\n  __extends(Value, _super);\n  function Value() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(Value.prototype, \"label\", {\n    get: function () {\n      return '单一时间';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Value.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var _b = this,\n      size = _b.iconSize,\n      lineWidth = _b.lineWidth;\n    select(this.icon).maybeAppend('.line', 'line').styles({\n      x1: x,\n      y1: y - size / 2,\n      x2: x,\n      y2: y + size / 2,\n      stroke: color,\n      lineWidth: lineWidth\n    });\n    var gap = lineWidth;\n    select(this.icon).maybeAppend('.left-arrow', 'line').styles({\n      x1: x - size / 2 - gap * 2,\n      y1: y,\n      x2: x - gap * 2,\n      y2: y,\n      stroke: color,\n      lineWidth: lineWidth,\n      markerEnd: arrow(lineWidth * 2, color)\n    });\n    select(this.icon).maybeAppend('.right-arrow', 'line').styles({\n      x1: x + size / 2 + gap * 2,\n      y1: y,\n      x2: x + gap * 2,\n      y2: y,\n      stroke: color,\n      lineWidth: lineWidth,\n      markerEnd: arrow(lineWidth * 2, color)\n    });\n  };\n  return Value;\n}(IconBase);\nexport { Value };\nvar getCoordinatePoints = function (size) {\n  return [[-size / 2, -size / 2], [-size / 2, size / 2], [size / 2, size / 2]];\n};\nvar LineChart = /** @class */function (_super) {\n  __extends(LineChart, _super);\n  function LineChart() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(LineChart.prototype, \"label\", {\n    get: function () {\n      return '折线图';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  LineChart.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var _b = this,\n      size = _b.iconSize,\n      lineWidth = _b.lineWidth;\n    var gap = lineWidth;\n    var deltaX = (size - gap * 2 - lineWidth) / 4;\n    var deltaY = (size - gap * 2 - lineWidth) / 2;\n    var _c = __read([x - size / 2 + gap, y + size / 2 - gap * 2], 2),\n      ox = _c[0],\n      oy = _c[1];\n    select(this.icon).maybeAppend('.coordinate', 'polyline').styles({\n      points: getCoordinatePoints(size).map(function (_a) {\n        var _b = __read(_a, 2),\n          px = _b[0],\n          py = _b[1];\n        return [px + x, py + y];\n      }),\n      stroke: color,\n      lineWidth: lineWidth\n    });\n    select(this.icon).maybeAppend('.line', 'polyline').styles({\n      points: [[ox, oy], [ox + deltaX, oy - deltaY], [ox + deltaX * 2, oy], [ox + deltaX * 4, oy - deltaY * 2]],\n      stroke: color,\n      lineWidth: lineWidth\n    });\n  };\n  return LineChart;\n}(IconBase);\nexport { LineChart };\nvar BarChart = /** @class */function (_super) {\n  __extends(BarChart, _super);\n  function BarChart() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  Object.defineProperty(BarChart.prototype, \"label\", {\n    get: function () {\n      return '条形图';\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(BarChart.prototype, \"data\", {\n    get: function () {\n      return [1, 4, 2, 4, 3];\n    },\n    enumerable: false,\n    configurable: true\n  });\n  BarChart.prototype.renderIcon = function () {\n    var data = this.data;\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var _b = this,\n      size = _b.iconSize,\n      lineWidth = _b.lineWidth;\n    var gap = lineWidth;\n    var deltaX = (size - gap) / data.length;\n    var deltaY = (size - gap * 2) / 4;\n    var _c = __read([x - size / 2 + gap * 2, y + size / 2 - gap], 2),\n      ox = _c[0],\n      oy = _c[1];\n    select(this.icon).maybeAppend('.coordinate', 'polyline').styles({\n      points: getCoordinatePoints(size).map(function (_a) {\n        var _b = __read(_a, 2),\n          px = _b[0],\n          py = _b[1];\n        return [px + x, py + y];\n      }),\n      stroke: color,\n      lineWidth: lineWidth\n    });\n    select(this.icon).maybeAppend('.bars', 'g').selectAll('.column').data(this.data.map(function (value, index) {\n      return {\n        value: value,\n        index: index\n      };\n    })).join(function (enter) {\n      return enter.append('line').attr('className', 'column').style('x1', function (_a) {\n        var index = _a.index;\n        return ox + deltaX * index;\n      }).style('y1', oy).style('x2', function (_a) {\n        var index = _a.index;\n        return ox + deltaX * index;\n      }).style('y2', function (_a) {\n        var value = _a.value;\n        return oy - deltaY * value;\n      }).styles({\n        y1: oy,\n        stroke: color,\n        lineWidth: lineWidth\n      });\n    });\n  };\n  return BarChart;\n}(IconBase);\nexport { BarChart };\n/** 分割线 */\nvar Split = /** @class */function (_super) {\n  __extends(Split, _super);\n  function Split(options) {\n    var _this = _super.call(this, deepAssign({}, {\n      style: {\n        color: '#d8d9d9'\n      }\n    }, options)) || this;\n    _this.showBackground = false;\n    return _this;\n  }\n  Split.prototype.renderIcon = function () {\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      color = _a.color;\n    var _b = this,\n      size = _b.iconSize,\n      lineWidth = _b.lineWidth;\n    select(this.icon).maybeAppend('.split', 'line').styles({\n      x1: x,\n      y1: y - size / 2,\n      x2: x,\n      y2: y + size / 2,\n      stroke: color,\n      lineWidth: lineWidth\n    });\n  };\n  return Split;\n}(IconBase);\nexport { Split };\nvar SpeedSelect = /** @class */function (_super) {\n  __extends(SpeedSelect, _super);\n  function SpeedSelect() {\n    var _this = _super.apply(this, __spreadArray([], __read(arguments), false)) || this;\n    _this.showBackground = false;\n    return _this;\n  }\n  Object.defineProperty(SpeedSelect.prototype, \"padding\", {\n    get: function () {\n      return parseSeriesAttr(0);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  SpeedSelect.prototype.renderIcon = function () {\n    var iconSize = this.iconSize;\n    var _a = this.attributes,\n      x = _a.x,\n      y = _a.y,\n      _b = _a.speed,\n      speed = _b === void 0 ? 1 : _b;\n    var inheritStyle = omit(this.attributes, ['x', 'y', 'transform', 'transformOrigin', 'width', 'height', 'size', 'color', 'speed']);\n    var width = clamp(iconSize, 20, Infinity);\n    var height = 20;\n    var style = __assign(__assign({}, inheritStyle), {\n      x: x - width / 2,\n      y: y - height / 2,\n      width: width,\n      height: height,\n      defaultValue: speed,\n      bordered: false,\n      showDropdownIcon: false,\n      selectRadius: 2,\n      dropdownPadding: this.padding,\n      dropdownRadius: 2,\n      dropdownSpacing: iconSize / 5,\n      placeholderFontSize: iconSize / 2,\n      optionPadding: 0,\n      optionLabelFontSize: iconSize / 2,\n      optionBackgroundRadius: 1,\n      options: [{\n        label: '1x',\n        value: 1\n      }, {\n        label: '1.5x',\n        value: 1.5\n      }, {\n        label: '2x',\n        value: 2\n      }]\n    });\n    select(this.icon).maybeAppend('.speed', function () {\n      return new Select({\n        style: style\n      });\n    }).attr('className', 'speed').each(function () {\n      this.update(style);\n    });\n  };\n  SpeedSelect.tag = 'SpeedSelect';\n  return SpeedSelect;\n}(IconBase);\nexport { SpeedSelect };\nvar ToggleIcon = /** @class */function (_super) {\n  __extends(ToggleIcon, _super);\n  function ToggleIcon(options) {\n    var _this = _super.call(this, options) || this;\n    _this.icon = _this.appendChild(new Group({}));\n    _this.currentType = _this.attributes.type;\n    return _this;\n  }\n  ToggleIcon.prototype.getType = function () {\n    return this.currentType;\n  };\n  ToggleIcon.prototype.render = function () {\n    var _this = this;\n    var _a = this.attributes,\n      onChange = _a.onChange,\n      restStyles = __rest(_a, [\"onChange\"]);\n    select(this.icon).selectAll('.icon').data([this.currentType]).join(function (enter) {\n      return enter.append(function (type) {\n        var _a;\n        var Ctor = (_a = _this.toggles.find(function (_a) {\n          var _b = __read(_a, 1),\n            key = _b[0];\n          return key === type;\n        })) === null || _a === void 0 ? void 0 : _a[1];\n        if (!Ctor) throw new Error(\"Invalid type: \".concat(type));\n        return new Ctor({});\n      }).attr('className', 'icon').styles(restStyles, false).update({});\n    }, function (update) {\n      return update.styles({\n        restStyles: restStyles\n      }).update({});\n    }, function (exit) {\n      return exit.remove();\n    });\n  };\n  ToggleIcon.prototype.bindEvents = function () {\n    var _this = this;\n    var onChange = this.attributes.onChange;\n    this.addEventListener('click', function (e) {\n      e.preventDefault();\n      e.stopPropagation();\n      var nextIndex = (_this.toggles.findIndex(function (_a) {\n        var _b = __read(_a, 1),\n          key = _b[0];\n        return key === _this.currentType;\n      }) + 1) % _this.toggles.length;\n      var nextType = _this.toggles[nextIndex][0];\n      onChange === null || onChange === void 0 ? void 0 : onChange(_this.currentType);\n      _this.currentType = nextType;\n      _this.render();\n    });\n  };\n  ToggleIcon.tag = 'ToggleIcon';\n  return ToggleIcon;\n}(Component);\nexport { ToggleIcon };\nvar PlayPause = /** @class */function (_super) {\n  __extends(PlayPause, _super);\n  function PlayPause(options) {\n    var _this = _super.call(this, deepAssign({}, {\n      style: {\n        type: 'play'\n      }\n    }, options)) || this;\n    _this.toggles = [['play', Play], ['pause', Pause]];\n    return _this;\n  }\n  return PlayPause;\n}(ToggleIcon);\nexport { PlayPause };\nvar SelectionType = /** @class */function (_super) {\n  __extends(SelectionType, _super);\n  function SelectionType(options) {\n    var _this = _super.call(this, deepAssign({}, {\n      style: {\n        type: 'range'\n      }\n    }, options)) || this;\n    _this.toggles = [['range', Range], ['value', Value]];\n    return _this;\n  }\n  return SelectionType;\n}(ToggleIcon);\nexport { SelectionType };\nvar ChartType = /** @class */function (_super) {\n  __extends(ChartType, _super);\n  function ChartType(options) {\n    var _this = _super.call(this, deepAssign({}, {\n      style: {\n        type: 'column'\n      }\n    }, options)) || this;\n    _this.toggles = [['line', LineChart], ['column', BarChart]];\n    return _this;\n  }\n  return ChartType;\n}(ToggleIcon);\nexport { ChartType };", "map": {"version": 3, "names": ["clamp", "omit", "Component", "Group", "Path", "Rect", "deepAssign", "parseSeriesAttr", "select", "subStyleProps", "Indicator", "Select", "IconBase", "_super", "__extends", "options", "_this", "call", "style", "backgroundOpacity", "backgroundOpacities", "default", "defaultOptions", "showBackground", "background", "append<PERSON><PERSON><PERSON>", "icon", "Object", "defineProperty", "prototype", "get", "Math", "log10", "attributes", "size", "_a", "__read", "padding", "top", "right", "bottom", "left", "max", "lineWidth", "renderBackground", "x", "y", "halfSize", "backgroundStyle", "attr", "__assign", "width", "height", "showIndicator", "label", "getBBox", "indicator", "update", "labelText", "visibility", "hideIndicator", "connectedCallback", "_b", "canvas", "ownerDocument", "defaultView", "position", "radius", "zIndex", "disconnectedCallback", "destroy", "render", "renderIcon", "bindEvents", "onClick", "addEventListener", "resetBackground_1", "opacity", "hoverBackground_1", "hover", "activeBackground_1", "active", "tag", "color", "backgroundRadius", "backgroundFill", "arrow", "fill", "d", "concat", "transform<PERSON><PERSON>in", "Reset", "arcPath", "cx", "cy", "rx", "ry", "getPosByAngle", "angle", "cos", "sin", "PI", "x1", "y1", "_c", "x2", "y2", "iconSize", "arrowSize", "maybe<PERSON><PERSON>nd", "styles", "stroke", "markerStart", "Backward", "deltaX", "deltaY", "pow", "points", "Forward", "Play", "Pause", "Range", "gap", "markerEnd", "Value", "getCoordinatePoints", "Line<PERSON>hart", "ox", "oy", "map", "px", "py", "<PERSON><PERSON><PERSON>", "data", "length", "selectAll", "value", "index", "join", "enter", "append", "Split", "SpeedSelect", "speed", "inheritStyle", "Infinity", "defaultValue", "bordered", "showDropdownIcon", "selectRadius", "dropdownPadding", "dropdownRadius", "dropdownSpacing", "placeholderFontSize", "optionPadding", "optionLabelFontSize", "optionBackgroundRadius", "each", "ToggleIcon", "currentType", "type", "getType", "onChange", "restStyles", "__rest", "Ctor", "toggles", "find", "key", "Error", "exit", "remove", "e", "preventDefault", "stopPropagation", "nextIndex", "findIndex", "nextType", "PlayPause", "SelectionType", "ChartType"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/timebar/icons.ts"], "sourcesContent": ["import type { Canvas } from '@antv/g';\nimport { clamp, omit } from '@antv/util';\nimport { ComponentOptions, Component } from '../../core';\nimport type { GroupStyleProps, PolygonStyleProps, RectStyleProps } from '../../shapes';\nimport { Group, Path, Rect } from '../../shapes';\nimport { PrefixObject } from '../../types';\nimport { deepAssign, parseSeriesAttr, select, subStyleProps } from '../../util';\nimport { Indicator } from '../indicator';\nimport type { SelectStyleProps } from '../select';\nimport { Select } from '../select';\n\ntype IconBaseStyleProps = GroupStyleProps &\n  PrefixObject<RectStyleProps, 'background'> & {\n    x?: number;\n    y?: number;\n    size?: number;\n    color?: string;\n    onClick?: (e: IconBase) => void;\n  };\ntype IconBaseOptions = ComponentOptions<IconBaseStyleProps>;\nexport abstract class IconBase<T extends Record<string, any> = {}> extends Component<T & IconBaseStyleProps> {\n  public static tag = 'IconBase';\n\n  static defaultOptions: IconBaseOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      size: 10,\n      color: '#565758',\n      backgroundRadius: 4,\n      backgroundFill: '#e2e2e2',\n    },\n  };\n\n  private static backgroundOpacities = {\n    default: 0,\n    hover: 0.8,\n    active: 1,\n  };\n\n  /** hover 时是否显示背景 */\n  protected showBackground = true;\n\n  protected get label() {\n    return 'BaseIcon';\n  }\n\n  protected indicator!: Indicator;\n\n  protected background = this.appendChild(new Rect({}));\n\n  protected icon = this.appendChild(new Group({}));\n\n  get lineWidth() {\n    return Math.log10(this.attributes.size);\n  }\n\n  protected get padding() {\n    return parseSeriesAttr(this.attributes.size / 5);\n  }\n\n  protected get iconSize() {\n    const { size } = this.attributes;\n    const [top, right, bottom, left] = this.padding;\n    return Math.max(size - Math.max(left + right, top + bottom), this.lineWidth * 2 + 1);\n  }\n\n  protected renderBackground() {\n    const { x, y, size } = this.attributes;\n    const halfSize = size / 2;\n    const backgroundStyle = subStyleProps(this.attributes, 'background');\n    this.background.attr({ x: x - halfSize, y: y - halfSize, width: size, height: size, ...backgroundStyle });\n  }\n\n  protected showIndicator() {\n    if (!this.label) return;\n    const { size } = this.attributes;\n    const { x, y } = this.background.getBBox();\n    this.indicator.update({ x: x + size / 2, y: y - 5, labelText: this.label, visibility: 'visible' });\n  }\n\n  protected hideIndicator() {\n    this.indicator.update({ visibility: 'hidden' });\n  }\n\n  constructor(options: IconBaseOptions) {\n    super(\n      deepAssign(\n        {},\n        { style: { backgroundOpacity: IconBase.backgroundOpacities.default } },\n        IconBase.defaultOptions,\n        options\n      )\n    );\n  }\n\n  connectedCallback(): void {\n    super.connectedCallback();\n    // indicator 脱离文档流，需要手动添加到 canvas\n    const { size } = this.attributes;\n    const { x, y } = this.background.getBBox();\n    const canvas = this.ownerDocument?.defaultView;\n\n    if (canvas) {\n      this.indicator = (canvas as unknown as Canvas).appendChild(\n        new Indicator({\n          style: {\n            x: x + size / 2,\n            y: y - size / 2,\n            visibility: 'hidden',\n            position: 'top',\n            radius: 3,\n            zIndex: 100,\n          },\n        })\n      );\n    }\n  }\n\n  disconnectedCallback() {\n    this.indicator.destroy();\n  }\n\n  render() {\n    this.renderIcon();\n    if (this.showBackground) this.renderBackground();\n  }\n\n  abstract renderIcon(): void;\n\n  public bindEvents(): void {\n    const { onClick } = this.attributes;\n    this.addEventListener('click', () => {\n      onClick?.(this);\n    });\n\n    if (this.showBackground) {\n      const resetBackground = () => this.background.attr({ opacity: IconBase.backgroundOpacities.default });\n      const hoverBackground = () => this.background.attr({ opacity: IconBase.backgroundOpacities.hover });\n      const activeBackground = () => this.background.attr({ opacity: IconBase.backgroundOpacities.active });\n\n      this.addEventListener('pointerenter', () => {\n        hoverBackground();\n        this.showIndicator();\n      });\n      this.addEventListener('pointerleave', () => {\n        resetBackground();\n        this.hideIndicator();\n      });\n      this.addEventListener('pointerdown', () => {\n        activeBackground();\n      });\n      this.addEventListener('pointerup', () => {\n        resetBackground();\n      });\n    }\n  }\n}\n\nconst arrow = (size: number, color: string = '#565758') => {\n  return new Path({\n    style: {\n      fill: color,\n      d: `M ${size},${size} L -${size},0 L ${size},-${size} Z`,\n      transformOrigin: 'center',\n    },\n  });\n};\n\n/** 重置 */\nexport class Reset extends IconBase {\n  private arcPath(cx: number, cy: number, radius: number) {\n    const [rx, ry] = [radius, radius];\n    const getPosByAngle = (angle: number) => [cx + radius * Math.cos(angle), cy + radius * Math.sin(angle)];\n    const [x1, y1] = getPosByAngle((-5 / 4) * Math.PI);\n    const [x2, y2] = getPosByAngle((1 / 4) * Math.PI);\n    return `M${x1},${y1},A${rx},${ry},0,1,1,${x2},${y2}`;\n  }\n\n  protected get label() {\n    return '重置';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const size = this.iconSize;\n    const { lineWidth } = this;\n    const arrowSize = lineWidth + 0.5;\n\n    select(this.icon)\n      .maybeAppend('.reset', 'path')\n      .styles({\n        stroke: color,\n        lineWidth,\n        d: this.arcPath(x, y, size / 2 - lineWidth),\n        markerStart: arrow(arrowSize, color),\n      });\n  }\n}\n\n/** 快退 */\nexport class Backward extends IconBase {\n  protected get label() {\n    return '快退';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const size = this.iconSize;\n    const deltaX = size / 2;\n    const deltaY = size / 2 / 3 ** 0.5;\n    const points: PolygonStyleProps['points'] = [\n      [x, y],\n      [x, y - deltaY],\n      [x - deltaX, y],\n      [x, y + deltaY],\n      [x, y],\n      [x + deltaX, y - deltaY],\n      [x + deltaX, y + deltaY],\n      [x, y],\n    ];\n    select(this.icon).maybeAppend('.backward', 'polygon').styles({\n      points,\n      fill: color,\n    });\n  }\n}\n\n/** 快进 */\nexport class Forward extends IconBase {\n  protected get label() {\n    return '快进';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const size = this.iconSize;\n    const deltaX = size / 2;\n    const deltaY = size / 2 / 3 ** 0.5;\n    const points: PolygonStyleProps['points'] = [\n      [x, y],\n      [x, y - deltaY],\n      [x + deltaX, y],\n      [x, y + deltaY],\n      [x, y],\n      [x - deltaX, y - deltaY],\n      [x - deltaX, y + deltaY],\n      [x, y],\n    ];\n    select(this.icon).maybeAppend('.forward', 'polygon').styles({\n      points,\n      fill: color,\n    });\n  }\n}\n\nexport class Play extends IconBase {\n  protected get label() {\n    return '播放';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const size = this.iconSize;\n    const deltaX = (size / 3) * 3 ** 0.5 * 0.8;\n    const points: PolygonStyleProps['points'] = [\n      [x + deltaX, y],\n      [x - deltaX / 2, y - (size / 2) * 0.8],\n      [x - deltaX / 2, y + (size / 2) * 0.8],\n      [x + deltaX, y],\n    ];\n    select(this.icon).maybeAppend('.play', 'polygon').styles({\n      points,\n      fill: color,\n    });\n  }\n}\n\nexport class Pause extends IconBase {\n  protected get label() {\n    return '暂停';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const size = this.iconSize;\n    const deltaX = size / 3;\n    const points: PolygonStyleProps['points'] = [\n      [x - deltaX, y - size / 2],\n      [x - deltaX, y + size / 2],\n      [x - deltaX / 2, y + size / 2],\n      [x - deltaX / 2, y - size / 2],\n      [x - deltaX, y - size / 2],\n      [x + deltaX / 2, y - size / 2],\n      [x + deltaX / 2, y + size / 2],\n      [x + deltaX, y + size / 2],\n      [x + deltaX, y - size / 2],\n    ];\n    select(this.icon).maybeAppend('.pause', 'polygon').styles({\n      points,\n      fill: color,\n    });\n  }\n}\n\n/** 时间范围 */\nexport class Range extends IconBase {\n  protected get label() {\n    return '范围时间';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const { iconSize: size, lineWidth } = this;\n    const gap = lineWidth;\n\n    select(this.icon)\n      .maybeAppend('.left-line', 'line')\n      .styles({\n        x1: x - size / 2,\n        y1: y - size / 2,\n        x2: x - size / 2,\n        y2: y + size / 2,\n        stroke: color,\n        lineWidth,\n      });\n\n    select(this.icon)\n      .maybeAppend('.right-line', 'line')\n      .styles({\n        x1: x + size / 2,\n        y1: y - size / 2,\n        x2: x + size / 2,\n        y2: y + size / 2,\n        stroke: color,\n        lineWidth,\n      });\n\n    select(this.icon)\n      .maybeAppend('.left-arrow', 'line')\n      .styles({\n        x1: x,\n        y1: y,\n        x2: x - size / 2 + gap * 2,\n        y2: y,\n        stroke: color,\n        lineWidth,\n        markerEnd: arrow(lineWidth * 2, color),\n      });\n\n    select(this.icon)\n      .maybeAppend('.right-arrow', 'line')\n      .styles({\n        x1: x,\n        y1: y,\n        x2: x + size / 2 - gap * 2,\n        y2: y,\n        stroke: color,\n        lineWidth,\n        markerEnd: arrow(lineWidth * 2, color),\n      });\n  }\n}\n\n/** 值范围 */\nexport class Value extends IconBase {\n  protected get label() {\n    return '单一时间';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const { iconSize: size, lineWidth } = this;\n\n    select(this.icon)\n      .maybeAppend('.line', 'line')\n      .styles({\n        x1: x,\n        y1: y - size / 2,\n        x2: x,\n        y2: y + size / 2,\n        stroke: color,\n        lineWidth,\n      });\n\n    const gap = lineWidth;\n\n    select(this.icon)\n      .maybeAppend('.left-arrow', 'line')\n      .styles({\n        x1: x - size / 2 - gap * 2,\n        y1: y,\n        x2: x - gap * 2,\n        y2: y,\n        stroke: color,\n        lineWidth,\n        markerEnd: arrow(lineWidth * 2, color),\n      });\n\n    select(this.icon)\n      .maybeAppend('.right-arrow', 'line')\n      .styles({\n        x1: x + size / 2 + gap * 2,\n        y1: y,\n        x2: x + gap * 2,\n        y2: y,\n        stroke: color,\n        lineWidth,\n        markerEnd: arrow(lineWidth * 2, color),\n      });\n  }\n}\n\nconst getCoordinatePoints = (size: number) => {\n  return [\n    [-size / 2, -size / 2],\n    [-size / 2, size / 2],\n    [size / 2, size / 2],\n  ];\n};\n\nexport class LineChart extends IconBase {\n  protected get label() {\n    return '折线图';\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const { iconSize: size, lineWidth } = this;\n\n    const gap = lineWidth;\n    const deltaX = (size - gap * 2 - lineWidth) / 4;\n    const deltaY = (size - gap * 2 - lineWidth) / 2;\n    const [ox, oy] = [x - size / 2 + gap, y + size / 2 - gap * 2];\n\n    select(this.icon)\n      .maybeAppend('.coordinate', 'polyline')\n      .styles({\n        points: getCoordinatePoints(size).map(([px, py]) => [px + x, py + y]),\n        stroke: color,\n        lineWidth,\n      });\n\n    select(this.icon)\n      .maybeAppend('.line', 'polyline')\n      .styles({\n        points: [\n          [ox, oy],\n          [ox + deltaX, oy - deltaY],\n          [ox + deltaX * 2, oy],\n          [ox + deltaX * 4, oy - deltaY * 2],\n        ],\n        stroke: color,\n        lineWidth,\n      });\n  }\n}\n\nexport class BarChart extends IconBase {\n  protected get label() {\n    return '条形图';\n  }\n\n  get data() {\n    return [1, 4, 2, 4, 3];\n  }\n\n  renderIcon() {\n    const { data } = this;\n    const { x, y, color } = this.attributes;\n    const { iconSize: size, lineWidth } = this;\n\n    const gap = lineWidth;\n    const deltaX = (size - gap) / data.length;\n    const deltaY = (size - gap * 2) / 4;\n    const [ox, oy] = [x - size / 2 + gap * 2, y + size / 2 - gap];\n\n    select(this.icon)\n      .maybeAppend('.coordinate', 'polyline')\n      .styles({\n        points: getCoordinatePoints(size).map(([px, py]) => [px + x, py + y]),\n        stroke: color,\n        lineWidth,\n      });\n\n    select(this.icon)\n      .maybeAppend('.bars', 'g')\n      .selectAll('.column')\n      .data(this.data.map((value, index) => ({ value, index })))\n      .join((enter) =>\n        enter\n          .append('line')\n          .attr('className', 'column')\n          .style('x1', ({ index }: any) => ox + deltaX * index)\n          .style('y1', oy)\n          .style('x2', ({ index }: any) => ox + deltaX * index)\n          .style('y2', ({ value }: any) => oy - deltaY * value)\n          .styles({\n            y1: oy,\n            stroke: color,\n            lineWidth,\n          })\n      );\n  }\n}\n\n/** 分割线 */\nexport class Split extends IconBase {\n  protected showBackground = false;\n\n  constructor(options: IconBaseOptions) {\n    super(deepAssign({}, { style: { color: '#d8d9d9' } }, options));\n  }\n\n  renderIcon() {\n    const { x, y, color } = this.attributes;\n    const { iconSize: size, lineWidth } = this;\n    select(this.icon)\n      .maybeAppend('.split', 'line')\n      .styles({\n        x1: x,\n        y1: y - size / 2,\n        x2: x,\n        y2: y + size / 2,\n        stroke: color,\n        lineWidth,\n      });\n  }\n}\n\nexport class SpeedSelect extends IconBase<{ speed?: number; onSelect: SelectStyleProps['onSelect'] }> {\n  public static tag = 'SpeedSelect';\n\n  protected showBackground = false;\n\n  protected get padding() {\n    return parseSeriesAttr(0);\n  }\n\n  renderIcon() {\n    const { iconSize } = this;\n    const { x, y, speed = 1 } = this.attributes;\n    const inheritStyle = omit(this.attributes as any, [\n      'x',\n      'y',\n      'transform',\n      'transformOrigin',\n      'width',\n      'height',\n      'size',\n      'color',\n      'speed',\n    ]);\n    const width = clamp(iconSize, 20, Infinity);\n    const height = 20;\n    const style: SelectStyleProps = {\n      ...inheritStyle,\n      x: x - width / 2,\n      y: y - height / 2,\n      width,\n      height,\n      defaultValue: speed,\n      bordered: false,\n      showDropdownIcon: false,\n      selectRadius: 2,\n      dropdownPadding: this.padding,\n      dropdownRadius: 2,\n      dropdownSpacing: iconSize / 5,\n      placeholderFontSize: iconSize / 2,\n      optionPadding: 0,\n      optionLabelFontSize: iconSize / 2,\n      optionBackgroundRadius: 1,\n      options: [\n        { label: '1x', value: 1 },\n        { label: '1.5x', value: 1.5 },\n        { label: '2x', value: 2 },\n      ],\n    };\n\n    select(this.icon)\n      .maybeAppend('.speed', () => new Select({ style }))\n      .attr('className', 'speed')\n      .each(function () {\n        this.update(style);\n      });\n  }\n}\n\ntype ToggleIconStyleProps<T extends string> = IconBaseStyleProps & {\n  type: T;\n  onChange?: (type: T) => void;\n};\ntype ToggleIconOptions<T extends string> = ComponentOptions<ToggleIconStyleProps<T>>;\nexport abstract class ToggleIcon<T extends string> extends Component<ToggleIconStyleProps<T>> {\n  abstract toggles: Array<\n    [T, typeof Play | typeof Pause | typeof Range | typeof Value | typeof LineChart | typeof BarChart]\n  >;\n\n  public static tag = 'ToggleIcon';\n\n  private icon = this.appendChild(new Group({}));\n\n  private currentType: T;\n\n  public getType() {\n    return this.currentType;\n  }\n\n  constructor(options: ToggleIconOptions<T>) {\n    super(options);\n    this.currentType = this.attributes.type;\n  }\n\n  render() {\n    const { onChange, ...restStyles } = this.attributes;\n    select(this.icon)\n      .selectAll('.icon')\n      .data([this.currentType])\n      .join(\n        (enter) =>\n          enter\n            .append((type) => {\n              const Ctor = this.toggles.find(([key]) => key === type)?.[1];\n              if (!Ctor) throw new Error(`Invalid type: ${type}`);\n              return new Ctor({});\n            })\n            .attr('className', 'icon')\n            .styles(restStyles, false)\n            .update({}),\n        (update) => update.styles({ restStyles }).update({}),\n        (exit) => exit.remove()\n      );\n  }\n\n  bindEvents() {\n    const { onChange } = this.attributes;\n    this.addEventListener('click', (e: Event) => {\n      e.preventDefault();\n      e.stopPropagation();\n      const nextIndex = (this.toggles.findIndex(([key]) => key === this.currentType) + 1) % this.toggles.length;\n      const nextType = this.toggles[nextIndex][0];\n      onChange?.(this.currentType);\n      this.currentType = nextType;\n      this.render();\n    });\n  }\n}\n\nexport class PlayPause extends ToggleIcon<'play' | 'pause'> {\n  toggles: ['play' | 'pause', typeof Play | typeof Pause][] = [\n    ['play', Play],\n    ['pause', Pause],\n  ];\n\n  constructor(options: ToggleIconOptions<'play' | 'pause'>) {\n    super(deepAssign({}, { style: { type: 'play' } }, options));\n  }\n}\n\nexport class SelectionType extends ToggleIcon<'range' | 'value'> {\n  toggles: ['range' | 'value', typeof Range | typeof Value][] = [\n    ['range', Range],\n    ['value', Value],\n  ];\n\n  constructor(options: ToggleIconOptions<'range' | 'value'>) {\n    super(deepAssign({}, { style: { type: 'range' } }, options));\n  }\n}\n\nexport class ChartType extends ToggleIcon<'line' | 'column'> {\n  toggles: ['line' | 'column', typeof LineChart | typeof BarChart][] = [\n    ['line', LineChart],\n    ['column', BarChart],\n  ];\n\n  constructor(options: ToggleIconOptions<'line' | 'column'>) {\n    super(deepAssign({}, { style: { type: 'column' } }, options));\n  }\n}\n"], "mappings": ";AACA,SAASA,KAAK,EAAEC,IAAI,QAAQ,YAAY;AACxC,SAA2BC,SAAS,QAAQ,YAAY;AAExD,SAASC,KAAK,EAAEC,IAAI,EAAEC,IAAI,QAAQ,cAAc;AAEhD,SAASC,UAAU,EAAEC,eAAe,EAAEC,MAAM,EAAEC,aAAa,QAAQ,YAAY;AAC/E,SAASC,SAAS,QAAQ,cAAc;AAExC,SAASC,MAAM,QAAQ,WAAW;AAWlC,IAAAC,QAAA,0BAAAC,MAAA;EAA2EC,SAAA,CAAAF,QAAA,EAAAC,MAAA;EAiEzE,SAAAD,SAAYG,OAAwB;IAClC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OACHX,UAAU,CACR,EAAE,EACF;MAAEY,KAAK,EAAE;QAAEC,iBAAiB,EAAEP,QAAQ,CAACQ,mBAAmB,CAACC;MAAO;IAAE,CAAE,EACtET,QAAQ,CAACU,cAAc,EACvBP,OAAO,CACR,CACF;IArDH;IACUC,KAAA,CAAAO,cAAc,GAAG,IAAI;IAQrBP,KAAA,CAAAQ,UAAU,GAAGR,KAAI,CAACS,WAAW,CAAC,IAAIpB,IAAI,CAAC,EAAE,CAAC,CAAC;IAE3CW,KAAA,CAAAU,IAAI,GAAGV,KAAI,CAACS,WAAW,CAAC,IAAItB,KAAK,CAAC,EAAE,CAAC,CAAC;;EA2ChD;EAnDAwB,MAAA,CAAAC,cAAA,CAAchB,QAAA,CAAAiB,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,UAAU;IACnB,CAAC;;;;EAQDH,MAAA,CAAAC,cAAA,CAAIhB,QAAA,CAAAiB,SAAA,aAAS;SAAb,SAAAC,CAAA;MACE,OAAOC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACC,UAAU,CAACC,IAAI,CAAC;IACzC,CAAC;;;;EAEDP,MAAA,CAAAC,cAAA,CAAchB,QAAA,CAAAiB,SAAA,WAAO;SAArB,SAAAC,CAAA;MACE,OAAOvB,eAAe,CAAC,IAAI,CAAC0B,UAAU,CAACC,IAAI,GAAG,CAAC,CAAC;IAClD,CAAC;;;;EAEDP,MAAA,CAAAC,cAAA,CAAchB,QAAA,CAAAiB,SAAA,YAAQ;SAAtB,SAAAC,CAAA;MACU,IAAAI,IAAI,GAAK,IAAI,CAACD,UAAU,CAAAC,IAApB;MACN,IAAAC,EAAA,GAAAC,MAAA,CAA6B,IAAI,CAACC,OAAO;QAAxCC,GAAG,GAAAH,EAAA;QAAEI,KAAK,GAAAJ,EAAA;QAAEK,MAAM,GAAAL,EAAA;QAAEM,IAAI,GAAAN,EAAA,GAAgB;MAC/C,OAAOJ,IAAI,CAACW,GAAG,CAACR,IAAI,GAAGH,IAAI,CAACW,GAAG,CAACD,IAAI,GAAGF,KAAK,EAAED,GAAG,GAAGE,MAAM,CAAC,EAAE,IAAI,CAACG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;IACtF,CAAC;;;;EAES/B,QAAA,CAAAiB,SAAA,CAAAe,gBAAgB,GAA1B;IACQ,IAAAT,EAAA,GAAiB,IAAI,CAACF,UAAU;MAA9BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEZ,IAAI,GAAAC,EAAA,CAAAD,IAAoB;IACtC,IAAMa,QAAQ,GAAGb,IAAI,GAAG,CAAC;IACzB,IAAMc,eAAe,GAAGvC,aAAa,CAAC,IAAI,CAACwB,UAAU,EAAE,YAAY,CAAC;IACpE,IAAI,CAACT,UAAU,CAACyB,IAAI,CAAAC,QAAA;MAAGL,CAAC,EAAEA,CAAC,GAAGE,QAAQ;MAAED,CAAC,EAAEA,CAAC,GAAGC,QAAQ;MAAEI,KAAK,EAAEjB,IAAI;MAAEkB,MAAM,EAAElB;IAAI,GAAKc,eAAe,EAAG;EAC3G,CAAC;EAESpC,QAAA,CAAAiB,SAAA,CAAAwB,aAAa,GAAvB;IACE,IAAI,CAAC,IAAI,CAACC,KAAK,EAAE;IACT,IAAApB,IAAI,GAAK,IAAI,CAACD,UAAU,CAAAC,IAApB;IACN,IAAAC,EAAA,GAAW,IAAI,CAACX,UAAU,CAAC+B,OAAO,EAAE;MAAlCV,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAA8B;IAC1C,IAAI,CAACU,SAAS,CAACC,MAAM,CAAC;MAAEZ,CAAC,EAAEA,CAAC,GAAGX,IAAI,GAAG,CAAC;MAAEY,CAAC,EAAEA,CAAC,GAAG,CAAC;MAAEY,SAAS,EAAE,IAAI,CAACJ,KAAK;MAAEK,UAAU,EAAE;IAAS,CAAE,CAAC;EACpG,CAAC;EAES/C,QAAA,CAAAiB,SAAA,CAAA+B,aAAa,GAAvB;IACE,IAAI,CAACJ,SAAS,CAACC,MAAM,CAAC;MAAEE,UAAU,EAAE;IAAQ,CAAE,CAAC;EACjD,CAAC;EAaD/C,QAAA,CAAAiB,SAAA,CAAAgC,iBAAiB,GAAjB;;IACEhD,MAAA,CAAAgB,SAAK,CAACgC,iBAAiB,CAAA5C,IAAA,MAAE;IACzB;IACQ,IAAAiB,IAAI,GAAK,IAAI,CAACD,UAAU,CAAAC,IAApB;IACN,IAAA4B,EAAA,GAAW,IAAI,CAACtC,UAAU,CAAC+B,OAAO,EAAE;MAAlCV,CAAC,GAAAiB,EAAA,CAAAjB,CAAA;MAAEC,CAAC,GAAAgB,EAAA,CAAAhB,CAA8B;IAC1C,IAAMiB,MAAM,GAAG,CAAA5B,EAAA,OAAI,CAAC6B,aAAa,cAAA7B,EAAA,uBAAAA,EAAA,CAAE8B,WAAW;IAE9C,IAAIF,MAAM,EAAE;MACV,IAAI,CAACP,SAAS,GAAIO,MAA4B,CAACtC,WAAW,CACxD,IAAIf,SAAS,CAAC;QACZQ,KAAK,EAAE;UACL2B,CAAC,EAAEA,CAAC,GAAGX,IAAI,GAAG,CAAC;UACfY,CAAC,EAAEA,CAAC,GAAGZ,IAAI,GAAG,CAAC;UACfyB,UAAU,EAAE,QAAQ;UACpBO,QAAQ,EAAE,KAAK;UACfC,MAAM,EAAE,CAAC;UACTC,MAAM,EAAE;;OAEX,CAAC,CACH;IACH;EACF,CAAC;EAEDxD,QAAA,CAAAiB,SAAA,CAAAwC,oBAAoB,GAApB;IACE,IAAI,CAACb,SAAS,CAACc,OAAO,EAAE;EAC1B,CAAC;EAED1D,QAAA,CAAAiB,SAAA,CAAA0C,MAAM,GAAN;IACE,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,IAAI,CAACjD,cAAc,EAAE,IAAI,CAACqB,gBAAgB,EAAE;EAClD,CAAC;EAIMhC,QAAA,CAAAiB,SAAA,CAAA4C,UAAU,GAAjB;IAAA,IAAAzD,KAAA;IACU,IAAA0D,OAAO,GAAK,IAAI,CAACzC,UAAU,CAAAyC,OAApB;IACf,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE;MAC7BD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG1D,KAAI,CAAC;IACjB,CAAC,CAAC;IAEF,IAAI,IAAI,CAACO,cAAc,EAAE;MACvB,IAAMqD,iBAAe,GAAG,SAAAA,CAAA;QAAM,OAAA5D,KAAI,CAACQ,UAAU,CAACyB,IAAI,CAAC;UAAE4B,OAAO,EAAEjE,QAAQ,CAACQ,mBAAmB,CAACC;QAAO,CAAE,CAAC;MAAvE,CAAuE;MACrG,IAAMyD,iBAAe,GAAG,SAAAA,CAAA;QAAM,OAAA9D,KAAI,CAACQ,UAAU,CAACyB,IAAI,CAAC;UAAE4B,OAAO,EAAEjE,QAAQ,CAACQ,mBAAmB,CAAC2D;QAAK,CAAE,CAAC;MAArE,CAAqE;MACnG,IAAMC,kBAAgB,GAAG,SAAAA,CAAA;QAAM,OAAAhE,KAAI,CAACQ,UAAU,CAACyB,IAAI,CAAC;UAAE4B,OAAO,EAAEjE,QAAQ,CAACQ,mBAAmB,CAAC6D;QAAM,CAAE,CAAC;MAAtE,CAAsE;MAErG,IAAI,CAACN,gBAAgB,CAAC,cAAc,EAAE;QACpCG,iBAAe,EAAE;QACjB9D,KAAI,CAACqC,aAAa,EAAE;MACtB,CAAC,CAAC;MACF,IAAI,CAACsB,gBAAgB,CAAC,cAAc,EAAE;QACpCC,iBAAe,EAAE;QACjB5D,KAAI,CAAC4C,aAAa,EAAE;MACtB,CAAC,CAAC;MACF,IAAI,CAACe,gBAAgB,CAAC,aAAa,EAAE;QACnCK,kBAAgB,EAAE;MACpB,CAAC,CAAC;MACF,IAAI,CAACL,gBAAgB,CAAC,WAAW,EAAE;QACjCC,iBAAe,EAAE;MACnB,CAAC,CAAC;IACJ;EACF,CAAC;EAvIahE,QAAA,CAAAsE,GAAG,GAAG,UAAU;EAEvBtE,QAAA,CAAAU,cAAc,GAAoB;IACvCJ,KAAK,EAAE;MACL2B,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJZ,IAAI,EAAE,EAAE;MACRiD,KAAK,EAAE,SAAS;MAChBC,gBAAgB,EAAE,CAAC;MACnBC,cAAc,EAAE;;GAEnB;EAEczE,QAAA,CAAAQ,mBAAmB,GAAG;IACnCC,OAAO,EAAE,CAAC;IACV0D,KAAK,EAAE,GAAG;IACVE,MAAM,EAAE;GACT;EAuHH,OAAArE,QAAC;CAAA,CAzI0EV,SAAS;SAA9DU,QAAQ;AA2I9B,IAAM0E,KAAK,GAAG,SAAAA,CAACpD,IAAY,EAAEiD,KAAyB;EAAzB,IAAAA,KAAA;IAAAA,KAAA,YAAyB;EAAA;EACpD,OAAO,IAAI/E,IAAI,CAAC;IACdc,KAAK,EAAE;MACLqE,IAAI,EAAEJ,KAAK;MACXK,CAAC,EAAE,KAAAC,MAAA,CAAKvD,IAAI,OAAAuD,MAAA,CAAIvD,IAAI,UAAAuD,MAAA,CAAOvD,IAAI,WAAAuD,MAAA,CAAQvD,IAAI,QAAAuD,MAAA,CAAKvD,IAAI,OAAI;MACxDwD,eAAe,EAAE;;GAEpB,CAAC;AACJ,CAAC;AAED;AACA,IAAAC,KAAA,0BAAA9E,MAAA;EAA2BC,SAAA,CAAA6E,KAAA,EAAA9E,MAAA;EAA3B,SAAA8E,MAAA;;EA4BA;EA3BUA,KAAA,CAAA9D,SAAA,CAAA+D,OAAO,GAAf,UAAgBC,EAAU,EAAEC,EAAU,EAAE3B,MAAc;IAC9C,IAAAhC,EAAA,GAAAC,MAAA,CAAW,CAAC+B,MAAM,EAAEA,MAAM,CAAC;MAA1B4B,EAAE,GAAA5D,EAAA;MAAE6D,EAAE,GAAA7D,EAAA,GAAoB;IACjC,IAAM8D,aAAa,GAAG,SAAAA,CAACC,KAAa;MAAK,QAACL,EAAE,GAAG1B,MAAM,GAAGpC,IAAI,CAACoE,GAAG,CAACD,KAAK,CAAC,EAAEJ,EAAE,GAAG3B,MAAM,GAAGpC,IAAI,CAACqE,GAAG,CAACF,KAAK,CAAC,CAAC;IAA9D,CAA8D;IACjG,IAAApC,EAAA,GAAA1B,MAAA,CAAW6D,aAAa,CAAE,CAAC,CAAC,GAAG,CAAC,GAAIlE,IAAI,CAACsE,EAAE,CAAC;MAA3CC,EAAE,GAAAxC,EAAA;MAAEyC,EAAE,GAAAzC,EAAA,GAAqC;IAC5C,IAAA0C,EAAA,GAAApE,MAAA,CAAW6D,aAAa,CAAE,CAAC,GAAG,CAAC,GAAIlE,IAAI,CAACsE,EAAE,CAAC;MAA1CI,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAAoC;IACjD,OAAO,IAAAf,MAAA,CAAIa,EAAE,OAAAb,MAAA,CAAIc,EAAE,QAAAd,MAAA,CAAKM,EAAE,OAAAN,MAAA,CAAIO,EAAE,aAAAP,MAAA,CAAUgB,EAAE,OAAAhB,MAAA,CAAIiB,EAAE,CAAE;EACtD,CAAC;EAED/E,MAAA,CAAAC,cAAA,CAAc+D,KAAA,CAAA9D,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,IAAI;IACb,CAAC;;;;EAED6D,KAAA,CAAA9D,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACvC,IAAMjD,IAAI,GAAG,IAAI,CAACyE,QAAQ;IAClB,IAAAhE,SAAS,GAAK,IAAI,CAAAA,SAAT;IACjB,IAAMiE,SAAS,GAAGjE,SAAS,GAAG,GAAG;IAEjCnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAC7BC,MAAM,CAAC;MACNC,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA,SAAA;MACT6C,CAAC,EAAE,IAAI,CAACI,OAAO,CAAC/C,CAAC,EAAEC,CAAC,EAAEZ,IAAI,GAAG,CAAC,GAAGS,SAAS,CAAC;MAC3CqE,WAAW,EAAE1B,KAAK,CAACsB,SAAS,EAAEzB,KAAK;KACpC,CAAC;EACN,CAAC;EACH,OAAAQ,KAAC;AAAD,CAAC,CA5B0B/E,QAAQ;;AA8BnC;AACA,IAAAqG,QAAA,0BAAApG,MAAA;EAA8BC,SAAA,CAAAmG,QAAA,EAAApG,MAAA;EAA9B,SAAAoG,SAAA;;EAyBA;EAxBEtF,MAAA,CAAAC,cAAA,CAAcqF,QAAA,CAAApF,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,IAAI;IACb,CAAC;;;;EAEDmF,QAAA,CAAApF,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACvC,IAAMjD,IAAI,GAAG,IAAI,CAACyE,QAAQ;IAC1B,IAAMO,MAAM,GAAGhF,IAAI,GAAG,CAAC;IACvB,IAAMiF,MAAM,GAAGjF,IAAI,GAAG,CAAC,GAAGH,IAAA,CAAAqF,GAAA,EAAC,EAAI,GAAG;IAClC,IAAMC,MAAM,GAAgC,CAC1C,CAACxE,CAAC,EAAEC,CAAC,CAAC,EACN,CAACD,CAAC,EAAEC,CAAC,GAAGqE,MAAM,CAAC,EACf,CAACtE,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,CAAC,EACf,CAACD,CAAC,EAAEC,CAAC,GAAGqE,MAAM,CAAC,EACf,CAACtE,CAAC,EAAEC,CAAC,CAAC,EACN,CAACD,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGqE,MAAM,CAAC,EACxB,CAACtE,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGqE,MAAM,CAAC,EACxB,CAACtE,CAAC,EAAEC,CAAC,CAAC,CACP;IACDtC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CAACmF,WAAW,CAAC,WAAW,EAAE,SAAS,CAAC,CAACC,MAAM,CAAC;MAC3DO,MAAM,EAAAA,MAAA;MACN9B,IAAI,EAAEJ;KACP,CAAC;EACJ,CAAC;EACH,OAAA8B,QAAC;AAAD,CAAC,CAzB6BrG,QAAQ;;AA2BtC;AACA,IAAA0G,OAAA,0BAAAzG,MAAA;EAA6BC,SAAA,CAAAwG,OAAA,EAAAzG,MAAA;EAA7B,SAAAyG,QAAA;;EAyBA;EAxBE3F,MAAA,CAAAC,cAAA,CAAc0F,OAAA,CAAAzF,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,IAAI;IACb,CAAC;;;;EAEDwF,OAAA,CAAAzF,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACvC,IAAMjD,IAAI,GAAG,IAAI,CAACyE,QAAQ;IAC1B,IAAMO,MAAM,GAAGhF,IAAI,GAAG,CAAC;IACvB,IAAMiF,MAAM,GAAGjF,IAAI,GAAG,CAAC,GAAGH,IAAA,CAAAqF,GAAA,EAAC,EAAI,GAAG;IAClC,IAAMC,MAAM,GAAgC,CAC1C,CAACxE,CAAC,EAAEC,CAAC,CAAC,EACN,CAACD,CAAC,EAAEC,CAAC,GAAGqE,MAAM,CAAC,EACf,CAACtE,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,CAAC,EACf,CAACD,CAAC,EAAEC,CAAC,GAAGqE,MAAM,CAAC,EACf,CAACtE,CAAC,EAAEC,CAAC,CAAC,EACN,CAACD,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGqE,MAAM,CAAC,EACxB,CAACtE,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGqE,MAAM,CAAC,EACxB,CAACtE,CAAC,EAAEC,CAAC,CAAC,CACP;IACDtC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CAACmF,WAAW,CAAC,UAAU,EAAE,SAAS,CAAC,CAACC,MAAM,CAAC;MAC1DO,MAAM,EAAAA,MAAA;MACN9B,IAAI,EAAEJ;KACP,CAAC;EACJ,CAAC;EACH,OAAAmC,OAAC;AAAD,CAAC,CAzB4B1G,QAAQ;;AA2BrC,IAAA2G,IAAA,0BAAA1G,MAAA;EAA0BC,SAAA,CAAAyG,IAAA,EAAA1G,MAAA;EAA1B,SAAA0G,KAAA;;EAoBA;EAnBE5F,MAAA,CAAAC,cAAA,CAAc2F,IAAA,CAAA1F,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,IAAI;IACb,CAAC;;;;EAEDyF,IAAA,CAAA1F,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACvC,IAAMjD,IAAI,GAAG,IAAI,CAACyE,QAAQ;IAC1B,IAAMO,MAAM,GAAIhF,IAAI,GAAG,CAAC,GAAIH,IAAA,CAAAqF,GAAA,EAAC,EAAI,GAAG,IAAG,GAAG;IAC1C,IAAMC,MAAM,GAAgC,CAC1C,CAACxE,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,CAAC,EACf,CAACD,CAAC,GAAGqE,MAAM,GAAG,CAAC,EAAEpE,CAAC,GAAIZ,IAAI,GAAG,CAAC,GAAI,GAAG,CAAC,EACtC,CAACW,CAAC,GAAGqE,MAAM,GAAG,CAAC,EAAEpE,CAAC,GAAIZ,IAAI,GAAG,CAAC,GAAI,GAAG,CAAC,EACtC,CAACW,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,CAAC,CAChB;IACDtC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CAACmF,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAACC,MAAM,CAAC;MACvDO,MAAM,EAAAA,MAAA;MACN9B,IAAI,EAAEJ;KACP,CAAC;EACJ,CAAC;EACH,OAAAoC,IAAC;AAAD,CAAC,CApByB3G,QAAQ;;AAsBlC,IAAA4G,KAAA,0BAAA3G,MAAA;EAA2BC,SAAA,CAAA0G,KAAA,EAAA3G,MAAA;EAA3B,SAAA2G,MAAA;;EAyBA;EAxBE7F,MAAA,CAAAC,cAAA,CAAc4F,KAAA,CAAA3F,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,IAAI;IACb,CAAC;;;;EAED0F,KAAA,CAAA3F,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACvC,IAAMjD,IAAI,GAAG,IAAI,CAACyE,QAAQ;IAC1B,IAAMO,MAAM,GAAGhF,IAAI,GAAG,CAAC;IACvB,IAAMmF,MAAM,GAAgC,CAC1C,CAACxE,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC1B,CAACW,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC1B,CAACW,CAAC,GAAGqE,MAAM,GAAG,CAAC,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC9B,CAACW,CAAC,GAAGqE,MAAM,GAAG,CAAC,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC9B,CAACW,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC1B,CAACW,CAAC,GAAGqE,MAAM,GAAG,CAAC,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC9B,CAACW,CAAC,GAAGqE,MAAM,GAAG,CAAC,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC9B,CAACW,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,EAC1B,CAACW,CAAC,GAAGqE,MAAM,EAAEpE,CAAC,GAAGZ,IAAI,GAAG,CAAC,CAAC,CAC3B;IACD1B,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CAACmF,WAAW,CAAC,QAAQ,EAAE,SAAS,CAAC,CAACC,MAAM,CAAC;MACxDO,MAAM,EAAAA,MAAA;MACN9B,IAAI,EAAEJ;KACP,CAAC;EACJ,CAAC;EACH,OAAAqC,KAAC;AAAD,CAAC,CAzB0B5G,QAAQ;;AA2BnC;AACA,IAAA6G,KAAA,0BAAA5G,MAAA;EAA2BC,SAAA,CAAA2G,KAAA,EAAA5G,MAAA;EAA3B,SAAA4G,MAAA;;EAwDA;EAvDE9F,MAAA,CAAAC,cAAA,CAAc6F,KAAA,CAAA5F,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,MAAM;IACf,CAAC;;;;EAED2F,KAAA,CAAA5F,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACjC,IAAArB,EAAA,GAAgC,IAAI;MAAxB5B,IAAI,GAAA4B,EAAA,CAAA6C,QAAA;MAAEhE,SAAS,GAAAmB,EAAA,CAAAnB,SAAS;IAC1C,IAAM+E,GAAG,GAAG/E,SAAS;IAErBnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,CACjCC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC,GAAGX,IAAI,GAAG,CAAC;MAChBqE,EAAE,EAAEzD,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChBuE,EAAE,EAAE5D,CAAC,GAAGX,IAAI,GAAG,CAAC;MAChBwE,EAAE,EAAE5D,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChB6E,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA;KACV,CAAC;IAEJnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,aAAa,EAAE,MAAM,CAAC,CAClCC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC,GAAGX,IAAI,GAAG,CAAC;MAChBqE,EAAE,EAAEzD,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChBuE,EAAE,EAAE5D,CAAC,GAAGX,IAAI,GAAG,CAAC;MAChBwE,EAAE,EAAE5D,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChB6E,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA;KACV,CAAC;IAEJnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,aAAa,EAAE,MAAM,CAAC,CAClCC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC;MACL0D,EAAE,EAAEzD,CAAC;MACL2D,EAAE,EAAE5D,CAAC,GAAGX,IAAI,GAAG,CAAC,GAAGwF,GAAG,GAAG,CAAC;MAC1BhB,EAAE,EAAE5D,CAAC;MACLiE,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA,SAAA;MACTgF,SAAS,EAAErC,KAAK,CAAC3C,SAAS,GAAG,CAAC,EAAEwC,KAAK;KACtC,CAAC;IAEJ3E,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,cAAc,EAAE,MAAM,CAAC,CACnCC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC;MACL0D,EAAE,EAAEzD,CAAC;MACL2D,EAAE,EAAE5D,CAAC,GAAGX,IAAI,GAAG,CAAC,GAAGwF,GAAG,GAAG,CAAC;MAC1BhB,EAAE,EAAE5D,CAAC;MACLiE,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA,SAAA;MACTgF,SAAS,EAAErC,KAAK,CAAC3C,SAAS,GAAG,CAAC,EAAEwC,KAAK;KACtC,CAAC;EACN,CAAC;EACH,OAAAsC,KAAC;AAAD,CAAC,CAxD0B7G,QAAQ;;AA0DnC;AACA,IAAAgH,KAAA,0BAAA/G,MAAA;EAA2BC,SAAA,CAAA8G,KAAA,EAAA/G,MAAA;EAA3B,SAAA+G,MAAA;;EA8CA;EA7CEjG,MAAA,CAAAC,cAAA,CAAcgG,KAAA,CAAA/F,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,MAAM;IACf,CAAC;;;;EAED8F,KAAA,CAAA/F,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACjC,IAAArB,EAAA,GAAgC,IAAI;MAAxB5B,IAAI,GAAA4B,EAAA,CAAA6C,QAAA;MAAEhE,SAAS,GAAAmB,EAAA,CAAAnB,SAAS;IAE1CnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAC5BC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC;MACL0D,EAAE,EAAEzD,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChBuE,EAAE,EAAE5D,CAAC;MACL6D,EAAE,EAAE5D,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChB6E,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA;KACV,CAAC;IAEJ,IAAM+E,GAAG,GAAG/E,SAAS;IAErBnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,aAAa,EAAE,MAAM,CAAC,CAClCC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC,GAAGX,IAAI,GAAG,CAAC,GAAGwF,GAAG,GAAG,CAAC;MAC1BnB,EAAE,EAAEzD,CAAC;MACL2D,EAAE,EAAE5D,CAAC,GAAG6E,GAAG,GAAG,CAAC;MACfhB,EAAE,EAAE5D,CAAC;MACLiE,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA,SAAA;MACTgF,SAAS,EAAErC,KAAK,CAAC3C,SAAS,GAAG,CAAC,EAAEwC,KAAK;KACtC,CAAC;IAEJ3E,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,cAAc,EAAE,MAAM,CAAC,CACnCC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC,GAAGX,IAAI,GAAG,CAAC,GAAGwF,GAAG,GAAG,CAAC;MAC1BnB,EAAE,EAAEzD,CAAC;MACL2D,EAAE,EAAE5D,CAAC,GAAG6E,GAAG,GAAG,CAAC;MACfhB,EAAE,EAAE5D,CAAC;MACLiE,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA,SAAA;MACTgF,SAAS,EAAErC,KAAK,CAAC3C,SAAS,GAAG,CAAC,EAAEwC,KAAK;KACtC,CAAC;EACN,CAAC;EACH,OAAAyC,KAAC;AAAD,CAAC,CA9C0BhH,QAAQ;;AAgDnC,IAAMiH,mBAAmB,GAAG,SAAAA,CAAC3F,IAAY;EACvC,OAAO,CACL,CAAC,CAACA,IAAI,GAAG,CAAC,EAAE,CAACA,IAAI,GAAG,CAAC,CAAC,EACtB,CAAC,CAACA,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,CAAC,CAAC,EACrB,CAACA,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG,CAAC,CAAC,CACrB;AACH,CAAC;AAED,IAAA4F,SAAA,0BAAAjH,MAAA;EAA+BC,SAAA,CAAAgH,SAAA,EAAAjH,MAAA;EAA/B,SAAAiH,UAAA;;EAmCA;EAlCEnG,MAAA,CAAAC,cAAA,CAAckG,SAAA,CAAAjG,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,KAAK;IACd,CAAC;;;;EAEDgG,SAAA,CAAAjG,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACjC,IAAArB,EAAA,GAAgC,IAAI;MAAxB5B,IAAI,GAAA4B,EAAA,CAAA6C,QAAA;MAAEhE,SAAS,GAAAmB,EAAA,CAAAnB,SAAS;IAE1C,IAAM+E,GAAG,GAAG/E,SAAS;IACrB,IAAMuE,MAAM,GAAG,CAAChF,IAAI,GAAGwF,GAAG,GAAG,CAAC,GAAG/E,SAAS,IAAI,CAAC;IAC/C,IAAMwE,MAAM,GAAG,CAACjF,IAAI,GAAGwF,GAAG,GAAG,CAAC,GAAG/E,SAAS,IAAI,CAAC;IACzC,IAAA6D,EAAA,GAAApE,MAAA,CAAW,CAACS,CAAC,GAAGX,IAAI,GAAG,CAAC,GAAGwF,GAAG,EAAE5E,CAAC,GAAGZ,IAAI,GAAG,CAAC,GAAGwF,GAAG,GAAG,CAAC,CAAC;MAAtDK,EAAE,GAAAvB,EAAA;MAAEwB,EAAE,GAAAxB,EAAA,GAAgD;IAE7DhG,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CACtCC,MAAM,CAAC;MACNO,MAAM,EAAEQ,mBAAmB,CAAC3F,IAAI,CAAC,CAAC+F,GAAG,CAAC,UAAC9F,EAAQ;YAAR2B,EAAA,GAAA1B,MAAA,CAAAD,EAAA,IAAQ;UAAP+F,EAAE,GAAApE,EAAA;UAAEqE,EAAE,GAAArE,EAAA;QAAM,QAACoE,EAAE,GAAGrF,CAAC,EAAEsF,EAAE,GAAGrF,CAAC,CAAC;MAAhB,CAAgB,CAAC;MACrEiE,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA;KACV,CAAC;IAEJnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,OAAO,EAAE,UAAU,CAAC,CAChCC,MAAM,CAAC;MACNO,MAAM,EAAE,CACN,CAACU,EAAE,EAAEC,EAAE,CAAC,EACR,CAACD,EAAE,GAAGb,MAAM,EAAEc,EAAE,GAAGb,MAAM,CAAC,EAC1B,CAACY,EAAE,GAAGb,MAAM,GAAG,CAAC,EAAEc,EAAE,CAAC,EACrB,CAACD,EAAE,GAAGb,MAAM,GAAG,CAAC,EAAEc,EAAE,GAAGb,MAAM,GAAG,CAAC,CAAC,CACnC;MACDJ,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA;KACV,CAAC;EACN,CAAC;EACH,OAAAmF,SAAC;AAAD,CAAC,CAnC8BlH,QAAQ;;AAqCvC,IAAAwH,QAAA,0BAAAvH,MAAA;EAA8BC,SAAA,CAAAsH,QAAA,EAAAvH,MAAA;EAA9B,SAAAuH,SAAA;;EA8CA;EA7CEzG,MAAA,CAAAC,cAAA,CAAcwG,QAAA,CAAAvG,SAAA,SAAK;SAAnB,SAAAC,CAAA;MACE,OAAO,KAAK;IACd,CAAC;;;;EAEDH,MAAA,CAAAC,cAAA,CAAIwG,QAAA,CAAAvG,SAAA,QAAI;SAAR,SAAAC,CAAA;MACE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB,CAAC;;;;EAEDsG,QAAA,CAAAvG,SAAA,CAAA2C,UAAU,GAAV;IACU,IAAA6D,IAAI,GAAK,IAAI,CAAAA,IAAT;IACN,IAAAlG,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACjC,IAAArB,EAAA,GAAgC,IAAI;MAAxB5B,IAAI,GAAA4B,EAAA,CAAA6C,QAAA;MAAEhE,SAAS,GAAAmB,EAAA,CAAAnB,SAAS;IAE1C,IAAM+E,GAAG,GAAG/E,SAAS;IACrB,IAAMuE,MAAM,GAAG,CAAChF,IAAI,GAAGwF,GAAG,IAAIW,IAAI,CAACC,MAAM;IACzC,IAAMnB,MAAM,GAAG,CAACjF,IAAI,GAAGwF,GAAG,GAAG,CAAC,IAAI,CAAC;IAC7B,IAAAlB,EAAA,GAAApE,MAAA,CAAW,CAACS,CAAC,GAAGX,IAAI,GAAG,CAAC,GAAGwF,GAAG,GAAG,CAAC,EAAE5E,CAAC,GAAGZ,IAAI,GAAG,CAAC,GAAGwF,GAAG,CAAC;MAAtDK,EAAE,GAAAvB,EAAA;MAAEwB,EAAE,GAAAxB,EAAA,GAAgD;IAE7DhG,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,CACtCC,MAAM,CAAC;MACNO,MAAM,EAAEQ,mBAAmB,CAAC3F,IAAI,CAAC,CAAC+F,GAAG,CAAC,UAAC9F,EAAQ;YAAR2B,EAAA,GAAA1B,MAAA,CAAAD,EAAA,IAAQ;UAAP+F,EAAE,GAAApE,EAAA;UAAEqE,EAAE,GAAArE,EAAA;QAAM,QAACoE,EAAE,GAAGrF,CAAC,EAAEsF,EAAE,GAAGrF,CAAC,CAAC;MAAhB,CAAgB,CAAC;MACrEiE,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA;KACV,CAAC;IAEJnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,OAAO,EAAE,GAAG,CAAC,CACzB0B,SAAS,CAAC,SAAS,CAAC,CACpBF,IAAI,CAAC,IAAI,CAACA,IAAI,CAACJ,GAAG,CAAC,UAACO,KAAK,EAAEC,KAAK;MAAK,OAAC;QAAED,KAAK,EAAAA,KAAA;QAAEC,KAAK,EAAAA;MAAA,CAAE;IAAjB,CAAkB,CAAC,CAAC,CACzDC,IAAI,CAAC,UAACC,KAAK;MACV,OAAAA,KAAK,CACFC,MAAM,CAAC,MAAM,CAAC,CACd3F,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAC3B/B,KAAK,CAAC,IAAI,EAAE,UAACiB,EAAc;YAAZsG,KAAK,GAAAtG,EAAA,CAAAsG,KAAA;QAAY,OAAAV,EAAE,GAAGb,MAAM,GAAGuB,KAAK;MAAnB,CAAmB,CAAC,CACpDvH,KAAK,CAAC,IAAI,EAAE8G,EAAE,CAAC,CACf9G,KAAK,CAAC,IAAI,EAAE,UAACiB,EAAc;YAAZsG,KAAK,GAAAtG,EAAA,CAAAsG,KAAA;QAAY,OAAAV,EAAE,GAAGb,MAAM,GAAGuB,KAAK;MAAnB,CAAmB,CAAC,CACpDvH,KAAK,CAAC,IAAI,EAAE,UAACiB,EAAc;YAAZqG,KAAK,GAAArG,EAAA,CAAAqG,KAAA;QAAY,OAAAR,EAAE,GAAGb,MAAM,GAAGqB,KAAK;MAAnB,CAAmB,CAAC,CACpD1B,MAAM,CAAC;QACNP,EAAE,EAAEyB,EAAE;QACNjB,MAAM,EAAE5B,KAAK;QACbxC,SAAS,EAAAA;OACV,CAAC;IAXJ,CAWI,CACL;EACL,CAAC;EACH,OAAAyF,QAAC;AAAD,CAAC,CA9C6BxH,QAAQ;;AAgDtC;AACA,IAAAiI,KAAA,0BAAAhI,MAAA;EAA2BC,SAAA,CAAA+H,KAAA,EAAAhI,MAAA;EAGzB,SAAAgI,MAAY9H,OAAwB;IAClC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACX,UAAU,CAAC,EAAE,EAAE;MAAEY,KAAK,EAAE;QAAEiE,KAAK,EAAE;MAAS;IAAE,CAAE,EAAEpE,OAAO,CAAC,CAAC;IAHvDC,KAAA,CAAAO,cAAc,GAAG,KAAK;;EAIhC;EAEAsH,KAAA,CAAAhH,SAAA,CAAA2C,UAAU,GAAV;IACQ,IAAArC,EAAA,GAAkB,IAAI,CAACF,UAAU;MAA/BY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEqC,KAAK,GAAAhD,EAAA,CAAAgD,KAAoB;IACjC,IAAArB,EAAA,GAAgC,IAAI;MAAxB5B,IAAI,GAAA4B,EAAA,CAAA6C,QAAA;MAAEhE,SAAS,GAAAmB,EAAA,CAAAnB,SAAS;IAC1CnC,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,CAC7BC,MAAM,CAAC;MACNR,EAAE,EAAEzD,CAAC;MACL0D,EAAE,EAAEzD,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChBuE,EAAE,EAAE5D,CAAC;MACL6D,EAAE,EAAE5D,CAAC,GAAGZ,IAAI,GAAG,CAAC;MAChB6E,MAAM,EAAE5B,KAAK;MACbxC,SAAS,EAAAA;KACV,CAAC;EACN,CAAC;EACH,OAAAkG,KAAC;AAAD,CAAC,CArB0BjI,QAAQ;;AAuBnC,IAAAkI,WAAA,0BAAAjI,MAAA;EAAiCC,SAAA,CAAAgI,WAAA,EAAAjI,MAAA;EAAjC,SAAAiI,YAAA;;IAGY9H,KAAA,CAAAO,cAAc,GAAG,KAAK;;EAqDlC;EAnDEI,MAAA,CAAAC,cAAA,CAAckH,WAAA,CAAAjH,SAAA,WAAO;SAArB,SAAAC,CAAA;MACE,OAAOvB,eAAe,CAAC,CAAC,CAAC;IAC3B,CAAC;;;;EAEDuI,WAAA,CAAAjH,SAAA,CAAA2C,UAAU,GAAV;IACU,IAAAmC,QAAQ,GAAK,IAAI,CAAAA,QAAT;IACV,IAAAxE,EAAA,GAAsB,IAAI,CAACF,UAAU;MAAnCY,CAAC,GAAAV,EAAA,CAAAU,CAAA;MAAEC,CAAC,GAAAX,EAAA,CAAAW,CAAA;MAAEgB,EAAA,GAAA3B,EAAA,CAAA4G,KAAS;MAATA,KAAK,GAAAjF,EAAA,cAAG,CAAC,GAAAA,EAAoB;IAC3C,IAAMkF,YAAY,GAAG/I,IAAI,CAAC,IAAI,CAACgC,UAAiB,EAAE,CAChD,GAAG,EACH,GAAG,EACH,WAAW,EACX,iBAAiB,EACjB,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,OAAO,CACR,CAAC;IACF,IAAMkB,KAAK,GAAGnD,KAAK,CAAC2G,QAAQ,EAAE,EAAE,EAAEsC,QAAQ,CAAC;IAC3C,IAAM7F,MAAM,GAAG,EAAE;IACjB,IAAMlC,KAAK,GAAAgC,QAAA,CAAAA,QAAA,KACN8F,YAAY;MACfnG,CAAC,EAAEA,CAAC,GAAGM,KAAK,GAAG,CAAC;MAChBL,CAAC,EAAEA,CAAC,GAAGM,MAAM,GAAG,CAAC;MACjBD,KAAK,EAAAA,KAAA;MACLC,MAAM,EAAAA,MAAA;MACN8F,YAAY,EAAEH,KAAK;MACnBI,QAAQ,EAAE,KAAK;MACfC,gBAAgB,EAAE,KAAK;MACvBC,YAAY,EAAE,CAAC;MACfC,eAAe,EAAE,IAAI,CAACjH,OAAO;MAC7BkH,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE7C,QAAQ,GAAG,CAAC;MAC7B8C,mBAAmB,EAAE9C,QAAQ,GAAG,CAAC;MACjC+C,aAAa,EAAE,CAAC;MAChBC,mBAAmB,EAAEhD,QAAQ,GAAG,CAAC;MACjCiD,sBAAsB,EAAE,CAAC;MACzB7I,OAAO,EAAE,CACP;QAAEuC,KAAK,EAAE,IAAI;QAAEkF,KAAK,EAAE;MAAC,CAAE,EACzB;QAAElF,KAAK,EAAE,MAAM;QAAEkF,KAAK,EAAE;MAAG,CAAE,EAC7B;QAAElF,KAAK,EAAE,IAAI;QAAEkF,KAAK,EAAE;MAAC,CAAE;IAC1B,EACF;IAEDhI,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACdmF,WAAW,CAAC,QAAQ,EAAE;MAAM,WAAIlG,MAAM,CAAC;QAAEO,KAAK,EAAAA;MAAA,CAAE,CAAC;IAArB,CAAqB,CAAC,CAClD+B,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAC1B4G,IAAI,CAAC;MACJ,IAAI,CAACpG,MAAM,CAACvC,KAAK,CAAC;IACpB,CAAC,CAAC;EACN,CAAC;EAtDa4H,WAAA,CAAA5D,GAAG,GAAG,aAAa;EAuDnC,OAAA4D,WAAC;CAAA,CAxDgClI,QAAQ;SAA5BkI,WAAW;AA+DxB,IAAAgB,UAAA,0BAAAjJ,MAAA;EAA2DC,SAAA,CAAAgJ,UAAA,EAAAjJ,MAAA;EAezD,SAAAiJ,WAAY/I,OAA6B;IACvC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACF,OAAO,CAAC;IATRC,KAAA,CAAAU,IAAI,GAAGV,KAAI,CAACS,WAAW,CAAC,IAAItB,KAAK,CAAC,EAAE,CAAC,CAAC;IAU5Ca,KAAI,CAAC+I,WAAW,GAAG/I,KAAI,CAACiB,UAAU,CAAC+H,IAAI;;EACzC;EAPOF,UAAA,CAAAjI,SAAA,CAAAoI,OAAO,GAAd;IACE,OAAO,IAAI,CAACF,WAAW;EACzB,CAAC;EAODD,UAAA,CAAAjI,SAAA,CAAA0C,MAAM,GAAN;IAAA,IAAAvD,KAAA;IACE,IAAMmB,EAAA,GAA8B,IAAI,CAACF,UAAU;MAA3CiI,QAAQ,GAAA/H,EAAA,CAAA+H,QAAA;MAAKC,UAAU,GAAAC,MAAA,CAAAjI,EAAA,EAAzB,YAA2B,CAAkB;IACnD3B,MAAM,CAAC,IAAI,CAACkB,IAAI,CAAC,CACd6G,SAAS,CAAC,OAAO,CAAC,CAClBF,IAAI,CAAC,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAAC,CACxBrB,IAAI,CACH,UAACC,KAAK;MACJ,OAAAA,KAAK,CACFC,MAAM,CAAC,UAACoB,IAAI;;QACX,IAAMK,IAAI,GAAG,CAAAlI,EAAA,GAAAnB,KAAI,CAACsJ,OAAO,CAACC,IAAI,CAAC,UAACpI,EAAK;cAAL2B,EAAA,GAAA1B,MAAA,CAAAD,EAAA,IAAK;YAAJqI,GAAG,GAAA1G,EAAA;UAAM,OAAA0G,GAAG,KAAKR,IAAI;QAAZ,CAAY,CAAC,cAAA7H,EAAA,uBAAAA,EAAA,CAAG,CAAC,CAAC;QAC5D,IAAI,CAACkI,IAAI,EAAE,MAAM,IAAII,KAAK,CAAC,iBAAAhF,MAAA,CAAiBuE,IAAI,CAAE,CAAC;QACnD,OAAO,IAAIK,IAAI,CAAC,EAAE,CAAC;MACrB,CAAC,CAAC,CACDpH,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CACzB6D,MAAM,CAACqD,UAAU,EAAE,KAAK,CAAC,CACzB1G,MAAM,CAAC,EAAE,CAAC;IARb,CAQa,EACf,UAACA,MAAM;MAAK,OAAAA,MAAM,CAACqD,MAAM,CAAC;QAAEqD,UAAU,EAAAA;MAAA,CAAE,CAAC,CAAC1G,MAAM,CAAC,EAAE,CAAC;IAAxC,CAAwC,EACpD,UAACiH,IAAI;MAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;IAAb,CAAa,CACxB;EACL,CAAC;EAEDb,UAAA,CAAAjI,SAAA,CAAA4C,UAAU,GAAV;IAAA,IAAAzD,KAAA;IACU,IAAAkJ,QAAQ,GAAK,IAAI,CAACjI,UAAU,CAAAiI,QAApB;IAChB,IAAI,CAACvF,gBAAgB,CAAC,OAAO,EAAE,UAACiG,CAAQ;MACtCA,CAAC,CAACC,cAAc,EAAE;MAClBD,CAAC,CAACE,eAAe,EAAE;MACnB,IAAMC,SAAS,GAAG,CAAC/J,KAAI,CAACsJ,OAAO,CAACU,SAAS,CAAC,UAAC7I,EAAK;YAAL2B,EAAA,GAAA1B,MAAA,CAAAD,EAAA,IAAK;UAAJqI,GAAG,GAAA1G,EAAA;QAAM,OAAA0G,GAAG,KAAKxJ,KAAI,CAAC+I,WAAW;MAAxB,CAAwB,CAAC,GAAG,CAAC,IAAI/I,KAAI,CAACsJ,OAAO,CAAChC,MAAM;MACzG,IAAM2C,QAAQ,GAAGjK,KAAI,CAACsJ,OAAO,CAACS,SAAS,CAAC,CAAC,CAAC,CAAC;MAC3Cb,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGlJ,KAAI,CAAC+I,WAAW,CAAC;MAC5B/I,KAAI,CAAC+I,WAAW,GAAGkB,QAAQ;MAC3BjK,KAAI,CAACuD,MAAM,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EA/CauF,UAAA,CAAA5E,GAAG,GAAG,YAAY;EAgDlC,OAAA4E,UAAC;CAAA,CArD0D5J,SAAS;SAA9C4J,UAAU;AAuDhC,IAAAoB,SAAA,0BAAArK,MAAA;EAA+BC,SAAA,CAAAoK,SAAA,EAAArK,MAAA;EAM7B,SAAAqK,UAAYnK,OAA4C;IACtD,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACX,UAAU,CAAC,EAAE,EAAE;MAAEY,KAAK,EAAE;QAAE8I,IAAI,EAAE;MAAM;IAAE,CAAE,EAAEjJ,OAAO,CAAC,CAAC;IAN7DC,KAAA,CAAAsJ,OAAO,GAAqD,CAC1D,CAAC,MAAM,EAAE/C,IAAI,CAAC,EACd,CAAC,OAAO,EAAEC,KAAK,CAAC,CACjB;;EAID;EACF,OAAA0D,SAAC;AAAD,CAAC,CAT8BpB,UAAU;;AAWzC,IAAAqB,aAAA,0BAAAtK,MAAA;EAAmCC,SAAA,CAAAqK,aAAA,EAAAtK,MAAA;EAMjC,SAAAsK,cAAYpK,OAA6C;IACvD,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACX,UAAU,CAAC,EAAE,EAAE;MAAEY,KAAK,EAAE;QAAE8I,IAAI,EAAE;MAAO;IAAE,CAAE,EAAEjJ,OAAO,CAAC,CAAC;IAN9DC,KAAA,CAAAsJ,OAAO,GAAuD,CAC5D,CAAC,OAAO,EAAE7C,KAAK,CAAC,EAChB,CAAC,OAAO,EAAEG,KAAK,CAAC,CACjB;;EAID;EACF,OAAAuD,aAAC;AAAD,CAAC,CATkCrB,UAAU;;AAW7C,IAAAsB,SAAA,0BAAAvK,MAAA;EAA+BC,SAAA,CAAAsK,SAAA,EAAAvK,MAAA;EAM7B,SAAAuK,UAAYrK,OAA6C;IACvD,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACX,UAAU,CAAC,EAAE,EAAE;MAAEY,KAAK,EAAE;QAAE8I,IAAI,EAAE;MAAQ;IAAE,CAAE,EAAEjJ,OAAO,CAAC,CAAC;IAN/DC,KAAA,CAAAsJ,OAAO,GAA8D,CACnE,CAAC,MAAM,EAAExC,SAAS,CAAC,EACnB,CAAC,QAAQ,EAAEM,QAAQ,CAAC,CACrB;;EAID;EACF,OAAAgD,SAAC;AAAD,CAAC,CAT8BtB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}