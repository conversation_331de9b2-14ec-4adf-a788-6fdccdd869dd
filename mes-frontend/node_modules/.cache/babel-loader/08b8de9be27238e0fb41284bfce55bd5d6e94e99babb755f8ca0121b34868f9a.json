{"ast": null, "code": "function substitute(str, o) {\n  if (!str || !o) {\n    return str;\n  }\n  return str.replace(/\\\\?\\{([^{}]+)\\}/g, function (match, name) {\n    if (match.charAt(0) === '\\\\') {\n      return match.slice(1);\n    }\n    return o[name] === undefined ? '' : o[name];\n  });\n}\nexport default substitute;", "map": {"version": 3, "names": ["substitute", "str", "o", "replace", "match", "name", "char<PERSON>t", "slice", "undefined"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/substitute.ts"], "sourcesContent": ["export interface ObjectType<T> {\n  [key: string]: T;\n}\n\nfunction substitute <T>(str: string, o: ObjectType<T>) {\n  if (!str || !o) {\n    return str;\n  }\n  return str.replace(/\\\\?\\{([^{}]+)\\}/g, (match, name): any => {\n    if (match.charAt(0) === '\\\\') {\n      return match.slice(1);\n    }\n    return (o[name] === undefined) ? '' : o[name];\n  });\n}\n\nexport default substitute;\n"], "mappings": "AAIA,SAASA,UAAUA,CAAKC,GAAW,EAAEC,CAAgB;EACnD,IAAI,CAACD,GAAG,IAAI,CAACC,CAAC,EAAE;IACd,OAAOD,GAAG;;EAEZ,OAAOA,GAAG,CAACE,OAAO,CAAC,kBAAkB,EAAE,UAACC,KAAK,EAAEC,IAAI;IACjD,IAAID,KAAK,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MAC5B,OAAOF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC;;IAEvB,OAAQL,CAAC,CAACG,IAAI,CAAC,KAAKG,SAAS,GAAI,EAAE,GAAGN,CAAC,CAACG,IAAI,CAAC;EAC/C,CAAC,CAAC;AACJ;AAEA,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}