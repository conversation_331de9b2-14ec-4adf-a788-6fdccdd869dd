{"ast": null, "code": "var _ = require(\"../lodash\");\nvar util = require(\"../util\");\nmodule.exports = sort;\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function (entry) {\n    return _.has(entry, \"barycenter\");\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function (entry) {\n      return -entry.i;\n    }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n  sortable.sort(compareWithBias(!!biasRight));\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n  var result = {\n    vs: _.flatten(vs, true)\n  };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\nfunction compareWithBias(bias) {\n  return function (entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}", "map": {"version": 3, "names": ["_", "require", "util", "module", "exports", "sort", "entries", "biasRight", "parts", "partition", "entry", "has", "sortable", "lhs", "unsortable", "sortBy", "rhs", "i", "vs", "sum", "weight", "vsIndex", "compareWithBias", "consumeUnsortable", "for<PERSON>ach", "length", "push", "barycenter", "result", "flatten", "index", "last", "pop", "bias", "entryV", "entryW"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/order/sort.js"], "sourcesContent": ["var _ = require(\"../lodash\");\nvar util = require(\"../util\");\n\nmodule.exports = sort;\n\nfunction sort(entries, biasRight) {\n  var parts = util.partition(entries, function(entry) {\n    return _.has(entry, \"barycenter\");\n  });\n  var sortable = parts.lhs,\n    unsortable = _.sortBy(parts.rhs, function(entry) { return -entry.i; }),\n    vs = [],\n    sum = 0,\n    weight = 0,\n    vsIndex = 0;\n\n  sortable.sort(compareWithBias(!!biasRight));\n\n  vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n\n  _.forEach(sortable, function (entry) {\n    vsIndex += entry.vs.length;\n    vs.push(entry.vs);\n    sum += entry.barycenter * entry.weight;\n    weight += entry.weight;\n    vsIndex = consumeUnsortable(vs, unsortable, vsIndex);\n  });\n\n  var result = { vs: _.flatten(vs, true) };\n  if (weight) {\n    result.barycenter = sum / weight;\n    result.weight = weight;\n  }\n  return result;\n}\n\nfunction consumeUnsortable(vs, unsortable, index) {\n  var last;\n  while (unsortable.length && (last = _.last(unsortable)).i <= index) {\n    unsortable.pop();\n    vs.push(last.vs);\n    index++;\n  }\n  return index;\n}\n\nfunction compareWithBias(bias) {\n  return function(entryV, entryW) {\n    if (entryV.barycenter < entryW.barycenter) {\n      return -1;\n    } else if (entryV.barycenter > entryW.barycenter) {\n      return 1;\n    }\n\n    return !bias ? entryV.i - entryW.i : entryW.i - entryV.i;\n  };\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC5B,IAAIC,IAAI,GAAGD,OAAO,CAAC,SAAS,CAAC;AAE7BE,MAAM,CAACC,OAAO,GAAGC,IAAI;AAErB,SAASA,IAAIA,CAACC,OAAO,EAAEC,SAAS,EAAE;EAChC,IAAIC,KAAK,GAAGN,IAAI,CAACO,SAAS,CAACH,OAAO,EAAE,UAASI,KAAK,EAAE;IAClD,OAAOV,CAAC,CAACW,GAAG,CAACD,KAAK,EAAE,YAAY,CAAC;EACnC,CAAC,CAAC;EACF,IAAIE,QAAQ,GAAGJ,KAAK,CAACK,GAAG;IACtBC,UAAU,GAAGd,CAAC,CAACe,MAAM,CAACP,KAAK,CAACQ,GAAG,EAAE,UAASN,KAAK,EAAE;MAAE,OAAO,CAACA,KAAK,CAACO,CAAC;IAAE,CAAC,CAAC;IACtEC,EAAE,GAAG,EAAE;IACPC,GAAG,GAAG,CAAC;IACPC,MAAM,GAAG,CAAC;IACVC,OAAO,GAAG,CAAC;EAEbT,QAAQ,CAACP,IAAI,CAACiB,eAAe,CAAC,CAAC,CAACf,SAAS,CAAC,CAAC;EAE3Cc,OAAO,GAAGE,iBAAiB,CAACL,EAAE,EAAEJ,UAAU,EAAEO,OAAO,CAAC;EAEpDrB,CAAC,CAACwB,OAAO,CAACZ,QAAQ,EAAE,UAAUF,KAAK,EAAE;IACnCW,OAAO,IAAIX,KAAK,CAACQ,EAAE,CAACO,MAAM;IAC1BP,EAAE,CAACQ,IAAI,CAAChB,KAAK,CAACQ,EAAE,CAAC;IACjBC,GAAG,IAAIT,KAAK,CAACiB,UAAU,GAAGjB,KAAK,CAACU,MAAM;IACtCA,MAAM,IAAIV,KAAK,CAACU,MAAM;IACtBC,OAAO,GAAGE,iBAAiB,CAACL,EAAE,EAAEJ,UAAU,EAAEO,OAAO,CAAC;EACtD,CAAC,CAAC;EAEF,IAAIO,MAAM,GAAG;IAAEV,EAAE,EAAElB,CAAC,CAAC6B,OAAO,CAACX,EAAE,EAAE,IAAI;EAAE,CAAC;EACxC,IAAIE,MAAM,EAAE;IACVQ,MAAM,CAACD,UAAU,GAAGR,GAAG,GAAGC,MAAM;IAChCQ,MAAM,CAACR,MAAM,GAAGA,MAAM;EACxB;EACA,OAAOQ,MAAM;AACf;AAEA,SAASL,iBAAiBA,CAACL,EAAE,EAAEJ,UAAU,EAAEgB,KAAK,EAAE;EAChD,IAAIC,IAAI;EACR,OAAOjB,UAAU,CAACW,MAAM,IAAI,CAACM,IAAI,GAAG/B,CAAC,CAAC+B,IAAI,CAACjB,UAAU,CAAC,EAAEG,CAAC,IAAIa,KAAK,EAAE;IAClEhB,UAAU,CAACkB,GAAG,CAAC,CAAC;IAChBd,EAAE,CAACQ,IAAI,CAACK,IAAI,CAACb,EAAE,CAAC;IAChBY,KAAK,EAAE;EACT;EACA,OAAOA,KAAK;AACd;AAEA,SAASR,eAAeA,CAACW,IAAI,EAAE;EAC7B,OAAO,UAASC,MAAM,EAAEC,MAAM,EAAE;IAC9B,IAAID,MAAM,CAACP,UAAU,GAAGQ,MAAM,CAACR,UAAU,EAAE;MACzC,OAAO,CAAC,CAAC;IACX,CAAC,MAAM,IAAIO,MAAM,CAACP,UAAU,GAAGQ,MAAM,CAACR,UAAU,EAAE;MAChD,OAAO,CAAC;IACV;IAEA,OAAO,CAACM,IAAI,GAAGC,MAAM,CAACjB,CAAC,GAAGkB,MAAM,CAAClB,CAAC,GAAGkB,MAAM,CAAClB,CAAC,GAAGiB,MAAM,CAACjB,CAAC;EAC1D,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}