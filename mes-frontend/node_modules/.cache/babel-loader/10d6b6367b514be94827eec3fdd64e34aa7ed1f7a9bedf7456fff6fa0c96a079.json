{"ast": null, "code": "var _jsxFileName = \"/root/mes-system/mes-frontend/src/components/Common/ApiStatus.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Badge, Tooltip, Button } from 'antd';\nimport { WifiOutlined, DisconnectOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { healthCheck } from '../../services/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ApiStatus = ({\n  showText = false,\n  size = 'default'\n}) => {\n  _s();\n  const [status, setStatus] = useState('checking');\n  const [lastCheck, setLastCheck] = useState(null);\n  const checkApiStatus = async () => {\n    setStatus('checking');\n    try {\n      await healthCheck();\n      setStatus('online');\n      setLastCheck(new Date());\n    } catch (error) {\n      setStatus('offline');\n      setLastCheck(new Date());\n    }\n  };\n  useEffect(() => {\n    checkApiStatus();\n\n    // 每30秒检查一次API状态\n    const interval = setInterval(checkApiStatus, 30000);\n    return () => clearInterval(interval);\n  }, []);\n  const getStatusConfig = () => {\n    switch (status) {\n      case 'online':\n        return {\n          status: 'success',\n          text: '在线',\n          icon: /*#__PURE__*/_jsxDEV(WifiOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 17\n          }, this),\n          color: '#52c41a'\n        };\n      case 'offline':\n        return {\n          status: 'error',\n          text: '离线',\n          icon: /*#__PURE__*/_jsxDEV(DisconnectOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 17\n          }, this),\n          color: '#ff4d4f'\n        };\n      case 'checking':\n        return {\n          status: 'processing',\n          text: '检查中',\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {\n            spin: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 17\n          }, this),\n          color: '#1890ff'\n        };\n      default:\n        return {\n          status: 'default',\n          text: '未知',\n          icon: /*#__PURE__*/_jsxDEV(DisconnectOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 17\n          }, this),\n          color: '#d9d9d9'\n        };\n    }\n  };\n  const config = getStatusConfig();\n  const tooltipTitle = /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"API\\u72B6\\u6001: \", config.text]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), lastCheck && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [\"\\u6700\\u540E\\u68C0\\u67E5: \", lastCheck.toLocaleTimeString()]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '4px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        size: \"small\",\n        type: \"text\",\n        icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 17\n        }, this),\n        onClick: checkApiStatus,\n        style: {\n          color: 'white',\n          padding: '0 4px'\n        },\n        children: \"\\u91CD\\u65B0\\u68C0\\u67E5\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 72,\n    columnNumber: 5\n  }, this);\n  if (showText) {\n    return /*#__PURE__*/_jsxDEV(Tooltip, {\n      title: tooltipTitle,\n      children: /*#__PURE__*/_jsxDEV(Badge, {\n        status: config.status,\n        text: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            color: config.color,\n            fontSize: size === 'small' ? '12px' : '14px'\n          },\n          children: [config.icon, \" \", config.text]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Tooltip, {\n    title: tooltipTitle,\n    children: /*#__PURE__*/_jsxDEV(Badge, {\n      status: config.status,\n      style: {\n        cursor: 'pointer'\n      },\n      onClick: checkApiStatus\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(ApiStatus, \"pyN+a+HK6vERTGqkFFKEBwB47Y4=\");\n_c = ApiStatus;\nexport default ApiStatus;\nvar _c;\n$RefreshReg$(_c, \"ApiStatus\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Badge", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "WifiOutlined", "DisconnectOutlined", "ReloadOutlined", "healthCheck", "jsxDEV", "_jsxDEV", "ApiStatus", "showText", "size", "_s", "status", "setStatus", "<PERSON><PERSON><PERSON><PERSON>", "setLastCheck", "checkApiStatus", "Date", "error", "interval", "setInterval", "clearInterval", "getStatusConfig", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "spin", "config", "tooltipTitle", "children", "toLocaleTimeString", "style", "marginTop", "type", "onClick", "padding", "title", "fontSize", "cursor", "_c", "$RefreshReg$"], "sources": ["/root/mes-system/mes-frontend/src/components/Common/ApiStatus.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, Toolt<PERSON>, But<PERSON> } from 'antd';\nimport { WifiOutlined, DisconnectOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { healthCheck } from '../../services/api';\n\ninterface ApiStatusProps {\n  showText?: boolean;\n  size?: 'small' | 'default';\n}\n\nconst ApiStatus: React.FC<ApiStatusProps> = ({ showText = false, size = 'default' }) => {\n  const [status, setStatus] = useState<'online' | 'offline' | 'checking'>('checking');\n  const [lastCheck, setLastCheck] = useState<Date | null>(null);\n\n  const checkApiStatus = async () => {\n    setStatus('checking');\n    try {\n      await healthCheck();\n      setStatus('online');\n      setLastCheck(new Date());\n    } catch (error) {\n      setStatus('offline');\n      setLastCheck(new Date());\n    }\n  };\n\n  useEffect(() => {\n    checkApiStatus();\n    \n    // 每30秒检查一次API状态\n    const interval = setInterval(checkApiStatus, 30000);\n    \n    return () => clearInterval(interval);\n  }, []);\n\n  const getStatusConfig = () => {\n    switch (status) {\n      case 'online':\n        return {\n          status: 'success' as const,\n          text: '在线',\n          icon: <WifiOutlined />,\n          color: '#52c41a',\n        };\n      case 'offline':\n        return {\n          status: 'error' as const,\n          text: '离线',\n          icon: <DisconnectOutlined />,\n          color: '#ff4d4f',\n        };\n      case 'checking':\n        return {\n          status: 'processing' as const,\n          text: '检查中',\n          icon: <ReloadOutlined spin />,\n          color: '#1890ff',\n        };\n      default:\n        return {\n          status: 'default' as const,\n          text: '未知',\n          icon: <DisconnectOutlined />,\n          color: '#d9d9d9',\n        };\n    }\n  };\n\n  const config = getStatusConfig();\n  \n  const tooltipTitle = (\n    <div>\n      <div>API状态: {config.text}</div>\n      {lastCheck && (\n        <div>最后检查: {lastCheck.toLocaleTimeString()}</div>\n      )}\n      <div style={{ marginTop: '4px' }}>\n        <Button \n          size=\"small\" \n          type=\"text\" \n          icon={<ReloadOutlined />}\n          onClick={checkApiStatus}\n          style={{ color: 'white', padding: '0 4px' }}\n        >\n          重新检查\n        </Button>\n      </div>\n    </div>\n  );\n\n  if (showText) {\n    return (\n      <Tooltip title={tooltipTitle}>\n        <Badge \n          status={config.status} \n          text={\n            <span style={{ color: config.color, fontSize: size === 'small' ? '12px' : '14px' }}>\n              {config.icon} {config.text}\n            </span>\n          } \n        />\n      </Tooltip>\n    );\n  }\n\n  return (\n    <Tooltip title={tooltipTitle}>\n      <Badge \n        status={config.status}\n        style={{ cursor: 'pointer' }}\n        onClick={checkApiStatus}\n      />\n    </Tooltip>\n  );\n};\n\nexport default ApiStatus;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,OAAO,EAAEC,MAAM,QAAQ,MAAM;AAC7C,SAASC,YAAY,EAAEC,kBAAkB,EAAEC,cAAc,QAAQ,mBAAmB;AACpF,SAASC,WAAW,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOjD,MAAMC,SAAmC,GAAGA,CAAC;EAAEC,QAAQ,GAAG,KAAK;EAAEC,IAAI,GAAG;AAAU,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAoC,UAAU,CAAC;EACnF,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAc,IAAI,CAAC;EAE7D,MAAMmB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCH,SAAS,CAAC,UAAU,CAAC;IACrB,IAAI;MACF,MAAMR,WAAW,CAAC,CAAC;MACnBQ,SAAS,CAAC,QAAQ,CAAC;MACnBE,YAAY,CAAC,IAAIE,IAAI,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdL,SAAS,CAAC,SAAS,CAAC;MACpBE,YAAY,CAAC,IAAIE,IAAI,CAAC,CAAC,CAAC;IAC1B;EACF,CAAC;EAEDnB,SAAS,CAAC,MAAM;IACdkB,cAAc,CAAC,CAAC;;IAEhB;IACA,MAAMG,QAAQ,GAAGC,WAAW,CAACJ,cAAc,EAAE,KAAK,CAAC;IAEnD,OAAO,MAAMK,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQV,MAAM;MACZ,KAAK,QAAQ;QACX,OAAO;UACLA,MAAM,EAAE,SAAkB;UAC1BW,IAAI,EAAE,IAAI;UACVC,IAAI,eAAEjB,OAAA,CAACL,YAAY;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACtBC,KAAK,EAAE;QACT,CAAC;MACH,KAAK,SAAS;QACZ,OAAO;UACLjB,MAAM,EAAE,OAAgB;UACxBW,IAAI,EAAE,IAAI;UACVC,IAAI,eAAEjB,OAAA,CAACJ,kBAAkB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAC5BC,KAAK,EAAE;QACT,CAAC;MACH,KAAK,UAAU;QACb,OAAO;UACLjB,MAAM,EAAE,YAAqB;UAC7BW,IAAI,EAAE,KAAK;UACXC,IAAI,eAAEjB,OAAA,CAACH,cAAc;YAAC0B,IAAI;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAC7BC,KAAK,EAAE;QACT,CAAC;MACH;QACE,OAAO;UACLjB,MAAM,EAAE,SAAkB;UAC1BW,IAAI,EAAE,IAAI;UACVC,IAAI,eAAEjB,OAAA,CAACJ,kBAAkB;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAC5BC,KAAK,EAAE;QACT,CAAC;IACL;EACF,CAAC;EAED,MAAME,MAAM,GAAGT,eAAe,CAAC,CAAC;EAEhC,MAAMU,YAAY,gBAChBzB,OAAA;IAAA0B,QAAA,gBACE1B,OAAA;MAAA0B,QAAA,GAAK,mBAAO,EAACF,MAAM,CAACR,IAAI;IAAA;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAC9Bd,SAAS,iBACRP,OAAA;MAAA0B,QAAA,GAAK,4BAAM,EAACnB,SAAS,CAACoB,kBAAkB,CAAC,CAAC;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACjD,eACDrB,OAAA;MAAK4B,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAE;MAAAH,QAAA,eAC/B1B,OAAA,CAACN,MAAM;QACLS,IAAI,EAAC,OAAO;QACZ2B,IAAI,EAAC,MAAM;QACXb,IAAI,eAAEjB,OAAA,CAACH,cAAc;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBU,OAAO,EAAEtB,cAAe;QACxBmB,KAAK,EAAE;UAAEN,KAAK,EAAE,OAAO;UAAEU,OAAO,EAAE;QAAQ,CAAE;QAAAN,QAAA,EAC7C;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;EAED,IAAInB,QAAQ,EAAE;IACZ,oBACEF,OAAA,CAACP,OAAO;MAACwC,KAAK,EAAER,YAAa;MAAAC,QAAA,eAC3B1B,OAAA,CAACR,KAAK;QACJa,MAAM,EAAEmB,MAAM,CAACnB,MAAO;QACtBW,IAAI,eACFhB,OAAA;UAAM4B,KAAK,EAAE;YAAEN,KAAK,EAAEE,MAAM,CAACF,KAAK;YAAEY,QAAQ,EAAE/B,IAAI,KAAK,OAAO,GAAG,MAAM,GAAG;UAAO,CAAE;UAAAuB,QAAA,GAChFF,MAAM,CAACP,IAAI,EAAC,GAAC,EAACO,MAAM,CAACR,IAAI;QAAA;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MACP;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC;EAEd;EAEA,oBACErB,OAAA,CAACP,OAAO;IAACwC,KAAK,EAAER,YAAa;IAAAC,QAAA,eAC3B1B,OAAA,CAACR,KAAK;MACJa,MAAM,EAAEmB,MAAM,CAACnB,MAAO;MACtBuB,KAAK,EAAE;QAAEO,MAAM,EAAE;MAAU,CAAE;MAC7BJ,OAAO,EAAEtB;IAAe;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEd,CAAC;AAACjB,EAAA,CAxGIH,SAAmC;AAAAmC,EAAA,GAAnCnC,SAAmC;AA0GzC,eAAeA,SAAS;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}