{"ast": null, "code": "export function weight(a, b) {\n  return b.value - a.value;\n}\nexport function frequency(a, b) {\n  return b.frequency - a.frequency;\n}\nexport function id(a, b) {\n  return `${a.id}`.localeCompare(`${b.id}`);\n}\nexport function name(a, b) {\n  return `${a.name}`.localeCompare(`${b.name}`);\n}", "map": {"version": 3, "names": ["weight", "a", "b", "value", "frequency", "id", "localeCompare", "name"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/arc/sort.ts"], "sourcesContent": ["export function weight(a, b) {\n  return b.value - a.value;\n}\n\nexport function frequency(a, b) {\n  return b.frequency - a.frequency;\n}\n\nexport function id(a, b) {\n  return `${a.id}`.localeCompare(`${b.id}`);\n}\n\nexport function name(a, b) {\n  return `${a.name}`.localeCompare(`${b.name}`);\n}\n"], "mappings": "AAAA,OAAM,SAAUA,MAAMA,CAACC,CAAC,EAAEC,CAAC;EACzB,OAAOA,CAAC,CAACC,KAAK,GAAGF,CAAC,CAACE,KAAK;AAC1B;AAEA,OAAM,SAAUC,SAASA,CAACH,CAAC,EAAEC,CAAC;EAC5B,OAAOA,CAAC,CAACE,SAAS,GAAGH,CAAC,CAACG,SAAS;AAClC;AAEA,OAAM,SAAUC,EAAEA,CAACJ,CAAC,EAAEC,CAAC;EACrB,OAAO,GAAGD,CAAC,CAACI,EAAE,EAAE,CAACC,aAAa,CAAC,GAAGJ,CAAC,CAACG,EAAE,EAAE,CAAC;AAC3C;AAEA,OAAM,SAAUE,IAAIA,CAACN,CAAC,EAAEC,CAAC;EACvB,OAAO,GAAGD,CAAC,CAACM,IAAI,EAAE,CAACD,aAAa,CAAC,GAAGJ,CAAC,CAACK,IAAI,EAAE,CAAC;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}