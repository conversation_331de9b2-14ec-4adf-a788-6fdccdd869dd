{"ast": null, "code": "var isNull = function (value) {\n  return value === null;\n};\nexport default isNull;", "map": {"version": 3, "names": ["isNull", "value"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-null.ts"], "sourcesContent": ["const isNull = function(value): value is null {\n  return value === null;\n};\n\nexport default isNull;\n"], "mappings": "AAAA,IAAMA,MAAM,GAAG,SAAAA,CAASC,KAAK;EAC3B,OAAOA,KAAK,KAAK,IAAI;AACvB,CAAC;AAED,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}