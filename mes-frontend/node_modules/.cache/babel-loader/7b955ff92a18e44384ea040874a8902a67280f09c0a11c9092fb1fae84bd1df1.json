{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport { useState, useEffect, useCallback, useRef } from 'react';\nexport function useApi(apiFunction, options = {}) {\n  _s();\n  const {\n    immediate = false,\n    onSuccess,\n    onError,\n    deps = [],\n    pollingInterval\n  } = options;\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastArgs, setLastArgs] = useState([]);\n  const pollingRef = useRef(null);\n  const mountedRef = useRef(true);\n  const execute = useCallback(async (...args) => {\n    if (!mountedRef.current) return null;\n    setLoading(true);\n    setError(null);\n    setLastArgs(args);\n    try {\n      const response = await apiFunction(...args);\n      if (!mountedRef.current) return null;\n      if (response.success && response.data !== undefined) {\n        setData(response.data);\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(response.data);\n        return response.data;\n      } else {\n        const errorMsg = response.message || 'API调用失败';\n        const error = new Error(errorMsg);\n        setError(error);\n        onError === null || onError === void 0 ? void 0 : onError(error);\n        return null;\n      }\n    } catch (err) {\n      if (!mountedRef.current) return null;\n      const error = err instanceof Error ? err : new Error('未知错误');\n      setError(error);\n      onError === null || onError === void 0 ? void 0 : onError(error);\n      return null;\n    } finally {\n      if (mountedRef.current) {\n        setLoading(false);\n      }\n    }\n  }, [apiFunction, onSuccess, onError]);\n  const refresh = useCallback(() => {\n    return execute(...lastArgs);\n  }, [execute, lastArgs]);\n  const reset = useCallback(() => {\n    setData(null);\n    setError(null);\n    setLoading(false);\n    setLastArgs([]);\n    if (pollingRef.current) {\n      clearInterval(pollingRef.current);\n      pollingRef.current = null;\n    }\n  }, []);\n\n  // 轮询功能\n  useEffect(() => {\n    if (pollingInterval && data !== null) {\n      pollingRef.current = setInterval(() => {\n        refresh();\n      }, pollingInterval);\n      return () => {\n        if (pollingRef.current) {\n          clearInterval(pollingRef.current);\n          pollingRef.current = null;\n        }\n      };\n    }\n  }, [pollingInterval, data, refresh]);\n\n  // 依赖变化时重新执行\n  useEffect(() => {\n    if (immediate) {\n      execute();\n    }\n  }, [immediate, ...deps]);\n\n  // 组件卸载时清理\n  useEffect(() => {\n    return () => {\n      mountedRef.current = false;\n      if (pollingRef.current) {\n        clearInterval(pollingRef.current);\n      }\n    };\n  }, []);\n  return {\n    data,\n    loading,\n    error,\n    execute,\n    refresh,\n    reset\n  };\n}\n\n// 专门用于列表数据的Hook\n_s(useApi, \"G8gWm48YS5edWHI1FwrYp4J1Zx4=\");\nexport function useApiList(apiFunction, options = {}) {\n  _s2();\n  var _result$data, _result$data2;\n  const result = useApi(apiFunction, options);\n  return {\n    ...result,\n    list: ((_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.data) || [],\n    total: ((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : _result$data2.total) || 0\n  };\n}\n\n// 用于分页数据的Hook\n_s2(useApiList, \"CqBSGkqJNsGV88+vjC/RaZfamkA=\", false, function () {\n  return [useApi];\n});\nexport function usePagination(apiFunction, initialParams = {}) {\n  _s3();\n  const [params, setParams] = useState({\n    page: 1,\n    limit: 10,\n    ...initialParams\n  });\n  const {\n    data,\n    loading,\n    error,\n    execute,\n    refresh\n  } = useApi(searchParams => apiFunction({\n    ...params,\n    ...searchParams\n  }), {\n    immediate: true,\n    deps: [params]\n  });\n  const changePage = useCallback((page, pageSize) => {\n    setParams(prev => ({\n      ...prev,\n      page,\n      ...(pageSize && {\n        limit: pageSize\n      })\n    }));\n  }, []);\n  const changeParams = useCallback(newParams => {\n    setParams(prev => ({\n      ...prev,\n      ...newParams,\n      page: 1 // 重置到第一页\n    }));\n  }, []);\n  return {\n    list: (data === null || data === void 0 ? void 0 : data.data) || [],\n    total: (data === null || data === void 0 ? void 0 : data.total) || 0,\n    loading,\n    error,\n    params,\n    changePage,\n    changeParams,\n    refresh: () => refresh(),\n    execute: searchParams => execute(searchParams)\n  };\n}\n_s3(usePagination, \"XR+0vX4/JqdYQSK9u1Sqwhk/iZQ=\", false, function () {\n  return [useApi];\n});", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "useApi", "apiFunction", "options", "_s", "immediate", "onSuccess", "onError", "deps", "pollingInterval", "data", "setData", "loading", "setLoading", "error", "setError", "lastArgs", "setLastArgs", "pollingRef", "mountedRef", "execute", "args", "current", "response", "success", "undefined", "errorMsg", "message", "Error", "err", "refresh", "reset", "clearInterval", "setInterval", "useApiList", "_s2", "_result$data", "_result$data2", "result", "list", "total", "usePagination", "initialParams", "_s3", "params", "setParams", "page", "limit", "searchParams", "changePage", "pageSize", "prev", "changeParams", "newParams"], "sources": ["/root/mes-system/mes-frontend/src/hooks/useApi.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\nimport { message } from 'antd';\n\ninterface UseApiOptions<T> {\n  immediate?: boolean;\n  onSuccess?: (data: T) => void;\n  onError?: (error: Error) => void;\n  deps?: any[];\n  pollingInterval?: number;\n}\n\ninterface UseApiResult<T> {\n  data: T | null;\n  loading: boolean;\n  error: Error | null;\n  execute: (...args: any[]) => Promise<T | null>;\n  refresh: () => Promise<T | null>;\n  reset: () => void;\n}\n\nexport function useApi<T>(\n  apiFunction: (...args: any[]) => Promise<{ success: boolean; data?: T; message?: string }>,\n  options: UseApiOptions<T> = {}\n): UseApiResult<T> {\n  const {\n    immediate = false,\n    onSuccess,\n    onError,\n    deps = [],\n    pollingInterval,\n  } = options;\n\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n  const [lastArgs, setLastArgs] = useState<any[]>([]);\n  \n  const pollingRef = useRef<NodeJS.Timeout | null>(null);\n  const mountedRef = useRef(true);\n\n  const execute = useCallback(async (...args: any[]): Promise<T | null> => {\n    if (!mountedRef.current) return null;\n    \n    setLoading(true);\n    setError(null);\n    setLastArgs(args);\n\n    try {\n      const response = await apiFunction(...args);\n      \n      if (!mountedRef.current) return null;\n\n      if (response.success && response.data !== undefined) {\n        setData(response.data);\n        onSuccess?.(response.data);\n        return response.data;\n      } else {\n        const errorMsg = response.message || 'API调用失败';\n        const error = new Error(errorMsg);\n        setError(error);\n        onError?.(error);\n        return null;\n      }\n    } catch (err) {\n      if (!mountedRef.current) return null;\n      \n      const error = err instanceof Error ? err : new Error('未知错误');\n      setError(error);\n      onError?.(error);\n      return null;\n    } finally {\n      if (mountedRef.current) {\n        setLoading(false);\n      }\n    }\n  }, [apiFunction, onSuccess, onError]);\n\n  const refresh = useCallback(() => {\n    return execute(...lastArgs);\n  }, [execute, lastArgs]);\n\n  const reset = useCallback(() => {\n    setData(null);\n    setError(null);\n    setLoading(false);\n    setLastArgs([]);\n    \n    if (pollingRef.current) {\n      clearInterval(pollingRef.current);\n      pollingRef.current = null;\n    }\n  }, []);\n\n  // 轮询功能\n  useEffect(() => {\n    if (pollingInterval && data !== null) {\n      pollingRef.current = setInterval(() => {\n        refresh();\n      }, pollingInterval);\n\n      return () => {\n        if (pollingRef.current) {\n          clearInterval(pollingRef.current);\n          pollingRef.current = null;\n        }\n      };\n    }\n  }, [pollingInterval, data, refresh]);\n\n  // 依赖变化时重新执行\n  useEffect(() => {\n    if (immediate) {\n      execute();\n    }\n  }, [immediate, ...deps]);\n\n  // 组件卸载时清理\n  useEffect(() => {\n    return () => {\n      mountedRef.current = false;\n      if (pollingRef.current) {\n        clearInterval(pollingRef.current);\n      }\n    };\n  }, []);\n\n  return {\n    data,\n    loading,\n    error,\n    execute,\n    refresh,\n    reset,\n  };\n}\n\n// 专门用于列表数据的Hook\nexport function useApiList<T>(\n  apiFunction: (...args: any[]) => Promise<{ success: boolean; data?: { data: T[]; total: number }; message?: string }>,\n  options: UseApiOptions<{ data: T[]; total: number }> = {}\n) {\n  const result = useApi(apiFunction, options);\n  \n  return {\n    ...result,\n    list: result.data?.data || [],\n    total: result.data?.total || 0,\n  };\n}\n\n// 用于分页数据的Hook\nexport function usePagination<T>(\n  apiFunction: (params: any) => Promise<{ success: boolean; data?: { data: T[]; total: number }; message?: string }>,\n  initialParams: any = {}\n) {\n  const [params, setParams] = useState({ page: 1, limit: 10, ...initialParams });\n  \n  const { data, loading, error, execute, refresh } = useApi(\n    (searchParams) => apiFunction({ ...params, ...searchParams }),\n    { immediate: true, deps: [params] }\n  );\n\n  const changePage = useCallback((page: number, pageSize?: number) => {\n    setParams(prev => ({\n      ...prev,\n      page,\n      ...(pageSize && { limit: pageSize }),\n    }));\n  }, []);\n\n  const changeParams = useCallback((newParams: any) => {\n    setParams(prev => ({\n      ...prev,\n      ...newParams,\n      page: 1, // 重置到第一页\n    }));\n  }, []);\n\n  return {\n    list: data?.data || [],\n    total: data?.total || 0,\n    loading,\n    error,\n    params,\n    changePage,\n    changeParams,\n    refresh: () => refresh(),\n    execute: (searchParams?: any) => execute(searchParams),\n  };\n}\n"], "mappings": ";;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAoBhE,OAAO,SAASC,MAAMA,CACpBC,WAA0F,EAC1FC,OAAyB,GAAG,CAAC,CAAC,EACb;EAAAC,EAAA;EACjB,MAAM;IACJC,SAAS,GAAG,KAAK;IACjBC,SAAS;IACTC,OAAO;IACPC,IAAI,GAAG,EAAE;IACTC;EACF,CAAC,GAAGN,OAAO;EAEX,MAAM,CAACO,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAW,IAAI,CAAC;EAChD,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAe,IAAI,CAAC;EACtD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAQ,EAAE,CAAC;EAEnD,MAAMqB,UAAU,GAAGlB,MAAM,CAAwB,IAAI,CAAC;EACtD,MAAMmB,UAAU,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAE/B,MAAMoB,OAAO,GAAGrB,WAAW,CAAC,OAAO,GAAGsB,IAAW,KAAwB;IACvE,IAAI,CAACF,UAAU,CAACG,OAAO,EAAE,OAAO,IAAI;IAEpCT,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACdE,WAAW,CAACI,IAAI,CAAC;IAEjB,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMrB,WAAW,CAAC,GAAGmB,IAAI,CAAC;MAE3C,IAAI,CAACF,UAAU,CAACG,OAAO,EAAE,OAAO,IAAI;MAEpC,IAAIC,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACb,IAAI,KAAKe,SAAS,EAAE;QACnDd,OAAO,CAACY,QAAQ,CAACb,IAAI,CAAC;QACtBJ,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGiB,QAAQ,CAACb,IAAI,CAAC;QAC1B,OAAOa,QAAQ,CAACb,IAAI;MACtB,CAAC,MAAM;QACL,MAAMgB,QAAQ,GAAGH,QAAQ,CAACI,OAAO,IAAI,SAAS;QAC9C,MAAMb,KAAK,GAAG,IAAIc,KAAK,CAACF,QAAQ,CAAC;QACjCX,QAAQ,CAACD,KAAK,CAAC;QACfP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGO,KAAK,CAAC;QAChB,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZ,IAAI,CAACV,UAAU,CAACG,OAAO,EAAE,OAAO,IAAI;MAEpC,MAAMR,KAAK,GAAGe,GAAG,YAAYD,KAAK,GAAGC,GAAG,GAAG,IAAID,KAAK,CAAC,MAAM,CAAC;MAC5Db,QAAQ,CAACD,KAAK,CAAC;MACfP,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGO,KAAK,CAAC;MAChB,OAAO,IAAI;IACb,CAAC,SAAS;MACR,IAAIK,UAAU,CAACG,OAAO,EAAE;QACtBT,UAAU,CAAC,KAAK,CAAC;MACnB;IACF;EACF,CAAC,EAAE,CAACX,WAAW,EAAEI,SAAS,EAAEC,OAAO,CAAC,CAAC;EAErC,MAAMuB,OAAO,GAAG/B,WAAW,CAAC,MAAM;IAChC,OAAOqB,OAAO,CAAC,GAAGJ,QAAQ,CAAC;EAC7B,CAAC,EAAE,CAACI,OAAO,EAAEJ,QAAQ,CAAC,CAAC;EAEvB,MAAMe,KAAK,GAAGhC,WAAW,CAAC,MAAM;IAC9BY,OAAO,CAAC,IAAI,CAAC;IACbI,QAAQ,CAAC,IAAI,CAAC;IACdF,UAAU,CAAC,KAAK,CAAC;IACjBI,WAAW,CAAC,EAAE,CAAC;IAEf,IAAIC,UAAU,CAACI,OAAO,EAAE;MACtBU,aAAa,CAACd,UAAU,CAACI,OAAO,CAAC;MACjCJ,UAAU,CAACI,OAAO,GAAG,IAAI;IAC3B;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxB,SAAS,CAAC,MAAM;IACd,IAAIW,eAAe,IAAIC,IAAI,KAAK,IAAI,EAAE;MACpCQ,UAAU,CAACI,OAAO,GAAGW,WAAW,CAAC,MAAM;QACrCH,OAAO,CAAC,CAAC;MACX,CAAC,EAAErB,eAAe,CAAC;MAEnB,OAAO,MAAM;QACX,IAAIS,UAAU,CAACI,OAAO,EAAE;UACtBU,aAAa,CAACd,UAAU,CAACI,OAAO,CAAC;UACjCJ,UAAU,CAACI,OAAO,GAAG,IAAI;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,CAACb,eAAe,EAAEC,IAAI,EAAEoB,OAAO,CAAC,CAAC;;EAEpC;EACAhC,SAAS,CAAC,MAAM;IACd,IAAIO,SAAS,EAAE;MACbe,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACf,SAAS,EAAE,GAAGG,IAAI,CAAC,CAAC;;EAExB;EACAV,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXqB,UAAU,CAACG,OAAO,GAAG,KAAK;MAC1B,IAAIJ,UAAU,CAACI,OAAO,EAAE;QACtBU,aAAa,CAACd,UAAU,CAACI,OAAO,CAAC;MACnC;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLZ,IAAI;IACJE,OAAO;IACPE,KAAK;IACLM,OAAO;IACPU,OAAO;IACPC;EACF,CAAC;AACH;;AAEA;AAAA3B,EAAA,CApHgBH,MAAM;AAqHtB,OAAO,SAASiC,UAAUA,CACxBhC,WAAqH,EACrHC,OAAoD,GAAG,CAAC,CAAC,EACzD;EAAAgC,GAAA;EAAA,IAAAC,YAAA,EAAAC,aAAA;EACA,MAAMC,MAAM,GAAGrC,MAAM,CAACC,WAAW,EAAEC,OAAO,CAAC;EAE3C,OAAO;IACL,GAAGmC,MAAM;IACTC,IAAI,EAAE,EAAAH,YAAA,GAAAE,MAAM,CAAC5B,IAAI,cAAA0B,YAAA,uBAAXA,YAAA,CAAa1B,IAAI,KAAI,EAAE;IAC7B8B,KAAK,EAAE,EAAAH,aAAA,GAAAC,MAAM,CAAC5B,IAAI,cAAA2B,aAAA,uBAAXA,aAAA,CAAaG,KAAK,KAAI;EAC/B,CAAC;AACH;;AAEA;AAAAL,GAAA,CAbgBD,UAAU;EAAA,QAITjC,MAAM;AAAA;AAUvB,OAAO,SAASwC,aAAaA,CAC3BvC,WAAkH,EAClHwC,aAAkB,GAAG,CAAC,CAAC,EACvB;EAAAC,GAAA;EACA,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC;IAAEiD,IAAI,EAAE,CAAC;IAAEC,KAAK,EAAE,EAAE;IAAE,GAAGL;EAAc,CAAC,CAAC;EAE9E,MAAM;IAAEhC,IAAI;IAAEE,OAAO;IAAEE,KAAK;IAAEM,OAAO;IAAEU;EAAQ,CAAC,GAAG7B,MAAM,CACtD+C,YAAY,IAAK9C,WAAW,CAAC;IAAE,GAAG0C,MAAM;IAAE,GAAGI;EAAa,CAAC,CAAC,EAC7D;IAAE3C,SAAS,EAAE,IAAI;IAAEG,IAAI,EAAE,CAACoC,MAAM;EAAE,CACpC,CAAC;EAED,MAAMK,UAAU,GAAGlD,WAAW,CAAC,CAAC+C,IAAY,EAAEI,QAAiB,KAAK;IAClEL,SAAS,CAACM,IAAI,KAAK;MACjB,GAAGA,IAAI;MACPL,IAAI;MACJ,IAAII,QAAQ,IAAI;QAAEH,KAAK,EAAEG;MAAS,CAAC;IACrC,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,YAAY,GAAGrD,WAAW,CAAEsD,SAAc,IAAK;IACnDR,SAAS,CAACM,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,GAAGE,SAAS;MACZP,IAAI,EAAE,CAAC,CAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLP,IAAI,EAAE,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEA,IAAI,KAAI,EAAE;IACtB8B,KAAK,EAAE,CAAA9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8B,KAAK,KAAI,CAAC;IACvB5B,OAAO;IACPE,KAAK;IACL8B,MAAM;IACNK,UAAU;IACVG,YAAY;IACZtB,OAAO,EAAEA,CAAA,KAAMA,OAAO,CAAC,CAAC;IACxBV,OAAO,EAAG4B,YAAkB,IAAK5B,OAAO,CAAC4B,YAAY;EACvD,CAAC;AACH;AAACL,GAAA,CAtCeF,aAAa;EAAA,QAMwBxC,MAAM;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}