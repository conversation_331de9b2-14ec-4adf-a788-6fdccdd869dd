{"ast": null, "code": "import { __assign, __extends, __read, __rest, __spreadArray } from \"tslib\";\nimport { clamp } from '@antv/util';\nimport { Component } from '../../core';\nimport { Line } from '../../shapes';\nimport { BBox, deepAssign, subStyleProps, formatTime } from '../../util';\nimport { Axis } from '../axis';\nimport { Slider } from '../slider';\nimport { Controller } from './controller';\nimport { ChartModeHandle, TimeModeHandle } from './handle';\nimport { labelFormatter, parseBySeries } from './utils';\nvar Timebar = /** @class */function (_super) {\n  __extends(Timebar, _super);\n  function Timebar(options) {\n    var _this = _super.call(this, deepAssign({}, Timebar.defaultOptions, options)) || this;\n    _this.axis = _this.appendChild(new Axis({\n      style: {\n        type: 'linear',\n        startPos: [0, 0],\n        endPos: [0, 0],\n        data: [],\n        showArrow: false,\n        animate: false\n      }\n    }));\n    /** 时间线 group，用于放置 timeline 或者 chart */\n    _this.timeline = _this.appendChild(new Slider({\n      style: {\n        onChange: function (values) {\n          _this.handleSliderChange(values);\n        }\n      }\n    }));\n    _this.controller = _this.appendChild(new Controller({}));\n    _this.states = {};\n    _this.handleSliderChange = function (values) {\n      var prevValues = function () {\n        var val = _this.states.values;\n        if (Array.isArray(val)) return __spreadArray([], __read(val), false);\n        return val;\n      }();\n      _this.setBySliderValues(values);\n      _this.dispatchOnChange(prevValues);\n    };\n    var _a = _this.attributes,\n      selectionType = _a.selectionType,\n      chartType = _a.chartType,\n      speed = _a.speed,\n      state = _a.state,\n      playMode = _a.playMode,\n      values = _a.values;\n    _this.states = {\n      chartType: chartType,\n      playMode: playMode,\n      selectionType: selectionType,\n      speed: speed,\n      state: state\n    };\n    _this.setByTimebarValues(values);\n    return _this;\n  }\n  Object.defineProperty(Timebar.prototype, \"data\", {\n    get: function () {\n      var data = this.attributes.data;\n      var compareFn = function (a, b) {\n        if (a.time < b.time) return -1;\n        if (a.time > b.time) return 1;\n        return 0;\n      };\n      return data.sort(compareFn);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Timebar.prototype, \"space\", {\n    /** 计算空间分配 */\n    get: function () {\n      var _a = this.attributes,\n        x = _a.x,\n        y = _a.y,\n        width = _a.width,\n        height = _a.height,\n        type = _a.type,\n        controllerHeight = _a.controllerHeight;\n      var availableTimelineHeight = clamp(+height - controllerHeight, 0, +height);\n      var controllerBBox = new BBox(x, y + +height - controllerHeight, +width, controllerHeight);\n      // chart 模式下可用\n      var axisBBox;\n      var axisHeight = 0;\n      if (type === 'chart') {\n        // axis 默认分配高度为 35\n        axisHeight = 35;\n        axisBBox = new BBox(x, y + availableTimelineHeight - axisHeight, +width, axisHeight);\n      } else axisBBox = new BBox();\n      var timelineHeight = type === 'time' ? 10 : availableTimelineHeight;\n      var timelineBBox = new BBox(x, y + (type === 'time' ? availableTimelineHeight : availableTimelineHeight - timelineHeight), +width, timelineHeight - axisHeight);\n      return {\n        axisBBox: axisBBox,\n        controllerBBox: controllerBBox,\n        timelineBBox: timelineBBox\n      };\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Timebar.prototype.setBySliderValues = function (val) {\n    var _a, _b;\n    var data = this.data;\n    var _c = __read(Array.isArray(val) ? val : [0, val], 2),\n      startRatio = _c[0],\n      endRatio = _c[1];\n    var length = data.length;\n    var startDatum = data[Math.floor(startRatio * length)];\n    var endDatum = data[Math.ceil(endRatio * length) - (Array.isArray(val) ? 0 : 1)];\n    // 如果 endDatum 不存在，则其已经比最大的时间范围更大\n    this.states.values = [(_a = startDatum === null || startDatum === void 0 ? void 0 : startDatum.time) !== null && _a !== void 0 ? _a : data[0].time, (_b = endDatum === null || endDatum === void 0 ? void 0 : endDatum.time) !== null && _b !== void 0 ? _b : Infinity];\n  };\n  Timebar.prototype.setByTimebarValues = function (val) {\n    var _a, _b, _c;\n    var data = this.data;\n    var _d = __read(Array.isArray(val) ? val : [undefined, val], 2),\n      start = _d[0],\n      end = _d[1];\n    var startDatum = data.find(function (_a) {\n      var time = _a.time;\n      return time === start;\n    });\n    var endDatum = data.find(function (_a) {\n      var time = _a.time;\n      return time === end;\n    });\n    this.states.values = [(_a = startDatum === null || startDatum === void 0 ? void 0 : startDatum.time) !== null && _a !== void 0 ? _a : (_b = data[0]) === null || _b === void 0 ? void 0 : _b.time, (_c = endDatum === null || endDatum === void 0 ? void 0 : endDatum.time) !== null && _c !== void 0 ? _c : Infinity];\n  };\n  Timebar.prototype.setByIndex = function (index) {\n    var _a, _b, _c, _d;\n    var data = this.data;\n    var _e = __read(index, 2),\n      startIndex = _e[0],\n      endIndex = _e[1];\n    this.states.values = [(_b = (_a = data[startIndex]) === null || _a === void 0 ? void 0 : _a.time) !== null && _b !== void 0 ? _b : data[0].time, (_d = (_c = this.data[endIndex]) === null || _c === void 0 ? void 0 : _c.time) !== null && _d !== void 0 ? _d : Infinity];\n  };\n  Object.defineProperty(Timebar.prototype, \"sliderValues\", {\n    /**\n     * 获取 timebar 的 values\n     */\n    get: function () {\n      var _a = this.states,\n        values = _a.values,\n        selectionType = _a.selectionType;\n      var _b = __read(Array.isArray(values) ? values : [undefined, values], 2),\n        start = _b[0],\n        end = _b[1];\n      var data = this.data;\n      var length = data.length;\n      var isValue = selectionType === 'value';\n      var getStartValue = function () {\n        var startDatumIndex = data.findIndex(function (_a) {\n          var time = _a.time;\n          return time === start;\n        });\n        if (isValue) return 0;\n        if (startDatumIndex > -1) return startDatumIndex / length;\n        // value 模式下默认取 0\n        return 0;\n      };\n      var getEndValue = function () {\n        if (end === Infinity) return 1;\n        var endDatumIndex = data.findIndex(function (_a) {\n          var time = _a.time;\n          return time === end;\n        });\n        if (endDatumIndex > -1) return endDatumIndex / length;\n        // range 模式下默认取 1，value 模式下默认取 0.5\n        if (isValue) return 0.5;\n        return 1;\n      };\n      return [getStartValue(), getEndValue()];\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Timebar.prototype, \"values\", {\n    get: function () {\n      var _a = this.states,\n        values = _a.values,\n        selectionType = _a.selectionType;\n      var _b = __read(Array.isArray(values) ? values : [this.data[0].time, values], 2),\n        start = _b[0],\n        end = _b[1];\n      if (selectionType === 'value') return end;\n      return [start, end];\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Timebar.prototype.getDatumByRatio = function (ratio) {\n    var data = this.data;\n    var length = data.length;\n    var index = Math.floor(ratio * (length - 1));\n    return data[index];\n  };\n  Object.defineProperty(Timebar.prototype, \"chartHandleIconShape\", {\n    get: function () {\n      var selectionType = this.states.selectionType;\n      var height = this.space.timelineBBox.height;\n      if (selectionType === 'range') return function (type) {\n        return new ChartModeHandle({\n          style: {\n            type: type,\n            height: height,\n            iconSize: height / 6\n          }\n        });\n      };\n      return function () {\n        return new Line({\n          style: {\n            x1: 0,\n            y1: -height / 2,\n            x2: 0,\n            y2: height / 2,\n            lineWidth: 2,\n            stroke: '#c8c8c8'\n          }\n        });\n      };\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Timebar.prototype.getChartStyle = function (bbox) {\n    var _this = this;\n    var x = bbox.x,\n      y = bbox.y,\n      width = bbox.width,\n      height = bbox.height;\n    var _a = this.states,\n      selectionType = _a.selectionType,\n      chartType = _a.chartType;\n    var data = this.data;\n    var _b = this.attributes,\n      type = _b.type,\n      userDefinedLabelFormatter = _b.labelFormatter;\n    var _c = subStyleProps(this.attributes, 'chart'),\n      ignoreType = _c.type,\n      userDefinedChartStyle = __rest(_c, [\"type\"]);\n    var isRange = selectionType === 'range';\n    if (type === 'time') {\n      return __assign({\n        handleIconShape: function () {\n          return new TimeModeHandle({});\n        },\n        selectionFill: '#2e7ff8',\n        selectionFillOpacity: 1,\n        showLabelOnInteraction: true,\n        handleLabelDy: isRange ? -15 : 0,\n        autoFitLabel: isRange,\n        handleSpacing: isRange ? -15 : 0,\n        trackFill: '#edeeef',\n        trackLength: width,\n        trackOpacity: 0.5,\n        trackRadius: height / 2,\n        trackSize: height / 2,\n        type: selectionType,\n        values: this.sliderValues,\n        formatter: function (value) {\n          if (userDefinedLabelFormatter) return userDefinedLabelFormatter(value);\n          var time = _this.getDatumByRatio(value).time;\n          if (typeof time === 'number') return parseBySeries(time);\n          return formatTime(time, 'YYYY-MM-DD HH:mm:ss');\n        },\n        transform: \"translate(\".concat(x, \", \").concat(y, \")\"),\n        // x,\n        // y,\n        zIndex: 1\n      }, userDefinedChartStyle);\n    }\n    // type === 'chart'\n    var handleIconOffset = selectionType === 'range' ? 5 : 0;\n    var sparklineData = data.map(function (_a) {\n      var value = _a.value;\n      return value;\n    });\n    return __assign({\n      handleIconOffset: handleIconOffset,\n      handleIconShape: this.chartHandleIconShape,\n      selectionFill: '#fff',\n      selectionFillOpacity: 0.5,\n      selectionType: 'invert',\n      sparklineSpacing: 0.1,\n      sparklineColumnLineWidth: 0,\n      sparklineColor: '#d4e5fd',\n      sparklineAreaOpacity: 1,\n      sparklineAreaLineWidth: 0,\n      sparklineData: sparklineData,\n      sparklineType: chartType,\n      sparklineScale: 0.8,\n      trackLength: width,\n      trackSize: height,\n      type: selectionType,\n      values: this.sliderValues,\n      // x,\n      // y,\n      transform: \"translate(\".concat(x, \", \").concat(y, \")\"),\n      zIndex: 1\n    }, userDefinedChartStyle);\n  };\n  Timebar.prototype.renderChart = function (bbox) {\n    if (bbox === void 0) {\n      bbox = this.space.timelineBBox;\n    }\n    this.timeline.update(this.getChartStyle(bbox));\n  };\n  Timebar.prototype.updateSelection = function () {\n    this.timeline.setValues(this.sliderValues, true);\n    this.handleSliderChange(this.sliderValues);\n  };\n  Timebar.prototype.getAxisStyle = function (bbox) {\n    var data = this.data;\n    var _a = this.attributes,\n      interval = _a.interval,\n      userDefinedLabelFormatter = _a.labelFormatter;\n    var userDefinedAxisStyle = subStyleProps(this.attributes, 'axis');\n    var x = bbox.x,\n      y = bbox.y,\n      width = bbox.width;\n    // 需要补一个刻度\n    var axisData = __spreadArray(__spreadArray([], __read(data), false), [{\n      time: 0\n    }], false).map(function (_a, index, arr) {\n      var time = _a.time;\n      return {\n        label: \"\".concat(time),\n        value: index / (arr.length - 1),\n        time: time\n      };\n    });\n    var style = __assign({\n      startPos: [x, y],\n      endPos: [x + width, y],\n      data: axisData,\n      // hide last label\n      labelFilter: function (_datum, index) {\n        return index < axisData.length - 1;\n      },\n      labelFormatter: function (_a) {\n        var time = _a.time;\n        return userDefinedLabelFormatter ? userDefinedLabelFormatter(time) : labelFormatter(time, interval);\n      }\n    }, userDefinedAxisStyle);\n    return style;\n  };\n  Timebar.prototype.renderAxis = function (bbox) {\n    if (bbox === void 0) {\n      bbox = this.space.axisBBox;\n    }\n    var type = this.attributes.type;\n    if (type !== 'chart') return;\n    this.axis.update(this.getAxisStyle(bbox));\n  };\n  Timebar.prototype.renderController = function (bbox) {\n    if (bbox === void 0) {\n      bbox = this.space.controllerBBox;\n    }\n    var type = this.attributes.type;\n    var _a = this.states,\n      state = _a.state,\n      speed = _a.speed,\n      selectionType = _a.selectionType,\n      chartType = _a.chartType;\n    var userDefinedControllerStyle = subStyleProps(this.attributes, 'controller');\n    var that = this;\n    var style = __assign(__assign(__assign({}, bbox), {\n      iconSize: 20,\n      speed: speed,\n      state: state,\n      selectionType: selectionType,\n      chartType: chartType,\n      onChange: function (type, _a) {\n        var value = _a.value;\n        switch (type) {\n          case 'reset':\n            that.internalReset();\n            break;\n          case 'speed':\n            that.handleSpeedChange(value);\n            break;\n          case 'backward':\n            that.internalBackward();\n            break;\n          case 'playPause':\n            if (value === 'play') that.internalPlay();else that.internalPause();\n            break;\n          case 'forward':\n            that.internalForward();\n            break;\n          case 'selectionType':\n            that.handleSelectionTypeChange(value);\n            break;\n          case 'chartType':\n            that.handleChartTypeChange(value);\n            break;\n          default:\n            break;\n        }\n      }\n    }), userDefinedControllerStyle);\n    if (type === 'time') {\n      style.functions = [['reset', 'speed'], ['backward', 'playPause', 'forward'], ['selectionType']];\n    }\n    this.controller.update(style);\n  };\n  Timebar.prototype.dispatchOnChange = function (prevValues) {\n    var data = this.data;\n    var onChange = this.attributes.onChange;\n    var _a = this.states,\n      values = _a.values,\n      selectionType = _a.selectionType;\n    var _b = __read(values, 2),\n      start = _b[0],\n      end = _b[1];\n    var endTime = end === Infinity ? data.at(-1).time : end;\n    var newValues = selectionType === 'range' ? [start, endTime] : endTime;\n    var isEqual = function (val1, val2) {\n      if (Array.isArray(val1)) {\n        if (!Array.isArray(val2)) return false;\n        if (val1[0] === val2[0]) {\n          if (val1[1] === val2[1]) return true;\n          if (val1[1] === Infinity || val2[1] === Infinity) return true;\n        }\n        return false;\n      }\n      if (Array.isArray(val2)) return false;\n      return val1 === val2;\n    };\n    // 如果和当前值不同，才响应\n    if (!prevValues || !isEqual(prevValues, newValues)) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(selectionType === 'range' ? [start, endTime] : endTime);\n    }\n  };\n  Timebar.prototype.internalReset = function (preventEvent) {\n    var _a, _b;\n    var selectionType = this.states.selectionType;\n    this.internalPause();\n    this.setBySliderValues(selectionType === 'range' ? [0, 1] : [0, 0]);\n    this.renderController();\n    this.updateSelection();\n    if (!preventEvent) {\n      (_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onReset) === null || _b === void 0 ? void 0 : _b.call(_a);\n      this.dispatchOnChange();\n    }\n  };\n  Timebar.prototype.reset = function () {\n    this.internalReset();\n  };\n  Timebar.prototype.moveSelection = function (direction, preventEvent) {\n    var data = this.data;\n    var length = data.length;\n    var _a = this.states,\n      values = _a.values,\n      selectionType = _a.selectionType,\n      playMode = _a.playMode;\n    var _b = __read(values, 2),\n      startTime = _b[0],\n      endTime = _b[1];\n    var startIndex = data.findIndex(function (_a) {\n      var time = _a.time;\n      return time === startTime;\n    });\n    var endIndex = data.findIndex(function (_a) {\n      var time = _a.time;\n      return time === endTime;\n    });\n    if (endIndex === -1) endIndex = length;\n    var diff = direction === 'backward' ? -1 : 1;\n    var currentIndexes;\n    if (selectionType === 'range') {\n      // end 后移一个时间间隔\n      if (playMode === 'acc') {\n        currentIndexes = [startIndex, endIndex + diff];\n        // 如果回退过程中，start 和 end 相遇，则 end 重置到 length\n        if (diff === -1 && startIndex === endIndex) {\n          currentIndexes = [startIndex, length];\n        }\n      }\n      // start, end 后移一个时间间隔\n      else currentIndexes = [startIndex + diff, endIndex + diff];\n    }\n    // end 后移一个时间间隔\n    else currentIndexes = [startIndex, endIndex + diff];\n    var normalizeIndexes = function (indexes) {\n      // 先进行排序\n      var _a = __read(indexes.sort(function (a, b) {\n          return a - b;\n        }), 2),\n        start = _a[0],\n        end = _a[1];\n      // 保证 index 在 [0, length]\n      var clampIndex = function (index) {\n        return clamp(index, 0, length);\n      };\n      // 如果 end 超出最大值\n      if (end > length) {\n        // value 模式下，重置到 0\n        if (selectionType === 'value') return [0, 0];\n        // 移动到 start\n        if (playMode === 'acc') return [clampIndex(start), clampIndex(start)];\n        // 整体移动到起始位置\n        return [0, clampIndex(end - start)];\n      }\n      // 如果是倒放，到头时，整体移动到末尾\n      if (start < 0) {\n        if (playMode === 'acc') return [0, clampIndex(end)];\n        return [clampIndex(start + length - end), length];\n      }\n      return [clampIndex(start), clampIndex(end)];\n    };\n    var normalizedIndexes = normalizeIndexes(currentIndexes);\n    this.setByIndex(normalizedIndexes);\n    this.updateSelection();\n    return normalizedIndexes;\n  };\n  Timebar.prototype.internalBackward = function (preventEvent) {\n    var _a, _b;\n    var indexes = this.moveSelection('backward', preventEvent);\n    if (!preventEvent) {\n      (_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onBackward) === null || _b === void 0 ? void 0 : _b.call(_a);\n      this.dispatchOnChange();\n    }\n    return indexes;\n  };\n  Timebar.prototype.backward = function () {\n    this.internalBackward();\n  };\n  Timebar.prototype.internalPlay = function (preventEvent) {\n    var _this = this;\n    var _a, _b;\n    var data = this.data;\n    var loop = this.attributes.loop;\n    var _c = this.states.speed,\n      speed = _c === void 0 ? 1 : _c;\n    this.playInterval = window.setInterval(function () {\n      var indexes = _this.internalForward();\n      // 如果不是循环播放，则播放到最后一个值时暂停\n      if (indexes[1] === data.length && !loop) {\n        // 这里需要抛出暂停事件\n        _this.internalPause();\n        _this.renderController();\n      }\n    }, 1000 / speed);\n    this.states.state = 'play';\n    !preventEvent && ((_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onPlay) === null || _b === void 0 ? void 0 : _b.call(_a));\n  };\n  Timebar.prototype.play = function () {\n    this.internalPlay();\n  };\n  Timebar.prototype.internalPause = function (preventEvent) {\n    var _a, _b;\n    clearInterval(this.playInterval);\n    this.states.state = 'pause';\n    !preventEvent && ((_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onPause) === null || _b === void 0 ? void 0 : _b.call(_a));\n  };\n  Timebar.prototype.pause = function () {\n    this.internalPause();\n  };\n  Timebar.prototype.internalForward = function (preventEvent) {\n    var _a, _b;\n    var indexes = this.moveSelection('forward', preventEvent);\n    if (!preventEvent) {\n      (_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onForward) === null || _b === void 0 ? void 0 : _b.call(_a);\n      this.dispatchOnChange();\n    }\n    return indexes;\n  };\n  Timebar.prototype.forward = function () {\n    this.internalForward();\n  };\n  Timebar.prototype.handleSpeedChange = function (value) {\n    var _a, _b;\n    this.states.speed = value;\n    var state = this.states.state;\n    if (state === 'play') {\n      // 重新设定 interval\n      this.internalPause(true);\n      this.internalPlay(true);\n    }\n    (_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onSpeedChange) === null || _b === void 0 ? void 0 : _b.call(_a, value);\n  };\n  Timebar.prototype.handleSelectionTypeChange = function (type) {\n    var _a, _b;\n    this.states.selectionType = type;\n    this.renderChart();\n    (_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onSelectionTypeChange) === null || _b === void 0 ? void 0 : _b.call(_a, type);\n  };\n  Timebar.prototype.handleChartTypeChange = function (type) {\n    var _a, _b;\n    this.states.chartType = type;\n    this.renderChart();\n    (_b = (_a = this.attributes) === null || _a === void 0 ? void 0 : _a.onChartTypeChange) === null || _b === void 0 ? void 0 : _b.call(_a, type);\n  };\n  Timebar.prototype.render = function () {\n    var _a = this.space,\n      axisBBox = _a.axisBBox,\n      controllerBBox = _a.controllerBBox,\n      timelineBBox = _a.timelineBBox;\n    this.renderController(controllerBBox);\n    this.renderAxis(axisBBox);\n    this.renderChart(timelineBBox);\n    if (this.states.state === 'play') this.internalPlay();\n  };\n  Timebar.prototype.destroy = function () {\n    _super.prototype.destroy.call(this);\n    this.internalPause(true);\n  };\n  Timebar.defaultOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      axisLabelFill: '#6e6e6e',\n      axisLabelTextAlign: 'left',\n      axisLabelTextBaseline: 'top',\n      axisLabelTransform: 'translate(5, -12)',\n      axisLineLineWidth: 1,\n      axisLineStroke: '#cacdd1',\n      axisTickLength: 15,\n      axisTickLineWidth: 1,\n      axisTickStroke: '#cacdd1',\n      chartShowLabel: false,\n      chartType: 'line',\n      controllerAlign: 'center',\n      controllerHeight: 40,\n      data: [],\n      interval: 'day',\n      loop: false,\n      playMode: 'acc',\n      selectionType: 'range',\n      type: 'time'\n    }\n  };\n  return Timebar;\n}(Component);\nexport { Timebar };", "map": {"version": 3, "names": ["clamp", "Component", "Line", "BBox", "deepAssign", "subStyleProps", "formatTime", "Axis", "Slide<PERSON>", "Controller", "ChartModeHandle", "TimeModeHandle", "labelFormatter", "parseBySeries", "Timebar", "_super", "__extends", "options", "_this", "call", "defaultOptions", "axis", "append<PERSON><PERSON><PERSON>", "style", "type", "startPos", "endPos", "data", "showArrow", "animate", "timeline", "onChange", "values", "handleSliderChange", "controller", "states", "prevV<PERSON><PERSON>", "val", "Array", "isArray", "__spread<PERSON><PERSON>y", "__read", "set<PERSON>y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchOnChange", "_a", "attributes", "selectionType", "chartType", "speed", "state", "playMode", "setByTimebarValues", "Object", "defineProperty", "prototype", "get", "compareFn", "a", "b", "time", "sort", "x", "y", "width", "height", "controllerHeight", "availableTimelineHeight", "controllerBBox", "axisBBox", "axisHeight", "timelineHeight", "timelineBBox", "_c", "startRatio", "endRatio", "length", "startDatum", "Math", "floor", "endDatum", "ceil", "_b", "Infinity", "_d", "undefined", "start", "end", "find", "setByIndex", "index", "_e", "startIndex", "endIndex", "isValue", "getStartValue", "startDatumIndex", "findIndex", "getEndValue", "endDatumIndex", "getDatumByRatio", "ratio", "space", "iconSize", "x1", "y1", "x2", "y2", "lineWidth", "stroke", "getChartStyle", "bbox", "userDefinedLabelFormatter", "ignoreType", "userDefinedChartStyle", "__rest", "isRange", "__assign", "handleIconShape", "selectionFill", "selectionFillOpacity", "showLabelOnInteraction", "handleLabelDy", "autoFitLabel", "handleSpacing", "trackFill", "trackLength", "trackOpacity", "trackRadius", "trackSize", "slider<PERSON><PERSON><PERSON>", "formatter", "value", "transform", "concat", "zIndex", "handleIconOffset", "sparklineData", "map", "chartHandleIconShape", "sparklineSpacing", "sparklineColumnLineWidth", "sparklineColor", "sparklineAreaOpacity", "sparklineAreaLineWidth", "sparklineType", "sparklineScale", "<PERSON><PERSON><PERSON>", "update", "updateSelection", "set<PERSON><PERSON><PERSON>", "getAxisStyle", "interval", "userDefinedAxisStyle", "axisData", "arr", "label", "labelFilter", "_datum", "renderAxis", "renderController", "userDefinedControllerStyle", "that", "internalReset", "handleSpeedChange", "internalBackward", "internalPlay", "internalPause", "internalForward", "handleSelectionTypeChange", "handleChartTypeChange", "functions", "endTime", "at", "newValues", "isEqual", "val1", "val2", "preventEvent", "onReset", "reset", "moveSelection", "direction", "startTime", "diff", "currentIndexes", "normalizeIndexes", "indexes", "clampIndex", "normalizedIndexes", "onBackward", "backward", "loop", "playInterval", "window", "setInterval", "onPlay", "play", "clearInterval", "onPause", "pause", "onForward", "forward", "onSpeedChange", "onSelectionTypeChange", "onChartTypeChange", "render", "destroy", "axisLabelFill", "axisLabelTextAlign", "axisLabelTextBaseline", "axisLabelTransform", "axisLineLineWidth", "axisLineStroke", "axisTick<PERSON>ength", "axisTickLineWidth", "axisTickStroke", "chartShowLabel", "controllerAlign"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/timebar/timebar.ts"], "sourcesContent": ["import { clamp } from '@antv/util';\nimport { Component } from '../../core';\nimport { Line } from '../../shapes';\nimport { BBox, deepAssign, subStyleProps, formatTime } from '../../util';\nimport { Axis } from '../axis';\nimport type { LinearAxisStyleProps } from '../axis/types';\nimport type { SliderStyleProps } from '../slider';\nimport { Slider } from '../slider';\nimport { Controller } from './controller';\nimport { ChartModeHandle, TimeModeHandle } from './handle';\nimport type { ControllerStyleProps, TimebarOptions, TimebarStyleProps, Datum } from './types';\nimport { labelFormatter, parseBySeries } from './utils';\n\ntype States = Pick<TimebarStyleProps, 'speed' | 'selectionType' | 'chartType' | 'state' | 'playMode'> & {\n  values?: [number, number] | [Date, Date] | [number | Date, typeof Infinity];\n};\n\nexport class Timebar extends Component<TimebarStyleProps> {\n  static defaultOptions: TimebarOptions = {\n    style: {\n      x: 0,\n      y: 0,\n      axisLabelFill: '#6e6e6e',\n      axisLabelTextAlign: 'left',\n      axisLabelTextBaseline: 'top',\n      axisLabelTransform: 'translate(5, -12)',\n      axisLineLineWidth: 1,\n      axisLineStroke: '#cacdd1',\n      axisTickLength: 15,\n      axisTickLineWidth: 1,\n      axisTickStroke: '#cacdd1',\n      chartShowLabel: false,\n      chartType: 'line',\n      controllerAlign: 'center',\n      controllerHeight: 40,\n      data: [],\n      interval: 'day',\n      loop: false,\n      playMode: 'acc',\n      selectionType: 'range',\n      type: 'time',\n    },\n  };\n\n  private axis = this.appendChild(\n    new Axis({\n      style: { type: 'linear', startPos: [0, 0], endPos: [0, 0], data: [], showArrow: false, animate: false },\n    })\n  );\n\n  /** 时间线 group，用于放置 timeline 或者 chart */\n  private timeline = this.appendChild(\n    new Slider({\n      style: {\n        onChange: (values) => {\n          this.handleSliderChange(values);\n        },\n      },\n    })\n  );\n\n  private controller = this.appendChild(new Controller({}));\n\n  private states: States = {};\n\n  private playInterval?: number;\n\n  private get data() {\n    const { data } = this.attributes;\n    const compareFn = (a: Datum, b: Datum) => {\n      if (a.time < b.time) return -1;\n      if (a.time > b.time) return 1;\n      return 0;\n    };\n    return data.sort(compareFn);\n  }\n\n  /** 计算空间分配 */\n  private get space() {\n    const { x, y, width, height, type, controllerHeight } = this.attributes;\n\n    const availableTimelineHeight = clamp(+height - controllerHeight, 0, +height);\n    const controllerBBox = new BBox(x, y + +height - controllerHeight, +width, controllerHeight);\n\n    // chart 模式下可用\n    let axisBBox: BBox;\n    let axisHeight = 0;\n    if (type === 'chart') {\n      // axis 默认分配高度为 35\n      axisHeight = 35;\n      axisBBox = new BBox(x, y + availableTimelineHeight - axisHeight, +width, axisHeight);\n    } else axisBBox = new BBox();\n\n    const timelineHeight = type === 'time' ? 10 : availableTimelineHeight;\n    const timelineBBox = new BBox(\n      x,\n      y + (type === 'time' ? availableTimelineHeight : availableTimelineHeight - timelineHeight),\n      +width,\n      timelineHeight - axisHeight\n    );\n\n    return { axisBBox, controllerBBox, timelineBBox };\n  }\n\n  private setBySliderValues(val: number | number[]) {\n    const { data } = this;\n    const [startRatio, endRatio] = Array.isArray(val) ? val : [0, val];\n    const length = data.length;\n    const startDatum = data[Math.floor(startRatio * length)];\n    const endDatum = data[Math.ceil(endRatio * length) - (Array.isArray(val) ? 0 : 1)];\n    // 如果 endDatum 不存在，则其已经比最大的时间范围更大\n    this.states.values = [startDatum?.time ?? data[0].time, endDatum?.time ?? Infinity] as any;\n  }\n\n  private setByTimebarValues(val: TimebarStyleProps['values']) {\n    const { data } = this;\n    const [start, end] = Array.isArray(val) ? val : [undefined, val];\n    const startDatum = data.find(({ time }) => time === start);\n    const endDatum = data.find(({ time }) => time === end);\n    this.states.values = [startDatum?.time ?? data[0]?.time, endDatum?.time ?? Infinity] as any;\n  }\n\n  private setByIndex(index: [number, number]) {\n    const { data } = this;\n    const [startIndex, endIndex] = index;\n    this.states.values = [data[startIndex]?.time ?? data[0].time, this.data[endIndex]?.time ?? Infinity] as any;\n  }\n\n  /**\n   * 获取 timebar 的 values\n   */\n  private get sliderValues(): [number, number] {\n    const { values, selectionType } = this.states;\n    const [start, end] = Array.isArray(values) ? values : [undefined, values];\n\n    const { data } = this;\n    const length = data.length;\n    const isValue = selectionType === 'value';\n\n    const getStartValue = () => {\n      const startDatumIndex = data.findIndex(({ time }) => time === start);\n      if (isValue) return 0;\n      if (startDatumIndex > -1) return startDatumIndex / length;\n      // value 模式下默认取 0\n      return 0;\n    };\n\n    const getEndValue = () => {\n      if (end === Infinity) return 1;\n      const endDatumIndex = data.findIndex(({ time }) => time === end);\n      if (endDatumIndex > -1) return endDatumIndex / length;\n      // range 模式下默认取 1，value 模式下默认取 0.5\n      if (isValue) return 0.5;\n      return 1;\n    };\n\n    return [getStartValue(), getEndValue()];\n  }\n\n  public get values() {\n    const { values, selectionType } = this.states;\n    const [start, end] = Array.isArray(values) ? values : [this.data[0].time, values];\n    if (selectionType === 'value') return end as number | Date;\n    return [start, end] as [number, number] | [Date, Date];\n  }\n\n  private getDatumByRatio(ratio: number) {\n    const { data } = this;\n    const length = data.length;\n    const index = Math.floor(ratio * (length - 1));\n    return data[index];\n  }\n\n  private get chartHandleIconShape() {\n    const { selectionType } = this.states;\n    const {\n      timelineBBox: { height },\n    } = this.space;\n    if (selectionType === 'range')\n      return (type: any) => new ChartModeHandle({ style: { type, height, iconSize: height / 6 } });\n\n    return () =>\n      new Line({ style: { x1: 0, y1: -height / 2, x2: 0, y2: height / 2, lineWidth: 2, stroke: '#c8c8c8' } });\n  }\n\n  private getChartStyle(bbox: BBox): SliderStyleProps {\n    const { x, y, width, height } = bbox;\n    const { selectionType, chartType } = this.states;\n    const { data } = this;\n    const { type, labelFormatter: userDefinedLabelFormatter } = this.attributes;\n    const { type: ignoreType, ...userDefinedChartStyle } = subStyleProps(this.attributes, 'chart');\n    const isRange = selectionType === 'range';\n    if (type === 'time') {\n      return {\n        handleIconShape: () => new TimeModeHandle({}),\n        selectionFill: '#2e7ff8',\n        selectionFillOpacity: 1,\n        showLabelOnInteraction: true,\n        handleLabelDy: isRange ? -15 : 0,\n        autoFitLabel: isRange,\n        handleSpacing: isRange ? -15 : 0,\n        trackFill: '#edeeef',\n        trackLength: width,\n        trackOpacity: 0.5,\n        trackRadius: height / 2,\n        trackSize: height / 2,\n        type: selectionType,\n        values: this.sliderValues,\n        formatter: (value) => {\n          if (userDefinedLabelFormatter) return userDefinedLabelFormatter(value);\n          const time = this.getDatumByRatio(value).time;\n          if (typeof time === 'number') return parseBySeries(time);\n          return formatTime(time, 'YYYY-MM-DD HH:mm:ss');\n        },\n        transform: `translate(${x}, ${y})`,\n        // x,\n        // y,\n        zIndex: 1,\n        ...userDefinedChartStyle,\n      };\n    }\n    // type === 'chart'\n    const handleIconOffset = selectionType === 'range' ? 5 : 0;\n    const sparklineData = data.map(({ value }) => value);\n    return {\n      handleIconOffset,\n      handleIconShape: this.chartHandleIconShape,\n      selectionFill: '#fff',\n      selectionFillOpacity: 0.5,\n      selectionType: 'invert',\n      sparklineSpacing: 0.1,\n      sparklineColumnLineWidth: 0,\n      sparklineColor: '#d4e5fd',\n      sparklineAreaOpacity: 1,\n      sparklineAreaLineWidth: 0,\n      sparklineData,\n      sparklineType: chartType,\n      sparklineScale: 0.8,\n      trackLength: width,\n      trackSize: height,\n      type: selectionType,\n      values: this.sliderValues,\n      // x,\n      // y,\n      transform: `translate(${x}, ${y})`,\n      zIndex: 1,\n      ...userDefinedChartStyle,\n    };\n  }\n\n  private renderChart(bbox: BBox = this.space.timelineBBox) {\n    this.timeline.update(this.getChartStyle(bbox));\n  }\n\n  private updateSelection() {\n    this.timeline.setValues(this.sliderValues, true);\n    this.handleSliderChange(this.sliderValues);\n  }\n\n  private getAxisStyle(bbox: BBox) {\n    const { data } = this;\n    const { interval, labelFormatter: userDefinedLabelFormatter } = this.attributes;\n    const userDefinedAxisStyle = subStyleProps(this.attributes, 'axis');\n\n    const { x, y, width } = bbox;\n    // 需要补一个刻度\n    const axisData = [...data, { time: 0 }].map(({ time }, index, arr) => ({\n      label: `${time}`,\n      value: index / (arr.length - 1),\n      time,\n    }));\n    const style: Partial<LinearAxisStyleProps> = {\n      startPos: [x, y],\n      endPos: [x + width, y],\n      data: axisData,\n      // hide last label\n      labelFilter: (_datum, index) => index < axisData.length - 1,\n      labelFormatter: ({ time }) =>\n        userDefinedLabelFormatter ? userDefinedLabelFormatter(time) : labelFormatter(time, interval),\n      ...userDefinedAxisStyle,\n    };\n    return style;\n  }\n\n  private renderAxis(bbox: BBox = this.space.axisBBox) {\n    const { type } = this.attributes;\n    if (type !== 'chart') return;\n    this.axis.update(this.getAxisStyle(bbox));\n  }\n\n  private renderController(bbox: BBox = this.space.controllerBBox) {\n    const { type } = this.attributes;\n    const { state, speed, selectionType, chartType } = this.states;\n    const userDefinedControllerStyle = subStyleProps(this.attributes, 'controller');\n\n    const that = this;\n\n    const style: ControllerStyleProps = {\n      ...bbox,\n      iconSize: 20,\n      speed,\n      state,\n      selectionType,\n      chartType,\n      onChange(type, { value }) {\n        switch (type) {\n          case 'reset':\n            that.internalReset();\n            break;\n          case 'speed':\n            that.handleSpeedChange(value);\n            break;\n          case 'backward':\n            that.internalBackward();\n            break;\n          case 'playPause':\n            if (value === 'play') that.internalPlay();\n            else that.internalPause();\n            break;\n          case 'forward':\n            that.internalForward();\n            break;\n          case 'selectionType':\n            that.handleSelectionTypeChange(value);\n            break;\n          case 'chartType':\n            that.handleChartTypeChange(value);\n            break;\n          default:\n            break;\n        }\n      },\n      ...userDefinedControllerStyle,\n    };\n\n    if (type === 'time') {\n      style.functions = [['reset', 'speed'], ['backward', 'playPause', 'forward'], ['selectionType']];\n    }\n\n    this.controller.update(style);\n  }\n\n  private dispatchOnChange(prevValues?: any) {\n    const { data } = this;\n    const { onChange } = this.attributes;\n    const { values, selectionType } = this.states;\n    const [start, end] = values as any;\n    const endTime = end === Infinity ? data.at(-1)!.time : end;\n    const newValues = selectionType === 'range' ? [start, endTime] : endTime;\n\n    const isEqual = (val1: TimebarStyleProps['values'], val2: TimebarStyleProps['values']) => {\n      if (Array.isArray(val1)) {\n        if (!Array.isArray(val2)) return false;\n        if (val1[0] === val2[0]) {\n          if (val1[1] === val2[1]) return true;\n          if (val1[1] === Infinity || val2[1] === Infinity) return true;\n        }\n        return false;\n      }\n      if (Array.isArray(val2)) return false;\n      return val1 === val2;\n    };\n    // 如果和当前值不同，才响应\n    if (!prevValues || !isEqual(prevValues, newValues)) {\n      onChange?.(selectionType === 'range' ? [start, endTime] : endTime);\n    }\n  }\n\n  private handleSliderChange = (values: SliderStyleProps['values']) => {\n    const prevValues: any = (() => {\n      const val = this.states.values;\n      if (Array.isArray(val)) return [...val];\n      return val;\n    })();\n\n    this.setBySliderValues(values!);\n    this.dispatchOnChange(prevValues);\n  };\n\n  private internalReset(preventEvent?: boolean) {\n    const { selectionType } = this.states;\n    this.internalPause();\n    this.setBySliderValues(selectionType === 'range' ? [0, 1] : [0, 0]);\n    this.renderController();\n    this.updateSelection();\n    if (!preventEvent) {\n      this.attributes?.onReset?.();\n      this.dispatchOnChange();\n    }\n  }\n\n  public reset() {\n    this.internalReset();\n  }\n\n  private moveSelection(direction: 'forward' | 'backward', preventEvent?: boolean) {\n    const { data } = this;\n    const length = data.length;\n    const { values, selectionType, playMode } = this.states;\n    const [startTime, endTime] = values!;\n    const startIndex = data.findIndex(({ time }) => time === startTime);\n    let endIndex = data.findIndex(({ time }) => time === endTime);\n    if (endIndex === -1) endIndex = length;\n    const diff = direction === 'backward' ? -1 : 1;\n    let currentIndexes: [number, number];\n    if (selectionType === 'range') {\n      // end 后移一个时间间隔\n      if (playMode === 'acc') {\n        currentIndexes = [startIndex, endIndex + diff];\n        // 如果回退过程中，start 和 end 相遇，则 end 重置到 length\n        if (diff === -1 && startIndex === endIndex) {\n          currentIndexes = [startIndex, length];\n        }\n      }\n      // start, end 后移一个时间间隔\n      else currentIndexes = [startIndex + diff, endIndex + diff];\n    }\n    // end 后移一个时间间隔\n    else currentIndexes = [startIndex, endIndex + diff];\n\n    const normalizeIndexes = (indexes: [number, number]): [number, number] => {\n      // 先进行排序\n      const [start, end] = indexes.sort((a, b) => a - b);\n      // 保证 index 在 [0, length]\n      const clampIndex = (index: number) => clamp(index, 0, length);\n      // 如果 end 超出最大值\n      if (end > length) {\n        // value 模式下，重置到 0\n        if (selectionType === 'value') return [0, 0];\n        // 移动到 start\n        if (playMode === 'acc') return [clampIndex(start), clampIndex(start)];\n        // 整体移动到起始位置\n        return [0, clampIndex(end - start)];\n      }\n      // 如果是倒放，到头时，整体移动到末尾\n      if (start < 0) {\n        if (playMode === 'acc') return [0, clampIndex(end)];\n        return [clampIndex(start + length - end), length];\n      }\n      return [clampIndex(start), clampIndex(end)];\n    };\n\n    const normalizedIndexes = normalizeIndexes(currentIndexes);\n\n    this.setByIndex(normalizedIndexes);\n    this.updateSelection();\n    return normalizedIndexes;\n  }\n\n  private internalBackward(preventEvent?: boolean) {\n    const indexes = this.moveSelection('backward', preventEvent);\n    if (!preventEvent) {\n      this.attributes?.onBackward?.();\n      this.dispatchOnChange();\n    }\n\n    return indexes;\n  }\n\n  public backward() {\n    this.internalBackward();\n  }\n\n  private internalPlay(preventEvent?: boolean) {\n    const { data } = this;\n    const { loop } = this.attributes;\n    const { speed = 1 } = this.states;\n    this.playInterval = window.setInterval(() => {\n      const indexes = this.internalForward();\n      // 如果不是循环播放，则播放到最后一个值时暂停\n      if (indexes[1] === data.length && !loop) {\n        // 这里需要抛出暂停事件\n        this.internalPause();\n        this.renderController();\n      }\n    }, 1000 / speed);\n    this.states.state = 'play';\n    !preventEvent && this.attributes?.onPlay?.();\n  }\n\n  public play() {\n    this.internalPlay();\n  }\n\n  private internalPause(preventEvent?: boolean) {\n    clearInterval(this.playInterval);\n    this.states.state = 'pause';\n    !preventEvent && this.attributes?.onPause?.();\n  }\n\n  public pause() {\n    this.internalPause();\n  }\n\n  private internalForward(preventEvent?: boolean) {\n    const indexes = this.moveSelection('forward', preventEvent);\n    if (!preventEvent) {\n      this.attributes?.onForward?.();\n      this.dispatchOnChange();\n    }\n    return indexes;\n  }\n\n  public forward() {\n    this.internalForward();\n  }\n\n  private handleSpeedChange(value: number) {\n    this.states.speed = value;\n    const { state } = this.states;\n    if (state === 'play') {\n      // 重新设定 interval\n      this.internalPause(true);\n      this.internalPlay(true);\n    }\n    this.attributes?.onSpeedChange?.(value);\n  }\n\n  private handleSelectionTypeChange(type: TimebarStyleProps['selectionType']) {\n    this.states.selectionType = type;\n    this.renderChart();\n    this.attributes?.onSelectionTypeChange?.(type);\n  }\n\n  private handleChartTypeChange(type: TimebarStyleProps['chartType']) {\n    this.states.chartType = type;\n    this.renderChart();\n    this.attributes?.onChartTypeChange?.(type);\n  }\n\n  constructor(options: TimebarOptions) {\n    super(deepAssign({}, Timebar.defaultOptions, options));\n    const { selectionType, chartType, speed, state, playMode, values } = this.attributes;\n    this.states = { chartType, playMode, selectionType, speed, state };\n    this.setByTimebarValues(values);\n  }\n\n  render() {\n    const { axisBBox, controllerBBox, timelineBBox } = this.space;\n    this.renderController(controllerBBox);\n    this.renderAxis(axisBBox);\n    this.renderChart(timelineBBox);\n\n    if (this.states.state === 'play') this.internalPlay();\n  }\n\n  destroy(): void {\n    super.destroy();\n    this.internalPause(true);\n  }\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,YAAY;AAClC,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,IAAI,EAAEC,UAAU,EAAEC,aAAa,EAAEC,UAAU,QAAQ,YAAY;AACxE,SAASC,IAAI,QAAQ,SAAS;AAG9B,SAASC,MAAM,QAAQ,WAAW;AAClC,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,eAAe,EAAEC,cAAc,QAAQ,UAAU;AAE1D,SAASC,cAAc,EAAEC,aAAa,QAAQ,SAAS;AAMvD,IAAAC,OAAA,0BAAAC,MAAA;EAA6BC,SAAA,CAAAF,OAAA,EAAAC,MAAA;EAigB3B,SAAAD,QAAYG,OAAuB;IACjC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACf,UAAU,CAAC,EAAE,EAAEU,OAAO,CAACM,cAAc,EAAEH,OAAO,CAAC,CAAC;IAvehDC,KAAA,CAAAG,IAAI,GAAGH,KAAI,CAACI,WAAW,CAC7B,IAAIf,IAAI,CAAC;MACPgB,KAAK,EAAE;QAAEC,IAAI,EAAE,QAAQ;QAAEC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAAEC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAK;KACtG,CAAC,CACH;IAED;IACQX,KAAA,CAAAY,QAAQ,GAAGZ,KAAI,CAACI,WAAW,CACjC,IAAId,MAAM,CAAC;MACTe,KAAK,EAAE;QACLQ,QAAQ,EAAE,SAAAA,CAACC,MAAM;UACfd,KAAI,CAACe,kBAAkB,CAACD,MAAM,CAAC;QACjC;;KAEH,CAAC,CACH;IAEOd,KAAA,CAAAgB,UAAU,GAAGhB,KAAI,CAACI,WAAW,CAAC,IAAIb,UAAU,CAAC,EAAE,CAAC,CAAC;IAEjDS,KAAA,CAAAiB,MAAM,GAAW,EAAE;IAiTnBjB,KAAA,CAAAe,kBAAkB,GAAG,UAACD,MAAkC;MAC9D,IAAMI,UAAU,GAAS;QACvB,IAAMC,GAAG,GAAGnB,KAAI,CAACiB,MAAM,CAACH,MAAM;QAC9B,IAAIM,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE,OAAAG,aAAA,KAAAC,MAAA,CAAWJ,GAAG;QACtC,OAAOA,GAAG;MACZ,CAAC,CAAC,CAAE;MAEJnB,KAAI,CAACwB,iBAAiB,CAACV,MAAO,CAAC;MAC/Bd,KAAI,CAACyB,gBAAgB,CAACP,UAAU,CAAC;IACnC,CAAC;IA2JO,IAAAQ,EAAA,GAA+D1B,KAAI,CAAC2B,UAAU;MAA5EC,aAAa,GAAAF,EAAA,CAAAE,aAAA;MAAEC,SAAS,GAAAH,EAAA,CAAAG,SAAA;MAAEC,KAAK,GAAAJ,EAAA,CAAAI,KAAA;MAAEC,KAAK,GAAAL,EAAA,CAAAK,KAAA;MAAEC,QAAQ,GAAAN,EAAA,CAAAM,QAAA;MAAElB,MAAM,GAAAY,EAAA,CAAAZ,MAAoB;IACpFd,KAAI,CAACiB,MAAM,GAAG;MAAEY,SAAS,EAAAA,SAAA;MAAEG,QAAQ,EAAAA,QAAA;MAAEJ,aAAa,EAAAA,aAAA;MAAEE,KAAK,EAAAA,KAAA;MAAEC,KAAK,EAAAA;IAAA,CAAE;IAClE/B,KAAI,CAACiC,kBAAkB,CAACnB,MAAM,CAAC;;EACjC;EApdAoB,MAAA,CAAAC,cAAA,CAAYvC,OAAA,CAAAwC,SAAA,QAAI;SAAhB,SAAAC,CAAA;MACU,IAAA5B,IAAI,GAAK,IAAI,CAACkB,UAAU,CAAAlB,IAApB;MACZ,IAAM6B,SAAS,GAAG,SAAAA,CAACC,CAAQ,EAAEC,CAAQ;QACnC,IAAID,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9B,IAAIF,CAAC,CAACE,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAE,OAAO,CAAC;QAC7B,OAAO,CAAC;MACV,CAAC;MACD,OAAOhC,IAAI,CAACiC,IAAI,CAACJ,SAAS,CAAC;IAC7B,CAAC;;;;EAGDJ,MAAA,CAAAC,cAAA,CAAYvC,OAAA,CAAAwC,SAAA,SAAK;IADjB;SACA,SAAAC,CAAA;MACQ,IAAAX,EAAA,GAAkD,IAAI,CAACC,UAAU;QAA/DgB,CAAC,GAAAjB,EAAA,CAAAiB,CAAA;QAAEC,CAAC,GAAAlB,EAAA,CAAAkB,CAAA;QAAEC,KAAK,GAAAnB,EAAA,CAAAmB,KAAA;QAAEC,MAAM,GAAApB,EAAA,CAAAoB,MAAA;QAAExC,IAAI,GAAAoB,EAAA,CAAApB,IAAA;QAAEyC,gBAAgB,GAAArB,EAAA,CAAAqB,gBAAoB;MAEvE,IAAMC,uBAAuB,GAAGlE,KAAK,CAAC,CAACgE,MAAM,GAAGC,gBAAgB,EAAE,CAAC,EAAE,CAACD,MAAM,CAAC;MAC7E,IAAMG,cAAc,GAAG,IAAIhE,IAAI,CAAC0D,CAAC,EAAEC,CAAC,GAAG,CAACE,MAAM,GAAGC,gBAAgB,EAAE,CAACF,KAAK,EAAEE,gBAAgB,CAAC;MAE5F;MACA,IAAIG,QAAc;MAClB,IAAIC,UAAU,GAAG,CAAC;MAClB,IAAI7C,IAAI,KAAK,OAAO,EAAE;QACpB;QACA6C,UAAU,GAAG,EAAE;QACfD,QAAQ,GAAG,IAAIjE,IAAI,CAAC0D,CAAC,EAAEC,CAAC,GAAGI,uBAAuB,GAAGG,UAAU,EAAE,CAACN,KAAK,EAAEM,UAAU,CAAC;MACtF,CAAC,MAAMD,QAAQ,GAAG,IAAIjE,IAAI,EAAE;MAE5B,IAAMmE,cAAc,GAAG9C,IAAI,KAAK,MAAM,GAAG,EAAE,GAAG0C,uBAAuB;MACrE,IAAMK,YAAY,GAAG,IAAIpE,IAAI,CAC3B0D,CAAC,EACDC,CAAC,IAAItC,IAAI,KAAK,MAAM,GAAG0C,uBAAuB,GAAGA,uBAAuB,GAAGI,cAAc,CAAC,EAC1F,CAACP,KAAK,EACNO,cAAc,GAAGD,UAAU,CAC5B;MAED,OAAO;QAAED,QAAQ,EAAAA,QAAA;QAAED,cAAc,EAAAA,cAAA;QAAEI,YAAY,EAAAA;MAAA,CAAE;IACnD,CAAC;;;;EAEOzD,OAAA,CAAAwC,SAAA,CAAAZ,iBAAiB,GAAzB,UAA0BL,GAAsB;;IACtC,IAAAV,IAAI,GAAK,IAAI,CAAAA,IAAT;IACN,IAAA6C,EAAA,GAAA/B,MAAA,CAAyBH,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC,EAAEA,GAAG,CAAC;MAA3DoC,UAAU,GAAAD,EAAA;MAAEE,QAAQ,GAAAF,EAAA,GAAuC;IAClE,IAAMG,MAAM,GAAGhD,IAAI,CAACgD,MAAM;IAC1B,IAAMC,UAAU,GAAGjD,IAAI,CAACkD,IAAI,CAACC,KAAK,CAACL,UAAU,GAAGE,MAAM,CAAC,CAAC;IACxD,IAAMI,QAAQ,GAAGpD,IAAI,CAACkD,IAAI,CAACG,IAAI,CAACN,QAAQ,GAAGC,MAAM,CAAC,IAAIrC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAClF;IACA,IAAI,CAACF,MAAM,CAACH,MAAM,GAAG,CAAC,CAAAY,EAAA,GAAAgC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjB,IAAI,cAAAf,EAAA,cAAAA,EAAA,GAAIjB,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,EAAE,CAAAsB,EAAA,GAAAF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpB,IAAI,cAAAsB,EAAA,cAAAA,EAAA,GAAIC,QAAQ,CAAQ;EAC5F,CAAC;EAEOpE,OAAA,CAAAwC,SAAA,CAAAH,kBAAkB,GAA1B,UAA2Bd,GAAgC;;IACjD,IAAAV,IAAI,GAAK,IAAI,CAAAA,IAAT;IACN,IAAAwD,EAAA,GAAA1C,MAAA,CAAeH,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC+C,SAAS,EAAE/C,GAAG,CAAC;MAAzDgD,KAAK,GAAAF,EAAA;MAAEG,GAAG,GAAAH,EAAA,GAA+C;IAChE,IAAMP,UAAU,GAAGjD,IAAI,CAAC4D,IAAI,CAAC,UAAC3C,EAAQ;UAANe,IAAI,GAAAf,EAAA,CAAAe,IAAA;MAAO,OAAAA,IAAI,KAAK0B,KAAK;IAAd,CAAc,CAAC;IAC1D,IAAMN,QAAQ,GAAGpD,IAAI,CAAC4D,IAAI,CAAC,UAAC3C,EAAQ;UAANe,IAAI,GAAAf,EAAA,CAAAe,IAAA;MAAO,OAAAA,IAAI,KAAK2B,GAAG;IAAZ,CAAY,CAAC;IACtD,IAAI,CAACnD,MAAM,CAACH,MAAM,GAAG,CAAC,CAAAY,EAAA,GAAAgC,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEjB,IAAI,cAAAf,EAAA,cAAAA,EAAA,GAAI,CAAAqC,EAAA,GAAAtD,IAAI,CAAC,CAAC,CAAC,cAAAsD,EAAA,uBAAAA,EAAA,CAAEtB,IAAI,EAAE,CAAAa,EAAA,GAAAO,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpB,IAAI,cAAAa,EAAA,cAAAA,EAAA,GAAIU,QAAQ,CAAQ;EAC7F,CAAC;EAEOpE,OAAA,CAAAwC,SAAA,CAAAkC,UAAU,GAAlB,UAAmBC,KAAuB;;IAChC,IAAA9D,IAAI,GAAK,IAAI,CAAAA,IAAT;IACN,IAAA+D,EAAA,GAAAjD,MAAA,CAAyBgD,KAAK;MAA7BE,UAAU,GAAAD,EAAA;MAAEE,QAAQ,GAAAF,EAAA,GAAS;IACpC,IAAI,CAACvD,MAAM,CAACH,MAAM,GAAG,CAAC,CAAAiD,EAAA,IAAArC,EAAA,GAAAjB,IAAI,CAACgE,UAAU,CAAC,cAAA/C,EAAA,uBAAAA,EAAA,CAAEe,IAAI,cAAAsB,EAAA,cAAAA,EAAA,GAAItD,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,EAAE,CAAAwB,EAAA,IAAAX,EAAA,OAAI,CAAC7C,IAAI,CAACiE,QAAQ,CAAC,cAAApB,EAAA,uBAAAA,EAAA,CAAEb,IAAI,cAAAwB,EAAA,cAAAA,EAAA,GAAID,QAAQ,CAAQ;EAC7G,CAAC;EAKD9B,MAAA,CAAAC,cAAA,CAAYvC,OAAA,CAAAwC,SAAA,gBAAY;IAHxB;;;SAGA,SAAAC,CAAA;MACQ,IAAAX,EAAA,GAA4B,IAAI,CAACT,MAAM;QAArCH,MAAM,GAAAY,EAAA,CAAAZ,MAAA;QAAEc,aAAa,GAAAF,EAAA,CAAAE,aAAgB;MACvC,IAAAmC,EAAA,GAAAxC,MAAA,CAAeH,KAAK,CAACC,OAAO,CAACP,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACoD,SAAS,EAAEpD,MAAM,CAAC;QAAlEqD,KAAK,GAAAJ,EAAA;QAAEK,GAAG,GAAAL,EAAA,GAAwD;MAEjE,IAAAtD,IAAI,GAAK,IAAI,CAAAA,IAAT;MACZ,IAAMgD,MAAM,GAAGhD,IAAI,CAACgD,MAAM;MAC1B,IAAMkB,OAAO,GAAG/C,aAAa,KAAK,OAAO;MAEzC,IAAMgD,aAAa,GAAG,SAAAA,CAAA;QACpB,IAAMC,eAAe,GAAGpE,IAAI,CAACqE,SAAS,CAAC,UAACpD,EAAQ;cAANe,IAAI,GAAAf,EAAA,CAAAe,IAAA;UAAO,OAAAA,IAAI,KAAK0B,KAAK;QAAd,CAAc,CAAC;QACpE,IAAIQ,OAAO,EAAE,OAAO,CAAC;QACrB,IAAIE,eAAe,GAAG,CAAC,CAAC,EAAE,OAAOA,eAAe,GAAGpB,MAAM;QACzD;QACA,OAAO,CAAC;MACV,CAAC;MAED,IAAMsB,WAAW,GAAG,SAAAA,CAAA;QAClB,IAAIX,GAAG,KAAKJ,QAAQ,EAAE,OAAO,CAAC;QAC9B,IAAMgB,aAAa,GAAGvE,IAAI,CAACqE,SAAS,CAAC,UAACpD,EAAQ;cAANe,IAAI,GAAAf,EAAA,CAAAe,IAAA;UAAO,OAAAA,IAAI,KAAK2B,GAAG;QAAZ,CAAY,CAAC;QAChE,IAAIY,aAAa,GAAG,CAAC,CAAC,EAAE,OAAOA,aAAa,GAAGvB,MAAM;QACrD;QACA,IAAIkB,OAAO,EAAE,OAAO,GAAG;QACvB,OAAO,CAAC;MACV,CAAC;MAED,OAAO,CAACC,aAAa,EAAE,EAAEG,WAAW,EAAE,CAAC;IACzC,CAAC;;;;EAED7C,MAAA,CAAAC,cAAA,CAAWvC,OAAA,CAAAwC,SAAA,UAAM;SAAjB,SAAAC,CAAA;MACQ,IAAAX,EAAA,GAA4B,IAAI,CAACT,MAAM;QAArCH,MAAM,GAAAY,EAAA,CAAAZ,MAAA;QAAEc,aAAa,GAAAF,EAAA,CAAAE,aAAgB;MACvC,IAAAmC,EAAA,GAAAxC,MAAA,CAAeH,KAAK,CAACC,OAAO,CAACP,MAAM,CAAC,GAAGA,MAAM,GAAG,CAAC,IAAI,CAACL,IAAI,CAAC,CAAC,CAAC,CAACgC,IAAI,EAAE3B,MAAM,CAAC;QAA1EqD,KAAK,GAAAJ,EAAA;QAAEK,GAAG,GAAAL,EAAA,GAAgE;MACjF,IAAInC,aAAa,KAAK,OAAO,EAAE,OAAOwC,GAAoB;MAC1D,OAAO,CAACD,KAAK,EAAEC,GAAG,CAAoC;IACxD,CAAC;;;;EAEOxE,OAAA,CAAAwC,SAAA,CAAA6C,eAAe,GAAvB,UAAwBC,KAAa;IAC3B,IAAAzE,IAAI,GAAK,IAAI,CAAAA,IAAT;IACZ,IAAMgD,MAAM,GAAGhD,IAAI,CAACgD,MAAM;IAC1B,IAAMc,KAAK,GAAGZ,IAAI,CAACC,KAAK,CAACsB,KAAK,IAAIzB,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9C,OAAOhD,IAAI,CAAC8D,KAAK,CAAC;EACpB,CAAC;EAEDrC,MAAA,CAAAC,cAAA,CAAYvC,OAAA,CAAAwC,SAAA,wBAAoB;SAAhC,SAAAC,CAAA;MACU,IAAAT,aAAa,GAAK,IAAI,CAACX,MAAM,CAAAW,aAAhB;MAEH,IAAAkB,MAAM,GACpB,IAAI,CAACqC,KAAK,CAAA9B,YAAA,CAAAP,MADU;MAExB,IAAIlB,aAAa,KAAK,OAAO,EAC3B,OAAO,UAACtB,IAAS;QAAK,WAAId,eAAe,CAAC;UAAEa,KAAK,EAAE;YAAEC,IAAI,EAAAA,IAAA;YAAEwC,MAAM,EAAAA,MAAA;YAAEsC,QAAQ,EAAEtC,MAAM,GAAG;UAAC;QAAE,CAAE,CAAC;MAAtE,CAAsE;MAE9F,OAAO;QACL,WAAI9D,IAAI,CAAC;UAAEqB,KAAK,EAAE;YAAEgF,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE,CAACxC,MAAM,GAAG,CAAC;YAAEyC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE1C,MAAM,GAAG,CAAC;YAAE2C,SAAS,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAS;QAAE,CAAE,CAAC;MAAvG,CAAuG;IAC3G,CAAC;;;;EAEO9F,OAAA,CAAAwC,SAAA,CAAAuD,aAAa,GAArB,UAAsBC,IAAU;IAAhC,IAAA5F,KAAA;IACU,IAAA2C,CAAC,GAAuBiD,IAAI,CAAAjD,CAA3B;MAAEC,CAAC,GAAoBgD,IAAI,CAAAhD,CAAxB;MAAEC,KAAK,GAAa+C,IAAI,CAAA/C,KAAjB;MAAEC,MAAM,GAAK8C,IAAI,CAAA9C,MAAT;IACrB,IAAApB,EAAA,GAA+B,IAAI,CAACT,MAAM;MAAxCW,aAAa,GAAAF,EAAA,CAAAE,aAAA;MAAEC,SAAS,GAAAH,EAAA,CAAAG,SAAgB;IACxC,IAAApB,IAAI,GAAK,IAAI,CAAAA,IAAT;IACN,IAAAsD,EAAA,GAAsD,IAAI,CAACpC,UAAU;MAAnErB,IAAI,GAAAyD,EAAA,CAAAzD,IAAA;MAAkBuF,yBAAyB,GAAA9B,EAAA,CAAArE,cAAoB;IAC3E,IAAM4D,EAAA,GAAiDnE,aAAa,CAAC,IAAI,CAACwC,UAAU,EAAE,OAAO,CAAC;MAAhFmE,UAAU,GAAAxC,EAAA,CAAAhD,IAAA;MAAKyF,qBAAqB,GAAAC,MAAA,CAAA1C,EAAA,EAA5C,QAA8C,CAA0C;IAC9F,IAAM2C,OAAO,GAAGrE,aAAa,KAAK,OAAO;IACzC,IAAItB,IAAI,KAAK,MAAM,EAAE;MACnB,OAAA4F,QAAA;QACEC,eAAe,EAAE,SAAAA,CAAA;UAAM,WAAI1G,cAAc,CAAC,EAAE,CAAC;QAAtB,CAAsB;QAC7C2G,aAAa,EAAE,SAAS;QACxBC,oBAAoB,EAAE,CAAC;QACvBC,sBAAsB,EAAE,IAAI;QAC5BC,aAAa,EAAEN,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC;QAChCO,YAAY,EAAEP,OAAO;QACrBQ,aAAa,EAAER,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC;QAChCS,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE9D,KAAK;QAClB+D,YAAY,EAAE,GAAG;QACjBC,WAAW,EAAE/D,MAAM,GAAG,CAAC;QACvBgE,SAAS,EAAEhE,MAAM,GAAG,CAAC;QACrBxC,IAAI,EAAEsB,aAAa;QACnBd,MAAM,EAAE,IAAI,CAACiG,YAAY;QACzBC,SAAS,EAAE,SAAAA,CAACC,KAAK;UACf,IAAIpB,yBAAyB,EAAE,OAAOA,yBAAyB,CAACoB,KAAK,CAAC;UACtE,IAAMxE,IAAI,GAAGzC,KAAI,CAACiF,eAAe,CAACgC,KAAK,CAAC,CAACxE,IAAI;UAC7C,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE,OAAO9C,aAAa,CAAC8C,IAAI,CAAC;UACxD,OAAOrD,UAAU,CAACqD,IAAI,EAAE,qBAAqB,CAAC;QAChD,CAAC;QACDyE,SAAS,EAAE,aAAAC,MAAA,CAAaxE,CAAC,QAAAwE,MAAA,CAAKvE,CAAC,MAAG;QAClC;QACA;QACAwE,MAAM,EAAE;MAAC,GACNrB,qBAAqB;IAE5B;IACA;IACA,IAAMsB,gBAAgB,GAAGzF,aAAa,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC;IAC1D,IAAM0F,aAAa,GAAG7G,IAAI,CAAC8G,GAAG,CAAC,UAAC7F,EAAS;UAAPuF,KAAK,GAAAvF,EAAA,CAAAuF,KAAA;MAAO,OAAAA,KAAK;IAAL,CAAK,CAAC;IACpD,OAAAf,QAAA;MACEmB,gBAAgB,EAAAA,gBAAA;MAChBlB,eAAe,EAAE,IAAI,CAACqB,oBAAoB;MAC1CpB,aAAa,EAAE,MAAM;MACrBC,oBAAoB,EAAE,GAAG;MACzBzE,aAAa,EAAE,QAAQ;MACvB6F,gBAAgB,EAAE,GAAG;MACrBC,wBAAwB,EAAE,CAAC;MAC3BC,cAAc,EAAE,SAAS;MACzBC,oBAAoB,EAAE,CAAC;MACvBC,sBAAsB,EAAE,CAAC;MACzBP,aAAa,EAAAA,aAAA;MACbQ,aAAa,EAAEjG,SAAS;MACxBkG,cAAc,EAAE,GAAG;MACnBpB,WAAW,EAAE9D,KAAK;MAClBiE,SAAS,EAAEhE,MAAM;MACjBxC,IAAI,EAAEsB,aAAa;MACnBd,MAAM,EAAE,IAAI,CAACiG,YAAY;MACzB;MACA;MACAG,SAAS,EAAE,aAAAC,MAAA,CAAaxE,CAAC,QAAAwE,MAAA,CAAKvE,CAAC,MAAG;MAClCwE,MAAM,EAAE;IAAC,GACNrB,qBAAqB;EAE5B,CAAC;EAEOnG,OAAA,CAAAwC,SAAA,CAAA4F,WAAW,GAAnB,UAAoBpC,IAAoC;IAApC,IAAAA,IAAA;MAAAA,IAAA,GAAa,IAAI,CAACT,KAAK,CAAC9B,YAAY;IAAA;IACtD,IAAI,CAACzC,QAAQ,CAACqH,MAAM,CAAC,IAAI,CAACtC,aAAa,CAACC,IAAI,CAAC,CAAC;EAChD,CAAC;EAEOhG,OAAA,CAAAwC,SAAA,CAAA8F,eAAe,GAAvB;IACE,IAAI,CAACtH,QAAQ,CAACuH,SAAS,CAAC,IAAI,CAACpB,YAAY,EAAE,IAAI,CAAC;IAChD,IAAI,CAAChG,kBAAkB,CAAC,IAAI,CAACgG,YAAY,CAAC;EAC5C,CAAC;EAEOnH,OAAA,CAAAwC,SAAA,CAAAgG,YAAY,GAApB,UAAqBxC,IAAU;IACrB,IAAAnF,IAAI,GAAK,IAAI,CAAAA,IAAT;IACN,IAAAiB,EAAA,GAA0D,IAAI,CAACC,UAAU;MAAvE0G,QAAQ,GAAA3G,EAAA,CAAA2G,QAAA;MAAkBxC,yBAAyB,GAAAnE,EAAA,CAAAhC,cAAoB;IAC/E,IAAM4I,oBAAoB,GAAGnJ,aAAa,CAAC,IAAI,CAACwC,UAAU,EAAE,MAAM,CAAC;IAE3D,IAAAgB,CAAC,GAAeiD,IAAI,CAAAjD,CAAnB;MAAEC,CAAC,GAAYgD,IAAI,CAAAhD,CAAhB;MAAEC,KAAK,GAAK+C,IAAI,CAAA/C,KAAT;IACnB;IACA,IAAM0F,QAAQ,GAAGjH,aAAA,CAAAA,aAAA,KAAAC,MAAA,CAAId,IAAI,YAAE;MAAEgC,IAAI,EAAE;IAAC,CAAE,UAAE8E,GAAG,CAAC,UAAC7F,EAAQ,EAAE6C,KAAK,EAAEiE,GAAG;UAAlB/F,IAAI,GAAAf,EAAA,CAAAe,IAAA;MAAmB,OAAC;QACrEgG,KAAK,EAAE,GAAAtB,MAAA,CAAG1E,IAAI,CAAE;QAChBwE,KAAK,EAAE1C,KAAK,IAAIiE,GAAG,CAAC/E,MAAM,GAAG,CAAC,CAAC;QAC/BhB,IAAI,EAAAA;OACL;IAJqE,CAIpE,CAAC;IACH,IAAMpC,KAAK,GAAA6F,QAAA;MACT3F,QAAQ,EAAE,CAACoC,CAAC,EAAEC,CAAC,CAAC;MAChBpC,MAAM,EAAE,CAACmC,CAAC,GAAGE,KAAK,EAAED,CAAC,CAAC;MACtBnC,IAAI,EAAE8H,QAAQ;MACd;MACAG,WAAW,EAAE,SAAAA,CAACC,MAAM,EAAEpE,KAAK;QAAK,OAAAA,KAAK,GAAGgE,QAAQ,CAAC9E,MAAM,GAAG,CAAC;MAA3B,CAA2B;MAC3D/D,cAAc,EAAE,SAAAA,CAACgC,EAAQ;YAANe,IAAI,GAAAf,EAAA,CAAAe,IAAA;QACrB,OAAAoD,yBAAyB,GAAGA,yBAAyB,CAACpD,IAAI,CAAC,GAAG/C,cAAc,CAAC+C,IAAI,EAAE4F,QAAQ,CAAC;MAA5F;IAA4F,GAC3FC,oBAAoB,CACxB;IACD,OAAOjI,KAAK;EACd,CAAC;EAEOT,OAAA,CAAAwC,SAAA,CAAAwG,UAAU,GAAlB,UAAmBhD,IAAgC;IAAhC,IAAAA,IAAA;MAAAA,IAAA,GAAa,IAAI,CAACT,KAAK,CAACjC,QAAQ;IAAA;IACzC,IAAA5C,IAAI,GAAK,IAAI,CAACqB,UAAU,CAAArB,IAApB;IACZ,IAAIA,IAAI,KAAK,OAAO,EAAE;IACtB,IAAI,CAACH,IAAI,CAAC8H,MAAM,CAAC,IAAI,CAACG,YAAY,CAACxC,IAAI,CAAC,CAAC;EAC3C,CAAC;EAEOhG,OAAA,CAAAwC,SAAA,CAAAyG,gBAAgB,GAAxB,UAAyBjD,IAAsC;IAAtC,IAAAA,IAAA;MAAAA,IAAA,GAAa,IAAI,CAACT,KAAK,CAAClC,cAAc;IAAA;IACrD,IAAA3C,IAAI,GAAK,IAAI,CAACqB,UAAU,CAAArB,IAApB;IACN,IAAAoB,EAAA,GAA6C,IAAI,CAACT,MAAM;MAAtDc,KAAK,GAAAL,EAAA,CAAAK,KAAA;MAAED,KAAK,GAAAJ,EAAA,CAAAI,KAAA;MAAEF,aAAa,GAAAF,EAAA,CAAAE,aAAA;MAAEC,SAAS,GAAAH,EAAA,CAAAG,SAAgB;IAC9D,IAAMiH,0BAA0B,GAAG3J,aAAa,CAAC,IAAI,CAACwC,UAAU,EAAE,YAAY,CAAC;IAE/E,IAAMoH,IAAI,GAAG,IAAI;IAEjB,IAAM1I,KAAK,GAAA6F,QAAA,CAAAA,QAAA,CAAAA,QAAA,KACNN,IAAI;MACPR,QAAQ,EAAE,EAAE;MACZtD,KAAK,EAAAA,KAAA;MACLC,KAAK,EAAAA,KAAA;MACLH,aAAa,EAAAA,aAAA;MACbC,SAAS,EAAAA,SAAA;MACThB,QAAQ,WAAAA,CAACP,IAAI,EAAEoB,EAAS;YAAPuF,KAAK,GAAAvF,EAAA,CAAAuF,KAAA;QACpB,QAAQ3G,IAAI;UACV,KAAK,OAAO;YACVyI,IAAI,CAACC,aAAa,EAAE;YACpB;UACF,KAAK,OAAO;YACVD,IAAI,CAACE,iBAAiB,CAAChC,KAAK,CAAC;YAC7B;UACF,KAAK,UAAU;YACb8B,IAAI,CAACG,gBAAgB,EAAE;YACvB;UACF,KAAK,WAAW;YACd,IAAIjC,KAAK,KAAK,MAAM,EAAE8B,IAAI,CAACI,YAAY,EAAE,CAAC,KACrCJ,IAAI,CAACK,aAAa,EAAE;YACzB;UACF,KAAK,SAAS;YACZL,IAAI,CAACM,eAAe,EAAE;YACtB;UACF,KAAK,eAAe;YAClBN,IAAI,CAACO,yBAAyB,CAACrC,KAAK,CAAC;YACrC;UACF,KAAK,WAAW;YACd8B,IAAI,CAACQ,qBAAqB,CAACtC,KAAK,CAAC;YACjC;UACF;YACE;QACJ;MACF;IAAC,IACE6B,0BAA0B,CAC9B;IAED,IAAIxI,IAAI,KAAK,MAAM,EAAE;MACnBD,KAAK,CAACmJ,SAAS,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC;IACjG;IAEA,IAAI,CAACxI,UAAU,CAACiH,MAAM,CAAC5H,KAAK,CAAC;EAC/B,CAAC;EAEOT,OAAA,CAAAwC,SAAA,CAAAX,gBAAgB,GAAxB,UAAyBP,UAAgB;IAC/B,IAAAT,IAAI,GAAK,IAAI,CAAAA,IAAT;IACJ,IAAAI,QAAQ,GAAK,IAAI,CAACc,UAAU,CAAAd,QAApB;IACV,IAAAa,EAAA,GAA4B,IAAI,CAACT,MAAM;MAArCH,MAAM,GAAAY,EAAA,CAAAZ,MAAA;MAAEc,aAAa,GAAAF,EAAA,CAAAE,aAAgB;IACvC,IAAAmC,EAAA,GAAAxC,MAAA,CAAeT,MAAa;MAA3BqD,KAAK,GAAAJ,EAAA;MAAEK,GAAG,GAAAL,EAAA,GAAiB;IAClC,IAAM0F,OAAO,GAAGrF,GAAG,KAAKJ,QAAQ,GAAGvD,IAAI,CAACiJ,EAAE,CAAC,CAAC,CAAC,CAAE,CAACjH,IAAI,GAAG2B,GAAG;IAC1D,IAAMuF,SAAS,GAAG/H,aAAa,KAAK,OAAO,GAAG,CAACuC,KAAK,EAAEsF,OAAO,CAAC,GAAGA,OAAO;IAExE,IAAMG,OAAO,GAAG,SAAAA,CAACC,IAAiC,EAAEC,IAAiC;MACnF,IAAI1I,KAAK,CAACC,OAAO,CAACwI,IAAI,CAAC,EAAE;QACvB,IAAI,CAACzI,KAAK,CAACC,OAAO,CAACyI,IAAI,CAAC,EAAE,OAAO,KAAK;QACtC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC,EAAE;UACvB,IAAID,IAAI,CAAC,CAAC,CAAC,KAAKC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;UACpC,IAAID,IAAI,CAAC,CAAC,CAAC,KAAK7F,QAAQ,IAAI8F,IAAI,CAAC,CAAC,CAAC,KAAK9F,QAAQ,EAAE,OAAO,IAAI;QAC/D;QACA,OAAO,KAAK;MACd;MACA,IAAI5C,KAAK,CAACC,OAAO,CAACyI,IAAI,CAAC,EAAE,OAAO,KAAK;MACrC,OAAOD,IAAI,KAAKC,IAAI;IACtB,CAAC;IACD;IACA,IAAI,CAAC5I,UAAU,IAAI,CAAC0I,OAAO,CAAC1I,UAAU,EAAEyI,SAAS,CAAC,EAAE;MAClD9I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGe,aAAa,KAAK,OAAO,GAAG,CAACuC,KAAK,EAAEsF,OAAO,CAAC,GAAGA,OAAO,CAAC;IACpE;EACF,CAAC;EAaO7J,OAAA,CAAAwC,SAAA,CAAA4G,aAAa,GAArB,UAAsBe,YAAsB;;IAClC,IAAAnI,aAAa,GAAK,IAAI,CAACX,MAAM,CAAAW,aAAhB;IACrB,IAAI,CAACwH,aAAa,EAAE;IACpB,IAAI,CAAC5H,iBAAiB,CAACI,aAAa,KAAK,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACnE,IAAI,CAACiH,gBAAgB,EAAE;IACvB,IAAI,CAACX,eAAe,EAAE;IACtB,IAAI,CAAC6B,YAAY,EAAE;MACjB,CAAAhG,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAEsI,OAAO,cAAAjG,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,CAAI;MAC5B,IAAI,CAACD,gBAAgB,EAAE;IACzB;EACF,CAAC;EAEM7B,OAAA,CAAAwC,SAAA,CAAA6H,KAAK,GAAZ;IACE,IAAI,CAACjB,aAAa,EAAE;EACtB,CAAC;EAEOpJ,OAAA,CAAAwC,SAAA,CAAA8H,aAAa,GAArB,UAAsBC,SAAiC,EAAEJ,YAAsB;IACrE,IAAAtJ,IAAI,GAAK,IAAI,CAAAA,IAAT;IACZ,IAAMgD,MAAM,GAAGhD,IAAI,CAACgD,MAAM;IACpB,IAAA/B,EAAA,GAAsC,IAAI,CAACT,MAAM;MAA/CH,MAAM,GAAAY,EAAA,CAAAZ,MAAA;MAAEc,aAAa,GAAAF,EAAA,CAAAE,aAAA;MAAEI,QAAQ,GAAAN,EAAA,CAAAM,QAAgB;IACjD,IAAA+B,EAAA,GAAAxC,MAAA,CAAuBT,MAAO;MAA7BsJ,SAAS,GAAArG,EAAA;MAAE0F,OAAO,GAAA1F,EAAA,GAAW;IACpC,IAAMU,UAAU,GAAGhE,IAAI,CAACqE,SAAS,CAAC,UAACpD,EAAQ;UAANe,IAAI,GAAAf,EAAA,CAAAe,IAAA;MAAO,OAAAA,IAAI,KAAK2H,SAAS;IAAlB,CAAkB,CAAC;IACnE,IAAI1F,QAAQ,GAAGjE,IAAI,CAACqE,SAAS,CAAC,UAACpD,EAAQ;UAANe,IAAI,GAAAf,EAAA,CAAAe,IAAA;MAAO,OAAAA,IAAI,KAAKgH,OAAO;IAAhB,CAAgB,CAAC;IAC7D,IAAI/E,QAAQ,KAAK,CAAC,CAAC,EAAEA,QAAQ,GAAGjB,MAAM;IACtC,IAAM4G,IAAI,GAAGF,SAAS,KAAK,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;IAC9C,IAAIG,cAAgC;IACpC,IAAI1I,aAAa,KAAK,OAAO,EAAE;MAC7B;MACA,IAAII,QAAQ,KAAK,KAAK,EAAE;QACtBsI,cAAc,GAAG,CAAC7F,UAAU,EAAEC,QAAQ,GAAG2F,IAAI,CAAC;QAC9C;QACA,IAAIA,IAAI,KAAK,CAAC,CAAC,IAAI5F,UAAU,KAAKC,QAAQ,EAAE;UAC1C4F,cAAc,GAAG,CAAC7F,UAAU,EAAEhB,MAAM,CAAC;QACvC;MACF;MACA;MAAA,KACK6G,cAAc,GAAG,CAAC7F,UAAU,GAAG4F,IAAI,EAAE3F,QAAQ,GAAG2F,IAAI,CAAC;IAC5D;IACA;IAAA,KACKC,cAAc,GAAG,CAAC7F,UAAU,EAAEC,QAAQ,GAAG2F,IAAI,CAAC;IAEnD,IAAME,gBAAgB,GAAG,SAAAA,CAACC,OAAyB;MACjD;MACM,IAAA9I,EAAA,GAAAH,MAAA,CAAeiJ,OAAO,CAAC9H,IAAI,CAAC,UAACH,CAAC,EAAEC,CAAC;UAAK,OAAAD,CAAC,GAAGC,CAAC;QAAL,CAAK,CAAC;QAA3C2B,KAAK,GAAAzC,EAAA;QAAE0C,GAAG,GAAA1C,EAAA,GAAiC;MAClD;MACA,IAAM+I,UAAU,GAAG,SAAAA,CAAClG,KAAa;QAAK,OAAAzF,KAAK,CAACyF,KAAK,EAAE,CAAC,EAAEd,MAAM,CAAC;MAAvB,CAAuB;MAC7D;MACA,IAAIW,GAAG,GAAGX,MAAM,EAAE;QAChB;QACA,IAAI7B,aAAa,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5C;QACA,IAAII,QAAQ,KAAK,KAAK,EAAE,OAAO,CAACyI,UAAU,CAACtG,KAAK,CAAC,EAAEsG,UAAU,CAACtG,KAAK,CAAC,CAAC;QACrE;QACA,OAAO,CAAC,CAAC,EAAEsG,UAAU,CAACrG,GAAG,GAAGD,KAAK,CAAC,CAAC;MACrC;MACA;MACA,IAAIA,KAAK,GAAG,CAAC,EAAE;QACb,IAAInC,QAAQ,KAAK,KAAK,EAAE,OAAO,CAAC,CAAC,EAAEyI,UAAU,CAACrG,GAAG,CAAC,CAAC;QACnD,OAAO,CAACqG,UAAU,CAACtG,KAAK,GAAGV,MAAM,GAAGW,GAAG,CAAC,EAAEX,MAAM,CAAC;MACnD;MACA,OAAO,CAACgH,UAAU,CAACtG,KAAK,CAAC,EAAEsG,UAAU,CAACrG,GAAG,CAAC,CAAC;IAC7C,CAAC;IAED,IAAMsG,iBAAiB,GAAGH,gBAAgB,CAACD,cAAc,CAAC;IAE1D,IAAI,CAAChG,UAAU,CAACoG,iBAAiB,CAAC;IAClC,IAAI,CAACxC,eAAe,EAAE;IACtB,OAAOwC,iBAAiB;EAC1B,CAAC;EAEO9K,OAAA,CAAAwC,SAAA,CAAA8G,gBAAgB,GAAxB,UAAyBa,YAAsB;;IAC7C,IAAMS,OAAO,GAAG,IAAI,CAACN,aAAa,CAAC,UAAU,EAAEH,YAAY,CAAC;IAC5D,IAAI,CAACA,YAAY,EAAE;MACjB,CAAAhG,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAEiJ,UAAU,cAAA5G,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,CAAI;MAC/B,IAAI,CAACD,gBAAgB,EAAE;IACzB;IAEA,OAAO+I,OAAO;EAChB,CAAC;EAEM5K,OAAA,CAAAwC,SAAA,CAAAwI,QAAQ,GAAf;IACE,IAAI,CAAC1B,gBAAgB,EAAE;EACzB,CAAC;EAEOtJ,OAAA,CAAAwC,SAAA,CAAA+G,YAAY,GAApB,UAAqBY,YAAsB;IAA3C,IAAA/J,KAAA;;IACU,IAAAS,IAAI,GAAK,IAAI,CAAAA,IAAT;IACJ,IAAAoK,IAAI,GAAK,IAAI,CAAClJ,UAAU,CAAAkJ,IAApB;IACJ,IAAAvH,EAAA,GAAc,IAAI,CAACrC,MAAM,CAAAa,KAAhB;MAATA,KAAK,GAAAwB,EAAA,cAAG,CAAC,GAAAA,EAAA;IACjB,IAAI,CAACwH,YAAY,GAAGC,MAAM,CAACC,WAAW,CAAC;MACrC,IAAMR,OAAO,GAAGxK,KAAI,CAACqJ,eAAe,EAAE;MACtC;MACA,IAAImB,OAAO,CAAC,CAAC,CAAC,KAAK/J,IAAI,CAACgD,MAAM,IAAI,CAACoH,IAAI,EAAE;QACvC;QACA7K,KAAI,CAACoJ,aAAa,EAAE;QACpBpJ,KAAI,CAAC6I,gBAAgB,EAAE;MACzB;IACF,CAAC,EAAE,IAAI,GAAG/G,KAAK,CAAC;IAChB,IAAI,CAACb,MAAM,CAACc,KAAK,GAAG,MAAM;IAC1B,CAACgI,YAAY,KAAI,CAAAhG,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAEuJ,MAAM,cAAAlH,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,CAAI;EAC9C,CAAC;EAEM9B,OAAA,CAAAwC,SAAA,CAAA8I,IAAI,GAAX;IACE,IAAI,CAAC/B,YAAY,EAAE;EACrB,CAAC;EAEOvJ,OAAA,CAAAwC,SAAA,CAAAgH,aAAa,GAArB,UAAsBW,YAAsB;;IAC1CoB,aAAa,CAAC,IAAI,CAACL,YAAY,CAAC;IAChC,IAAI,CAAC7J,MAAM,CAACc,KAAK,GAAG,OAAO;IAC3B,CAACgI,YAAY,KAAI,CAAAhG,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAE0J,OAAO,cAAArH,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,CAAI;EAC/C,CAAC;EAEM9B,OAAA,CAAAwC,SAAA,CAAAiJ,KAAK,GAAZ;IACE,IAAI,CAACjC,aAAa,EAAE;EACtB,CAAC;EAEOxJ,OAAA,CAAAwC,SAAA,CAAAiH,eAAe,GAAvB,UAAwBU,YAAsB;;IAC5C,IAAMS,OAAO,GAAG,IAAI,CAACN,aAAa,CAAC,SAAS,EAAEH,YAAY,CAAC;IAC3D,IAAI,CAACA,YAAY,EAAE;MACjB,CAAAhG,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAE4J,SAAS,cAAAvH,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,CAAI;MAC9B,IAAI,CAACD,gBAAgB,EAAE;IACzB;IACA,OAAO+I,OAAO;EAChB,CAAC;EAEM5K,OAAA,CAAAwC,SAAA,CAAAmJ,OAAO,GAAd;IACE,IAAI,CAAClC,eAAe,EAAE;EACxB,CAAC;EAEOzJ,OAAA,CAAAwC,SAAA,CAAA6G,iBAAiB,GAAzB,UAA0BhC,KAAa;;IACrC,IAAI,CAAChG,MAAM,CAACa,KAAK,GAAGmF,KAAK;IACjB,IAAAlF,KAAK,GAAK,IAAI,CAACd,MAAM,CAAAc,KAAhB;IACb,IAAIA,KAAK,KAAK,MAAM,EAAE;MACpB;MACA,IAAI,CAACqH,aAAa,CAAC,IAAI,CAAC;MACxB,IAAI,CAACD,YAAY,CAAC,IAAI,CAAC;IACzB;IACA,CAAApF,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAE8J,aAAa,cAAAzH,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,EAAGuF,KAAK,CAAC;EACzC,CAAC;EAEOrH,OAAA,CAAAwC,SAAA,CAAAkH,yBAAyB,GAAjC,UAAkChJ,IAAwC;;IACxE,IAAI,CAACW,MAAM,CAACW,aAAa,GAAGtB,IAAI;IAChC,IAAI,CAAC0H,WAAW,EAAE;IAClB,CAAAjE,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAE+J,qBAAqB,cAAA1H,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,EAAGpB,IAAI,CAAC;EAChD,CAAC;EAEOV,OAAA,CAAAwC,SAAA,CAAAmH,qBAAqB,GAA7B,UAA8BjJ,IAAoC;;IAChE,IAAI,CAACW,MAAM,CAACY,SAAS,GAAGvB,IAAI;IAC5B,IAAI,CAAC0H,WAAW,EAAE;IAClB,CAAAjE,EAAA,IAAArC,EAAA,OAAI,CAACC,UAAU,cAAAD,EAAA,uBAAAA,EAAA,CAAEgK,iBAAiB,cAAA3H,EAAA,uBAAAA,EAAA,CAAA9D,IAAA,CAAAyB,EAAA,EAAGpB,IAAI,CAAC;EAC5C,CAAC;EASDV,OAAA,CAAAwC,SAAA,CAAAuJ,MAAM,GAAN;IACQ,IAAAjK,EAAA,GAA6C,IAAI,CAACyD,KAAK;MAArDjC,QAAQ,GAAAxB,EAAA,CAAAwB,QAAA;MAAED,cAAc,GAAAvB,EAAA,CAAAuB,cAAA;MAAEI,YAAY,GAAA3B,EAAA,CAAA2B,YAAe;IAC7D,IAAI,CAACwF,gBAAgB,CAAC5F,cAAc,CAAC;IACrC,IAAI,CAAC2F,UAAU,CAAC1F,QAAQ,CAAC;IACzB,IAAI,CAAC8E,WAAW,CAAC3E,YAAY,CAAC;IAE9B,IAAI,IAAI,CAACpC,MAAM,CAACc,KAAK,KAAK,MAAM,EAAE,IAAI,CAACoH,YAAY,EAAE;EACvD,CAAC;EAEDvJ,OAAA,CAAAwC,SAAA,CAAAwJ,OAAO,GAAP;IACE/L,MAAA,CAAAuC,SAAK,CAACwJ,OAAO,CAAA3L,IAAA,MAAE;IACf,IAAI,CAACmJ,aAAa,CAAC,IAAI,CAAC;EAC1B,CAAC;EAnhBMxJ,OAAA,CAAAM,cAAc,GAAmB;IACtCG,KAAK,EAAE;MACLsC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJiJ,aAAa,EAAE,SAAS;MACxBC,kBAAkB,EAAE,MAAM;MAC1BC,qBAAqB,EAAE,KAAK;MAC5BC,kBAAkB,EAAE,mBAAmB;MACvCC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,SAAS;MACzBC,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,cAAc,EAAE,SAAS;MACzBC,cAAc,EAAE,KAAK;MACrBzK,SAAS,EAAE,MAAM;MACjB0K,eAAe,EAAE,QAAQ;MACzBxJ,gBAAgB,EAAE,EAAE;MACpBtC,IAAI,EAAE,EAAE;MACR4H,QAAQ,EAAE,KAAK;MACfwC,IAAI,EAAE,KAAK;MACX7I,QAAQ,EAAE,KAAK;MACfJ,aAAa,EAAE,OAAO;MACtBtB,IAAI,EAAE;;GAET;EA4fH,OAAAV,OAAC;CAAA,CArhB4Bb,SAAS;SAAzBa,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}