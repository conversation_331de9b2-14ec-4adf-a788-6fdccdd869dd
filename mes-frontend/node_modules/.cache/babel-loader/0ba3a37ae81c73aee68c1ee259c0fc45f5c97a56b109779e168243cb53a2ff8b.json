{"ast": null, "code": "/**\n * Parse heatmap gradient.\n */\nexport function parseGradient(gradient) {\n  if (typeof gradient === 'string') {\n    return gradient.split(' ').map(stop => {\n      const [r, c] = stop.split(':');\n      return [+r, c];\n    });\n  }\n  return gradient;\n}", "map": {"version": 3, "names": ["parseGradient", "gradient", "split", "map", "stop", "r", "c"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/heatmap/renderer/gradient.ts"], "sourcesContent": ["import { HeatmapGradient } from './types';\n\n/**\n * Parse heatmap gradient.\n */\nexport function parseGradient(\n  gradient: HeatmapGradient,\n): Array<[number, string]> {\n  if (typeof gradient === 'string') {\n    return gradient.split(' ').map((stop) => {\n      const [r, c] = stop.split(':');\n      return [+r, c];\n    });\n  }\n  return gradient;\n}\n"], "mappings": "AAEA;;;AAGA,OAAM,SAAUA,aAAaA,CAC3BC,QAAyB;EAEzB,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;IAChC,OAAOA,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAEC,IAAI,IAAI;MACtC,MAAM,CAACC,CAAC,EAAEC,CAAC,CAAC,GAAGF,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC;MAC9B,OAAO,CAAC,CAACG,CAAC,EAAEC,CAAC,CAAC;IAChB,CAAC,CAAC;;EAEJ,OAAOL,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}