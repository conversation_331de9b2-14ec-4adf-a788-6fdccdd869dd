{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { getCallbackValue } from '../../../util';\nimport { isAxisHorizontal } from '../guides/line';\nimport { boundTest } from '../utils/test';\nfunction inferTextBaseline(attr) {\n  var type = attr.type,\n    labelDirection = attr.labelDirection;\n  if (type === 'linear' && isAxisHorizontal(attr)) {\n    return labelDirection === 'negative' ? 'bottom' : 'top';\n  }\n  return 'middle';\n}\nexport default function wrapLabels(labels, overlapCfg, attr, utils, main) {\n  var _a;\n  var _b = overlapCfg.maxLines,\n    maxLines = _b === void 0 ? 3 : _b,\n    _c = overlapCfg.recoverWhenFailed,\n    recoverWhenFailed = _c === void 0 ? true : _c,\n    _d = overlapCfg.margin,\n    margin = _d === void 0 ? [0, 0, 0, 0] : _d;\n  var wordWrapWidth = getCallbackValue((_a = overlapCfg.wordWrapWidth) !== null && _a !== void 0 ? _a : 50, [main]);\n  var defaultLines = labels.map(function (label) {\n    return label.attr('maxLines') || 1;\n  });\n  var minLines = Math.min.apply(Math, __spreadArray([], __read(defaultLines), false));\n  var runAndPassed = function () {\n    return boundTest(labels, attr, margin).length < 1;\n  };\n  var textBaseline = inferTextBaseline(attr);\n  var setLabelsWrap = function (lines) {\n    return labels.forEach(function (label, index) {\n      var maxLines = Array.isArray(lines) ? lines[index] : lines;\n      utils.wrap(label, wordWrapWidth, maxLines, textBaseline);\n    });\n  };\n  if (minLines > maxLines) return;\n  if (attr.type === 'linear' && isAxisHorizontal(attr)) {\n    setLabelsWrap(maxLines);\n    if (runAndPassed()) {\n      return;\n    }\n  } else {\n    for (var lines = minLines; lines <= maxLines; lines++) {\n      setLabelsWrap(lines);\n      if (runAndPassed()) return;\n    }\n  }\n  if (recoverWhenFailed) {\n    setLabelsWrap(defaultLines);\n  }\n}", "map": {"version": 3, "names": ["getCallbackValue", "isAxisHorizontal", "boundTest", "inferTextBaseline", "attr", "type", "labelDirection", "wrapLabels", "labels", "overlapCfg", "utils", "main", "_b", "maxLines", "_c", "recoverWhenFailed", "_d", "margin", "wordWrapWidth", "_a", "defaultLines", "map", "label", "minLines", "Math", "min", "apply", "__spread<PERSON><PERSON>y", "__read", "runAndPassed", "length", "textBaseline", "setLabelsWrap", "lines", "for<PERSON>ach", "index", "Array", "isArray", "wrap"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/overlap/autoWrap.ts"], "sourcesContent": ["import { getCallbackValue } from '../../../util';\nimport type { DisplayObject, Text } from '../../../shapes';\nimport { isAxisHorizontal } from '../guides/line';\nimport { AxisStyleProps, LinearAxisStyleProps, WrapOverlapCfg } from '../types';\nimport { boundTest } from '../utils/test';\n\nexport type Utils = {\n  wrap: (label: Text, wordWrapWidth: number, maxLines?: number, textBaseline?: string) => void;\n};\n\ntype WrapType = Parameters<Utils['wrap']>[1];\n\nfunction inferTextBaseline(attr: AxisStyleProps) {\n  const { type, labelDirection } = attr;\n  if (type === 'linear' && isAxisHorizontal(attr as Required<LinearAxisStyleProps>)) {\n    return labelDirection === 'negative' ? 'bottom' : 'top';\n  }\n  return 'middle';\n}\n\nexport default function wrapLabels(\n  labels: Text[],\n  overlapCfg: WrapOverlapCfg,\n  attr: AxisStyleProps,\n  utils: Utils,\n  main: DisplayObject\n) {\n  const { maxLines = 3, recoverWhenFailed = true, margin = [0, 0, 0, 0] } = overlapCfg;\n  const wordWrapWidth = getCallbackValue(overlapCfg.wordWrapWidth ?? 50, [main]);\n  const defaultLines = labels.map((label) => label.attr('maxLines') || 1);\n\n  const minLines = Math.min(...defaultLines);\n\n  const runAndPassed = () => boundTest(labels, attr, margin).length < 1;\n\n  const textBaseline = inferTextBaseline(attr);\n\n  const setLabelsWrap = (lines: WrapType | WrapType[]) =>\n    labels.forEach((label, index) => {\n      const maxLines = Array.isArray(lines) ? lines[index] : lines;\n      utils.wrap(label, wordWrapWidth, maxLines, textBaseline);\n    });\n\n  if (minLines > maxLines) return;\n\n  if (attr.type === 'linear' && isAxisHorizontal(attr as Required<LinearAxisStyleProps>)) {\n    setLabelsWrap(maxLines);\n    if (runAndPassed()) {\n      return;\n    }\n  } else {\n    for (let lines = minLines; lines <= maxLines; lines++) {\n      setLabelsWrap(lines);\n      if (runAndPassed()) return;\n    }\n  }\n\n  if (recoverWhenFailed) {\n    setLabelsWrap(defaultLines);\n  }\n}\n"], "mappings": ";AAAA,SAASA,gBAAgB,QAAQ,eAAe;AAEhD,SAASC,gBAAgB,QAAQ,gBAAgB;AAEjD,SAASC,SAAS,QAAQ,eAAe;AAQzC,SAASC,iBAAiBA,CAACC,IAAoB;EACrC,IAAAC,IAAI,GAAqBD,IAAI,CAAAC,IAAzB;IAAEC,cAAc,GAAKF,IAAI,CAAAE,cAAT;EAC5B,IAAID,IAAI,KAAK,QAAQ,IAAIJ,gBAAgB,CAACG,IAAsC,CAAC,EAAE;IACjF,OAAOE,cAAc,KAAK,UAAU,GAAG,QAAQ,GAAG,KAAK;EACzD;EACA,OAAO,QAAQ;AACjB;AAEA,eAAc,SAAUC,UAAUA,CAChCC,MAAc,EACdC,UAA0B,EAC1BL,IAAoB,EACpBM,KAAY,EACZC,IAAmB;;EAEX,IAAAC,EAAA,GAAkEH,UAAU,CAAAI,QAAhE;IAAZA,QAAQ,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;IAAEE,EAAA,GAAoDL,UAAU,CAAAM,iBAAtC;IAAxBA,iBAAiB,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAEE,EAAA,GAA0BP,UAAU,CAAAQ,MAAf;IAArBA,MAAM,GAAAD,EAAA,cAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAAA,EAAA;EACrE,IAAME,aAAa,GAAGlB,gBAAgB,CAAC,CAAAmB,EAAA,GAAAV,UAAU,CAACS,aAAa,cAAAC,EAAA,cAAAA,EAAA,GAAI,EAAE,EAAE,CAACR,IAAI,CAAC,CAAC;EAC9E,IAAMS,YAAY,GAAGZ,MAAM,CAACa,GAAG,CAAC,UAACC,KAAK;IAAK,OAAAA,KAAK,CAAClB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;EAA3B,CAA2B,CAAC;EAEvE,IAAMmB,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAAC,KAAA,CAARF,IAAI,EAAAG,aAAA,KAAAC,MAAA,CAAQR,YAAY,UAAC;EAE1C,IAAMS,YAAY,GAAG,SAAAA,CAAA;IAAM,OAAA3B,SAAS,CAACM,MAAM,EAAEJ,IAAI,EAAEa,MAAM,CAAC,CAACa,MAAM,GAAG,CAAC;EAA1C,CAA0C;EAErE,IAAMC,YAAY,GAAG5B,iBAAiB,CAACC,IAAI,CAAC;EAE5C,IAAM4B,aAAa,GAAG,SAAAA,CAACC,KAA4B;IACjD,OAAAzB,MAAM,CAAC0B,OAAO,CAAC,UAACZ,KAAK,EAAEa,KAAK;MAC1B,IAAMtB,QAAQ,GAAGuB,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,GAAGA,KAAK,CAACE,KAAK,CAAC,GAAGF,KAAK;MAC5DvB,KAAK,CAAC4B,IAAI,CAAChB,KAAK,EAAEJ,aAAa,EAAEL,QAAQ,EAAEkB,YAAY,CAAC;IAC1D,CAAC,CAAC;EAHF,CAGE;EAEJ,IAAIR,QAAQ,GAAGV,QAAQ,EAAE;EAEzB,IAAIT,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAIJ,gBAAgB,CAACG,IAAsC,CAAC,EAAE;IACtF4B,aAAa,CAACnB,QAAQ,CAAC;IACvB,IAAIgB,YAAY,EAAE,EAAE;MAClB;IACF;EACF,CAAC,MAAM;IACL,KAAK,IAAII,KAAK,GAAGV,QAAQ,EAAEU,KAAK,IAAIpB,QAAQ,EAAEoB,KAAK,EAAE,EAAE;MACrDD,aAAa,CAACC,KAAK,CAAC;MACpB,IAAIJ,YAAY,EAAE,EAAE;IACtB;EACF;EAEA,IAAId,iBAAiB,EAAE;IACrBiB,aAAa,CAACZ,YAAY,CAAC;EAC7B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}