{"ast": null, "code": "import isFunction from './is-function';\nimport isEqual from './is-equal';\n/**\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {Function} [fn] The function to customize comparisons.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * function isGreeting(value) {\n *   return /^h(?:i|ello)$/.test(value);\n * }\n *\n * function customizer(objValue, othValue) {\n *   if (isGreeting(objValue) && isGreeting(othValue)) {\n *     return true;\n *   }\n * }\n *\n * var array = ['hello', 'goodbye'];\n * var other = ['hi', 'goodbye'];\n *\n * isEqualWith(array, other, customizer);  // => true\n */\nexport default (function (value, other, fn) {\n  if (!isFunction(fn)) {\n    return isEqual(value, other);\n  }\n  return !!fn(value, other);\n});", "map": {"version": 3, "names": ["isFunction", "isEqual", "value", "other", "fn"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-equal-with.ts"], "sourcesContent": ["import isFunction from './is-function';\nimport isEqual from './is-equal';\n/**\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @param {Function} [fn] The function to customize comparisons.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * function isGreeting(value) {\n *   return /^h(?:i|ello)$/.test(value);\n * }\n *\n * function customizer(objValue, othValue) {\n *   if (isGreeting(objValue) && isGreeting(othValue)) {\n *     return true;\n *   }\n * }\n *\n * var array = ['hello', 'goodbye'];\n * var other = ['hi', 'goodbye'];\n *\n * isEqualWith(array, other, customizer);  // => true\n */\n\nexport default <T>(value: T, other: T, fn: (v1: T, v2: T) => boolean): boolean => {\n  if (!isFunction(fn)) {\n    return isEqual(value, other);\n  }\n  return !!fn(value, other);\n};\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,eAAe;AACtC,OAAOC,OAAO,MAAM,YAAY;AAChC;;;;;;;;;;;;;;;;;;;;;;AAuBA,gBAAe,UAAIC,KAAQ,EAAEC,KAAQ,EAAEC,EAA6B;EAClE,IAAI,CAACJ,UAAU,CAACI,EAAE,CAAC,EAAE;IACnB,OAAOH,OAAO,CAACC,KAAK,EAAEC,KAAK,CAAC;;EAE9B,OAAO,CAAC,CAACC,EAAE,CAACF,KAAK,EAAEC,KAAK,CAAC;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}