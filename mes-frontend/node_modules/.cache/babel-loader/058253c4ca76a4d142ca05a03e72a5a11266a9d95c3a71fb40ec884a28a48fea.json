{"ast": null, "code": "export default function (x, y, z) {\n  var nodes,\n    strength = 1;\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n  if (z == null) z = 0;\n  function force() {\n    var i,\n      n = nodes.length,\n      node,\n      sx = 0,\n      sy = 0,\n      sz = 0;\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], sx += node.x || 0, sy += node.y || 0, sz += node.z || 0;\n    }\n    for (sx = (sx / n - x) * strength, sy = (sy / n - y) * strength, sz = (sz / n - z) * strength, i = 0; i < n; ++i) {\n      node = nodes[i];\n      if (sx) {\n        node.x -= sx;\n      }\n      if (sy) {\n        node.y -= sy;\n      }\n      if (sz) {\n        node.z -= sz;\n      }\n    }\n  }\n  force.initialize = function (_) {\n    nodes = _;\n  };\n  force.x = function (_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n  force.y = function (_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n  force.z = function (_) {\n    return arguments.length ? (z = +_, force) : z;\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["x", "y", "z", "nodes", "strength", "force", "i", "n", "length", "node", "sx", "sy", "sz", "initialize", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force-3d/src/center.js"], "sourcesContent": ["export default function(x, y, z) {\n  var nodes, strength = 1;\n\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n  if (z == null) z = 0;\n\n  function force() {\n    var i,\n        n = nodes.length,\n        node,\n        sx = 0,\n        sy = 0,\n        sz = 0;\n\n    for (i = 0; i < n; ++i) {\n      node = nodes[i], sx += node.x || 0, sy += node.y || 0, sz += node.z || 0;\n    }\n\n    for (sx = (sx / n - x) * strength, sy = (sy / n - y) * strength, sz = (sz / n - z) * strength, i = 0; i < n; ++i) {\n      node = nodes[i];\n      if (sx) { node.x -= sx }\n      if (sy) { node.y -= sy; }\n      if (sz) { node.z -= sz; }\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  force.z = function(_) {\n    return arguments.length ? (z = +_, force) : z;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIC,KAAK;IAAEC,QAAQ,GAAG,CAAC;EAEvB,IAAIJ,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EACpB,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EACpB,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EAEpB,SAASG,KAAKA,CAAA,EAAG;IACf,IAAIC,CAAC;MACDC,CAAC,GAAGJ,KAAK,CAACK,MAAM;MAChBC,IAAI;MACJC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;IAEV,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBG,IAAI,GAAGN,KAAK,CAACG,CAAC,CAAC,EAAEI,EAAE,IAAID,IAAI,CAACT,CAAC,IAAI,CAAC,EAAEW,EAAE,IAAIF,IAAI,CAACR,CAAC,IAAI,CAAC,EAAEW,EAAE,IAAIH,IAAI,CAACP,CAAC,IAAI,CAAC;IAC1E;IAEA,KAAKQ,EAAE,GAAG,CAACA,EAAE,GAAGH,CAAC,GAAGP,CAAC,IAAII,QAAQ,EAAEO,EAAE,GAAG,CAACA,EAAE,GAAGJ,CAAC,GAAGN,CAAC,IAAIG,QAAQ,EAAEQ,EAAE,GAAG,CAACA,EAAE,GAAGL,CAAC,GAAGL,CAAC,IAAIE,QAAQ,EAAEE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAChHG,IAAI,GAAGN,KAAK,CAACG,CAAC,CAAC;MACf,IAAII,EAAE,EAAE;QAAED,IAAI,CAACT,CAAC,IAAIU,EAAE;MAAC;MACvB,IAAIC,EAAE,EAAE;QAAEF,IAAI,CAACR,CAAC,IAAIU,EAAE;MAAE;MACxB,IAAIC,EAAE,EAAE;QAAEH,IAAI,CAACP,CAAC,IAAIU,EAAE;MAAE;IAC1B;EACF;EAEAP,KAAK,CAACQ,UAAU,GAAG,UAASC,CAAC,EAAE;IAC7BX,KAAK,GAAGW,CAAC;EACX,CAAC;EAEDT,KAAK,CAACL,CAAC,GAAG,UAASc,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACP,MAAM,IAAIR,CAAC,GAAG,CAACc,CAAC,EAAET,KAAK,IAAIL,CAAC;EAC/C,CAAC;EAEDK,KAAK,CAACJ,CAAC,GAAG,UAASa,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACP,MAAM,IAAIP,CAAC,GAAG,CAACa,CAAC,EAAET,KAAK,IAAIJ,CAAC;EAC/C,CAAC;EAEDI,KAAK,CAACH,CAAC,GAAG,UAASY,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACP,MAAM,IAAIN,CAAC,GAAG,CAACY,CAAC,EAAET,KAAK,IAAIH,CAAC;EAC/C,CAAC;EAEDG,KAAK,CAACD,QAAQ,GAAG,UAASU,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACP,MAAM,IAAIJ,QAAQ,GAAG,CAACU,CAAC,EAAET,KAAK,IAAID,QAAQ;EAC7D,CAAC;EAED,OAAOC,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}