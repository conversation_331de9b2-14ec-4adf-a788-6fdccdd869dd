{"ast": null, "code": "var _ = require(\"./lodash\");\nmodule.exports = parentDummyChains;\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca && g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n      if (!ascending) {\n        while (pathIdx < path.length - 1 && g.node(pathV = path[pathIdx + 1]).minRank <= node.rank) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent && (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n  return {\n    path: vPath.concat(wPath.reverse()),\n    lca: lca\n  };\n}\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = {\n      low: low,\n      lim: lim++\n    };\n  }\n  _.forEach(g.children(), dfs);\n  return result;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "parent<PERSON>ummy<PERSON><PERSON><PERSON>", "g", "postorderNums", "postorder", "for<PERSON>ach", "graph", "dummy<PERSON><PERSON><PERSON>", "v", "node", "edgeObj", "pathData", "<PERSON><PERSON><PERSON>", "w", "path", "lca", "pathIdx", "pathV", "ascending", "maxRank", "rank", "length", "minRank", "setParent", "successors", "vPath", "wPath", "low", "Math", "min", "lim", "max", "parent", "push", "concat", "reverse", "result", "dfs", "children"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/parent-dummy-chains.js"], "sourcesContent": ["var _ = require(\"./lodash\");\n\nmodule.exports = parentDummyChains;\n\nfunction parentDummyChains(g) {\n  var postorderNums = postorder(g);\n\n  _.forEach(g.graph().dummyChains, function(v) {\n    var node = g.node(v);\n    var edgeObj = node.edgeObj;\n    var pathData = findPath(g, postorderNums, edgeObj.v, edgeObj.w);\n    var path = pathData.path;\n    var lca = pathData.lca;\n    var pathIdx = 0;\n    var pathV = path[pathIdx];\n    var ascending = true;\n\n    while (v !== edgeObj.w) {\n      node = g.node(v);\n\n      if (ascending) {\n        while ((pathV = path[pathIdx]) !== lca &&\n               g.node(pathV).maxRank < node.rank) {\n          pathIdx++;\n        }\n\n        if (pathV === lca) {\n          ascending = false;\n        }\n      }\n\n      if (!ascending) {\n        while (pathIdx < path.length - 1 &&\n               g.node(pathV = path[pathIdx + 1]).minRank <= node.rank) {\n          pathIdx++;\n        }\n        pathV = path[pathIdx];\n      }\n\n      g.setParent(v, pathV);\n      v = g.successors(v)[0];\n    }\n  });\n}\n\n// Find a path from v to w through the lowest common ancestor (LCA). Return the\n// full path and the LCA.\nfunction findPath(g, postorderNums, v, w) {\n  var vPath = [];\n  var wPath = [];\n  var low = Math.min(postorderNums[v].low, postorderNums[w].low);\n  var lim = Math.max(postorderNums[v].lim, postorderNums[w].lim);\n  var parent;\n  var lca;\n\n  // Traverse up from v to find the LCA\n  parent = v;\n  do {\n    parent = g.parent(parent);\n    vPath.push(parent);\n  } while (parent &&\n           (postorderNums[parent].low > low || lim > postorderNums[parent].lim));\n  lca = parent;\n\n  // Traverse from w to LCA\n  parent = w;\n  while ((parent = g.parent(parent)) !== lca) {\n    wPath.push(parent);\n  }\n\n  return { path: vPath.concat(wPath.reverse()), lca: lca };\n}\n\nfunction postorder(g) {\n  var result = {};\n  var lim = 0;\n\n  function dfs(v) {\n    var low = lim;\n    _.forEach(g.children(v), dfs);\n    result[v] = { low: low, lim: lim++ };\n  }\n  _.forEach(g.children(), dfs);\n\n  return result;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAE3BC,MAAM,CAACC,OAAO,GAAGC,iBAAiB;AAElC,SAASA,iBAAiBA,CAACC,CAAC,EAAE;EAC5B,IAAIC,aAAa,GAAGC,SAAS,CAACF,CAAC,CAAC;EAEhCL,CAAC,CAACQ,OAAO,CAACH,CAAC,CAACI,KAAK,CAAC,CAAC,CAACC,WAAW,EAAE,UAASC,CAAC,EAAE;IAC3C,IAAIC,IAAI,GAAGP,CAAC,CAACO,IAAI,CAACD,CAAC,CAAC;IACpB,IAAIE,OAAO,GAAGD,IAAI,CAACC,OAAO;IAC1B,IAAIC,QAAQ,GAAGC,QAAQ,CAACV,CAAC,EAAEC,aAAa,EAAEO,OAAO,CAACF,CAAC,EAAEE,OAAO,CAACG,CAAC,CAAC;IAC/D,IAAIC,IAAI,GAAGH,QAAQ,CAACG,IAAI;IACxB,IAAIC,GAAG,GAAGJ,QAAQ,CAACI,GAAG;IACtB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIC,KAAK,GAAGH,IAAI,CAACE,OAAO,CAAC;IACzB,IAAIE,SAAS,GAAG,IAAI;IAEpB,OAAOV,CAAC,KAAKE,OAAO,CAACG,CAAC,EAAE;MACtBJ,IAAI,GAAGP,CAAC,CAACO,IAAI,CAACD,CAAC,CAAC;MAEhB,IAAIU,SAAS,EAAE;QACb,OAAO,CAACD,KAAK,GAAGH,IAAI,CAACE,OAAO,CAAC,MAAMD,GAAG,IAC/Bb,CAAC,CAACO,IAAI,CAACQ,KAAK,CAAC,CAACE,OAAO,GAAGV,IAAI,CAACW,IAAI,EAAE;UACxCJ,OAAO,EAAE;QACX;QAEA,IAAIC,KAAK,KAAKF,GAAG,EAAE;UACjBG,SAAS,GAAG,KAAK;QACnB;MACF;MAEA,IAAI,CAACA,SAAS,EAAE;QACd,OAAOF,OAAO,GAAGF,IAAI,CAACO,MAAM,GAAG,CAAC,IACzBnB,CAAC,CAACO,IAAI,CAACQ,KAAK,GAAGH,IAAI,CAACE,OAAO,GAAG,CAAC,CAAC,CAAC,CAACM,OAAO,IAAIb,IAAI,CAACW,IAAI,EAAE;UAC7DJ,OAAO,EAAE;QACX;QACAC,KAAK,GAAGH,IAAI,CAACE,OAAO,CAAC;MACvB;MAEAd,CAAC,CAACqB,SAAS,CAACf,CAAC,EAAES,KAAK,CAAC;MACrBT,CAAC,GAAGN,CAAC,CAACsB,UAAU,CAAChB,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA,SAASI,QAAQA,CAACV,CAAC,EAAEC,aAAa,EAAEK,CAAC,EAAEK,CAAC,EAAE;EACxC,IAAIY,KAAK,GAAG,EAAE;EACd,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC1B,aAAa,CAACK,CAAC,CAAC,CAACmB,GAAG,EAAExB,aAAa,CAACU,CAAC,CAAC,CAACc,GAAG,CAAC;EAC9D,IAAIG,GAAG,GAAGF,IAAI,CAACG,GAAG,CAAC5B,aAAa,CAACK,CAAC,CAAC,CAACsB,GAAG,EAAE3B,aAAa,CAACU,CAAC,CAAC,CAACiB,GAAG,CAAC;EAC9D,IAAIE,MAAM;EACV,IAAIjB,GAAG;;EAEP;EACAiB,MAAM,GAAGxB,CAAC;EACV,GAAG;IACDwB,MAAM,GAAG9B,CAAC,CAAC8B,MAAM,CAACA,MAAM,CAAC;IACzBP,KAAK,CAACQ,IAAI,CAACD,MAAM,CAAC;EACpB,CAAC,QAAQA,MAAM,KACL7B,aAAa,CAAC6B,MAAM,CAAC,CAACL,GAAG,GAAGA,GAAG,IAAIG,GAAG,GAAG3B,aAAa,CAAC6B,MAAM,CAAC,CAACF,GAAG,CAAC;EAC7Ef,GAAG,GAAGiB,MAAM;;EAEZ;EACAA,MAAM,GAAGnB,CAAC;EACV,OAAO,CAACmB,MAAM,GAAG9B,CAAC,CAAC8B,MAAM,CAACA,MAAM,CAAC,MAAMjB,GAAG,EAAE;IAC1CW,KAAK,CAACO,IAAI,CAACD,MAAM,CAAC;EACpB;EAEA,OAAO;IAAElB,IAAI,EAAEW,KAAK,CAACS,MAAM,CAACR,KAAK,CAACS,OAAO,CAAC,CAAC,CAAC;IAAEpB,GAAG,EAAEA;EAAI,CAAC;AAC1D;AAEA,SAASX,SAASA,CAACF,CAAC,EAAE;EACpB,IAAIkC,MAAM,GAAG,CAAC,CAAC;EACf,IAAIN,GAAG,GAAG,CAAC;EAEX,SAASO,GAAGA,CAAC7B,CAAC,EAAE;IACd,IAAImB,GAAG,GAAGG,GAAG;IACbjC,CAAC,CAACQ,OAAO,CAACH,CAAC,CAACoC,QAAQ,CAAC9B,CAAC,CAAC,EAAE6B,GAAG,CAAC;IAC7BD,MAAM,CAAC5B,CAAC,CAAC,GAAG;MAAEmB,GAAG,EAAEA,GAAG;MAAEG,GAAG,EAAEA,GAAG;IAAG,CAAC;EACtC;EACAjC,CAAC,CAACQ,OAAO,CAACH,CAAC,CAACoC,QAAQ,CAAC,CAAC,EAAED,GAAG,CAAC;EAE5B,OAAOD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}