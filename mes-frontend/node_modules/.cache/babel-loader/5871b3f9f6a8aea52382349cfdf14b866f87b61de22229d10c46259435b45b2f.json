{"ast": null, "code": "import { __assign, __read, __rest } from \"tslib\";\nimport { transition } from '../../../animation';\nimport { normalize, parseSeriesAttr, percentTransform, renderExtDo, scale, select, splitStyle, subStyleProps } from '../../../util';\nimport { parsePosition } from '../../title';\nimport { CLASS_NAMES } from '../constant';\nfunction getTitlePosition(mainGroup, titleGroup, attr) {\n  var _a = attr.titlePosition,\n    position = _a === void 0 ? 'lb' : _a,\n    spacing = attr.titleSpacing;\n  var pos = parsePosition(position);\n  var _b = mainGroup.node().getLocalBounds(),\n    _c = __read(_b.min, 2),\n    mainX = _c[0],\n    mainY = _c[1],\n    _d = __read(_b.halfExtents, 2),\n    mainHalfWidth = _d[0],\n    mainHalfHeight = _d[1];\n  var _e = __read(titleGroup.node().getLocalBounds().halfExtents, 2),\n    titleHalfWidth = _e[0],\n    titleHalfHeight = _e[1];\n  var _f = __read([mainX + mainHalfWidth, mainY + mainHalfHeight], 2),\n    x = _f[0],\n    y = _f[1];\n  var _g = __read(parseSeriesAttr(spacing), 4),\n    spacingTop = _g[0],\n    spacingRight = _g[1],\n    spacingBottom = _g[2],\n    spacingLeft = _g[3];\n  if (['start', 'end'].includes(position) && attr.type === 'linear') {\n    var startPos = attr.startPos,\n      endPos = attr.endPos;\n    // todo did not consider the truncate case\n    var _h = __read(position === 'start' ? [startPos, endPos] : [endPos, startPos], 2),\n      from = _h[0],\n      to = _h[1];\n    var direction = normalize([-to[0] + from[0], -to[1] + from[1]]);\n    var _j = __read(scale(direction, spacingTop), 2),\n      dx = _j[0],\n      dy = _j[1];\n    return {\n      x: from[0] + dx,\n      y: from[1] + dy\n    };\n  }\n  if (pos.includes('t')) y -= mainHalfHeight + titleHalfHeight + spacingTop;\n  if (pos.includes('r')) x += mainHalfWidth + titleHalfWidth + spacingRight;\n  if (pos.includes('l')) x -= mainHalfWidth + titleHalfWidth + spacingLeft;\n  if (pos.includes('b')) y += mainHalfHeight + titleHalfHeight + spacingBottom;\n  return {\n    x: x,\n    y: y\n  };\n}\nfunction inferTransform(n, direction, position) {\n  var halfExtents = n.getGeometryBounds().halfExtents;\n  var height = halfExtents[1] * 2;\n  if (direction === 'vertical') {\n    if (position === 'left') return \"rotate(-90) translate(0, \".concat(height / 2, \")\");\n    if (position === 'right') return \"rotate(-90) translate(0, -\".concat(height / 2, \")\");\n  }\n  return '';\n}\nfunction applyTitleStyle(title, group, axis, attr, animate) {\n  var style = subStyleProps(attr, 'title');\n  var _a = __read(splitStyle(style), 2),\n    titleStyle = _a[0],\n    _b = _a[1],\n    specified = _b.transform,\n    transformOrigin = _b.transformOrigin,\n    groupStyle = __rest(_b, [\"transform\", \"transformOrigin\"]);\n  group.styles(groupStyle);\n  var transform = specified || inferTransform(title.node(), titleStyle.direction, titleStyle.position);\n  title.styles(__assign(__assign({}, titleStyle), {\n    transformOrigin: transformOrigin\n  }));\n  percentTransform(title.node(), transform);\n  var _c = getTitlePosition(\n    // @ts-ignore\n    select(axis._offscreen || axis.querySelector(CLASS_NAMES.mainGroup.class)), group, attr),\n    x = _c.x,\n    y = _c.y;\n  var animation = transition(group.node(), {\n    transform: \"translate(\".concat(x, \", \").concat(y, \")\")\n  }, animate);\n  return animation;\n}\nexport function renderTitle(container, axis, attr, animate) {\n  var titleText = attr.titleText;\n  return container.selectAll(CLASS_NAMES.title.class).data([{\n    title: titleText\n  }].filter(function (d) {\n    return !!d.title;\n  }), function (d, i) {\n    return d.title;\n  }).join(function (enter) {\n    return enter.append(function () {\n      return renderExtDo(titleText);\n    }).attr('className', CLASS_NAMES.title.name).transition(function () {\n      return applyTitleStyle(select(this), container, axis, attr, animate.enter);\n    });\n  }, function (update) {\n    return update.transition(function () {\n      return applyTitleStyle(select(this), container, axis, attr, animate.update);\n    });\n  }, function (exit) {\n    return exit.remove();\n  }).transitions();\n}", "map": {"version": 3, "names": ["transition", "normalize", "parseSeriesAttr", "percentTransform", "renderExtDo", "scale", "select", "splitStyle", "subStyleProps", "parsePosition", "CLASS_NAMES", "getTitlePosition", "mainGroup", "titleGroup", "attr", "_a", "titlePosition", "position", "spacing", "titleSpacing", "pos", "_b", "node", "getLocalBounds", "_c", "__read", "min", "mainX", "mainY", "_d", "halfExtents", "mainHalfWidth", "mainHalfHeight", "_e", "titleHalfWidth", "titleHalfHeight", "_f", "x", "y", "_g", "spacingTop", "spacingRight", "spacingBottom", "spacingLeft", "includes", "type", "startPos", "endPos", "_h", "from", "to", "direction", "_j", "dx", "dy", "inferTransform", "n", "getGeometryBounds", "height", "concat", "applyTitleStyle", "title", "group", "axis", "animate", "style", "titleStyle", "specified", "transform", "transform<PERSON><PERSON>in", "groupStyle", "__rest", "styles", "__assign", "_offscreen", "querySelector", "class", "animation", "renderTitle", "container", "titleText", "selectAll", "data", "filter", "d", "i", "join", "enter", "append", "name", "update", "exit", "remove", "transitions"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/guides/title.ts"], "sourcesContent": ["import type { GenericAnimation, StandardAnimationOption } from '../../../animation';\nimport { transition } from '../../../animation';\nimport type { DisplayObject } from '../../../shapes';\nimport type { Selection } from '../../../util';\nimport {\n  normalize,\n  parseSeriesAttr,\n  percentTransform,\n  renderExtDo,\n  scale,\n  select,\n  splitStyle,\n  subStyleProps,\n} from '../../../util';\nimport { parsePosition } from '../../title';\nimport { CLASS_NAMES } from '../constant';\nimport type { RequiredAxisStyleProps } from '../types';\n\nfunction getTitlePosition(\n  mainGroup: Selection,\n  titleGroup: Selection,\n  attr: RequiredAxisStyleProps\n): {\n  x: number;\n  y: number;\n} {\n  const { titlePosition: position = 'lb', titleSpacing: spacing } = attr;\n  const pos = parsePosition(position);\n\n  const {\n    min: [mainX, mainY],\n    halfExtents: [mainHalfWidth, mainHalfHeight],\n  } = mainGroup.node().getLocalBounds();\n\n  const {\n    halfExtents: [titleHalfWidth, titleHalfHeight],\n  } = titleGroup.node().getLocalBounds();\n\n  let [x, y] = [mainX + mainHalfWidth, mainY + mainHalfHeight];\n\n  const [spacingTop, spacingRight, spacingBottom, spacingLeft] = parseSeriesAttr(spacing);\n\n  if (['start', 'end'].includes(position) && attr.type === 'linear') {\n    const { startPos, endPos } = attr;\n    // todo did not consider the truncate case\n    const [from, to] = position === 'start' ? [startPos, endPos] : [endPos, startPos];\n    const direction = normalize([-to[0] + from[0], -to[1] + from[1]]);\n    const [dx, dy] = scale(direction, spacingTop);\n    return { x: from[0] + dx, y: from[1] + dy };\n  }\n\n  if (pos.includes('t')) y -= mainHalfHeight + titleHalfHeight + spacingTop;\n  if (pos.includes('r')) x += mainHalfWidth + titleHalfWidth + spacingRight;\n  if (pos.includes('l')) x -= mainHalfWidth + titleHalfWidth + spacingLeft;\n  if (pos.includes('b')) y += mainHalfHeight + titleHalfHeight + spacingBottom;\n\n  return { x, y };\n}\n\nfunction inferTransform(n: DisplayObject, direction: string, position: string): string {\n  const { halfExtents } = n.getGeometryBounds();\n  const height = halfExtents[1] * 2;\n\n  if (direction === 'vertical') {\n    if (position === 'left') return `rotate(-90) translate(0, ${height / 2})`;\n    if (position === 'right') return `rotate(-90) translate(0, -${height / 2})`;\n  }\n  return '';\n}\n\nfunction applyTitleStyle(\n  title: Selection,\n  group: Selection,\n  axis: DisplayObject,\n  attr: RequiredAxisStyleProps,\n  animate: GenericAnimation\n) {\n  const style = subStyleProps(attr, 'title');\n  const [titleStyle, { transform: specified, transformOrigin, ...groupStyle }] = splitStyle(style);\n\n  group.styles(groupStyle);\n\n  const transform = specified || inferTransform(title.node(), titleStyle.direction, titleStyle.position);\n  title.styles({ ...titleStyle, transformOrigin });\n  percentTransform(title.node(), transform);\n\n  const { x, y } = getTitlePosition(\n    // @ts-ignore\n    select(axis._offscreen || axis.querySelector(CLASS_NAMES.mainGroup.class)),\n    group,\n    attr\n  );\n\n  const animation = transition(group.node(), { transform: `translate(${x}, ${y})` }, animate);\n  return animation;\n}\n\nexport function renderTitle(\n  container: Selection,\n  axis: DisplayObject,\n  attr: RequiredAxisStyleProps,\n  animate: StandardAnimationOption\n) {\n  const { titleText } = attr;\n  return container\n    .selectAll(CLASS_NAMES.title.class)\n    .data(\n      [{ title: titleText }].filter((d) => !!d.title),\n      (d, i) => d.title\n    )\n    .join(\n      (enter) =>\n        enter\n          .append(() => renderExtDo(titleText))\n          .attr('className', CLASS_NAMES.title.name)\n          .transition(function () {\n            return applyTitleStyle(select(this), container, axis, attr, animate.enter);\n          }),\n      (update) =>\n        update.transition(function () {\n          return applyTitleStyle(select(this), container, axis, attr, animate.update);\n        }),\n      (exit) => exit.remove()\n    )\n    .transitions();\n}\n"], "mappings": ";AACA,SAASA,UAAU,QAAQ,oBAAoB;AAG/C,SACEC,SAAS,EACTC,eAAe,EACfC,gBAAgB,EAChBC,WAAW,EACXC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,aAAa,QACR,eAAe;AACtB,SAASC,aAAa,QAAQ,aAAa;AAC3C,SAASC,WAAW,QAAQ,aAAa;AAGzC,SAASC,gBAAgBA,CACvBC,SAAoB,EACpBC,UAAqB,EACrBC,IAA4B;EAKpB,IAAAC,EAAA,GAA0DD,IAAI,CAAAE,aAAhC;IAAfC,QAAQ,GAAAF,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAgBG,OAAO,GAAKJ,IAAI,CAAAK,YAAT;EAC7D,IAAMC,GAAG,GAAGX,aAAa,CAACQ,QAAQ,CAAC;EAE7B,IAAAI,EAAA,GAGFT,SAAS,CAACU,IAAI,EAAE,CAACC,cAAc,EAAE;IAFnCC,EAAA,GAAAC,MAAA,CAAAJ,EAAA,CAAAK,GAAA,IAAmB;IAAbC,KAAK,GAAAH,EAAA;IAAEI,KAAK,GAAAJ,EAAA;IAClBK,EAAA,GAAAJ,MAAA,CAAAJ,EAAA,CAAAS,WAAA,IAA4C;IAA9BC,aAAa,GAAAF,EAAA;IAAEG,cAAc,GAAAH,EAAA,GACR;EAGnC,IAAAI,EAAA,GAAAR,MAAA,CACEZ,UAAU,CAACS,IAAI,EAAE,CAACC,cAAc,EAAE,CAAAO,WAAA,IADU;IAAhCI,cAAc,GAAAD,EAAA;IAAEE,eAAe,GAAAF,EAAA,GAAC;EAG5C,IAAAG,EAAA,GAAAX,MAAA,CAAS,CAACE,KAAK,GAAGI,aAAa,EAAEH,KAAK,GAAGI,cAAc,CAAC;IAAvDK,CAAC,GAAAD,EAAA;IAAEE,CAAC,GAAAF,EAAA,GAAmD;EAEtD,IAAAG,EAAA,GAAAd,MAAA,CAAyDvB,eAAe,CAACgB,OAAO,CAAC;IAAhFsB,UAAU,GAAAD,EAAA;IAAEE,YAAY,GAAAF,EAAA;IAAEG,aAAa,GAAAH,EAAA;IAAEI,WAAW,GAAAJ,EAAA,GAA4B;EAEvF,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAACK,QAAQ,CAAC3B,QAAQ,CAAC,IAAIH,IAAI,CAAC+B,IAAI,KAAK,QAAQ,EAAE;IACzD,IAAAC,QAAQ,GAAahC,IAAI,CAAAgC,QAAjB;MAAEC,MAAM,GAAKjC,IAAI,CAAAiC,MAAT;IACxB;IACM,IAAAC,EAAA,GAAAvB,MAAA,CAAaR,QAAQ,KAAK,OAAO,GAAG,CAAC6B,QAAQ,EAAEC,MAAM,CAAC,GAAG,CAACA,MAAM,EAAED,QAAQ,CAAC;MAA1EG,IAAI,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAAkE;IACjF,IAAMG,SAAS,GAAGlD,SAAS,CAAC,CAAC,CAACiD,EAAE,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,EAAE,CAACC,EAAE,CAAC,CAAC,CAAC,GAAGD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D,IAAAG,EAAA,GAAA3B,MAAA,CAAWpB,KAAK,CAAC8C,SAAS,EAAEX,UAAU,CAAC;MAAtCa,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAAgC;IAC7C,OAAO;MAAEf,CAAC,EAAEY,IAAI,CAAC,CAAC,CAAC,GAAGI,EAAE;MAAEf,CAAC,EAAEW,IAAI,CAAC,CAAC,CAAC,GAAGK;IAAE,CAAE;EAC7C;EAEA,IAAIlC,GAAG,CAACwB,QAAQ,CAAC,GAAG,CAAC,EAAEN,CAAC,IAAIN,cAAc,GAAGG,eAAe,GAAGK,UAAU;EACzE,IAAIpB,GAAG,CAACwB,QAAQ,CAAC,GAAG,CAAC,EAAEP,CAAC,IAAIN,aAAa,GAAGG,cAAc,GAAGO,YAAY;EACzE,IAAIrB,GAAG,CAACwB,QAAQ,CAAC,GAAG,CAAC,EAAEP,CAAC,IAAIN,aAAa,GAAGG,cAAc,GAAGS,WAAW;EACxE,IAAIvB,GAAG,CAACwB,QAAQ,CAAC,GAAG,CAAC,EAAEN,CAAC,IAAIN,cAAc,GAAGG,eAAe,GAAGO,aAAa;EAE5E,OAAO;IAAEL,CAAC,EAAAA,CAAA;IAAEC,CAAC,EAAAA;EAAA,CAAE;AACjB;AAEA,SAASiB,cAAcA,CAACC,CAAgB,EAAEL,SAAiB,EAAElC,QAAgB;EACnE,IAAAa,WAAW,GAAK0B,CAAC,CAACC,iBAAiB,EAAE,CAAA3B,WAA1B;EACnB,IAAM4B,MAAM,GAAG5B,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;EAEjC,IAAIqB,SAAS,KAAK,UAAU,EAAE;IAC5B,IAAIlC,QAAQ,KAAK,MAAM,EAAE,OAAO,4BAAA0C,MAAA,CAA4BD,MAAM,GAAG,CAAC,MAAG;IACzE,IAAIzC,QAAQ,KAAK,OAAO,EAAE,OAAO,6BAAA0C,MAAA,CAA6BD,MAAM,GAAG,CAAC,MAAG;EAC7E;EACA,OAAO,EAAE;AACX;AAEA,SAASE,eAAeA,CACtBC,KAAgB,EAChBC,KAAgB,EAChBC,IAAmB,EACnBjD,IAA4B,EAC5BkD,OAAyB;EAEzB,IAAMC,KAAK,GAAGzD,aAAa,CAACM,IAAI,EAAE,OAAO,CAAC;EACpC,IAAAC,EAAA,GAAAU,MAAA,CAAyElB,UAAU,CAAC0D,KAAK,CAAC;IAAzFC,UAAU,GAAAnD,EAAA;IAAAM,EAAA,GAAAN,EAAA;IAAeoD,SAAS,GAAA9C,EAAA,CAAA+C,SAAA;IAAEC,eAAe,GAAAhD,EAAA,CAAAgD,eAAA;IAAKC,UAAU,GAAAC,MAAA,CAAAlD,EAAA,EAAtD,gCAAwD,CAAqB;EAEhGyC,KAAK,CAACU,MAAM,CAACF,UAAU,CAAC;EAExB,IAAMF,SAAS,GAAGD,SAAS,IAAIZ,cAAc,CAACM,KAAK,CAACvC,IAAI,EAAE,EAAE4C,UAAU,CAACf,SAAS,EAAEe,UAAU,CAACjD,QAAQ,CAAC;EACtG4C,KAAK,CAACW,MAAM,CAAAC,QAAA,CAAAA,QAAA,KAAMP,UAAU;IAAEG,eAAe,EAAAA;EAAA,GAAG;EAChDlE,gBAAgB,CAAC0D,KAAK,CAACvC,IAAI,EAAE,EAAE8C,SAAS,CAAC;EAEnC,IAAA5C,EAAA,GAAWb,gBAAgB;IAC/B;IACAL,MAAM,CAACyD,IAAI,CAACW,UAAU,IAAIX,IAAI,CAACY,aAAa,CAACjE,WAAW,CAACE,SAAS,CAACgE,KAAK,CAAC,CAAC,EAC1Ed,KAAK,EACLhD,IAAI,CACL;IALOuB,CAAC,GAAAb,EAAA,CAAAa,CAAA;IAAEC,CAAC,GAAAd,EAAA,CAAAc,CAKX;EAED,IAAMuC,SAAS,GAAG7E,UAAU,CAAC8D,KAAK,CAACxC,IAAI,EAAE,EAAE;IAAE8C,SAAS,EAAE,aAAAT,MAAA,CAAatB,CAAC,QAAAsB,MAAA,CAAKrB,CAAC;EAAG,CAAE,EAAE0B,OAAO,CAAC;EAC3F,OAAOa,SAAS;AAClB;AAEA,OAAM,SAAUC,WAAWA,CACzBC,SAAoB,EACpBhB,IAAmB,EACnBjD,IAA4B,EAC5BkD,OAAgC;EAExB,IAAAgB,SAAS,GAAKlE,IAAI,CAAAkE,SAAT;EACjB,OAAOD,SAAS,CACbE,SAAS,CAACvE,WAAW,CAACmD,KAAK,CAACe,KAAK,CAAC,CAClCM,IAAI,CACH,CAAC;IAAErB,KAAK,EAAEmB;EAAS,CAAE,CAAC,CAACG,MAAM,CAAC,UAACC,CAAC;IAAK,QAAC,CAACA,CAAC,CAACvB,KAAK;EAAT,CAAS,CAAC,EAC/C,UAACuB,CAAC,EAAEC,CAAC;IAAK,OAAAD,CAAC,CAACvB,KAAK;EAAP,CAAO,CAClB,CACAyB,IAAI,CACH,UAACC,KAAK;IACJ,OAAAA,KAAK,CACFC,MAAM,CAAC;MAAM,OAAApF,WAAW,CAAC4E,SAAS,CAAC;IAAtB,CAAsB,CAAC,CACpClE,IAAI,CAAC,WAAW,EAAEJ,WAAW,CAACmD,KAAK,CAAC4B,IAAI,CAAC,CACzCzF,UAAU,CAAC;MACV,OAAO4D,eAAe,CAACtD,MAAM,CAAC,IAAI,CAAC,EAAEyE,SAAS,EAAEhB,IAAI,EAAEjD,IAAI,EAAEkD,OAAO,CAACuB,KAAK,CAAC;IAC5E,CAAC,CAAC;EALJ,CAKI,EACN,UAACG,MAAM;IACL,OAAAA,MAAM,CAAC1F,UAAU,CAAC;MAChB,OAAO4D,eAAe,CAACtD,MAAM,CAAC,IAAI,CAAC,EAAEyE,SAAS,EAAEhB,IAAI,EAAEjD,IAAI,EAAEkD,OAAO,CAAC0B,MAAM,CAAC;IAC7E,CAAC,CAAC;EAFF,CAEE,EACJ,UAACC,IAAI;IAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;EAAb,CAAa,CACxB,CACAC,WAAW,EAAE;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}