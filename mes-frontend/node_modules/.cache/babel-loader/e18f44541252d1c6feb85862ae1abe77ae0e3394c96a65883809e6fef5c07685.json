{"ast": null, "code": "/**\n * Validates an A (arc-to) specific path command value.\n * Usually a `large-arc-flag` or `sweep-flag`.\n */\nexport function scanFlag(path) {\n  var index = path.index,\n    pathValue = path.pathValue;\n  var code = pathValue.charCodeAt(index);\n  if (code === 0x30 /* 0 */) {\n    path.param = 0;\n    path.index += 1;\n    return;\n  }\n  if (code === 0x31 /* 1 */) {\n    path.param = 1;\n    path.index += 1;\n    return;\n  }\n  path.err = \"[path-util]: invalid Arc flag \\\"\".concat(pathValue[index], \"\\\", expecting 0 or 1 at index \").concat(index);\n}", "map": {"version": 3, "names": ["scanFlag", "path", "index", "pathValue", "code", "charCodeAt", "param", "err", "concat"], "sources": ["path/parser/scan-flag.ts"], "sourcesContent": [null], "mappings": "AAEA;;;;AAIA,OAAM,SAAUA,QAAQA,CAACC,IAAgB;EAC/B,IAAAC,KAAK,GAAgBD,IAAI,CAAAC,KAApB;IAAEC,SAAS,GAAKF,IAAI,CAAAE,SAAT;EACxB,IAAMC,IAAI,GAAGD,SAAS,CAACE,UAAU,CAACH,KAAK,CAAC;EAExC,IAAIE,IAAI,KAAK,IAAI,CAAC,SAAS;IACzBH,IAAI,CAACK,KAAK,GAAG,CAAC;IACdL,IAAI,CAACC,KAAK,IAAI,CAAC;IACf;EACF;EAEA,IAAIE,IAAI,KAAK,IAAI,CAAC,SAAS;IACzBH,IAAI,CAACK,KAAK,GAAG,CAAC;IACdL,IAAI,CAACC,KAAK,IAAI,CAAC;IACf;EACF;EAEAD,IAAI,CAACM,GAAG,GAAG,mCAAAC,MAAA,CAAkCL,SAAS,CAACD,KAAK,CAAC,oCAAAM,MAAA,CAAgCN,KAAK,CAAE;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}