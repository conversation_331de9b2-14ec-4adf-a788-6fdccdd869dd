{"ast": null, "code": "import { __read } from \"tslib\";\nimport { degToRad, parseSeriesAttr, textOf } from '../../../util';\nvar Bounds = /** @class */function () {\n  function Bounds(x1, y1, x2, y2) {\n    this.set(x1, y1, x2, y2);\n  }\n  Object.defineProperty(Bounds.prototype, \"left\", {\n    get: function () {\n      return this.x1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Bounds.prototype, \"top\", {\n    get: function () {\n      return this.y1;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Bounds.prototype, \"right\", {\n    get: function () {\n      return this.x2;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Bounds.prototype, \"bottom\", {\n    get: function () {\n      return this.y2;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Bounds.prototype, \"width\", {\n    get: function () {\n      return this.defined('x2') && this.defined('x1') ? this.x2 - this.x1 : undefined;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(Bounds.prototype, \"height\", {\n    get: function () {\n      return this.defined('y2') && this.defined('y1') ? this.y2 - this.y1 : undefined;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Bounds.prototype.rotatedPoints = function (radian, x, y) {\n    var _a = this,\n      x1 = _a.x1,\n      y1 = _a.y1,\n      x2 = _a.x2,\n      y2 = _a.y2;\n    var cos = Math.cos(radian);\n    var sin = Math.sin(radian);\n    var cx = x - x * cos + y * sin;\n    var cy = y - x * sin - y * cos;\n    var points = [[cos * x1 - sin * y2 + cx, sin * x1 + cos * y2 + cy], [cos * x2 - sin * y2 + cx, sin * x2 + cos * y2 + cy], [cos * x1 - sin * y1 + cx, sin * x1 + cos * y1 + cy], [cos * x2 - sin * y1 + cx, sin * x2 + cos * y1 + cy]];\n    return points;\n  };\n  Bounds.prototype.set = function (x1, y1, x2, y2) {\n    if (x2 < x1) {\n      this.x2 = x1;\n      this.x1 = x2;\n    } else {\n      this.x1 = x1;\n      this.x2 = x2;\n    }\n    if (y2 < y1) {\n      this.y2 = y1;\n      this.y1 = y2;\n    } else {\n      this.y1 = y1;\n      this.y2 = y2;\n    }\n    return this;\n  };\n  Bounds.prototype.defined = function (key) {\n    return this[key] !== Number.MAX_VALUE && this[key] !== -Number.MAX_VALUE;\n  };\n  return Bounds;\n}();\nexport { Bounds };\n/**\n * Can't use getBounds directly since we should not use AABB here.\n */\nexport function getBounds(item, margin) {\n  var angle = item.getEulerAngles() || 0;\n  item.setEulerAngles(0);\n  // get dimensions\n  var _a = item.getBounds(),\n    _b = __read(_a.min, 2),\n    x = _b[0],\n    y = _b[1],\n    _c = __read(_a.max, 2),\n    right = _c[0],\n    bottom = _c[1];\n  var _d = item.getBBox(),\n    w = _d.width,\n    h = _d.height;\n  var height = h;\n  var dx = 0;\n  var dy = 0;\n  var anchorX = x;\n  var anchorY = y;\n  var text = textOf(item);\n  if (text) {\n    // [to fix] 目前 G 上计算 bbox 有一点误差\n    height -= 1.5;\n    var a = text.style.textAlign;\n    var b_1 = text.style.textBaseline;\n    // horizontal alignment\n    if (a === 'center') {\n      anchorX = (x + right) / 2;\n    } else if (a === 'right' || a === 'end') {\n      anchorX = right;\n    } else {\n      // left by default, do nothing\n    }\n    // vertical alignment\n    if (b_1 === 'middle') {\n      anchorY = (y + bottom) / 2;\n    } else if (b_1 === 'bottom') {\n      anchorY = bottom;\n    }\n  }\n  var _e = __read(parseSeriesAttr(margin), 4),\n    _f = _e[0],\n    t = _f === void 0 ? 0 : _f,\n    _g = _e[1],\n    r = _g === void 0 ? 0 : _g,\n    _h = _e[2],\n    b = _h === void 0 ? t : _h,\n    _j = _e[3],\n    l = _j === void 0 ? r : _j;\n  var bounds = new Bounds((dx += x) - l, (dy += y) - t, dx + w + r, dy + height + b);\n  item.setEulerAngles(angle);\n  return bounds.rotatedPoints(degToRad(angle), anchorX, anchorY);\n}", "map": {"version": 3, "names": ["degToRad", "parseSeriesAttr", "textOf", "Bounds", "x1", "y1", "x2", "y2", "set", "Object", "defineProperty", "prototype", "get", "defined", "undefined", "rotatedPoints", "radian", "x", "y", "_a", "cos", "Math", "sin", "cx", "cy", "points", "key", "Number", "MAX_VALUE", "getBounds", "item", "margin", "angle", "getEulerAngles", "setEulerAngles", "_b", "__read", "min", "_c", "max", "right", "bottom", "_d", "getBBox", "w", "width", "h", "height", "dx", "dy", "anchorX", "anchorY", "text", "a", "style", "textAlign", "b_1", "textBaseline", "_e", "_f", "t", "_g", "r", "_h", "b", "_j", "l", "bounds"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/utils/bounds.ts"], "sourcesContent": ["import type { DisplayObject } from '../../../shapes';\nimport type { Point } from '../../../types';\nimport type { SeriesAttr } from '../../../util';\nimport { degToRad, parseSeriesAttr, textOf } from '../../../util';\n\nexport class Bounds {\n  public x1!: number;\n\n  public y1!: number;\n\n  public x2!: number;\n\n  public y2!: number;\n\n  constructor(x1: number, y1: number, x2: number, y2: number) {\n    this.set(x1, y1, x2, y2);\n  }\n\n  public get left() {\n    return this.x1;\n  }\n\n  public get top() {\n    return this.y1;\n  }\n\n  public get right() {\n    return this.x2;\n  }\n\n  public get bottom() {\n    return this.y2;\n  }\n\n  public get width() {\n    return this.defined('x2') && this.defined('x1') ? this.x2 - this.x1 : undefined;\n  }\n\n  public get height() {\n    return this.defined('y2') && this.defined('y1') ? this.y2 - this.y1 : undefined;\n  }\n\n  rotatedPoints(radian: number, x: number, y: number) {\n    const { x1, y1, x2, y2 } = this;\n    const cos = Math.cos(radian);\n    const sin = Math.sin(radian);\n    const cx = x - x * cos + y * sin;\n    const cy = y - x * sin - y * cos;\n    const points: Point[] = [\n      [cos * x1 - sin * y2 + cx, sin * x1 + cos * y2 + cy],\n      [cos * x2 - sin * y2 + cx, sin * x2 + cos * y2 + cy],\n      [cos * x1 - sin * y1 + cx, sin * x1 + cos * y1 + cy],\n      [cos * x2 - sin * y1 + cx, sin * x2 + cos * y1 + cy],\n    ];\n    return points;\n  }\n\n  set(x1: number, y1: number, x2: number, y2: number) {\n    if (x2 < x1) {\n      this.x2 = x1;\n      this.x1 = x2;\n    } else {\n      this.x1 = x1;\n      this.x2 = x2;\n    }\n    if (y2 < y1) {\n      this.y2 = y1;\n      this.y1 = y2;\n    } else {\n      this.y1 = y1;\n      this.y2 = y2;\n    }\n    return this;\n  }\n\n  defined(key: 'left' | 'right' | 'top' | 'bottom' | 'x1' | 'x2' | 'y1' | 'y2') {\n    return this[key] !== Number.MAX_VALUE && this[key] !== -Number.MAX_VALUE;\n  }\n}\n\n/**\n * Can't use getBounds directly since we should not use AABB here.\n */\nexport function getBounds(item: DisplayObject<any>, margin?: SeriesAttr) {\n  const angle = item.getEulerAngles() || 0;\n  item.setEulerAngles(0);\n  // get dimensions\n  const {\n    min: [x, y],\n    max: [right, bottom],\n  } = item.getBounds();\n  const { width: w, height: h } = item.getBBox();\n\n  let height = h;\n  let dx = 0;\n  let dy = 0;\n  let anchorX = x;\n  let anchorY = y;\n\n  const text = textOf(item);\n\n  if (text) {\n    // [to fix] 目前 G 上计算 bbox 有一点误差\n    height -= 1.5;\n    const a = text.style.textAlign;\n    const b = text.style.textBaseline;\n\n    // horizontal alignment\n    if (a === 'center') {\n      anchorX = (x + right) / 2;\n    } else if (a === 'right' || a === 'end') {\n      anchorX = right;\n    } else {\n      // left by default, do nothing\n    }\n\n    // vertical alignment\n    if (b === 'middle') {\n      anchorY = (y + bottom) / 2;\n    } else if (b === 'bottom') {\n      anchorY = bottom;\n    }\n  }\n\n  const [t = 0, r = 0, b = t, l = r] = parseSeriesAttr(margin);\n  const bounds = new Bounds((dx += x) - l, (dy += y) - t, dx + w + r, dy + height + b);\n  item.setEulerAngles(angle);\n\n  return bounds.rotatedPoints(degToRad(angle), anchorX, anchorY);\n}\n"], "mappings": ";AAGA,SAASA,QAAQ,EAAEC,eAAe,EAAEC,MAAM,QAAQ,eAAe;AAEjE,IAAAC,MAAA;EASE,SAAAA,OAAYC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;IACxD,IAAI,CAACC,GAAG,CAACJ,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC1B;EAEAE,MAAA,CAAAC,cAAA,CAAWP,MAAA,CAAAQ,SAAA,QAAI;SAAf,SAAAC,CAAA;MACE,OAAO,IAAI,CAACR,EAAE;IAChB,CAAC;;;;EAEDK,MAAA,CAAAC,cAAA,CAAWP,MAAA,CAAAQ,SAAA,OAAG;SAAd,SAAAC,CAAA;MACE,OAAO,IAAI,CAACP,EAAE;IAChB,CAAC;;;;EAEDI,MAAA,CAAAC,cAAA,CAAWP,MAAA,CAAAQ,SAAA,SAAK;SAAhB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACN,EAAE;IAChB,CAAC;;;;EAEDG,MAAA,CAAAC,cAAA,CAAWP,MAAA,CAAAQ,SAAA,UAAM;SAAjB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACL,EAAE;IAChB,CAAC;;;;EAEDE,MAAA,CAAAC,cAAA,CAAWP,MAAA,CAAAQ,SAAA,SAAK;SAAhB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAACP,EAAE,GAAG,IAAI,CAACF,EAAE,GAAGU,SAAS;IACjF,CAAC;;;;EAEDL,MAAA,CAAAC,cAAA,CAAWP,MAAA,CAAAQ,SAAA,UAAM;SAAjB,SAAAC,CAAA;MACE,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAACA,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAACN,EAAE,GAAG,IAAI,CAACF,EAAE,GAAGS,SAAS;IACjF,CAAC;;;;EAEDX,MAAA,CAAAQ,SAAA,CAAAI,aAAa,GAAb,UAAcC,MAAc,EAAEC,CAAS,EAAEC,CAAS;IAC1C,IAAAC,EAAA,GAAqB,IAAI;MAAvBf,EAAE,GAAAe,EAAA,CAAAf,EAAA;MAAEC,EAAE,GAAAc,EAAA,CAAAd,EAAA;MAAEC,EAAE,GAAAa,EAAA,CAAAb,EAAA;MAAEC,EAAE,GAAAY,EAAA,CAAAZ,EAAS;IAC/B,IAAMa,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACJ,MAAM,CAAC;IAC5B,IAAMM,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACN,MAAM,CAAC;IAC5B,IAAMO,EAAE,GAAGN,CAAC,GAAGA,CAAC,GAAGG,GAAG,GAAGF,CAAC,GAAGI,GAAG;IAChC,IAAME,EAAE,GAAGN,CAAC,GAAGD,CAAC,GAAGK,GAAG,GAAGJ,CAAC,GAAGE,GAAG;IAChC,IAAMK,MAAM,GAAY,CACtB,CAACL,GAAG,GAAGhB,EAAE,GAAGkB,GAAG,GAAGf,EAAE,GAAGgB,EAAE,EAAED,GAAG,GAAGlB,EAAE,GAAGgB,GAAG,GAAGb,EAAE,GAAGiB,EAAE,CAAC,EACpD,CAACJ,GAAG,GAAGd,EAAE,GAAGgB,GAAG,GAAGf,EAAE,GAAGgB,EAAE,EAAED,GAAG,GAAGhB,EAAE,GAAGc,GAAG,GAAGb,EAAE,GAAGiB,EAAE,CAAC,EACpD,CAACJ,GAAG,GAAGhB,EAAE,GAAGkB,GAAG,GAAGjB,EAAE,GAAGkB,EAAE,EAAED,GAAG,GAAGlB,EAAE,GAAGgB,GAAG,GAAGf,EAAE,GAAGmB,EAAE,CAAC,EACpD,CAACJ,GAAG,GAAGd,EAAE,GAAGgB,GAAG,GAAGjB,EAAE,GAAGkB,EAAE,EAAED,GAAG,GAAGhB,EAAE,GAAGc,GAAG,GAAGf,EAAE,GAAGmB,EAAE,CAAC,CACrD;IACD,OAAOC,MAAM;EACf,CAAC;EAEDtB,MAAA,CAAAQ,SAAA,CAAAH,GAAG,GAAH,UAAIJ,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;IAChD,IAAID,EAAE,GAAGF,EAAE,EAAE;MACX,IAAI,CAACE,EAAE,GAAGF,EAAE;MACZ,IAAI,CAACA,EAAE,GAAGE,EAAE;IACd,CAAC,MAAM;MACL,IAAI,CAACF,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACE,EAAE,GAAGA,EAAE;IACd;IACA,IAAIC,EAAE,GAAGF,EAAE,EAAE;MACX,IAAI,CAACE,EAAE,GAAGF,EAAE;MACZ,IAAI,CAACA,EAAE,GAAGE,EAAE;IACd,CAAC,MAAM;MACL,IAAI,CAACF,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACE,EAAE,GAAGA,EAAE;IACd;IACA,OAAO,IAAI;EACb,CAAC;EAEDJ,MAAA,CAAAQ,SAAA,CAAAE,OAAO,GAAP,UAAQa,GAAoE;IAC1E,OAAO,IAAI,CAACA,GAAG,CAAC,KAAKC,MAAM,CAACC,SAAS,IAAI,IAAI,CAACF,GAAG,CAAC,KAAK,CAACC,MAAM,CAACC,SAAS;EAC1E,CAAC;EACH,OAAAzB,MAAC;AAAD,CAAC,CAzED;;AA2EA;;;AAGA,OAAM,SAAU0B,SAASA,CAACC,IAAwB,EAAEC,MAAmB;EACrE,IAAMC,KAAK,GAAGF,IAAI,CAACG,cAAc,EAAE,IAAI,CAAC;EACxCH,IAAI,CAACI,cAAc,CAAC,CAAC,CAAC;EACtB;EACM,IAAAf,EAAA,GAGFW,IAAI,CAACD,SAAS,EAAE;IAFlBM,EAAA,GAAAC,MAAA,CAAAjB,EAAA,CAAAkB,GAAA,IAAW;IAALpB,CAAC,GAAAkB,EAAA;IAAEjB,CAAC,GAAAiB,EAAA;IACVG,EAAA,GAAAF,MAAA,CAAAjB,EAAA,CAAAoB,GAAA,IAAoB;IAAdC,KAAK,GAAAF,EAAA;IAAEG,MAAM,GAAAH,EAAA,GACD;EACd,IAAAI,EAAA,GAA0BZ,IAAI,CAACa,OAAO,EAAE;IAA/BC,CAAC,GAAAF,EAAA,CAAAG,KAAA;IAAUC,CAAC,GAAAJ,EAAA,CAAAK,MAAmB;EAE9C,IAAIA,MAAM,GAAGD,CAAC;EACd,IAAIE,EAAE,GAAG,CAAC;EACV,IAAIC,EAAE,GAAG,CAAC;EACV,IAAIC,OAAO,GAAGjC,CAAC;EACf,IAAIkC,OAAO,GAAGjC,CAAC;EAEf,IAAMkC,IAAI,GAAGlD,MAAM,CAAC4B,IAAI,CAAC;EAEzB,IAAIsB,IAAI,EAAE;IACR;IACAL,MAAM,IAAI,GAAG;IACb,IAAMM,CAAC,GAAGD,IAAI,CAACE,KAAK,CAACC,SAAS;IAC9B,IAAMC,GAAC,GAAGJ,IAAI,CAACE,KAAK,CAACG,YAAY;IAEjC;IACA,IAAIJ,CAAC,KAAK,QAAQ,EAAE;MAClBH,OAAO,GAAG,CAACjC,CAAC,GAAGuB,KAAK,IAAI,CAAC;IAC3B,CAAC,MAAM,IAAIa,CAAC,KAAK,OAAO,IAAIA,CAAC,KAAK,KAAK,EAAE;MACvCH,OAAO,GAAGV,KAAK;IACjB,CAAC,MAAM;MACL;IAAA;IAGF;IACA,IAAIgB,GAAC,KAAK,QAAQ,EAAE;MAClBL,OAAO,GAAG,CAACjC,CAAC,GAAGuB,MAAM,IAAI,CAAC;IAC5B,CAAC,MAAM,IAAIe,GAAC,KAAK,QAAQ,EAAE;MACzBL,OAAO,GAAGV,MAAM;IAClB;EACF;EAEM,IAAAiB,EAAA,GAAAtB,MAAA,CAA+BnC,eAAe,CAAC8B,MAAM,CAAC;IAArD4B,EAAA,GAAAD,EAAA,GAAK;IAALE,CAAC,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;IAAEE,EAAA,GAAAH,EAAA,GAAK;IAALI,CAAC,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;IAAEE,EAAA,GAAAL,EAAA,GAAK;IAALM,CAAC,GAAAD,EAAA,cAAGH,CAAC,GAAAG,EAAA;IAAEE,EAAA,GAAAP,EAAA,GAAK;IAALQ,CAAC,GAAAD,EAAA,cAAGH,CAAC,GAAAG,EAA2B;EAC5D,IAAME,MAAM,GAAG,IAAIhE,MAAM,CAAC,CAAC6C,EAAE,IAAI/B,CAAC,IAAIiD,CAAC,EAAE,CAACjB,EAAE,IAAI/B,CAAC,IAAI0C,CAAC,EAAEZ,EAAE,GAAGJ,CAAC,GAAGkB,CAAC,EAAEb,EAAE,GAAGF,MAAM,GAAGiB,CAAC,CAAC;EACpFlC,IAAI,CAACI,cAAc,CAACF,KAAK,CAAC;EAE1B,OAAOmC,MAAM,CAACpD,aAAa,CAACf,QAAQ,CAACgC,KAAK,CAAC,EAAEkB,OAAO,EAAEC,OAAO,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}