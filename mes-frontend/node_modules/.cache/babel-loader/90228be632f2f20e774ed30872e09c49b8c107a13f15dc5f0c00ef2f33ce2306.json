{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { getRadius, isCircular } from '../../../utils/coordinate';\nimport { angleWithQuadrant } from '../../../utils/vector';\nimport { pointOfArc } from './default';\nimport { inferOutsideCircularStyle } from './outside';\n/**\n * Surround label transform is used to make labels surround circular.\n */\nexport function surround(position, points, value, coordinate) {\n  if (!isCircular(coordinate)) return {};\n  const {\n    connectorLength,\n    connectorLength2,\n    connectorDistance\n  } = value;\n  const style = __rest(inferOutsideCircularStyle('outside', points, value, coordinate), []);\n  const {\n    x0,\n    y0\n  } = style;\n  const center = coordinate.getCenter();\n  const radius = getRadius(coordinate);\n  const radius1 = radius + connectorLength;\n  const angle = angleWithQuadrant([x0 - center[0], y0 - center[1]]);\n  const sign = Math.sin(angle) > 0 ? 1 : -1;\n  const [newX, newY] = pointOfArc(center, angle, radius1);\n  style.x = newX + (connectorLength2 + connectorDistance) * sign;\n  style.y = newY;\n  return style;\n}", "map": {"version": 3, "names": ["getRadius", "isCircular", "angleWithQuadrant", "pointOfArc", "inferOutsideCircularStyle", "surround", "position", "points", "value", "coordinate", "connectorLength", "connectorLength2", "connectorDistance", "style", "__rest", "x0", "y0", "center", "getCenter", "radius", "radius1", "angle", "sign", "Math", "sin", "newX", "newY", "x", "y"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/surround.ts"], "sourcesContent": ["import { Coordinate } from '@antv/coord';\nimport { Vector2 } from '../../../runtime';\nimport { getRadius, isCircular } from '../../../utils/coordinate';\nimport { angleWithQuadrant } from '../../../utils/vector';\nimport { LabelPosition, pointOfArc } from './default';\nimport { inferOutsideCircularStyle } from './outside';\n\n/**\n * Surround label transform is used to make labels surround circular.\n */\nexport function surround(\n  position: LabelPosition,\n  points: Vector2[],\n  value: Record<string, any>,\n  coordinate: Coordinate,\n) {\n  if (!isCircular(coordinate)) return {};\n  const { connectorLength, connectorLength2, connectorDistance } = value;\n\n  const { ...style }: any = inferOutsideCircularStyle(\n    'outside',\n    points,\n    value,\n    coordinate,\n  );\n  const { x0, y0 } = style;\n\n  const center = coordinate.getCenter();\n  const radius = getRadius(coordinate);\n  const radius1 = radius + connectorLength;\n  const angle = angleWithQuadrant([x0 - center[0], y0 - center[1]]);\n  const sign = Math.sin(angle) > 0 ? 1 : -1;\n\n  const [newX, newY] = pointOfArc(center, angle, radius1);\n\n  style.x = newX + (connectorLength2 + connectorDistance) * sign;\n  style.y = newY;\n\n  return style;\n}\n"], "mappings": ";;;;;;;;AAEA,SAASA,SAAS,EAAEC,UAAU,QAAQ,2BAA2B;AACjE,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAAwBC,UAAU,QAAQ,WAAW;AACrD,SAASC,yBAAyB,QAAQ,WAAW;AAErD;;;AAGA,OAAM,SAAUC,QAAQA,CACtBC,QAAuB,EACvBC,MAAiB,EACjBC,KAA0B,EAC1BC,UAAsB;EAEtB,IAAI,CAACR,UAAU,CAACQ,UAAU,CAAC,EAAE,OAAO,EAAE;EACtC,MAAM;IAAEC,eAAe;IAAEC,gBAAgB;IAAEC;EAAiB,CAAE,GAAGJ,KAAK;EAEtE,MAAWK,KAAK,GAAAC,MAAA,CAAUV,yBAAyB,CACjD,SAAS,EACTG,MAAM,EACNC,KAAK,EACLC,UAAU,CACX,EALK,EAAY,CAKjB;EACD,MAAM;IAAEM,EAAE;IAAEC;EAAE,CAAE,GAAGH,KAAK;EAExB,MAAMI,MAAM,GAAGR,UAAU,CAACS,SAAS,EAAE;EACrC,MAAMC,MAAM,GAAGnB,SAAS,CAACS,UAAU,CAAC;EACpC,MAAMW,OAAO,GAAGD,MAAM,GAAGT,eAAe;EACxC,MAAMW,KAAK,GAAGnB,iBAAiB,CAAC,CAACa,EAAE,GAAGE,MAAM,CAAC,CAAC,CAAC,EAAED,EAAE,GAAGC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACjE,MAAMK,IAAI,GAAGC,IAAI,CAACC,GAAG,CAACH,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;EAEzC,MAAM,CAACI,IAAI,EAAEC,IAAI,CAAC,GAAGvB,UAAU,CAACc,MAAM,EAAEI,KAAK,EAAED,OAAO,CAAC;EAEvDP,KAAK,CAACc,CAAC,GAAGF,IAAI,GAAG,CAACd,gBAAgB,GAAGC,iBAAiB,IAAIU,IAAI;EAC9DT,KAAK,CAACe,CAAC,GAAGF,IAAI;EAEd,OAAOb,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}