{"ast": null, "code": "/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function (value, index, collection) {\n    accumulator = initAccum ? (initAccum = false, value) : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\nmodule.exports = baseReduce;", "map": {"version": 3, "names": ["baseReduce", "collection", "iteratee", "accumulator", "initAccum", "eachFunc", "value", "index", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_baseReduce.js"], "sourcesContent": ["/**\n * The base implementation of `_.reduce` and `_.reduceRight`, without support\n * for iteratee shorthands, which iterates over `collection` using `eachFunc`.\n *\n * @private\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {*} accumulator The initial value.\n * @param {boolean} initAccum Specify using the first or last element of\n *  `collection` as the initial value.\n * @param {Function} eachFunc The function to iterate over `collection`.\n * @returns {*} Returns the accumulated value.\n */\nfunction baseReduce(collection, iteratee, accumulator, initAccum, eachFunc) {\n  eachFunc(collection, function(value, index, collection) {\n    accumulator = initAccum\n      ? (initAccum = false, value)\n      : iteratee(accumulator, value, index, collection);\n  });\n  return accumulator;\n}\n\nmodule.exports = baseReduce;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAE;EAC1EA,QAAQ,CAACJ,UAAU,EAAE,UAASK,KAAK,EAAEC,KAAK,EAAEN,UAAU,EAAE;IACtDE,WAAW,GAAGC,SAAS,IAClBA,SAAS,GAAG,KAAK,EAAEE,KAAK,IACzBJ,QAAQ,CAACC,WAAW,EAAEG,KAAK,EAAEC,KAAK,EAAEN,UAAU,CAAC;EACrD,CAAC,CAAC;EACF,OAAOE,WAAW;AACpB;AAEAK,MAAM,CAACC,OAAO,GAAGT,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}