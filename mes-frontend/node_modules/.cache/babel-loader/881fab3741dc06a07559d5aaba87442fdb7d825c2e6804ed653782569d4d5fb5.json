{"ast": null, "code": "import { addBorderNode, addDummyNode } from './util';\n/*\n * A nesting graph creates dummy nodes for the tops and bottoms of subgraphs,\n * adds appropriate edges to ensure that all cluster nodes are placed between\n * these boundries, and ensures that the graph is connected.\n *\n * In addition we ensure, through the use of the minlen property, that nodes\n * and subgraph border nodes to not end up on the same rank.\n *\n * Preconditions:\n *\n *    1. Input graph is a DAG\n *    2. Nodes in the input graph has a minlen attribute\n *\n * Postconditions:\n *\n *    1. Input graph is connected.\n *    2. Dummy nodes are added for the tops and bottoms of subgraphs.\n *    3. The minlen attribute for nodes is adjusted to ensure nodes do not\n *       get placed on the same rank as subgraph border nodes.\n *\n * The nesting graph idea comes from <PERSON><PERSON>, \"Layout of Compound Directed\n * Graphs.\"\n */\nconst run = g => {\n  const root = addDummyNode(g, 'root', {}, '_root');\n  const depths = treeDepths(g);\n  let maxDepth = Math.max(...Object.values(depths));\n  if (Math.abs(maxDepth) === Infinity) {\n    maxDepth = 1;\n  }\n  const height = maxDepth - 1; // Note: depths is an Object not an array\n  const nodeSep = 2 * height + 1;\n  // g.graph().nestingRoot = root;\n  // Multiply minlen by nodeSep to align nodes on non-border ranks.\n  g.getAllEdges().forEach(e => {\n    e.data.minlen *= nodeSep;\n  });\n  // Calculate a weight that is sufficient to keep subgraphs vertically compact\n  const weight = sumWeights(g) + 1;\n  // Create border nodes and link them up\n  // g.children()?.forEach((child) => {\n  //   dfs(g, root, nodeSep, weight, height, depths, child);\n  // });\n  g.getRoots().forEach(child => {\n    dfs(g, root, nodeSep, weight, height, depths, child.id);\n  });\n  // Save the multiplier for node layers for later removal of empty border\n  // layers.\n  // g.graph().nodeRankFactor = nodeSep;\n  return {\n    nestingRoot: root,\n    nodeRankFactor: nodeSep\n  };\n};\nconst dfs = (g, root, nodeSep, weight, height, depths, v) => {\n  const children = g.getChildren(v);\n  if (!(children === null || children === void 0 ? void 0 : children.length)) {\n    if (v !== root) {\n      // g.setEdge(root, v, { weight: 0, minlen: nodeSep });\n      g.addEdge({\n        id: `e${Math.random()}`,\n        source: root,\n        target: v,\n        data: {\n          weight: 0,\n          minlen: nodeSep\n        }\n      });\n    }\n    return;\n  }\n  const top = addBorderNode(g, '_bt');\n  const bottom = addBorderNode(g, '_bb');\n  const label = g.getNode(v);\n  g.setParent(top, v);\n  label.data.borderTop = top;\n  g.setParent(bottom, v);\n  label.data.borderBottom = bottom;\n  children === null || children === void 0 ? void 0 : children.forEach(childNode => {\n    dfs(g, root, nodeSep, weight, height, depths, childNode.id);\n    const childTop = childNode.data.borderTop ? childNode.data.borderTop : childNode.id;\n    const childBottom = childNode.data.borderBottom ? childNode.data.borderBottom : childNode.id;\n    const thisWeight = childNode.data.borderTop ? weight : 2 * weight;\n    const minlen = childTop !== childBottom ? 1 : height - depths[v] + 1;\n    g.addEdge({\n      id: `e${Math.random()}`,\n      source: top,\n      target: childTop,\n      data: {\n        minlen,\n        weight: thisWeight,\n        nestingEdge: true\n      }\n    });\n    g.addEdge({\n      id: `e${Math.random()}`,\n      source: childBottom,\n      target: bottom,\n      data: {\n        minlen,\n        weight: thisWeight,\n        nestingEdge: true\n      }\n    });\n  });\n  if (!g.getParent(v)) {\n    g.addEdge({\n      id: `e${Math.random()}`,\n      source: root,\n      target: top,\n      data: {\n        weight: 0,\n        minlen: height + depths[v]\n      }\n    });\n  }\n};\nconst treeDepths = g => {\n  const depths = {};\n  const dfs = (v, depth) => {\n    const children = g.getChildren(v);\n    children === null || children === void 0 ? void 0 : children.forEach(child => dfs(child.id, depth + 1));\n    depths[v] = depth;\n  };\n  // g.children()?.forEach((v) => dfs(v, 1));\n  g.getRoots().forEach(v => dfs(v.id, 1));\n  return depths;\n};\nconst sumWeights = g => {\n  let result = 0;\n  g.getAllEdges().forEach(e => {\n    result += e.data.weight;\n  });\n  return result;\n};\nconst cleanup = (g, nestingRoot) => {\n  // const graphLabel = g.graph();\n  // graphLabel.nestingRoot && g.removeNode(graphLabel.nestingRoot);\n  // delete graphLabel.nestingRoot;\n  if (nestingRoot) {\n    g.removeNode(nestingRoot);\n  }\n  g.getAllEdges().forEach(e => {\n    if (e.data.nestingEdge) {\n      g.removeEdge(e.id);\n    }\n  });\n};\nexport { run, cleanup };", "map": {"version": 3, "names": ["addBorderNode", "addDummyNode", "run", "g", "root", "depths", "treeDepths", "max<PERSON><PERSON><PERSON>", "Math", "max", "Object", "values", "abs", "Infinity", "height", "nodeSep", "getAllEdges", "for<PERSON>ach", "e", "data", "minlen", "weight", "sumWeights", "getRoots", "child", "dfs", "id", "nestingRoot", "nodeRankFactor", "v", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "addEdge", "random", "source", "target", "top", "bottom", "label", "getNode", "setParent", "borderTop", "borderBottom", "childNode", "childTop", "childBottom", "thisWeight", "nestingEdge", "getParent", "depth", "result", "cleanup", "removeNode", "removeEdge"], "sources": ["../../src/antv-dagre/nesting-graph.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,aAAa,EAAEC,YAAY,QAAQ,QAAQ;AAEpD;;;;;;;;;;;;;;;;;;;;;;;AAuBA,MAAMC,GAAG,GAAIC,CAAS,IAAI;EACxB,MAAMC,IAAI,GAAGH,YAAY,CAACE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,OAAO,CAAC;EACjD,MAAME,MAAM,GAAGC,UAAU,CAACH,CAAC,CAAC;EAC5B,IAAII,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGC,MAAM,CAACC,MAAM,CAACN,MAAM,CAAC,CAAC;EAEjD,IAAIG,IAAI,CAACI,GAAG,CAACL,QAAQ,CAAC,KAAKM,QAAQ,EAAE;IACnCN,QAAQ,GAAG,CAAC;;EAGd,MAAMO,MAAM,GAAGP,QAAQ,GAAG,CAAC,CAAC,CAAC;EAC7B,MAAMQ,OAAO,GAAG,CAAC,GAAGD,MAAM,GAAG,CAAC;EAE9B;EAEA;EACAX,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEC,CAAC,IAAI;IAC5BA,CAAC,CAACC,IAAI,CAACC,MAAO,IAAIL,OAAO;EAC3B,CAAC,CAAC;EAEF;EACA,MAAMM,MAAM,GAAGC,UAAU,CAACnB,CAAC,CAAC,GAAG,CAAC;EAEhC;EACA;EACA;EACA;EACAA,CAAC,CAACoB,QAAQ,EAAE,CAACN,OAAO,CAAEO,KAAK,IAAI;IAC7BC,GAAG,CAACtB,CAAC,EAAEC,IAAI,EAAEW,OAAO,EAAEM,MAAM,EAAEP,MAAM,EAAET,MAAM,EAAEmB,KAAK,CAACE,EAAE,CAAC;EACzD,CAAC,CAAC;EAEF;EACA;EACA;EAEA,OAAO;IACLC,WAAW,EAAEvB,IAAI;IACjBwB,cAAc,EAAEb;GACjB;AACH,CAAC;AAED,MAAMU,GAAG,GAAGA,CACVtB,CAAS,EACTC,IAAQ,EACRW,OAAe,EACfM,MAAc,EACdP,MAAc,EACdT,MAA8B,EAC9BwB,CAAK,KACH;EACF,MAAMC,QAAQ,GAAG3B,CAAC,CAAC4B,WAAW,CAACF,CAAC,CAAC;EACjC,IAAI,EAACC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,MAAM,GAAE;IACrB,IAAIH,CAAC,KAAKzB,IAAI,EAAE;MACd;MACAD,CAAC,CAAC8B,OAAO,CAAC;QACRP,EAAE,EAAE,IAAIlB,IAAI,CAAC0B,MAAM,EAAE,EAAE;QACvBC,MAAM,EAAE/B,IAAI;QACZgC,MAAM,EAAEP,CAAC;QACTV,IAAI,EAAE;UAAEE,MAAM,EAAE,CAAC;UAAED,MAAM,EAAEL;QAAO;OACnC,CAAC;;IAEJ;;EAGF,MAAMsB,GAAG,GAAGrC,aAAa,CAACG,CAAC,EAAE,KAAK,CAAC;EACnC,MAAMmC,MAAM,GAAGtC,aAAa,CAACG,CAAC,EAAE,KAAK,CAAC;EACtC,MAAMoC,KAAK,GAAGpC,CAAC,CAACqC,OAAO,CAACX,CAAC,CAAE;EAE3B1B,CAAC,CAACsC,SAAS,CAACJ,GAAG,EAAER,CAAC,CAAC;EACnBU,KAAK,CAACpB,IAAI,CAACuB,SAAS,GAAGL,GAAG;EAC1BlC,CAAC,CAACsC,SAAS,CAACH,MAAM,EAAET,CAAC,CAAC;EACtBU,KAAK,CAACpB,IAAI,CAACwB,YAAY,GAAGL,MAAM;EAEhCR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEb,OAAO,CAAE2B,SAAS,IAAI;IAC9BnB,GAAG,CAACtB,CAAC,EAAEC,IAAI,EAAEW,OAAO,EAAEM,MAAM,EAAEP,MAAM,EAAET,MAAM,EAAEuC,SAAS,CAAClB,EAAE,CAAC;IAE3D,MAAMmB,QAAQ,GAAGD,SAAS,CAACzB,IAAI,CAACuB,SAAS,GACpCE,SAAS,CAACzB,IAAI,CAACuB,SAAgB,GAChCE,SAAS,CAAClB,EAAE;IAChB,MAAMoB,WAAW,GAAGF,SAAS,CAACzB,IAAI,CAACwB,YAAY,GAC1CC,SAAS,CAACzB,IAAI,CAACwB,YAAmB,GACnCC,SAAS,CAAClB,EAAE;IAChB,MAAMqB,UAAU,GAAGH,SAAS,CAACzB,IAAI,CAACuB,SAAS,GAAGrB,MAAM,GAAG,CAAC,GAAGA,MAAM;IACjE,MAAMD,MAAM,GAAGyB,QAAQ,KAAKC,WAAW,GAAG,CAAC,GAAGhC,MAAM,GAAGT,MAAM,CAACwB,CAAC,CAAC,GAAG,CAAC;IAEpE1B,CAAC,CAAC8B,OAAO,CAAC;MACRP,EAAE,EAAE,IAAIlB,IAAI,CAAC0B,MAAM,EAAE,EAAE;MACvBC,MAAM,EAAEE,GAAG;MACXD,MAAM,EAAES,QAAQ;MAChB1B,IAAI,EAAE;QACJC,MAAM;QACNC,MAAM,EAAE0B,UAAU;QAClBC,WAAW,EAAE;;KAEhB,CAAC;IAEF7C,CAAC,CAAC8B,OAAO,CAAC;MACRP,EAAE,EAAE,IAAIlB,IAAI,CAAC0B,MAAM,EAAE,EAAE;MACvBC,MAAM,EAAEW,WAAW;MACnBV,MAAM,EAAEE,MAAM;MACdnB,IAAI,EAAE;QACJC,MAAM;QACNC,MAAM,EAAE0B,UAAU;QAClBC,WAAW,EAAE;;KAEhB,CAAC;EACJ,CAAC,CAAC;EAEF,IAAI,CAAC7C,CAAC,CAAC8C,SAAS,CAACpB,CAAC,CAAC,EAAE;IACnB1B,CAAC,CAAC8B,OAAO,CAAC;MACRP,EAAE,EAAE,IAAIlB,IAAI,CAAC0B,MAAM,EAAE,EAAE;MACvBC,MAAM,EAAE/B,IAAI;MACZgC,MAAM,EAAEC,GAAG;MACXlB,IAAI,EAAE;QACJE,MAAM,EAAE,CAAC;QACTD,MAAM,EAAEN,MAAM,GAAGT,MAAM,CAACwB,CAAC;;KAE5B,CAAC;;AAEN,CAAC;AAED,MAAMvB,UAAU,GAAIH,CAAS,IAAI;EAC/B,MAAME,MAAM,GAAuB,EAAE;EACrC,MAAMoB,GAAG,GAAGA,CAACI,CAAK,EAAEqB,KAAa,KAAI;IACnC,MAAMpB,QAAQ,GAAG3B,CAAC,CAAC4B,WAAW,CAACF,CAAC,CAAC;IACjCC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEb,OAAO,CAAEO,KAAK,IAAKC,GAAG,CAACD,KAAK,CAACE,EAAE,EAAEwB,KAAK,GAAG,CAAC,CAAC,CAAC;IACtD7C,MAAM,CAACwB,CAAC,CAAC,GAAGqB,KAAK;EACnB,CAAC;EACD;EAEA/C,CAAC,CAACoB,QAAQ,EAAE,CAACN,OAAO,CAAEY,CAAC,IAAKJ,GAAG,CAACI,CAAC,CAACH,EAAE,EAAE,CAAC,CAAC,CAAC;EACzC,OAAOrB,MAAM;AACf,CAAC;AAED,MAAMiB,UAAU,GAAInB,CAAS,IAAI;EAC/B,IAAIgD,MAAM,GAAG,CAAC;EACdhD,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEC,CAAC,IAAI;IAC5BiC,MAAM,IAAIjC,CAAC,CAACC,IAAI,CAACE,MAAO;EAC1B,CAAC,CAAC;EACF,OAAO8B,MAAM;AACf,CAAC;AAED,MAAMC,OAAO,GAAGA,CAACjD,CAAS,EAAEwB,WAAgB,KAAI;EAC9C;EACA;EACA;EACA,IAAIA,WAAW,EAAE;IACfxB,CAAC,CAACkD,UAAU,CAAC1B,WAAW,CAAC;;EAG3BxB,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEC,CAAC,IAAI;IAC5B,IAAIA,CAAC,CAACC,IAAI,CAAC6B,WAAW,EAAE;MACtB7C,CAAC,CAACmD,UAAU,CAACpC,CAAC,CAACQ,EAAE,CAAC;;EAEtB,CAAC,CAAC;AACJ,CAAC;AAED,SAASxB,GAAG,EAAEkD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}