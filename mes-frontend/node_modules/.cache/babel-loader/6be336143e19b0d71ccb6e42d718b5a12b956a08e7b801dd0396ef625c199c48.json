{"ast": null, "code": "export { Polygon } from '@antv/g';", "map": {"version": 3, "names": ["Polygon"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Polygon.ts"], "sourcesContent": ["import type { PolygonStyleProps as GPolygonStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Polygon } from '@antv/g';\nexport type PolygonStyleProps = OmitConflictStyleProps<GPolygonStyleProps>;\n"], "mappings": "AAGA,SAASA,OAAO,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}