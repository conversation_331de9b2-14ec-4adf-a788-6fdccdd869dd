{"ast": null, "code": "import { __read } from \"tslib\";\nimport { isNil } from '@antv/util';\nimport { getFont, measureTextWidth } from '../../../util';\nimport { boundTest } from '../utils/test';\nfunction parseLengthString(str, font) {\n  if (font === void 0) {\n    font = {};\n  }\n  if (isNil(str)) return 0;\n  if (typeof str === 'number') return str;\n  return Math.floor(measureTextWidth(str, font));\n}\nexport default function ellipseLabels(labels, overlapCfg, attr, utils) {\n  if (labels.length <= 0) return;\n  var _a = overlapCfg.suffix,\n    suffix = _a === void 0 ? '...' : _a,\n    minLength = overlapCfg.minLength,\n    _b = overlapCfg.maxLength,\n    maxLength = _b === void 0 ? Infinity : _b,\n    _c = overlapCfg.step,\n    ellipsisStep = _c === void 0 ? ' ' : _c,\n    _d = overlapCfg.margin,\n    margin = _d === void 0 ? [0, 0, 0, 0] : _d;\n  var font = getFont(utils.getTextShape(labels[0]));\n  var step = parseLengthString(ellipsisStep, font);\n  var min = minLength ? parseLengthString(minLength, font) : step;\n  var max = parseLengthString(maxLength, font);\n  // Enable to ellipsis label when overlap.\n  if (isNil(max) || max === Infinity) {\n    max = Math.max.apply(null, labels.map(function (d) {\n      return d.getBBox().width;\n    }));\n  }\n  // Generally, 100 ticks cost less than 300ms. If cost time exceed, means ticks count is too large to see.\n  var source = labels.slice();\n  var _e = __read(margin, 4),\n    _f = _e[0],\n    top = _f === void 0 ? 0 : _f,\n    _g = _e[1],\n    right = _g === void 0 ? 0 : _g,\n    _h = _e[2],\n    bottom = _h === void 0 ? top : _h,\n    _j = _e[3],\n    left = _j === void 0 ? right : _j;\n  var _loop_1 = function (allowedLength) {\n    source.forEach(function (label) {\n      utils.ellipsis(utils.getTextShape(label), allowedLength, suffix);\n    });\n    source = boundTest(labels, attr, margin);\n    // 碰撞检测\n    if (source.length < 1) return {\n      value: void 0\n    };\n  };\n  for (var allowedLength = max; allowedLength > min + step; allowedLength -= step) {\n    var state_1 = _loop_1(allowedLength);\n    if (typeof state_1 === \"object\") return state_1.value;\n  }\n}", "map": {"version": 3, "names": ["isNil", "getFont", "measureTextWidth", "boundTest", "parseLengthString", "str", "font", "Math", "floor", "ellipse<PERSON><PERSON><PERSON>", "labels", "overlapCfg", "attr", "utils", "length", "_a", "suffix", "<PERSON><PERSON><PERSON><PERSON>", "_b", "max<PERSON><PERSON><PERSON>", "Infinity", "_c", "step", "ellipsisStep", "_d", "margin", "getTextShape", "min", "max", "apply", "map", "d", "getBBox", "width", "source", "slice", "_e", "__read", "_f", "top", "_g", "right", "_h", "bottom", "_j", "left", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "label", "ellipsis"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/overlap/autoEllipsis.ts"], "sourcesContent": ["import { isNil } from '@antv/util';\nimport type { DisplayObject } from '../../../shapes';\nimport { Text } from '../../../shapes';\nimport { getFont, measureTextWidth } from '../../../util';\nimport { AxisStyleProps, EllipsisOverlapCfg } from '../types';\nimport { boundTest } from '../utils/test';\n\nexport type Utils = {\n  ellipsis: (text: Text, len: number, suffix?: string) => void;\n  getTextShape: (el: DisplayObject) => Text;\n};\n\nfunction parseLengthString(str: number | string, font = {}): number {\n  if (isNil(str)) return 0;\n  if (typeof str === 'number') return str;\n  return Math.floor(measureTextWidth(str, font));\n}\n\nexport default function ellipseLabels(\n  labels: DisplayObject[],\n  overlapCfg: EllipsisOverlapCfg,\n  attr: AxisStyleProps,\n  utils: Utils\n) {\n  if (labels.length <= 0) return;\n  const {\n    suffix = '...',\n    minLength,\n    maxLength = Infinity,\n    step: ellipsisStep = ' ',\n    margin = [0, 0, 0, 0],\n  } = overlapCfg;\n\n  const font = getFont(utils.getTextShape(labels[0]));\n  const step = parseLengthString(ellipsisStep, font);\n  const min = minLength ? parseLengthString(minLength, font) : step;\n  let max = parseLengthString(maxLength, font);\n  // Enable to ellipsis label when overlap.\n  if (isNil(max) || max === Infinity) {\n    max = Math.max.apply(\n      null,\n      labels.map((d) => d.getBBox().width)\n    );\n  }\n  // Generally, 100 ticks cost less than 300ms. If cost time exceed, means ticks count is too large to see.\n  let source = labels.slice();\n  const [top = 0, right = 0, bottom = top, left = right] = margin as number[];\n  for (let allowedLength = max; allowedLength > min + step; allowedLength -= step) {\n    source.forEach((label) => {\n      utils.ellipsis(utils.getTextShape(label), allowedLength, suffix);\n    });\n\n    source = boundTest(labels, attr, margin);\n    // 碰撞检测\n    if (source.length < 1) return;\n  }\n}\n"], "mappings": ";AAAA,SAASA,KAAK,QAAQ,YAAY;AAGlC,SAASC,OAAO,EAAEC,gBAAgB,QAAQ,eAAe;AAEzD,SAASC,SAAS,QAAQ,eAAe;AAOzC,SAASC,iBAAiBA,CAACC,GAAoB,EAAEC,IAAS;EAAT,IAAAA,IAAA;IAAAA,IAAA,KAAS;EAAA;EACxD,IAAIN,KAAK,CAACK,GAAG,CAAC,EAAE,OAAO,CAAC;EACxB,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;EACvC,OAAOE,IAAI,CAACC,KAAK,CAACN,gBAAgB,CAACG,GAAG,EAAEC,IAAI,CAAC,CAAC;AAChD;AAEA,eAAc,SAAUG,aAAaA,CACnCC,MAAuB,EACvBC,UAA8B,EAC9BC,IAAoB,EACpBC,KAAY;EAEZ,IAAIH,MAAM,CAACI,MAAM,IAAI,CAAC,EAAE;EAEtB,IAAAC,EAAA,GAKEJ,UAAU,CAAAK,MALE;IAAdA,MAAM,GAAAD,EAAA,cAAG,KAAK,GAAAA,EAAA;IACdE,SAAS,GAIPN,UAAU,CAAAM,SAJH;IACTC,EAAA,GAGEP,UAAU,CAAAQ,SAHQ;IAApBA,SAAS,GAAAD,EAAA,cAAGE,QAAQ,GAAAF,EAAA;IACpBG,EAAA,GAEEV,UAAU,CAAAW,IAFY;IAAlBC,YAAY,GAAAF,EAAA,cAAG,GAAG,GAAAA,EAAA;IACxBG,EAAA,GACEb,UAAU,CAAAc,MADS;IAArBA,MAAM,GAAAD,EAAA,cAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,GAAAA,EAAA;EAGvB,IAAMlB,IAAI,GAAGL,OAAO,CAACY,KAAK,CAACa,YAAY,CAAChB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,IAAMY,IAAI,GAAGlB,iBAAiB,CAACmB,YAAY,EAAEjB,IAAI,CAAC;EAClD,IAAMqB,GAAG,GAAGV,SAAS,GAAGb,iBAAiB,CAACa,SAAS,EAAEX,IAAI,CAAC,GAAGgB,IAAI;EACjE,IAAIM,GAAG,GAAGxB,iBAAiB,CAACe,SAAS,EAAEb,IAAI,CAAC;EAC5C;EACA,IAAIN,KAAK,CAAC4B,GAAG,CAAC,IAAIA,GAAG,KAAKR,QAAQ,EAAE;IAClCQ,GAAG,GAAGrB,IAAI,CAACqB,GAAG,CAACC,KAAK,CAClB,IAAI,EACJnB,MAAM,CAACoB,GAAG,CAAC,UAACC,CAAC;MAAK,OAAAA,CAAC,CAACC,OAAO,EAAE,CAACC,KAAK;IAAjB,CAAiB,CAAC,CACrC;EACH;EACA;EACA,IAAIC,MAAM,GAAGxB,MAAM,CAACyB,KAAK,EAAE;EACrB,IAAAC,EAAA,GAAAC,MAAA,CAAmDZ,MAAkB;IAApEa,EAAA,GAAAF,EAAA,GAAO;IAAPG,GAAG,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;IAAEE,EAAA,GAAAJ,EAAA,GAAS;IAATK,KAAK,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;IAAEE,EAAA,GAAAN,EAAA,GAAY;IAAZO,MAAM,GAAAD,EAAA,cAAGH,GAAG,GAAAG,EAAA;IAAEE,EAAA,GAAAR,EAAA,GAAY;IAAZS,IAAI,GAAAD,EAAA,cAAGH,KAAK,GAAAG,EAAsB;0BAClEE,aAAa;IACpBZ,MAAM,CAACa,OAAO,CAAC,UAACC,KAAK;MACnBnC,KAAK,CAACoC,QAAQ,CAACpC,KAAK,CAACa,YAAY,CAACsB,KAAK,CAAC,EAAEF,aAAa,EAAE9B,MAAM,CAAC;IAClE,CAAC,CAAC;IAEFkB,MAAM,GAAG/B,SAAS,CAACO,MAAM,EAAEE,IAAI,EAAEa,MAAM,CAAC;IACxC;IACA,IAAIS,MAAM,CAACpB,MAAM,GAAG,CAAC,E;;;;EAPvB,KAAK,IAAIgC,aAAa,GAAGlB,GAAG,EAAEkB,aAAa,GAAGnB,GAAG,GAAGL,IAAI,EAAEwB,aAAa,IAAIxB,IAAI;0BAAtEwB,aAAa;;;AASxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}