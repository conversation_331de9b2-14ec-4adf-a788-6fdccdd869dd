{"ast": null, "code": "export { CustomElement } from '@antv/g';", "map": {"version": 3, "names": ["CustomElement"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/CustomElement.ts"], "sourcesContent": ["import type { BaseCustomElementStyleProps as GBaseCustomElementStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { CustomElement } from '@antv/g';\nexport type BaseCustomElementStyleProps = OmitConflictStyleProps<GBaseCustomElementStyleProps>;\n"], "mappings": "AAGA,SAASA,aAAa,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}