{"ast": null, "code": "export { Path } from '@antv/g';", "map": {"version": 3, "names": ["Path"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Path.ts"], "sourcesContent": ["import type { PathStyleProps as GPathStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Path } from '@antv/g';\nexport type PathStyleProps = OmitConflictStyleProps<GPathStyleProps>;\n"], "mappings": "AAGA,SAASA,IAAI,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}