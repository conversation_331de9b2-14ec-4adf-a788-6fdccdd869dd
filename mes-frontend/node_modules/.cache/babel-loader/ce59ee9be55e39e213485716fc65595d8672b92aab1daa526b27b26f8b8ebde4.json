{"ast": null, "code": "import { deepMix } from '@antv/util';\nimport { Path } from '../../shapes';\nimport { classNames } from '../../util';\nexport var AXIS_BASE_DEFAULT_ATTR = {\n  data: [],\n  animate: {\n    enter: false,\n    update: {\n      duration: 100,\n      easing: 'ease-in-out-sine',\n      fill: 'both'\n    },\n    exit: {\n      duration: 100,\n      fill: 'both'\n    }\n  },\n  showArrow: true,\n  showGrid: true,\n  showLabel: true,\n  showLine: true,\n  showTick: true,\n  showTitle: true,\n  showTrunc: false,\n  dataThreshold: 100,\n  lineLineWidth: 1,\n  lineStroke: 'black',\n  crossPadding: 10,\n  titleFill: 'black',\n  titleFontSize: 12,\n  titlePosition: 'lb',\n  titleSpacing: 0,\n  titleTextAlign: 'center',\n  titleTextBaseline: 'middle',\n  lineArrow: function () {\n    return new Path({\n      style: {\n        d: [['M', 10, 10], ['L', -10, 0], ['L', 10, -10], ['L', 0, 0], ['L', 10, 10], ['Z']],\n        fill: 'black',\n        transformOrigin: 'center'\n      }\n    });\n  },\n  labelAlign: 'parallel',\n  labelDirection: 'positive',\n  labelFontSize: 12,\n  labelSpacing: 0,\n  gridConnect: 'line',\n  gridControlAngles: [],\n  gridDirection: 'positive',\n  gridLength: 0,\n  gridType: 'segment',\n  lineArrowOffset: 15,\n  lineArrowSize: 10,\n  tickDirection: 'positive',\n  tickLength: 5,\n  tickLineWidth: 1,\n  tickStroke: 'black',\n  labelOverlap: [\n    // { type: 'rotate', optionalAngles: [0, 45, 90] },\n    // { type: 'ellipsis', suffix: '...', minLength: 14, maxLength: 160 },\n    // { type: 'hide' },\n  ]\n};\nexport var ARC_DEFAULT_OPTIONS = deepMix({}, AXIS_BASE_DEFAULT_ATTR, {\n  style: {\n    type: 'arc'\n  }\n});\nexport var HELIX_DEFAULT_OPTIONS = deepMix({}, AXIS_BASE_DEFAULT_ATTR, {\n  style: {}\n});\nexport var CLASS_NAMES = classNames({\n  mainGroup: 'main-group',\n  gridGroup: 'grid-group',\n  grid: 'grid',\n  lineGroup: 'line-group',\n  line: 'line',\n  tickGroup: 'tick-group',\n  tick: 'tick',\n  tickItem: 'tick-item',\n  labelGroup: 'label-group',\n  label: 'label',\n  labelItem: 'label-item',\n  titleGroup: 'title-group',\n  title: 'title',\n  lineFirst: 'line-first',\n  lineSecond: 'line-second'\n}, 'axis');", "map": {"version": 3, "names": ["deepMix", "Path", "classNames", "AXIS_BASE_DEFAULT_ATTR", "data", "animate", "enter", "update", "duration", "easing", "fill", "exit", "showArrow", "showGrid", "showLabel", "showLine", "showTick", "showTitle", "showTrunc", "dataThreshold", "lineLineWidth", "lineStroke", "crossPadding", "titleFill", "titleFontSize", "titlePosition", "titleSpacing", "titleTextAlign", "titleTextBaseline", "lineArrow", "style", "d", "transform<PERSON><PERSON>in", "labelAlign", "labelDirection", "labelFontSize", "labelSpacing", "gridConnect", "gridControlAngles", "gridDirection", "gridLength", "gridType", "lineArrowOffset", "lineArrowSize", "tickDirection", "tick<PERSON><PERSON>th", "tickLine<PERSON>idth", "tickStroke", "labelOverlap", "ARC_DEFAULT_OPTIONS", "type", "HELIX_DEFAULT_OPTIONS", "CLASS_NAMES", "mainGroup", "gridGroup", "grid", "lineGroup", "line", "tickGroup", "tick", "tickItem", "labelGroup", "label", "labelItem", "titleGroup", "title", "lineFirst", "lineSecond"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/constant.ts"], "sourcesContent": ["import { deepMix } from '@antv/util';\nimport { Path } from '../../shapes';\nimport { classNames } from '../../util';\nimport type { AxisBaseStyleProps } from './types';\n\nexport const AXIS_BASE_DEFAULT_ATTR: Partial<AxisBaseStyleProps> = {\n  data: [],\n  animate: {\n    enter: false,\n    update: {\n      duration: 100,\n      easing: 'ease-in-out-sine',\n      fill: 'both',\n    },\n    exit: {\n      duration: 100,\n      fill: 'both',\n    },\n  },\n  showArrow: true,\n  showGrid: true,\n  showLabel: true,\n  showLine: true,\n  showTick: true,\n  showTitle: true,\n  showTrunc: false,\n  dataThreshold: 100,\n  lineLineWidth: 1,\n  lineStroke: 'black',\n  crossPadding: 10,\n  titleFill: 'black',\n  titleFontSize: 12,\n  titlePosition: 'lb',\n  titleSpacing: 0,\n  titleTextAlign: 'center',\n  titleTextBaseline: 'middle',\n  lineArrow: () =>\n    new Path({\n      style: {\n        d: [['M', 10, 10], ['L', -10, 0], ['L', 10, -10], ['L', 0, 0], ['L', 10, 10], ['Z']],\n        fill: 'black',\n        transformOrigin: 'center',\n      },\n    }),\n  labelAlign: 'parallel',\n  labelDirection: 'positive',\n  labelFontSize: 12,\n  labelSpacing: 0,\n  gridConnect: 'line',\n  gridControlAngles: [],\n  gridDirection: 'positive',\n  gridLength: 0,\n  gridType: 'segment',\n  lineArrowOffset: 15,\n  lineArrowSize: 10,\n  tickDirection: 'positive',\n  tickLength: 5,\n  tickLineWidth: 1,\n  tickStroke: 'black',\n  labelOverlap: [\n    // { type: 'rotate', optionalAngles: [0, 45, 90] },\n    // { type: 'ellipsis', suffix: '...', minLength: 14, maxLength: 160 },\n    // { type: 'hide' },\n  ],\n};\n\nexport const ARC_DEFAULT_OPTIONS = deepMix({}, AXIS_BASE_DEFAULT_ATTR, {\n  style: {\n    type: 'arc',\n  },\n});\n\nexport const HELIX_DEFAULT_OPTIONS = deepMix({}, AXIS_BASE_DEFAULT_ATTR, {\n  style: {},\n});\n\nexport const CLASS_NAMES = classNames(\n  {\n    mainGroup: 'main-group',\n    gridGroup: 'grid-group',\n    grid: 'grid',\n    lineGroup: 'line-group',\n    line: 'line',\n    tickGroup: 'tick-group',\n    tick: 'tick',\n    tickItem: 'tick-item',\n    labelGroup: 'label-group',\n    label: 'label',\n    labelItem: 'label-item',\n    titleGroup: 'title-group',\n    title: 'title',\n    lineFirst: 'line-first',\n    lineSecond: 'line-second',\n  },\n  'axis'\n);\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,UAAU,QAAQ,YAAY;AAGvC,OAAO,IAAMC,sBAAsB,GAAgC;EACjEC,IAAI,EAAE,EAAE;EACRC,OAAO,EAAE;IACPC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE;MACNC,QAAQ,EAAE,GAAG;MACbC,MAAM,EAAE,kBAAkB;MAC1BC,IAAI,EAAE;KACP;IACDC,IAAI,EAAE;MACJH,QAAQ,EAAE,GAAG;MACbE,IAAI,EAAE;;GAET;EACDE,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,KAAK;EAChBC,aAAa,EAAE,GAAG;EAClBC,aAAa,EAAE,CAAC;EAChBC,UAAU,EAAE,OAAO;EACnBC,YAAY,EAAE,EAAE;EAChBC,SAAS,EAAE,OAAO;EAClBC,aAAa,EAAE,EAAE;EACjBC,aAAa,EAAE,IAAI;EACnBC,YAAY,EAAE,CAAC;EACfC,cAAc,EAAE,QAAQ;EACxBC,iBAAiB,EAAE,QAAQ;EAC3BC,SAAS,EAAE,SAAAA,CAAA;IACT,WAAI5B,IAAI,CAAC;MACP6B,KAAK,EAAE;QACLC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QACpFrB,IAAI,EAAE,OAAO;QACbsB,eAAe,EAAE;;KAEpB,CAAC;EANF,CAME;EACJC,UAAU,EAAE,UAAU;EACtBC,cAAc,EAAE,UAAU;EAC1BC,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,CAAC;EACfC,WAAW,EAAE,MAAM;EACnBC,iBAAiB,EAAE,EAAE;EACrBC,aAAa,EAAE,UAAU;EACzBC,UAAU,EAAE,CAAC;EACbC,QAAQ,EAAE,SAAS;EACnBC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,EAAE;EACjBC,aAAa,EAAE,UAAU;EACzBC,UAAU,EAAE,CAAC;EACbC,aAAa,EAAE,CAAC;EAChBC,UAAU,EAAE,OAAO;EACnBC,YAAY,EAAE;IACZ;IACA;IACA;EAAA;CAEH;AAED,OAAO,IAAMC,mBAAmB,GAAGjD,OAAO,CAAC,EAAE,EAAEG,sBAAsB,EAAE;EACrE2B,KAAK,EAAE;IACLoB,IAAI,EAAE;;CAET,CAAC;AAEF,OAAO,IAAMC,qBAAqB,GAAGnD,OAAO,CAAC,EAAE,EAAEG,sBAAsB,EAAE;EACvE2B,KAAK,EAAE;CACR,CAAC;AAEF,OAAO,IAAMsB,WAAW,GAAGlD,UAAU,CACnC;EACEmD,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE,YAAY;EACvBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,YAAY;EACvBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,YAAY;EACvBC,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE,WAAW;EACrBC,UAAU,EAAE,aAAa;EACzBC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE,YAAY;EACvBC,UAAU,EAAE,aAAa;EACzBC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE,YAAY;EACvBC,UAAU,EAAE;CACb,EACD,MAAM,CACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}