{"ast": null, "code": "import { addDummyNode } from './util';\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nconst DUMMY_NODE_EDGE = 'edge';\nconst DUMMY_NODE_EDGE_LABEL = 'edge-label';\nconst run = (g, dummyChains) => {\n  g.getAllEdges().forEach(edge => normalizeEdge(g, edge, dummyChains));\n};\nconst normalizeEdge = (g, e, dummyChains) => {\n  let v = e.source;\n  let vRank = g.getNode(v).data.rank;\n  const w = e.target;\n  const wRank = g.getNode(w).data.rank;\n  const labelRank = e.data.labelRank;\n  if (wRank === vRank + 1) return;\n  g.removeEdge(e.id);\n  let dummy;\n  let nodeData;\n  let i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    e.data.points = [];\n    nodeData = {\n      originalEdge: e,\n      width: 0,\n      height: 0,\n      rank: vRank\n    };\n    dummy = addDummyNode(g, DUMMY_NODE_EDGE, nodeData, '_d');\n    if (vRank === labelRank) {\n      nodeData.width = e.data.width;\n      nodeData.height = e.data.height;\n      nodeData.dummy = DUMMY_NODE_EDGE_LABEL;\n      nodeData.labelpos = e.data.labelpos;\n    }\n    g.addEdge({\n      id: `e${Math.random()}`,\n      source: v,\n      target: dummy,\n      data: {\n        weight: e.data.weight\n      }\n    });\n    if (i === 0) {\n      dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n  g.addEdge({\n    id: `e${Math.random()}`,\n    source: v,\n    target: w,\n    data: {\n      weight: e.data.weight\n    }\n  });\n};\nconst undo = (g, dummyChains) => {\n  dummyChains.forEach(v => {\n    let node = g.getNode(v);\n    const {\n      data\n    } = node;\n    const originalEdge = data.originalEdge;\n    let w;\n    // Restore original edge.\n    if (originalEdge) {\n      g.addEdge(originalEdge);\n    }\n    let currentV = v;\n    while (node.data.dummy) {\n      w = g.getSuccessors(currentV)[0];\n      g.removeNode(currentV);\n      originalEdge.data.points.push({\n        x: node.data.x,\n        y: node.data.y\n      });\n      if (node.data.dummy === DUMMY_NODE_EDGE_LABEL) {\n        originalEdge.data.x = node.data.x;\n        originalEdge.data.y = node.data.y;\n        originalEdge.data.width = node.data.width;\n        originalEdge.data.height = node.data.height;\n      }\n      currentV = w.id;\n      node = g.getNode(currentV);\n    }\n  });\n};\nexport { run, undo };", "map": {"version": 3, "names": ["addDummyNode", "DUMMY_NODE_EDGE", "DUMMY_NODE_EDGE_LABEL", "run", "g", "dummy<PERSON><PERSON><PERSON>", "getAllEdges", "for<PERSON>ach", "edge", "normalizeEdge", "e", "v", "source", "vRank", "getNode", "data", "rank", "w", "target", "wRank", "labelRank", "removeEdge", "id", "dummy", "nodeData", "i", "points", "originalEdge", "width", "height", "labelpos", "addEdge", "Math", "random", "weight", "push", "undo", "node", "currentV", "getSuccessors", "removeNode", "x", "y"], "sources": ["../../src/antv-dagre/normalize.ts"], "sourcesContent": [null], "mappings": "AAEA,SAASA,YAAY,QAAQ,QAAQ;AAErC;;;;;;;;;;;;;;;;AAiBA,MAAMC,eAAe,GAAG,MAAM;AAC9B,MAAMC,qBAAqB,GAAG,YAAY;AAE1C,MAAMC,GAAG,GAAGA,CAACC,CAAS,EAAEC,WAAiB,KAAI;EAC3CD,CAAC,CAACE,WAAW,EAAE,CAACC,OAAO,CAAEC,IAAI,IAAKC,aAAa,CAACL,CAAC,EAAEI,IAAI,EAAEH,WAAW,CAAC,CAAC;AACxE,CAAC;AAED,MAAMI,aAAa,GAAGA,CAACL,CAAS,EAAEM,CAAiB,EAAEL,WAAiB,KAAI;EACxE,IAAIM,CAAC,GAAGD,CAAC,CAACE,MAAM;EAChB,IAAIC,KAAK,GAAGT,CAAC,CAACU,OAAO,CAACH,CAAC,CAAE,CAACI,IAAI,CAACC,IAAK;EACpC,MAAMC,CAAC,GAAGP,CAAC,CAACQ,MAAM;EAClB,MAAMC,KAAK,GAAGf,CAAC,CAACU,OAAO,CAACG,CAAC,CAAE,CAACF,IAAI,CAACC,IAAK;EACtC,MAAMI,SAAS,GAAGV,CAAC,CAACK,IAAI,CAACK,SAAS;EAElC,IAAID,KAAK,KAAKN,KAAK,GAAG,CAAC,EAAE;EAEzBT,CAAC,CAACiB,UAAU,CAACX,CAAC,CAACY,EAAE,CAAC;EAElB,IAAIC,KAAS;EACb,IAAIC,QAOH;EACD,IAAIC,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAE,EAAEZ,KAAK,EAAEA,KAAK,GAAGM,KAAK,EAAE,EAAEM,CAAC,EAAE,EAAEZ,KAAK,EAAE;IAChDH,CAAC,CAACK,IAAI,CAACW,MAAM,GAAG,EAAE;IAClBF,QAAQ,GAAG;MACTG,YAAY,EAAEjB,CAAC;MACfkB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTb,IAAI,EAAEH;KACP;IAEDU,KAAK,GAAGvB,YAAY,CAACI,CAAC,EAAEH,eAAe,EAAEuB,QAAQ,EAAE,IAAI,CAAC;IAExD,IAAIX,KAAK,KAAKO,SAAS,EAAE;MACvBI,QAAQ,CAACI,KAAK,GAAGlB,CAAC,CAACK,IAAI,CAACa,KAAM;MAC9BJ,QAAQ,CAACK,MAAM,GAAGnB,CAAC,CAACK,IAAI,CAACc,MAAO;MAChCL,QAAQ,CAACD,KAAK,GAAGrB,qBAAqB;MACtCsB,QAAQ,CAACM,QAAQ,GAAGpB,CAAC,CAACK,IAAI,CAACe,QAAkB;;IAG/C1B,CAAC,CAAC2B,OAAO,CAAC;MACRT,EAAE,EAAE,IAAIU,IAAI,CAACC,MAAM,EAAE,EAAE;MACvBrB,MAAM,EAAED,CAAC;MACTO,MAAM,EAAEK,KAAK;MACbR,IAAI,EAAE;QAAEmB,MAAM,EAAExB,CAAC,CAACK,IAAI,CAACmB;MAAM;KAC9B,CAAC;IACF,IAAIT,CAAC,KAAK,CAAC,EAAE;MACXpB,WAAW,CAAC8B,IAAI,CAACZ,KAAK,CAAC;;IAEzBZ,CAAC,GAAGY,KAAK;;EAGXnB,CAAC,CAAC2B,OAAO,CAAC;IACRT,EAAE,EAAE,IAAIU,IAAI,CAACC,MAAM,EAAE,EAAE;IACvBrB,MAAM,EAAED,CAAC;IACTO,MAAM,EAAED,CAAC;IACTF,IAAI,EAAE;MAAEmB,MAAM,EAAExB,CAAC,CAACK,IAAI,CAACmB;IAAM;GAC9B,CAAC;AACJ,CAAC;AAED,MAAME,IAAI,GAAGA,CAAChC,CAAS,EAAEC,WAAiB,KAAI;EAC5CA,WAAW,CAACE,OAAO,CAAEI,CAAC,IAAI;IACxB,IAAI0B,IAAI,GAAGjC,CAAC,CAACU,OAAO,CAACH,CAAC,CAAE;IACxB,MAAM;MAAEI;IAAI,CAAE,GAAGsB,IAAI;IACrB,MAAMV,YAAY,GAAGZ,IAAI,CAACY,YAA8B;IAExD,IAAIV,CAAC;IACL;IACA,IAAIU,YAAY,EAAE;MAChBvB,CAAC,CAAC2B,OAAO,CAACJ,YAAY,CAAC;;IAGzB,IAAIW,QAAQ,GAAG3B,CAAC;IAChB,OAAO0B,IAAI,CAACtB,IAAI,CAACQ,KAAK,EAAE;MACtBN,CAAC,GAAGb,CAAC,CAACmC,aAAa,CAACD,QAAQ,CAAE,CAAC,CAAC,CAAC;MACjClC,CAAC,CAACoC,UAAU,CAACF,QAAQ,CAAC;MACtBX,YAAY,CAACZ,IAAI,CAACW,MAAO,CAACS,IAAI,CAAC;QAC7BM,CAAC,EAAEJ,IAAI,CAACtB,IAAI,CAAC0B,CAAE;QACfC,CAAC,EAAEL,IAAI,CAACtB,IAAI,CAAC2B;OACd,CAAC;MACF,IAAIL,IAAI,CAACtB,IAAI,CAACQ,KAAK,KAAKrB,qBAAqB,EAAE;QAC7CyB,YAAY,CAACZ,IAAI,CAAC0B,CAAC,GAAGJ,IAAI,CAACtB,IAAI,CAAC0B,CAAC;QACjCd,YAAY,CAACZ,IAAI,CAAC2B,CAAC,GAAGL,IAAI,CAACtB,IAAI,CAAC2B,CAAC;QACjCf,YAAY,CAACZ,IAAI,CAACa,KAAK,GAAGS,IAAI,CAACtB,IAAI,CAACa,KAAK;QACzCD,YAAY,CAACZ,IAAI,CAACc,MAAM,GAAGQ,IAAI,CAACtB,IAAI,CAACc,MAAM;;MAE7CS,QAAQ,GAAGrB,CAAC,CAACK,EAAE;MACfe,IAAI,GAAGjC,CAAC,CAACU,OAAO,CAACwB,QAAQ,CAAE;;EAE/B,CAAC,CAAC;AACJ,CAAC;AAED,SAASnC,GAAG,EAAEiC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}