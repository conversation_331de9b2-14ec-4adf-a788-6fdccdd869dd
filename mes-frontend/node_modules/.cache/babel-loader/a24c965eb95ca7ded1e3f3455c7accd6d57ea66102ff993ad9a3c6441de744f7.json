{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = PriorityQueue;\n\n/**\n * A min-priority queue data structure. This algorithm is derived from <PERSON><PERSON><PERSON>,\n * et al., \"Introduction to Algorithms\". The basic idea of a min-priority\n * queue is that you can efficiently (in O(1) time) get the smallest key in\n * the queue. Adding and removing elements takes O(log n) time. A key can\n * have its priority decreased in O(log n) time.\n */\nfunction PriorityQueue() {\n  this._arr = [];\n  this._keyIndices = {};\n}\n\n/**\n * Returns the number of elements in the queue. Takes `O(1)` time.\n */\nPriorityQueue.prototype.size = function () {\n  return this._arr.length;\n};\n\n/**\n * Returns the keys that are in the queue. Takes `O(n)` time.\n */\nPriorityQueue.prototype.keys = function () {\n  return this._arr.map(function (x) {\n    return x.key;\n  });\n};\n\n/**\n * Returns `true` if **key** is in the queue and `false` if not.\n */\nPriorityQueue.prototype.has = function (key) {\n  return _.has(this._keyIndices, key);\n};\n\n/**\n * Returns the priority for **key**. If **key** is not present in the queue\n * then this function returns `undefined`. Takes `O(1)` time.\n *\n * @param {Object} key\n */\nPriorityQueue.prototype.priority = function (key) {\n  var index = this._keyIndices[key];\n  if (index !== undefined) {\n    return this._arr[index].priority;\n  }\n};\n\n/**\n * Returns the key for the minimum element in this queue. If the queue is\n * empty this function throws an Error. Takes `O(1)` time.\n */\nPriorityQueue.prototype.min = function () {\n  if (this.size() === 0) {\n    throw new Error(\"Queue underflow\");\n  }\n  return this._arr[0].key;\n};\n\n/**\n * Inserts a new key into the priority queue. If the key already exists in\n * the queue this function returns `false`; otherwise it will return `true`.\n * Takes `O(n)` time.\n *\n * @param {Object} key the key to add\n * @param {Number} priority the initial priority for the key\n */\nPriorityQueue.prototype.add = function (key, priority) {\n  var keyIndices = this._keyIndices;\n  key = String(key);\n  if (!_.has(keyIndices, key)) {\n    var arr = this._arr;\n    var index = arr.length;\n    keyIndices[key] = index;\n    arr.push({\n      key: key,\n      priority: priority\n    });\n    this._decrease(index);\n    return true;\n  }\n  return false;\n};\n\n/**\n * Removes and returns the smallest key in the queue. Takes `O(log n)` time.\n */\nPriorityQueue.prototype.removeMin = function () {\n  this._swap(0, this._arr.length - 1);\n  var min = this._arr.pop();\n  delete this._keyIndices[min.key];\n  this._heapify(0);\n  return min.key;\n};\n\n/**\n * Decreases the priority for **key** to **priority**. If the new priority is\n * greater than the previous priority, this function will throw an Error.\n *\n * @param {Object} key the key for which to raise priority\n * @param {Number} priority the new priority for the key\n */\nPriorityQueue.prototype.decrease = function (key, priority) {\n  var index = this._keyIndices[key];\n  if (priority > this._arr[index].priority) {\n    throw new Error(\"New priority is greater than current priority. \" + \"Key: \" + key + \" Old: \" + this._arr[index].priority + \" New: \" + priority);\n  }\n  this._arr[index].priority = priority;\n  this._decrease(index);\n};\nPriorityQueue.prototype._heapify = function (i) {\n  var arr = this._arr;\n  var l = 2 * i;\n  var r = l + 1;\n  var largest = i;\n  if (l < arr.length) {\n    largest = arr[l].priority < arr[largest].priority ? l : largest;\n    if (r < arr.length) {\n      largest = arr[r].priority < arr[largest].priority ? r : largest;\n    }\n    if (largest !== i) {\n      this._swap(i, largest);\n      this._heapify(largest);\n    }\n  }\n};\nPriorityQueue.prototype._decrease = function (index) {\n  var arr = this._arr;\n  var priority = arr[index].priority;\n  var parent;\n  while (index !== 0) {\n    parent = index >> 1;\n    if (arr[parent].priority < priority) {\n      break;\n    }\n    this._swap(index, parent);\n    index = parent;\n  }\n};\nPriorityQueue.prototype._swap = function (i, j) {\n  var arr = this._arr;\n  var keyIndices = this._keyIndices;\n  var origArrI = arr[i];\n  var origArrJ = arr[j];\n  arr[i] = origArrJ;\n  arr[j] = origArrI;\n  keyIndices[origArrJ.key] = i;\n  keyIndices[origArrI.key] = j;\n};", "map": {"version": 3, "names": ["_", "require", "module", "exports", "PriorityQueue", "_arr", "_keyIndices", "prototype", "size", "length", "keys", "map", "x", "key", "has", "priority", "index", "undefined", "min", "Error", "add", "keyIndices", "String", "arr", "push", "_decrease", "removeMin", "_swap", "pop", "_heapify", "decrease", "i", "l", "r", "largest", "parent", "j", "origArrI", "origArrJ"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/data/priority-queue.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = PriorityQueue;\n\n/**\n * A min-priority queue data structure. This algorithm is derived from <PERSON><PERSON><PERSON>,\n * et al., \"Introduction to Algorithms\". The basic idea of a min-priority\n * queue is that you can efficiently (in O(1) time) get the smallest key in\n * the queue. Adding and removing elements takes O(log n) time. A key can\n * have its priority decreased in O(log n) time.\n */\nfunction PriorityQueue() {\n  this._arr = [];\n  this._keyIndices = {};\n}\n\n/**\n * Returns the number of elements in the queue. Takes `O(1)` time.\n */\nPriorityQueue.prototype.size = function() {\n  return this._arr.length;\n};\n\n/**\n * Returns the keys that are in the queue. Takes `O(n)` time.\n */\nPriorityQueue.prototype.keys = function() {\n  return this._arr.map(function(x) { return x.key; });\n};\n\n/**\n * Returns `true` if **key** is in the queue and `false` if not.\n */\nPriorityQueue.prototype.has = function(key) {\n  return _.has(this._keyIndices, key);\n};\n\n/**\n * Returns the priority for **key**. If **key** is not present in the queue\n * then this function returns `undefined`. Takes `O(1)` time.\n *\n * @param {Object} key\n */\nPriorityQueue.prototype.priority = function(key) {\n  var index = this._keyIndices[key];\n  if (index !== undefined) {\n    return this._arr[index].priority;\n  }\n};\n\n/**\n * Returns the key for the minimum element in this queue. If the queue is\n * empty this function throws an Error. Takes `O(1)` time.\n */\nPriorityQueue.prototype.min = function() {\n  if (this.size() === 0) {\n    throw new Error(\"Queue underflow\");\n  }\n  return this._arr[0].key;\n};\n\n/**\n * Inserts a new key into the priority queue. If the key already exists in\n * the queue this function returns `false`; otherwise it will return `true`.\n * Takes `O(n)` time.\n *\n * @param {Object} key the key to add\n * @param {Number} priority the initial priority for the key\n */\nPriorityQueue.prototype.add = function(key, priority) {\n  var keyIndices = this._keyIndices;\n  key = String(key);\n  if (!_.has(keyIndices, key)) {\n    var arr = this._arr;\n    var index = arr.length;\n    keyIndices[key] = index;\n    arr.push({key: key, priority: priority});\n    this._decrease(index);\n    return true;\n  }\n  return false;\n};\n\n/**\n * Removes and returns the smallest key in the queue. Takes `O(log n)` time.\n */\nPriorityQueue.prototype.removeMin = function() {\n  this._swap(0, this._arr.length - 1);\n  var min = this._arr.pop();\n  delete this._keyIndices[min.key];\n  this._heapify(0);\n  return min.key;\n};\n\n/**\n * Decreases the priority for **key** to **priority**. If the new priority is\n * greater than the previous priority, this function will throw an Error.\n *\n * @param {Object} key the key for which to raise priority\n * @param {Number} priority the new priority for the key\n */\nPriorityQueue.prototype.decrease = function(key, priority) {\n  var index = this._keyIndices[key];\n  if (priority > this._arr[index].priority) {\n    throw new Error(\"New priority is greater than current priority. \" +\n        \"Key: \" + key + \" Old: \" + this._arr[index].priority + \" New: \" + priority);\n  }\n  this._arr[index].priority = priority;\n  this._decrease(index);\n};\n\nPriorityQueue.prototype._heapify = function(i) {\n  var arr = this._arr;\n  var l = 2 * i;\n  var r = l + 1;\n  var largest = i;\n  if (l < arr.length) {\n    largest = arr[l].priority < arr[largest].priority ? l : largest;\n    if (r < arr.length) {\n      largest = arr[r].priority < arr[largest].priority ? r : largest;\n    }\n    if (largest !== i) {\n      this._swap(i, largest);\n      this._heapify(largest);\n    }\n  }\n};\n\nPriorityQueue.prototype._decrease = function(index) {\n  var arr = this._arr;\n  var priority = arr[index].priority;\n  var parent;\n  while (index !== 0) {\n    parent = index >> 1;\n    if (arr[parent].priority < priority) {\n      break;\n    }\n    this._swap(index, parent);\n    index = parent;\n  }\n};\n\nPriorityQueue.prototype._swap = function(i, j) {\n  var arr = this._arr;\n  var keyIndices = this._keyIndices;\n  var origArrI = arr[i];\n  var origArrJ = arr[j];\n  arr[i] = origArrJ;\n  arr[j] = origArrI;\n  keyIndices[origArrJ.key] = i;\n  keyIndices[origArrI.key] = j;\n};\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,aAAa;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAaA,CAAA,EAAG;EACvB,IAAI,CAACC,IAAI,GAAG,EAAE;EACd,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC;AACvB;;AAEA;AACA;AACA;AACAF,aAAa,CAACG,SAAS,CAACC,IAAI,GAAG,YAAW;EACxC,OAAO,IAAI,CAACH,IAAI,CAACI,MAAM;AACzB,CAAC;;AAED;AACA;AACA;AACAL,aAAa,CAACG,SAAS,CAACG,IAAI,GAAG,YAAW;EACxC,OAAO,IAAI,CAACL,IAAI,CAACM,GAAG,CAAC,UAASC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACC,GAAG;EAAE,CAAC,CAAC;AACrD,CAAC;;AAED;AACA;AACA;AACAT,aAAa,CAACG,SAAS,CAACO,GAAG,GAAG,UAASD,GAAG,EAAE;EAC1C,OAAOb,CAAC,CAACc,GAAG,CAAC,IAAI,CAACR,WAAW,EAAEO,GAAG,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAT,aAAa,CAACG,SAAS,CAACQ,QAAQ,GAAG,UAASF,GAAG,EAAE;EAC/C,IAAIG,KAAK,GAAG,IAAI,CAACV,WAAW,CAACO,GAAG,CAAC;EACjC,IAAIG,KAAK,KAAKC,SAAS,EAAE;IACvB,OAAO,IAAI,CAACZ,IAAI,CAACW,KAAK,CAAC,CAACD,QAAQ;EAClC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACAX,aAAa,CAACG,SAAS,CAACW,GAAG,GAAG,YAAW;EACvC,IAAI,IAAI,CAACV,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE;IACrB,MAAM,IAAIW,KAAK,CAAC,iBAAiB,CAAC;EACpC;EACA,OAAO,IAAI,CAACd,IAAI,CAAC,CAAC,CAAC,CAACQ,GAAG;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAT,aAAa,CAACG,SAAS,CAACa,GAAG,GAAG,UAASP,GAAG,EAAEE,QAAQ,EAAE;EACpD,IAAIM,UAAU,GAAG,IAAI,CAACf,WAAW;EACjCO,GAAG,GAAGS,MAAM,CAACT,GAAG,CAAC;EACjB,IAAI,CAACb,CAAC,CAACc,GAAG,CAACO,UAAU,EAAER,GAAG,CAAC,EAAE;IAC3B,IAAIU,GAAG,GAAG,IAAI,CAAClB,IAAI;IACnB,IAAIW,KAAK,GAAGO,GAAG,CAACd,MAAM;IACtBY,UAAU,CAACR,GAAG,CAAC,GAAGG,KAAK;IACvBO,GAAG,CAACC,IAAI,CAAC;MAACX,GAAG,EAAEA,GAAG;MAAEE,QAAQ,EAAEA;IAAQ,CAAC,CAAC;IACxC,IAAI,CAACU,SAAS,CAACT,KAAK,CAAC;IACrB,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACAZ,aAAa,CAACG,SAAS,CAACmB,SAAS,GAAG,YAAW;EAC7C,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE,IAAI,CAACtB,IAAI,CAACI,MAAM,GAAG,CAAC,CAAC;EACnC,IAAIS,GAAG,GAAG,IAAI,CAACb,IAAI,CAACuB,GAAG,CAAC,CAAC;EACzB,OAAO,IAAI,CAACtB,WAAW,CAACY,GAAG,CAACL,GAAG,CAAC;EAChC,IAAI,CAACgB,QAAQ,CAAC,CAAC,CAAC;EAChB,OAAOX,GAAG,CAACL,GAAG;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACAT,aAAa,CAACG,SAAS,CAACuB,QAAQ,GAAG,UAASjB,GAAG,EAAEE,QAAQ,EAAE;EACzD,IAAIC,KAAK,GAAG,IAAI,CAACV,WAAW,CAACO,GAAG,CAAC;EACjC,IAAIE,QAAQ,GAAG,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC,CAACD,QAAQ,EAAE;IACxC,MAAM,IAAII,KAAK,CAAC,iDAAiD,GAC7D,OAAO,GAAGN,GAAG,GAAG,QAAQ,GAAG,IAAI,CAACR,IAAI,CAACW,KAAK,CAAC,CAACD,QAAQ,GAAG,QAAQ,GAAGA,QAAQ,CAAC;EACjF;EACA,IAAI,CAACV,IAAI,CAACW,KAAK,CAAC,CAACD,QAAQ,GAAGA,QAAQ;EACpC,IAAI,CAACU,SAAS,CAACT,KAAK,CAAC;AACvB,CAAC;AAEDZ,aAAa,CAACG,SAAS,CAACsB,QAAQ,GAAG,UAASE,CAAC,EAAE;EAC7C,IAAIR,GAAG,GAAG,IAAI,CAAClB,IAAI;EACnB,IAAI2B,CAAC,GAAG,CAAC,GAAGD,CAAC;EACb,IAAIE,CAAC,GAAGD,CAAC,GAAG,CAAC;EACb,IAAIE,OAAO,GAAGH,CAAC;EACf,IAAIC,CAAC,GAAGT,GAAG,CAACd,MAAM,EAAE;IAClByB,OAAO,GAAGX,GAAG,CAACS,CAAC,CAAC,CAACjB,QAAQ,GAAGQ,GAAG,CAACW,OAAO,CAAC,CAACnB,QAAQ,GAAGiB,CAAC,GAAGE,OAAO;IAC/D,IAAID,CAAC,GAAGV,GAAG,CAACd,MAAM,EAAE;MAClByB,OAAO,GAAGX,GAAG,CAACU,CAAC,CAAC,CAAClB,QAAQ,GAAGQ,GAAG,CAACW,OAAO,CAAC,CAACnB,QAAQ,GAAGkB,CAAC,GAAGC,OAAO;IACjE;IACA,IAAIA,OAAO,KAAKH,CAAC,EAAE;MACjB,IAAI,CAACJ,KAAK,CAACI,CAAC,EAAEG,OAAO,CAAC;MACtB,IAAI,CAACL,QAAQ,CAACK,OAAO,CAAC;IACxB;EACF;AACF,CAAC;AAED9B,aAAa,CAACG,SAAS,CAACkB,SAAS,GAAG,UAAST,KAAK,EAAE;EAClD,IAAIO,GAAG,GAAG,IAAI,CAAClB,IAAI;EACnB,IAAIU,QAAQ,GAAGQ,GAAG,CAACP,KAAK,CAAC,CAACD,QAAQ;EAClC,IAAIoB,MAAM;EACV,OAAOnB,KAAK,KAAK,CAAC,EAAE;IAClBmB,MAAM,GAAGnB,KAAK,IAAI,CAAC;IACnB,IAAIO,GAAG,CAACY,MAAM,CAAC,CAACpB,QAAQ,GAAGA,QAAQ,EAAE;MACnC;IACF;IACA,IAAI,CAACY,KAAK,CAACX,KAAK,EAAEmB,MAAM,CAAC;IACzBnB,KAAK,GAAGmB,MAAM;EAChB;AACF,CAAC;AAED/B,aAAa,CAACG,SAAS,CAACoB,KAAK,GAAG,UAASI,CAAC,EAAEK,CAAC,EAAE;EAC7C,IAAIb,GAAG,GAAG,IAAI,CAAClB,IAAI;EACnB,IAAIgB,UAAU,GAAG,IAAI,CAACf,WAAW;EACjC,IAAI+B,QAAQ,GAAGd,GAAG,CAACQ,CAAC,CAAC;EACrB,IAAIO,QAAQ,GAAGf,GAAG,CAACa,CAAC,CAAC;EACrBb,GAAG,CAACQ,CAAC,CAAC,GAAGO,QAAQ;EACjBf,GAAG,CAACa,CAAC,CAAC,GAAGC,QAAQ;EACjBhB,UAAU,CAACiB,QAAQ,CAACzB,GAAG,CAAC,GAAGkB,CAAC;EAC5BV,UAAU,CAACgB,QAAQ,CAACxB,GAAG,CAAC,GAAGuB,CAAC;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}