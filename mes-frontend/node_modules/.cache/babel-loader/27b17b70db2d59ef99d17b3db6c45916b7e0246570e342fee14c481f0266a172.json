{"ast": null, "code": "export { getDefaultStyle as left } from './default';", "map": {"version": 3, "names": ["getDefaultStyle", "left"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/left.ts"], "sourcesContent": ["export { getDefaultStyle as left } from './default';\n"], "mappings": "AAAA,SAASA,eAAe,IAAIC,IAAI,QAAQ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}