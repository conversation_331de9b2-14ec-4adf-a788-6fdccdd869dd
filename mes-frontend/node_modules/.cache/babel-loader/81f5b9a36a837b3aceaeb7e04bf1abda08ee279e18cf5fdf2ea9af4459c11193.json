{"ast": null, "code": "export { Rect } from '@antv/g';", "map": {"version": 3, "names": ["Rect"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Rect.ts"], "sourcesContent": ["import type { RectStyleProps as GRectStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Rect } from '@antv/g';\nexport type RectStyleProps = OmitConflictStyleProps<GRectStyleProps>;\n"], "mappings": "AAGA,SAASA,IAAI,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}