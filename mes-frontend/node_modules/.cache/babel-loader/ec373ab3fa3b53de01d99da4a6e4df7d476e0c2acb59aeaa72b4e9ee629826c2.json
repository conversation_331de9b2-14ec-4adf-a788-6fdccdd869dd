{"ast": null, "code": "import { classNames, deepAssign, superStyleProps } from '../../util';\nimport { DEFAULT_HANDLE_CFG } from './continuous/handle';\nexport var LEGEND_BASE_DEFAULT_OPTIONS = {\n  showTitle: true,\n  padding: 0,\n  orientation: 'horizontal',\n  backgroundFill: 'transparent',\n  titleText: '',\n  titleSpacing: 4,\n  titlePosition: 'top-left',\n  titleFill: '#2C3542',\n  titleFontWeight: 'bold',\n  titleFontFamily: 'sans-serif',\n  titleFontSize: 12\n};\nexport var CATEGORY_DEFAULT_OPTIONS = deepAssign({}, LEGEND_BASE_DEFAULT_OPTIONS, {});\nexport var CONTINUOUS_DEFAULT_OPTIONS = deepAssign({}, LEGEND_BASE_DEFAULT_OPTIONS, superStyleProps(DEFAULT_HANDLE_CFG, 'handle'), {\n  color: ['#d0e3fa', '#acc7f6', '#8daaf2', '#6d8eea', '#4d73cd', '#325bb1', '#5a3e75', '#8c3c79', '#e23455', '#e7655b'],\n  indicatorBackgroundFill: '#262626',\n  indicatorLabelFill: 'white',\n  indicatorLabelFontSize: 12,\n  indicatorVisibility: 'hidden',\n  labelAlign: 'value',\n  labelDirection: 'positive',\n  labelSpacing: 5,\n  showHandle: true,\n  showIndicator: true,\n  showLabel: true,\n  slidable: true,\n  titleText: '',\n  type: 'continuous'\n});\n// 连续图例步长比例\nexport var STEP_RATIO = 0.01;\n// 分类图例name和value宽度比例\nexport var NAME_VALUE_RATIO = 0.5;\nexport var CLASS_NAMES = classNames({\n  title: 'title',\n  titleGroup: 'title-group',\n  items: 'items',\n  itemsGroup: 'items-group',\n  contentGroup: 'content-group',\n  ribbonGroup: 'ribbon-group',\n  ribbon: 'ribbon',\n  handlesGroup: 'handles-group',\n  handle: 'handle',\n  startHandle: 'start-handle',\n  endHandle: 'end-handle',\n  labelGroup: 'label-group',\n  label: 'label',\n  indicator: 'indicator'\n}, 'legend');", "map": {"version": 3, "names": ["classNames", "deepAssign", "superStyleProps", "DEFAULT_HANDLE_CFG", "LEGEND_BASE_DEFAULT_OPTIONS", "showTitle", "padding", "orientation", "backgroundFill", "titleText", "titleSpacing", "titlePosition", "titleFill", "titleFontWeight", "titleFontFamily", "titleFontSize", "CATEGORY_DEFAULT_OPTIONS", "CONTINUOUS_DEFAULT_OPTIONS", "color", "indicatorBackgroundFill", "indicatorLabelFill", "indicatorLabelFontSize", "indicatorVisibility", "labelAlign", "labelDirection", "labelSpacing", "showHandle", "showIndicator", "showLabel", "slidable", "type", "STEP_RATIO", "NAME_VALUE_RATIO", "CLASS_NAMES", "title", "titleGroup", "items", "itemsGroup", "contentGroup", "ribbonGroup", "ribbon", "handlesGroup", "handle", "startHandle", "endHandle", "labelGroup", "label", "indicator"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/legend/constant.ts"], "sourcesContent": ["import { classNames, deepAssign, superStyleProps } from '../../util';\nimport { DEFAULT_HANDLE_CFG } from './continuous/handle';\nimport type { LegendBaseStyleProps } from './types';\n\nexport const LEGEND_BASE_DEFAULT_OPTIONS: Partial<LegendBaseStyleProps> = {\n  showTitle: true,\n  padding: 0,\n  orientation: 'horizontal',\n  backgroundFill: 'transparent',\n  titleText: '',\n  titleSpacing: 4,\n  titlePosition: 'top-left',\n  titleFill: '#2C3542',\n  titleFontWeight: 'bold',\n  titleFontFamily: 'sans-serif',\n  titleFontSize: 12,\n};\n\nexport const CATEGORY_DEFAULT_OPTIONS = deepAssign({}, LEGEND_BASE_DEFAULT_OPTIONS, {});\n\nexport const CONTINUOUS_DEFAULT_OPTIONS = deepAssign(\n  {},\n  LEGEND_BASE_DEFAULT_OPTIONS,\n  superStyleProps(DEFAULT_HANDLE_CFG, 'handle'),\n  {\n    color: [\n      '#d0e3fa',\n      '#acc7f6',\n      '#8daaf2',\n      '#6d8eea',\n      '#4d73cd',\n      '#325bb1',\n      '#5a3e75',\n      '#8c3c79',\n      '#e23455',\n      '#e7655b',\n    ],\n    indicatorBackgroundFill: '#262626',\n    indicatorLabelFill: 'white',\n    indicatorLabelFontSize: 12,\n    indicatorVisibility: 'hidden',\n    labelAlign: 'value',\n    labelDirection: 'positive',\n    labelSpacing: 5,\n    showHandle: true,\n    showIndicator: true,\n    showLabel: true,\n    slidable: true,\n    titleText: '',\n    type: 'continuous',\n  }\n);\n\n// 连续图例步长比例\nexport const STEP_RATIO = 0.01;\n\n// 分类图例name和value宽度比例\nexport const NAME_VALUE_RATIO = 0.5;\n\nexport const CLASS_NAMES = classNames(\n  {\n    title: 'title',\n    titleGroup: 'title-group',\n    items: 'items',\n    itemsGroup: 'items-group',\n    contentGroup: 'content-group',\n    ribbonGroup: 'ribbon-group',\n    ribbon: 'ribbon',\n    handlesGroup: 'handles-group',\n    handle: 'handle',\n    startHandle: 'start-handle',\n    endHandle: 'end-handle',\n    labelGroup: 'label-group',\n    label: 'label',\n    indicator: 'indicator',\n  },\n  'legend'\n);\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,UAAU,EAAEC,eAAe,QAAQ,YAAY;AACpE,SAASC,kBAAkB,QAAQ,qBAAqB;AAGxD,OAAO,IAAMC,2BAA2B,GAAkC;EACxEC,SAAS,EAAE,IAAI;EACfC,OAAO,EAAE,CAAC;EACVC,WAAW,EAAE,YAAY;EACzBC,cAAc,EAAE,aAAa;EAC7BC,SAAS,EAAE,EAAE;EACbC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAE,UAAU;EACzBC,SAAS,EAAE,SAAS;EACpBC,eAAe,EAAE,MAAM;EACvBC,eAAe,EAAE,YAAY;EAC7BC,aAAa,EAAE;CAChB;AAED,OAAO,IAAMC,wBAAwB,GAAGf,UAAU,CAAC,EAAE,EAAEG,2BAA2B,EAAE,EAAE,CAAC;AAEvF,OAAO,IAAMa,0BAA0B,GAAGhB,UAAU,CAClD,EAAE,EACFG,2BAA2B,EAC3BF,eAAe,CAACC,kBAAkB,EAAE,QAAQ,CAAC,EAC7C;EACEe,KAAK,EAAE,CACL,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CACV;EACDC,uBAAuB,EAAE,SAAS;EAClCC,kBAAkB,EAAE,OAAO;EAC3BC,sBAAsB,EAAE,EAAE;EAC1BC,mBAAmB,EAAE,QAAQ;EAC7BC,UAAU,EAAE,OAAO;EACnBC,cAAc,EAAE,UAAU;EAC1BC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,IAAI;EAChBC,aAAa,EAAE,IAAI;EACnBC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdpB,SAAS,EAAE,EAAE;EACbqB,IAAI,EAAE;CACP,CACF;AAED;AACA,OAAO,IAAMC,UAAU,GAAG,IAAI;AAE9B;AACA,OAAO,IAAMC,gBAAgB,GAAG,GAAG;AAEnC,OAAO,IAAMC,WAAW,GAAGjC,UAAU,CACnC;EACEkC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,aAAa;EACzBC,KAAK,EAAE,OAAO;EACdC,UAAU,EAAE,aAAa;EACzBC,YAAY,EAAE,eAAe;EAC7BC,WAAW,EAAE,cAAc;EAC3BC,MAAM,EAAE,QAAQ;EAChBC,YAAY,EAAE,eAAe;EAC7BC,MAAM,EAAE,QAAQ;EAChBC,WAAW,EAAE,cAAc;EAC3BC,SAAS,EAAE,YAAY;EACvBC,UAAU,EAAE,aAAa;EACzBC,KAAK,EAAE,OAAO;EACdC,SAAS,EAAE;CACZ,EACD,QAAQ,CACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}