{"ast": null, "code": "import { __assign, __read } from \"tslib\";\n/* global Keyframe */\nimport { isNil } from '@antv/util';\nimport { show, hide } from '../util';\nfunction isStandardAnimationOption(option) {\n  if (typeof option === 'boolean') return false;\n  return 'enter' in option && 'update' in option && 'exit' in option;\n}\nexport function parseAnimationOption(option) {\n  // option is false => all animation is false\n  // option is { enter: {}, update: {}, exit: {}, ...baseOption } =>\n  //    { enter: { ...enter, ...baseOption }, update: { ...update, ...baseOption }, exit: { ...exit, ...baseOption } }\n  // option is { enter: {}, update: {}, exit: {} } => option\n  if (!option) return {\n    enter: false,\n    update: false,\n    exit: false\n  };\n  var keys = ['enter', 'update', 'exit'];\n  var baseOption = Object.fromEntries(Object.entries(option).filter(function (_a) {\n    var _b = __read(_a, 1),\n      k = _b[0];\n    return !keys.includes(k);\n  }));\n  return Object.fromEntries(keys.map(function (k) {\n    if (isStandardAnimationOption(option)) {\n      if (option[k] === false) return [k, false];\n      return [k, __assign(__assign({}, option[k]), baseOption)];\n    }\n    return [k, baseOption];\n  }));\n}\nexport function onAnimateFinished(animation, callback) {\n  if (!animation) callback();else animation.finished.then(callback);\n}\nexport function onAnimatesFinished(animations, callback) {\n  if (animations.length === 0) callback();else Promise.all(animations.map(function (a) {\n    return a === null || a === void 0 ? void 0 : a.finished;\n  })).then(callback);\n}\nfunction attr(target, value) {\n  if ('update' in target) target.update(value);else target.attr(value);\n}\nexport function animate(target, keyframes, options) {\n  if (keyframes.length === 0) return null;\n  if (!options) {\n    var state = keyframes.slice(-1)[0];\n    attr(target, {\n      style: state\n    });\n    return null;\n  }\n  return target.animate(keyframes, options);\n}\nfunction identicalTextNode(source, target) {\n  if (source.nodeName !== 'text' || target.nodeName !== 'text') return false;\n  if (source.attributes.text !== target.attributes.text) return false;\n  return true;\n}\n/**\n * transition source shape to target shape\n * @param source\n * @param target\n * @param options\n * @param after destroy or hide source shape after transition\n */\nexport function transitionShape(source, target, options, after) {\n  if (after === void 0) {\n    after = 'destroy';\n  }\n  // If source and target are both text node and with same text,\n  // do not apply shape animation.\n  if (identicalTextNode(source, target)) {\n    source.remove();\n    return [null];\n  }\n  var afterTransition = function () {\n    if (after === 'destroy') source.destroy();else if (after === 'hide') hide(source);\n    if (target.isVisible()) show(target);\n  };\n  if (!options) {\n    afterTransition();\n    return [null];\n  }\n  var _a = options.duration,\n    duration = _a === void 0 ? 0 : _a,\n    _b = options.delay,\n    delay = _b === void 0 ? 0 : _b;\n  var middle = Math.ceil(+duration / 2);\n  var offset = +duration / 4;\n  var _c = __read(source.getGeometryBounds().center, 2),\n    sx = _c[0],\n    sy = _c[1];\n  var _d = __read(target.getGeometryBounds().center, 2),\n    ex = _d[0],\n    ey = _d[1];\n  var _e = __read([(sx + ex) / 2 - sx, (sy + ey) / 2 - sy], 2),\n    mx = _e[0],\n    my = _e[1];\n  var _f = source.style.opacity,\n    so = _f === void 0 ? 1 : _f;\n  var _g = target.style.opacity,\n    to = _g === void 0 ? 1 : _g;\n  var st = source.style.transform || '';\n  var tt = target.style.transform || '';\n  // const st = source.style._transform || '';\n  // const tt = target.style._transform || '';\n  var sourceAnimation = source.animate([{\n    opacity: so,\n    transform: \"translate(0, 0) \".concat(st)\n  }, {\n    opacity: 0,\n    transform: \"translate(\".concat(mx, \", \").concat(my, \") \").concat(st)\n  }], __assign(__assign({\n    fill: 'both'\n  }, options), {\n    duration: delay + middle + offset\n  }));\n  var targetAnimation = target.animate([{\n    opacity: 0,\n    transform: \"translate(\".concat(-mx, \", \").concat(-my, \") \").concat(tt),\n    offset: 0.01\n  }, {\n    opacity: to,\n    transform: \"translate(0, 0) \".concat(tt)\n  }], __assign(__assign({\n    fill: 'both'\n  }, options), {\n    duration: middle + offset,\n    delay: delay + middle - offset\n  }));\n  onAnimateFinished(targetAnimation, afterTransition);\n  return [sourceAnimation, targetAnimation];\n}\n/**\n * execute transition animation on element\n * @description in the current stage, only support the following properties:\n * x, y, width, height, opacity, fill, stroke, lineWidth, radius\n * @param target element to be animated\n * @param state target properties or element\n * @param options transition options\n * @param animate whether to animate\n * @returns transition instance\n */\nexport function transition(target, state, options) {\n  var from = {};\n  var to = {};\n  Object.entries(state).forEach(function (_a) {\n    var _b = __read(_a, 2),\n      key = _b[0],\n      tarStyle = _b[1];\n    if (!isNil(tarStyle)) {\n      // 关闭 CSS 解析后，attr / getAttribute 只能获取到用户显式传入的属性，此时可以\n      // 获取解析值，如果仍获取不到（例如 x/y），则使用 0 作为默认值\n      var currStyle = target.style[key] || target.parsedStyle[key] || 0; // x/y\n      if (currStyle !== tarStyle) {\n        from[key] = currStyle;\n        to[key] = tarStyle;\n      }\n    }\n  });\n  if (!options) {\n    attr(target, to);\n    return null;\n  }\n  return animate(target, [from, to], __assign({\n    fill: 'both'\n  }, options));\n}", "map": {"version": 3, "names": ["isNil", "show", "hide", "isStandardAnimationOption", "option", "parseAnimationOption", "enter", "update", "exit", "keys", "baseOption", "Object", "fromEntries", "entries", "filter", "_a", "_b", "__read", "k", "includes", "map", "__assign", "onAnimateFinished", "animation", "callback", "finished", "then", "onAnimatesFinished", "animations", "length", "Promise", "all", "a", "attr", "target", "value", "animate", "keyframes", "options", "state", "slice", "style", "identicalTextNode", "source", "nodeName", "attributes", "text", "transitionShape", "after", "remove", "afterTransition", "destroy", "isVisible", "duration", "delay", "middle", "Math", "ceil", "offset", "_c", "getGeometryBounds", "center", "sx", "sy", "_d", "ex", "ey", "_e", "mx", "my", "_f", "opacity", "so", "_g", "to", "st", "transform", "tt", "sourceAnimation", "concat", "fill", "targetAnimation", "transition", "from", "for<PERSON>ach", "key", "tarStyle", "currStyle", "parsedStyle"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/animation/utils.ts"], "sourcesContent": ["/* global Keyframe */\nimport { isNil } from '@antv/util';\nimport type { DisplayObject } from '../shapes';\nimport type { Component } from '../core';\nimport { show, hide } from '../util';\nimport type { AnimationOption, AnimationResult, GenericAnimation, StandardAnimationOption } from './types';\n\nfunction isStandardAnimationOption(option: AnimationOption): option is StandardAnimationOption {\n  if (typeof option === 'boolean') return false;\n  return 'enter' in option && 'update' in option && 'exit' in option;\n}\n\nexport function parseAnimationOption(option: AnimationOption): StandardAnimationOption {\n  // option is false => all animation is false\n  // option is { enter: {}, update: {}, exit: {}, ...baseOption } =>\n  //    { enter: { ...enter, ...baseOption }, update: { ...update, ...baseOption }, exit: { ...exit, ...baseOption } }\n  // option is { enter: {}, update: {}, exit: {} } => option\n\n  if (!option) return { enter: false, update: false, exit: false };\n\n  const keys = ['enter', 'update', 'exit'] as const;\n  const baseOption = Object.fromEntries(Object.entries(option).filter(([k]) => !keys.includes(k as any)));\n\n  return Object.fromEntries(\n    keys.map((k) => {\n      if (isStandardAnimationOption(option)) {\n        if (option[k] === false) return [k, false];\n        return [k, { ...option[k], ...baseOption }];\n      }\n      return [k, baseOption];\n    })\n  );\n}\n\nexport function onAnimateFinished(animation: AnimationResult, callback: () => void) {\n  if (!animation) callback();\n  else animation.finished.then(callback);\n}\n\nexport function onAnimatesFinished(animations: AnimationResult[], callback: () => void) {\n  if (animations.length === 0) callback();\n  else Promise.all(animations.map((a) => a?.finished)).then(callback);\n}\n\nfunction attr(target: DisplayObject | Component<any>, value: Record<string, any>) {\n  if ('update' in target) target.update(value);\n  else target.attr(value);\n}\n\nexport function animate(target: DisplayObject | Component<any>, keyframes: Keyframe[], options: GenericAnimation) {\n  if (keyframes.length === 0) return null;\n  if (!options) {\n    const state = keyframes.slice(-1)[0];\n    attr(target, { style: state });\n    return null;\n  }\n  return target.animate(keyframes, options);\n}\n\nfunction identicalTextNode(source: DisplayObject, target: DisplayObject): boolean {\n  if (source.nodeName !== 'text' || target.nodeName !== 'text') return false;\n  if (source.attributes.text !== target.attributes.text) return false;\n  return true;\n}\n\n/**\n * transition source shape to target shape\n * @param source\n * @param target\n * @param options\n * @param after destroy or hide source shape after transition\n */\nexport function transitionShape(\n  source: DisplayObject,\n  target: DisplayObject,\n  options: GenericAnimation,\n  after: 'destroy' | 'hide' = 'destroy'\n) {\n  // If source and target are both text node and with same text,\n  // do not apply shape animation.\n  if (identicalTextNode(source, target)) {\n    source.remove();\n    return [null];\n  }\n\n  const afterTransition = () => {\n    if (after === 'destroy') source.destroy();\n    else if (after === 'hide') hide(source);\n    if (target.isVisible()) show(target);\n  };\n  if (!options) {\n    afterTransition();\n    return [null];\n  }\n  const { duration = 0, delay = 0 } = options;\n  const middle = Math.ceil(+duration / 2);\n  const offset = +duration / 4;\n\n  const {\n    center: [sx, sy],\n  } = source.getGeometryBounds();\n  const {\n    center: [ex, ey],\n  } = target.getGeometryBounds();\n  const [mx, my] = [(sx + ex) / 2 - sx, (sy + ey) / 2 - sy];\n\n  const { opacity: so = 1 } = source.style;\n  const { opacity: to = 1 } = target.style;\n\n  const st = source.style.transform || '';\n  const tt = target.style.transform || '';\n  // const st = source.style._transform || '';\n  // const tt = target.style._transform || '';\n\n  const sourceAnimation = source.animate(\n    [\n      { opacity: so, transform: `translate(0, 0) ${st}` },\n      { opacity: 0, transform: `translate(${mx}, ${my}) ${st}` },\n    ],\n    {\n      fill: 'both',\n      ...options,\n      duration: delay + middle + offset,\n    }\n  );\n  const targetAnimation = target.animate(\n    [\n      { opacity: 0, transform: `translate(${-mx}, ${-my}) ${tt}`, offset: 0.01 },\n      { opacity: to, transform: `translate(0, 0) ${tt}` },\n    ],\n    {\n      fill: 'both',\n      ...options,\n      duration: middle + offset,\n      delay: delay + middle - offset,\n    }\n  );\n\n  onAnimateFinished(targetAnimation, afterTransition);\n  return [sourceAnimation, targetAnimation];\n}\n\n/**\n * execute transition animation on element\n * @description in the current stage, only support the following properties:\n * x, y, width, height, opacity, fill, stroke, lineWidth, radius\n * @param target element to be animated\n * @param state target properties or element\n * @param options transition options\n * @param animate whether to animate\n * @returns transition instance\n */\nexport function transition(\n  target: DisplayObject | Component<any>,\n  state: Record<string, any> | (DisplayObject | Component<any>),\n  options: GenericAnimation\n) {\n  const from: typeof state = {};\n  const to: typeof state = {};\n  Object.entries(state).forEach(([key, tarStyle]) => {\n    if (!isNil(tarStyle)) {\n      // 关闭 CSS 解析后，attr / getAttribute 只能获取到用户显式传入的属性，此时可以\n      // 获取解析值，如果仍获取不到（例如 x/y），则使用 0 作为默认值\n      const currStyle = target.style[key] || target.parsedStyle[key] || 0; // x/y\n      if (currStyle !== tarStyle) {\n        from[key] = currStyle;\n        to[key] = tarStyle;\n      }\n    }\n  });\n\n  if (!options) {\n    attr(target, to);\n    return null;\n  }\n\n  return animate(target, [from, to], { fill: 'both', ...options });\n}\n"], "mappings": ";AAAA;AACA,SAASA,KAAK,QAAQ,YAAY;AAGlC,SAASC,IAAI,EAAEC,IAAI,QAAQ,SAAS;AAGpC,SAASC,yBAAyBA,CAACC,MAAuB;EACxD,IAAI,OAAOA,MAAM,KAAK,SAAS,EAAE,OAAO,KAAK;EAC7C,OAAO,OAAO,IAAIA,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAI,MAAM,IAAIA,MAAM;AACpE;AAEA,OAAM,SAAUC,oBAAoBA,CAACD,MAAuB;EAC1D;EACA;EACA;EACA;EAEA,IAAI,CAACA,MAAM,EAAE,OAAO;IAAEE,KAAK,EAAE,KAAK;IAAEC,MAAM,EAAE,KAAK;IAAEC,IAAI,EAAE;EAAK,CAAE;EAEhE,IAAMC,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAU;EACjD,IAAMC,UAAU,GAAGC,MAAM,CAACC,WAAW,CAACD,MAAM,CAACE,OAAO,CAACT,MAAM,CAAC,CAACU,MAAM,CAAC,UAACC,EAAG;QAAHC,EAAA,GAAAC,MAAA,CAAAF,EAAA,IAAG;MAAFG,CAAC,GAAAF,EAAA;IAAM,QAACP,IAAI,CAACU,QAAQ,CAACD,CAAQ,CAAC;EAAxB,CAAwB,CAAC,CAAC;EAEvG,OAAOP,MAAM,CAACC,WAAW,CACvBH,IAAI,CAACW,GAAG,CAAC,UAACF,CAAC;IACT,IAAIf,yBAAyB,CAACC,MAAM,CAAC,EAAE;MACrC,IAAIA,MAAM,CAACc,CAAC,CAAC,KAAK,KAAK,EAAE,OAAO,CAACA,CAAC,EAAE,KAAK,CAAC;MAC1C,OAAO,CAACA,CAAC,EAAAG,QAAA,CAAAA,QAAA,KAAOjB,MAAM,CAACc,CAAC,CAAC,GAAKR,UAAU,EAAG;IAC7C;IACA,OAAO,CAACQ,CAAC,EAAER,UAAU,CAAC;EACxB,CAAC,CAAC,CACH;AACH;AAEA,OAAM,SAAUY,iBAAiBA,CAACC,SAA0B,EAAEC,QAAoB;EAChF,IAAI,CAACD,SAAS,EAAEC,QAAQ,EAAE,CAAC,KACtBD,SAAS,CAACE,QAAQ,CAACC,IAAI,CAACF,QAAQ,CAAC;AACxC;AAEA,OAAM,SAAUG,kBAAkBA,CAACC,UAA6B,EAAEJ,QAAoB;EACpF,IAAII,UAAU,CAACC,MAAM,KAAK,CAAC,EAAEL,QAAQ,EAAE,CAAC,KACnCM,OAAO,CAACC,GAAG,CAACH,UAAU,CAACR,GAAG,CAAC,UAACY,CAAC;IAAK,OAAAA,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEP,QAAQ;EAAX,CAAW,CAAC,CAAC,CAACC,IAAI,CAACF,QAAQ,CAAC;AACrE;AAEA,SAASS,IAAIA,CAACC,MAAsC,EAAEC,KAA0B;EAC9E,IAAI,QAAQ,IAAID,MAAM,EAAEA,MAAM,CAAC3B,MAAM,CAAC4B,KAAK,CAAC,CAAC,KACxCD,MAAM,CAACD,IAAI,CAACE,KAAK,CAAC;AACzB;AAEA,OAAM,SAAUC,OAAOA,CAACF,MAAsC,EAAEG,SAAqB,EAAEC,OAAyB;EAC9G,IAAID,SAAS,CAACR,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EACvC,IAAI,CAACS,OAAO,EAAE;IACZ,IAAMC,KAAK,GAAGF,SAAS,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpCP,IAAI,CAACC,MAAM,EAAE;MAAEO,KAAK,EAAEF;IAAK,CAAE,CAAC;IAC9B,OAAO,IAAI;EACb;EACA,OAAOL,MAAM,CAACE,OAAO,CAACC,SAAS,EAAEC,OAAO,CAAC;AAC3C;AAEA,SAASI,iBAAiBA,CAACC,MAAqB,EAAET,MAAqB;EACrE,IAAIS,MAAM,CAACC,QAAQ,KAAK,MAAM,IAAIV,MAAM,CAACU,QAAQ,KAAK,MAAM,EAAE,OAAO,KAAK;EAC1E,IAAID,MAAM,CAACE,UAAU,CAACC,IAAI,KAAKZ,MAAM,CAACW,UAAU,CAACC,IAAI,EAAE,OAAO,KAAK;EACnE,OAAO,IAAI;AACb;AAEA;;;;;;;AAOA,OAAM,SAAUC,eAAeA,CAC7BJ,MAAqB,EACrBT,MAAqB,EACrBI,OAAyB,EACzBU,KAAqC;EAArC,IAAAA,KAAA;IAAAA,KAAA,YAAqC;EAAA;EAErC;EACA;EACA,IAAIN,iBAAiB,CAACC,MAAM,EAAET,MAAM,CAAC,EAAE;IACrCS,MAAM,CAACM,MAAM,EAAE;IACf,OAAO,CAAC,IAAI,CAAC;EACf;EAEA,IAAMC,eAAe,GAAG,SAAAA,CAAA;IACtB,IAAIF,KAAK,KAAK,SAAS,EAAEL,MAAM,CAACQ,OAAO,EAAE,CAAC,KACrC,IAAIH,KAAK,KAAK,MAAM,EAAE9C,IAAI,CAACyC,MAAM,CAAC;IACvC,IAAIT,MAAM,CAACkB,SAAS,EAAE,EAAEnD,IAAI,CAACiC,MAAM,CAAC;EACtC,CAAC;EACD,IAAI,CAACI,OAAO,EAAE;IACZY,eAAe,EAAE;IACjB,OAAO,CAAC,IAAI,CAAC;EACf;EACQ,IAAAnC,EAAA,GAA4BuB,OAAO,CAAAe,QAAvB;IAAZA,QAAQ,GAAAtC,EAAA,cAAG,CAAC,GAAAA,EAAA;IAAEC,EAAA,GAAcsB,OAAO,CAAAgB,KAAZ;IAATA,KAAK,GAAAtC,EAAA,cAAG,CAAC,GAAAA,EAAA;EAC/B,IAAMuC,MAAM,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,QAAQ,GAAG,CAAC,CAAC;EACvC,IAAMK,MAAM,GAAG,CAACL,QAAQ,GAAG,CAAC;EAG1B,IAAAM,EAAA,GAAA1C,MAAA,CACE0B,MAAM,CAACiB,iBAAiB,EAAE,CAAAC,MAAA,IADZ;IAAPC,EAAE,GAAAH,EAAA;IAAEI,EAAE,GAAAJ,EAAA,GAAC;EAGhB,IAAAK,EAAA,GAAA/C,MAAA,CACEiB,MAAM,CAAC0B,iBAAiB,EAAE,CAAAC,MAAA,IADZ;IAAPI,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAC;EAEZ,IAAAG,EAAA,GAAAlD,MAAA,CAAW,CAAC,CAAC6C,EAAE,GAAGG,EAAE,IAAI,CAAC,GAAGH,EAAE,EAAE,CAACC,EAAE,GAAGG,EAAE,IAAI,CAAC,GAAGH,EAAE,CAAC;IAAlDK,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAA4C;EAEjD,IAAAG,EAAA,GAAoB3B,MAAM,CAACF,KAAK,CAAA8B,OAAjB;IAANC,EAAE,GAAAF,EAAA,cAAG,CAAC,GAAAA,EAAA;EACf,IAAAG,EAAA,GAAoBvC,MAAM,CAACO,KAAK,CAAA8B,OAAjB;IAANG,EAAE,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;EAEvB,IAAME,EAAE,GAAGhC,MAAM,CAACF,KAAK,CAACmC,SAAS,IAAI,EAAE;EACvC,IAAMC,EAAE,GAAG3C,MAAM,CAACO,KAAK,CAACmC,SAAS,IAAI,EAAE;EACvC;EACA;EAEA,IAAME,eAAe,GAAGnC,MAAM,CAACP,OAAO,CACpC,CACE;IAAEmC,OAAO,EAAEC,EAAE;IAAEI,SAAS,EAAE,mBAAAG,MAAA,CAAmBJ,EAAE;EAAE,CAAE,EACnD;IAAEJ,OAAO,EAAE,CAAC;IAAEK,SAAS,EAAE,aAAAG,MAAA,CAAaX,EAAE,QAAAW,MAAA,CAAKV,EAAE,QAAAU,MAAA,CAAKJ,EAAE;EAAE,CAAE,CAC3D,EAAAtD,QAAA,CAAAA,QAAA;IAEC2D,IAAI,EAAE;EAAM,GACT1C,OAAO;IACVe,QAAQ,EAAEC,KAAK,GAAGC,MAAM,GAAGG;EAAM,GAEpC;EACD,IAAMuB,eAAe,GAAG/C,MAAM,CAACE,OAAO,CACpC,CACE;IAAEmC,OAAO,EAAE,CAAC;IAAEK,SAAS,EAAE,aAAAG,MAAA,CAAa,CAACX,EAAE,QAAAW,MAAA,CAAK,CAACV,EAAE,QAAAU,MAAA,CAAKF,EAAE,CAAE;IAAEnB,MAAM,EAAE;EAAI,CAAE,EAC1E;IAAEa,OAAO,EAAEG,EAAE;IAAEE,SAAS,EAAE,mBAAAG,MAAA,CAAmBF,EAAE;EAAE,CAAE,CACpD,EAAAxD,QAAA,CAAAA,QAAA;IAEC2D,IAAI,EAAE;EAAM,GACT1C,OAAO;IACVe,QAAQ,EAAEE,MAAM,GAAGG,MAAM;IACzBJ,KAAK,EAAEA,KAAK,GAAGC,MAAM,GAAGG;EAAM,GAEjC;EAEDpC,iBAAiB,CAAC2D,eAAe,EAAE/B,eAAe,CAAC;EACnD,OAAO,CAAC4B,eAAe,EAAEG,eAAe,CAAC;AAC3C;AAEA;;;;;;;;;;AAUA,OAAM,SAAUC,UAAUA,CACxBhD,MAAsC,EACtCK,KAA6D,EAC7DD,OAAyB;EAEzB,IAAM6C,IAAI,GAAiB,EAAE;EAC7B,IAAMT,EAAE,GAAiB,EAAE;EAC3B/D,MAAM,CAACE,OAAO,CAAC0B,KAAK,CAAC,CAAC6C,OAAO,CAAC,UAACrE,EAAe;QAAfC,EAAA,GAAAC,MAAA,CAAAF,EAAA,IAAe;MAAdsE,GAAG,GAAArE,EAAA;MAAEsE,QAAQ,GAAAtE,EAAA;IAC3C,IAAI,CAAChB,KAAK,CAACsF,QAAQ,CAAC,EAAE;MACpB;MACA;MACA,IAAMC,SAAS,GAAGrD,MAAM,CAACO,KAAK,CAAC4C,GAAG,CAAC,IAAInD,MAAM,CAACsD,WAAW,CAACH,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;MACrE,IAAIE,SAAS,KAAKD,QAAQ,EAAE;QAC1BH,IAAI,CAACE,GAAG,CAAC,GAAGE,SAAS;QACrBb,EAAE,CAACW,GAAG,CAAC,GAAGC,QAAQ;MACpB;IACF;EACF,CAAC,CAAC;EAEF,IAAI,CAAChD,OAAO,EAAE;IACZL,IAAI,CAACC,MAAM,EAAEwC,EAAE,CAAC;IAChB,OAAO,IAAI;EACb;EAEA,OAAOtC,OAAO,CAACF,MAAM,EAAE,CAACiD,IAAI,EAAET,EAAE,CAAC,EAAArD,QAAA;IAAI2D,IAAI,EAAE;EAAM,GAAK1C,OAAO,EAAG;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}