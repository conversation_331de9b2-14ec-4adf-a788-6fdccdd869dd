{"ast": null, "code": "import isArray from './is-array';\nimport isFunction from './is-function';\n/**\n * @param {Array} arr The array to iterate over.\n * @param {Function} [fn] The iteratee invoked per element.\n * @return {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nexport default (function (arr, fn) {\n  if (!isArray(arr)) {\n    return undefined;\n  }\n  var minItem;\n  var min = Infinity;\n  for (var i = 0; i < arr.length; i++) {\n    var item = arr[i];\n    var v = isFunction(fn) ? fn(item) : item[fn];\n    if (v < min) {\n      minItem = item;\n      min = v;\n    }\n  }\n  return minItem;\n});", "map": {"version": 3, "names": ["isArray", "isFunction", "arr", "fn", "undefined", "minItem", "min", "Infinity", "i", "length", "item", "v"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/min-by.ts"], "sourcesContent": ["import each from './each';\nimport isArray from './is-array';\nimport isFunction from './is-function';\n\n/**\n * @param {Array} arr The array to iterate over.\n * @param {Function} [fn] The iteratee invoked per element.\n * @return {*} Returns the minimum value.\n * @example\n *\n * var objects = [{ 'n': 1 }, { 'n': 2 }];\n *\n * minBy(objects, function(o) { return o.n; });\n * // => { 'n': 1 }\n *\n * minBy(objects, 'n');\n * // => { 'n': 1 }\n */\nexport default <T>(arr: T[], fn: ((v: T) => number) | string): T | undefined => {\n  if (!isArray(arr)) {\n    return undefined;\n  }\n\n  let minItem;\n  let min = Infinity;\n\n  for (let i = 0; i < arr.length; i++) {\n    const item = arr[i];\n    const v = isFunction(fn) ? fn(item) : item[fn];\n    \n    if (v < min) {\n      minItem = item;\n      min = v;\n    }\n  }\n\n  return minItem;\n};\n"], "mappings": "AACA,OAAOA,OAAO,MAAM,YAAY;AAChC,OAAOC,UAAU,MAAM,eAAe;AAEtC;;;;;;;;;;;;;;AAcA,gBAAe,UAAIC,GAAQ,EAAEC,EAA+B;EAC1D,IAAI,CAACH,OAAO,CAACE,GAAG,CAAC,EAAE;IACjB,OAAOE,SAAS;;EAGlB,IAAIC,OAAO;EACX,IAAIC,GAAG,GAAGC,QAAQ;EAElB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,GAAG,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,IAAME,IAAI,GAAGR,GAAG,CAACM,CAAC,CAAC;IACnB,IAAMG,CAAC,GAAGV,UAAU,CAACE,EAAE,CAAC,GAAGA,EAAE,CAACO,IAAI,CAAC,GAAGA,IAAI,CAACP,EAAE,CAAC;IAE9C,IAAIQ,CAAC,GAAGL,GAAG,EAAE;MACXD,OAAO,GAAGK,IAAI;MACdJ,GAAG,GAAGK,CAAC;;;EAIX,OAAON,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}