{"ast": null, "code": "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\nexport var phi = (1 + Math.sqrt(5)) / 2;\nexport function squarifyRatio(ratio, parent, x0, y0, x1, y1) {\n  var rows = [],\n    nodes = parent.children,\n    row,\n    nodeValue,\n    i0 = 0,\n    i1 = 0,\n    n = nodes.length,\n    dx,\n    dy,\n    value = parent.value,\n    sumValue,\n    minValue,\n    maxValue,\n    newRatio,\n    minRatio,\n    alpha,\n    beta;\n  while (i0 < n) {\n    dx = x1 - x0, dy = y1 - y0;\n\n    // Find the next non-empty node.\n    do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);\n    minValue = maxValue = sumValue;\n    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);\n    beta = sumValue * sumValue * alpha;\n    minRatio = Math.max(maxValue / beta, beta / minValue);\n\n    // Keep adding nodes while the aspect ratio maintains or improves.\n    for (; i1 < n; ++i1) {\n      sumValue += nodeValue = nodes[i1].value;\n      if (nodeValue < minValue) minValue = nodeValue;\n      if (nodeValue > maxValue) maxValue = nodeValue;\n      beta = sumValue * sumValue * alpha;\n      newRatio = Math.max(maxValue / beta, beta / minValue);\n      if (newRatio > minRatio) {\n        sumValue -= nodeValue;\n        break;\n      }\n      minRatio = newRatio;\n    }\n\n    // Position and record the row orientation.\n    rows.push(row = {\n      value: sumValue,\n      dice: dx < dy,\n      children: nodes.slice(i0, i1)\n    });\n    if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);\n    value -= sumValue, i0 = i1;\n  }\n  return rows;\n}\nexport default (function custom(ratio) {\n  function squarify(parent, x0, y0, x1, y1) {\n    squarifyRatio(ratio, parent, x0, y0, x1, y1);\n  }\n  squarify.ratio = function (x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n  return squarify;\n})(phi);", "map": {"version": 3, "names": ["treemapDice", "treemapSlice", "phi", "Math", "sqrt", "squarifyRatio", "ratio", "parent", "x0", "y0", "x1", "y1", "rows", "nodes", "children", "row", "nodeValue", "i0", "i1", "n", "length", "dx", "dy", "value", "sumValue", "minValue", "maxValue", "newRatio", "minRatio", "alpha", "beta", "max", "push", "dice", "slice", "custom", "squarify", "x"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/treemap/squarify.js"], "sourcesContent": ["import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\n\nexport var phi = (1 + Math.sqrt(5)) / 2;\n\nexport function squarifyRatio(ratio, parent, x0, y0, x1, y1) {\n  var rows = [],\n      nodes = parent.children,\n      row,\n      nodeValue,\n      i0 = 0,\n      i1 = 0,\n      n = nodes.length,\n      dx, dy,\n      value = parent.value,\n      sumValue,\n      minValue,\n      maxValue,\n      newRatio,\n      minRatio,\n      alpha,\n      beta;\n\n  while (i0 < n) {\n    dx = x1 - x0, dy = y1 - y0;\n\n    // Find the next non-empty node.\n    do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);\n    minValue = maxValue = sumValue;\n    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);\n    beta = sumValue * sumValue * alpha;\n    minRatio = Math.max(maxValue / beta, beta / minValue);\n\n    // Keep adding nodes while the aspect ratio maintains or improves.\n    for (; i1 < n; ++i1) {\n      sumValue += nodeValue = nodes[i1].value;\n      if (nodeValue < minValue) minValue = nodeValue;\n      if (nodeValue > maxValue) maxValue = nodeValue;\n      beta = sumValue * sumValue * alpha;\n      newRatio = Math.max(maxValue / beta, beta / minValue);\n      if (newRatio > minRatio) { sumValue -= nodeValue; break; }\n      minRatio = newRatio;\n    }\n\n    // Position and record the row orientation.\n    rows.push(row = {value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1)});\n    if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);\n    else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);\n    value -= sumValue, i0 = i1;\n  }\n\n  return rows;\n}\n\nexport default (function custom(ratio) {\n\n  function squarify(parent, x0, y0, x1, y1) {\n    squarifyRatio(ratio, parent, x0, y0, x1, y1);\n  }\n\n  squarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return squarify;\n})(phi);\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,WAAW;AACnC,OAAOC,YAAY,MAAM,YAAY;AAErC,OAAO,IAAIC,GAAG,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAEvC,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAC3D,IAAIC,IAAI,GAAG,EAAE;IACTC,KAAK,GAAGN,MAAM,CAACO,QAAQ;IACvBC,GAAG;IACHC,SAAS;IACTC,EAAE,GAAG,CAAC;IACNC,EAAE,GAAG,CAAC;IACNC,CAAC,GAAGN,KAAK,CAACO,MAAM;IAChBC,EAAE;IAAEC,EAAE;IACNC,KAAK,GAAGhB,MAAM,CAACgB,KAAK;IACpBC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,QAAQ;IACRC,KAAK;IACLC,IAAI;EAER,OAAOb,EAAE,GAAGE,CAAC,EAAE;IACbE,EAAE,GAAGX,EAAE,GAAGF,EAAE,EAAEc,EAAE,GAAGX,EAAE,GAAGF,EAAE;;IAE1B;IACA,GAAGe,QAAQ,GAAGX,KAAK,CAACK,EAAE,EAAE,CAAC,CAACK,KAAK,CAAC,QAAQ,CAACC,QAAQ,IAAIN,EAAE,GAAGC,CAAC;IAC3DM,QAAQ,GAAGC,QAAQ,GAAGF,QAAQ;IAC9BK,KAAK,GAAG1B,IAAI,CAAC4B,GAAG,CAACT,EAAE,GAAGD,EAAE,EAAEA,EAAE,GAAGC,EAAE,CAAC,IAAIC,KAAK,GAAGjB,KAAK,CAAC;IACpDwB,IAAI,GAAGN,QAAQ,GAAGA,QAAQ,GAAGK,KAAK;IAClCD,QAAQ,GAAGzB,IAAI,CAAC4B,GAAG,CAACL,QAAQ,GAAGI,IAAI,EAAEA,IAAI,GAAGL,QAAQ,CAAC;;IAErD;IACA,OAAOP,EAAE,GAAGC,CAAC,EAAE,EAAED,EAAE,EAAE;MACnBM,QAAQ,IAAIR,SAAS,GAAGH,KAAK,CAACK,EAAE,CAAC,CAACK,KAAK;MACvC,IAAIP,SAAS,GAAGS,QAAQ,EAAEA,QAAQ,GAAGT,SAAS;MAC9C,IAAIA,SAAS,GAAGU,QAAQ,EAAEA,QAAQ,GAAGV,SAAS;MAC9Cc,IAAI,GAAGN,QAAQ,GAAGA,QAAQ,GAAGK,KAAK;MAClCF,QAAQ,GAAGxB,IAAI,CAAC4B,GAAG,CAACL,QAAQ,GAAGI,IAAI,EAAEA,IAAI,GAAGL,QAAQ,CAAC;MACrD,IAAIE,QAAQ,GAAGC,QAAQ,EAAE;QAAEJ,QAAQ,IAAIR,SAAS;QAAE;MAAO;MACzDY,QAAQ,GAAGD,QAAQ;IACrB;;IAEA;IACAf,IAAI,CAACoB,IAAI,CAACjB,GAAG,GAAG;MAACQ,KAAK,EAAEC,QAAQ;MAAES,IAAI,EAAEZ,EAAE,GAAGC,EAAE;MAAER,QAAQ,EAAED,KAAK,CAACqB,KAAK,CAACjB,EAAE,EAAEC,EAAE;IAAC,CAAC,CAAC;IAChF,IAAIH,GAAG,CAACkB,IAAI,EAAEjC,WAAW,CAACe,GAAG,EAAEP,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEa,KAAK,GAAGd,EAAE,IAAIa,EAAE,GAAGE,QAAQ,GAAGD,KAAK,GAAGZ,EAAE,CAAC,CAAC,KAChFV,YAAY,CAACc,GAAG,EAAEP,EAAE,EAAEC,EAAE,EAAEc,KAAK,GAAGf,EAAE,IAAIa,EAAE,GAAGG,QAAQ,GAAGD,KAAK,GAAGb,EAAE,EAAEC,EAAE,CAAC;IAC5EY,KAAK,IAAIC,QAAQ,EAAEP,EAAE,GAAGC,EAAE;EAC5B;EAEA,OAAON,IAAI;AACb;AAEA,eAAe,CAAC,SAASuB,MAAMA,CAAC7B,KAAK,EAAE;EAErC,SAAS8B,QAAQA,CAAC7B,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;IACxCN,aAAa,CAACC,KAAK,EAAEC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;EAC9C;EAEAyB,QAAQ,CAAC9B,KAAK,GAAG,UAAS+B,CAAC,EAAE;IAC3B,OAAOF,MAAM,CAAC,CAACE,CAAC,GAAG,CAACA,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC;EACrC,CAAC;EAED,OAAOD,QAAQ;AACjB,CAAC,EAAElC,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}