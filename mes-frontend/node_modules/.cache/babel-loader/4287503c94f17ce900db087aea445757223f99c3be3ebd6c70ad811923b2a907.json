{"ast": null, "code": "export default function uniq(arr, cache) {\n  if (cache === void 0) {\n    cache = new Map();\n  }\n  var r = [];\n  if (Array.isArray(arr)) {\n    for (var i = 0, len = arr.length; i < len; i++) {\n      var item = arr[i];\n      // 加一个 cache，提升性能\n      if (!cache.has(item)) {\n        r.push(item);\n        cache.set(item, true);\n      }\n    }\n  }\n  return r;\n}", "map": {"version": 3, "names": ["uniq", "arr", "cache", "Map", "r", "Array", "isArray", "i", "len", "length", "item", "has", "push", "set"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/uniq.ts"], "sourcesContent": ["export default function uniq(arr: any[], cache = new Map()) {\n  const r = [];\n\n  if (Array.isArray(arr)) {\n    for (let i = 0, len = arr.length; i < len; i ++) {\n      const item = arr[i];\n      // 加一个 cache，提升性能\n      if (!cache.has(item)) {\n        r.push(item);\n        cache.set(item, true);\n      }\n    }\n  }\n  return r;\n}\n"], "mappings": "AAAA,eAAc,SAAUA,IAAIA,CAACC,GAAU,EAAEC,KAAiB;EAAjB,IAAAA,KAAA;IAAAA,KAAA,OAAYC,GAAG,EAAE;EAAA;EACxD,IAAMC,CAAC,GAAG,EAAE;EAEZ,IAAIC,KAAK,CAACC,OAAO,CAACL,GAAG,CAAC,EAAE;IACtB,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGP,GAAG,CAACQ,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAG,EAAE;MAC/C,IAAMG,IAAI,GAAGT,GAAG,CAACM,CAAC,CAAC;MACnB;MACA,IAAI,CAACL,KAAK,CAACS,GAAG,CAACD,IAAI,CAAC,EAAE;QACpBN,CAAC,CAACQ,IAAI,CAACF,IAAI,CAAC;QACZR,KAAK,CAACW,GAAG,CAACH,IAAI,EAAE,IAAI,CAAC;;;;EAI3B,OAAON,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}