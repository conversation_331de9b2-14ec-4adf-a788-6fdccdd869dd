{"ast": null, "code": "import isArray from './is-array';\nimport { default as getMax } from './max';\nimport { default as getMin } from './min';\nvar getRange = function (values) {\n  // 存在 NaN 时，min,max 判定会出问题\n  var filterValues = values.filter(function (v) {\n    return !isNaN(v);\n  });\n  if (!filterValues.length) {\n    // 如果没有数值则直接返回0\n    return {\n      min: 0,\n      max: 0\n    };\n  }\n  if (isArray(values[0])) {\n    var tmp = [];\n    for (var i = 0; i < values.length; i++) {\n      tmp = tmp.concat(values[i]);\n    }\n    filterValues = tmp;\n  }\n  var max = getMax(filterValues);\n  var min = getMin(filterValues);\n  return {\n    min: min,\n    max: max\n  };\n};\nexport default getRange;", "map": {"version": 3, "names": ["isArray", "default", "getMax", "getMin", "getRange", "values", "filterValues", "filter", "v", "isNaN", "length", "min", "max", "tmp", "i", "concat"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/get-range.ts"], "sourcesContent": ["import isArray from './is-array';\nimport { default as getMax } from './max';\nimport { default as getMin } from './min';\n\nexport interface RangeType {\n  readonly min: number;\n  readonly max: number;\n}\n\nconst getRange = function (values: number[]): RangeType {\n  // 存在 NaN 时，min,max 判定会出问题\n  let filterValues = values.filter((v) => !isNaN(v));\n  if (!filterValues.length) {\n    // 如果没有数值则直接返回0\n    return {\n      min: 0,\n      max: 0,\n    };\n  }\n  if (isArray(values[0])) {\n    let tmp = [];\n    for (let i = 0; i < values.length; i++) {\n      tmp = tmp.concat(values[i]);\n    }\n    filterValues = tmp;\n  }\n  const max = getMax(filterValues);\n  const min = getMin(filterValues);\n  return {\n    min,\n    max,\n  };\n};\n\nexport default getRange;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,YAAY;AAChC,SAASC,OAAO,IAAIC,MAAM,QAAQ,OAAO;AACzC,SAASD,OAAO,IAAIE,MAAM,QAAQ,OAAO;AAOzC,IAAMC,QAAQ,GAAG,SAAAA,CAAUC,MAAgB;EACzC;EACA,IAAIC,YAAY,GAAGD,MAAM,CAACE,MAAM,CAAC,UAACC,CAAC;IAAK,QAACC,KAAK,CAACD,CAAC,CAAC;EAAT,CAAS,CAAC;EAClD,IAAI,CAACF,YAAY,CAACI,MAAM,EAAE;IACxB;IACA,OAAO;MACLC,GAAG,EAAE,CAAC;MACNC,GAAG,EAAE;KACN;;EAEH,IAAIZ,OAAO,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IACtB,IAAIQ,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACK,MAAM,EAAEI,CAAC,EAAE,EAAE;MACtCD,GAAG,GAAGA,GAAG,CAACE,MAAM,CAACV,MAAM,CAACS,CAAC,CAAC,CAAC;;IAE7BR,YAAY,GAAGO,GAAG;;EAEpB,IAAMD,GAAG,GAAGV,MAAM,CAACI,YAAY,CAAC;EAChC,IAAMK,GAAG,GAAGR,MAAM,CAACG,YAAY,CAAC;EAChC,OAAO;IACLK,GAAG,EAAAA,GAAA;IACHC,GAAG,EAAAA;GACJ;AACH,CAAC;AAED,eAAeR,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}