{"ast": null, "code": "export { DisplayObject } from '@antv/g';", "map": {"version": 3, "names": ["DisplayObject"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/DisplayObject.ts"], "sourcesContent": ["export { DisplayObject } from '@antv/g';\nexport type { BaseStyleProps, DisplayObjectConfig } from '@antv/g';\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}