{"ast": null, "code": "function findIndex(arr, predicate, fromIndex) {\n  if (fromIndex === void 0) {\n    fromIndex = 0;\n  }\n  for (var i = fromIndex; i < arr.length; i++) {\n    if (predicate(arr[i], i)) {\n      // 找到终止循环\n      return i;\n    }\n  }\n  return -1;\n}\nexport default findIndex;", "map": {"version": 3, "names": ["findIndex", "arr", "predicate", "fromIndex", "i", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/find-index.ts"], "sourcesContent": ["function findIndex<T>(arr: T[], predicate: (item: T, idx?: number) => boolean, fromIndex: number = 0): number {\n  for (let i = fromIndex; i < arr.length; i ++) {\n    if (predicate(arr[i], i)) {\n      // 找到终止循环\n      return i;\n    }\n  }\n\n  return -1;\n}\n\nexport default findIndex;\n"], "mappings": "AAAA,SAASA,SAASA,CAAIC,GAAQ,EAAEC,SAA6C,EAAEC,SAAqB;EAArB,IAAAA,SAAA;IAAAA,SAAA,IAAqB;EAAA;EAClG,KAAK,IAAIC,CAAC,GAAGD,SAAS,EAAEC,CAAC,GAAGH,GAAG,CAACI,MAAM,EAAED,CAAC,EAAG,EAAE;IAC5C,IAAIF,SAAS,CAACD,GAAG,CAACG,CAAC,CAAC,EAAEA,CAAC,CAAC,EAAE;MACxB;MACA,OAAOA,CAAC;;;EAIZ,OAAO,CAAC,CAAC;AACX;AAEA,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}