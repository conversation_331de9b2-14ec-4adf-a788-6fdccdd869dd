{"ast": null, "code": "export var DEFAULT_INDICATOR_STYLE_PROPS = {\n  backgroundFill: '#262626',\n  backgroundLineCap: 'round',\n  backgroundLineWidth: 1,\n  backgroundStroke: '#333',\n  backgroundZIndex: -1,\n  formatter: function (val) {\n    return val.toString();\n  },\n  labelFill: '#fff',\n  labelFontSize: 12,\n  labelTextBaseline: 'middle',\n  padding: [2, 4],\n  position: 'right',\n  radius: 0,\n  zIndex: 999\n};", "map": {"version": 3, "names": ["DEFAULT_INDICATOR_STYLE_PROPS", "backgroundFill", "backgroundLineCap", "background<PERSON>ine<PERSON><PERSON><PERSON>", "backgroundStroke", "backgroundZIndex", "formatter", "val", "toString", "labelFill", "labelFontSize", "labelTextBaseline", "padding", "position", "radius", "zIndex"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/indicator/constant.ts"], "sourcesContent": ["import type { IndicatorStyleProps } from './types';\n\nexport const DEFAULT_INDICATOR_STYLE_PROPS: Partial<IndicatorStyleProps> = {\n  backgroundFill: '#262626',\n  backgroundLineCap: 'round',\n  backgroundLineWidth: 1,\n  backgroundStroke: '#333',\n  backgroundZIndex: -1,\n  formatter: (val) => val.toString(),\n  labelFill: '#fff',\n  labelFontSize: 12,\n  labelTextBaseline: 'middle',\n  padding: [2, 4],\n  position: 'right',\n  radius: 0,\n  zIndex: 999,\n};\n"], "mappings": "AAEA,OAAO,IAAMA,6BAA6B,GAAiC;EACzEC,cAAc,EAAE,SAAS;EACzBC,iBAAiB,EAAE,OAAO;EAC1BC,mBAAmB,EAAE,CAAC;EACtBC,gBAAgB,EAAE,MAAM;EACxBC,gBAAgB,EAAE,CAAC,CAAC;EACpBC,SAAS,EAAE,SAAAA,CAACC,GAAG;IAAK,OAAAA,GAAG,CAACC,QAAQ,EAAE;EAAd,CAAc;EAClCC,SAAS,EAAE,MAAM;EACjBC,aAAa,EAAE,EAAE;EACjBC,iBAAiB,EAAE,QAAQ;EAC3BC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACfC,QAAQ,EAAE,OAAO;EACjBC,MAAM,EAAE,CAAC;EACTC,MAAM,EAAE;CACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}