{"ast": null, "code": "export { Line } from '@antv/g';", "map": {"version": 3, "names": ["Line"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/shapes/Line.ts"], "sourcesContent": ["import type { LineStyleProps as GLineStyleProps } from '@antv/g';\nimport { OmitConflictStyleProps } from './types';\n\nexport { Line } from '@antv/g';\nexport type LineStyleProps = OmitConflictStyleProps<GLineStyleProps>;\n"], "mappings": "AAGA,SAASA,IAAI,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}