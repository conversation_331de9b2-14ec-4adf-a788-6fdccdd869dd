{"ast": null, "code": "export default function requestAnimationFrame(fn) {\n  var method = window.requestAnimationFrame || window.webkitRequestAnimationFrame ||\n  // @ts-ignore\n  window.mozRequestAnimationFrame ||\n  // @ts-ignore\n  window.msRequestAnimationFrame || function (f) {\n    return setTimeout(f, 16);\n  };\n  return method(fn);\n}\n;", "map": {"version": 3, "names": ["requestAnimationFrame", "fn", "method", "window", "webkitRequestAnimationFrame", "mozRequestAnimationFrame", "msRequestAnimationFrame", "f", "setTimeout"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/request-animation-frame.ts"], "sourcesContent": ["export default function requestAnimationFrame(fn: FrameRequestCallback) {\n  const method = window.requestAnimationFrame ||\n    window.webkitRequestAnimationFrame ||\n    // @ts-ignore\n    window.mozRequestAnimationFrame ||\n    // @ts-ignore\n    window.msRequestAnimationFrame ||\n    function(f) {\n      return setTimeout(f, 16);\n    };\n\n  return method(fn);\n};\n"], "mappings": "AAAA,eAAc,SAAUA,qBAAqBA,CAACC,EAAwB;EACpE,IAAMC,MAAM,GAAGC,MAAM,CAACH,qBAAqB,IACzCG,MAAM,CAACC,2BAA2B;EAClC;EACAD,MAAM,CAACE,wBAAwB;EAC/B;EACAF,MAAM,CAACG,uBAAuB,IAC9B,UAASC,CAAC;IACR,OAAOC,UAAU,CAACD,CAAC,EAAE,EAAE,CAAC;EAC1B,CAAC;EAEH,OAAOL,MAAM,CAACD,EAAE,CAAC;AACnB;AAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}