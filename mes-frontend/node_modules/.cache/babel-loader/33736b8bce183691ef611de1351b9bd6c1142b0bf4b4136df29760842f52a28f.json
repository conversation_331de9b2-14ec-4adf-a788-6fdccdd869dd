{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"../lodash\");\nmodule.exports = initOrder;\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nfunction initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function (v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(_.map(simpleNodes, function (v) {\n    return g.node(v).rank;\n  }));\n  var layers = _.map(_.range(maxRank + 1), function () {\n    return [];\n  });\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n  var orderedVs = _.sortBy(simpleNodes, function (v) {\n    return g.node(v).rank;\n  });\n  _.forEach(orderedVs, dfs);\n  return layers;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "initOrder", "g", "visited", "simpleNodes", "filter", "nodes", "v", "children", "length", "maxRank", "max", "map", "node", "rank", "layers", "range", "dfs", "has", "push", "for<PERSON>ach", "successors", "orderedVs", "sortBy"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/order/init-order.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"../lodash\");\n\nmodule.exports = initOrder;\n\n/*\n * Assigns an initial order value for each node by performing a DFS search\n * starting from nodes in the first rank. Nodes are assigned an order in their\n * rank as they are first visited.\n *\n * This approach comes from <PERSON><PERSON><PERSON>, et al., \"A Technique for Drawing Directed\n * Graphs.\"\n *\n * Returns a layering matrix with an array per layer and each layer sorted by\n * the order of its nodes.\n */\nfunction initOrder(g) {\n  var visited = {};\n  var simpleNodes = _.filter(g.nodes(), function(v) {\n    return !g.children(v).length;\n  });\n  var maxRank = _.max(_.map(simpleNodes, function(v) { return g.node(v).rank; }));\n  var layers = _.map(_.range(maxRank + 1), function() { return []; });\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    var node = g.node(v);\n    layers[node.rank].push(v);\n    _.forEach(g.successors(v), dfs);\n  }\n\n  var orderedVs = _.sortBy(simpleNodes, function(v) { return g.node(v).rank; });\n  _.forEach(orderedVs, dfs);\n\n  return layers;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,SAAS;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,CAAC,EAAE;EACpB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,WAAW,GAAGP,CAAC,CAACQ,MAAM,CAACH,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAChD,OAAO,CAACL,CAAC,CAACM,QAAQ,CAACD,CAAC,CAAC,CAACE,MAAM;EAC9B,CAAC,CAAC;EACF,IAAIC,OAAO,GAAGb,CAAC,CAACc,GAAG,CAACd,CAAC,CAACe,GAAG,CAACR,WAAW,EAAE,UAASG,CAAC,EAAE;IAAE,OAAOL,CAAC,CAACW,IAAI,CAACN,CAAC,CAAC,CAACO,IAAI;EAAE,CAAC,CAAC,CAAC;EAC/E,IAAIC,MAAM,GAAGlB,CAAC,CAACe,GAAG,CAACf,CAAC,CAACmB,KAAK,CAACN,OAAO,GAAG,CAAC,CAAC,EAAE,YAAW;IAAE,OAAO,EAAE;EAAE,CAAC,CAAC;EAEnE,SAASO,GAAGA,CAACV,CAAC,EAAE;IACd,IAAIV,CAAC,CAACqB,GAAG,CAACf,OAAO,EAAEI,CAAC,CAAC,EAAE;IACvBJ,OAAO,CAACI,CAAC,CAAC,GAAG,IAAI;IACjB,IAAIM,IAAI,GAAGX,CAAC,CAACW,IAAI,CAACN,CAAC,CAAC;IACpBQ,MAAM,CAACF,IAAI,CAACC,IAAI,CAAC,CAACK,IAAI,CAACZ,CAAC,CAAC;IACzBV,CAAC,CAACuB,OAAO,CAAClB,CAAC,CAACmB,UAAU,CAACd,CAAC,CAAC,EAAEU,GAAG,CAAC;EACjC;EAEA,IAAIK,SAAS,GAAGzB,CAAC,CAAC0B,MAAM,CAACnB,WAAW,EAAE,UAASG,CAAC,EAAE;IAAE,OAAOL,CAAC,CAACW,IAAI,CAACN,CAAC,CAAC,CAACO,IAAI;EAAE,CAAC,CAAC;EAC7EjB,CAAC,CAACuB,OAAO,CAACE,SAAS,EAAEL,GAAG,CAAC;EAEzB,OAAOF,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}