{"ast": null, "code": "export default function (node, x0, y0, z0, x1, y1, z1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.z0 = z0;\n  this.x1 = x1;\n  this.y1 = y1;\n  this.z1 = z1;\n}", "map": {"version": 3, "names": ["node", "x0", "y0", "z0", "x1", "y1", "z1"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-octree/src/octant.js"], "sourcesContent": ["export default function(node, x0, y0, z0, x1, y1, z1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.z0 = z0;\n  this.x1 = x1;\n  this.y1 = y1;\n  this.z1 = z1;\n}\n"], "mappings": "AAAA,eAAe,UAASA,IAAI,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;EACpD,IAAI,CAACN,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACC,EAAE,GAAGA,EAAE;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}