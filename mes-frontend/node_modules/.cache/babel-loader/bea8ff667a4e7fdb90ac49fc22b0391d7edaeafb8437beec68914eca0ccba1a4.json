{"ast": null, "code": "export default function (d) {\n  if (isNaN(x = +this._x.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n    node = this._root,\n    retainer,\n    previous,\n    next,\n    x0 = this._x0,\n    x1 = this._x1,\n    x,\n    xm,\n    right,\n    i,\n    j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (!(parent = node, node = node[i = +right])) return this;\n    if (!node.length) break;\n    if (parent[i + 1 & 1]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return next ? previous.next = next : delete previous.next, this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1]) && node === (parent[1] || parent[0]) && !node.length) {\n    if (retainer) retainer[j] = node;else this._root = node;\n  }\n  return this;\n}\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}", "map": {"version": 3, "names": ["d", "isNaN", "x", "_x", "call", "parent", "node", "_root", "retainer", "previous", "next", "x0", "_x0", "x1", "_x1", "xm", "right", "i", "j", "length", "data", "removeAll", "n", "remove"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-binarytree/src/remove.js"], "sourcesContent": ["export default function(d) {\n  if (isNaN(x = +this._x.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n      node = this._root,\n      retainer,\n      previous,\n      next,\n      x0 = this._x0,\n      x1 = this._x1,\n      x,\n      xm,\n      right,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (!(parent = node, node = node[i = +right])) return this;\n    if (!node.length) break;\n    if (parent[(i + 1) & 1]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return (next ? previous.next = next : delete previous.next), this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1])\n      && node === (parent[1] || parent[0])\n      && !node.length) {\n    if (retainer) retainer[j] = node;\n    else this._root = node;\n  }\n\n  return this;\n}\n\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,IAAIC,KAAK,CAACC,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAEpD,IAAIK,MAAM;IACNC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,QAAQ;IACRC,QAAQ;IACRC,IAAI;IACJC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbZ,CAAC;IACDa,EAAE;IACFC,KAAK;IACLC,CAAC;IACDC,CAAC;;EAEL;EACA,IAAI,CAACZ,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA;EACA,IAAIA,IAAI,CAACa,MAAM,EAAE,OAAO,IAAI,EAAE;IAC5B,IAAIH,KAAK,GAAGd,CAAC,KAAKa,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC,CAAC,EAAEF,EAAE,GAAGI,EAAE,CAAC,KAAMF,EAAE,GAAGE,EAAE;IAC5D,IAAI,EAAEV,MAAM,GAAGC,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACW,CAAC,GAAG,CAACD,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IAC1D,IAAI,CAACV,IAAI,CAACa,MAAM,EAAE;IAClB,IAAId,MAAM,CAAEY,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,EAAET,QAAQ,GAAGH,MAAM,EAAEa,CAAC,GAAGD,CAAC;EACnD;;EAEA;EACA,OAAOX,IAAI,CAACc,IAAI,KAAKpB,CAAC,EAAE,IAAI,EAAES,QAAQ,GAAGH,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACI,IAAI,CAAC,EAAE,OAAO,IAAI;EAC7E,IAAIA,IAAI,GAAGJ,IAAI,CAACI,IAAI,EAAE,OAAOJ,IAAI,CAACI,IAAI;;EAEtC;EACA,IAAID,QAAQ,EAAE,OAAQC,IAAI,GAAGD,QAAQ,CAACC,IAAI,GAAGA,IAAI,GAAG,OAAOD,QAAQ,CAACC,IAAI,EAAG,IAAI;;EAE/E;EACA,IAAI,CAACL,MAAM,EAAE,OAAO,IAAI,CAACE,KAAK,GAAGG,IAAI,EAAE,IAAI;;EAE3C;EACAA,IAAI,GAAGL,MAAM,CAACY,CAAC,CAAC,GAAGP,IAAI,GAAG,OAAOL,MAAM,CAACY,CAAC,CAAC;;EAE1C;EACA,IAAI,CAACX,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,KAC3BC,IAAI,MAAMD,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAAC,IACjC,CAACC,IAAI,CAACa,MAAM,EAAE;IACnB,IAAIX,QAAQ,EAAEA,QAAQ,CAACU,CAAC,CAAC,GAAGZ,IAAI,CAAC,KAC5B,IAAI,CAACC,KAAK,GAAGD,IAAI;EACxB;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAASe,SAASA,CAACD,IAAI,EAAE;EAC9B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAGF,IAAI,CAACD,MAAM,EAAEF,CAAC,GAAGK,CAAC,EAAE,EAAEL,CAAC,EAAE,IAAI,CAACM,MAAM,CAACH,IAAI,CAACH,CAAC,CAAC,CAAC;EACjE,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}