{"ast": null, "code": "import { __assign, __extends, __read, __spreadArray } from \"tslib\";\nimport { CustomEvent } from '@antv/g';\nimport { noop, set } from '@antv/util';\nimport { Component } from '../../../core';\nimport { Group } from '../../../shapes';\nimport { classNames, deepAssign, getCallbackValue, groupBy, select, subStyleProps } from '../../../util';\nimport { Navigator } from '../../navigator';\nimport { ifHorizontal } from '../utils';\nimport { CategoryItem } from './item';\nvar CLASS_NAMES = classNames({\n  page: 'item-page',\n  navigator: 'navigator',\n  item: 'item'\n}, 'items');\n/**\n * if value exists, it need to follow rule, otherwise, return default value\n * @param value\n * @param rule\n * @param defaultValue\n * @returns\n */\nvar ifSatisfied = function (value, rule, defaultValue) {\n  if (defaultValue === void 0) {\n    defaultValue = true;\n  }\n  if (value) {\n    return rule(value);\n  }\n  return defaultValue;\n};\nvar CategoryItems = /** @class */function (_super) {\n  __extends(CategoryItems, _super);\n  function CategoryItems(options) {\n    var _this = _super.call(this, options, {\n      data: [],\n      gridRow: Infinity,\n      gridCol: undefined,\n      padding: 0,\n      width: 1000,\n      height: 100,\n      rowPadding: 0,\n      colPadding: 0,\n      layout: 'flex',\n      orientation: 'horizontal',\n      click: noop,\n      mouseenter: noop,\n      mouseleave: noop\n    }) || this;\n    _this.navigatorShape = [0, 0];\n    return _this;\n  }\n  Object.defineProperty(CategoryItems.prototype, \"pageViews\", {\n    get: function () {\n      return this.navigator.getContainer();\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(CategoryItems.prototype, \"grid\", {\n    get: function () {\n      var _a = this.attributes,\n        gridRow = _a.gridRow,\n        gridCol = _a.gridCol,\n        data = _a.data;\n      if (!gridRow && !gridCol) throw new Error('gridRow and gridCol can not be set null at the same time');\n      if (!!gridRow && !!gridCol) return [gridRow, gridCol];\n      if (gridRow) return [gridRow, data.length];\n      return [data.length, gridCol]; // !!gridCol\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Object.defineProperty(CategoryItems.prototype, \"renderData\", {\n    get: function () {\n      var _a = this.attributes,\n        data = _a.data,\n        layout = _a.layout;\n      var style = subStyleProps(this.attributes, 'item');\n      var d = data.map(function (datum, index) {\n        var _a = datum.id,\n          id = _a === void 0 ? index : _a,\n          labelText = datum.label,\n          valueText = datum.value;\n        return {\n          id: \"\".concat(id),\n          index: index,\n          style: __assign({\n            layout: layout,\n            labelText: labelText,\n            valueText: valueText\n          }, Object.fromEntries(Object.entries(style).map(function (_a) {\n            var _b = __read(_a, 2),\n              key = _b[0],\n              val = _b[1];\n            return [key, getCallbackValue(val, [datum, index, data])];\n          })))\n        };\n      });\n      return d;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  CategoryItems.prototype.getGridLayout = function () {\n    var _this = this;\n    var _a = this.attributes,\n      orientation = _a.orientation,\n      width = _a.width,\n      rowPadding = _a.rowPadding,\n      colPadding = _a.colPadding;\n    var _b = __read(this.navigatorShape, 1),\n      navWidth = _b[0];\n    var _c = __read(this.grid, 2),\n      gridRow = _c[0],\n      gridCol = _c[1];\n    var pageSize = gridCol * gridRow;\n    var prevOffset = 0;\n    return this.pageViews.children.map(function (item, index) {\n      var _a, _b;\n      // calc page, row and column\n      var page = Math.floor(index / pageSize);\n      var pageIndex = index % pageSize;\n      var dir = _this.ifHorizontal(gridCol, gridRow);\n      var pos = [Math.floor(pageIndex / dir), pageIndex % dir];\n      if (orientation === 'vertical') pos.reverse();\n      var _c = __read(pos, 2),\n        row = _c[0],\n        col = _c[1];\n      // calc x, y and shape\n      var colWidth = (width - navWidth - (gridCol - 1) * colPadding) / gridCol;\n      // const rowHeight = getRenderBBox(item).height;\n      var rowHeight = item.getBBox().height;\n      var _d = __read([0, 0], 2),\n        x = _d[0],\n        y = _d[1];\n      if (orientation === 'horizontal') {\n        _a = __read([prevOffset, row * (rowHeight + rowPadding)], 2), x = _a[0], y = _a[1];\n        prevOffset = col === gridCol - 1 ? 0 : prevOffset + colWidth + colPadding;\n      } else {\n        _b = __read([col * (colWidth + colPadding), prevOffset], 2), x = _b[0], y = _b[1];\n        prevOffset = row === gridRow - 1 ? 0 : prevOffset + rowHeight + rowPadding;\n      }\n      return {\n        page: page,\n        index: index,\n        row: row,\n        col: col,\n        pageIndex: pageIndex,\n        width: colWidth,\n        height: rowHeight,\n        x: x,\n        y: y\n      };\n    });\n  };\n  CategoryItems.prototype.getFlexLayout = function () {\n    var _a = this.attributes,\n      maxWidth = _a.width,\n      maxHeight = _a.height,\n      rowPadding = _a.rowPadding,\n      cP = _a.colPadding;\n    var _b = __read(this.navigatorShape, 1),\n      navWidth = _b[0];\n    var _c = __read(this.grid, 2),\n      gridRow = _c[0],\n      gridCol = _c[1];\n    var _d = __read([maxWidth - navWidth, maxHeight], 2),\n      limitWidth = _d[0],\n      limitHeight = _d[1];\n    var _e = __read([0, 0, 0, 0, 0, 0, 0, 0], 8),\n      x = _e[0],\n      y = _e[1],\n      page = _e[2],\n      pageIndex = _e[3],\n      col = _e[4],\n      row = _e[5],\n      prevWidth = _e[6],\n      prevHeight = _e[7];\n    return this.pageViews.children.map(function (item, index) {\n      var _a, _b, _c, _d;\n      // const { width, height } = getRenderBBox(item);\n      var _e = item.getBBox(),\n        width = _e.width,\n        height = _e.height;\n      var colPadding = prevWidth === 0 ? 0 : cP;\n      // assume that every item has the same height\n      var nextWidth = prevWidth + colPadding + width;\n      // inline\n      if (nextWidth <= limitWidth && ifSatisfied(col, function (c) {\n        return c < gridCol;\n      })) {\n        _a = __read([prevWidth + colPadding, prevHeight, nextWidth], 3), x = _a[0], y = _a[1], prevWidth = _a[2];\n        return {\n          width: width,\n          height: height,\n          x: x,\n          y: y,\n          page: page,\n          index: index,\n          pageIndex: pageIndex++,\n          row: row,\n          col: col++\n        };\n      }\n      // wrap\n      _b = __read([row + 1, 0, 0, prevHeight + height + rowPadding], 4), row = _b[0], col = _b[1], prevWidth = _b[2], prevHeight = _b[3];\n      var nextHeight = prevHeight + height;\n      if (nextHeight <= limitHeight && ifSatisfied(row, function (r) {\n        return r < gridRow;\n      })) {\n        _c = __read([prevWidth, prevHeight, width], 3), x = _c[0], y = _c[1], prevWidth = _c[2];\n        return {\n          width: width,\n          height: height,\n          x: x,\n          y: y,\n          page: page,\n          index: index,\n          pageIndex: pageIndex++,\n          row: row,\n          col: col++\n        };\n      }\n      // paging\n      _d = __read([0, 0, width, 0, page + 1, 0, 0, 0], 8), x = _d[0], y = _d[1], prevWidth = _d[2], prevHeight = _d[3], page = _d[4], pageIndex = _d[5], row = _d[6], col = _d[7];\n      return {\n        width: width,\n        height: height,\n        x: x,\n        y: y,\n        page: page,\n        index: index,\n        pageIndex: pageIndex++,\n        row: row,\n        col: col++\n      };\n    });\n  };\n  Object.defineProperty(CategoryItems.prototype, \"itemsLayout\", {\n    get: function () {\n      this.navigatorShape = [0, 0];\n      var cb = this.attributes.layout === 'grid' ? this.getGridLayout : this.getFlexLayout;\n      var layout = cb.call(this);\n      // re layout\n      if (layout.slice(-1)[0].page > 0) {\n        this.navigatorShape = [55, 0];\n        return cb.call(this);\n      }\n      return layout;\n    },\n    enumerable: false,\n    configurable: true\n  });\n  CategoryItems.prototype.ifHorizontal = function (a, b) {\n    var orientation = this.attributes.orientation;\n    return ifHorizontal(orientation, a, b);\n  };\n  CategoryItems.prototype.flattenPage = function (container) {\n    container.querySelectorAll(CLASS_NAMES.item.class).forEach(function (item) {\n      container.appendChild(item);\n    });\n    container.querySelectorAll(CLASS_NAMES.page.class).forEach(function (page) {\n      var removedPage = container.removeChild(page);\n      removedPage.destroy();\n    });\n  };\n  CategoryItems.prototype.renderItems = function (container) {\n    var _a = this.attributes,\n      click = _a.click,\n      mouseenter = _a.mouseenter,\n      mouseleave = _a.mouseleave;\n    this.flattenPage(container);\n    var dispatchCustomEvent = this.dispatchCustomEvent.bind(this);\n    select(container).selectAll(CLASS_NAMES.item.class).data(this.renderData, function (d) {\n      return d.id;\n    }).join(function (enter) {\n      return enter.append(function (_a) {\n        var style = _a.style;\n        return new CategoryItem({\n          style: style\n        });\n      }).attr('className', CLASS_NAMES.item.name).on('click', function () {\n        click === null || click === void 0 ? void 0 : click(this);\n        dispatchCustomEvent('itemClick', {\n          item: this\n        });\n      }).on('pointerenter', function () {\n        mouseenter === null || mouseenter === void 0 ? void 0 : mouseenter(this);\n        dispatchCustomEvent('itemMouseenter', {\n          item: this\n        });\n      }).on('pointerleave', function () {\n        mouseleave === null || mouseleave === void 0 ? void 0 : mouseleave(this);\n        dispatchCustomEvent('itemMouseleave', {\n          item: this\n        });\n      });\n    }, function (update) {\n      return update.each(function (_a) {\n        var style = _a.style;\n        this.update(style);\n      });\n    }, function (exit) {\n      return exit.remove();\n    });\n  };\n  CategoryItems.prototype.relayoutNavigator = function () {\n    var _a;\n    var _b = this.attributes,\n      layout = _b.layout,\n      width = _b.width;\n    var height = ((_a = this.pageViews.children[0]) === null || _a === void 0 ? void 0 : _a.getBBox().height) || 0;\n    var _c = __read(this.navigatorShape, 2),\n      navWidth = _c[0],\n      navHeight = _c[1];\n    this.navigator.update(layout === 'grid' ? {\n      pageWidth: width - navWidth,\n      pageHeight: height - navHeight\n    } : {});\n  };\n  CategoryItems.prototype.adjustLayout = function () {\n    var _this = this;\n    var itemsLayouts = Object.entries(groupBy(this.itemsLayout, 'page')).map(function (_a) {\n      var _b = __read(_a, 2),\n        page = _b[0],\n        layouts = _b[1];\n      return {\n        page: page,\n        layouts: layouts\n      };\n    });\n    var categoryItems = __spreadArray([], __read(this.navigator.getContainer().children), false);\n    itemsLayouts.forEach(function (_a) {\n      var layouts = _a.layouts;\n      var page = _this.pageViews.appendChild(new Group({\n        className: CLASS_NAMES.page.name\n      }));\n      layouts.forEach(function (layout) {\n        var x = layout.x,\n          y = layout.y,\n          index = layout.index,\n          width = layout.width,\n          height = layout.height;\n        var item = categoryItems[index];\n        // @ts-ignore\n        page.appendChild(item);\n        set(item, '__layout__', layout);\n        item.update({\n          x: x,\n          y: y,\n          width: width,\n          height: height\n        });\n      });\n    });\n    this.relayoutNavigator();\n  };\n  CategoryItems.prototype.renderNavigator = function (container) {\n    var orientation = this.attributes.orientation;\n    var navStyle = subStyleProps(this.attributes, 'nav');\n    var style = deepAssign({\n      orientation: orientation\n    }, navStyle);\n    var that = this;\n    container.selectAll(CLASS_NAMES.navigator.class).data(['nav']).join(function (enter) {\n      return enter.append(function () {\n        return new Navigator({\n          style: style\n        });\n      }).attr('className', CLASS_NAMES.navigator.name).each(function () {\n        that.navigator = this;\n      });\n    }, function (update) {\n      return update.each(function () {\n        this.update(style);\n      });\n    }, function (exit) {\n      return exit.remove();\n    });\n    return this.navigator;\n  };\n  CategoryItems.prototype.getBBox = function () {\n    return this.navigator.getBBox();\n  };\n  CategoryItems.prototype.render = function (attributes, container) {\n    var data = this.attributes.data;\n    if (!data || data.length === 0) return;\n    /**\n     * 1. render items\n     * 2. paging\n     * 3. layout\n     */\n    var navigator = this.renderNavigator(select(container));\n    this.renderItems(navigator.getContainer());\n    this.adjustLayout();\n  };\n  CategoryItems.prototype.dispatchCustomEvent = function (type, payload) {\n    var evt = new CustomEvent(type, {\n      detail: payload\n    });\n    this.dispatchEvent(evt);\n  };\n  return CategoryItems;\n}(Component);\nexport { CategoryItems };", "map": {"version": 3, "names": ["CustomEvent", "noop", "set", "Component", "Group", "classNames", "deepAssign", "getCallbackValue", "groupBy", "select", "subStyleProps", "Navigator", "ifHorizontal", "CategoryItem", "CLASS_NAMES", "page", "navigator", "item", "ifSatisfied", "value", "rule", "defaultValue", "CategoryItems", "_super", "__extends", "options", "_this", "call", "data", "gridRow", "Infinity", "gridCol", "undefined", "padding", "width", "height", "rowPadding", "colPadding", "layout", "orientation", "click", "mouseenter", "mouseleave", "navigator<PERSON><PERSON><PERSON>", "Object", "defineProperty", "prototype", "get", "getContainer", "_a", "attributes", "Error", "length", "style", "d", "map", "datum", "index", "id", "labelText", "label", "valueText", "concat", "__assign", "fromEntries", "entries", "_b", "__read", "key", "val", "getGridLayout", "navWidth", "_c", "grid", "pageSize", "prevOffset", "pageViews", "children", "Math", "floor", "pageIndex", "dir", "pos", "reverse", "row", "col", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rowHeight", "getBBox", "_d", "x", "y", "getFlexLayout", "max<PERSON><PERSON><PERSON>", "maxHeight", "cP", "limitWidth", "limitHeight", "_e", "prevWidth", "prevHeight", "nextWidth", "c", "nextHeight", "r", "cb", "slice", "a", "b", "flattenPage", "container", "querySelectorAll", "class", "for<PERSON>ach", "append<PERSON><PERSON><PERSON>", "removedPage", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "renderItems", "dispatchCustomEvent", "bind", "selectAll", "renderData", "join", "enter", "append", "attr", "name", "on", "update", "each", "exit", "remove", "relayoutNavigator", "navHeight", "pageWidth", "pageHeight", "adjustLayout", "itemsLayouts", "itemsLayout", "layouts", "categoryItems", "__spread<PERSON><PERSON>y", "className", "renderNavigator", "navStyle", "that", "render", "type", "payload", "evt", "detail", "dispatchEvent"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/legend/category/items.ts"], "sourcesContent": ["import { CustomEvent } from '@antv/g';\nimport { noop, set } from '@antv/util';\nimport type { CallableStyleProps, ComponentOptions, PrefixStyleProps } from '../../../core';\nimport { Component } from '../../../core';\nimport type { GroupStyleProps } from '../../../shapes';\nimport { Group } from '../../../shapes';\nimport type { CallbackParameter } from '../../../types';\nimport {\n  Selection,\n  SeriesAttr,\n  classNames,\n  deepAssign,\n  getCallbackValue,\n  groupBy,\n  select,\n  subStyleProps,\n} from '../../../util';\nimport type { NavigatorStyleProps } from '../../navigator';\nimport { Navigator } from '../../navigator';\nimport { ifHorizontal } from '../utils';\nimport type { CategoryItemStyleProps } from './item';\nimport { CategoryItem } from './item';\n\ninterface CategoryItemsDatum {\n  [keys: string]: any;\n}\n\ntype CallableItemStyle = CallableStyleProps<\n  Omit<CategoryItemStyleProps, 'width' | 'height'>,\n  CallbackParameter<CategoryItemsDatum>\n>;\n\nexport type CategoryItemsStyleProps = GroupStyleProps &\n  PrefixStyleProps<CallableItemStyle, 'item'> &\n  PrefixStyleProps<NavigatorStyleProps, 'nav'> & {\n    data: CategoryItemsDatum[];\n    orientation?: 'horizontal' | 'vertical';\n    layout?: 'flex' | 'grid';\n    x?: number;\n    y?: number;\n    width?: number;\n    height?: number;\n    gridRow?: number;\n    gridCol?: number;\n    // maxItemWidth?: number;\n    padding?: SeriesAttr;\n    rowPadding?: number;\n    colPadding?: number;\n    click?: (el: Selection) => void;\n    mouseenter?: (el: Selection) => void;\n    mouseleave?: (el: Selection) => void;\n  };\n\nexport type CategoryItemsOptions = ComponentOptions<CategoryItemsStyleProps>;\n\ntype ItemLayout = {\n  page: number;\n  index: number;\n  pageIndex: number;\n  row: number;\n  col: number;\n  x: number;\n  y: number;\n  [keys: string]: any;\n};\n\nconst CLASS_NAMES = classNames(\n  {\n    page: 'item-page',\n    navigator: 'navigator',\n    item: 'item',\n  },\n  'items'\n);\n\n/**\n * if value exists, it need to follow rule, otherwise, return default value\n * @param value\n * @param rule\n * @param defaultValue\n * @returns\n */\nconst ifSatisfied = <T>(value: T, rule: (val: T) => boolean, defaultValue = true) => {\n  if (value) {\n    return rule(value);\n  }\n  return defaultValue;\n};\n\nexport class CategoryItems extends Component<CategoryItemsStyleProps> {\n  constructor(options: CategoryItemsOptions) {\n    super(options, {\n      data: [],\n      gridRow: Infinity,\n      gridCol: undefined,\n      padding: 0,\n      width: 1000,\n      height: 100,\n      rowPadding: 0,\n      colPadding: 0,\n      layout: 'flex',\n      orientation: 'horizontal',\n      click: noop,\n      mouseenter: noop,\n      mouseleave: noop,\n    });\n  }\n\n  private navigator!: Navigator;\n\n  private navigatorShape: [number, number] = [0, 0];\n\n  private get pageViews() {\n    return this.navigator.getContainer();\n  }\n\n  private get grid(): [number, number] {\n    const { gridRow, gridCol, data } = this.attributes;\n    if (!gridRow && !gridCol) throw new Error('gridRow and gridCol can not be set null at the same time');\n    if (!!gridRow && !!gridCol) return [gridRow, gridCol];\n    if (gridRow) return [gridRow, data.length];\n    return [data.length, gridCol!]; // !!gridCol\n  }\n\n  private get renderData() {\n    const { data, layout } = this.attributes;\n    const style = subStyleProps<CategoryItemStyleProps>(this.attributes, 'item');\n\n    const d = data.map((datum, index) => {\n      const { id = index as number, label: labelText, value: valueText } = datum;\n      return {\n        id: `${id}`,\n        index,\n        style: {\n          layout,\n          labelText,\n          valueText,\n          ...Object.fromEntries(\n            Object.entries(style).map(([key, val]) => [key, getCallbackValue(val, [datum, index, data])])\n          ),\n        } as CategoryItemStyleProps,\n      };\n    });\n    return d;\n  }\n\n  private getGridLayout() {\n    const { orientation, width, rowPadding, colPadding } = this.attributes;\n    const [navWidth] = this.navigatorShape;\n    const [gridRow, gridCol] = this.grid;\n    const pageSize = gridCol * gridRow;\n\n    let prevOffset = 0;\n    return (this.pageViews.children as CategoryItem[]).map((item, index) => {\n      // calc page, row and column\n      const page = Math.floor(index / pageSize);\n      const pageIndex = index % pageSize;\n      const dir = this.ifHorizontal(gridCol, gridRow);\n      const pos = [Math.floor(pageIndex / dir), pageIndex % dir];\n      if (orientation === 'vertical') pos.reverse();\n      const [row, col] = pos;\n\n      // calc x, y and shape\n      const colWidth = (width - navWidth - (gridCol - 1) * colPadding) / gridCol;\n      // const rowHeight = getRenderBBox(item).height;\n      const rowHeight = item.getBBox().height;\n\n      let [x, y] = [0, 0];\n      if (orientation === 'horizontal') {\n        [x, y] = [prevOffset, row * (rowHeight + rowPadding)];\n        prevOffset = col === gridCol - 1 ? 0 : prevOffset + colWidth + colPadding;\n      } else {\n        [x, y] = [col * (colWidth + colPadding), prevOffset];\n        prevOffset = row === gridRow - 1 ? 0 : prevOffset + rowHeight + rowPadding;\n      }\n\n      return { page, index, row, col, pageIndex, width: colWidth, height: rowHeight, x, y } as ItemLayout;\n    });\n  }\n\n  private getFlexLayout(): ItemLayout[] {\n    const { width: maxWidth, height: maxHeight, rowPadding, colPadding: cP } = this.attributes;\n    const [navWidth] = this.navigatorShape;\n    const [gridRow, gridCol] = this.grid;\n\n    const [limitWidth, limitHeight] = [maxWidth - navWidth, maxHeight];\n    let [x, y, page, pageIndex, col, row, prevWidth, prevHeight] = [0, 0, 0, 0, 0, 0, 0, 0];\n    return (this.pageViews.children as CategoryItem[]).map((item, index) => {\n      // const { width, height } = getRenderBBox(item);\n      const { width, height } = item.getBBox();\n      const colPadding = prevWidth === 0 ? 0 : cP;\n      // assume that every item has the same height\n      const nextWidth = prevWidth + colPadding + width;\n      // inline\n      if (nextWidth <= limitWidth && ifSatisfied(col, (c) => c < gridCol)) {\n        [x, y, prevWidth] = [prevWidth + colPadding, prevHeight, nextWidth];\n        return { width, height, x, y, page, index, pageIndex: pageIndex++, row, col: col++ };\n      }\n\n      // wrap\n      [row, col, prevWidth, prevHeight] = [row + 1, 0, 0, prevHeight + height + rowPadding];\n      const nextHeight = prevHeight + height;\n      if (nextHeight <= limitHeight && ifSatisfied(row, (r) => r < gridRow)) {\n        [x, y, prevWidth] = [prevWidth, prevHeight, width];\n        return { width, height, x, y, page, index, pageIndex: pageIndex++, row, col: col++ };\n      }\n\n      // paging\n      [x, y, prevWidth, prevHeight, page, pageIndex, row, col] = [0, 0, width, 0, page + 1, 0, 0, 0];\n      return { width, height, x, y, page, index, pageIndex: pageIndex++, row, col: col++ };\n    });\n  }\n\n  private get itemsLayout() {\n    this.navigatorShape = [0, 0];\n    const cb = this.attributes.layout === 'grid' ? this.getGridLayout : this.getFlexLayout;\n    const layout = cb.call(this);\n    // re layout\n    if (layout.slice(-1)[0].page > 0) {\n      this.navigatorShape = [55, 0];\n      return cb.call(this);\n    }\n    return layout;\n  }\n\n  private ifHorizontal<T>(a: T, b: T): T {\n    const { orientation } = this.attributes;\n    return ifHorizontal(orientation, a, b);\n  }\n\n  private flattenPage(container: Group) {\n    container.querySelectorAll(CLASS_NAMES.item.class).forEach((item) => {\n      container.appendChild(item);\n    });\n    container.querySelectorAll(CLASS_NAMES.page.class).forEach((page) => {\n      const removedPage = container.removeChild(page);\n      removedPage.destroy();\n    });\n  }\n\n  private renderItems(container: Group) {\n    const { click, mouseenter, mouseleave } = this.attributes;\n    this.flattenPage(container);\n    const dispatchCustomEvent = this.dispatchCustomEvent.bind(this);\n    select(container)\n      .selectAll(CLASS_NAMES.item.class)\n      .data(this.renderData, (d) => d.id)\n      .join(\n        (enter) =>\n          enter\n            .append(({ style }) => new CategoryItem({ style }))\n            .attr('className', CLASS_NAMES.item.name)\n            .on('click', function () {\n              click?.(this);\n              dispatchCustomEvent('itemClick', { item: this });\n            })\n            .on('pointerenter', function () {\n              mouseenter?.(this);\n              dispatchCustomEvent('itemMouseenter', { item: this });\n            })\n            .on('pointerleave', function () {\n              mouseleave?.(this);\n              dispatchCustomEvent('itemMouseleave', { item: this });\n            }),\n        (update) =>\n          update.each(function ({ style }) {\n            this.update(style);\n          }),\n        (exit) => exit.remove()\n      );\n  }\n\n  private relayoutNavigator() {\n    const { layout, width } = this.attributes;\n    const height = (this.pageViews.children[0] as Group)?.getBBox().height || 0;\n    const [navWidth, navHeight] = this.navigatorShape;\n\n    this.navigator.update(layout === 'grid' ? { pageWidth: width! - navWidth, pageHeight: height - navHeight } : {});\n  }\n\n  private adjustLayout() {\n    const itemsLayouts = Object.entries(groupBy(this.itemsLayout, 'page')).map(([page, layouts]) => ({\n      page,\n      layouts,\n    }));\n    const categoryItems = [...this.navigator.getContainer().children] as CategoryItem[];\n\n    itemsLayouts.forEach(({ layouts }) => {\n      const page = this.pageViews.appendChild(new Group({ className: CLASS_NAMES.page.name }));\n      layouts.forEach((layout) => {\n        const { x, y, index, width, height } = layout;\n        const item = categoryItems[index];\n        // @ts-ignore\n        page.appendChild(item);\n        set(item, '__layout__', layout);\n        item.update({ x, y, width, height });\n      });\n    });\n    this.relayoutNavigator();\n  }\n\n  private renderNavigator(container: Selection) {\n    const { orientation } = this.attributes;\n    const navStyle = subStyleProps(this.attributes, 'nav');\n    const style = deepAssign({ orientation }, navStyle);\n    const that = this;\n    container\n      .selectAll(CLASS_NAMES.navigator.class)\n      .data(['nav'])\n      .join(\n        (enter) =>\n          enter\n            .append(() => new Navigator({ style }))\n            .attr('className', CLASS_NAMES.navigator.name)\n            .each(function () {\n              that.navigator = this;\n            }),\n        (update) =>\n          update.each(function () {\n            this.update(style);\n          }),\n        (exit) => exit.remove()\n      );\n\n    return this.navigator;\n  }\n\n  public getBBox(): DOMRect {\n    return this.navigator.getBBox();\n  }\n\n  render(attributes: Required<CategoryItemsStyleProps>, container: Group) {\n    const { data } = this.attributes;\n    if (!data || data.length === 0) return;\n    /**\n     * 1. render items\n     * 2. paging\n     * 3. layout\n     */\n    const navigator = this.renderNavigator(select(container));\n\n    this.renderItems(navigator.getContainer());\n    this.adjustLayout();\n  }\n\n  private dispatchCustomEvent(type: string, payload: any) {\n    const evt = new CustomEvent(type, {\n      detail: payload,\n    });\n    this.dispatchEvent(evt as any);\n  }\n}\n"], "mappings": ";AAAA,SAASA,WAAW,QAAQ,SAAS;AACrC,SAASC,IAAI,EAAEC,GAAG,QAAQ,YAAY;AAEtC,SAASC,SAAS,QAAQ,eAAe;AAEzC,SAASC,KAAK,QAAQ,iBAAiB;AAEvC,SAGEC,UAAU,EACVC,UAAU,EACVC,gBAAgB,EAChBC,OAAO,EACPC,MAAM,EACNC,aAAa,QACR,eAAe;AAEtB,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,YAAY,QAAQ,UAAU;AAEvC,SAASC,YAAY,QAAQ,QAAQ;AA6CrC,IAAMC,WAAW,GAAGT,UAAU,CAC5B;EACEU,IAAI,EAAE,WAAW;EACjBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE;CACP,EACD,OAAO,CACR;AAED;;;;;;;AAOA,IAAMC,WAAW,GAAG,SAAAA,CAAIC,KAAQ,EAAEC,IAAyB,EAAEC,YAAmB;EAAnB,IAAAA,YAAA;IAAAA,YAAA,OAAmB;EAAA;EAC9E,IAAIF,KAAK,EAAE;IACT,OAAOC,IAAI,CAACD,KAAK,CAAC;EACpB;EACA,OAAOE,YAAY;AACrB,CAAC;AAED,IAAAC,aAAA,0BAAAC,MAAA;EAAmCC,SAAA,CAAAF,aAAA,EAAAC,MAAA;EACjC,SAAAD,cAAYG,OAA6B;IACvC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACF,OAAO,EAAE;MACbG,IAAI,EAAE,EAAE;MACRC,OAAO,EAAEC,QAAQ;MACjBC,OAAO,EAAEC,SAAS;MAClBC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,GAAG;MACXC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAEvC,IAAI;MACXwC,UAAU,EAAExC,IAAI;MAChByC,UAAU,EAAEzC;KACb,CAAC;IAKIyB,KAAA,CAAAiB,cAAc,GAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;;EAJjD;EAMAC,MAAA,CAAAC,cAAA,CAAYvB,aAAA,CAAAwB,SAAA,aAAS;SAArB,SAAAC,CAAA;MACE,OAAO,IAAI,CAAC/B,SAAS,CAACgC,YAAY,EAAE;IACtC,CAAC;;;;EAEDJ,MAAA,CAAAC,cAAA,CAAYvB,aAAA,CAAAwB,SAAA,QAAI;SAAhB,SAAAC,CAAA;MACQ,IAAAE,EAAA,GAA6B,IAAI,CAACC,UAAU;QAA1CrB,OAAO,GAAAoB,EAAA,CAAApB,OAAA;QAAEE,OAAO,GAAAkB,EAAA,CAAAlB,OAAA;QAAEH,IAAI,GAAAqB,EAAA,CAAArB,IAAoB;MAClD,IAAI,CAACC,OAAO,IAAI,CAACE,OAAO,EAAE,MAAM,IAAIoB,KAAK,CAAC,0DAA0D,CAAC;MACrG,IAAI,CAAC,CAACtB,OAAO,IAAI,CAAC,CAACE,OAAO,EAAE,OAAO,CAACF,OAAO,EAAEE,OAAO,CAAC;MACrD,IAAIF,OAAO,EAAE,OAAO,CAACA,OAAO,EAAED,IAAI,CAACwB,MAAM,CAAC;MAC1C,OAAO,CAACxB,IAAI,CAACwB,MAAM,EAAErB,OAAQ,CAAC,CAAC,CAAC;IAClC,CAAC;;;;EAEDa,MAAA,CAAAC,cAAA,CAAYvB,aAAA,CAAAwB,SAAA,cAAU;SAAtB,SAAAC,CAAA;MACQ,IAAAE,EAAA,GAAmB,IAAI,CAACC,UAAU;QAAhCtB,IAAI,GAAAqB,EAAA,CAAArB,IAAA;QAAEU,MAAM,GAAAW,EAAA,CAAAX,MAAoB;MACxC,IAAMe,KAAK,GAAG3C,aAAa,CAAyB,IAAI,CAACwC,UAAU,EAAE,MAAM,CAAC;MAE5E,IAAMI,CAAC,GAAG1B,IAAI,CAAC2B,GAAG,CAAC,UAACC,KAAK,EAAEC,KAAK;QACtB,IAAAR,EAAA,GAA6DO,KAAK,CAAAE,EAA9C;UAApBA,EAAE,GAAAT,EAAA,cAAGQ,KAAe,GAAAR,EAAA;UAASU,SAAS,GAAuBH,KAAK,CAAAI,KAA5B;UAASC,SAAS,GAAKL,KAAK,CAAArC,KAAV;QAChE,OAAO;UACLuC,EAAE,EAAE,GAAAI,MAAA,CAAGJ,EAAE,CAAE;UACXD,KAAK,EAAAA,KAAA;UACLJ,KAAK,EAAEU,QAAA;YACLzB,MAAM,EAAAA,MAAA;YACNqB,SAAS,EAAAA,SAAA;YACTE,SAAS,EAAAA;UAAA,GACNjB,MAAM,CAACoB,WAAW,CACnBpB,MAAM,CAACqB,OAAO,CAACZ,KAAK,CAAC,CAACE,GAAG,CAAC,UAACN,EAAU;gBAAViB,EAAA,GAAAC,MAAA,CAAAlB,EAAA,IAAU;cAATmB,GAAG,GAAAF,EAAA;cAAEG,GAAG,GAAAH,EAAA;YAAM,QAACE,GAAG,EAAE7D,gBAAgB,CAAC8D,GAAG,EAAE,CAACb,KAAK,EAAEC,KAAK,EAAE7B,IAAI,CAAC,CAAC,CAAC;UAAlD,CAAkD,CAAC,CAC9F;SAEJ;MACH,CAAC,CAAC;MACF,OAAO0B,CAAC;IACV,CAAC;;;;EAEOhC,aAAA,CAAAwB,SAAA,CAAAwB,aAAa,GAArB;IAAA,IAAA5C,KAAA;IACQ,IAAAuB,EAAA,GAAiD,IAAI,CAACC,UAAU;MAA9DX,WAAW,GAAAU,EAAA,CAAAV,WAAA;MAAEL,KAAK,GAAAe,EAAA,CAAAf,KAAA;MAAEE,UAAU,GAAAa,EAAA,CAAAb,UAAA;MAAEC,UAAU,GAAAY,EAAA,CAAAZ,UAAoB;IAChE,IAAA6B,EAAA,GAAAC,MAAA,CAAa,IAAI,CAACxB,cAAc;MAA/B4B,QAAQ,GAAAL,EAAA,GAAuB;IAChC,IAAAM,EAAA,GAAAL,MAAA,CAAqB,IAAI,CAACM,IAAI;MAA7B5C,OAAO,GAAA2C,EAAA;MAAEzC,OAAO,GAAAyC,EAAA,GAAa;IACpC,IAAME,QAAQ,GAAG3C,OAAO,GAAGF,OAAO;IAElC,IAAI8C,UAAU,GAAG,CAAC;IAClB,OAAQ,IAAI,CAACC,SAAS,CAACC,QAA2B,CAACtB,GAAG,CAAC,UAACtC,IAAI,EAAEwC,KAAK;;MACjE;MACA,IAAM1C,IAAI,GAAG+D,IAAI,CAACC,KAAK,CAACtB,KAAK,GAAGiB,QAAQ,CAAC;MACzC,IAAMM,SAAS,GAAGvB,KAAK,GAAGiB,QAAQ;MAClC,IAAMO,GAAG,GAAGvD,KAAI,CAACd,YAAY,CAACmB,OAAO,EAAEF,OAAO,CAAC;MAC/C,IAAMqD,GAAG,GAAG,CAACJ,IAAI,CAACC,KAAK,CAACC,SAAS,GAAGC,GAAG,CAAC,EAAED,SAAS,GAAGC,GAAG,CAAC;MAC1D,IAAI1C,WAAW,KAAK,UAAU,EAAE2C,GAAG,CAACC,OAAO,EAAE;MACvC,IAAAX,EAAA,GAAAL,MAAA,CAAae,GAAG;QAAfE,GAAG,GAAAZ,EAAA;QAAEa,GAAG,GAAAb,EAAA,GAAO;MAEtB;MACA,IAAMc,QAAQ,GAAG,CAACpD,KAAK,GAAGqC,QAAQ,GAAG,CAACxC,OAAO,GAAG,CAAC,IAAIM,UAAU,IAAIN,OAAO;MAC1E;MACA,IAAMwD,SAAS,GAAGtE,IAAI,CAACuE,OAAO,EAAE,CAACrD,MAAM;MAEnC,IAAAsD,EAAA,GAAAtB,MAAA,CAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QAAduB,CAAC,GAAAD,EAAA;QAAEE,CAAC,GAAAF,EAAA,GAAU;MACnB,IAAIlD,WAAW,KAAK,YAAY,EAAE;QAChCU,EAAA,GAAAkB,MAAA,CAAS,CAACQ,UAAU,EAAES,GAAG,IAAIG,SAAS,GAAGnD,UAAU,CAAC,CAAC,MAApDsD,CAAC,GAAAzC,EAAA,KAAE0C,CAAC,GAAA1C,EAAA;QACL0B,UAAU,GAAGU,GAAG,KAAKtD,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG4C,UAAU,GAAGW,QAAQ,GAAGjD,UAAU;MAC3E,CAAC,MAAM;QACL6B,EAAA,GAAAC,MAAA,CAAS,CAACkB,GAAG,IAAIC,QAAQ,GAAGjD,UAAU,CAAC,EAAEsC,UAAU,CAAC,MAAnDe,CAAC,GAAAxB,EAAA,KAAEyB,CAAC,GAAAzB,EAAA;QACLS,UAAU,GAAGS,GAAG,KAAKvD,OAAO,GAAG,CAAC,GAAG,CAAC,GAAG8C,UAAU,GAAGY,SAAS,GAAGnD,UAAU;MAC5E;MAEA,OAAO;QAAErB,IAAI,EAAAA,IAAA;QAAE0C,KAAK,EAAAA,KAAA;QAAE2B,GAAG,EAAAA,GAAA;QAAEC,GAAG,EAAAA,GAAA;QAAEL,SAAS,EAAAA,SAAA;QAAE9C,KAAK,EAAEoD,QAAQ;QAAEnD,MAAM,EAAEoD,SAAS;QAAEG,CAAC,EAAAA,CAAA;QAAEC,CAAC,EAAAA;MAAA,CAAgB;IACrG,CAAC,CAAC;EACJ,CAAC;EAEOrE,aAAA,CAAAwB,SAAA,CAAA8C,aAAa,GAArB;IACQ,IAAA3C,EAAA,GAAqE,IAAI,CAACC,UAAU;MAA3E2C,QAAQ,GAAA5C,EAAA,CAAAf,KAAA;MAAU4D,SAAS,GAAA7C,EAAA,CAAAd,MAAA;MAAEC,UAAU,GAAAa,EAAA,CAAAb,UAAA;MAAc2D,EAAE,GAAA9C,EAAA,CAAAZ,UAAoB;IACpF,IAAA6B,EAAA,GAAAC,MAAA,CAAa,IAAI,CAACxB,cAAc;MAA/B4B,QAAQ,GAAAL,EAAA,GAAuB;IAChC,IAAAM,EAAA,GAAAL,MAAA,CAAqB,IAAI,CAACM,IAAI;MAA7B5C,OAAO,GAAA2C,EAAA;MAAEzC,OAAO,GAAAyC,EAAA,GAAa;IAE9B,IAAAiB,EAAA,GAAAtB,MAAA,CAA4B,CAAC0B,QAAQ,GAAGtB,QAAQ,EAAEuB,SAAS,CAAC;MAA3DE,UAAU,GAAAP,EAAA;MAAEQ,WAAW,GAAAR,EAAA,GAAoC;IAC9D,IAAAS,EAAA,GAAA/B,MAAA,CAA2D,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAAlFuB,CAAC,GAAAQ,EAAA;MAAEP,CAAC,GAAAO,EAAA;MAAEnF,IAAI,GAAAmF,EAAA;MAAElB,SAAS,GAAAkB,EAAA;MAAEb,GAAG,GAAAa,EAAA;MAAEd,GAAG,GAAAc,EAAA;MAAEC,SAAS,GAAAD,EAAA;MAAEE,UAAU,GAAAF,EAAA,GAA4B;IACvF,OAAQ,IAAI,CAACtB,SAAS,CAACC,QAA2B,CAACtB,GAAG,CAAC,UAACtC,IAAI,EAAEwC,KAAK;;MACjE;MACM,IAAAyC,EAAA,GAAoBjF,IAAI,CAACuE,OAAO,EAAE;QAAhCtD,KAAK,GAAAgE,EAAA,CAAAhE,KAAA;QAAEC,MAAM,GAAA+D,EAAA,CAAA/D,MAAmB;MACxC,IAAME,UAAU,GAAG8D,SAAS,KAAK,CAAC,GAAG,CAAC,GAAGJ,EAAE;MAC3C;MACA,IAAMM,SAAS,GAAGF,SAAS,GAAG9D,UAAU,GAAGH,KAAK;MAChD;MACA,IAAImE,SAAS,IAAIL,UAAU,IAAI9E,WAAW,CAACmE,GAAG,EAAE,UAACiB,CAAC;QAAK,OAAAA,CAAC,GAAGvE,OAAO;MAAX,CAAW,CAAC,EAAE;QACnEkB,EAAA,GAAAkB,MAAA,CAAoB,CAACgC,SAAS,GAAG9D,UAAU,EAAE+D,UAAU,EAAEC,SAAS,CAAC,MAAlEX,CAAC,GAAAzC,EAAA,KAAE0C,CAAC,GAAA1C,EAAA,KAAEkD,SAAS,GAAAlD,EAAA;QAChB,OAAO;UAAEf,KAAK,EAAAA,KAAA;UAAEC,MAAM,EAAAA,MAAA;UAAEuD,CAAC,EAAAA,CAAA;UAAEC,CAAC,EAAAA,CAAA;UAAE5E,IAAI,EAAAA,IAAA;UAAE0C,KAAK,EAAAA,KAAA;UAAEuB,SAAS,EAAEA,SAAS,EAAE;UAAEI,GAAG,EAAAA,GAAA;UAAEC,GAAG,EAAEA,GAAG;QAAE,CAAE;MACtF;MAEA;MACAnB,EAAA,GAAAC,MAAA,CAAoC,CAACiB,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEgB,UAAU,GAAGjE,MAAM,GAAGC,UAAU,CAAC,MAApFgD,GAAG,GAAAlB,EAAA,KAAEmB,GAAG,GAAAnB,EAAA,KAAEiC,SAAS,GAAAjC,EAAA,KAAEkC,UAAU,GAAAlC,EAAA;MAChC,IAAMqC,UAAU,GAAGH,UAAU,GAAGjE,MAAM;MACtC,IAAIoE,UAAU,IAAIN,WAAW,IAAI/E,WAAW,CAACkE,GAAG,EAAE,UAACoB,CAAC;QAAK,OAAAA,CAAC,GAAG3E,OAAO;MAAX,CAAW,CAAC,EAAE;QACrE2C,EAAA,GAAAL,MAAA,CAAoB,CAACgC,SAAS,EAAEC,UAAU,EAAElE,KAAK,CAAC,MAAjDwD,CAAC,GAAAlB,EAAA,KAAEmB,CAAC,GAAAnB,EAAA,KAAE2B,SAAS,GAAA3B,EAAA;QAChB,OAAO;UAAEtC,KAAK,EAAAA,KAAA;UAAEC,MAAM,EAAAA,MAAA;UAAEuD,CAAC,EAAAA,CAAA;UAAEC,CAAC,EAAAA,CAAA;UAAE5E,IAAI,EAAAA,IAAA;UAAE0C,KAAK,EAAAA,KAAA;UAAEuB,SAAS,EAAEA,SAAS,EAAE;UAAEI,GAAG,EAAAA,GAAA;UAAEC,GAAG,EAAEA,GAAG;QAAE,CAAE;MACtF;MAEA;MACAI,EAAA,GAAAtB,MAAA,CAA2D,CAAC,CAAC,EAAE,CAAC,EAAEjC,KAAK,EAAE,CAAC,EAAEnB,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAA7F2E,CAAC,GAAAD,EAAA,KAAEE,CAAC,GAAAF,EAAA,KAAEU,SAAS,GAAAV,EAAA,KAAEW,UAAU,GAAAX,EAAA,KAAE1E,IAAI,GAAA0E,EAAA,KAAET,SAAS,GAAAS,EAAA,KAAEL,GAAG,GAAAK,EAAA,KAAEJ,GAAG,GAAAI,EAAA;MACvD,OAAO;QAAEvD,KAAK,EAAAA,KAAA;QAAEC,MAAM,EAAAA,MAAA;QAAEuD,CAAC,EAAAA,CAAA;QAAEC,CAAC,EAAAA,CAAA;QAAE5E,IAAI,EAAAA,IAAA;QAAE0C,KAAK,EAAAA,KAAA;QAAEuB,SAAS,EAAEA,SAAS,EAAE;QAAEI,GAAG,EAAAA,GAAA;QAAEC,GAAG,EAAEA,GAAG;MAAE,CAAE;IACtF,CAAC,CAAC;EACJ,CAAC;EAEDzC,MAAA,CAAAC,cAAA,CAAYvB,aAAA,CAAAwB,SAAA,eAAW;SAAvB,SAAAC,CAAA;MACE,IAAI,CAACJ,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MAC5B,IAAM8D,EAAE,GAAG,IAAI,CAACvD,UAAU,CAACZ,MAAM,KAAK,MAAM,GAAG,IAAI,CAACgC,aAAa,GAAG,IAAI,CAACsB,aAAa;MACtF,IAAMtD,MAAM,GAAGmE,EAAE,CAAC9E,IAAI,CAAC,IAAI,CAAC;MAC5B;MACA,IAAIW,MAAM,CAACoE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC3F,IAAI,GAAG,CAAC,EAAE;QAChC,IAAI,CAAC4B,cAAc,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QAC7B,OAAO8D,EAAE,CAAC9E,IAAI,CAAC,IAAI,CAAC;MACtB;MACA,OAAOW,MAAM;IACf,CAAC;;;;EAEOhB,aAAA,CAAAwB,SAAA,CAAAlC,YAAY,GAApB,UAAwB+F,CAAI,EAAEC,CAAI;IACxB,IAAArE,WAAW,GAAK,IAAI,CAACW,UAAU,CAAAX,WAApB;IACnB,OAAO3B,YAAY,CAAC2B,WAAW,EAAEoE,CAAC,EAAEC,CAAC,CAAC;EACxC,CAAC;EAEOtF,aAAA,CAAAwB,SAAA,CAAA+D,WAAW,GAAnB,UAAoBC,SAAgB;IAClCA,SAAS,CAACC,gBAAgB,CAACjG,WAAW,CAACG,IAAI,CAAC+F,KAAK,CAAC,CAACC,OAAO,CAAC,UAAChG,IAAI;MAC9D6F,SAAS,CAACI,WAAW,CAACjG,IAAI,CAAC;IAC7B,CAAC,CAAC;IACF6F,SAAS,CAACC,gBAAgB,CAACjG,WAAW,CAACC,IAAI,CAACiG,KAAK,CAAC,CAACC,OAAO,CAAC,UAAClG,IAAI;MAC9D,IAAMoG,WAAW,GAAGL,SAAS,CAACM,WAAW,CAACrG,IAAI,CAAC;MAC/CoG,WAAW,CAACE,OAAO,EAAE;IACvB,CAAC,CAAC;EACJ,CAAC;EAEO/F,aAAA,CAAAwB,SAAA,CAAAwE,WAAW,GAAnB,UAAoBR,SAAgB;IAC5B,IAAA7D,EAAA,GAAoC,IAAI,CAACC,UAAU;MAAjDV,KAAK,GAAAS,EAAA,CAAAT,KAAA;MAAEC,UAAU,GAAAQ,EAAA,CAAAR,UAAA;MAAEC,UAAU,GAAAO,EAAA,CAAAP,UAAoB;IACzD,IAAI,CAACmE,WAAW,CAACC,SAAS,CAAC;IAC3B,IAAMS,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAACC,IAAI,CAAC,IAAI,CAAC;IAC/D/G,MAAM,CAACqG,SAAS,CAAC,CACdW,SAAS,CAAC3G,WAAW,CAACG,IAAI,CAAC+F,KAAK,CAAC,CACjCpF,IAAI,CAAC,IAAI,CAAC8F,UAAU,EAAE,UAACpE,CAAC;MAAK,OAAAA,CAAC,CAACI,EAAE;IAAJ,CAAI,CAAC,CAClCiE,IAAI,CACH,UAACC,KAAK;MACJ,OAAAA,KAAK,CACFC,MAAM,CAAC,UAAC5E,EAAS;YAAPI,KAAK,GAAAJ,EAAA,CAAAI,KAAA;QAAO,WAAIxC,YAAY,CAAC;UAAEwC,KAAK,EAAAA;QAAA,CAAE,CAAC;MAA3B,CAA2B,CAAC,CAClDyE,IAAI,CAAC,WAAW,EAAEhH,WAAW,CAACG,IAAI,CAAC8G,IAAI,CAAC,CACxCC,EAAE,CAAC,OAAO,EAAE;QACXxF,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,IAAI,CAAC;QACb+E,mBAAmB,CAAC,WAAW,EAAE;UAAEtG,IAAI,EAAE;QAAI,CAAE,CAAC;MAClD,CAAC,CAAC,CACD+G,EAAE,CAAC,cAAc,EAAE;QAClBvF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG,IAAI,CAAC;QAClB8E,mBAAmB,CAAC,gBAAgB,EAAE;UAAEtG,IAAI,EAAE;QAAI,CAAE,CAAC;MACvD,CAAC,CAAC,CACD+G,EAAE,CAAC,cAAc,EAAE;QAClBtF,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG,IAAI,CAAC;QAClB6E,mBAAmB,CAAC,gBAAgB,EAAE;UAAEtG,IAAI,EAAE;QAAI,CAAE,CAAC;MACvD,CAAC,CAAC;IAdJ,CAcI,EACN,UAACgH,MAAM;MACL,OAAAA,MAAM,CAACC,IAAI,CAAC,UAAUjF,EAAS;YAAPI,KAAK,GAAAJ,EAAA,CAAAI,KAAA;QAC3B,IAAI,CAAC4E,MAAM,CAAC5E,KAAK,CAAC;MACpB,CAAC,CAAC;IAFF,CAEE,EACJ,UAAC8E,IAAI;MAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;IAAb,CAAa,CACxB;EACL,CAAC;EAEO9G,aAAA,CAAAwB,SAAA,CAAAuF,iBAAiB,GAAzB;;IACQ,IAAAnE,EAAA,GAAoB,IAAI,CAAChB,UAAU;MAAjCZ,MAAM,GAAA4B,EAAA,CAAA5B,MAAA;MAAEJ,KAAK,GAAAgC,EAAA,CAAAhC,KAAoB;IACzC,IAAMC,MAAM,GAAG,EAAAc,EAAA,GAAC,IAAI,CAAC2B,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAW,cAAA5B,EAAA,uBAAAA,EAAA,CAAEuC,OAAO,GAAGrD,MAAM,KAAI,CAAC;IACrE,IAAAqC,EAAA,GAAAL,MAAA,CAAwB,IAAI,CAACxB,cAAc;MAA1C4B,QAAQ,GAAAC,EAAA;MAAE8D,SAAS,GAAA9D,EAAA,GAAuB;IAEjD,IAAI,CAACxD,SAAS,CAACiH,MAAM,CAAC3F,MAAM,KAAK,MAAM,GAAG;MAAEiG,SAAS,EAAErG,KAAM,GAAGqC,QAAQ;MAAEiE,UAAU,EAAErG,MAAM,GAAGmG;IAAS,CAAE,GAAG,EAAE,CAAC;EAClH,CAAC;EAEOhH,aAAA,CAAAwB,SAAA,CAAA2F,YAAY,GAApB;IAAA,IAAA/G,KAAA;IACE,IAAMgH,YAAY,GAAG9F,MAAM,CAACqB,OAAO,CAACzD,OAAO,CAAC,IAAI,CAACmI,WAAW,EAAE,MAAM,CAAC,CAAC,CAACpF,GAAG,CAAC,UAACN,EAAe;UAAfiB,EAAA,GAAAC,MAAA,CAAAlB,EAAA,IAAe;QAAdlC,IAAI,GAAAmD,EAAA;QAAE0E,OAAO,GAAA1E,EAAA;MAAM,OAAC;QAC/FnD,IAAI,EAAAA,IAAA;QACJ6H,OAAO,EAAAA;OACR;IAH+F,CAG9F,CAAC;IACH,IAAMC,aAAa,GAAGC,aAAA,KAAA3E,MAAA,CAAI,IAAI,CAACnD,SAAS,CAACgC,YAAY,EAAE,CAAC6B,QAAQ,SAAmB;IAEnF6D,YAAY,CAACzB,OAAO,CAAC,UAAChE,EAAW;UAAT2F,OAAO,GAAA3F,EAAA,CAAA2F,OAAA;MAC7B,IAAM7H,IAAI,GAAGW,KAAI,CAACkD,SAAS,CAACsC,WAAW,CAAC,IAAI9G,KAAK,CAAC;QAAE2I,SAAS,EAAEjI,WAAW,CAACC,IAAI,CAACgH;MAAI,CAAE,CAAC,CAAC;MACxFa,OAAO,CAAC3B,OAAO,CAAC,UAAC3E,MAAM;QACb,IAAAoD,CAAC,GAA8BpD,MAAM,CAAAoD,CAApC;UAAEC,CAAC,GAA2BrD,MAAM,CAAAqD,CAAjC;UAAElC,KAAK,GAAoBnB,MAAM,CAAAmB,KAA1B;UAAEvB,KAAK,GAAaI,MAAM,CAAAJ,KAAnB;UAAEC,MAAM,GAAKG,MAAM,CAAAH,MAAX;QAClC,IAAMlB,IAAI,GAAG4H,aAAa,CAACpF,KAAK,CAAC;QACjC;QACA1C,IAAI,CAACmG,WAAW,CAACjG,IAAI,CAAC;QACtBf,GAAG,CAACe,IAAI,EAAE,YAAY,EAAEqB,MAAM,CAAC;QAC/BrB,IAAI,CAACgH,MAAM,CAAC;UAAEvC,CAAC,EAAAA,CAAA;UAAEC,CAAC,EAAAA,CAAA;UAAEzD,KAAK,EAAAA,KAAA;UAAEC,MAAM,EAAAA;QAAA,CAAE,CAAC;MACtC,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAI,CAACkG,iBAAiB,EAAE;EAC1B,CAAC;EAEO/G,aAAA,CAAAwB,SAAA,CAAAkG,eAAe,GAAvB,UAAwBlC,SAAoB;IAClC,IAAAvE,WAAW,GAAK,IAAI,CAACW,UAAU,CAAAX,WAApB;IACnB,IAAM0G,QAAQ,GAAGvI,aAAa,CAAC,IAAI,CAACwC,UAAU,EAAE,KAAK,CAAC;IACtD,IAAMG,KAAK,GAAG/C,UAAU,CAAC;MAAEiC,WAAW,EAAAA;IAAA,CAAE,EAAE0G,QAAQ,CAAC;IACnD,IAAMC,IAAI,GAAG,IAAI;IACjBpC,SAAS,CACNW,SAAS,CAAC3G,WAAW,CAACE,SAAS,CAACgG,KAAK,CAAC,CACtCpF,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CACb+F,IAAI,CACH,UAACC,KAAK;MACJ,OAAAA,KAAK,CACFC,MAAM,CAAC;QAAM,WAAIlH,SAAS,CAAC;UAAE0C,KAAK,EAAAA;QAAA,CAAE,CAAC;MAAxB,CAAwB,CAAC,CACtCyE,IAAI,CAAC,WAAW,EAAEhH,WAAW,CAACE,SAAS,CAAC+G,IAAI,CAAC,CAC7CG,IAAI,CAAC;QACJgB,IAAI,CAAClI,SAAS,GAAG,IAAI;MACvB,CAAC,CAAC;IALJ,CAKI,EACN,UAACiH,MAAM;MACL,OAAAA,MAAM,CAACC,IAAI,CAAC;QACV,IAAI,CAACD,MAAM,CAAC5E,KAAK,CAAC;MACpB,CAAC,CAAC;IAFF,CAEE,EACJ,UAAC8E,IAAI;MAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;IAAb,CAAa,CACxB;IAEH,OAAO,IAAI,CAACpH,SAAS;EACvB,CAAC;EAEMM,aAAA,CAAAwB,SAAA,CAAA0C,OAAO,GAAd;IACE,OAAO,IAAI,CAACxE,SAAS,CAACwE,OAAO,EAAE;EACjC,CAAC;EAEDlE,aAAA,CAAAwB,SAAA,CAAAqG,MAAM,GAAN,UAAOjG,UAA6C,EAAE4D,SAAgB;IAC5D,IAAAlF,IAAI,GAAK,IAAI,CAACsB,UAAU,CAAAtB,IAApB;IACZ,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACwB,MAAM,KAAK,CAAC,EAAE;IAChC;;;;;IAKA,IAAMpC,SAAS,GAAG,IAAI,CAACgI,eAAe,CAACvI,MAAM,CAACqG,SAAS,CAAC,CAAC;IAEzD,IAAI,CAACQ,WAAW,CAACtG,SAAS,CAACgC,YAAY,EAAE,CAAC;IAC1C,IAAI,CAACyF,YAAY,EAAE;EACrB,CAAC;EAEOnH,aAAA,CAAAwB,SAAA,CAAAyE,mBAAmB,GAA3B,UAA4B6B,IAAY,EAAEC,OAAY;IACpD,IAAMC,GAAG,GAAG,IAAItJ,WAAW,CAACoJ,IAAI,EAAE;MAChCG,MAAM,EAAEF;KACT,CAAC;IACF,IAAI,CAACG,aAAa,CAACF,GAAU,CAAC;EAChC,CAAC;EACH,OAAAhI,aAAC;AAAD,CAAC,CAtQkCnB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}