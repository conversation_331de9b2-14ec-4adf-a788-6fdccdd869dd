{"ast": null, "code": "import isObject from './is-object';\nimport isString from './is-string';\nimport isNumber from './is-number';\n/**\n * https://github.com/developit/dlv/blob/master/index.js\n * @param obj\n * @param path\n * @param value\n */\nexport default (function (obj, path, value) {\n  var o = obj;\n  var keyArr = isString(path) ? path.split('.') : path;\n  keyArr.forEach(function (key, idx) {\n    // 不是最后一个\n    if (idx < keyArr.length - 1) {\n      if (!isObject(o[key])) {\n        o[key] = isNumber(keyArr[idx + 1]) ? [] : {};\n      }\n      o = o[key];\n    } else {\n      o[key] = value;\n    }\n  });\n  return obj;\n});", "map": {"version": 3, "names": ["isObject", "isString", "isNumber", "obj", "path", "value", "o", "keyArr", "split", "for<PERSON>ach", "key", "idx", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/set.ts"], "sourcesContent": ["import isObject from './is-object';\nimport isString from './is-string';\nimport isNumber from './is-number';\n\n/**\n * https://github.com/developit/dlv/blob/master/index.js\n * @param obj\n * @param path\n * @param value\n */\nexport default (obj: any, path: string | any[], value: any): any => {\n  let o = obj;\n\n  const keyArr = isString(path) ? path.split('.') : path;\n\n  keyArr.forEach((key: string | number, idx: number) => {\n    // 不是最后一个\n    if (idx < keyArr.length - 1) {\n      if (!isObject(o[key])) {\n        o[key] = isNumber(keyArr[idx + 1]) ? [] : {};\n      }\n      o = o[key]\n    } else {\n      o[key] = value;\n    }\n  });\n\n  return obj;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,QAAQ,MAAM,aAAa;AAElC;;;;;;AAMA,gBAAe,UAACC,GAAQ,EAAEC,IAAoB,EAAEC,KAAU;EACxD,IAAIC,CAAC,GAAGH,GAAG;EAEX,IAAMI,MAAM,GAAGN,QAAQ,CAACG,IAAI,CAAC,GAAGA,IAAI,CAACI,KAAK,CAAC,GAAG,CAAC,GAAGJ,IAAI;EAEtDG,MAAM,CAACE,OAAO,CAAC,UAACC,GAAoB,EAAEC,GAAW;IAC/C;IACA,IAAIA,GAAG,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACZ,QAAQ,CAACM,CAAC,CAACI,GAAG,CAAC,CAAC,EAAE;QACrBJ,CAAC,CAACI,GAAG,CAAC,GAAGR,QAAQ,CAACK,MAAM,CAACI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;;MAE9CL,CAAC,GAAGA,CAAC,CAACI,GAAG,CAAC;KACX,MAAM;MACLJ,CAAC,CAACI,GAAG,CAAC,GAAGL,KAAK;;EAElB,CAAC,CAAC;EAEF,OAAOF,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}