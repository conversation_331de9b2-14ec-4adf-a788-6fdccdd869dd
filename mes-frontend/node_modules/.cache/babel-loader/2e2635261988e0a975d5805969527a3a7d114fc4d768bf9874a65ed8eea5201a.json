{"ast": null, "code": "import { __read, __values } from \"tslib\";\nimport { parseSeriesAttr, scale } from '../../../util';\nimport { getLabelVector } from '../guides/utils';\nimport { isAxisVertical } from '../guides/line';\nimport { Bounds } from './bounds';\nimport { contain } from './contain';\nimport { intersect } from './intersect';\n/**\n * 创建副轴包围盒\n * @returns return false if no crossSize, else croseBBox\n */\nfunction createCrossBBox(attr, padding) {\n  var type = attr.type,\n    labelDirection = attr.labelDirection,\n    crossSize = attr.crossSize;\n  if (!crossSize) return false;\n  if (type === 'arc') {\n    var center = attr.center,\n      radius = attr.radius;\n    var _a = __read(center, 2),\n      cx = _a[0],\n      cy = _a[1];\n    var size = labelDirection === 'negative' ? 0 : crossSize;\n    var dMin = -radius - size;\n    var dMax = radius + size;\n    var _b = __read(parseSeriesAttr(padding), 4),\n      top_1 = _b[0],\n      right_1 = _b[1],\n      bottom_1 = _b[2],\n      left_1 = _b[3];\n    // 假定始终为顺时针方向\n    return new Bounds(cx + dMin - left_1, cy + dMin - top_1, cx + dMax + right_1, cy + dMax + bottom_1);\n  }\n  var _c = __read(attr.startPos, 2),\n    sx = _c[0],\n    sy = _c[1],\n    _d = __read(attr.endPos, 2),\n    ex = _d[0],\n    ey = _d[1];\n  // 水平时取左右，垂直时取上下\n  var _e = __read(isAxisVertical(attr) ? [-padding, 0, padding, 0] : [0, padding, 0, -padding], 4),\n    top = _e[0],\n    right = _e[1],\n    bottom = _e[2],\n    left = _e[3];\n  var labelVector = getLabelVector(0, attr);\n  var diff = scale(labelVector, crossSize);\n  var bbox = new Bounds(sx, sy, ex, ey);\n  bbox.x1 += left;\n  bbox.y1 += top;\n  bbox.x2 += right + diff[0];\n  bbox.y2 += bottom + diff[1];\n  return bbox;\n}\nexport function boundTest(items, attr, margin) {\n  var e_1, _a;\n  var crossPadding = attr.crossPadding;\n  var resultSet = new Set();\n  var prev = null;\n  var crossBBox = createCrossBBox(attr, crossPadding);\n  var testContain = function (item) {\n    if (crossBBox) return contain(crossBBox, item);\n    return true;\n  };\n  var testIntersect = function (prevItem, currItem) {\n    if (!prevItem || !prevItem.firstChild) return true;\n    // Get the first child of the item(Text).\n    // @ts-ignore\n    return !intersect(prevItem.firstChild, currItem.firstChild, parseSeriesAttr(margin));\n  };\n  try {\n    for (var items_1 = __values(items), items_1_1 = items_1.next(); !items_1_1.done; items_1_1 = items_1.next()) {\n      var curr = items_1_1.value;\n      if (!testContain(curr)) {\n        resultSet.add(curr);\n      } else if (!prev || testIntersect(prev, curr)) {\n        prev = curr;\n      } else {\n        resultSet.add(prev);\n        resultSet.add(curr);\n      }\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (items_1_1 && !items_1_1.done && (_a = items_1.return)) _a.call(items_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  return Array.from(resultSet);\n}", "map": {"version": 3, "names": ["parseSeriesAttr", "scale", "getLabelVector", "isAxisVertical", "Bounds", "contain", "intersect", "createCrossBBox", "attr", "padding", "type", "labelDirection", "crossSize", "center", "radius", "_a", "__read", "cx", "cy", "size", "dMin", "dMax", "_b", "top_1", "right_1", "bottom_1", "left_1", "_c", "startPos", "sx", "sy", "_d", "endPos", "ex", "ey", "_e", "top", "right", "bottom", "left", "labelVector", "diff", "bbox", "x1", "y1", "x2", "y2", "boundTest", "items", "margin", "crossPadding", "resultSet", "Set", "prev", "crossBBox", "testContain", "item", "testIntersect", "prevItem", "currItem", "<PERSON><PERSON><PERSON><PERSON>", "items_1", "__values", "items_1_1", "next", "done", "curr", "value", "add", "Array", "from"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/utils/test.ts"], "sourcesContent": ["import type { DisplayObject } from '../../../shapes';\nimport type { SeriesAttr } from '../../../util';\nimport { parseSeriesAttr, scale } from '../../../util';\nimport { getLabelVector } from '../guides/utils';\nimport { isAxisVertical } from '../guides/line';\nimport type { AxisStyleProps, LinearAxisStyleProps } from '../types';\nimport { Bounds } from './bounds';\nimport { contain } from './contain';\nimport { intersect } from './intersect';\n\n/**\n * 创建副轴包围盒\n * @returns return false if no crossSize, else croseBBox\n */\nfunction createCrossBBox(attr: AxisStyleProps, padding: number) {\n  const { type, labelDirection, crossSize } = attr;\n  if (!crossSize) return false;\n\n  if (type === 'arc') {\n    const { center, radius } = attr;\n    const [cx, cy] = center;\n    const size = labelDirection === 'negative' ? 0 : crossSize;\n    const dMin = -radius - size;\n    const dMax = radius + size;\n    const [top, right, bottom, left] = parseSeriesAttr(padding);\n    // 假定始终为顺时针方向\n    return new Bounds(cx + dMin - left, cy + dMin - top, cx + dMax + right, cy + dMax + bottom);\n  }\n  const {\n    startPos: [sx, sy],\n    endPos: [ex, ey],\n  } = attr;\n  // 水平时取左右，垂直时取上下\n  const [top, right, bottom, left] = isAxisVertical(attr as Required<LinearAxisStyleProps>)\n    ? [-padding, 0, padding, 0]\n    : [0, padding, 0, -padding];\n\n  const labelVector = getLabelVector(0, attr as Required<AxisStyleProps>);\n  const diff = scale(labelVector, crossSize);\n  const bbox = new Bounds(sx, sy, ex, ey);\n  bbox.x1 += left;\n  bbox.y1 += top;\n  bbox.x2 += right + diff[0];\n  bbox.y2 += bottom + diff[1];\n  return bbox;\n}\n\nexport function boundTest<T extends DisplayObject>(items: T[], attr: AxisStyleProps, margin?: SeriesAttr): T[] {\n  const { crossPadding } = attr;\n  const resultSet = new Set<T>();\n  let prev: T | null = null;\n\n  const crossBBox = createCrossBBox(attr, crossPadding!);\n  const testContain = (item: T) => {\n    if (crossBBox) return contain(crossBBox, item);\n    return true;\n  };\n\n  const testIntersect = (prevItem: T, currItem: T) => {\n    if (!prevItem || !prevItem.firstChild) return true;\n    // Get the first child of the item(Text).\n    // @ts-ignore\n    return !intersect(prevItem.firstChild, currItem.firstChild, parseSeriesAttr(margin));\n  };\n\n  for (const curr of items) {\n    if (!testContain(curr)) {\n      resultSet.add(curr);\n    } else if (!prev || testIntersect(prev, curr)) {\n      prev = curr;\n    } else {\n      resultSet.add(prev);\n      resultSet.add(curr);\n    }\n  }\n\n  return Array.from(resultSet);\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAEC,KAAK,QAAQ,eAAe;AACtD,SAASC,cAAc,QAAQ,iBAAiB;AAChD,SAASC,cAAc,QAAQ,gBAAgB;AAE/C,SAASC,MAAM,QAAQ,UAAU;AACjC,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,SAAS,QAAQ,aAAa;AAEvC;;;;AAIA,SAASC,eAAeA,CAACC,IAAoB,EAAEC,OAAe;EACpD,IAAAC,IAAI,GAAgCF,IAAI,CAAAE,IAApC;IAAEC,cAAc,GAAgBH,IAAI,CAAAG,cAApB;IAAEC,SAAS,GAAKJ,IAAI,CAAAI,SAAT;EACvC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAE5B,IAAIF,IAAI,KAAK,KAAK,EAAE;IACV,IAAAG,MAAM,GAAaL,IAAI,CAAAK,MAAjB;MAAEC,MAAM,GAAKN,IAAI,CAAAM,MAAT;IAChB,IAAAC,EAAA,GAAAC,MAAA,CAAWH,MAAM;MAAhBI,EAAE,GAAAF,EAAA;MAAEG,EAAE,GAAAH,EAAA,GAAU;IACvB,IAAMI,IAAI,GAAGR,cAAc,KAAK,UAAU,GAAG,CAAC,GAAGC,SAAS;IAC1D,IAAMQ,IAAI,GAAG,CAACN,MAAM,GAAGK,IAAI;IAC3B,IAAME,IAAI,GAAGP,MAAM,GAAGK,IAAI;IACpB,IAAAG,EAAA,GAAAN,MAAA,CAA6BhB,eAAe,CAACS,OAAO,CAAC;MAApDc,KAAG,GAAAD,EAAA;MAAEE,OAAK,GAAAF,EAAA;MAAEG,QAAM,GAAAH,EAAA;MAAEI,MAAI,GAAAJ,EAAA,GAA4B;IAC3D;IACA,OAAO,IAAIlB,MAAM,CAACa,EAAE,GAAGG,IAAI,GAAGM,MAAI,EAAER,EAAE,GAAGE,IAAI,GAAGG,KAAG,EAAEN,EAAE,GAAGI,IAAI,GAAGG,OAAK,EAAEN,EAAE,GAAGG,IAAI,GAAGI,QAAM,CAAC;EAC7F;EAEE,IAAAE,EAAA,GAAAX,MAAA,CAEER,IAAI,CAAAoB,QAAA,IAFY;IAAPC,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA;IACjBI,EAAA,GAAAf,MAAA,CACER,IAAI,CAAAwB,MAAA,IADU;IAAPC,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA,GAAC;EAElB;EACM,IAAAI,EAAA,GAAAnB,MAAA,CAA6Bb,cAAc,CAACK,IAAsC,CAAC,GACrF,CAAC,CAACC,OAAO,EAAE,CAAC,EAAEA,OAAO,EAAE,CAAC,CAAC,GACzB,CAAC,CAAC,EAAEA,OAAO,EAAE,CAAC,EAAE,CAACA,OAAO,CAAC;IAFtB2B,GAAG,GAAAD,EAAA;IAAEE,KAAK,GAAAF,EAAA;IAAEG,MAAM,GAAAH,EAAA;IAAEI,IAAI,GAAAJ,EAAA,GAEF;EAE7B,IAAMK,WAAW,GAAGtC,cAAc,CAAC,CAAC,EAAEM,IAAgC,CAAC;EACvE,IAAMiC,IAAI,GAAGxC,KAAK,CAACuC,WAAW,EAAE5B,SAAS,CAAC;EAC1C,IAAM8B,IAAI,GAAG,IAAItC,MAAM,CAACyB,EAAE,EAAEC,EAAE,EAAEG,EAAE,EAAEC,EAAE,CAAC;EACvCQ,IAAI,CAACC,EAAE,IAAIJ,IAAI;EACfG,IAAI,CAACE,EAAE,IAAIR,GAAG;EACdM,IAAI,CAACG,EAAE,IAAIR,KAAK,GAAGI,IAAI,CAAC,CAAC,CAAC;EAC1BC,IAAI,CAACI,EAAE,IAAIR,MAAM,GAAGG,IAAI,CAAC,CAAC,CAAC;EAC3B,OAAOC,IAAI;AACb;AAEA,OAAM,SAAUK,SAASA,CAA0BC,KAAU,EAAExC,IAAoB,EAAEyC,MAAmB;;EAC9F,IAAAC,YAAY,GAAK1C,IAAI,CAAA0C,YAAT;EACpB,IAAMC,SAAS,GAAG,IAAIC,GAAG,EAAK;EAC9B,IAAIC,IAAI,GAAa,IAAI;EAEzB,IAAMC,SAAS,GAAG/C,eAAe,CAACC,IAAI,EAAE0C,YAAa,CAAC;EACtD,IAAMK,WAAW,GAAG,SAAAA,CAACC,IAAO;IAC1B,IAAIF,SAAS,EAAE,OAAOjD,OAAO,CAACiD,SAAS,EAAEE,IAAI,CAAC;IAC9C,OAAO,IAAI;EACb,CAAC;EAED,IAAMC,aAAa,GAAG,SAAAA,CAACC,QAAW,EAAEC,QAAW;IAC7C,IAAI,CAACD,QAAQ,IAAI,CAACA,QAAQ,CAACE,UAAU,EAAE,OAAO,IAAI;IAClD;IACA;IACA,OAAO,CAACtD,SAAS,CAACoD,QAAQ,CAACE,UAAU,EAAED,QAAQ,CAACC,UAAU,EAAE5D,eAAe,CAACiD,MAAM,CAAC,CAAC;EACtF,CAAC;;IAED,KAAmB,IAAAY,OAAA,GAAAC,QAAA,CAAAd,KAAK,GAAAe,SAAA,GAAAF,OAAA,CAAAG,IAAA,KAAAD,SAAA,CAAAE,IAAA,EAAAF,SAAA,GAAAF,OAAA,CAAAG,IAAA,IAAE;MAArB,IAAME,IAAI,GAAAH,SAAA,CAAAI,KAAA;MACb,IAAI,CAACZ,WAAW,CAACW,IAAI,CAAC,EAAE;QACtBf,SAAS,CAACiB,GAAG,CAACF,IAAI,CAAC;MACrB,CAAC,MAAM,IAAI,CAACb,IAAI,IAAII,aAAa,CAACJ,IAAI,EAAEa,IAAI,CAAC,EAAE;QAC7Cb,IAAI,GAAGa,IAAI;MACb,CAAC,MAAM;QACLf,SAAS,CAACiB,GAAG,CAACf,IAAI,CAAC;QACnBF,SAAS,CAACiB,GAAG,CAACF,IAAI,CAAC;MACrB;IACF;;;;;;;;;;;;EAEA,OAAOG,KAAK,CAACC,IAAI,CAACnB,SAAS,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}