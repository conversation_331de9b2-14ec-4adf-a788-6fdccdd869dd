{"ast": null, "code": "/**\n * Checks if the character is an A (arc-to) path command.\n */\nexport function isArcCommand(code) {\n  return (code | 0x20) === 0x61;\n}", "map": {"version": 3, "names": ["isArcCommand", "code"], "sources": ["path/parser/is-arc-command.ts"], "sourcesContent": [null], "mappings": "AAAA;;;AAGA,OAAM,SAAUA,YAAYA,CAACC,IAAY;EACvC,OAAO,CAACA,IAAI,GAAG,IAAI,MAAM,IAAI;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}