{"ast": null, "code": "import { min } from '@antv/vendor/d3-array';\nfunction targetDepth(d) {\n  return d.target.depth;\n}\nexport function left(node) {\n  return node.depth;\n}\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\nexport function center(node) {\n  return node.targetLinks.length ? node.depth : node.sourceLinks.length ? min(node.sourceLinks, targetDepth) - 1 : 0;\n}", "map": {"version": 3, "names": ["min", "targetDepth", "d", "target", "depth", "left", "node", "right", "n", "height", "justify", "sourceLinks", "length", "center", "targetLinks"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/d3-sankey/align.ts"], "sourcesContent": ["import { min } from '@antv/vendor/d3-array';\n\nfunction targetDepth(d) {\n  return d.target.depth;\n}\n\nexport function left(node) {\n  return node.depth;\n}\n\nexport function right(node, n) {\n  return n - 1 - node.height;\n}\n\nexport function justify(node, n) {\n  return node.sourceLinks.length ? node.depth : n - 1;\n}\n\nexport function center(node) {\n  return node.targetLinks.length\n    ? node.depth\n    : node.sourceLinks.length\n    ? (min(node.sourceLinks, targetDepth) as any) - 1\n    : 0;\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,uBAAuB;AAE3C,SAASC,WAAWA,CAACC,CAAC;EACpB,OAAOA,CAAC,CAACC,MAAM,CAACC,KAAK;AACvB;AAEA,OAAM,SAAUC,IAAIA,CAACC,IAAI;EACvB,OAAOA,IAAI,CAACF,KAAK;AACnB;AAEA,OAAM,SAAUG,KAAKA,CAACD,IAAI,EAAEE,CAAC;EAC3B,OAAOA,CAAC,GAAG,CAAC,GAAGF,IAAI,CAACG,MAAM;AAC5B;AAEA,OAAM,SAAUC,OAAOA,CAACJ,IAAI,EAAEE,CAAC;EAC7B,OAAOF,IAAI,CAACK,WAAW,CAACC,MAAM,GAAGN,IAAI,CAACF,KAAK,GAAGI,CAAC,GAAG,CAAC;AACrD;AAEA,OAAM,SAAUK,MAAMA,CAACP,IAAI;EACzB,OAAOA,IAAI,CAACQ,WAAW,CAACF,MAAM,GAC1BN,IAAI,CAACF,KAAK,GACVE,IAAI,CAACK,WAAW,CAACC,MAAM,GACtBZ,GAAG,CAACM,IAAI,CAACK,WAAW,EAAEV,WAAW,CAAS,GAAG,CAAC,GAC/C,CAAC;AACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}