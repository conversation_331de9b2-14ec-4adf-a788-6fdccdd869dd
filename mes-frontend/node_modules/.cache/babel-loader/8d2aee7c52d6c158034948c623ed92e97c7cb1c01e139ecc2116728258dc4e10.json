{"ast": null, "code": "export var VACANT_EDGE_ID = -1;\nexport var VACANT_NODE_ID = -1;\nexport var VACANT_EDGE_LABEL = \"-1\";\nexport var VACANT_NODE_LABEL = \"-1\";\nexport var VACANT_GRAPH_ID = -1;\nexport var AUTO_EDGE_ID = \"-1\";\nvar Edge = /** @class */function () {\n  function Edge(id, from, to, label) {\n    if (id === void 0) {\n      id = VACANT_EDGE_ID;\n    }\n    if (from === void 0) {\n      from = VACANT_NODE_ID;\n    }\n    if (to === void 0) {\n      to = VACANT_NODE_ID;\n    }\n    if (label === void 0) {\n      label = VACANT_EDGE_LABEL;\n    }\n    this.id = id;\n    this.from = from;\n    this.to = to;\n    this.label = label;\n  }\n  return Edge;\n}();\nexport { Edge };\nvar Node = /** @class */function () {\n  function Node(id, label) {\n    if (id === void 0) {\n      id = VACANT_NODE_ID;\n    }\n    if (label === void 0) {\n      label = VACANT_NODE_LABEL;\n    }\n    this.id = id;\n    this.label = label;\n    this.edges = [];\n    this.edgeMap = {};\n  }\n  Node.prototype.addEdge = function (edge) {\n    this.edges.push(edge);\n    this.edgeMap[edge.id] = edge;\n  };\n  return Node;\n}();\nexport { Node };\nvar Graph = /** @class */function () {\n  function Graph(id, edgeIdAutoIncrease, directed) {\n    if (id === void 0) {\n      id = VACANT_NODE_ID;\n    }\n    if (edgeIdAutoIncrease === void 0) {\n      edgeIdAutoIncrease = true;\n    }\n    if (directed === void 0) {\n      directed = false;\n    }\n    this.id = id;\n    this.edgeIdAutoIncrease = edgeIdAutoIncrease;\n    this.edges = [];\n    this.nodes = [];\n    this.nodeMap = {};\n    this.edgeMap = {};\n    this.nodeLabelMap = {};\n    this.edgeLabelMap = {};\n    this.counter = 0;\n    this.directed = directed;\n  }\n  Graph.prototype.getNodeNum = function () {\n    return this.nodes.length;\n  };\n  Graph.prototype.addNode = function (id, label) {\n    if (this.nodeMap[id]) return;\n    var node = new Node(id, label);\n    this.nodes.push(node);\n    this.nodeMap[id] = node;\n    if (!this.nodeLabelMap[label]) this.nodeLabelMap[label] = [];\n    this.nodeLabelMap[label].push(id);\n  };\n  Graph.prototype.addEdge = function (id, from, to, label) {\n    if (this.edgeIdAutoIncrease || id === undefined) id = this.counter++;\n    if (this.nodeMap[from] && this.nodeMap[to] && this.nodeMap[to].edgeMap[id]) return;\n    var edge = new Edge(id, from, to, label);\n    this.edges.push(edge);\n    this.edgeMap[id] = edge;\n    this.nodeMap[from].addEdge(edge);\n    if (!this.edgeLabelMap[label]) this.edgeLabelMap[label] = [];\n    this.edgeLabelMap[label].push(edge);\n    if (!this.directed) {\n      var rEdge = new Edge(id, to, from, label);\n      this.nodeMap[to].addEdge(rEdge);\n      this.edgeLabelMap[label].push(rEdge);\n    }\n  };\n  return Graph;\n}();\nexport { Graph };", "map": {"version": 3, "names": ["VACANT_EDGE_ID", "VACANT_NODE_ID", "VACANT_EDGE_LABEL", "VACANT_NODE_LABEL", "VACANT_GRAPH_ID", "AUTO_EDGE_ID", "Edge", "id", "from", "to", "label", "Node", "edges", "edgeMap", "prototype", "addEdge", "edge", "push", "Graph", "edgeIdAutoIncrease", "directed", "nodes", "nodeMap", "nodeLabelMap", "edgeLabelMap", "counter", "getNodeNum", "length", "addNode", "node", "undefined", "rEdge"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/es/gSpan/struct.js"], "sourcesContent": ["export var VACANT_EDGE_ID = -1;\nexport var VACANT_NODE_ID = -1;\nexport var VACANT_EDGE_LABEL = \"-1\";\nexport var VACANT_NODE_LABEL = \"-1\";\nexport var VACANT_GRAPH_ID = -1;\nexport var AUTO_EDGE_ID = \"-1\";\nvar Edge = /** @class */function () {\n  function Edge(id, from, to, label) {\n    if (id === void 0) {\n      id = VACANT_EDGE_ID;\n    }\n    if (from === void 0) {\n      from = VACANT_NODE_ID;\n    }\n    if (to === void 0) {\n      to = VACANT_NODE_ID;\n    }\n    if (label === void 0) {\n      label = VACANT_EDGE_LABEL;\n    }\n    this.id = id;\n    this.from = from;\n    this.to = to;\n    this.label = label;\n  }\n  return Edge;\n}();\nexport { Edge };\nvar Node = /** @class */function () {\n  function Node(id, label) {\n    if (id === void 0) {\n      id = VACANT_NODE_ID;\n    }\n    if (label === void 0) {\n      label = VACANT_NODE_LABEL;\n    }\n    this.id = id;\n    this.label = label;\n    this.edges = [];\n    this.edgeMap = {};\n  }\n  Node.prototype.addEdge = function (edge) {\n    this.edges.push(edge);\n    this.edgeMap[edge.id] = edge;\n  };\n  return Node;\n}();\nexport { Node };\nvar Graph = /** @class */function () {\n  function Graph(id, edgeIdAutoIncrease, directed) {\n    if (id === void 0) {\n      id = VACANT_NODE_ID;\n    }\n    if (edgeIdAutoIncrease === void 0) {\n      edgeIdAutoIncrease = true;\n    }\n    if (directed === void 0) {\n      directed = false;\n    }\n    this.id = id;\n    this.edgeIdAutoIncrease = edgeIdAutoIncrease;\n    this.edges = [];\n    this.nodes = [];\n    this.nodeMap = {};\n    this.edgeMap = {};\n    this.nodeLabelMap = {};\n    this.edgeLabelMap = {};\n    this.counter = 0;\n    this.directed = directed;\n  }\n  Graph.prototype.getNodeNum = function () {\n    return this.nodes.length;\n  };\n  Graph.prototype.addNode = function (id, label) {\n    if (this.nodeMap[id]) return;\n    var node = new Node(id, label);\n    this.nodes.push(node);\n    this.nodeMap[id] = node;\n    if (!this.nodeLabelMap[label]) this.nodeLabelMap[label] = [];\n    this.nodeLabelMap[label].push(id);\n  };\n  Graph.prototype.addEdge = function (id, from, to, label) {\n    if (this.edgeIdAutoIncrease || id === undefined) id = this.counter++;\n    if (this.nodeMap[from] && this.nodeMap[to] && this.nodeMap[to].edgeMap[id]) return;\n    var edge = new Edge(id, from, to, label);\n    this.edges.push(edge);\n    this.edgeMap[id] = edge;\n    this.nodeMap[from].addEdge(edge);\n    if (!this.edgeLabelMap[label]) this.edgeLabelMap[label] = [];\n    this.edgeLabelMap[label].push(edge);\n    if (!this.directed) {\n      var rEdge = new Edge(id, to, from, label);\n      this.nodeMap[to].addEdge(rEdge);\n      this.edgeLabelMap[label].push(rEdge);\n    }\n  };\n  return Graph;\n}();\nexport { Graph };"], "mappings": "AAAA,OAAO,IAAIA,cAAc,GAAG,CAAC,CAAC;AAC9B,OAAO,IAAIC,cAAc,GAAG,CAAC,CAAC;AAC9B,OAAO,IAAIC,iBAAiB,GAAG,IAAI;AACnC,OAAO,IAAIC,iBAAiB,GAAG,IAAI;AACnC,OAAO,IAAIC,eAAe,GAAG,CAAC,CAAC;AAC/B,OAAO,IAAIC,YAAY,GAAG,IAAI;AAC9B,IAAIC,IAAI,GAAG,aAAa,YAAY;EAClC,SAASA,IAAIA,CAACC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAEC,KAAK,EAAE;IACjC,IAAIH,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBA,EAAE,GAAGP,cAAc;IACrB;IACA,IAAIQ,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAGP,cAAc;IACvB;IACA,IAAIQ,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBA,EAAE,GAAGR,cAAc;IACrB;IACA,IAAIS,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAGR,iBAAiB;IAC3B;IACA,IAAI,CAACK,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;EACA,OAAOJ,IAAI;AACb,CAAC,CAAC,CAAC;AACH,SAASA,IAAI;AACb,IAAIK,IAAI,GAAG,aAAa,YAAY;EAClC,SAASA,IAAIA,CAACJ,EAAE,EAAEG,KAAK,EAAE;IACvB,IAAIH,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBA,EAAE,GAAGN,cAAc;IACrB;IACA,IAAIS,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAGP,iBAAiB;IAC3B;IACA,IAAI,CAACI,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACE,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;EACnB;EACAF,IAAI,CAACG,SAAS,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;IACvC,IAAI,CAACJ,KAAK,CAACK,IAAI,CAACD,IAAI,CAAC;IACrB,IAAI,CAACH,OAAO,CAACG,IAAI,CAACT,EAAE,CAAC,GAAGS,IAAI;EAC9B,CAAC;EACD,OAAOL,IAAI;AACb,CAAC,CAAC,CAAC;AACH,SAASA,IAAI;AACb,IAAIO,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACX,EAAE,EAAEY,kBAAkB,EAAEC,QAAQ,EAAE;IAC/C,IAAIb,EAAE,KAAK,KAAK,CAAC,EAAE;MACjBA,EAAE,GAAGN,cAAc;IACrB;IACA,IAAIkB,kBAAkB,KAAK,KAAK,CAAC,EAAE;MACjCA,kBAAkB,GAAG,IAAI;IAC3B;IACA,IAAIC,QAAQ,KAAK,KAAK,CAAC,EAAE;MACvBA,QAAQ,GAAG,KAAK;IAClB;IACA,IAAI,CAACb,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACY,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACP,KAAK,GAAG,EAAE;IACf,IAAI,CAACS,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACT,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAACU,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACL,QAAQ,GAAGA,QAAQ;EAC1B;EACAF,KAAK,CAACJ,SAAS,CAACY,UAAU,GAAG,YAAY;IACvC,OAAO,IAAI,CAACL,KAAK,CAACM,MAAM;EAC1B,CAAC;EACDT,KAAK,CAACJ,SAAS,CAACc,OAAO,GAAG,UAAUrB,EAAE,EAAEG,KAAK,EAAE;IAC7C,IAAI,IAAI,CAACY,OAAO,CAACf,EAAE,CAAC,EAAE;IACtB,IAAIsB,IAAI,GAAG,IAAIlB,IAAI,CAACJ,EAAE,EAAEG,KAAK,CAAC;IAC9B,IAAI,CAACW,KAAK,CAACJ,IAAI,CAACY,IAAI,CAAC;IACrB,IAAI,CAACP,OAAO,CAACf,EAAE,CAAC,GAAGsB,IAAI;IACvB,IAAI,CAAC,IAAI,CAACN,YAAY,CAACb,KAAK,CAAC,EAAE,IAAI,CAACa,YAAY,CAACb,KAAK,CAAC,GAAG,EAAE;IAC5D,IAAI,CAACa,YAAY,CAACb,KAAK,CAAC,CAACO,IAAI,CAACV,EAAE,CAAC;EACnC,CAAC;EACDW,KAAK,CAACJ,SAAS,CAACC,OAAO,GAAG,UAAUR,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAEC,KAAK,EAAE;IACvD,IAAI,IAAI,CAACS,kBAAkB,IAAIZ,EAAE,KAAKuB,SAAS,EAAEvB,EAAE,GAAG,IAAI,CAACkB,OAAO,EAAE;IACpE,IAAI,IAAI,CAACH,OAAO,CAACd,IAAI,CAAC,IAAI,IAAI,CAACc,OAAO,CAACb,EAAE,CAAC,IAAI,IAAI,CAACa,OAAO,CAACb,EAAE,CAAC,CAACI,OAAO,CAACN,EAAE,CAAC,EAAE;IAC5E,IAAIS,IAAI,GAAG,IAAIV,IAAI,CAACC,EAAE,EAAEC,IAAI,EAAEC,EAAE,EAAEC,KAAK,CAAC;IACxC,IAAI,CAACE,KAAK,CAACK,IAAI,CAACD,IAAI,CAAC;IACrB,IAAI,CAACH,OAAO,CAACN,EAAE,CAAC,GAAGS,IAAI;IACvB,IAAI,CAACM,OAAO,CAACd,IAAI,CAAC,CAACO,OAAO,CAACC,IAAI,CAAC;IAChC,IAAI,CAAC,IAAI,CAACQ,YAAY,CAACd,KAAK,CAAC,EAAE,IAAI,CAACc,YAAY,CAACd,KAAK,CAAC,GAAG,EAAE;IAC5D,IAAI,CAACc,YAAY,CAACd,KAAK,CAAC,CAACO,IAAI,CAACD,IAAI,CAAC;IACnC,IAAI,CAAC,IAAI,CAACI,QAAQ,EAAE;MAClB,IAAIW,KAAK,GAAG,IAAIzB,IAAI,CAACC,EAAE,EAAEE,EAAE,EAAED,IAAI,EAAEE,KAAK,CAAC;MACzC,IAAI,CAACY,OAAO,CAACb,EAAE,CAAC,CAACM,OAAO,CAACgB,KAAK,CAAC;MAC/B,IAAI,CAACP,YAAY,CAACd,KAAK,CAAC,CAACO,IAAI,CAACc,KAAK,CAAC;IACtC;EACF,CAAC;EACD,OAAOb,KAAK;AACd,CAAC,CAAC,CAAC;AACH,SAASA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}