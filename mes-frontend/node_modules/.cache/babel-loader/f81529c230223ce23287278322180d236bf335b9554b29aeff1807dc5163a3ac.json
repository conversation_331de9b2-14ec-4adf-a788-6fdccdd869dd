{"ast": null, "code": "export { default as fadeIn } from './fadeIn';\nexport { default as fadeOut } from './fadeOut';\nexport * from './utils';", "map": {"version": 3, "names": ["default", "fadeIn", "fadeOut"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/animation/index.ts"], "sourcesContent": ["export { default as fadeIn } from './fadeIn';\nexport { default as fadeOut } from './fadeOut';\nexport type { StandardAnimationOption, AnimationOption, GenericAnimation, AnimationResult } from './types';\nexport * from './utils';\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,MAAM,QAAQ,UAAU;AAC5C,SAASD,OAAO,IAAIE,OAAO,QAAQ,WAAW;AAE9C,cAAc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}