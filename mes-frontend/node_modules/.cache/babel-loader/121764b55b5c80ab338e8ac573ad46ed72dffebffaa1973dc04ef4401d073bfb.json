{"ast": null, "code": "import { Graph } from '@antv/graphlib';\nimport { isNumber } from '@antv/util';\nconst safeSort = (valueA, valueB) => {\n  return Number(valueA) - Number(valueB);\n};\n/*\n * Adds a dummy node to the graph and return v.\n */\nexport const addDummyNode = (g, type, data, name) => {\n  let v;\n  do {\n    v = `${name}${Math.random()}`;\n  } while (g.hasNode(v));\n  data.dummy = type;\n  g.addNode({\n    id: v,\n    data\n  });\n  return v;\n};\n/*\n * Returns a new graph with only simple edges. Handles aggregation of data\n * associated with multi-edges.\n */\nexport const simplify = g => {\n  const simplified = new Graph();\n  g.getAllNodes().forEach(v => {\n    simplified.addNode(Object.assign({}, v));\n  });\n  g.getAllEdges().forEach(e => {\n    const edge = simplified.getRelatedEdges(e.source, 'out').find(edge => edge.target === e.target);\n    if (!edge) {\n      simplified.addEdge({\n        id: e.id,\n        source: e.source,\n        target: e.target,\n        data: {\n          weight: e.data.weight || 0,\n          minlen: e.data.minlen || 1\n        }\n      });\n    } else {\n      simplified.updateEdgeData(edge === null || edge === void 0 ? void 0 : edge.id, Object.assign(Object.assign({}, edge.data), {\n        weight: edge.data.weight + e.data.weight || 0,\n        minlen: Math.max(edge.data.minlen, e.data.minlen || 1)\n      }));\n    }\n  });\n  return simplified;\n};\nexport const asNonCompoundGraph = g => {\n  const simplified = new Graph();\n  g.getAllNodes().forEach(node => {\n    if (!g.getChildren(node.id).length) {\n      simplified.addNode(Object.assign({}, node));\n    }\n  });\n  g.getAllEdges().forEach(edge => {\n    simplified.addEdge(edge);\n  });\n  return simplified;\n};\nexport const zipObject = (keys, values) => {\n  return keys === null || keys === void 0 ? void 0 : keys.reduce((obj, key, i) => {\n    obj[key] = values[i];\n    return obj;\n  }, {});\n};\nexport const successorWeights = g => {\n  const weightsMap = {};\n  g.getAllNodes().forEach(node => {\n    const sucs = {};\n    g.getRelatedEdges(node.id, 'out').forEach(e => {\n      sucs[e.target] = (sucs[e.target] || 0) + (e.data.weight || 0);\n    });\n    weightsMap[node.id] = sucs;\n  });\n  return weightsMap;\n};\nexport const predecessorWeights = g => {\n  const nodes = g.getAllNodes();\n  const weightMap = nodes.map(v => {\n    const preds = {};\n    g.getRelatedEdges(v.id, 'in').forEach(e => {\n      preds[e.source] = (preds[e.source] || 0) + e.data.weight;\n    });\n    return preds;\n  });\n  return zipObject(nodes.map(n => n.id), weightMap);\n};\n/*\n * Finds where a line starting at point ({x, y}) would intersect a rectangle\n * ({x, y, width, height}) if it were pointing at the rectangle's center.\n */\nexport const intersectRect = (rect, point) => {\n  const x = Number(rect.x);\n  const y = Number(rect.y);\n  // Rectangle intersection algorithm from:\n  // http://math.stackexchange.com/questions/108113/find-edge-between-two-boxes\n  const dx = Number(point.x) - x;\n  const dy = Number(point.y) - y;\n  let w = Number(rect.width) / 2;\n  let h = Number(rect.height) / 2;\n  if (!dx && !dy) {\n    // completely overlapped directly, then return points its self\n    return {\n      x: 0,\n      y: 0\n    };\n  }\n  let sx;\n  let sy;\n  if (Math.abs(dy) * w > Math.abs(dx) * h) {\n    // Intersection is top or bottom of rect.\n    if (dy < 0) {\n      h = -h;\n    }\n    sx = h * dx / dy;\n    sy = h;\n  } else {\n    // Intersection is left or right of rect.\n    if (dx < 0) {\n      w = -w;\n    }\n    sx = w;\n    sy = w * dy / dx;\n  }\n  return {\n    x: x + sx,\n    y: y + sy\n  };\n};\n/*\n * Given a DAG with each node assigned \"rank\" and \"order\" properties, this\n * const will produce a matrix with the ids of each node.\n */\nexport const buildLayerMatrix = g => {\n  const layeringNodes = [];\n  const rankMax = maxRank(g) + 1;\n  for (let i = 0; i < rankMax; i++) {\n    layeringNodes.push([]);\n  }\n  // const layering = _.map(_.range(maxRank(g) + 1), function() { return []; });\n  g.getAllNodes().forEach(node => {\n    const rank = node.data.rank;\n    if (rank !== undefined && layeringNodes[rank]) {\n      layeringNodes[rank].push(node.id);\n    }\n  });\n  for (let i = 0; i < rankMax; i++) {\n    layeringNodes[i] = layeringNodes[i].sort((va, vb) => safeSort(g.getNode(va).data.order, g.getNode(vb).data.order));\n  }\n  return layeringNodes;\n};\n/*\n * Adjusts the ranks for all nodes in the graph such that all nodes v have\n * rank(v) >= 0 and at least one node w has rank(w) = 0.\n */\nexport const normalizeRanks = g => {\n  const nodeRanks = g.getAllNodes().filter(v => v.data.rank !== undefined).map(v => v.data.rank);\n  const min = Math.min(...nodeRanks);\n  g.getAllNodes().forEach(v => {\n    if (v.data.hasOwnProperty('rank') && min !== Infinity) {\n      v.data.rank -= min;\n    }\n  });\n};\nexport const removeEmptyRanks = (g, nodeRankFactor = 0) => {\n  // Ranks may not start at 0, so we need to offset them\n  const nodes = g.getAllNodes();\n  const nodeRanks = nodes.filter(v => v.data.rank !== undefined).map(v => v.data.rank);\n  const offset = Math.min(...nodeRanks);\n  const layers = [];\n  nodes.forEach(v => {\n    const rank = (v.data.rank || 0) - offset;\n    if (!layers[rank]) {\n      layers[rank] = [];\n    }\n    layers[rank].push(v.id);\n  });\n  let delta = 0;\n  for (let i = 0; i < layers.length; i++) {\n    const vs = layers[i];\n    if (vs === undefined) {\n      if (i % nodeRankFactor !== 0) {\n        delta -= 1;\n      }\n    } else if (delta) {\n      vs === null || vs === void 0 ? void 0 : vs.forEach(v => {\n        const node = g.getNode(v);\n        if (node) {\n          node.data.rank = node.data.rank || 0;\n          node.data.rank += delta;\n        }\n      });\n    }\n  }\n};\nexport const addBorderNode = (g, prefix, rank, order) => {\n  const node = {\n    width: 0,\n    height: 0\n  };\n  if (isNumber(rank) && isNumber(order)) {\n    node.rank = rank;\n    node.order = order;\n  }\n  return addDummyNode(g, 'border', node, prefix);\n};\nexport const maxRank = g => {\n  let maxRank;\n  g.getAllNodes().forEach(v => {\n    const rank = v.data.rank;\n    if (rank !== undefined) {\n      if (maxRank === undefined || rank > maxRank) {\n        maxRank = rank;\n      }\n    }\n  });\n  if (!maxRank) {\n    maxRank = 0;\n  }\n  return maxRank;\n};\n/*\n * Partition a collection into two groups: `lhs` and `rhs`. If the supplied\n * const returns true for an entry it goes into `lhs`. Otherwise it goes\n * into `rhs.\n */\nexport const partition = (collection, fn) => {\n  const result = {\n    lhs: [],\n    rhs: []\n  };\n  collection === null || collection === void 0 ? void 0 : collection.forEach(value => {\n    if (fn(value)) {\n      result.lhs.push(value);\n    } else {\n      result.rhs.push(value);\n    }\n  });\n  return result;\n};\nexport const minBy = (array, func) => {\n  return array.reduce((a, b) => {\n    const valA = func(a);\n    const valB = func(b);\n    return valA > valB ? b : a;\n  });\n};\nconst doDFS = (graph, node, postorder, visited, navigator, result) => {\n  if (!visited.includes(node.id)) {\n    visited.push(node.id);\n    if (!postorder) {\n      result.push(node.id);\n    }\n    navigator(node.id).forEach(n => doDFS(graph, n, postorder, visited, navigator, result));\n    if (postorder) {\n      result.push(node.id);\n    }\n  }\n};\n/**\n * @description DFS traversal.\n * @description.zh-CN DFS 遍历。\n */\nexport const dfs = (graph, node, order, isDirected) => {\n  const nodes = Array.isArray(node) ? node : [node];\n  const navigator = n => isDirected ? graph.getSuccessors(n) : graph.getNeighbors(n);\n  const results = [];\n  const visited = [];\n  nodes.forEach(node => {\n    if (!graph.hasNode(node.id)) {\n      throw new Error(`Graph does not have node: ${node}`);\n    } else {\n      doDFS(graph, node, order === 'post', visited, navigator, results);\n    }\n  });\n  return results;\n};", "map": {"version": 3, "names": ["Graph", "isNumber", "safeSort", "valueA", "valueB", "Number", "addDummyNode", "g", "type", "data", "name", "v", "Math", "random", "hasNode", "dummy", "addNode", "id", "simplify", "simplified", "getAllNodes", "for<PERSON>ach", "Object", "assign", "getAllEdges", "e", "edge", "getRelatedEdges", "source", "find", "target", "addEdge", "weight", "minlen", "updateEdgeData", "max", "asNonCompoundGraph", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "zipObject", "keys", "values", "reduce", "obj", "key", "i", "successorWeights", "weightsMap", "sucs", "predecessorWeights", "nodes", "weightMap", "map", "preds", "n", "intersectRect", "rect", "point", "x", "y", "dx", "dy", "w", "width", "h", "height", "sx", "sy", "abs", "buildLayerMatrix", "layeringNodes", "rankMax", "maxRank", "push", "rank", "undefined", "sort", "va", "vb", "getNode", "order", "normalizeRanks", "nodeRanks", "filter", "min", "hasOwnProperty", "Infinity", "removeEmptyRanks", "nodeRankFactor", "offset", "layers", "delta", "vs", "addBorderNode", "prefix", "partition", "collection", "fn", "result", "lhs", "rhs", "value", "minBy", "array", "func", "a", "b", "valA", "valB", "doDFS", "graph", "postorder", "visited", "navigator", "includes", "dfs", "isDirected", "Array", "isArray", "getSuccessors", "getNeighbors", "results", "Error"], "sources": ["../../src/antv-dagre/util.ts"], "sourcesContent": [null], "mappings": "AAAA,SAASA,KAAK,QAAkB,gBAAgB;AAChD,SAASC,QAAQ,QAAQ,YAAY;AAGrC,MAAMC,QAAQ,GAAGA,CAACC,MAAe,EAAEC,MAAe,KAAI;EACpD,OAAOC,MAAM,CAACF,MAAM,CAAC,GAAGE,MAAM,CAACD,MAAM,CAAC;AACxC,CAAC;AAED;;;AAGA,OAAO,MAAME,YAAY,GAAGA,CAC1BC,CAAS,EACTC,IAAY,EACZC,IAAc,EACdC,IAAY,KACN;EACN,IAAIC,CAAK;EACT,GAAG;IACDA,CAAC,GAAG,GAAGD,IAAI,GAAGE,IAAI,CAACC,MAAM,EAAE,EAAE;GAC9B,QAAQN,CAAC,CAACO,OAAO,CAACH,CAAC,CAAC;EAErBF,IAAI,CAACM,KAAK,GAAGP,IAAI;EACjBD,CAAC,CAACS,OAAO,CAAC;IACRC,EAAE,EAAEN,CAAC;IACLF;GACD,CAAC;EAEF,OAAOE,CAAC;AACV,CAAC;AAED;;;;AAIA,OAAO,MAAMO,QAAQ,GAAIX,CAAS,IAAI;EACpC,MAAMY,UAAU,GAAG,IAAInB,KAAK,EAAsB;EAClDO,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEV,CAAC,IAAI;IAC5BQ,UAAU,CAACH,OAAO,CAAAM,MAAA,CAAAC,MAAA,KAAMZ,CAAC,EAAG;EAC9B,CAAC,CAAC;EACFJ,CAAC,CAACiB,WAAW,EAAE,CAACH,OAAO,CAAEI,CAAC,IAAI;IAC5B,MAAMC,IAAI,GAAGP,UAAU,CACpBQ,eAAe,CAACF,CAAC,CAACG,MAAM,EAAE,KAAK,CAAC,CAChCC,IAAI,CAAEH,IAAI,IAAKA,IAAI,CAACI,MAAM,KAAKL,CAAC,CAACK,MAAM,CAAC;IAC3C,IAAI,CAACJ,IAAI,EAAE;MACTP,UAAU,CAACY,OAAO,CAAC;QACjBd,EAAE,EAAEQ,CAAC,CAACR,EAAE;QACRW,MAAM,EAAEH,CAAC,CAACG,MAAM;QAChBE,MAAM,EAAEL,CAAC,CAACK,MAAM;QAChBrB,IAAI,EAAE;UACJuB,MAAM,EAAEP,CAAC,CAAChB,IAAI,CAACuB,MAAO,IAAI,CAAC;UAC3BC,MAAM,EAAER,CAAC,CAAChB,IAAI,CAACwB,MAAO,IAAI;;OAE7B,CAAC;KACH,MAAM;MACLd,UAAU,CAACe,cAAc,CAACR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAET,EAAG,EAAAK,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAC9BG,IAAI,CAACjB,IAAI;QACZuB,MAAM,EAAEN,IAAI,CAACjB,IAAI,CAACuB,MAAO,GAAGP,CAAC,CAAChB,IAAI,CAACuB,MAAO,IAAI,CAAC;QAC/CC,MAAM,EAAErB,IAAI,CAACuB,GAAG,CAACT,IAAI,CAACjB,IAAI,CAACwB,MAAO,EAAER,CAAC,CAAChB,IAAI,CAACwB,MAAO,IAAI,CAAC;MAAC,GACxD;;EAEN,CAAC,CAAC;EACF,OAAOd,UAAU;AACnB,CAAC;AAED,OAAO,MAAMiB,kBAAkB,GAAI7B,CAAS,IAAY;EACtD,MAAMY,UAAU,GAAG,IAAInB,KAAK,EAAE;EAE9BO,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEgB,IAAI,IAAI;IAC/B,IAAI,CAAC9B,CAAC,CAAC+B,WAAW,CAACD,IAAI,CAACpB,EAAE,CAAC,CAACsB,MAAM,EAAE;MAClCpB,UAAU,CAACH,OAAO,CAAAM,MAAA,CAAAC,MAAA,KAAMc,IAAI,EAAG;;EAEnC,CAAC,CAAC;EAEF9B,CAAC,CAACiB,WAAW,EAAE,CAACH,OAAO,CAAEK,IAAI,IAAI;IAC/BP,UAAU,CAACY,OAAO,CAACL,IAAI,CAAC;EAC1B,CAAC,CAAC;EAEF,OAAOP,UAAU;AACnB,CAAC;AAED,OAAO,MAAMqB,SAAS,GAAGA,CAAUC,IAAU,EAAEC,MAAW,KAAI;EAC5D,OAAOD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,EAAEC,CAAC,KAAI;IAClCF,GAAG,CAACC,GAAG,CAAC,GAAGH,MAAM,CAACI,CAAC,CAAC;IACpB,OAAOF,GAAG;EACZ,CAAC,EAAE,EAAmB,CAAC;AACzB,CAAC;AAED,OAAO,MAAMG,gBAAgB,GAAIxC,CAAS,IAAI;EAC5C,MAAMyC,UAAU,GAAuC,EAAE;EAEzDzC,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEgB,IAAI,IAAI;IAC/B,MAAMY,IAAI,GAAuB,EAAE;IACnC1C,CAAC,CAACoB,eAAe,CAACU,IAAI,CAACpB,EAAE,EAAE,KAAK,CAAC,CAACI,OAAO,CAAEI,CAAC,IAAI;MAC9CwB,IAAI,CAACxB,CAAC,CAACK,MAAM,CAAC,GAAG,CAACmB,IAAI,CAACxB,CAAC,CAACK,MAAM,CAAC,IAAI,CAAC,KAAKL,CAAC,CAAChB,IAAI,CAACuB,MAAM,IAAI,CAAC,CAAC;IAC/D,CAAC,CAAC;IACFgB,UAAU,CAACX,IAAI,CAACpB,EAAE,CAAC,GAAGgC,IAAI;EAC5B,CAAC,CAAC;EAEF,OAAOD,UAAU;AACnB,CAAC;AAED,OAAO,MAAME,kBAAkB,GAAI3C,CAAS,IAAI;EAC9C,MAAM4C,KAAK,GAAG5C,CAAC,CAACa,WAAW,EAAE;EAE7B,MAAMgC,SAAS,GAAGD,KAAK,CAACE,GAAG,CAAE1C,CAAC,IAAI;IAChC,MAAM2C,KAAK,GAAuB,EAAE;IACpC/C,CAAC,CAACoB,eAAe,CAAChB,CAAC,CAACM,EAAE,EAAE,IAAI,CAAC,CAACI,OAAO,CAAEI,CAAC,IAAI;MAC1C6B,KAAK,CAAC7B,CAAC,CAACG,MAAM,CAAC,GAAG,CAAC0B,KAAK,CAAC7B,CAAC,CAACG,MAAM,CAAC,IAAI,CAAC,IAAIH,CAAC,CAAChB,IAAI,CAACuB,MAAO;IAC3D,CAAC,CAAC;IACF,OAAOsB,KAAK;EACd,CAAC,CAAC;EACF,OAAOd,SAAS,CACdW,KAAK,CAACE,GAAG,CAAEE,CAAC,IAAKA,CAAC,CAACtC,EAAE,CAAC,EACtBmC,SAAS,CACV;AACH,CAAC;AAED;;;;AAIA,OAAO,MAAMI,aAAa,GAAGA,CAC3BC,IAAiE,EACjEC,KAAiC,KAC/B;EACF,MAAMC,CAAC,GAAGtD,MAAM,CAACoD,IAAI,CAACE,CAAC,CAAC;EACxB,MAAMC,CAAC,GAAGvD,MAAM,CAACoD,IAAI,CAACG,CAAC,CAAC;EAExB;EACA;EACA,MAAMC,EAAE,GAAGxD,MAAM,CAACqD,KAAK,CAACC,CAAC,CAAC,GAAGA,CAAC;EAC9B,MAAMG,EAAE,GAAGzD,MAAM,CAACqD,KAAK,CAACE,CAAC,CAAC,GAAGA,CAAC;EAC9B,IAAIG,CAAC,GAAG1D,MAAM,CAACoD,IAAI,CAACO,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAIC,CAAC,GAAG5D,MAAM,CAACoD,IAAI,CAACS,MAAM,CAAC,GAAG,CAAC;EAE/B,IAAI,CAACL,EAAE,IAAI,CAACC,EAAE,EAAE;IACd;IACA,OAAO;MAAEH,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAC,CAAE;;EAGvB,IAAIO,EAAE;EACN,IAAIC,EAAE;EAEN,IAAIxD,IAAI,CAACyD,GAAG,CAACP,EAAE,CAAC,GAAGC,CAAC,GAAGnD,IAAI,CAACyD,GAAG,CAACR,EAAE,CAAC,GAAGI,CAAC,EAAE;IACvC;IACA,IAAIH,EAAE,GAAG,CAAC,EAAE;MACVG,CAAC,GAAG,CAACA,CAAC;;IAERE,EAAE,GAAIF,CAAC,GAAGJ,EAAE,GAAIC,EAAE;IAClBM,EAAE,GAAGH,CAAC;GACP,MAAM;IACL;IACA,IAAIJ,EAAE,GAAG,CAAC,EAAE;MACVE,CAAC,GAAG,CAACA,CAAC;;IAERI,EAAE,GAAGJ,CAAC;IACNK,EAAE,GAAIL,CAAC,GAAGD,EAAE,GAAID,EAAE;;EAGpB,OAAO;IAAEF,CAAC,EAAEA,CAAC,GAAGQ,EAAE;IAAEP,CAAC,EAAEA,CAAC,GAAGQ;EAAE,CAAE;AACjC,CAAC;AAED;;;;AAIA,OAAO,MAAME,gBAAgB,GAAI/D,CAAS,IAAI;EAC5C,MAAMgE,aAAa,GAAW,EAAE;EAChC,MAAMC,OAAO,GAAGC,OAAO,CAAClE,CAAC,CAAC,GAAG,CAAC;EAC9B,KAAK,IAAIuC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,OAAO,EAAE1B,CAAC,EAAE,EAAE;IAChCyB,aAAa,CAACG,IAAI,CAAC,EAAE,CAAC;;EAGxB;EACAnE,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEgB,IAAI,IAAI;IAC/B,MAAMsC,IAAI,GAAGtC,IAAI,CAAC5B,IAAI,CAACkE,IAAK;IAC5B,IAAIA,IAAI,KAAKC,SAAS,IAAIL,aAAa,CAACI,IAAI,CAAC,EAAE;MAC7CJ,aAAa,CAACI,IAAI,CAAC,CAACD,IAAI,CAACrC,IAAI,CAACpB,EAAE,CAAC;;EAErC,CAAC,CAAC;EAEF,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,OAAO,EAAE1B,CAAC,EAAE,EAAE;IAChCyB,aAAa,CAACzB,CAAC,CAAC,GAAGyB,aAAa,CAACzB,CAAC,CAAC,CAAC+B,IAAI,CAAC,CAACC,EAAM,EAAEC,EAAM,KACtD7E,QAAQ,CAACK,CAAC,CAACyE,OAAO,CAACF,EAAE,CAAC,CAACrE,IAAI,CAACwE,KAAM,EAAE1E,CAAC,CAACyE,OAAO,CAACD,EAAE,CAAC,CAACtE,IAAI,CAACwE,KAAM,CAAC,CAC/D;;EAGH,OAAOV,aAAa;AACtB,CAAC;AAED;;;;AAIA,OAAO,MAAMW,cAAc,GAAI3E,CAAS,IAAI;EAC1C,MAAM4E,SAAS,GAAG5E,CAAC,CAChBa,WAAW,EAAE,CACbgE,MAAM,CAAEzE,CAAC,IAAKA,CAAC,CAACF,IAAI,CAACkE,IAAI,KAAKC,SAAS,CAAC,CACxCvB,GAAG,CAAE1C,CAAC,IAAKA,CAAC,CAACF,IAAI,CAACkE,IAAK,CAAC;EAC3B,MAAMU,GAAG,GAAGzE,IAAI,CAACyE,GAAG,CAAC,GAAGF,SAAS,CAAC;EAClC5E,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEV,CAAC,IAAI;IAC5B,IAAIA,CAAC,CAACF,IAAI,CAAC6E,cAAc,CAAC,MAAM,CAAC,IAAID,GAAG,KAAKE,QAAQ,EAAE;MACrD5E,CAAC,CAACF,IAAI,CAACkE,IAAK,IAAIU,GAAG;;EAEvB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,gBAAgB,GAAGA,CAACjF,CAAS,EAAEkF,cAAA,GAAyB,CAAC,KAAI;EACxE;EACA,MAAMtC,KAAK,GAAG5C,CAAC,CAACa,WAAW,EAAE;EAC7B,MAAM+D,SAAS,GAAGhC,KAAK,CACpBiC,MAAM,CAAEzE,CAAC,IAAKA,CAAC,CAACF,IAAI,CAACkE,IAAI,KAAKC,SAAS,CAAC,CACxCvB,GAAG,CAAE1C,CAAC,IAAKA,CAAC,CAACF,IAAI,CAACkE,IAAK,CAAC;EAE3B,MAAMe,MAAM,GAAG9E,IAAI,CAACyE,GAAG,CAAC,GAAGF,SAAS,CAAC;EACrC,MAAMQ,MAAM,GAAW,EAAE;EAEzBxC,KAAK,CAAC9B,OAAO,CAAEV,CAAC,IAAI;IAClB,MAAMgE,IAAI,GAAG,CAAChE,CAAC,CAACF,IAAI,CAACkE,IAAK,IAAI,CAAC,IAAIe,MAAM;IAEzC,IAAI,CAACC,MAAM,CAAChB,IAAI,CAAC,EAAE;MACjBgB,MAAM,CAAChB,IAAI,CAAC,GAAG,EAAE;;IAEnBgB,MAAM,CAAChB,IAAI,CAAC,CAACD,IAAI,CAAC/D,CAAC,CAACM,EAAE,CAAC;EACzB,CAAC,CAAC;EAEF,IAAI2E,KAAK,GAAG,CAAC;EACb,KAAK,IAAI9C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6C,MAAM,CAACpD,MAAM,EAAEO,CAAC,EAAE,EAAE;IACtC,MAAM+C,EAAE,GAAGF,MAAM,CAAC7C,CAAC,CAAC;IACpB,IAAI+C,EAAE,KAAKjB,SAAS,EAAE;MACpB,IAAI9B,CAAC,GAAG2C,cAAc,KAAK,CAAC,EAAE;QAC5BG,KAAK,IAAI,CAAC;;KAEb,MAAM,IAAIA,KAAK,EAAE;MAChBC,EAAE,aAAFA,EAAE,uBAAFA,EAAE,CAAExE,OAAO,CAAEV,CAAK,IAAI;QACpB,MAAM0B,IAAI,GAAG9B,CAAC,CAACyE,OAAO,CAACrE,CAAC,CAAC;QACzB,IAAI0B,IAAI,EAAE;UACRA,IAAI,CAAC5B,IAAI,CAACkE,IAAI,GAAGtC,IAAI,CAAC5B,IAAI,CAACkE,IAAI,IAAI,CAAC;UACpCtC,IAAI,CAAC5B,IAAI,CAACkE,IAAK,IAAIiB,KAAK;;MAE5B,CAAC,CAAC;;;AAGR,CAAC;AAED,OAAO,MAAME,aAAa,GAAGA,CAC3BvF,CAAS,EACTwF,MAAc,EACdpB,IAAa,EACbM,KAAc,KACZ;EACF,MAAM5C,IAAI,GAAa;IACrB2B,KAAK,EAAE,CAAC;IACRE,MAAM,EAAE;GACT;EACD,IAAIjE,QAAQ,CAAC0E,IAAI,CAAC,IAAI1E,QAAQ,CAACgF,KAAK,CAAC,EAAE;IACrC5C,IAAI,CAACsC,IAAI,GAAGA,IAAI;IAChBtC,IAAI,CAAC4C,KAAK,GAAGA,KAAK;;EAEpB,OAAO3E,YAAY,CAACC,CAAC,EAAE,QAAQ,EAAE8B,IAAI,EAAE0D,MAAM,CAAC;AAChD,CAAC;AAED,OAAO,MAAMtB,OAAO,GAAIlE,CAAS,IAAI;EACnC,IAAIkE,OAAe;EACnBlE,CAAC,CAACa,WAAW,EAAE,CAACC,OAAO,CAAEV,CAAC,IAAI;IAC5B,MAAMgE,IAAI,GAAGhE,CAAC,CAACF,IAAI,CAACkE,IAAK;IACzB,IAAIA,IAAI,KAAKC,SAAS,EAAE;MACtB,IAAIH,OAAO,KAAKG,SAAS,IAAID,IAAI,GAAGF,OAAO,EAAE;QAC3CA,OAAO,GAAGE,IAAI;;;EAGpB,CAAC,CAAC;EAEF,IAAI,CAACF,OAAQ,EAAE;IACbA,OAAO,GAAG,CAAC;;EAEb,OAAOA,OAAO;AAChB,CAAC;AAED;;;;;AAKA,OAAO,MAAMuB,SAAS,GAAGA,CACvBC,UAAe,EACfC,EAAuB,KACrB;EACF,MAAMC,MAAM,GAAG;IAAEC,GAAG,EAAE,EAAS;IAAEC,GAAG,EAAE;EAAS,CAAE;EACjDJ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE5E,OAAO,CAAEiF,KAAK,IAAI;IAC5B,IAAIJ,EAAE,CAACI,KAAK,CAAC,EAAE;MACbH,MAAM,CAACC,GAAG,CAAC1B,IAAI,CAAC4B,KAAK,CAAC;KACvB,MAAM;MACLH,MAAM,CAACE,GAAG,CAAC3B,IAAI,CAAC4B,KAAK,CAAC;;EAE1B,CAAC,CAAC;EACF,OAAOH,MAAM;AACf,CAAC;AAED,OAAO,MAAMI,KAAK,GAAGA,CAAUC,KAAU,EAAEC,IAA0B,KAAI;EACvE,OAAOD,KAAK,CAAC7D,MAAM,CAAC,CAAC+D,CAAC,EAAEC,CAAC,KAAI;IAC3B,MAAMC,IAAI,GAAGH,IAAI,CAACC,CAAC,CAAC;IACpB,MAAMG,IAAI,GAAGJ,IAAI,CAACE,CAAC,CAAC;IACpB,OAAOC,IAAI,GAAGC,IAAI,GAAGF,CAAC,GAAGD,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;AAED,MAAMI,KAAK,GAAGA,CACZC,KAAa,EACb1E,IAAoB,EACpB2E,SAAkB,EAClBC,OAAa,EACbC,SAAsC,EACtCf,MAAY,KACV;EACF,IAAI,CAACc,OAAO,CAACE,QAAQ,CAAC9E,IAAI,CAACpB,EAAE,CAAC,EAAE;IAC9BgG,OAAO,CAACvC,IAAI,CAACrC,IAAI,CAACpB,EAAE,CAAC;IACrB,IAAI,CAAC+F,SAAS,EAAE;MACdb,MAAM,CAACzB,IAAI,CAACrC,IAAI,CAACpB,EAAE,CAAC;;IAEtBiG,SAAS,CAAC7E,IAAI,CAACpB,EAAE,CAAC,CAACI,OAAO,CAAEkC,CAAC,IAC3BuD,KAAK,CAACC,KAAK,EAAExD,CAAC,EAAEyD,SAAS,EAAEC,OAAO,EAAEC,SAAS,EAAEf,MAAM,CAAC,CACvD;IACD,IAAIa,SAAS,EAAE;MACbb,MAAM,CAACzB,IAAI,CAACrC,IAAI,CAACpB,EAAE,CAAC;;;AAG1B,CAAC;AAED;;;;AAIA,OAAO,MAAMmG,GAAG,GAAGA,CACjBL,KAAa,EACb1E,IAAuC,EACvC4C,KAAqB,EACrBoC,UAAmB,KACjB;EACF,MAAMlE,KAAK,GAAGmE,KAAK,CAACC,OAAO,CAAClF,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;EACjD,MAAM6E,SAAS,GAAI3D,CAAK,IACrB8D,UAAU,GAAGN,KAAK,CAACS,aAAa,CAACjE,CAAC,CAAC,GAAGwD,KAAK,CAACU,YAAY,CAAClE,CAAC,CAAG;EAChE,MAAMmE,OAAO,GAAS,EAAE;EACxB,MAAMT,OAAO,GAAS,EAAE;EACxB9D,KAAK,CAAC9B,OAAO,CAAEgB,IAAI,IAAI;IACrB,IAAI,CAAC0E,KAAK,CAACjG,OAAO,CAACuB,IAAI,CAACpB,EAAE,CAAC,EAAE;MAC3B,MAAM,IAAI0G,KAAK,CAAC,6BAA6BtF,IAAI,EAAE,CAAC;KACrD,MAAM;MACLyE,KAAK,CAACC,KAAK,EAAE1E,IAAI,EAAE4C,KAAK,KAAK,MAAM,EAAEgC,OAAO,EAAEC,SAAS,EAAEQ,OAAO,CAAC;;EAErE,CAAC,CAAC;EAEF,OAAOA,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}