{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = components;\nfunction components(g) {\n  var visited = {};\n  var cmpts = [];\n  var cmpt;\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    cmpt.push(v);\n    _.each(g.successors(v), dfs);\n    _.each(g.predecessors(v), dfs);\n  }\n  _.each(g.nodes(), function (v) {\n    cmpt = [];\n    dfs(v);\n    if (cmpt.length) {\n      cmpts.push(cmpt);\n    }\n  });\n  return cmpts;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "components", "g", "visited", "cmpts", "cmpt", "dfs", "v", "has", "push", "each", "successors", "predecessors", "nodes", "length"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/components.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = components;\n\nfunction components(g) {\n  var visited = {};\n  var cmpts = [];\n  var cmpt;\n\n  function dfs(v) {\n    if (_.has(visited, v)) return;\n    visited[v] = true;\n    cmpt.push(v);\n    _.each(g.successors(v), dfs);\n    _.each(g.predecessors(v), dfs);\n  }\n\n  _.each(g.nodes(), function(v) {\n    cmpt = [];\n    dfs(v);\n    if (cmpt.length) {\n      cmpts.push(cmpt);\n    }\n  });\n\n  return cmpts;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,UAAU;AAE3B,SAASA,UAAUA,CAACC,CAAC,EAAE;EACrB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,KAAK,GAAG,EAAE;EACd,IAAIC,IAAI;EAER,SAASC,GAAGA,CAACC,CAAC,EAAE;IACd,IAAIV,CAAC,CAACW,GAAG,CAACL,OAAO,EAAEI,CAAC,CAAC,EAAE;IACvBJ,OAAO,CAACI,CAAC,CAAC,GAAG,IAAI;IACjBF,IAAI,CAACI,IAAI,CAACF,CAAC,CAAC;IACZV,CAAC,CAACa,IAAI,CAACR,CAAC,CAACS,UAAU,CAACJ,CAAC,CAAC,EAAED,GAAG,CAAC;IAC5BT,CAAC,CAACa,IAAI,CAACR,CAAC,CAACU,YAAY,CAACL,CAAC,CAAC,EAAED,GAAG,CAAC;EAChC;EAEAT,CAAC,CAACa,IAAI,CAACR,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE,UAASN,CAAC,EAAE;IAC5BF,IAAI,GAAG,EAAE;IACTC,GAAG,CAACC,CAAC,CAAC;IACN,IAAIF,IAAI,CAACS,MAAM,EAAE;MACfV,KAAK,CAACK,IAAI,CAACJ,IAAI,CAAC;IAClB;EACF,CAAC,CAAC;EAEF,OAAOD,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}