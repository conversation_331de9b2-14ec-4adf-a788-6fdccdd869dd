{"ast": null, "code": "import { cartesian } from \"../cartesian.js\";\nimport { abs, asin, atan2, cos, epsilon, radians, sqrt } from \"../math.js\";\nimport { transformer } from \"../transform.js\";\nvar maxDepth = 16,\n  // maximum depth of subdivision\n  cosMinDistance = cos(30 * radians); // cos(minimum angular distance)\n\nexport default function (project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\nfunction resampleNone(project) {\n  return transformer({\n    point: function (x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\nfunction resample(project, delta2) {\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n      dy = y1 - y0,\n      d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n        b = b0 + b1,\n        c = c0 + c1,\n        m = sqrt(a * a + b * b + c * c),\n        phi2 = asin(c /= m),\n        lambda2 = abs(abs(c) - 1) < epsilon || abs(lambda0 - lambda1) < epsilon ? (lambda0 + lambda1) / 2 : atan2(b, a),\n        p = project(lambda2, phi2),\n        x2 = p[0],\n        y2 = p[1],\n        dx2 = x2 - x0,\n        dy2 = y2 - y0,\n        dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n      || abs((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n      || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) {\n        // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function (stream) {\n    var lambda00, x00, y00, a00, b00, c00,\n      // first point\n      lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function () {\n        stream.polygonStart();\n        resampleStream.lineStart = ringStart;\n      },\n      polygonEnd: function () {\n        stream.polygonEnd();\n        resampleStream.lineStart = lineStart;\n      }\n    };\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n    function linePoint(lambda, phi) {\n      var c = cartesian([lambda, phi]),\n        p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n    return resampleStream;\n  };\n}", "map": {"version": 3, "names": ["cartesian", "abs", "asin", "atan2", "cos", "epsilon", "radians", "sqrt", "transformer", "max<PERSON><PERSON><PERSON>", "cosMinDistance", "project", "delta2", "resample", "resampleNone", "point", "x", "y", "stream", "resampleLineTo", "x0", "y0", "lambda0", "a0", "b0", "c0", "x1", "y1", "lambda1", "a1", "b1", "c1", "depth", "dx", "dy", "d2", "a", "b", "c", "m", "phi2", "lambda2", "p", "x2", "y2", "dx2", "dy2", "dz", "lambda00", "x00", "y00", "a00", "b00", "c00", "resampleStream", "lineStart", "lineEnd", "polygonStart", "ringStart", "polygonEnd", "NaN", "linePoint", "lambda", "phi", "ringPoint", "ringEnd"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-geo/src/projection/resample.js"], "sourcesContent": ["import {cartesian} from \"../cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, radians, sqrt} from \"../math.js\";\nimport {transformer} from \"../transform.js\";\n\nvar maxDepth = 16, // maximum depth of subdivision\n    cosMinDistance = cos(30 * radians); // cos(minimum angular distance)\n\nexport default function(project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\n\nfunction resampleNone(project) {\n  return transformer({\n    point: function(x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\n\nfunction resample(project, delta2) {\n\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n        dy = y1 - y0,\n        d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n          b = b0 + b1,\n          c = c0 + c1,\n          m = sqrt(a * a + b * b + c * c),\n          phi2 = asin(c /= m),\n          lambda2 = abs(abs(c) - 1) < epsilon || abs(lambda0 - lambda1) < epsilon ? (lambda0 + lambda1) / 2 : atan2(b, a),\n          p = project(lambda2, phi2),\n          x2 = p[0],\n          y2 = p[1],\n          dx2 = x2 - x0,\n          dy2 = y2 - y0,\n          dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n          || abs((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n          || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) { // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function(stream) {\n    var lambda00, x00, y00, a00, b00, c00, // first point\n        lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() { stream.polygonStart(); resampleStream.lineStart = ringStart; },\n      polygonEnd: function() { stream.polygonEnd(); resampleStream.lineStart = lineStart; }\n    };\n\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n\n    function linePoint(lambda, phi) {\n      var c = cartesian([lambda, phi]), p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n\n    return resampleStream;\n  };\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,iBAAiB;AACzC,SAAQC,GAAG,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,EAAEC,OAAO,EAAEC,IAAI,QAAO,YAAY;AACxE,SAAQC,WAAW,QAAO,iBAAiB;AAE3C,IAAIC,QAAQ,GAAG,EAAE;EAAE;EACfC,cAAc,GAAGN,GAAG,CAAC,EAAE,GAAGE,OAAO,CAAC,CAAC,CAAC;;AAExC,eAAe,UAASK,OAAO,EAAEC,MAAM,EAAE;EACvC,OAAO,CAACA,MAAM,GAAGC,QAAQ,CAACF,OAAO,EAAEC,MAAM,CAAC,GAAGE,YAAY,CAACH,OAAO,CAAC;AACpE;AAEA,SAASG,YAAYA,CAACH,OAAO,EAAE;EAC7B,OAAOH,WAAW,CAAC;IACjBO,KAAK,EAAE,SAAAA,CAASC,CAAC,EAAEC,CAAC,EAAE;MACpBD,CAAC,GAAGL,OAAO,CAACK,CAAC,EAAEC,CAAC,CAAC;MACjB,IAAI,CAACC,MAAM,CAACH,KAAK,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC,CAAC;AACJ;AAEA,SAASH,QAAQA,CAACF,OAAO,EAAEC,MAAM,EAAE;EAEjC,SAASO,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEd,MAAM,EAAE;IAC/F,IAAIe,EAAE,GAAGP,EAAE,GAAGN,EAAE;MACZc,EAAE,GAAGP,EAAE,GAAGN,EAAE;MACZc,EAAE,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAC1B,IAAIC,EAAE,GAAG,CAAC,GAAGvB,MAAM,IAAIoB,KAAK,EAAE,EAAE;MAC9B,IAAII,CAAC,GAAGb,EAAE,GAAGM,EAAE;QACXQ,CAAC,GAAGb,EAAE,GAAGM,EAAE;QACXQ,CAAC,GAAGb,EAAE,GAAGM,EAAE;QACXQ,CAAC,GAAGhC,IAAI,CAAC6B,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC,CAAC;QAC/BE,IAAI,GAAGtC,IAAI,CAACoC,CAAC,IAAIC,CAAC,CAAC;QACnBE,OAAO,GAAGxC,GAAG,CAACA,GAAG,CAACqC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAGjC,OAAO,IAAIJ,GAAG,CAACqB,OAAO,GAAGM,OAAO,CAAC,GAAGvB,OAAO,GAAG,CAACiB,OAAO,GAAGM,OAAO,IAAI,CAAC,GAAGzB,KAAK,CAACkC,CAAC,EAAED,CAAC,CAAC;QAC/GM,CAAC,GAAG/B,OAAO,CAAC8B,OAAO,EAAED,IAAI,CAAC;QAC1BG,EAAE,GAAGD,CAAC,CAAC,CAAC,CAAC;QACTE,EAAE,GAAGF,CAAC,CAAC,CAAC,CAAC;QACTG,GAAG,GAAGF,EAAE,GAAGvB,EAAE;QACb0B,GAAG,GAAGF,EAAE,GAAGvB,EAAE;QACb0B,EAAE,GAAGb,EAAE,GAAGW,GAAG,GAAGZ,EAAE,GAAGa,GAAG;MAC5B,IAAIC,EAAE,GAAGA,EAAE,GAAGZ,EAAE,GAAGvB,MAAM,CAAC;MAAA,GACnBX,GAAG,CAAC,CAACgC,EAAE,GAAGY,GAAG,GAAGX,EAAE,GAAGY,GAAG,IAAIX,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;MAAA,GAC5CZ,EAAE,GAAGM,EAAE,GAAGL,EAAE,GAAGM,EAAE,GAAGL,EAAE,GAAGM,EAAE,GAAGrB,cAAc,EAAE;QAAE;QACrDS,cAAc,CAACC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEkB,EAAE,EAAEC,EAAE,EAAEH,OAAO,EAAEL,CAAC,IAAIG,CAAC,EAAEF,CAAC,IAAIE,CAAC,EAAED,CAAC,EAAEN,KAAK,EAAEd,MAAM,CAAC;QAC9FA,MAAM,CAACH,KAAK,CAAC4B,EAAE,EAAEC,EAAE,CAAC;QACpBzB,cAAc,CAACwB,EAAE,EAAEC,EAAE,EAAEH,OAAO,EAAEL,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEZ,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAEd,MAAM,CAAC;MACtF;IACF;EACF;EACA,OAAO,UAASA,MAAM,EAAE;IACtB,IAAI8B,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAEC,GAAG;MAAE;MACnC/B,OAAO,EAAEF,EAAE,EAAEC,EAAE,EAAEE,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;;IAEjC,IAAI6B,cAAc,GAAG;MACnBvC,KAAK,EAAEA,KAAK;MACZwC,SAAS,EAAEA,SAAS;MACpBC,OAAO,EAAEA,OAAO;MAChBC,YAAY,EAAE,SAAAA,CAAA,EAAW;QAAEvC,MAAM,CAACuC,YAAY,CAAC,CAAC;QAAEH,cAAc,CAACC,SAAS,GAAGG,SAAS;MAAE,CAAC;MACzFC,UAAU,EAAE,SAAAA,CAAA,EAAW;QAAEzC,MAAM,CAACyC,UAAU,CAAC,CAAC;QAAEL,cAAc,CAACC,SAAS,GAAGA,SAAS;MAAE;IACtF,CAAC;IAED,SAASxC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;MACnBD,CAAC,GAAGL,OAAO,CAACK,CAAC,EAAEC,CAAC,CAAC;MACjBC,MAAM,CAACH,KAAK,CAACC,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B;IAEA,SAASuC,SAASA,CAAA,EAAG;MACnBnC,EAAE,GAAGwC,GAAG;MACRN,cAAc,CAACvC,KAAK,GAAG8C,SAAS;MAChC3C,MAAM,CAACqC,SAAS,CAAC,CAAC;IACpB;IAEA,SAASM,SAASA,CAACC,MAAM,EAAEC,GAAG,EAAE;MAC9B,IAAIzB,CAAC,GAAGtC,SAAS,CAAC,CAAC8D,MAAM,EAAEC,GAAG,CAAC,CAAC;QAAErB,CAAC,GAAG/B,OAAO,CAACmD,MAAM,EAAEC,GAAG,CAAC;MAC1D5C,cAAc,CAACC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEL,EAAE,GAAGsB,CAAC,CAAC,CAAC,CAAC,EAAErB,EAAE,GAAGqB,CAAC,CAAC,CAAC,CAAC,EAAEpB,OAAO,GAAGwC,MAAM,EAAEvC,EAAE,GAAGe,CAAC,CAAC,CAAC,CAAC,EAAEd,EAAE,GAAGc,CAAC,CAAC,CAAC,CAAC,EAAEb,EAAE,GAAGa,CAAC,CAAC,CAAC,CAAC,EAAE7B,QAAQ,EAAES,MAAM,CAAC;MACtIA,MAAM,CAACH,KAAK,CAACK,EAAE,EAAEC,EAAE,CAAC;IACtB;IAEA,SAASmC,OAAOA,CAAA,EAAG;MACjBF,cAAc,CAACvC,KAAK,GAAGA,KAAK;MAC5BG,MAAM,CAACsC,OAAO,CAAC,CAAC;IAClB;IAEA,SAASE,SAASA,CAAA,EAAG;MACnBH,SAAS,CAAC,CAAC;MACXD,cAAc,CAACvC,KAAK,GAAGiD,SAAS;MAChCV,cAAc,CAACE,OAAO,GAAGS,OAAO;IAClC;IAEA,SAASD,SAASA,CAACF,MAAM,EAAEC,GAAG,EAAE;MAC9BF,SAAS,CAACb,QAAQ,GAAGc,MAAM,EAAEC,GAAG,CAAC,EAAEd,GAAG,GAAG7B,EAAE,EAAE8B,GAAG,GAAG7B,EAAE,EAAE8B,GAAG,GAAG5B,EAAE,EAAE6B,GAAG,GAAG5B,EAAE,EAAE6B,GAAG,GAAG5B,EAAE;MACnF6B,cAAc,CAACvC,KAAK,GAAG8C,SAAS;IAClC;IAEA,SAASI,OAAOA,CAAA,EAAG;MACjB9C,cAAc,CAACC,EAAE,EAAEC,EAAE,EAAEC,OAAO,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEwB,GAAG,EAAEC,GAAG,EAAEF,QAAQ,EAAEG,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE5C,QAAQ,EAAES,MAAM,CAAC;MAChGoC,cAAc,CAACE,OAAO,GAAGA,OAAO;MAChCA,OAAO,CAAC,CAAC;IACX;IAEA,OAAOF,cAAc;EACvB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}