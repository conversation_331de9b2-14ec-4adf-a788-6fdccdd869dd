{"ast": null, "code": "import { __assign, __extends, __read, __rest } from \"tslib\";\nimport { Component } from '../../../core';\nimport { classNames, ifShow, select, subStyleProps } from '../../../util';\nimport { Marker } from '../../marker';\nimport { ifHorizontal } from '../utils';\nvar CLASS_NAMES = classNames({\n  markerGroup: 'marker-group',\n  marker: 'marker',\n  labelGroup: 'label-group',\n  label: 'label'\n}, 'handle');\n// todo @xiaoiver, 配置 TEXT_INHERITABLE_PROPS 后文本包围盒依旧不准确\nexport var DEFAULT_HANDLE_CFG = {\n  showLabel: true,\n  formatter: function (val) {\n    return val.toString();\n  },\n  markerSize: 25,\n  markerStroke: '#c5c5c5',\n  markerFill: '#fff',\n  markerLineWidth: 1,\n  labelFontSize: 12,\n  labelFill: '#c5c5c5',\n  labelText: '',\n  orientation: 'vertical',\n  spacing: 0\n};\nvar Handle = /** @class */function (_super) {\n  __extends(Handle, _super);\n  function Handle(options) {\n    return _super.call(this, options, DEFAULT_HANDLE_CFG) || this;\n  }\n  Handle.prototype.render = function (attributes, container) {\n    var markerGroup = select(container).maybeAppendByClassName(CLASS_NAMES.markerGroup, 'g');\n    this.renderMarker(markerGroup);\n    var labelGroup = select(container).maybeAppendByClassName(CLASS_NAMES.labelGroup, 'g');\n    this.renderLabel(labelGroup);\n  };\n  Handle.prototype.renderMarker = function (container) {\n    var _this = this;\n    var _a = this.attributes,\n      orientation = _a.orientation,\n      _b = _a.markerSymbol,\n      markerSymbol = _b === void 0 ? ifHorizontal(orientation, 'horizontalHandle', 'verticalHandle') : _b;\n    ifShow(!!markerSymbol, container, function (group) {\n      var handleStyle = subStyleProps(_this.attributes, 'marker');\n      var markerStyle = __assign({\n        symbol: markerSymbol\n      }, handleStyle);\n      _this.marker = group.maybeAppendByClassName(CLASS_NAMES.marker, function () {\n        return new Marker({\n          style: markerStyle\n        });\n      }).update(markerStyle);\n    });\n  };\n  Handle.prototype.renderLabel = function (container) {\n    var _this = this;\n    var _a = this.attributes,\n      showLabel = _a.showLabel,\n      orientation = _a.orientation,\n      _b = _a.spacing,\n      spacing = _b === void 0 ? 0 : _b,\n      formatter = _a.formatter;\n    ifShow(showLabel, container, function (group) {\n      var _a;\n      var _b = subStyleProps(_this.attributes, 'label'),\n        text = _b.text,\n        labelStyle = __rest(_b, [\"text\"]);\n      // adjust layout\n      var _c = ((_a = group.select(CLASS_NAMES.marker.class)) === null || _a === void 0 ? void 0 : _a.node().getBBox()) || {},\n        _d = _c.width,\n        width = _d === void 0 ? 0 : _d,\n        _e = _c.height,\n        height = _e === void 0 ? 0 : _e;\n      var _f = __read(ifHorizontal(orientation, [0, height + spacing, 'center', 'top'], [width + spacing, 0, 'start', 'middle']), 4),\n        x = _f[0],\n        y = _f[1],\n        textAlign = _f[2],\n        textBaseline = _f[3];\n      group.maybeAppendByClassName(CLASS_NAMES.label, 'text').styles(__assign(__assign({}, labelStyle), {\n        x: x,\n        y: y,\n        text: formatter(text).toString(),\n        textAlign: textAlign,\n        textBaseline: textBaseline\n      }));\n    });\n  };\n  return Handle;\n}(Component);\nexport { Handle };", "map": {"version": 3, "names": ["Component", "classNames", "ifShow", "select", "subStyleProps", "<PERSON><PERSON>", "ifHorizontal", "CLASS_NAMES", "markerGroup", "marker", "labelGroup", "label", "DEFAULT_HANDLE_CFG", "showLabel", "formatter", "val", "toString", "markerSize", "markerStroke", "markerFill", "markerLineWidth", "labelFontSize", "labelFill", "labelText", "orientation", "spacing", "<PERSON><PERSON>", "_super", "__extends", "options", "call", "prototype", "render", "attributes", "container", "maybeAppendByClassName", "<PERSON><PERSON>ark<PERSON>", "renderLabel", "_this", "_a", "_b", "markerSymbol", "group", "handleStyle", "markerStyle", "__assign", "symbol", "style", "update", "text", "labelStyle", "__rest", "_c", "class", "node", "getBBox", "_d", "width", "_e", "height", "_f", "__read", "x", "y", "textAlign", "textBaseline", "styles"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/legend/continuous/handle.ts"], "sourcesContent": ["import type { ComponentOptions, PrefixStyleProps } from '../../../core';\nimport { Component } from '../../../core';\nimport type { GroupStyleProps, TextStyleProps } from '../../../shapes';\nimport { Group } from '../../../shapes';\nimport type { PrefixObject } from '../../../types';\nimport type { Selection } from '../../../util';\nimport { classNames, ifShow, select, subStyleProps } from '../../../util';\nimport { Marker, MarkerStyleProps } from '../../marker';\nimport { ifHorizontal } from '../utils';\n\nexport type HandleStyleProps<T = any> = GroupStyleProps &\n  PrefixObject<TextStyleProps, 'label'> &\n  PrefixStyleProps<Omit<MarkerStyleProps, 'x' | 'y'>, 'marker'> & {\n    showLabel?: boolean;\n    formatter?: (val: T) => string;\n    orientation: 'vertical' | 'horizontal';\n    /** spacing between marker and label */\n    spacing?: number;\n    shape?: 'basic' | 'slider';\n  };\n\nexport type HandleOptions = ComponentOptions<HandleStyleProps>;\n\nexport type HandleType = 'start' | 'end';\n\nconst CLASS_NAMES = classNames(\n  {\n    markerGroup: 'marker-group',\n    marker: 'marker',\n    labelGroup: 'label-group',\n    label: 'label',\n  } as const,\n  'handle'\n);\n\n// todo @xiaoiver, 配置 TEXT_INHERITABLE_PROPS 后文本包围盒依旧不准确\nexport const DEFAULT_HANDLE_CFG: Partial<HandleStyleProps> = {\n  showLabel: true,\n  formatter: (val: any) => val.toString(),\n  markerSize: 25,\n  markerStroke: '#c5c5c5',\n  markerFill: '#fff',\n  markerLineWidth: 1,\n  labelFontSize: 12,\n  labelFill: '#c5c5c5',\n  labelText: '',\n  orientation: 'vertical',\n  spacing: 0,\n};\n\nexport class Handle extends Component<HandleStyleProps> {\n  constructor(options: HandleOptions) {\n    super(options, DEFAULT_HANDLE_CFG);\n  }\n\n  private marker!: Selection;\n\n  render(attributes: Required<HandleStyleProps>, container: Group) {\n    const markerGroup = select(container).maybeAppendByClassName(CLASS_NAMES.markerGroup, 'g');\n    this.renderMarker(markerGroup);\n\n    const labelGroup = select(container).maybeAppendByClassName(CLASS_NAMES.labelGroup, 'g');\n    this.renderLabel(labelGroup);\n  }\n\n  private renderMarker(container: Selection) {\n    const { orientation, markerSymbol = ifHorizontal(orientation, 'horizontalHandle', 'verticalHandle') } =\n      this.attributes;\n\n    ifShow(!!markerSymbol, container, (group) => {\n      const handleStyle = subStyleProps(this.attributes, 'marker');\n      const markerStyle = { symbol: markerSymbol, ...handleStyle };\n      this.marker = group\n        .maybeAppendByClassName(CLASS_NAMES.marker, () => new Marker({ style: markerStyle }))\n        .update(markerStyle);\n    });\n  }\n\n  private renderLabel(container: Selection) {\n    const { showLabel, orientation, spacing = 0, formatter } = this.attributes;\n\n    ifShow(showLabel, container, (group) => {\n      const { text, ...labelStyle } = subStyleProps(this.attributes, 'label');\n\n      // adjust layout\n      const { width = 0, height = 0 } = group.select(CLASS_NAMES.marker.class)?.node().getBBox() || {};\n      const [x, y, textAlign, textBaseline] = ifHorizontal(\n        orientation,\n        [0, height + spacing, 'center', 'top'],\n        [width + spacing, 0, 'start', 'middle']\n      );\n\n      group\n        .maybeAppendByClassName(CLASS_NAMES.label, 'text')\n        .styles({ ...labelStyle, x, y, text: formatter(text).toString(), textAlign, textBaseline });\n    });\n  }\n}\n"], "mappings": ";AACA,SAASA,SAAS,QAAQ,eAAe;AAKzC,SAASC,UAAU,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,QAAQ,eAAe;AACzE,SAASC,MAAM,QAA0B,cAAc;AACvD,SAASC,YAAY,QAAQ,UAAU;AAiBvC,IAAMC,WAAW,GAAGN,UAAU,CAC5B;EACEO,WAAW,EAAE,cAAc;EAC3BC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,aAAa;EACzBC,KAAK,EAAE;CACC,EACV,QAAQ,CACT;AAED;AACA,OAAO,IAAMC,kBAAkB,GAA8B;EAC3DC,SAAS,EAAE,IAAI;EACfC,SAAS,EAAE,SAAAA,CAACC,GAAQ;IAAK,OAAAA,GAAG,CAACC,QAAQ,EAAE;EAAd,CAAc;EACvCC,UAAU,EAAE,EAAE;EACdC,YAAY,EAAE,SAAS;EACvBC,UAAU,EAAE,MAAM;EAClBC,eAAe,EAAE,CAAC;EAClBC,aAAa,EAAE,EAAE;EACjBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,EAAE;EACbC,WAAW,EAAE,UAAU;EACvBC,OAAO,EAAE;CACV;AAED,IAAAC,MAAA,0BAAAC,MAAA;EAA4BC,SAAA,CAAAF,MAAA,EAAAC,MAAA;EAC1B,SAAAD,OAAYG,OAAsB;IAChC,OAAAF,MAAK,CAAAG,IAAA,OAACD,OAAO,EAAEjB,kBAAkB,CAAC;EACpC;EAIAc,MAAA,CAAAK,SAAA,CAAAC,MAAM,GAAN,UAAOC,UAAsC,EAAEC,SAAgB;IAC7D,IAAM1B,WAAW,GAAGL,MAAM,CAAC+B,SAAS,CAAC,CAACC,sBAAsB,CAAC5B,WAAW,CAACC,WAAW,EAAE,GAAG,CAAC;IAC1F,IAAI,CAAC4B,YAAY,CAAC5B,WAAW,CAAC;IAE9B,IAAME,UAAU,GAAGP,MAAM,CAAC+B,SAAS,CAAC,CAACC,sBAAsB,CAAC5B,WAAW,CAACG,UAAU,EAAE,GAAG,CAAC;IACxF,IAAI,CAAC2B,WAAW,CAAC3B,UAAU,CAAC;EAC9B,CAAC;EAEOgB,MAAA,CAAAK,SAAA,CAAAK,YAAY,GAApB,UAAqBF,SAAoB;IAAzC,IAAAI,KAAA;IACQ,IAAAC,EAAA,GACJ,IAAI,CAACN,UAAU;MADTT,WAAW,GAAAe,EAAA,CAAAf,WAAA;MAAEgB,EAAA,GAAAD,EAAA,CAAAE,YAA8E;MAA9EA,YAAY,GAAAD,EAAA,cAAGlC,YAAY,CAACkB,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,CAAC,GAAAgB,EAClF;IAEjBtC,MAAM,CAAC,CAAC,CAACuC,YAAY,EAAEP,SAAS,EAAE,UAACQ,KAAK;MACtC,IAAMC,WAAW,GAAGvC,aAAa,CAACkC,KAAI,CAACL,UAAU,EAAE,QAAQ,CAAC;MAC5D,IAAMW,WAAW,GAAAC,QAAA;QAAKC,MAAM,EAAEL;MAAY,GAAKE,WAAW,CAAE;MAC5DL,KAAI,CAAC7B,MAAM,GAAGiC,KAAK,CAChBP,sBAAsB,CAAC5B,WAAW,CAACE,MAAM,EAAE;QAAM,WAAIJ,MAAM,CAAC;UAAE0C,KAAK,EAAEH;QAAW,CAAE,CAAC;MAAlC,CAAkC,CAAC,CACpFI,MAAM,CAACJ,WAAW,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC;EAEOlB,MAAA,CAAAK,SAAA,CAAAM,WAAW,GAAnB,UAAoBH,SAAoB;IAAxC,IAAAI,KAAA;IACQ,IAAAC,EAAA,GAAqD,IAAI,CAACN,UAAU;MAAlEpB,SAAS,GAAA0B,EAAA,CAAA1B,SAAA;MAAEW,WAAW,GAAAe,EAAA,CAAAf,WAAA;MAAEgB,EAAA,GAAAD,EAAA,CAAAd,OAAW;MAAXA,OAAO,GAAAe,EAAA,cAAG,CAAC,GAAAA,EAAA;MAAE1B,SAAS,GAAAyB,EAAA,CAAAzB,SAAoB;IAE1EZ,MAAM,CAACW,SAAS,EAAEqB,SAAS,EAAE,UAACQ,KAAK;;MACjC,IAAMF,EAAA,GAA0BpC,aAAa,CAACkC,KAAI,CAACL,UAAU,EAAE,OAAO,CAAC;QAA/DgB,IAAI,GAAAT,EAAA,CAAAS,IAAA;QAAKC,UAAU,GAAAC,MAAA,CAAAX,EAAA,EAArB,QAAuB,CAA0C;MAEvE;MACM,IAAAY,EAAA,GAA4B,EAAAb,EAAA,GAAAG,KAAK,CAACvC,MAAM,CAACI,WAAW,CAACE,MAAM,CAAC4C,KAAK,CAAC,cAAAd,EAAA,uBAAAA,EAAA,CAAEe,IAAI,GAAGC,OAAO,EAAE,KAAI,EAAE;QAAxFC,EAAA,GAAAJ,EAAA,CAAAK,KAAS;QAATA,KAAK,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;QAAEE,EAAA,GAAAN,EAAA,CAAAO,MAAU;QAAVA,MAAM,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAmE;MAC1F,IAAAE,EAAA,GAAAC,MAAA,CAAkCvD,YAAY,CAClDkB,WAAW,EACX,CAAC,CAAC,EAAEmC,MAAM,GAAGlC,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EACtC,CAACgC,KAAK,GAAGhC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,CACxC;QAJMqC,CAAC,GAAAF,EAAA;QAAEG,CAAC,GAAAH,EAAA;QAAEI,SAAS,GAAAJ,EAAA;QAAEK,YAAY,GAAAL,EAAA,GAInC;MAEDlB,KAAK,CACFP,sBAAsB,CAAC5B,WAAW,CAACI,KAAK,EAAE,MAAM,CAAC,CACjDuD,MAAM,CAAArB,QAAA,CAAAA,QAAA,KAAMK,UAAU;QAAEY,CAAC,EAAAA,CAAA;QAAEC,CAAC,EAAAA,CAAA;QAAEd,IAAI,EAAEnC,SAAS,CAACmC,IAAI,CAAC,CAACjC,QAAQ,EAAE;QAAEgD,SAAS,EAAAA,SAAA;QAAEC,YAAY,EAAAA;MAAA,GAAG;IAC/F,CAAC,CAAC;EACJ,CAAC;EACH,OAAAvC,MAAC;AAAD,CAAC,CA/C2B1B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}