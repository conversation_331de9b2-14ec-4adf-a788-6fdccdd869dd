{"ast": null, "code": "import { sort } from '@antv/vendor/d3-array';\n// Optimize antiCollision from: https://github.com/antvis/G2/blob/master/src/geometry/label/layout/pie/util.ts\nexport function dodgeY(labels, options = {}) {\n  const {\n    labelHeight = 14,\n    height\n  } = options;\n  // Sort labels by y and init boxes (one box for each label)\n  const sortedLabels = sort(labels, d => d.y);\n  const n = sortedLabels.length;\n  const boxes = new Array(n);\n  for (let i = 0; i < n; i++) {\n    const label = sortedLabels[i];\n    const {\n      y\n    } = label;\n    boxes[i] = {\n      y,\n      y1: y + labelHeight,\n      labels: [y]\n    };\n  }\n  // Merge boxes until no overlapping boxes or only one box left.\n  // All the boxes should start higher than 0, but maybe higher than height.\n  let overlap = true;\n  while (overlap) {\n    overlap = false;\n    // Scan backward because boxes maybe deleted.\n    for (let i = boxes.length - 1; i > 0; i--) {\n      const box = boxes[i];\n      const preBox = boxes[i - 1];\n      if (preBox.y1 > box.y) {\n        overlap = true;\n        preBox.labels.push(...box.labels);\n        boxes.splice(i, 1);\n        // Compute new y1 to contain the current box.\n        preBox.y1 += box.y1 - box.y;\n        // Make sure the new box is in the range of [0, height].\n        const newHeight = preBox.y1 - preBox.y;\n        preBox.y1 = Math.max(Math.min(preBox.y1, height), newHeight);\n        preBox.y = preBox.y1 - newHeight;\n      }\n    }\n  }\n  let i = 0;\n  for (const box of boxes) {\n    const {\n      y,\n      labels\n    } = box;\n    let prevY = y - labelHeight;\n    for (const curY of labels) {\n      const label = sortedLabels[i++];\n      const expectedY = prevY + labelHeight;\n      const dy = expectedY - curY;\n      label.connectorPoints[0][1] -= dy;\n      label.y = prevY + labelHeight;\n      prevY += labelHeight;\n    }\n  }\n}\nexport function hideAndDodgeY(unsorted, options) {\n  const labels = sort(unsorted, d => d.y);\n  const {\n    height,\n    labelHeight = 14\n  } = options;\n  const maxCount = Math.ceil(height / labelHeight);\n  if (labels.length <= maxCount) return dodgeY(labels, options);\n  const filtered = [];\n  for (let i = 0; i < labels.length; i++) {\n    // Hide labels out of range.\n    if (i < labels.length - maxCount) {\n      labels[i].opacity = 0;\n      labels[i].connector = false;\n    } else filtered.push(labels[i]);\n  }\n  dodgeY(filtered, options);\n}", "map": {"version": 3, "names": ["sort", "dodgeY", "labels", "options", "labelHeight", "height", "sorted<PERSON><PERSON><PERSON>", "d", "y", "n", "length", "boxes", "Array", "i", "label", "y1", "overlap", "box", "preBox", "push", "splice", "newHeight", "Math", "max", "min", "prevY", "curY", "expectedY", "dy", "connectorPoints", "hideAndDodgeY", "unsorted", "maxCount", "ceil", "filtered", "opacity", "connector"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/shape/label/position/utils.ts"], "sourcesContent": ["import { sort } from '@antv/vendor/d3-array';\n\n// Optimize antiCollision from: https://github.com/antvis/G2/blob/master/src/geometry/label/layout/pie/util.ts\nexport function dodgeY(\n  labels: Record<string, any>[],\n  options: Record<string, any> = {},\n) {\n  const { labelHeight = 14, height } = options;\n\n  // Sort labels by y and init boxes (one box for each label)\n  const sortedLabels = sort(labels, (d) => d.y);\n  const n = sortedLabels.length;\n  const boxes = new Array(n);\n  for (let i = 0; i < n; i++) {\n    const label = sortedLabels[i];\n    const { y } = label;\n    boxes[i] = { y, y1: y + labelHeight, labels: [y] };\n  }\n\n  // Merge boxes until no overlapping boxes or only one box left.\n  // All the boxes should start higher than 0, but maybe higher than height.\n  let overlap = true;\n  while (overlap) {\n    overlap = false;\n    // Scan backward because boxes maybe deleted.\n    for (let i = boxes.length - 1; i > 0; i--) {\n      const box = boxes[i];\n      const preBox = boxes[i - 1];\n      if (preBox.y1 > box.y) {\n        overlap = true;\n        preBox.labels.push(...box.labels);\n        boxes.splice(i, 1);\n\n        // Compute new y1 to contain the current box.\n        preBox.y1 += box.y1 - box.y;\n\n        // Make sure the new box is in the range of [0, height].\n        const newHeight = preBox.y1 - preBox.y;\n        preBox.y1 = Math.max(Math.min(preBox.y1, height), newHeight);\n        preBox.y = preBox.y1 - newHeight;\n      }\n    }\n  }\n\n  let i = 0;\n  for (const box of boxes) {\n    const { y, labels } = box;\n    let prevY = y - labelHeight;\n    for (const curY of labels) {\n      const label = sortedLabels[i++];\n      const expectedY = prevY + labelHeight;\n      const dy = expectedY - curY;\n      label.connectorPoints[0][1] -= dy;\n      label.y = prevY + labelHeight;\n      prevY += labelHeight;\n    }\n  }\n}\n\nexport function hideAndDodgeY(\n  unsorted: Record<string, any>[],\n  options: Record<string, any>,\n) {\n  const labels = sort(unsorted, (d) => d.y);\n  const { height, labelHeight = 14 } = options;\n  const maxCount = Math.ceil(height / labelHeight);\n  if (labels.length <= maxCount) return dodgeY(labels, options);\n  const filtered = [];\n  for (let i = 0; i < labels.length; i++) {\n    // Hide labels out of range.\n    if (i < labels.length - maxCount) {\n      labels[i].opacity = 0;\n      labels[i].connector = false;\n    } else filtered.push(labels[i]);\n  }\n  dodgeY(filtered, options);\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,uBAAuB;AAE5C;AACA,OAAM,SAAUC,MAAMA,CACpBC,MAA6B,EAC7BC,OAAA,GAA+B,EAAE;EAEjC,MAAM;IAAEC,WAAW,GAAG,EAAE;IAAEC;EAAM,CAAE,GAAGF,OAAO;EAE5C;EACA,MAAMG,YAAY,GAAGN,IAAI,CAACE,MAAM,EAAGK,CAAC,IAAKA,CAAC,CAACC,CAAC,CAAC;EAC7C,MAAMC,CAAC,GAAGH,YAAY,CAACI,MAAM;EAC7B,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC;EAC1B,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAAE;IAC1B,MAAMC,KAAK,GAAGR,YAAY,CAACO,CAAC,CAAC;IAC7B,MAAM;MAAEL;IAAC,CAAE,GAAGM,KAAK;IACnBH,KAAK,CAACE,CAAC,CAAC,GAAG;MAAEL,CAAC;MAAEO,EAAE,EAAEP,CAAC,GAAGJ,WAAW;MAAEF,MAAM,EAAE,CAACM,CAAC;IAAC,CAAE;;EAGpD;EACA;EACA,IAAIQ,OAAO,GAAG,IAAI;EAClB,OAAOA,OAAO,EAAE;IACdA,OAAO,GAAG,KAAK;IACf;IACA,KAAK,IAAIH,CAAC,GAAGF,KAAK,CAACD,MAAM,GAAG,CAAC,EAAEG,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MACzC,MAAMI,GAAG,GAAGN,KAAK,CAACE,CAAC,CAAC;MACpB,MAAMK,MAAM,GAAGP,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC;MAC3B,IAAIK,MAAM,CAACH,EAAE,GAAGE,GAAG,CAACT,CAAC,EAAE;QACrBQ,OAAO,GAAG,IAAI;QACdE,MAAM,CAAChB,MAAM,CAACiB,IAAI,CAAC,GAAGF,GAAG,CAACf,MAAM,CAAC;QACjCS,KAAK,CAACS,MAAM,CAACP,CAAC,EAAE,CAAC,CAAC;QAElB;QACAK,MAAM,CAACH,EAAE,IAAIE,GAAG,CAACF,EAAE,GAAGE,GAAG,CAACT,CAAC;QAE3B;QACA,MAAMa,SAAS,GAAGH,MAAM,CAACH,EAAE,GAAGG,MAAM,CAACV,CAAC;QACtCU,MAAM,CAACH,EAAE,GAAGO,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACN,MAAM,CAACH,EAAE,EAAEV,MAAM,CAAC,EAAEgB,SAAS,CAAC;QAC5DH,MAAM,CAACV,CAAC,GAAGU,MAAM,CAACH,EAAE,GAAGM,SAAS;;;;EAKtC,IAAIR,CAAC,GAAG,CAAC;EACT,KAAK,MAAMI,GAAG,IAAIN,KAAK,EAAE;IACvB,MAAM;MAAEH,CAAC;MAAEN;IAAM,CAAE,GAAGe,GAAG;IACzB,IAAIQ,KAAK,GAAGjB,CAAC,GAAGJ,WAAW;IAC3B,KAAK,MAAMsB,IAAI,IAAIxB,MAAM,EAAE;MACzB,MAAMY,KAAK,GAAGR,YAAY,CAACO,CAAC,EAAE,CAAC;MAC/B,MAAMc,SAAS,GAAGF,KAAK,GAAGrB,WAAW;MACrC,MAAMwB,EAAE,GAAGD,SAAS,GAAGD,IAAI;MAC3BZ,KAAK,CAACe,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAID,EAAE;MACjCd,KAAK,CAACN,CAAC,GAAGiB,KAAK,GAAGrB,WAAW;MAC7BqB,KAAK,IAAIrB,WAAW;;;AAG1B;AAEA,OAAM,SAAU0B,aAAaA,CAC3BC,QAA+B,EAC/B5B,OAA4B;EAE5B,MAAMD,MAAM,GAAGF,IAAI,CAAC+B,QAAQ,EAAGxB,CAAC,IAAKA,CAAC,CAACC,CAAC,CAAC;EACzC,MAAM;IAAEH,MAAM;IAAED,WAAW,GAAG;EAAE,CAAE,GAAGD,OAAO;EAC5C,MAAM6B,QAAQ,GAAGV,IAAI,CAACW,IAAI,CAAC5B,MAAM,GAAGD,WAAW,CAAC;EAChD,IAAIF,MAAM,CAACQ,MAAM,IAAIsB,QAAQ,EAAE,OAAO/B,MAAM,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC7D,MAAM+B,QAAQ,GAAG,EAAE;EACnB,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,MAAM,CAACQ,MAAM,EAAEG,CAAC,EAAE,EAAE;IACtC;IACA,IAAIA,CAAC,GAAGX,MAAM,CAACQ,MAAM,GAAGsB,QAAQ,EAAE;MAChC9B,MAAM,CAACW,CAAC,CAAC,CAACsB,OAAO,GAAG,CAAC;MACrBjC,MAAM,CAACW,CAAC,CAAC,CAACuB,SAAS,GAAG,KAAK;KAC5B,MAAMF,QAAQ,CAACf,IAAI,CAACjB,MAAM,CAACW,CAAC,CAAC,CAAC;;EAEjCZ,MAAM,CAACiC,QAAQ,EAAE/B,OAAO,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}