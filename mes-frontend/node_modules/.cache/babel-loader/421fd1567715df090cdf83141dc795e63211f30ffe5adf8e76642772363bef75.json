{"ast": null, "code": "import isType from './is-type';\nvar isRegExp = function (str) {\n  return isType(str, 'RegExp');\n};\nexport default isRegExp;", "map": {"version": 3, "names": ["isType", "isRegExp", "str"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/is-reg-exp.ts"], "sourcesContent": ["import isType from './is-type';\n\nconst isRegExp = function(str: any): str is RegExp {\n  return isType(str, 'RegExp');\n};\n\nexport default isRegExp;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,WAAW;AAE9B,IAAMC,QAAQ,GAAG,SAAAA,CAASC,GAAQ;EAChC,OAAOF,MAAM,CAACE,GAAG,EAAE,QAAQ,CAAC;AAC9B,CAAC;AAED,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}