{"ast": null, "code": "import isArrayLike from './is-array-like';\nvar splice = Array.prototype.splice;\nvar pullAt = function pullAt(arr, indexes) {\n  if (!isArrayLike(arr)) {\n    return [];\n  }\n  var length = arr ? indexes.length : 0;\n  var last = length - 1;\n  while (length--) {\n    var previous = void 0;\n    var index = indexes[length];\n    if (length === last || index !== previous) {\n      previous = index;\n      splice.call(arr, index, 1);\n    }\n  }\n  return arr;\n};\nexport default pullAt;", "map": {"version": 3, "names": ["isArrayLike", "splice", "Array", "prototype", "pullAt", "arr", "indexes", "length", "last", "previous", "index", "call"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/pull-at.ts"], "sourcesContent": ["import isArrayLike from './is-array-like';\n\nconst splice = Array.prototype.splice;\n\nconst pullAt = function pullAt <T>(arr: T[], indexes: number[]): T[] {\n  if (!isArrayLike(arr)) {\n    return [];\n  }\n  let length = arr ? indexes.length : 0;\n  const last = length - 1;\n\n  while (length--) {\n    let previous;\n    const index = indexes[length];\n    if (length === last || index !== previous) {\n      previous = index;\n      splice.call(arr, index, 1);\n    }\n  }\n  return arr;\n};\n\nexport default pullAt;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,iBAAiB;AAEzC,IAAMC,MAAM,GAAGC,KAAK,CAACC,SAAS,CAACF,MAAM;AAErC,IAAMG,MAAM,GAAG,SAASA,MAAMA,CAAKC,GAAQ,EAAEC,OAAiB;EAC5D,IAAI,CAACN,WAAW,CAACK,GAAG,CAAC,EAAE;IACrB,OAAO,EAAE;;EAEX,IAAIE,MAAM,GAAGF,GAAG,GAAGC,OAAO,CAACC,MAAM,GAAG,CAAC;EACrC,IAAMC,IAAI,GAAGD,MAAM,GAAG,CAAC;EAEvB,OAAOA,MAAM,EAAE,EAAE;IACf,IAAIE,QAAQ;IACZ,IAAMC,KAAK,GAAGJ,OAAO,CAACC,MAAM,CAAC;IAC7B,IAAIA,MAAM,KAAKC,IAAI,IAAIE,KAAK,KAAKD,QAAQ,EAAE;MACzCA,QAAQ,GAAGC,KAAK;MAChBT,MAAM,CAACU,IAAI,CAACN,GAAG,EAAEK,KAAK,EAAE,CAAC,CAAC;;;EAG9B,OAAOL,GAAG;AACZ,CAAC;AAED,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}