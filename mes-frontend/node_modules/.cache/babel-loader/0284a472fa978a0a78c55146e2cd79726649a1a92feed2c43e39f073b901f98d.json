{"ast": null, "code": "import isArray from './is-array';\n/**\n * @param {Array} arr The array to iterate over.\n * @return {*} Returns the maximum value.\n * @example\n *\n * max([1, 2]);\n * // => 2\n *\n * max([]);\n * // => undefined\n *\n * const data = new Array(1250010).fill(1).map((d,idx) => idx);\n *\n * max(data);\n * // => 1250010\n * // Math.max(...data) will encounter \"Maximum call stack size exceeded\" error\n */\nexport default (function (arr) {\n  if (!isArray(arr)) {\n    return undefined;\n  }\n  return arr.reduce(function (prev, curr) {\n    return Math.max(prev, curr);\n  }, arr[0]);\n});", "map": {"version": 3, "names": ["isArray", "arr", "undefined", "reduce", "prev", "curr", "Math", "max"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/max.ts"], "sourcesContent": ["import each from './each';\nimport isArray from './is-array';\n\n/**\n * @param {Array} arr The array to iterate over.\n * @return {*} Returns the maximum value.\n * @example\n *\n * max([1, 2]);\n * // => 2\n *\n * max([]);\n * // => undefined\n *\n * const data = new Array(1250010).fill(1).map((d,idx) => idx);\n *\n * max(data);\n * // => 1250010\n * // Math.max(...data) will encounter \"Maximum call stack size exceeded\" error\n */\nexport default (arr: number[]): number | undefined => {\n  if (!isArray(arr)) {\n    return undefined;\n  }\n  return arr.reduce((prev, curr) => {\n    return Math.max(prev, curr)\n  }, arr[0]);\n};\n"], "mappings": "AACA,OAAOA,OAAO,MAAM,YAAY;AAEhC;;;;;;;;;;;;;;;;;AAiBA,gBAAe,UAACC,GAAa;EAC3B,IAAI,CAACD,OAAO,CAACC,GAAG,CAAC,EAAE;IACjB,OAAOC,SAAS;;EAElB,OAAOD,GAAG,CAACE,MAAM,CAAC,UAACC,IAAI,EAAEC,IAAI;IAC3B,OAAOC,IAAI,CAACC,GAAG,CAACH,IAAI,EAAEC,IAAI,CAAC;EAC7B,CAAC,EAAEJ,GAAG,CAAC,CAAC,CAAC,CAAC;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}