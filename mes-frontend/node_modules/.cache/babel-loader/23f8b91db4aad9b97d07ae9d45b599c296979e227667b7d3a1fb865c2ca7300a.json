{"ast": null, "code": "import { asin, cos, sin } from \"../math.js\";\nexport function cylindricalEqualAreaRaw(phi0) {\n  var cosPhi0 = cos(phi0);\n  function forward(lambda, phi) {\n    return [lambda * cosPhi0, sin(phi) / cosPhi0];\n  }\n  forward.invert = function (x, y) {\n    return [x / cosPhi0, asin(y * cosPhi0)];\n  };\n  return forward;\n}", "map": {"version": 3, "names": ["asin", "cos", "sin", "cylindricalEqualAreaRaw", "phi0", "cosPhi0", "forward", "lambda", "phi", "invert", "x", "y"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-geo/src/projection/cylindricalEqualArea.js"], "sourcesContent": ["import {asin, cos, sin} from \"../math.js\";\n\nexport function cylindricalEqualAreaRaw(phi0) {\n  var cosPhi0 = cos(phi0);\n\n  function forward(lambda, phi) {\n    return [lambda * cosPhi0, sin(phi) / cosPhi0];\n  }\n\n  forward.invert = function(x, y) {\n    return [x / cosPhi0, asin(y * cosPhi0)];\n  };\n\n  return forward;\n}\n"], "mappings": "AAAA,SAAQA,IAAI,EAAEC,GAAG,EAAEC,GAAG,QAAO,YAAY;AAEzC,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,IAAIC,OAAO,GAAGJ,GAAG,CAACG,IAAI,CAAC;EAEvB,SAASE,OAAOA,CAACC,MAAM,EAAEC,GAAG,EAAE;IAC5B,OAAO,CAACD,MAAM,GAAGF,OAAO,EAAEH,GAAG,CAACM,GAAG,CAAC,GAAGH,OAAO,CAAC;EAC/C;EAEAC,OAAO,CAACG,MAAM,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAE;IAC9B,OAAO,CAACD,CAAC,GAAGL,OAAO,EAAEL,IAAI,CAACW,CAAC,GAAGN,OAAO,CAAC,CAAC;EACzC,CAAC;EAED,OAAOC,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}