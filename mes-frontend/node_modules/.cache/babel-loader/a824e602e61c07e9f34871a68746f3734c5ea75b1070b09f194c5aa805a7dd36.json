{"ast": null, "code": "import { dispatch } from \"d3-dispatch\";\nimport { timer } from \"d3-timer\";\nimport lcg from \"./lcg.js\";\nexport function x(d) {\n  return d.x;\n}\nexport function y(d) {\n  return d.y;\n}\nvar initialRadius = 10,\n  initialAngle = Math.PI * (3 - Math.sqrt(5));\nexport default function (nodes) {\n  var simulation,\n    alpha = 1,\n    alphaMin = 0.001,\n    alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n    alphaTarget = 0,\n    velocityDecay = 0.6,\n    forces = new Map(),\n    stepper = timer(step),\n    event = dispatch(\"tick\", \"end\"),\n    random = lcg();\n  if (nodes == null) nodes = [];\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n  function tick(iterations) {\n    var i,\n      n = nodes.length,\n      node;\n    if (iterations === undefined) iterations = 1;\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n      forces.forEach(function (force) {\n        force(alpha);\n      });\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;else node.x = node.fx, node.vx = 0;\n        if (node.fy == null) node.y += node.vy *= velocityDecay;else node.y = node.fy, node.vy = 0;\n      }\n    }\n    return simulation;\n  }\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (isNaN(node.x) || isNaN(node.y)) {\n        var radius = initialRadius * Math.sqrt(0.5 + i),\n          angle = i * initialAngle;\n        node.x = radius * Math.cos(angle);\n        node.y = radius * Math.sin(angle);\n      }\n      if (isNaN(node.vx) || isNaN(node.vy)) {\n        node.vx = node.vy = 0;\n      }\n    }\n  }\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random);\n    return force;\n  }\n  initializeNodes();\n  return simulation = {\n    tick: tick,\n    restart: function () {\n      return stepper.restart(step), simulation;\n    },\n    stop: function () {\n      return stepper.stop(), simulation;\n    },\n    nodes: function (_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n    alpha: function (_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n    alphaMin: function (_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n    alphaDecay: function (_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n    alphaTarget: function (_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n    velocityDecay: function (_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n    randomSource: function (_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n    force: function (name, _) {\n      return arguments.length > 1 ? (_ == null ? forces.delete(name) : forces.set(name, initializeForce(_)), simulation) : forces.get(name);\n    },\n    find: function (x, y, radius) {\n      var i = 0,\n        n = nodes.length,\n        dx,\n        dy,\n        d2,\n        node,\n        closest;\n      if (radius == null) radius = Infinity;else radius *= radius;\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - node.y;\n        d2 = dx * dx + dy * dy;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n      return closest;\n    },\n    on: function (name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}", "map": {"version": 3, "names": ["dispatch", "timer", "lcg", "x", "d", "y", "initialRadius", "initialAngle", "Math", "PI", "sqrt", "nodes", "simulation", "alpha", "alphaMin", "alphaDecay", "pow", "alphaTarget", "velocityDecay", "forces", "Map", "stepper", "step", "event", "random", "tick", "call", "stop", "iterations", "i", "n", "length", "node", "undefined", "k", "for<PERSON>ach", "force", "fx", "vx", "fy", "vy", "initializeNodes", "index", "isNaN", "radius", "angle", "cos", "sin", "initializeForce", "initialize", "restart", "_", "arguments", "randomSource", "name", "delete", "set", "get", "find", "dx", "dy", "d2", "closest", "Infinity", "on"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force/src/simulation.js"], "sourcesContent": ["import {dispatch} from \"d3-dispatch\";\nimport {timer} from \"d3-timer\";\nimport lcg from \"./lcg.js\";\n\nexport function x(d) {\n  return d.x;\n}\n\nexport function y(d) {\n  return d.y;\n}\n\nvar initialRadius = 10,\n    initialAngle = Math.PI * (3 - Math.sqrt(5));\n\nexport default function(nodes) {\n  var simulation,\n      alpha = 1,\n      alphaMin = 0.001,\n      alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n      alphaTarget = 0,\n      velocityDecay = 0.6,\n      forces = new Map(),\n      stepper = timer(step),\n      event = dispatch(\"tick\", \"end\"),\n      random = lcg();\n\n  if (nodes == null) nodes = [];\n\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n\n  function tick(iterations) {\n    var i, n = nodes.length, node;\n\n    if (iterations === undefined) iterations = 1;\n\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n\n      forces.forEach(function(force) {\n        force(alpha);\n      });\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;\n        else node.x = node.fx, node.vx = 0;\n        if (node.fy == null) node.y += node.vy *= velocityDecay;\n        else node.y = node.fy, node.vy = 0;\n      }\n    }\n\n    return simulation;\n  }\n\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (isNaN(node.x) || isNaN(node.y)) {\n        var radius = initialRadius * Math.sqrt(0.5 + i), angle = i * initialAngle;\n        node.x = radius * Math.cos(angle);\n        node.y = radius * Math.sin(angle);\n      }\n      if (isNaN(node.vx) || isNaN(node.vy)) {\n        node.vx = node.vy = 0;\n      }\n    }\n  }\n\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random);\n    return force;\n  }\n\n  initializeNodes();\n\n  return simulation = {\n    tick: tick,\n\n    restart: function() {\n      return stepper.restart(step), simulation;\n    },\n\n    stop: function() {\n      return stepper.stop(), simulation;\n    },\n\n    nodes: function(_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n\n    alpha: function(_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n\n    alphaMin: function(_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n\n    alphaDecay: function(_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n\n    alphaTarget: function(_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n\n    velocityDecay: function(_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n\n    randomSource: function(_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n\n    force: function(name, _) {\n      return arguments.length > 1 ? ((_ == null ? forces.delete(name) : forces.set(name, initializeForce(_))), simulation) : forces.get(name);\n    },\n\n    find: function(x, y, radius) {\n      var i = 0,\n          n = nodes.length,\n          dx,\n          dy,\n          d2,\n          node,\n          closest;\n\n      if (radius == null) radius = Infinity;\n      else radius *= radius;\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - node.y;\n        d2 = dx * dx + dy * dy;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n\n      return closest;\n    },\n\n    on: function(name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}\n"], "mappings": "AAAA,SAAQA,QAAQ,QAAO,aAAa;AACpC,SAAQC,KAAK,QAAO,UAAU;AAC9B,OAAOC,GAAG,MAAM,UAAU;AAE1B,OAAO,SAASC,CAACA,CAACC,CAAC,EAAE;EACnB,OAAOA,CAAC,CAACD,CAAC;AACZ;AAEA,OAAO,SAASE,CAACA,CAACD,CAAC,EAAE;EACnB,OAAOA,CAAC,CAACC,CAAC;AACZ;AAEA,IAAIC,aAAa,GAAG,EAAE;EAClBC,YAAY,GAAGC,IAAI,CAACC,EAAE,IAAI,CAAC,GAAGD,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;AAE/C,eAAe,UAASC,KAAK,EAAE;EAC7B,IAAIC,UAAU;IACVC,KAAK,GAAG,CAAC;IACTC,QAAQ,GAAG,KAAK;IAChBC,UAAU,GAAG,CAAC,GAAGP,IAAI,CAACQ,GAAG,CAACF,QAAQ,EAAE,CAAC,GAAG,GAAG,CAAC;IAC5CG,WAAW,GAAG,CAAC;IACfC,aAAa,GAAG,GAAG;IACnBC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;IAClBC,OAAO,GAAGpB,KAAK,CAACqB,IAAI,CAAC;IACrBC,KAAK,GAAGvB,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;IAC/BwB,MAAM,GAAGtB,GAAG,CAAC,CAAC;EAElB,IAAIS,KAAK,IAAI,IAAI,EAAEA,KAAK,GAAG,EAAE;EAE7B,SAASW,IAAIA,CAAA,EAAG;IACdG,IAAI,CAAC,CAAC;IACNF,KAAK,CAACG,IAAI,CAAC,MAAM,EAAEd,UAAU,CAAC;IAC9B,IAAIC,KAAK,GAAGC,QAAQ,EAAE;MACpBO,OAAO,CAACM,IAAI,CAAC,CAAC;MACdJ,KAAK,CAACG,IAAI,CAAC,KAAK,EAAEd,UAAU,CAAC;IAC/B;EACF;EAEA,SAASa,IAAIA,CAACG,UAAU,EAAE;IACxB,IAAIC,CAAC;MAAEC,CAAC,GAAGnB,KAAK,CAACoB,MAAM;MAAEC,IAAI;IAE7B,IAAIJ,UAAU,KAAKK,SAAS,EAAEL,UAAU,GAAG,CAAC;IAE5C,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,UAAU,EAAE,EAAEM,CAAC,EAAE;MACnCrB,KAAK,IAAI,CAACI,WAAW,GAAGJ,KAAK,IAAIE,UAAU;MAE3CI,MAAM,CAACgB,OAAO,CAAC,UAASC,KAAK,EAAE;QAC7BA,KAAK,CAACvB,KAAK,CAAC;MACd,CAAC,CAAC;MAEF,KAAKgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtBG,IAAI,GAAGrB,KAAK,CAACkB,CAAC,CAAC;QACf,IAAIG,IAAI,CAACK,EAAE,IAAI,IAAI,EAAEL,IAAI,CAAC7B,CAAC,IAAI6B,IAAI,CAACM,EAAE,IAAIpB,aAAa,CAAC,KACnDc,IAAI,CAAC7B,CAAC,GAAG6B,IAAI,CAACK,EAAE,EAAEL,IAAI,CAACM,EAAE,GAAG,CAAC;QAClC,IAAIN,IAAI,CAACO,EAAE,IAAI,IAAI,EAAEP,IAAI,CAAC3B,CAAC,IAAI2B,IAAI,CAACQ,EAAE,IAAItB,aAAa,CAAC,KACnDc,IAAI,CAAC3B,CAAC,GAAG2B,IAAI,CAACO,EAAE,EAAEP,IAAI,CAACQ,EAAE,GAAG,CAAC;MACpC;IACF;IAEA,OAAO5B,UAAU;EACnB;EAEA,SAAS6B,eAAeA,CAAA,EAAG;IACzB,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGnB,KAAK,CAACoB,MAAM,EAAEC,IAAI,EAAEH,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAClDG,IAAI,GAAGrB,KAAK,CAACkB,CAAC,CAAC,EAAEG,IAAI,CAACU,KAAK,GAAGb,CAAC;MAC/B,IAAIG,IAAI,CAACK,EAAE,IAAI,IAAI,EAAEL,IAAI,CAAC7B,CAAC,GAAG6B,IAAI,CAACK,EAAE;MACrC,IAAIL,IAAI,CAACO,EAAE,IAAI,IAAI,EAAEP,IAAI,CAAC3B,CAAC,GAAG2B,IAAI,CAACO,EAAE;MACrC,IAAII,KAAK,CAACX,IAAI,CAAC7B,CAAC,CAAC,IAAIwC,KAAK,CAACX,IAAI,CAAC3B,CAAC,CAAC,EAAE;QAClC,IAAIuC,MAAM,GAAGtC,aAAa,GAAGE,IAAI,CAACE,IAAI,CAAC,GAAG,GAAGmB,CAAC,CAAC;UAAEgB,KAAK,GAAGhB,CAAC,GAAGtB,YAAY;QACzEyB,IAAI,CAAC7B,CAAC,GAAGyC,MAAM,GAAGpC,IAAI,CAACsC,GAAG,CAACD,KAAK,CAAC;QACjCb,IAAI,CAAC3B,CAAC,GAAGuC,MAAM,GAAGpC,IAAI,CAACuC,GAAG,CAACF,KAAK,CAAC;MACnC;MACA,IAAIF,KAAK,CAACX,IAAI,CAACM,EAAE,CAAC,IAAIK,KAAK,CAACX,IAAI,CAACQ,EAAE,CAAC,EAAE;QACpCR,IAAI,CAACM,EAAE,GAAGN,IAAI,CAACQ,EAAE,GAAG,CAAC;MACvB;IACF;EACF;EAEA,SAASQ,eAAeA,CAACZ,KAAK,EAAE;IAC9B,IAAIA,KAAK,CAACa,UAAU,EAAEb,KAAK,CAACa,UAAU,CAACtC,KAAK,EAAEa,MAAM,CAAC;IACrD,OAAOY,KAAK;EACd;EAEAK,eAAe,CAAC,CAAC;EAEjB,OAAO7B,UAAU,GAAG;IAClBa,IAAI,EAAEA,IAAI;IAEVyB,OAAO,EAAE,SAAAA,CAAA,EAAW;MAClB,OAAO7B,OAAO,CAAC6B,OAAO,CAAC5B,IAAI,CAAC,EAAEV,UAAU;IAC1C,CAAC;IAEDe,IAAI,EAAE,SAAAA,CAAA,EAAW;MACf,OAAON,OAAO,CAACM,IAAI,CAAC,CAAC,EAAEf,UAAU;IACnC,CAAC;IAEDD,KAAK,EAAE,SAAAA,CAASwC,CAAC,EAAE;MACjB,OAAOC,SAAS,CAACrB,MAAM,IAAIpB,KAAK,GAAGwC,CAAC,EAAEV,eAAe,CAAC,CAAC,EAAEtB,MAAM,CAACgB,OAAO,CAACa,eAAe,CAAC,EAAEpC,UAAU,IAAID,KAAK;IAC/G,CAAC;IAEDE,KAAK,EAAE,SAAAA,CAASsC,CAAC,EAAE;MACjB,OAAOC,SAAS,CAACrB,MAAM,IAAIlB,KAAK,GAAG,CAACsC,CAAC,EAAEvC,UAAU,IAAIC,KAAK;IAC5D,CAAC;IAEDC,QAAQ,EAAE,SAAAA,CAASqC,CAAC,EAAE;MACpB,OAAOC,SAAS,CAACrB,MAAM,IAAIjB,QAAQ,GAAG,CAACqC,CAAC,EAAEvC,UAAU,IAAIE,QAAQ;IAClE,CAAC;IAEDC,UAAU,EAAE,SAAAA,CAASoC,CAAC,EAAE;MACtB,OAAOC,SAAS,CAACrB,MAAM,IAAIhB,UAAU,GAAG,CAACoC,CAAC,EAAEvC,UAAU,IAAI,CAACG,UAAU;IACvE,CAAC;IAEDE,WAAW,EAAE,SAAAA,CAASkC,CAAC,EAAE;MACvB,OAAOC,SAAS,CAACrB,MAAM,IAAId,WAAW,GAAG,CAACkC,CAAC,EAAEvC,UAAU,IAAIK,WAAW;IACxE,CAAC;IAEDC,aAAa,EAAE,SAAAA,CAASiC,CAAC,EAAE;MACzB,OAAOC,SAAS,CAACrB,MAAM,IAAIb,aAAa,GAAG,CAAC,GAAGiC,CAAC,EAAEvC,UAAU,IAAI,CAAC,GAAGM,aAAa;IACnF,CAAC;IAEDmC,YAAY,EAAE,SAAAA,CAASF,CAAC,EAAE;MACxB,OAAOC,SAAS,CAACrB,MAAM,IAAIP,MAAM,GAAG2B,CAAC,EAAEhC,MAAM,CAACgB,OAAO,CAACa,eAAe,CAAC,EAAEpC,UAAU,IAAIY,MAAM;IAC9F,CAAC;IAEDY,KAAK,EAAE,SAAAA,CAASkB,IAAI,EAAEH,CAAC,EAAE;MACvB,OAAOC,SAAS,CAACrB,MAAM,GAAG,CAAC,IAAKoB,CAAC,IAAI,IAAI,GAAGhC,MAAM,CAACoC,MAAM,CAACD,IAAI,CAAC,GAAGnC,MAAM,CAACqC,GAAG,CAACF,IAAI,EAAEN,eAAe,CAACG,CAAC,CAAC,CAAC,EAAGvC,UAAU,IAAIO,MAAM,CAACsC,GAAG,CAACH,IAAI,CAAC;IACzI,CAAC;IAEDI,IAAI,EAAE,SAAAA,CAASvD,CAAC,EAAEE,CAAC,EAAEuC,MAAM,EAAE;MAC3B,IAAIf,CAAC,GAAG,CAAC;QACLC,CAAC,GAAGnB,KAAK,CAACoB,MAAM;QAChB4B,EAAE;QACFC,EAAE;QACFC,EAAE;QACF7B,IAAI;QACJ8B,OAAO;MAEX,IAAIlB,MAAM,IAAI,IAAI,EAAEA,MAAM,GAAGmB,QAAQ,CAAC,KACjCnB,MAAM,IAAIA,MAAM;MAErB,KAAKf,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;QACtBG,IAAI,GAAGrB,KAAK,CAACkB,CAAC,CAAC;QACf8B,EAAE,GAAGxD,CAAC,GAAG6B,IAAI,CAAC7B,CAAC;QACfyD,EAAE,GAAGvD,CAAC,GAAG2B,IAAI,CAAC3B,CAAC;QACfwD,EAAE,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;QACtB,IAAIC,EAAE,GAAGjB,MAAM,EAAEkB,OAAO,GAAG9B,IAAI,EAAEY,MAAM,GAAGiB,EAAE;MAC9C;MAEA,OAAOC,OAAO;IAChB,CAAC;IAEDE,EAAE,EAAE,SAAAA,CAASV,IAAI,EAAEH,CAAC,EAAE;MACpB,OAAOC,SAAS,CAACrB,MAAM,GAAG,CAAC,IAAIR,KAAK,CAACyC,EAAE,CAACV,IAAI,EAAEH,CAAC,CAAC,EAAEvC,UAAU,IAAIW,KAAK,CAACyC,EAAE,CAACV,IAAI,CAAC;IAChF;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}