{"ast": null, "code": "import { Typography } from 'antd';\nconst {\n  Title,\n  Text\n} = Typography;", "map": {"version": 3, "names": ["Typography", "Title", "Text"], "sources": ["/root/mes-system/mes-frontend/src/pages/Project/ProjectDetail.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Typography,\n  Card,\n  Row,\n  Col,\n  Descriptions,\n  Tag,\n  Button,\n  Space,\n  Tabs,\n  Table,\n  Progress,\n  Statistic,\n  Timeline,\n  Avatar,\n  List,\n} from 'antd';\nimport {\n  ArrowLeftOutlined,\n  EditOutlined,\n  FileTextOutlined,\n  ScheduleOutlined,\n  TeamOutlined,\n  CalendarOutlined,\n} from '@ant-design/icons';\nimport { projectService, workOrderService } from '../../services/business';\nimport { Project, WorkOrder } from '../../types';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\n"], "mappings": "AAEA,SACEA,UAAU,QAeL,MAAM;AAab,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}