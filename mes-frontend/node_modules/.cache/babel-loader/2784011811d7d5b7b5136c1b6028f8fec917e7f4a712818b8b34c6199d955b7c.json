{"ast": null, "code": "import axios from 'axios';\nimport { message, notification } from 'antd';\n// 创建axios实例\nconst api = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://************:3000/api',\n  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求重试配置\nconst MAX_RETRY_COUNT = 3;\nconst RETRY_DELAY = 1000;\n\n// 重试函数\nconst retryRequest = async (error, retryCount = 0) => {\n  if (retryCount >= MAX_RETRY_COUNT) {\n    throw error;\n  }\n\n  // 只对网络错误或5xx错误进行重试\n  if (!error.response || error.response.status >= 500 && error.response.status < 600) {\n    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retryCount + 1)));\n    return api.request(error.config);\n  }\n  throw error;\n};\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 添加认证token\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  // 检查业务状态码\n  const {\n    data\n  } = response;\n  if (data && typeof data === 'object' && 'success' in data) {\n    if (!data.success && data.message) {\n      message.error(data.message);\n      return Promise.reject(new Error(data.message));\n    }\n  }\n  return response;\n}, async error => {\n  const originalRequest = error.config;\n\n  // 统一错误处理\n  if (error.response) {\n    const {\n      status,\n      data\n    } = error.response;\n    switch (status) {\n      case 401:\n        message.error('登录已过期，请重新登录');\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n        // 避免在登录页面重复跳转\n        if (window.location.pathname !== '/login') {\n          window.location.href = '/login';\n        }\n        break;\n      case 403:\n        message.error('没有权限访问此资源');\n        break;\n      case 404:\n        message.error('请求的资源不存在');\n        break;\n      case 422:\n        // 表单验证错误\n        const errorMsg = (data === null || data === void 0 ? void 0 : data.message) || '数据验证失败';\n        message.error(errorMsg);\n        break;\n      case 429:\n        message.error('请求过于频繁，请稍后再试');\n        break;\n      case 500:\n      case 502:\n      case 503:\n      case 504:\n        // 服务器错误，尝试重试\n        try {\n          return await retryRequest(error);\n        } catch (retryError) {\n          notification.error({\n            message: '服务器错误',\n            description: '服务器暂时不可用，请稍后重试',\n            duration: 5\n          });\n        }\n        break;\n      default:\n        const errorMessage = (data === null || data === void 0 ? void 0 : data.message) || `请求失败 (${status})`;\n        message.error(errorMessage);\n    }\n  } else if (error.request) {\n    // 网络错误，尝试重试\n    try {\n      return await retryRequest(error);\n    } catch (retryError) {\n      notification.error({\n        message: '网络连接失败',\n        description: '请检查网络连接或稍后重试',\n        duration: 5\n      });\n    }\n  } else {\n    message.error('请求配置错误');\n  }\n  return Promise.reject(error);\n});\n\n// 通用API方法\nexport const apiRequest = {\n  get: (url, params) => api.get(url, {\n    params\n  }).then(res => res.data),\n  post: (url, data) => api.post(url, data).then(res => res.data),\n  put: (url, data) => api.put(url, data).then(res => res.data),\n  delete: url => api.delete(url).then(res => res.data),\n  patch: (url, data) => api.patch(url, data).then(res => res.data)\n};\n\n// 健康检查\nexport const healthCheck = () => apiRequest.get('/health');\nexport default api;", "map": {"version": 3, "names": ["axios", "message", "notification", "api", "create", "baseURL", "process", "env", "REACT_APP_API_URL", "timeout", "parseInt", "REACT_APP_REQUEST_TIMEOUT", "headers", "MAX_RETRY_COUNT", "RETRY_DELAY", "retryRequest", "error", "retryCount", "response", "status", "Promise", "resolve", "setTimeout", "request", "config", "interceptors", "use", "token", "localStorage", "getItem", "Authorization", "reject", "data", "success", "Error", "originalRequest", "removeItem", "window", "location", "pathname", "href", "errorMsg", "retryError", "description", "duration", "errorMessage", "apiRequest", "get", "url", "params", "then", "res", "post", "put", "delete", "patch", "healthCheck"], "sources": ["/root/mes-system/mes-frontend/src/services/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\nimport { message, notification } from 'antd';\nimport { ApiResponse } from '../types';\n\n// 创建axios实例\nconst api: AxiosInstance = axios.create({\n  baseURL: process.env.REACT_APP_API_URL || 'http://************:3000/api',\n  timeout: parseInt(process.env.REACT_APP_REQUEST_TIMEOUT || '10000'),\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求重试配置\nconst MAX_RETRY_COUNT = 3;\nconst RETRY_DELAY = 1000;\n\n// 重试函数\nconst retryRequest = async (error: AxiosError, retryCount = 0): Promise<any> => {\n  if (retryCount >= MAX_RETRY_COUNT) {\n    throw error;\n  }\n\n  // 只对网络错误或5xx错误进行重试\n  if (!error.response || (error.response.status >= 500 && error.response.status < 600)) {\n    await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * (retryCount + 1)));\n    return api.request(error.config!);\n  }\n\n  throw error;\n};\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 添加认证token\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response: AxiosResponse) => {\n    // 检查业务状态码\n    const { data } = response;\n    if (data && typeof data === 'object' && 'success' in data) {\n      if (!data.success && data.message) {\n        message.error(data.message);\n        return Promise.reject(new Error(data.message));\n      }\n    }\n    return response;\n  },\n  async (error: AxiosError) => {\n    const originalRequest = error.config;\n\n    // 统一错误处理\n    if (error.response) {\n      const { status, data } = error.response;\n\n      switch (status) {\n        case 401:\n          message.error('登录已过期，请重新登录');\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          // 避免在登录页面重复跳转\n          if (window.location.pathname !== '/login') {\n            window.location.href = '/login';\n          }\n          break;\n        case 403:\n          message.error('没有权限访问此资源');\n          break;\n        case 404:\n          message.error('请求的资源不存在');\n          break;\n        case 422:\n          // 表单验证错误\n          const errorMsg = (data as any)?.message || '数据验证失败';\n          message.error(errorMsg);\n          break;\n        case 429:\n          message.error('请求过于频繁，请稍后再试');\n          break;\n        case 500:\n        case 502:\n        case 503:\n        case 504:\n          // 服务器错误，尝试重试\n          try {\n            return await retryRequest(error);\n          } catch (retryError) {\n            notification.error({\n              message: '服务器错误',\n              description: '服务器暂时不可用，请稍后重试',\n              duration: 5,\n            });\n          }\n          break;\n        default:\n          const errorMessage = (data as any)?.message || `请求失败 (${status})`;\n          message.error(errorMessage);\n      }\n    } else if (error.request) {\n      // 网络错误，尝试重试\n      try {\n        return await retryRequest(error);\n      } catch (retryError) {\n        notification.error({\n          message: '网络连接失败',\n          description: '请检查网络连接或稍后重试',\n          duration: 5,\n        });\n      }\n    } else {\n      message.error('请求配置错误');\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// 通用API方法\nexport const apiRequest = {\n  get: <T>(url: string, params?: any): Promise<ApiResponse<T>> =>\n    api.get(url, { params }).then(res => res.data),\n    \n  post: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>\n    api.post(url, data).then(res => res.data),\n    \n  put: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>\n    api.put(url, data).then(res => res.data),\n    \n  delete: <T>(url: string): Promise<ApiResponse<T>> =>\n    api.delete(url).then(res => res.data),\n    \n  patch: <T>(url: string, data?: any): Promise<ApiResponse<T>> =>\n    api.patch(url, data).then(res => res.data),\n};\n\n// 健康检查\nexport const healthCheck = () => apiRequest.get('/health');\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAoD,OAAO;AACvE,SAASC,OAAO,EAAEC,YAAY,QAAQ,MAAM;AAG5C;AACA,MAAMC,GAAkB,GAAGH,KAAK,CAACI,MAAM,CAAC;EACtCC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,8BAA8B;EACxEC,OAAO,EAAEC,QAAQ,CAACJ,OAAO,CAACC,GAAG,CAACI,yBAAyB,IAAI,OAAO,CAAC;EACnEC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,WAAW,GAAG,IAAI;;AAExB;AACA,MAAMC,YAAY,GAAG,MAAAA,CAAOC,KAAiB,EAAEC,UAAU,GAAG,CAAC,KAAmB;EAC9E,IAAIA,UAAU,IAAIJ,eAAe,EAAE;IACjC,MAAMG,KAAK;EACb;;EAEA;EACA,IAAI,CAACA,KAAK,CAACE,QAAQ,IAAKF,KAAK,CAACE,QAAQ,CAACC,MAAM,IAAI,GAAG,IAAIH,KAAK,CAACE,QAAQ,CAACC,MAAM,GAAG,GAAI,EAAE;IACpF,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEP,WAAW,IAAIG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IACjF,OAAOd,GAAG,CAACoB,OAAO,CAACP,KAAK,CAACQ,MAAO,CAAC;EACnC;EAEA,MAAMR,KAAK;AACb,CAAC;;AAED;AACAb,GAAG,CAACsB,YAAY,CAACF,OAAO,CAACG,GAAG,CACzBF,MAAM,IAAK;EACV;EACA,MAAMG,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTH,MAAM,CAACZ,OAAO,CAACkB,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOH,MAAM;AACf,CAAC,EACAR,KAAK,IAAK;EACT,OAAOI,OAAO,CAACW,MAAM,CAACf,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACsB,YAAY,CAACP,QAAQ,CAACQ,GAAG,CAC1BR,QAAuB,IAAK;EAC3B;EACA,MAAM;IAAEc;EAAK,CAAC,GAAGd,QAAQ;EACzB,IAAIc,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,SAAS,IAAIA,IAAI,EAAE;IACzD,IAAI,CAACA,IAAI,CAACC,OAAO,IAAID,IAAI,CAAC/B,OAAO,EAAE;MACjCA,OAAO,CAACe,KAAK,CAACgB,IAAI,CAAC/B,OAAO,CAAC;MAC3B,OAAOmB,OAAO,CAACW,MAAM,CAAC,IAAIG,KAAK,CAACF,IAAI,CAAC/B,OAAO,CAAC,CAAC;IAChD;EACF;EACA,OAAOiB,QAAQ;AACjB,CAAC,EACD,MAAOF,KAAiB,IAAK;EAC3B,MAAMmB,eAAe,GAAGnB,KAAK,CAACQ,MAAM;;EAEpC;EACA,IAAIR,KAAK,CAACE,QAAQ,EAAE;IAClB,MAAM;MAAEC,MAAM;MAAEa;IAAK,CAAC,GAAGhB,KAAK,CAACE,QAAQ;IAEvC,QAAQC,MAAM;MACZ,KAAK,GAAG;QACNlB,OAAO,CAACe,KAAK,CAAC,aAAa,CAAC;QAC5BY,YAAY,CAACQ,UAAU,CAAC,OAAO,CAAC;QAChCR,YAAY,CAACQ,UAAU,CAAC,MAAM,CAAC;QAC/B;QACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;UACzCF,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,QAAQ;QACjC;QACA;MACF,KAAK,GAAG;QACNvC,OAAO,CAACe,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF,KAAK,GAAG;QACNf,OAAO,CAACe,KAAK,CAAC,UAAU,CAAC;QACzB;MACF,KAAK,GAAG;QACN;QACA,MAAMyB,QAAQ,GAAG,CAACT,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAU/B,OAAO,KAAI,QAAQ;QACnDA,OAAO,CAACe,KAAK,CAACyB,QAAQ,CAAC;QACvB;MACF,KAAK,GAAG;QACNxC,OAAO,CAACe,KAAK,CAAC,cAAc,CAAC;QAC7B;MACF,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;MACR,KAAK,GAAG;QACN;QACA,IAAI;UACF,OAAO,MAAMD,YAAY,CAACC,KAAK,CAAC;QAClC,CAAC,CAAC,OAAO0B,UAAU,EAAE;UACnBxC,YAAY,CAACc,KAAK,CAAC;YACjBf,OAAO,EAAE,OAAO;YAChB0C,WAAW,EAAE,gBAAgB;YAC7BC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;QACA;MACF;QACE,MAAMC,YAAY,GAAG,CAACb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAU/B,OAAO,KAAI,SAASkB,MAAM,GAAG;QACjElB,OAAO,CAACe,KAAK,CAAC6B,YAAY,CAAC;IAC/B;EACF,CAAC,MAAM,IAAI7B,KAAK,CAACO,OAAO,EAAE;IACxB;IACA,IAAI;MACF,OAAO,MAAMR,YAAY,CAACC,KAAK,CAAC;IAClC,CAAC,CAAC,OAAO0B,UAAU,EAAE;MACnBxC,YAAY,CAACc,KAAK,CAAC;QACjBf,OAAO,EAAE,QAAQ;QACjB0C,WAAW,EAAE,cAAc;QAC3BC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,MAAM;IACL3C,OAAO,CAACe,KAAK,CAAC,QAAQ,CAAC;EACzB;EAEA,OAAOI,OAAO,CAACW,MAAM,CAACf,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,OAAO,MAAM8B,UAAU,GAAG;EACxBC,GAAG,EAAEA,CAAIC,GAAW,EAAEC,MAAY,KAChC9C,GAAG,CAAC4C,GAAG,CAACC,GAAG,EAAE;IAAEC;EAAO,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnB,IAAI,CAAC;EAEhDoB,IAAI,EAAEA,CAAIJ,GAAW,EAAEhB,IAAU,KAC/B7B,GAAG,CAACiD,IAAI,CAACJ,GAAG,EAAEhB,IAAI,CAAC,CAACkB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnB,IAAI,CAAC;EAE3CqB,GAAG,EAAEA,CAAIL,GAAW,EAAEhB,IAAU,KAC9B7B,GAAG,CAACkD,GAAG,CAACL,GAAG,EAAEhB,IAAI,CAAC,CAACkB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnB,IAAI,CAAC;EAE1CsB,MAAM,EAAMN,GAAW,IACrB7C,GAAG,CAACmD,MAAM,CAACN,GAAG,CAAC,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnB,IAAI,CAAC;EAEvCuB,KAAK,EAAEA,CAAIP,GAAW,EAAEhB,IAAU,KAChC7B,GAAG,CAACoD,KAAK,CAACP,GAAG,EAAEhB,IAAI,CAAC,CAACkB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnB,IAAI;AAC7C,CAAC;;AAED;AACA,OAAO,MAAMwB,WAAW,GAAGA,CAAA,KAAMV,UAAU,CAACC,GAAG,CAAC,SAAS,CAAC;AAE1D,eAAe5C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}