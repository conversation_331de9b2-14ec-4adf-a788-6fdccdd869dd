{"ast": null, "code": "import groupToMap from './group-to-map';\nexport default (function (data, condition) {\n  if (!condition) {\n    // 没有条件，则自身改成数组\n    return [data];\n  }\n  var groups = groupToMap(data, condition);\n  var array = [];\n  for (var i in groups) {\n    array.push(groups[i]);\n  }\n  return array;\n});", "map": {"version": 3, "names": ["groupToMap", "data", "condition", "groups", "array", "i", "push"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/group.ts"], "sourcesContent": ["import groupToMap from './group-to-map';\n\nexport default <T>(data: T[], condition: ((v: T) => string) | string | string[]): T[][] => {\n  if (!condition) {\n    // 没有条件，则自身改成数组\n    return [ data ];\n  }\n  const groups = groupToMap(data, condition) as { [key: string]: T };\n  const array = [];\n  for (const i in groups) {\n    array.push(groups[i]);\n  }\n  return array;\n};\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,gBAAgB;AAEvC,gBAAe,UAAIC,IAAS,EAAEC,SAAiD;EAC7E,IAAI,CAACA,SAAS,EAAE;IACd;IACA,OAAO,CAAED,IAAI,CAAE;;EAEjB,IAAME,MAAM,GAAGH,UAAU,CAACC,IAAI,EAAEC,SAAS,CAAyB;EAClE,IAAME,KAAK,GAAG,EAAE;EAChB,KAAK,IAAMC,CAAC,IAAIF,MAAM,EAAE;IACtBC,KAAK,CAACE,IAAI,CAACH,MAAM,CAACE,CAAC,CAAC,CAAC;;EAEvB,OAAOD,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}