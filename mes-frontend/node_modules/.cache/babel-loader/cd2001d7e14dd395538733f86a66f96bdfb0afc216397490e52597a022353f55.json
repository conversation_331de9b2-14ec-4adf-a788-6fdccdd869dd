{"ast": null, "code": "var copyObject = require('./_copyObject'),\n  keysIn = require('./keysIn');\n\n/**\n * The base implementation of `_.assignIn` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssignIn(object, source) {\n  return object && copyObject(source, keysIn(source), object);\n}\nmodule.exports = baseAssignIn;", "map": {"version": 3, "names": ["copyObject", "require", "keysIn", "baseAssignIn", "object", "source", "module", "exports"], "sources": ["/root/mes-system/mes-frontend/node_modules/lodash/_baseAssignIn.js"], "sourcesContent": ["var copyObject = require('./_copyObject'),\n    keysIn = require('./keysIn');\n\n/**\n * The base implementation of `_.assignIn` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssignIn(object, source) {\n  return object && copyObject(source, keysIn(source), object);\n}\n\nmodule.exports = baseAssignIn;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,MAAM,GAAGD,OAAO,CAAC,UAAU,CAAC;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACpC,OAAOD,MAAM,IAAIJ,UAAU,CAACK,MAAM,EAAEH,MAAM,CAACG,MAAM,CAAC,EAAED,MAAM,CAAC;AAC7D;AAEAE,MAAM,CAACC,OAAO,GAAGJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}