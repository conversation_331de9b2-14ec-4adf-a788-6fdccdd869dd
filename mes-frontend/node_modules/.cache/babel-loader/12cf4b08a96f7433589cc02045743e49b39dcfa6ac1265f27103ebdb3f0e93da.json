{"ast": null, "code": "import isArray from './is-array';\nimport isString from './is-string';\nfunction startsWith(arr, e) {\n  return isArray(arr) || isString(arr) ? arr[0] === e : false;\n}\nexport default startsWith;", "map": {"version": 3, "names": ["isArray", "isString", "startsWith", "arr", "e"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/starts-with.ts"], "sourcesContent": ["import isArray from './is-array';\nimport isString from './is-string';\n\nfunction startsWith(arr: string, e: string): boolean;\nfunction startsWith<T>(arr: T[], e: T): boolean;\nfunction startsWith<T>(arr: string | T[], e: string | T): boolean {\n  return (isArray(arr) || isString(arr)) ? arr[0] === e : false;\n}\n\nexport default startsWith;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,YAAY;AAChC,OAAOC,QAAQ,MAAM,aAAa;AAIlC,SAASC,UAAUA,CAAIC,GAAiB,EAAEC,CAAa;EACrD,OAAQJ,OAAO,CAACG,GAAG,CAAC,IAAIF,QAAQ,CAACE,GAAG,CAAC,GAAIA,GAAG,CAAC,CAAC,CAAC,KAAKC,CAAC,GAAG,KAAK;AAC/D;AAEA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}