{"ast": null, "code": "import { segmentLineFactory } from './segment-line-factory';\nimport { distanceSquareRoot } from './distance-square-root';\nfunction angleBetween(v0, v1) {\n  var v0x = v0.x,\n    v0y = v0.y;\n  var v1x = v1.x,\n    v1y = v1.y;\n  var p = v0x * v1x + v0y * v1y;\n  var n = Math.sqrt((Math.pow(v0x, 2) + Math.pow(v0y, 2)) * (Math.pow(v1x, 2) + Math.pow(v1y, 2)));\n  var sign = v0x * v1y - v0y * v1x < 0 ? -1 : 1;\n  var angle = sign * Math.acos(p / n);\n  return angle;\n}\n/**\n * Returns a {x,y} point at a given length, the total length and\n * the minimum and maximum {x,y} coordinates of a C (cubic-bezier) segment.\n * @see https://github.com/MadLittleMods/svg-curve-lib/blob/master/src/js/svg-curve-lib.js\n */\nfunction getPointAtArcSegmentLength(x1, y1, RX, RY, angle, LAF, SF, x, y, t) {\n  var abs = Math.abs,\n    sin = Math.sin,\n    cos = Math.cos,\n    sqrt = Math.sqrt,\n    PI = Math.PI;\n  var rx = abs(RX);\n  var ry = abs(RY);\n  var xRot = (angle % 360 + 360) % 360;\n  var xRotRad = xRot * (PI / 180);\n  if (x1 === x && y1 === y) {\n    return {\n      x: x1,\n      y: y1\n    };\n  }\n  if (rx === 0 || ry === 0) {\n    return segmentLineFactory(x1, y1, x, y, t).point;\n  }\n  var dx = (x1 - x) / 2;\n  var dy = (y1 - y) / 2;\n  var transformedPoint = {\n    x: cos(xRotRad) * dx + sin(xRotRad) * dy,\n    y: -sin(xRotRad) * dx + cos(xRotRad) * dy\n  };\n  var radiiCheck = Math.pow(transformedPoint.x, 2) / Math.pow(rx, 2) + Math.pow(transformedPoint.y, 2) / Math.pow(ry, 2);\n  if (radiiCheck > 1) {\n    rx *= sqrt(radiiCheck);\n    ry *= sqrt(radiiCheck);\n  }\n  var cSquareNumerator = Math.pow(rx, 2) * Math.pow(ry, 2) - Math.pow(rx, 2) * Math.pow(transformedPoint.y, 2) - Math.pow(ry, 2) * Math.pow(transformedPoint.x, 2);\n  var cSquareRootDenom = Math.pow(rx, 2) * Math.pow(transformedPoint.y, 2) + Math.pow(ry, 2) * Math.pow(transformedPoint.x, 2);\n  var cRadicand = cSquareNumerator / cSquareRootDenom;\n  cRadicand = cRadicand < 0 ? 0 : cRadicand;\n  var cCoef = (LAF !== SF ? 1 : -1) * sqrt(cRadicand);\n  var transformedCenter = {\n    x: cCoef * (rx * transformedPoint.y / ry),\n    y: cCoef * (-(ry * transformedPoint.x) / rx)\n  };\n  var center = {\n    x: cos(xRotRad) * transformedCenter.x - sin(xRotRad) * transformedCenter.y + (x1 + x) / 2,\n    y: sin(xRotRad) * transformedCenter.x + cos(xRotRad) * transformedCenter.y + (y1 + y) / 2\n  };\n  var startVector = {\n    x: (transformedPoint.x - transformedCenter.x) / rx,\n    y: (transformedPoint.y - transformedCenter.y) / ry\n  };\n  var startAngle = angleBetween({\n    x: 1,\n    y: 0\n  }, startVector);\n  var endVector = {\n    x: (-transformedPoint.x - transformedCenter.x) / rx,\n    y: (-transformedPoint.y - transformedCenter.y) / ry\n  };\n  var sweepAngle = angleBetween(startVector, endVector);\n  if (!SF && sweepAngle > 0) {\n    sweepAngle -= 2 * PI;\n  } else if (SF && sweepAngle < 0) {\n    sweepAngle += 2 * PI;\n  }\n  sweepAngle %= 2 * PI;\n  var alpha = startAngle + sweepAngle * t;\n  var ellipseComponentX = rx * cos(alpha);\n  var ellipseComponentY = ry * sin(alpha);\n  var point = {\n    x: cos(xRotRad) * ellipseComponentX - sin(xRotRad) * ellipseComponentY + center.x,\n    y: sin(xRotRad) * ellipseComponentX + cos(xRotRad) * ellipseComponentY + center.y\n  };\n  // to be used later\n  // point.ellipticalArcStartAngle = startAngle;\n  // point.ellipticalArcEndAngle = startAngle + sweepAngle;\n  // point.ellipticalArcAngle = alpha;\n  // point.ellipticalArcCenter = center;\n  // point.resultantRx = rx;\n  // point.resultantRy = ry;\n  return point;\n}\n/**\n * Returns a {x,y} point at a given length, the total length and\n * the shape minimum and maximum {x,y} coordinates of an A (arc-to) segment.\n *\n * For better performance, it can skip calculate bbox or length in some scenario.\n */\nexport function segmentArcFactory(X1, Y1, RX, RY, angle, LAF, SF, X2, Y2, distance, options) {\n  var _a;\n  var _b = options.bbox,\n    bbox = _b === void 0 ? true : _b,\n    _c = options.length,\n    length = _c === void 0 ? true : _c,\n    _d = options.sampleSize,\n    sampleSize = _d === void 0 ? 30 : _d;\n  var distanceIsNumber = typeof distance === 'number';\n  var x = X1;\n  var y = Y1;\n  var LENGTH = 0;\n  var prev = [x, y, LENGTH];\n  var cur = [x, y];\n  var t = 0;\n  var POINT = {\n    x: 0,\n    y: 0\n  };\n  var POINTS = [{\n    x: x,\n    y: y\n  }];\n  if (distanceIsNumber && distance <= 0) {\n    POINT = {\n      x: x,\n      y: y\n    };\n  }\n  // bad perf when size > 100\n  for (var j = 0; j <= sampleSize; j += 1) {\n    t = j / sampleSize;\n    _a = getPointAtArcSegmentLength(X1, Y1, RX, RY, angle, LAF, SF, X2, Y2, t), x = _a.x, y = _a.y;\n    if (bbox) {\n      POINTS.push({\n        x: x,\n        y: y\n      });\n    }\n    if (length) {\n      LENGTH += distanceSquareRoot(cur, [x, y]);\n    }\n    cur = [x, y];\n    if (distanceIsNumber && LENGTH >= distance && distance > prev[2]) {\n      var dv = (LENGTH - distance) / (LENGTH - prev[2]);\n      POINT = {\n        x: cur[0] * (1 - dv) + prev[0] * dv,\n        y: cur[1] * (1 - dv) + prev[1] * dv\n      };\n    }\n    prev = [x, y, LENGTH];\n  }\n  if (distanceIsNumber && distance >= LENGTH) {\n    POINT = {\n      x: X2,\n      y: Y2\n    };\n  }\n  return {\n    length: LENGTH,\n    point: POINT,\n    min: {\n      x: Math.min.apply(null, POINTS.map(function (n) {\n        return n.x;\n      })),\n      y: Math.min.apply(null, POINTS.map(function (n) {\n        return n.y;\n      }))\n    },\n    max: {\n      x: Math.max.apply(null, POINTS.map(function (n) {\n        return n.x;\n      })),\n      y: Math.max.apply(null, POINTS.map(function (n) {\n        return n.y;\n      }))\n    }\n  };\n}", "map": {"version": 3, "names": ["segmentLineFactory", "distanceSquareRoot", "angleBetween", "v0", "v1", "v0x", "x", "v0y", "y", "v1x", "v1y", "p", "n", "Math", "sqrt", "pow", "sign", "angle", "acos", "getPointAtArcSegmentLength", "x1", "y1", "RX", "RY", "LAF", "SF", "t", "abs", "sin", "cos", "PI", "rx", "ry", "xRot", "xRotRad", "point", "dx", "dy", "transformedPoint", "radiiCheck", "cSquareNumerator", "cSquareRootDenom", "cRadicand", "c<PERSON><PERSON><PERSON>", "transformedCenter", "center", "startVector", "startAngle", "endVector", "sweepAngle", "alpha", "ellipseComponentX", "ellipseComponentY", "segmentArcFactory", "X1", "Y1", "X2", "Y2", "distance", "options", "_b", "bbox", "_c", "length", "_d", "sampleSize", "distanceIsNumber", "LENGTH", "prev", "cur", "POINT", "POINTS", "j", "_a", "push", "dv", "min", "apply", "map", "max"], "sources": ["path/util/segment-arc-factory.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D,SAASC,YAAYA,CAACC,EAAS,EAAEC,EAAS;EAChC,IAAGC,GAAG,GAAaF,EAAE,CAAAG,CAAf;IAAKC,GAAG,GAAKJ,EAAE,CAAAK,CAAP;EACd,IAAGC,GAAG,GAAaL,EAAE,CAAAE,CAAf;IAAKI,GAAG,GAAKN,EAAE,CAAAI,CAAP;EACtB,IAAMG,CAAC,GAAGN,GAAG,GAAGI,GAAG,GAAGF,GAAG,GAAGG,GAAG;EAC/B,IAAME,CAAC,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACD,IAAA,CAAAE,GAAA,CAAAV,GAAG,EAAI,CAAC,IAAGQ,IAAA,CAAAE,GAAA,CAAAR,GAAG,EAAI,CAAC,MAAKM,IAAA,CAAAE,GAAA,CAAAN,GAAG,EAAI,CAAC,IAAGI,IAAA,CAAAE,GAAA,CAAAL,GAAG,EAAI,CAAC,EAAC,CAAC;EAClE,IAAMM,IAAI,GAAGX,GAAG,GAAGK,GAAG,GAAGH,GAAG,GAAGE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAC/C,IAAMQ,KAAK,GAAGD,IAAI,GAAGH,IAAI,CAACK,IAAI,CAACP,CAAC,GAAGC,CAAC,CAAC;EAErC,OAAOK,KAAK;AACd;AAEA;;;;;AAKA,SAASE,0BAA0BA,CACjCC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVN,KAAa,EACbO,GAAW,EACXC,EAAU,EACVnB,CAAS,EACTE,CAAS,EACTkB,CAAS;EAED,IAAAC,GAAG,GAAyBd,IAAI,CAAAc,GAA7B;IAAEC,GAAG,GAAoBf,IAAI,CAAAe,GAAxB;IAAEC,GAAG,GAAehB,IAAI,CAAAgB,GAAnB;IAAEf,IAAI,GAASD,IAAI,CAAAC,IAAb;IAAEgB,EAAE,GAAKjB,IAAI,CAAAiB,EAAT;EAC/B,IAAIC,EAAE,GAAGJ,GAAG,CAACL,EAAE,CAAC;EAChB,IAAIU,EAAE,GAAGL,GAAG,CAACJ,EAAE,CAAC;EAChB,IAAMU,IAAI,GAAG,CAAEhB,KAAK,GAAG,GAAG,GAAI,GAAG,IAAI,GAAG;EACxC,IAAMiB,OAAO,GAAGD,IAAI,IAAIH,EAAE,GAAG,GAAG,CAAC;EAEjC,IAAIV,EAAE,KAAKd,CAAC,IAAIe,EAAE,KAAKb,CAAC,EAAE;IACxB,OAAO;MAAEF,CAAC,EAAEc,EAAE;MAAEZ,CAAC,EAAEa;IAAE,CAAE;EACzB;EAEA,IAAIU,EAAE,KAAK,CAAC,IAAIC,EAAE,KAAK,CAAC,EAAE;IACxB,OAAOhC,kBAAkB,CAACoB,EAAE,EAAEC,EAAE,EAAEf,CAAC,EAAEE,CAAC,EAAEkB,CAAC,CAAC,CAACS,KAAK;EAClD;EAEA,IAAMC,EAAE,GAAG,CAAChB,EAAE,GAAGd,CAAC,IAAI,CAAC;EACvB,IAAM+B,EAAE,GAAG,CAAChB,EAAE,GAAGb,CAAC,IAAI,CAAC;EAEvB,IAAM8B,gBAAgB,GAAG;IACvBhC,CAAC,EAAEuB,GAAG,CAACK,OAAO,CAAC,GAAGE,EAAE,GAAGR,GAAG,CAACM,OAAO,CAAC,GAAGG,EAAE;IACxC7B,CAAC,EAAE,CAACoB,GAAG,CAACM,OAAO,CAAC,GAAGE,EAAE,GAAGP,GAAG,CAACK,OAAO,CAAC,GAAGG;GACxC;EAED,IAAME,UAAU,GAAG1B,IAAA,CAAAE,GAAA,CAAAuB,gBAAgB,CAAChC,CAAC,EAAI,CAAC,IAAGO,IAAA,CAAAE,GAAA,CAAAgB,EAAE,EAAI,CAAC,IAAGlB,IAAA,CAAAE,GAAA,CAAAuB,gBAAgB,CAAC9B,CAAC,EAAI,CAAC,IAAGK,IAAA,CAAAE,GAAA,CAAAiB,EAAE,EAAI,CAAC;EAExF,IAAIO,UAAU,GAAG,CAAC,EAAE;IAClBR,EAAE,IAAIjB,IAAI,CAACyB,UAAU,CAAC;IACtBP,EAAE,IAAIlB,IAAI,CAACyB,UAAU,CAAC;EACxB;EAEA,IAAMC,gBAAgB,GAAG3B,IAAA,CAAAE,GAAA,CAAAgB,EAAE,EAAI,CAAC,IAAGlB,IAAA,CAAAE,GAAA,CAAAiB,EAAE,EAAI,CAAC,IAAGnB,IAAA,CAAAE,GAAA,CAAAgB,EAAE,EAAI,CAAC,IAAGlB,IAAA,CAAAE,GAAA,CAAAuB,gBAAgB,CAAC9B,CAAC,EAAI,CAAC,IAAGK,IAAA,CAAAE,GAAA,CAAAiB,EAAE,EAAI,CAAC,IAAGnB,IAAA,CAAAE,GAAA,CAAAuB,gBAAgB,CAAChC,CAAC,EAAI,CAAC;EAElH,IAAMmC,gBAAgB,GAAG5B,IAAA,CAAAE,GAAA,CAAAgB,EAAE,EAAI,CAAC,IAAGlB,IAAA,CAAAE,GAAA,CAAAuB,gBAAgB,CAAC9B,CAAC,EAAI,CAAC,IAAGK,IAAA,CAAAE,GAAA,CAAAiB,EAAE,EAAI,CAAC,IAAGnB,IAAA,CAAAE,GAAA,CAAAuB,gBAAgB,CAAChC,CAAC,EAAI,CAAC;EAE9F,IAAIoC,SAAS,GAAGF,gBAAgB,GAAGC,gBAAgB;EACnDC,SAAS,GAAGA,SAAS,GAAG,CAAC,GAAG,CAAC,GAAGA,SAAS;EACzC,IAAMC,KAAK,GAAG,CAACnB,GAAG,KAAKC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,IAAIX,IAAI,CAAC4B,SAAS,CAAC;EACrD,IAAME,iBAAiB,GAAG;IACxBtC,CAAC,EAAEqC,KAAK,IAAKZ,EAAE,GAAGO,gBAAgB,CAAC9B,CAAC,GAAIwB,EAAE,CAAC;IAC3CxB,CAAC,EAAEmC,KAAK,IAAI,EAAEX,EAAE,GAAGM,gBAAgB,CAAChC,CAAC,CAAC,GAAGyB,EAAE;GAC5C;EAED,IAAMc,MAAM,GAAG;IACbvC,CAAC,EAAEuB,GAAG,CAACK,OAAO,CAAC,GAAGU,iBAAiB,CAACtC,CAAC,GAAGsB,GAAG,CAACM,OAAO,CAAC,GAAGU,iBAAiB,CAACpC,CAAC,GAAG,CAACY,EAAE,GAAGd,CAAC,IAAI,CAAC;IACzFE,CAAC,EAAEoB,GAAG,CAACM,OAAO,CAAC,GAAGU,iBAAiB,CAACtC,CAAC,GAAGuB,GAAG,CAACK,OAAO,CAAC,GAAGU,iBAAiB,CAACpC,CAAC,GAAG,CAACa,EAAE,GAAGb,CAAC,IAAI;GACzF;EAED,IAAMsC,WAAW,GAAG;IAClBxC,CAAC,EAAE,CAACgC,gBAAgB,CAAChC,CAAC,GAAGsC,iBAAiB,CAACtC,CAAC,IAAIyB,EAAE;IAClDvB,CAAC,EAAE,CAAC8B,gBAAgB,CAAC9B,CAAC,GAAGoC,iBAAiB,CAACpC,CAAC,IAAIwB;GACjD;EAED,IAAMe,UAAU,GAAG7C,YAAY,CAAC;IAAEI,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAE;EAAC,CAAE,EAAEsC,WAAW,CAAC;EAE5D,IAAME,SAAS,GAAG;IAChB1C,CAAC,EAAE,CAAC,CAACgC,gBAAgB,CAAChC,CAAC,GAAGsC,iBAAiB,CAACtC,CAAC,IAAIyB,EAAE;IACnDvB,CAAC,EAAE,CAAC,CAAC8B,gBAAgB,CAAC9B,CAAC,GAAGoC,iBAAiB,CAACpC,CAAC,IAAIwB;GAClD;EAED,IAAIiB,UAAU,GAAG/C,YAAY,CAAC4C,WAAW,EAAEE,SAAS,CAAC;EACrD,IAAI,CAACvB,EAAE,IAAIwB,UAAU,GAAG,CAAC,EAAE;IACzBA,UAAU,IAAI,CAAC,GAAGnB,EAAE;EACtB,CAAC,MAAM,IAAIL,EAAE,IAAIwB,UAAU,GAAG,CAAC,EAAE;IAC/BA,UAAU,IAAI,CAAC,GAAGnB,EAAE;EACtB;EACAmB,UAAU,IAAI,CAAC,GAAGnB,EAAE;EAEpB,IAAMoB,KAAK,GAAGH,UAAU,GAAGE,UAAU,GAAGvB,CAAC;EACzC,IAAMyB,iBAAiB,GAAGpB,EAAE,GAAGF,GAAG,CAACqB,KAAK,CAAC;EACzC,IAAME,iBAAiB,GAAGpB,EAAE,GAAGJ,GAAG,CAACsB,KAAK,CAAC;EAEzC,IAAMf,KAAK,GAAG;IACZ7B,CAAC,EAAEuB,GAAG,CAACK,OAAO,CAAC,GAAGiB,iBAAiB,GAAGvB,GAAG,CAACM,OAAO,CAAC,GAAGkB,iBAAiB,GAAGP,MAAM,CAACvC,CAAC;IACjFE,CAAC,EAAEoB,GAAG,CAACM,OAAO,CAAC,GAAGiB,iBAAiB,GAAGtB,GAAG,CAACK,OAAO,CAAC,GAAGkB,iBAAiB,GAAGP,MAAM,CAACrC;GACjF;EAED;EACA;EACA;EACA;EAEA;EACA;EACA;EAEA,OAAO2B,KAAK;AACd;AAEA;;;;;;AAMA,OAAM,SAAUkB,iBAAiBA,CAC/BC,EAAU,EACVC,EAAU,EACVjC,EAAU,EACVC,EAAU,EACVN,KAAa,EACbO,GAAW,EACXC,EAAU,EACV+B,EAAU,EACVC,EAAU,EACVC,QAAgB,EAChBC,OAA0C;;EAElC,IAAAC,EAAA,GAAgDD,OAAO,CAAAE,IAA5C;IAAXA,IAAI,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAEE,EAAA,GAAmCH,OAAO,CAAAI,MAA7B;IAAbA,MAAM,GAAAD,EAAA,cAAG,IAAI,GAAAA,EAAA;IAAEE,EAAA,GAAoBL,OAAO,CAAAM,UAAZ;IAAfA,UAAU,GAAAD,EAAA,cAAG,EAAE,GAAAA,EAAA;EACnD,IAAME,gBAAgB,GAAG,OAAOR,QAAQ,KAAK,QAAQ;EACrD,IAAIpD,CAAC,GAAGgD,EAAE;EACV,IAAI9C,CAAC,GAAG+C,EAAE;EACV,IAAIY,MAAM,GAAG,CAAC;EACd,IAAIC,IAAI,GAAG,CAAC9D,CAAC,EAAEE,CAAC,EAAE2D,MAAM,CAAC;EACzB,IAAIE,GAAG,GAAqB,CAAC/D,CAAC,EAAEE,CAAC,CAAC;EAClC,IAAIkB,CAAC,GAAG,CAAC;EACT,IAAI4C,KAAK,GAAG;IAAEhE,CAAC,EAAE,CAAC;IAAEE,CAAC,EAAE;EAAC,CAAE;EAC1B,IAAM+D,MAAM,GAAG,CAAC;IAAEjE,CAAC,EAAAA,CAAA;IAAEE,CAAC,EAAAA;EAAA,CAAE,CAAC;EAEzB,IAAI0D,gBAAgB,IAAIR,QAAQ,IAAI,CAAC,EAAE;IACrCY,KAAK,GAAG;MAAEhE,CAAC,EAAAA,CAAA;MAAEE,CAAC,EAAAA;IAAA,CAAE;EAClB;EAEA;EACA,KAAK,IAAIgE,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIP,UAAU,EAAEO,CAAC,IAAI,CAAC,EAAE;IACvC9C,CAAC,GAAG8C,CAAC,GAAGP,UAAU;IAEjBQ,EAAA,GAAWtD,0BAA0B,CAACmC,EAAE,EAAEC,EAAE,EAAEjC,EAAE,EAAEC,EAAE,EAAEN,KAAK,EAAEO,GAAG,EAAEC,EAAE,EAAE+B,EAAE,EAAEC,EAAE,EAAE/B,CAAC,CAAC,EAA9EpB,CAAC,GAAAmE,EAAA,CAAAnE,CAAA,EAAEE,CAAC,GAAAiE,EAAA,CAAAjE,CAAA;IAEP,IAAIqD,IAAI,EAAE;MACRU,MAAM,CAACG,IAAI,CAAC;QAAEpE,CAAC,EAAAA,CAAA;QAAEE,CAAC,EAAAA;MAAA,CAAE,CAAC;IACvB;IAEA,IAAIuD,MAAM,EAAE;MACVI,MAAM,IAAIlE,kBAAkB,CAACoE,GAAG,EAAE,CAAC/D,CAAC,EAAEE,CAAC,CAAC,CAAC;IAC3C;IACA6D,GAAG,GAAG,CAAC/D,CAAC,EAAEE,CAAC,CAAC;IAEZ,IAAI0D,gBAAgB,IAAIC,MAAM,IAAIT,QAAQ,IAAIA,QAAQ,GAAGU,IAAI,CAAC,CAAC,CAAC,EAAE;MAChE,IAAMO,EAAE,GAAG,CAACR,MAAM,GAAGT,QAAQ,KAAKS,MAAM,GAAGC,IAAI,CAAC,CAAC,CAAC,CAAC;MAEnDE,KAAK,GAAG;QACNhE,CAAC,EAAE+D,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGM,EAAE,CAAC,GAAGP,IAAI,CAAC,CAAC,CAAC,GAAGO,EAAE;QACnCnE,CAAC,EAAE6D,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGM,EAAE,CAAC,GAAGP,IAAI,CAAC,CAAC,CAAC,GAAGO;OAClC;IACH;IACAP,IAAI,GAAG,CAAC9D,CAAC,EAAEE,CAAC,EAAE2D,MAAM,CAAC;EACvB;EAEA,IAAID,gBAAgB,IAAIR,QAAQ,IAAIS,MAAM,EAAE;IAC1CG,KAAK,GAAG;MAAEhE,CAAC,EAAEkD,EAAE;MAAEhD,CAAC,EAAEiD;IAAE,CAAE;EAC1B;EAEA,OAAO;IACLM,MAAM,EAAEI,MAAM;IACdhC,KAAK,EAAEmC,KAAK;IACZM,GAAG,EAAE;MACHtE,CAAC,EAAEO,IAAI,CAAC+D,GAAG,CAACC,KAAK,CACf,IAAI,EACJN,MAAM,CAACO,GAAG,CAAC,UAAClE,CAAC;QAAK,OAAAA,CAAC,CAACN,CAAC;MAAH,CAAG,CAAC,CACvB;MACDE,CAAC,EAAEK,IAAI,CAAC+D,GAAG,CAACC,KAAK,CACf,IAAI,EACJN,MAAM,CAACO,GAAG,CAAC,UAAClE,CAAC;QAAK,OAAAA,CAAC,CAACJ,CAAC;MAAH,CAAG,CAAC;KAEzB;IACDuE,GAAG,EAAE;MACHzE,CAAC,EAAEO,IAAI,CAACkE,GAAG,CAACF,KAAK,CACf,IAAI,EACJN,MAAM,CAACO,GAAG,CAAC,UAAClE,CAAC;QAAK,OAAAA,CAAC,CAACN,CAAC;MAAH,CAAG,CAAC,CACvB;MACDE,CAAC,EAAEK,IAAI,CAACkE,GAAG,CAACF,KAAK,CACf,IAAI,EACJN,MAAM,CAACO,GAAG,CAAC,UAAClE,CAAC;QAAK,OAAAA,CAAC,CAACJ,CAAC;MAAH,CAAG,CAAC;;GAG3B;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}