{"ast": null, "code": "var _ = require(\"../lodash\");\nvar PriorityQueue = require(\"../data/priority-queue\");\nmodule.exports = dijkstra;\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(g, String(source), weightFn || DEFAULT_WEIGHT_FUNC, edgeFn || function (v) {\n    return g.outEdges(v);\n  });\n}\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n  var updateNeighbors = function (edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n    if (weight < 0) {\n      throw new Error(\"dijkstra does not allow negative edge weights. \" + \"Bad edge: \" + edge + \" Weight: \" + weight);\n    }\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n  g.nodes().forEach(function (v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = {\n      distance: distance\n    };\n    pq.add(v, distance);\n  });\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n    edgeFn(v).forEach(updateNeighbors);\n  }\n  return results;\n}", "map": {"version": 3, "names": ["_", "require", "PriorityQueue", "module", "exports", "<PERSON><PERSON><PERSON>", "DEFAULT_WEIGHT_FUNC", "constant", "g", "source", "weightFn", "edgeFn", "runDijkstra", "String", "v", "outEdges", "results", "pq", "vEntry", "updateNeighbors", "edge", "w", "wEntry", "weight", "distance", "Error", "predecessor", "decrease", "nodes", "for<PERSON>ach", "Number", "POSITIVE_INFINITY", "add", "size", "removeMin"], "sources": ["/root/mes-system/mes-frontend/node_modules/graphlib/lib/alg/dijkstra.js"], "sourcesContent": ["var _ = require(\"../lodash\");\nvar PriorityQueue = require(\"../data/priority-queue\");\n\nmodule.exports = dijkstra;\n\nvar DEFAULT_WEIGHT_FUNC = _.constant(1);\n\nfunction dijkstra(g, source, weightFn, edgeFn) {\n  return runDijkstra(g, String(source),\n    weightFn || DEFAULT_WEIGHT_FUNC,\n    edgeFn || function(v) { return g.outEdges(v); });\n}\n\nfunction runDijkstra(g, source, weightFn, edgeFn) {\n  var results = {};\n  var pq = new PriorityQueue();\n  var v, vEntry;\n\n  var updateNeighbors = function(edge) {\n    var w = edge.v !== v ? edge.v : edge.w;\n    var wEntry = results[w];\n    var weight = weightFn(edge);\n    var distance = vEntry.distance + weight;\n\n    if (weight < 0) {\n      throw new Error(\"dijkstra does not allow negative edge weights. \" +\n                      \"Bad edge: \" + edge + \" Weight: \" + weight);\n    }\n\n    if (distance < wEntry.distance) {\n      wEntry.distance = distance;\n      wEntry.predecessor = v;\n      pq.decrease(w, distance);\n    }\n  };\n\n  g.nodes().forEach(function(v) {\n    var distance = v === source ? 0 : Number.POSITIVE_INFINITY;\n    results[v] = { distance: distance };\n    pq.add(v, distance);\n  });\n\n  while (pq.size() > 0) {\n    v = pq.removeMin();\n    vEntry = results[v];\n    if (vEntry.distance === Number.POSITIVE_INFINITY) {\n      break;\n    }\n\n    edgeFn(v).forEach(updateNeighbors);\n  }\n\n  return results;\n}\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAC5B,IAAIC,aAAa,GAAGD,OAAO,CAAC,wBAAwB,CAAC;AAErDE,MAAM,CAACC,OAAO,GAAGC,QAAQ;AAEzB,IAAIC,mBAAmB,GAAGN,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC;AAEvC,SAASF,QAAQA,CAACG,CAAC,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAC7C,OAAOC,WAAW,CAACJ,CAAC,EAAEK,MAAM,CAACJ,MAAM,CAAC,EAClCC,QAAQ,IAAIJ,mBAAmB,EAC/BK,MAAM,IAAI,UAASG,CAAC,EAAE;IAAE,OAAON,CAAC,CAACO,QAAQ,CAACD,CAAC,CAAC;EAAE,CAAC,CAAC;AACpD;AAEA,SAASF,WAAWA,CAACJ,CAAC,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EAChD,IAAIK,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,EAAE,GAAG,IAAIf,aAAa,CAAC,CAAC;EAC5B,IAAIY,CAAC,EAAEI,MAAM;EAEb,IAAIC,eAAe,GAAG,SAAAA,CAASC,IAAI,EAAE;IACnC,IAAIC,CAAC,GAAGD,IAAI,CAACN,CAAC,KAAKA,CAAC,GAAGM,IAAI,CAACN,CAAC,GAAGM,IAAI,CAACC,CAAC;IACtC,IAAIC,MAAM,GAAGN,OAAO,CAACK,CAAC,CAAC;IACvB,IAAIE,MAAM,GAAGb,QAAQ,CAACU,IAAI,CAAC;IAC3B,IAAII,QAAQ,GAAGN,MAAM,CAACM,QAAQ,GAAGD,MAAM;IAEvC,IAAIA,MAAM,GAAG,CAAC,EAAE;MACd,MAAM,IAAIE,KAAK,CAAC,iDAAiD,GACjD,YAAY,GAAGL,IAAI,GAAG,WAAW,GAAGG,MAAM,CAAC;IAC7D;IAEA,IAAIC,QAAQ,GAAGF,MAAM,CAACE,QAAQ,EAAE;MAC9BF,MAAM,CAACE,QAAQ,GAAGA,QAAQ;MAC1BF,MAAM,CAACI,WAAW,GAAGZ,CAAC;MACtBG,EAAE,CAACU,QAAQ,CAACN,CAAC,EAAEG,QAAQ,CAAC;IAC1B;EACF,CAAC;EAEDhB,CAAC,CAACoB,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,UAASf,CAAC,EAAE;IAC5B,IAAIU,QAAQ,GAAGV,CAAC,KAAKL,MAAM,GAAG,CAAC,GAAGqB,MAAM,CAACC,iBAAiB;IAC1Df,OAAO,CAACF,CAAC,CAAC,GAAG;MAAEU,QAAQ,EAAEA;IAAS,CAAC;IACnCP,EAAE,CAACe,GAAG,CAAClB,CAAC,EAAEU,QAAQ,CAAC;EACrB,CAAC,CAAC;EAEF,OAAOP,EAAE,CAACgB,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACpBnB,CAAC,GAAGG,EAAE,CAACiB,SAAS,CAAC,CAAC;IAClBhB,MAAM,GAAGF,OAAO,CAACF,CAAC,CAAC;IACnB,IAAII,MAAM,CAACM,QAAQ,KAAKM,MAAM,CAACC,iBAAiB,EAAE;MAChD;IACF;IAEApB,MAAM,CAACG,CAAC,CAAC,CAACe,OAAO,CAACV,eAAe,CAAC;EACpC;EAEA,OAAOH,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}