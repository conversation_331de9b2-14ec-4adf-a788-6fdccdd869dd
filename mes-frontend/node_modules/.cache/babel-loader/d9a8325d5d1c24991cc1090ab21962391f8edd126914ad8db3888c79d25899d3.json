{"ast": null, "code": "/**\n * 按照数据中的结果设置fixorder\n */\nexport const initDataOrder = (g, nodeOrder) => {\n  const simpleNodes = g.getAllNodes().filter(v => {\n    var _a;\n    return !((_a = g.getChildren(v.id)) === null || _a === void 0 ? void 0 : _a.length);\n  });\n  const ranks = simpleNodes.map(v => v.data.rank);\n  const maxRank = Math.max(...ranks);\n  const layers = [];\n  for (let i = 0; i < maxRank + 1; i++) {\n    layers[i] = [];\n  }\n  nodeOrder === null || nodeOrder === void 0 ? void 0 : nodeOrder.forEach(n => {\n    const node = g.getNode(n);\n    // 只考虑原有节点，dummy节点需要按照后续算法排出\n    if (!node || node.data.dummy) {\n      return;\n    }\n    if (!isNaN(node.data.rank)) {\n      node.data.fixorder = layers[node.data.rank].length; // 设置fixorder为当层的顺序\n      layers[node.data.rank].push(n);\n    }\n  });\n};", "map": {"version": 3, "names": ["initDataOrder", "g", "nodeOrder", "simpleNodes", "getAllNodes", "filter", "v", "_a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id", "length", "ranks", "map", "data", "rank", "maxRank", "Math", "max", "layers", "i", "for<PERSON>ach", "n", "node", "getNode", "dummy", "isNaN", "fixorder", "push"], "sources": ["../../../src/antv-dagre/order/init-data-order.ts"], "sourcesContent": [null], "mappings": "AAGA;;;AAGA,OAAO,MAAMA,aAAa,GAAGA,CAACC,CAAQ,EAAEC,SAAgB,KAAI;EAC1D,MAAMC,WAAW,GAAGF,CAAC,CAACG,WAAW,EAAE,CAACC,MAAM,CAAEC,CAAC,IAAI;;IAC/C,OAAO,EAAC,CAAAC,EAAA,GAAAN,CAAC,CAACO,WAAW,CAACF,CAAC,CAACG,EAAE,CAAC,cAAAF,EAAA,uBAAAA,EAAA,CAAEG,MAAM;EACrC,CAAC,CAAC;EACF,MAAMC,KAAK,GAAGR,WAAW,CAACS,GAAG,CAAEN,CAAC,IAAKA,CAAC,CAACO,IAAI,CAACC,IAAK,CAAC;EAClD,MAAMC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGN,KAAK,CAAC;EAClC,MAAMO,MAAM,GAAW,EAAE;EACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,GAAG,CAAC,EAAEI,CAAC,EAAE,EAAE;IACpCD,MAAM,CAACC,CAAC,CAAC,GAAG,EAAE;;EAGhBjB,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkB,OAAO,CAAEC,CAAC,IAAI;IACvB,MAAMC,IAAI,GAAGrB,CAAC,CAACsB,OAAO,CAACF,CAAC,CAAC;IACzB;IACA,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACT,IAAI,CAACW,KAAK,EAAE;MAC5B;;IAEF,IAAI,CAACC,KAAK,CAACH,IAAI,CAACT,IAAI,CAACC,IAAK,CAAC,EAAE;MAC3BQ,IAAI,CAACT,IAAI,CAACa,QAAQ,GAAGR,MAAM,CAACI,IAAI,CAACT,IAAI,CAACC,IAAK,CAAC,CAACJ,MAAM,CAAC,CAAC;MACrDQ,MAAM,CAACI,IAAI,CAACT,IAAI,CAACC,IAAK,CAAC,CAACa,IAAI,CAACN,CAAC,CAAC;;EAEnC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}