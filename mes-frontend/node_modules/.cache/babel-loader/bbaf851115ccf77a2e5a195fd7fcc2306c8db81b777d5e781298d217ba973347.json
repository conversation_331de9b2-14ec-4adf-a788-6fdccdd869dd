{"ast": null, "code": "var arrPrototype = Array.prototype;\nvar splice = arrPrototype.splice;\nvar indexOf = arrPrototype.indexOf;\nvar pull = function (arr) {\n  var values = [];\n  for (var _i = 1; _i < arguments.length; _i++) {\n    values[_i - 1] = arguments[_i];\n  }\n  for (var i = 0; i < values.length; i++) {\n    var value = values[i];\n    var fromIndex = -1;\n    while ((fromIndex = indexOf.call(arr, value)) > -1) {\n      splice.call(arr, fromIndex, 1);\n    }\n  }\n  return arr;\n};\nexport default pull;", "map": {"version": 3, "names": ["arrPrototype", "Array", "prototype", "splice", "indexOf", "pull", "arr", "values", "_i", "arguments", "length", "i", "value", "fromIndex", "call"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/pull.ts"], "sourcesContent": ["const arrPrototype = Array.prototype;\nconst splice = arrPrototype.splice;\nconst indexOf = arrPrototype.indexOf;\n\nconst pull = function<T>(arr: T[], ...values: any[]): T[] {\n  for (let i = 0; i < values.length; i++) {\n    const value = values[i];\n    let fromIndex = -1;\n    while ((fromIndex = indexOf.call(arr, value)) > -1) {\n      splice.call(arr, fromIndex, 1);\n    }\n  }\n  return arr;\n};\n\nexport default pull;\n"], "mappings": "AAAA,IAAMA,YAAY,GAAGC,KAAK,CAACC,SAAS;AACpC,IAAMC,MAAM,GAAGH,YAAY,CAACG,MAAM;AAClC,IAAMC,OAAO,GAAGJ,YAAY,CAACI,OAAO;AAEpC,IAAMC,IAAI,GAAG,SAAAA,CAAYC,GAAQ;EAAE,IAAAC,MAAA;OAAA,IAAAC,EAAA,IAAgB,EAAhBA,EAAA,GAAAC,SAAA,CAAAC,MAAgB,EAAhBF,EAAA,EAAgB;IAAhBD,MAAA,CAAAC,EAAA,QAAAC,SAAA,CAAAD,EAAA;;EACjC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,MAAM,CAACG,MAAM,EAAEC,CAAC,EAAE,EAAE;IACtC,IAAMC,KAAK,GAAGL,MAAM,CAACI,CAAC,CAAC;IACvB,IAAIE,SAAS,GAAG,CAAC,CAAC;IAClB,OAAO,CAACA,SAAS,GAAGT,OAAO,CAACU,IAAI,CAACR,GAAG,EAAEM,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE;MAClDT,MAAM,CAACW,IAAI,CAACR,GAAG,EAAEO,SAAS,EAAE,CAAC,CAAC;;;EAGlC,OAAOP,GAAG;AACZ,CAAC;AAED,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}