{"ast": null, "code": "import { __assign, __extends, __read } from \"tslib\";\nimport { parseColor } from '@antv/g';\nimport { isFunction } from '@antv/util';\nimport { Component } from '../../../core';\nimport { classNames, select, subStyleProps } from '../../../util';\nimport { ifHorizontal } from '../utils';\nimport { getBlockColor } from './utils';\nvar CLASS_NAMES = classNames({\n  trackGroup: 'background-group',\n  track: 'background',\n  selectionGroup: 'ribbon-group',\n  selection: 'ribbon',\n  clipPath: 'clip-path'\n}, 'ribbon');\nfunction getShape(attr) {\n  var orientation = attr.orientation,\n    size = attr.size,\n    length = attr.length;\n  return ifHorizontal(orientation, [length, size], [size, length]);\n}\nfunction getTrackPath(attr) {\n  var type = attr.type;\n  var _a = __read(getShape(attr), 2),\n    cw = _a[0],\n    ch = _a[1];\n  if (type === 'size') {\n    return [['M', 0, ch], ['L', 0 + cw, 0], ['L', 0 + cw, ch], ['Z']];\n  }\n  return [['M', 0, ch], ['L', 0, 0], ['L', 0 + cw, 0], ['L', 0 + cw, ch], ['Z']];\n}\nfunction getSelectionPath(attr) {\n  return getTrackPath(attr);\n}\nfunction getColor(attr) {\n  var orientation = attr.orientation,\n    color = attr.color,\n    block = attr.block,\n    partition = attr.partition;\n  var colors;\n  if (isFunction(color)) {\n    var len = 20;\n    colors = new Array(len).fill(0).map(function (_, index, arr) {\n      return color(index / (arr.length - 1));\n    });\n  } else colors = color;\n  var count = colors.length;\n  var genericColor = colors.map(function (c) {\n    return parseColor(c).toString();\n  });\n  if (!count) return '';\n  if (count === 1) return genericColor[0];\n  if (block) return getBlockColor(partition, genericColor, orientation);\n  return genericColor.reduce(function (r, c, idx) {\n    return r += \" \".concat(idx / (count - 1), \":\").concat(c);\n  }, \"l(\".concat(ifHorizontal(orientation, '0', '270'), \")\"));\n}\nfunction getClipPath(attr) {\n  var orientation = attr.orientation,\n    range = attr.range;\n  if (!range) return [];\n  var _a = __read(getShape(attr), 2),\n    width = _a[0],\n    height = _a[1];\n  var _b = __read(range, 2),\n    st = _b[0],\n    et = _b[1];\n  var x = ifHorizontal(orientation, st * width, 0);\n  var y = ifHorizontal(orientation, 0, st * height);\n  var w = ifHorizontal(orientation, et * width, width);\n  var h = ifHorizontal(orientation, height, et * height);\n  return [['M', x, y], ['L', x, h], ['L', w, h], ['L', w, y], ['Z']];\n}\nfunction renderTrack(container, attr) {\n  var style = subStyleProps(attr, 'track');\n  container.maybeAppendByClassName(CLASS_NAMES.track, 'path').styles(__assign({\n    d: getTrackPath(attr)\n  }, style));\n}\nfunction renderSelection(container, attr) {\n  var style = subStyleProps(attr, 'selection');\n  var fill = getColor(attr);\n  var ribbon = container.maybeAppendByClassName(CLASS_NAMES.selection, 'path').styles(__assign({\n    d: getSelectionPath(attr),\n    fill: fill\n  }, style));\n  var clipPath = ribbon.maybeAppendByClassName(CLASS_NAMES.clipPath, 'path').styles({\n    d: getClipPath(attr)\n  }).node();\n  ribbon.style('clipPath', clipPath);\n}\nvar Ribbon = /** @class */function (_super) {\n  __extends(Ribbon, _super);\n  function Ribbon(options) {\n    return _super.call(this, options, {\n      type: 'color',\n      orientation: 'horizontal',\n      size: 30,\n      range: [0, 1],\n      length: 200,\n      block: false,\n      partition: [],\n      color: ['#fff', '#000'],\n      trackFill: '#e5e5e5'\n    }) || this;\n  }\n  Ribbon.prototype.render = function (attribute, container) {\n    var trackGroup = select(container).maybeAppendByClassName(CLASS_NAMES.trackGroup, 'g');\n    renderTrack(trackGroup, attribute);\n    /**\n     * - ribbon group\n     *  |- ribbon\n     * - clip path\n     */\n    var ribbonGroup = select(container).maybeAppendByClassName(CLASS_NAMES.selectionGroup, 'g');\n    renderSelection(ribbonGroup, attribute);\n  };\n  return Ribbon;\n}(Component);\nexport { Ribbon };", "map": {"version": 3, "names": ["parseColor", "isFunction", "Component", "classNames", "select", "subStyleProps", "ifHorizontal", "getBlockColor", "CLASS_NAMES", "trackGroup", "track", "selectionGroup", "selection", "clipPath", "getShape", "attr", "orientation", "size", "length", "getTrackPath", "type", "_a", "__read", "cw", "ch", "getSelectionPath", "getColor", "color", "block", "partition", "colors", "len", "Array", "fill", "map", "_", "index", "arr", "count", "genericColor", "c", "toString", "reduce", "r", "idx", "concat", "getClipPath", "range", "width", "height", "_b", "st", "et", "x", "y", "w", "h", "renderTrack", "container", "style", "maybeAppendByClassName", "styles", "__assign", "d", "renderSelection", "ribbon", "node", "Ribbon", "_super", "__extends", "options", "call", "trackFill", "prototype", "render", "attribute", "ribbonGroup"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/legend/continuous/ribbon.ts"], "sourcesContent": ["import { parseColor } from '@antv/g';\nimport { isFunction } from '@antv/util';\nimport type { ComponentOptions, PrefixStyleProps } from '../../../core';\nimport { Component } from '../../../core';\nimport type { GroupStyleProps, PathStyleProps, RectStyleProps } from '../../../shapes';\nimport { Group } from '../../../shapes';\nimport { classNames, select, Selection, subStyleProps } from '../../../util';\nimport { ifHorizontal } from '../utils';\nimport { getBlockColor } from './utils';\n\nexport type Interpolate<T = string> = (val: number) => T;\n\nexport type RibbonStyleProps = GroupStyleProps &\n  PrefixStyleProps<PathStyleProps, 'selection'> &\n  PrefixStyleProps<RectStyleProps, 'track'> & {\n    block?: boolean;\n    color: string[] | Interpolate;\n    length: number;\n    orientation?: 'horizontal' | 'vertical';\n    /** partition of the block ,the length of it is the block count */\n    partition?: number[];\n    /** select area, 0~1 */\n    range?: [number, number];\n    size: number;\n    type?: 'size' | 'color';\n  };\n\nexport type RibbonOptions = ComponentOptions<RibbonStyleProps>;\n\ntype RequiredRibbonStyleProps = Required<RibbonStyleProps>;\n\nconst CLASS_NAMES = classNames(\n  {\n    trackGroup: 'background-group',\n    track: 'background',\n    selectionGroup: 'ribbon-group',\n    selection: 'ribbon',\n    clipPath: 'clip-path',\n  },\n  'ribbon'\n);\n\nfunction getShape(attr: RequiredRibbonStyleProps) {\n  const { orientation, size, length } = attr;\n\n  return ifHorizontal(orientation, [length, size], [size, length]);\n}\n\nfunction getTrackPath(attr: RequiredRibbonStyleProps) {\n  const { type } = attr;\n  const [cw, ch] = getShape(attr);\n\n  if (type === 'size') {\n    return [['M', 0, ch], ['L', 0 + cw, 0], ['L', 0 + cw, ch], ['Z']] as any[];\n  }\n  return [['M', 0, ch], ['L', 0, 0], ['L', 0 + cw, 0], ['L', 0 + cw, ch], ['Z']] as any[];\n}\n\nfunction getSelectionPath(attr: RequiredRibbonStyleProps) {\n  return getTrackPath(attr);\n}\n\nfunction getColor(attr: RequiredRibbonStyleProps) {\n  const { orientation, color, block, partition } = attr;\n  let colors: string[];\n  if (isFunction(color)) {\n    const len = 20;\n    colors = new Array(len).fill(0).map((_, index, arr) => color(index / (arr.length - 1)));\n  } else colors = color;\n\n  const count = colors.length;\n  const genericColor = colors.map((c) => parseColor(c).toString());\n  if (!count) return '';\n  if (count === 1) return genericColor[0];\n  if (block) return getBlockColor(partition, genericColor, orientation);\n  return genericColor.reduce(\n    (r, c, idx) => (r += ` ${idx / (count - 1)}:${c}`),\n    `l(${ifHorizontal(orientation, '0', '270')})`\n  );\n}\n\nfunction getClipPath(attr: RequiredRibbonStyleProps): any[] {\n  const { orientation, range } = attr;\n  if (!range) return [];\n  const [width, height] = getShape(attr);\n  const [st, et] = range;\n  const x = ifHorizontal(orientation, st * width, 0);\n  const y = ifHorizontal(orientation, 0, st * height);\n  const w = ifHorizontal(orientation, et * width, width);\n  const h = ifHorizontal(orientation, height, et * height);\n  return [['M', x, y], ['L', x, h], ['L', w, h], ['L', w, y], ['Z']];\n}\n\nfunction renderTrack(container: Selection, attr: RequiredRibbonStyleProps) {\n  const style = subStyleProps(attr, 'track');\n  container.maybeAppendByClassName(CLASS_NAMES.track, 'path').styles({ d: getTrackPath(attr), ...style });\n}\n\nfunction renderSelection(container: Selection, attr: RequiredRibbonStyleProps) {\n  const style = subStyleProps(attr, 'selection');\n  const fill = getColor(attr);\n\n  const ribbon = container\n    .maybeAppendByClassName(CLASS_NAMES.selection, 'path')\n    .styles({ d: getSelectionPath(attr), fill, ...style });\n  const clipPath = ribbon\n    .maybeAppendByClassName(CLASS_NAMES.clipPath, 'path')\n    .styles({ d: getClipPath(attr) })\n    .node();\n  ribbon.style('clipPath', clipPath);\n}\n\nexport class Ribbon extends Component<RibbonStyleProps> {\n  constructor(options: RibbonOptions) {\n    super(options, {\n      type: 'color',\n      orientation: 'horizontal',\n      size: 30,\n      range: [0, 1],\n      length: 200,\n      block: false,\n      partition: [],\n      color: ['#fff', '#000'],\n      trackFill: '#e5e5e5',\n    });\n  }\n\n  render(attribute: RequiredRibbonStyleProps, container: Group) {\n    const trackGroup = select(container).maybeAppendByClassName(CLASS_NAMES.trackGroup, 'g');\n    renderTrack(trackGroup, attribute);\n\n    /**\n     * - ribbon group\n     *  |- ribbon\n     * - clip path\n     */\n    const ribbonGroup = select(container).maybeAppendByClassName(CLASS_NAMES.selectionGroup, 'g');\n    renderSelection(ribbonGroup, attribute);\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,SAAS;AACpC,SAASC,UAAU,QAAQ,YAAY;AAEvC,SAASC,SAAS,QAAQ,eAAe;AAGzC,SAASC,UAAU,EAAEC,MAAM,EAAaC,aAAa,QAAQ,eAAe;AAC5E,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,aAAa,QAAQ,SAAS;AAuBvC,IAAMC,WAAW,GAAGL,UAAU,CAC5B;EACEM,UAAU,EAAE,kBAAkB;EAC9BC,KAAK,EAAE,YAAY;EACnBC,cAAc,EAAE,cAAc;EAC9BC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE;CACX,EACD,QAAQ,CACT;AAED,SAASC,QAAQA,CAACC,IAA8B;EACtC,IAAAC,WAAW,GAAmBD,IAAI,CAAAC,WAAvB;IAAEC,IAAI,GAAaF,IAAI,CAAAE,IAAjB;IAAEC,MAAM,GAAKH,IAAI,CAAAG,MAAT;EAEjC,OAAOZ,YAAY,CAACU,WAAW,EAAE,CAACE,MAAM,EAAED,IAAI,CAAC,EAAE,CAACA,IAAI,EAAEC,MAAM,CAAC,CAAC;AAClE;AAEA,SAASC,YAAYA,CAACJ,IAA8B;EAC1C,IAAAK,IAAI,GAAKL,IAAI,CAAAK,IAAT;EACN,IAAAC,EAAA,GAAAC,MAAA,CAAWR,QAAQ,CAACC,IAAI,CAAC;IAAxBQ,EAAE,GAAAF,EAAA;IAAEG,EAAE,GAAAH,EAAA,GAAkB;EAE/B,IAAID,IAAI,KAAK,MAAM,EAAE;IACnB,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAEI,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAGD,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAGA,EAAE,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAU;EAC5E;EACA,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,EAAEA,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAGD,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,GAAGA,EAAE,EAAEC,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAU;AACzF;AAEA,SAASC,gBAAgBA,CAACV,IAA8B;EACtD,OAAOI,YAAY,CAACJ,IAAI,CAAC;AAC3B;AAEA,SAASW,QAAQA,CAACX,IAA8B;EACtC,IAAAC,WAAW,GAA8BD,IAAI,CAAAC,WAAlC;IAAEW,KAAK,GAAuBZ,IAAI,CAAAY,KAA3B;IAAEC,KAAK,GAAgBb,IAAI,CAAAa,KAApB;IAAEC,SAAS,GAAKd,IAAI,CAAAc,SAAT;EAC5C,IAAIC,MAAgB;EACpB,IAAI7B,UAAU,CAAC0B,KAAK,CAAC,EAAE;IACrB,IAAMI,GAAG,GAAG,EAAE;IACdD,MAAM,GAAG,IAAIE,KAAK,CAACD,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,UAACC,CAAC,EAAEC,KAAK,EAAEC,GAAG;MAAK,OAAAV,KAAK,CAACS,KAAK,IAAIC,GAAG,CAACnB,MAAM,GAAG,CAAC,CAAC,CAAC;IAA/B,CAA+B,CAAC;EACzF,CAAC,MAAMY,MAAM,GAAGH,KAAK;EAErB,IAAMW,KAAK,GAAGR,MAAM,CAACZ,MAAM;EAC3B,IAAMqB,YAAY,GAAGT,MAAM,CAACI,GAAG,CAAC,UAACM,CAAC;IAAK,OAAAxC,UAAU,CAACwC,CAAC,CAAC,CAACC,QAAQ,EAAE;EAAxB,CAAwB,CAAC;EAChE,IAAI,CAACH,KAAK,EAAE,OAAO,EAAE;EACrB,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAOC,YAAY,CAAC,CAAC,CAAC;EACvC,IAAIX,KAAK,EAAE,OAAOrB,aAAa,CAACsB,SAAS,EAAEU,YAAY,EAAEvB,WAAW,CAAC;EACrE,OAAOuB,YAAY,CAACG,MAAM,CACxB,UAACC,CAAC,EAAEH,CAAC,EAAEI,GAAG;IAAK,OAACD,CAAC,IAAI,IAAAE,MAAA,CAAID,GAAG,IAAIN,KAAK,GAAG,CAAC,CAAC,OAAAO,MAAA,CAAIL,CAAC,CAAE;EAAlC,CAAmC,EAClD,KAAAK,MAAA,CAAKvC,YAAY,CAACU,WAAW,EAAE,GAAG,EAAE,KAAK,CAAC,MAAG,CAC9C;AACH;AAEA,SAAS8B,WAAWA,CAAC/B,IAA8B;EACzC,IAAAC,WAAW,GAAYD,IAAI,CAAAC,WAAhB;IAAE+B,KAAK,GAAKhC,IAAI,CAAAgC,KAAT;EAC1B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;EACf,IAAA1B,EAAA,GAAAC,MAAA,CAAkBR,QAAQ,CAACC,IAAI,CAAC;IAA/BiC,KAAK,GAAA3B,EAAA;IAAE4B,MAAM,GAAA5B,EAAA,GAAkB;EAChC,IAAA6B,EAAA,GAAA5B,MAAA,CAAWyB,KAAK;IAAfI,EAAE,GAAAD,EAAA;IAAEE,EAAE,GAAAF,EAAA,GAAS;EACtB,IAAMG,CAAC,GAAG/C,YAAY,CAACU,WAAW,EAAEmC,EAAE,GAAGH,KAAK,EAAE,CAAC,CAAC;EAClD,IAAMM,CAAC,GAAGhD,YAAY,CAACU,WAAW,EAAE,CAAC,EAAEmC,EAAE,GAAGF,MAAM,CAAC;EACnD,IAAMM,CAAC,GAAGjD,YAAY,CAACU,WAAW,EAAEoC,EAAE,GAAGJ,KAAK,EAAEA,KAAK,CAAC;EACtD,IAAMQ,CAAC,GAAGlD,YAAY,CAACU,WAAW,EAAEiC,MAAM,EAAEG,EAAE,GAAGH,MAAM,CAAC;EACxD,OAAO,CAAC,CAAC,GAAG,EAAEI,CAAC,EAAEC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAED,CAAC,EAAEG,CAAC,CAAC,EAAE,CAAC,GAAG,EAAED,CAAC,EAAEC,CAAC,CAAC,EAAE,CAAC,GAAG,EAAED,CAAC,EAAED,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AACpE;AAEA,SAASG,WAAWA,CAACC,SAAoB,EAAE3C,IAA8B;EACvE,IAAM4C,KAAK,GAAGtD,aAAa,CAACU,IAAI,EAAE,OAAO,CAAC;EAC1C2C,SAAS,CAACE,sBAAsB,CAACpD,WAAW,CAACE,KAAK,EAAE,MAAM,CAAC,CAACmD,MAAM,CAAAC,QAAA;IAAGC,CAAC,EAAE5C,YAAY,CAACJ,IAAI;EAAC,GAAK4C,KAAK,EAAG;AACzG;AAEA,SAASK,eAAeA,CAACN,SAAoB,EAAE3C,IAA8B;EAC3E,IAAM4C,KAAK,GAAGtD,aAAa,CAACU,IAAI,EAAE,WAAW,CAAC;EAC9C,IAAMkB,IAAI,GAAGP,QAAQ,CAACX,IAAI,CAAC;EAE3B,IAAMkD,MAAM,GAAGP,SAAS,CACrBE,sBAAsB,CAACpD,WAAW,CAACI,SAAS,EAAE,MAAM,CAAC,CACrDiD,MAAM,CAAAC,QAAA;IAAGC,CAAC,EAAEtC,gBAAgB,CAACV,IAAI,CAAC;IAAEkB,IAAI,EAAAA;EAAA,GAAK0B,KAAK,EAAG;EACxD,IAAM9C,QAAQ,GAAGoD,MAAM,CACpBL,sBAAsB,CAACpD,WAAW,CAACK,QAAQ,EAAE,MAAM,CAAC,CACpDgD,MAAM,CAAC;IAAEE,CAAC,EAAEjB,WAAW,CAAC/B,IAAI;EAAC,CAAE,CAAC,CAChCmD,IAAI,EAAE;EACTD,MAAM,CAACN,KAAK,CAAC,UAAU,EAAE9C,QAAQ,CAAC;AACpC;AAEA,IAAAsD,MAAA,0BAAAC,MAAA;EAA4BC,SAAA,CAAAF,MAAA,EAAAC,MAAA;EAC1B,SAAAD,OAAYG,OAAsB;IAChC,OAAAF,MAAK,CAAAG,IAAA,OAACD,OAAO,EAAE;MACblD,IAAI,EAAE,OAAO;MACbJ,WAAW,EAAE,YAAY;MACzBC,IAAI,EAAE,EAAE;MACR8B,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MACb7B,MAAM,EAAE,GAAG;MACXU,KAAK,EAAE,KAAK;MACZC,SAAS,EAAE,EAAE;MACbF,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;MACvB6C,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAL,MAAA,CAAAM,SAAA,CAAAC,MAAM,GAAN,UAAOC,SAAmC,EAAEjB,SAAgB;IAC1D,IAAMjD,UAAU,GAAGL,MAAM,CAACsD,SAAS,CAAC,CAACE,sBAAsB,CAACpD,WAAW,CAACC,UAAU,EAAE,GAAG,CAAC;IACxFgD,WAAW,CAAChD,UAAU,EAAEkE,SAAS,CAAC;IAElC;;;;;IAKA,IAAMC,WAAW,GAAGxE,MAAM,CAACsD,SAAS,CAAC,CAACE,sBAAsB,CAACpD,WAAW,CAACG,cAAc,EAAE,GAAG,CAAC;IAC7FqD,eAAe,CAACY,WAAW,EAAED,SAAS,CAAC;EACzC,CAAC;EACH,OAAAR,MAAC;AAAD,CAAC,CA3B2BjE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}