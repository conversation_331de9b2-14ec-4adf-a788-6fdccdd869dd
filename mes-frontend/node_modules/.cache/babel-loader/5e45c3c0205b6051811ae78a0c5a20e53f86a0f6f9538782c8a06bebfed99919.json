{"ast": null, "code": "export default function (a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n    c = b.slice(),\n    i;\n  return function (t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}", "map": {"version": 3, "names": ["a", "b", "n", "Math", "min", "length", "c", "slice", "i", "t", "isNumberArray", "x", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "DataView"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-interpolate/src/numberArray.js"], "sourcesContent": ["export default function(a, b) {\n  if (!b) b = [];\n  var n = a ? Math.min(b.length, a.length) : 0,\n      c = b.slice(),\n      i;\n  return function(t) {\n    for (i = 0; i < n; ++i) c[i] = a[i] * (1 - t) + b[i] * t;\n    return c;\n  };\n}\n\nexport function isNumberArray(x) {\n  return ArrayBuffer.isView(x) && !(x instanceof DataView);\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAI,CAACA,CAAC,EAAEA,CAAC,GAAG,EAAE;EACd,IAAIC,CAAC,GAAGF,CAAC,GAAGG,IAAI,CAACC,GAAG,CAACH,CAAC,CAACI,MAAM,EAAEL,CAAC,CAACK,MAAM,CAAC,GAAG,CAAC;IACxCC,CAAC,GAAGL,CAAC,CAACM,KAAK,CAAC,CAAC;IACbC,CAAC;EACL,OAAO,UAASC,CAAC,EAAE;IACjB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,CAAC,EAAE,EAAEM,CAAC,EAAEF,CAAC,CAACE,CAAC,CAAC,GAAGR,CAAC,CAACQ,CAAC,CAAC,IAAI,CAAC,GAAGC,CAAC,CAAC,GAAGR,CAAC,CAACO,CAAC,CAAC,GAAGC,CAAC;IACxD,OAAOH,CAAC;EACV,CAAC;AACH;AAEA,OAAO,SAASI,aAAaA,CAACC,CAAC,EAAE;EAC/B,OAAOC,WAAW,CAACC,MAAM,CAACF,CAAC,CAAC,IAAI,EAAEA,CAAC,YAAYG,QAAQ,CAAC;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}