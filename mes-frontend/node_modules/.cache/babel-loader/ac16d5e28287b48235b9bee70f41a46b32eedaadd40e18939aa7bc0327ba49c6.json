{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"./lodash\");\nmodule.exports = {\n  adjust: adjust,\n  undo: undo\n};\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapWidthHeight(g);\n  }\n}\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"bt\" || rankDir === \"rl\") {\n    reverseY(g);\n  }\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapWidthHeightOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    swapWidthHeightOne(g.edge(e));\n  });\n}\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function (v) {\n    reverseYOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (_.has(edge, \"y\")) {\n      reverseYOne(edge);\n    }\n  });\n}\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function (v) {\n    swapXYOne(g.node(v));\n  });\n  _.forEach(g.edges(), function (e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (_.has(edge, \"x\")) {\n      swapXYOne(edge);\n    }\n  });\n}\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "adjust", "undo", "g", "rankDir", "graph", "rankdir", "toLowerCase", "swapWidthHeight", "reverseY", "swapXY", "for<PERSON>ach", "nodes", "v", "swapWidthHeightOne", "node", "edges", "e", "edge", "attrs", "w", "width", "height", "reverseYOne", "points", "has", "y", "swapXYOne", "x"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/coordinate-system.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"./lodash\");\n\nmodule.exports = {\n  adjust: adjust,\n  undo: undo\n};\n\nfunction adjust(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapWidthHeight(g);\n  }\n}\n\nfunction undo(g) {\n  var rankDir = g.graph().rankdir.toLowerCase();\n  if (rankDir === \"bt\" || rankDir === \"rl\") {\n    reverseY(g);\n  }\n\n  if (rankDir === \"lr\" || rankDir === \"rl\") {\n    swapXY(g);\n    swapWidthHeight(g);\n  }\n}\n\nfunction swapWidthHeight(g) {\n  _.forEach(g.nodes(), function(v) { swapWidthHeightOne(g.node(v)); });\n  _.forEach(g.edges(), function(e) { swapWidthHeightOne(g.edge(e)); });\n}\n\nfunction swapWidthHeightOne(attrs) {\n  var w = attrs.width;\n  attrs.width = attrs.height;\n  attrs.height = w;\n}\n\nfunction reverseY(g) {\n  _.forEach(g.nodes(), function(v) { reverseYOne(g.node(v)); });\n\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, reverseYOne);\n    if (_.has(edge, \"y\")) {\n      reverseYOne(edge);\n    }\n  });\n}\n\nfunction reverseYOne(attrs) {\n  attrs.y = -attrs.y;\n}\n\nfunction swapXY(g) {\n  _.forEach(g.nodes(), function(v) { swapXYOne(g.node(v)); });\n\n  _.forEach(g.edges(), function(e) {\n    var edge = g.edge(e);\n    _.forEach(edge.points, swapXYOne);\n    if (_.has(edge, \"x\")) {\n      swapXYOne(edge);\n    }\n  });\n}\n\nfunction swapXYOne(attrs) {\n  var x = attrs.x;\n  attrs.x = attrs.y;\n  attrs.y = x;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAE3BC,MAAM,CAACC,OAAO,GAAG;EACfC,MAAM,EAAEA,MAAM;EACdC,IAAI,EAAEA;AACR,CAAC;AAED,SAASD,MAAMA,CAACE,CAAC,EAAE;EACjB,IAAIC,OAAO,GAAGD,CAAC,CAACE,KAAK,CAAC,CAAC,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,IAAI,EAAE;IACxCI,eAAe,CAACL,CAAC,CAAC;EACpB;AACF;AAEA,SAASD,IAAIA,CAACC,CAAC,EAAE;EACf,IAAIC,OAAO,GAAGD,CAAC,CAACE,KAAK,CAAC,CAAC,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC;EAC7C,IAAIH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,IAAI,EAAE;IACxCK,QAAQ,CAACN,CAAC,CAAC;EACb;EAEA,IAAIC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,IAAI,EAAE;IACxCM,MAAM,CAACP,CAAC,CAAC;IACTK,eAAe,CAACL,CAAC,CAAC;EACpB;AACF;AAEA,SAASK,eAAeA,CAACL,CAAC,EAAE;EAC1BN,CAAC,CAACc,OAAO,CAACR,CAAC,CAACS,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAAEC,kBAAkB,CAACX,CAAC,CAACY,IAAI,CAACF,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;EACpEhB,CAAC,CAACc,OAAO,CAACR,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAAEH,kBAAkB,CAACX,CAAC,CAACe,IAAI,CAACD,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;AACtE;AAEA,SAASH,kBAAkBA,CAACK,KAAK,EAAE;EACjC,IAAIC,CAAC,GAAGD,KAAK,CAACE,KAAK;EACnBF,KAAK,CAACE,KAAK,GAAGF,KAAK,CAACG,MAAM;EAC1BH,KAAK,CAACG,MAAM,GAAGF,CAAC;AAClB;AAEA,SAASX,QAAQA,CAACN,CAAC,EAAE;EACnBN,CAAC,CAACc,OAAO,CAACR,CAAC,CAACS,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAAEU,WAAW,CAACpB,CAAC,CAACY,IAAI,CAACF,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;EAE7DhB,CAAC,CAACc,OAAO,CAACR,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGf,CAAC,CAACe,IAAI,CAACD,CAAC,CAAC;IACpBpB,CAAC,CAACc,OAAO,CAACO,IAAI,CAACM,MAAM,EAAED,WAAW,CAAC;IACnC,IAAI1B,CAAC,CAAC4B,GAAG,CAACP,IAAI,EAAE,GAAG,CAAC,EAAE;MACpBK,WAAW,CAACL,IAAI,CAAC;IACnB;EACF,CAAC,CAAC;AACJ;AAEA,SAASK,WAAWA,CAACJ,KAAK,EAAE;EAC1BA,KAAK,CAACO,CAAC,GAAG,CAACP,KAAK,CAACO,CAAC;AACpB;AAEA,SAAShB,MAAMA,CAACP,CAAC,EAAE;EACjBN,CAAC,CAACc,OAAO,CAACR,CAAC,CAACS,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAAEc,SAAS,CAACxB,CAAC,CAACY,IAAI,CAACF,CAAC,CAAC,CAAC;EAAE,CAAC,CAAC;EAE3DhB,CAAC,CAACc,OAAO,CAACR,CAAC,CAACa,KAAK,CAAC,CAAC,EAAE,UAASC,CAAC,EAAE;IAC/B,IAAIC,IAAI,GAAGf,CAAC,CAACe,IAAI,CAACD,CAAC,CAAC;IACpBpB,CAAC,CAACc,OAAO,CAACO,IAAI,CAACM,MAAM,EAAEG,SAAS,CAAC;IACjC,IAAI9B,CAAC,CAAC4B,GAAG,CAACP,IAAI,EAAE,GAAG,CAAC,EAAE;MACpBS,SAAS,CAACT,IAAI,CAAC;IACjB;EACF,CAAC,CAAC;AACJ;AAEA,SAASS,SAASA,CAACR,KAAK,EAAE;EACxB,IAAIS,CAAC,GAAGT,KAAK,CAACS,CAAC;EACfT,KAAK,CAACS,CAAC,GAAGT,KAAK,CAACO,CAAC;EACjBP,KAAK,CAACO,CAAC,GAAGE,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}