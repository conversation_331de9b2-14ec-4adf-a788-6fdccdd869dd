{"ast": null, "code": "import { __assign, __read } from \"tslib\";\nimport { isFunction } from '@antv/util';\nimport { degToRad, getCallbackValue, scale, subStyleProps } from '../../../util';\nimport { Grid } from '../../grid';\nimport { CLASS_NAMES } from '../constant';\nimport { getValuePos } from './line';\nimport { filterExec, getDirectionVector } from './utils';\nfunction getGridVector(value, attr) {\n  return getDirectionVector(value, attr.gridDirection, attr);\n}\nfunction getGridCenter(attr) {\n  var type = attr.type,\n    gridCenter = attr.gridCenter;\n  if (type === 'linear') return gridCenter;\n  return gridCenter || attr.center;\n}\nfunction renderStraight(data, attr) {\n  var gridLength = attr.gridLength;\n  return data.map(function (_a, index) {\n    var value = _a.value;\n    var _b = __read(getValuePos(value, attr), 2),\n      x = _b[0],\n      y = _b[1];\n    var _c = __read(scale(getGridVector(value, attr), gridLength), 2),\n      dx = _c[0],\n      dy = _c[1];\n    return {\n      id: index,\n      points: [[x, y], [x + dx, y + dy]]\n    };\n  });\n}\nfunction renderSurround(data, attr) {\n  var controlAngles = attr.gridControlAngles;\n  var center = getGridCenter(attr);\n  if (!center) throw new Error('grid center is not provide');\n  if (data.length < 2) throw new Error('Invalid grid data');\n  if (!controlAngles || controlAngles.length === 0) throw new Error('Invalid gridControlAngles');\n  var _a = __read(center, 2),\n    cx = _a[0],\n    cy = _a[1];\n  return data.map(function (_a, index) {\n    var value = _a.value;\n    var _b = __read(getValuePos(value, attr), 2),\n      sx = _b[0],\n      sy = _b[1];\n    var _c = __read([sx - cx, sy - cy], 2),\n      dx = _c[0],\n      dy = _c[1];\n    var points = [];\n    controlAngles.forEach(function (angle) {\n      var angleInRad = degToRad(angle);\n      var _a = __read([Math.cos(angleInRad), Math.sin(angleInRad)], 2),\n        cosa = _a[0],\n        sina = _a[1];\n      var x = dx * cosa - dy * sina + cx;\n      var y = dx * sina + dy * cosa + cy;\n      points.push([x, y]);\n    });\n    return {\n      points: points,\n      id: index\n    };\n  });\n}\nexport function renderGrid(container, data, attr, animate) {\n  var gridAttr = subStyleProps(attr, 'grid');\n  var type = gridAttr.type,\n    areaFill = gridAttr.areaFill;\n  var center = getGridCenter(attr);\n  var finalData = filterExec(data, attr.gridFilter);\n  var gridItems = type === 'segment' ? renderStraight(finalData, attr) : renderSurround(finalData, attr);\n  var style = __assign(__assign({}, gridAttr), {\n    center: center,\n    areaFill: isFunction(areaFill) ? finalData.map(function (datum, index) {\n      return getCallbackValue(areaFill, [datum, index, finalData]);\n    }) : areaFill,\n    animate: animate,\n    data: gridItems\n  });\n  return container.selectAll(CLASS_NAMES.grid.class).data([1]).join(function (enter) {\n    return enter.append(function () {\n      return new Grid({\n        style: style\n      });\n    }).attr('className', CLASS_NAMES.grid.name);\n  }, function (update) {\n    return update.transition(function () {\n      return this.update(style);\n    });\n  }, function (exit) {\n    return exit.remove();\n  }).transitions();\n}", "map": {"version": 3, "names": ["isFunction", "degToRad", "getCallbackValue", "scale", "subStyleProps", "Grid", "CLASS_NAMES", "getValuePos", "filterExec", "getDirectionVector", "getGridVector", "value", "attr", "gridDirection", "getGridCenter", "type", "gridCenter", "center", "renderStraight", "data", "gridLength", "map", "_a", "index", "_b", "__read", "x", "y", "_c", "dx", "dy", "id", "points", "renderSurround", "controlAngles", "gridControlAngles", "Error", "length", "cx", "cy", "sx", "sy", "for<PERSON>ach", "angle", "angleInRad", "Math", "cos", "sin", "cosa", "sina", "push", "renderGrid", "container", "animate", "gridAttr", "areaFill", "finalData", "gridFilter", "gridItems", "style", "__assign", "datum", "selectAll", "grid", "class", "join", "enter", "append", "name", "update", "transition", "exit", "remove", "transitions"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/axis/guides/grid.ts"], "sourcesContent": ["import { isFunction } from '@antv/util';\nimport { StandardAnimationOption } from '../../../animation';\nimport type { Point } from '../../../types';\nimport { degToRad, getCallbackValue, scale, Selection, subStyleProps } from '../../../util';\nimport { Grid, GridStyleProps } from '../../grid';\nimport { CLASS_NAMES } from '../constant';\nimport type { AxisDatum, AxisGridStyleProps, AxisStyleProps } from '../types';\nimport { getValuePos } from './line';\nimport { filterExec, getDirectionVector } from './utils';\n\nfunction getGridVector(value: number, attr: Required<AxisStyleProps>) {\n  return getDirectionVector(value, attr.gridDirection, attr);\n}\n\nfunction getGridCenter(attr: Required<AxisStyleProps>) {\n  const { type, gridCenter } = attr;\n  if (type === 'linear') return gridCenter;\n  return gridCenter || attr.center;\n}\n\nfunction renderStraight(data: AxisDatum[], attr: Required<AxisStyleProps>) {\n  const { gridLength } = attr;\n  return data.map(({ value }, index) => {\n    const [x, y] = getValuePos(value, attr);\n    const [dx, dy] = scale(getGridVector(value, attr), gridLength);\n    return {\n      id: index,\n      points: [\n        [x, y],\n        [x + dx, y + dy],\n      ] as Point[],\n    };\n  });\n}\n\nfunction renderSurround(data: AxisDatum[], attr: Required<AxisStyleProps>) {\n  const controlAngles = attr.gridControlAngles;\n  const center = getGridCenter(attr);\n  if (!center) throw new Error('grid center is not provide');\n  if (data.length < 2) throw new Error('Invalid grid data');\n  if (!controlAngles || controlAngles.length === 0) throw new Error('Invalid gridControlAngles');\n\n  const [cx, cy] = center;\n  return data.map(({ value }, index) => {\n    const [sx, sy] = getValuePos(value, attr);\n    const [dx, dy] = [sx - cx, sy - cy];\n    const points: Point[] = [];\n    controlAngles.forEach((angle: number) => {\n      const angleInRad = degToRad(angle);\n      const [cosa, sina] = [Math.cos(angleInRad), Math.sin(angleInRad)];\n      const x = dx * cosa - dy * sina + cx;\n      const y = dx * sina + dy * cosa + cy;\n      points.push([x, y]);\n    });\n\n    return { points, id: index };\n  });\n}\n\nexport function renderGrid(\n  container: Selection,\n  data: AxisDatum[],\n  attr: Required<AxisStyleProps>,\n  animate: StandardAnimationOption\n) {\n  const gridAttr = subStyleProps<Required<AxisGridStyleProps>>(attr, 'grid');\n  const { type, areaFill } = gridAttr;\n  const center = getGridCenter(attr);\n  const finalData = filterExec(data, attr.gridFilter);\n  const gridItems = type === 'segment' ? renderStraight(finalData, attr) : renderSurround(finalData, attr);\n\n  const style: Required<GridStyleProps> = {\n    ...gridAttr,\n    center,\n    areaFill: isFunction(areaFill)\n      ? finalData.map((datum, index) => getCallbackValue(areaFill, [datum, index, finalData]))\n      : areaFill,\n    animate,\n    data: gridItems,\n  };\n\n  return container\n    .selectAll(CLASS_NAMES.grid.class)\n    .data([1])\n    .join(\n      (enter) => enter.append(() => new Grid({ style })).attr('className', CLASS_NAMES.grid.name),\n      (update) =>\n        update.transition(function () {\n          return this.update(style);\n        }),\n      (exit) => exit.remove()\n    )\n    .transitions();\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,YAAY;AAGvC,SAASC,QAAQ,EAAEC,gBAAgB,EAAEC,KAAK,EAAaC,aAAa,QAAQ,eAAe;AAC3F,SAASC,IAAI,QAAwB,YAAY;AACjD,SAASC,WAAW,QAAQ,aAAa;AAEzC,SAASC,WAAW,QAAQ,QAAQ;AACpC,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,SAAS;AAExD,SAASC,aAAaA,CAACC,KAAa,EAAEC,IAA8B;EAClE,OAAOH,kBAAkB,CAACE,KAAK,EAAEC,IAAI,CAACC,aAAa,EAAED,IAAI,CAAC;AAC5D;AAEA,SAASE,aAAaA,CAACF,IAA8B;EAC3C,IAAAG,IAAI,GAAiBH,IAAI,CAAAG,IAArB;IAAEC,UAAU,GAAKJ,IAAI,CAAAI,UAAT;EACxB,IAAID,IAAI,KAAK,QAAQ,EAAE,OAAOC,UAAU;EACxC,OAAOA,UAAU,IAAIJ,IAAI,CAACK,MAAM;AAClC;AAEA,SAASC,cAAcA,CAACC,IAAiB,EAAEP,IAA8B;EAC/D,IAAAQ,UAAU,GAAKR,IAAI,CAAAQ,UAAT;EAClB,OAAOD,IAAI,CAACE,GAAG,CAAC,UAACC,EAAS,EAAEC,KAAK;QAAdZ,KAAK,GAAAW,EAAA,CAAAX,KAAA;IAChB,IAAAa,EAAA,GAAAC,MAAA,CAASlB,WAAW,CAACI,KAAK,EAAEC,IAAI,CAAC;MAAhCc,CAAC,GAAAF,EAAA;MAAEG,CAAC,GAAAH,EAAA,GAA4B;IACjC,IAAAI,EAAA,GAAAH,MAAA,CAAWtB,KAAK,CAACO,aAAa,CAACC,KAAK,EAAEC,IAAI,CAAC,EAAEQ,UAAU,CAAC;MAAvDS,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAAiD;IAC9D,OAAO;MACLG,EAAE,EAAER,KAAK;MACTS,MAAM,EAAE,CACN,CAACN,CAAC,EAAEC,CAAC,CAAC,EACN,CAACD,CAAC,GAAGG,EAAE,EAAEF,CAAC,GAAGG,EAAE,CAAC;KAEnB;EACH,CAAC,CAAC;AACJ;AAEA,SAASG,cAAcA,CAACd,IAAiB,EAAEP,IAA8B;EACvE,IAAMsB,aAAa,GAAGtB,IAAI,CAACuB,iBAAiB;EAC5C,IAAMlB,MAAM,GAAGH,aAAa,CAACF,IAAI,CAAC;EAClC,IAAI,CAACK,MAAM,EAAE,MAAM,IAAImB,KAAK,CAAC,4BAA4B,CAAC;EAC1D,IAAIjB,IAAI,CAACkB,MAAM,GAAG,CAAC,EAAE,MAAM,IAAID,KAAK,CAAC,mBAAmB,CAAC;EACzD,IAAI,CAACF,aAAa,IAAIA,aAAa,CAACG,MAAM,KAAK,CAAC,EAAE,MAAM,IAAID,KAAK,CAAC,2BAA2B,CAAC;EAExF,IAAAd,EAAA,GAAAG,MAAA,CAAWR,MAAM;IAAhBqB,EAAE,GAAAhB,EAAA;IAAEiB,EAAE,GAAAjB,EAAA,GAAU;EACvB,OAAOH,IAAI,CAACE,GAAG,CAAC,UAACC,EAAS,EAAEC,KAAK;QAAdZ,KAAK,GAAAW,EAAA,CAAAX,KAAA;IAChB,IAAAa,EAAA,GAAAC,MAAA,CAAWlB,WAAW,CAACI,KAAK,EAAEC,IAAI,CAAC;MAAlC4B,EAAE,GAAAhB,EAAA;MAAEiB,EAAE,GAAAjB,EAAA,GAA4B;IACnC,IAAAI,EAAA,GAAAH,MAAA,CAAW,CAACe,EAAE,GAAGF,EAAE,EAAEG,EAAE,GAAGF,EAAE,CAAC;MAA5BV,EAAE,GAAAD,EAAA;MAAEE,EAAE,GAAAF,EAAA,GAAsB;IACnC,IAAMI,MAAM,GAAY,EAAE;IAC1BE,aAAa,CAACQ,OAAO,CAAC,UAACC,KAAa;MAClC,IAAMC,UAAU,GAAG3C,QAAQ,CAAC0C,KAAK,CAAC;MAC5B,IAAArB,EAAA,GAAAG,MAAA,CAAe,CAACoB,IAAI,CAACC,GAAG,CAACF,UAAU,CAAC,EAAEC,IAAI,CAACE,GAAG,CAACH,UAAU,CAAC,CAAC;QAA1DI,IAAI,GAAA1B,EAAA;QAAE2B,IAAI,GAAA3B,EAAA,GAAgD;MACjE,IAAMI,CAAC,GAAGG,EAAE,GAAGmB,IAAI,GAAGlB,EAAE,GAAGmB,IAAI,GAAGX,EAAE;MACpC,IAAMX,CAAC,GAAGE,EAAE,GAAGoB,IAAI,GAAGnB,EAAE,GAAGkB,IAAI,GAAGT,EAAE;MACpCP,MAAM,CAACkB,IAAI,CAAC,CAACxB,CAAC,EAAEC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;IAEF,OAAO;MAAEK,MAAM,EAAAA,MAAA;MAAED,EAAE,EAAER;IAAK,CAAE;EAC9B,CAAC,CAAC;AACJ;AAEA,OAAM,SAAU4B,UAAUA,CACxBC,SAAoB,EACpBjC,IAAiB,EACjBP,IAA8B,EAC9ByC,OAAgC;EAEhC,IAAMC,QAAQ,GAAGlD,aAAa,CAA+BQ,IAAI,EAAE,MAAM,CAAC;EAClE,IAAAG,IAAI,GAAeuC,QAAQ,CAAAvC,IAAvB;IAAEwC,QAAQ,GAAKD,QAAQ,CAAAC,QAAb;EACtB,IAAMtC,MAAM,GAAGH,aAAa,CAACF,IAAI,CAAC;EAClC,IAAM4C,SAAS,GAAGhD,UAAU,CAACW,IAAI,EAAEP,IAAI,CAAC6C,UAAU,CAAC;EACnD,IAAMC,SAAS,GAAG3C,IAAI,KAAK,SAAS,GAAGG,cAAc,CAACsC,SAAS,EAAE5C,IAAI,CAAC,GAAGqB,cAAc,CAACuB,SAAS,EAAE5C,IAAI,CAAC;EAExG,IAAM+C,KAAK,GAAAC,QAAA,CAAAA,QAAA,KACNN,QAAQ;IACXrC,MAAM,EAAAA,MAAA;IACNsC,QAAQ,EAAEvD,UAAU,CAACuD,QAAQ,CAAC,GAC1BC,SAAS,CAACnC,GAAG,CAAC,UAACwC,KAAK,EAAEtC,KAAK;MAAK,OAAArB,gBAAgB,CAACqD,QAAQ,EAAE,CAACM,KAAK,EAAEtC,KAAK,EAAEiC,SAAS,CAAC,CAAC;IAArD,CAAqD,CAAC,GACtFD,QAAQ;IACZF,OAAO,EAAAA,OAAA;IACPlC,IAAI,EAAEuC;EAAS,EAChB;EAED,OAAON,SAAS,CACbU,SAAS,CAACxD,WAAW,CAACyD,IAAI,CAACC,KAAK,CAAC,CACjC7C,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CACT8C,IAAI,CACH,UAACC,KAAK;IAAK,OAAAA,KAAK,CAACC,MAAM,CAAC;MAAM,WAAI9D,IAAI,CAAC;QAAEsD,KAAK,EAAAA;MAAA,CAAE,CAAC;IAAnB,CAAmB,CAAC,CAAC/C,IAAI,CAAC,WAAW,EAAEN,WAAW,CAACyD,IAAI,CAACK,IAAI,CAAC;EAAhF,CAAgF,EAC3F,UAACC,MAAM;IACL,OAAAA,MAAM,CAACC,UAAU,CAAC;MAChB,OAAO,IAAI,CAACD,MAAM,CAACV,KAAK,CAAC;IAC3B,CAAC,CAAC;EAFF,CAEE,EACJ,UAACY,IAAI;IAAK,OAAAA,IAAI,CAACC,MAAM,EAAE;EAAb,CAAa,CACxB,CACAC,WAAW,EAAE;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}