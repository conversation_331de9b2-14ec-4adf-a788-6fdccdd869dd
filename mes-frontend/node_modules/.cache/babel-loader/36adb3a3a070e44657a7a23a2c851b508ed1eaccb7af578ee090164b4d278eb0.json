{"ast": null, "code": "import { paramsCount } from './params-count';\n/**\n * Breaks the parsing of a pathString once a segment is finalized.\n */\nexport function finalizeSegment(path) {\n  var pathCommand = path.pathValue[path.segmentStart];\n  var LK = pathCommand.toLowerCase();\n  var data = path.data;\n  while (data.length >= paramsCount[LK]) {\n    // overloaded `moveTo`\n    // https://github.com/rveciana/svg-path-properties/blob/master/src/parse.ts\n    if (LK === 'm' && data.length > 2) {\n      // @ts-ignore\n      path.segments.push([pathCommand].concat(data.splice(0, 2)));\n      LK = 'l';\n      pathCommand = pathCommand === 'm' ? 'l' : 'L';\n    } else {\n      // @ts-ignore\n      path.segments.push([pathCommand].concat(data.splice(0, paramsCount[LK])));\n    }\n    if (!paramsCount[LK]) {\n      break;\n    }\n  }\n}", "map": {"version": 3, "names": ["paramsCount", "finalizeSegment", "path", "pathCommand", "pathValue", "segmentStart", "LK", "toLowerCase", "data", "length", "segments", "push", "concat", "splice"], "sources": ["path/parser/finalize-segment.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAG5C;;;AAGA,OAAM,SAAUC,eAAeA,CAACC,IAAgB;EAC9C,IAAIC,WAAW,GAAGD,IAAI,CAACE,SAAS,CAACF,IAAI,CAACG,YAAY,CAAgB;EAClE,IAAIC,EAAE,GAAGH,WAAW,CAACI,WAAW,EAAE;EAC1B,IAAAC,IAAI,GAAKN,IAAI,CAAAM,IAAT;EAEZ,OAAOA,IAAI,CAACC,MAAM,IAAIT,WAAW,CAACM,EAAE,CAAC,EAAE;IACrC;IACA;IACA,IAAIA,EAAE,KAAK,GAAG,IAAIE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;MACjC;MACAP,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAACR,WAAW,CAAC,CAACS,MAAM,CAACJ,IAAI,CAACK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3DP,EAAE,GAAG,GAAG;MACRH,WAAW,GAAGA,WAAW,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;IAC/C,CAAC,MAAM;MACL;MACAD,IAAI,CAACQ,QAAQ,CAACC,IAAI,CAAC,CAACR,WAAW,CAAC,CAACS,MAAM,CAACJ,IAAI,CAACK,MAAM,CAAC,CAAC,EAAEb,WAAW,CAACM,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3E;IAEA,IAAI,CAACN,WAAW,CAACM,EAAE,CAAC,EAAE;MACpB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}