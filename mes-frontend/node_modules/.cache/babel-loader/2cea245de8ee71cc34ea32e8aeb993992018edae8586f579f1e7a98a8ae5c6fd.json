{"ast": null, "code": "var _ = require(\"../lodash\");\nmodule.exports = barycenter;\nfunction barycenter(g, movable) {\n  return _.map(movable, function (v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return {\n        v: v\n      };\n    } else {\n      var result = _.reduce(inV, function (acc, e) {\n        var edge = g.edge(e),\n          nodeU = g.node(e.v);\n        return {\n          sum: acc.sum + edge.weight * nodeU.order,\n          weight: acc.weight + edge.weight\n        };\n      }, {\n        sum: 0,\n        weight: 0\n      });\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight\n      };\n    }\n  });\n}", "map": {"version": 3, "names": ["_", "require", "module", "exports", "barycenter", "g", "movable", "map", "v", "inV", "inEdges", "length", "result", "reduce", "acc", "e", "edge", "nodeU", "node", "sum", "weight", "order"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/order/barycenter.js"], "sourcesContent": ["var _ = require(\"../lodash\");\n\nmodule.exports = barycenter;\n\nfunction barycenter(g, movable) {\n  return _.map(movable, function(v) {\n    var inV = g.inEdges(v);\n    if (!inV.length) {\n      return { v: v };\n    } else {\n      var result = _.reduce(inV, function(acc, e) {\n        var edge = g.edge(e),\n          nodeU = g.node(e.v);\n        return {\n          sum: acc.sum + (edge.weight * nodeU.order),\n          weight: acc.weight + edge.weight\n        };\n      }, { sum: 0, weight: 0 });\n\n      return {\n        v: v,\n        barycenter: result.sum / result.weight,\n        weight: result.weight\n      };\n    }\n  });\n}\n\n"], "mappings": "AAAA,IAAIA,CAAC,GAAGC,OAAO,CAAC,WAAW,CAAC;AAE5BC,MAAM,CAACC,OAAO,GAAGC,UAAU;AAE3B,SAASA,UAAUA,CAACC,CAAC,EAAEC,OAAO,EAAE;EAC9B,OAAON,CAAC,CAACO,GAAG,CAACD,OAAO,EAAE,UAASE,CAAC,EAAE;IAChC,IAAIC,GAAG,GAAGJ,CAAC,CAACK,OAAO,CAACF,CAAC,CAAC;IACtB,IAAI,CAACC,GAAG,CAACE,MAAM,EAAE;MACf,OAAO;QAAEH,CAAC,EAAEA;MAAE,CAAC;IACjB,CAAC,MAAM;MACL,IAAII,MAAM,GAAGZ,CAAC,CAACa,MAAM,CAACJ,GAAG,EAAE,UAASK,GAAG,EAAEC,CAAC,EAAE;QAC1C,IAAIC,IAAI,GAAGX,CAAC,CAACW,IAAI,CAACD,CAAC,CAAC;UAClBE,KAAK,GAAGZ,CAAC,CAACa,IAAI,CAACH,CAAC,CAACP,CAAC,CAAC;QACrB,OAAO;UACLW,GAAG,EAAEL,GAAG,CAACK,GAAG,GAAIH,IAAI,CAACI,MAAM,GAAGH,KAAK,CAACI,KAAM;UAC1CD,MAAM,EAAEN,GAAG,CAACM,MAAM,GAAGJ,IAAI,CAACI;QAC5B,CAAC;MACH,CAAC,EAAE;QAAED,GAAG,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC,CAAC;MAEzB,OAAO;QACLZ,CAAC,EAAEA,CAAC;QACJJ,UAAU,EAAEQ,MAAM,CAACO,GAAG,GAAGP,MAAM,CAACQ,MAAM;QACtCA,MAAM,EAAER,MAAM,CAACQ;MACjB,CAAC;IACH;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}