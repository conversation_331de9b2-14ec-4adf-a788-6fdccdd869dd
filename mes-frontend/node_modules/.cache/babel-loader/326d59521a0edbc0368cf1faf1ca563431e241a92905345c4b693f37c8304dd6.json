{"ast": null, "code": "export function quadToCubic(x1, y1, qx, qy, x2, y2) {\n  var r13 = 1 / 3;\n  var r23 = 2 / 3;\n  return [r13 * x1 + r23 * qx,\n  // cpx1\n  r13 * y1 + r23 * qy,\n  // cpy1\n  r13 * x2 + r23 * qx,\n  // cpx2\n  r13 * y2 + r23 * qy,\n  // cpy2\n  x2, y2 // x,y\n  ];\n}", "map": {"version": 3, "names": ["quadToCubic", "x1", "y1", "qx", "qy", "x2", "y2", "r13", "r23"], "sources": ["path/process/quad-2-cubic.ts"], "sourcesContent": [null], "mappings": "AAAA,OAAM,SAAUA,WAAWA,CAACC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU,EAAEC,EAAU;EAChG,IAAMC,GAAG,GAAG,CAAC,GAAG,CAAC;EACjB,IAAMC,GAAG,GAAG,CAAC,GAAG,CAAC;EACjB,OAAO,CACLD,GAAG,GAAGN,EAAE,GAAGO,GAAG,GAAGL,EAAE;EAAE;EACrBI,GAAG,GAAGL,EAAE,GAAGM,GAAG,GAAGJ,EAAE;EAAE;EACrBG,GAAG,GAAGF,EAAE,GAAGG,GAAG,GAAGL,EAAE;EAAE;EACrBI,GAAG,GAAGD,EAAE,GAAGE,GAAG,GAAGJ,EAAE;EAAE;EACrBC,EAAE,EACFC,EAAE,CAAE;EAAA,CACL;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}