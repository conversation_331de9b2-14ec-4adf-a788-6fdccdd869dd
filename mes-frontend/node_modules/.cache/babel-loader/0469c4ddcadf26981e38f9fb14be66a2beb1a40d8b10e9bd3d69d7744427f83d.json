{"ast": null, "code": "var __extends = this && this.__extends || function () {\n  var extendStatics = function (d, b) {\n    extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nvar __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { Polygon as GPolygon } from '@antv/g';\nvar Polygon = /** @class */function (_super) {\n  __extends(Polygon, _super);\n  function Polygon(_a) {\n    if (_a === void 0) {\n      _a = {};\n    }\n    var style = _a.style,\n      restOptions = __rest(_a, [\"style\"]);\n    return _super.call(this, __assign({\n      style: __assign({\n        fill: '#eee'\n      }, style)\n    }, restOptions)) || this;\n  }\n  return Polygon;\n}(GPolygon);\nexport { Polygon };", "map": {"version": 3, "names": ["__extends", "extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "TypeError", "String", "__", "constructor", "create", "__assign", "assign", "t", "s", "i", "n", "arguments", "length", "apply", "__rest", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "Polygon", "GPolygon", "_super", "_a", "style", "restOptions", "fill"], "sources": ["/root/mes-system/mes-frontend/node_modules/@ant-design/plots/es/core/annotation/shapes/Polygon.js"], "sourcesContent": ["var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nimport { Polygon as GPolygon } from '@antv/g';\nvar Polygon = /** @class */ (function (_super) {\n    __extends(Polygon, _super);\n    function Polygon(_a) {\n        if (_a === void 0) { _a = {}; }\n        var style = _a.style, restOptions = __rest(_a, [\"style\"]);\n        return _super.call(this, __assign({ style: __assign({ fill: '#eee' }, style) }, restOptions)) || this;\n    }\n    return Polygon;\n}(GPolygon));\nexport { Polygon };\n"], "mappings": "AAAA,IAAIA,SAAS,GAAI,IAAI,IAAI,IAAI,CAACA,SAAS,IAAM,YAAY;EACrD,IAAIC,aAAa,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAChCF,aAAa,GAAGG,MAAM,CAACC,cAAc,IAChC;MAAEC,SAAS,EAAE;IAAG,CAAC,YAAYC,KAAK,IAAI,UAAUL,CAAC,EAAEC,CAAC,EAAE;MAAED,CAAC,CAACI,SAAS,GAAGH,CAAC;IAAE,CAAE,IAC5E,UAAUD,CAAC,EAAEC,CAAC,EAAE;MAAE,KAAK,IAAIK,CAAC,IAAIL,CAAC,EAAE,IAAIC,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAAE,CAAC;IACrG,OAAOP,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B,CAAC;EACD,OAAO,UAAUD,CAAC,EAAEC,CAAC,EAAE;IACnB,IAAI,OAAOA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,IAAI,EACrC,MAAM,IAAIS,SAAS,CAAC,sBAAsB,GAAGC,MAAM,CAACV,CAAC,CAAC,GAAG,+BAA+B,CAAC;IAC7FF,aAAa,CAACC,CAAC,EAAEC,CAAC,CAAC;IACnB,SAASW,EAAEA,CAAA,EAAG;MAAE,IAAI,CAACC,WAAW,GAAGb,CAAC;IAAE;IACtCA,CAAC,CAACO,SAAS,GAAGN,CAAC,KAAK,IAAI,GAAGC,MAAM,CAACY,MAAM,CAACb,CAAC,CAAC,IAAIW,EAAE,CAACL,SAAS,GAAGN,CAAC,CAACM,SAAS,EAAE,IAAIK,EAAE,CAAC,CAAC,CAAC;EACxF,CAAC;AACL,CAAC,CAAE,CAAC;AACJ,IAAIG,QAAQ,GAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,IAAK,YAAY;EAClDA,QAAQ,GAAGb,MAAM,CAACc,MAAM,IAAI,UAASC,CAAC,EAAE;IACpC,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACjDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAIb,CAAC,IAAIY,CAAC,EAAE,IAAIhB,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACS,CAAC,EAAEZ,CAAC,CAAC,EAC3DW,CAAC,CAACX,CAAC,CAAC,GAAGY,CAAC,CAACZ,CAAC,CAAC;IACnB;IACA,OAAOW,CAAC;EACZ,CAAC;EACD,OAAOF,QAAQ,CAACQ,KAAK,CAAC,IAAI,EAAEF,SAAS,CAAC;AAC1C,CAAC;AACD,IAAIG,MAAM,GAAI,IAAI,IAAI,IAAI,CAACA,MAAM,IAAK,UAAUN,CAAC,EAAEO,CAAC,EAAE;EAClD,IAAIR,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIX,CAAC,IAAIY,CAAC,EAAE,IAAIhB,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACS,CAAC,EAAEZ,CAAC,CAAC,IAAImB,CAAC,CAACC,OAAO,CAACpB,CAAC,CAAC,GAAG,CAAC,EAC/EW,CAAC,CAACX,CAAC,CAAC,GAAGY,CAAC,CAACZ,CAAC,CAAC;EACf,IAAIY,CAAC,IAAI,IAAI,IAAI,OAAOhB,MAAM,CAACyB,qBAAqB,KAAK,UAAU,EAC/D,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEb,CAAC,GAAGJ,MAAM,CAACyB,qBAAqB,CAACT,CAAC,CAAC,EAAEC,CAAC,GAAGb,CAAC,CAACgB,MAAM,EAAEH,CAAC,EAAE,EAAE;IACpE,IAAIM,CAAC,CAACC,OAAO,CAACpB,CAAC,CAACa,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIjB,MAAM,CAACK,SAAS,CAACqB,oBAAoB,CAACnB,IAAI,CAACS,CAAC,EAAEZ,CAAC,CAACa,CAAC,CAAC,CAAC,EAC1EF,CAAC,CAACX,CAAC,CAACa,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACZ,CAAC,CAACa,CAAC,CAAC,CAAC;EACzB;EACJ,OAAOF,CAAC;AACZ,CAAC;AACD,SAASY,OAAO,IAAIC,QAAQ,QAAQ,SAAS;AAC7C,IAAID,OAAO,GAAG,aAAe,UAAUE,MAAM,EAAE;EAC3CjC,SAAS,CAAC+B,OAAO,EAAEE,MAAM,CAAC;EAC1B,SAASF,OAAOA,CAACG,EAAE,EAAE;IACjB,IAAIA,EAAE,KAAK,KAAK,CAAC,EAAE;MAAEA,EAAE,GAAG,CAAC,CAAC;IAAE;IAC9B,IAAIC,KAAK,GAAGD,EAAE,CAACC,KAAK;MAAEC,WAAW,GAAGV,MAAM,CAACQ,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;IACzD,OAAOD,MAAM,CAACtB,IAAI,CAAC,IAAI,EAAEM,QAAQ,CAAC;MAAEkB,KAAK,EAAElB,QAAQ,CAAC;QAAEoB,IAAI,EAAE;MAAO,CAAC,EAAEF,KAAK;IAAE,CAAC,EAAEC,WAAW,CAAC,CAAC,IAAI,IAAI;EACzG;EACA,OAAOL,OAAO;AAClB,CAAC,CAACC,QAAQ,CAAE;AACZ,SAASD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}