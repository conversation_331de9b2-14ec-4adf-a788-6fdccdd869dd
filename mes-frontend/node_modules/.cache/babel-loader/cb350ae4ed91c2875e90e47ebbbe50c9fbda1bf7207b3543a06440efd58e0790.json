{"ast": null, "code": "import { feasibleTreeWithLayer as feasibleTree } from './feasible-tree';\nimport { networkSimplex } from './network-simplex';\nimport { longestPath, longestPathWithLayer } from './util';\n/*\n * Assigns a rank to each node in the input graph that respects the \"minlen\"\n * constraint specified on edges between nodes.\n *\n * This basic structure is derived from <PERSON><PERSON><PERSON>, et al., \"A Technique for\n * Drawing Directed Graphs.\"\n *\n * Pre-conditions:\n *\n *    1. Graph must be a connected DAG\n *    2. Graph nodes must be objects\n *    3. Graph edges must have \"weight\" and \"minlen\" attributes\n *\n * Post-conditions:\n *\n *    1. Graph nodes will have a \"rank\" attribute based on the results of the\n *       algorithm. Ranks can start at any index (including negative), we'll\n *       fix them up later.\n */\nexport const rank = (g, ranker) => {\n  switch (ranker) {\n    case 'network-simplex':\n      networkSimplexRanker(g);\n      break;\n    case 'tight-tree':\n      tightTreeRanker(g);\n      break;\n    case 'longest-path':\n      longestPathRanker(g);\n      break;\n    // default: networkSimplexRanker(g);\n    default:\n      tightTreeRanker(g);\n  }\n};\n// A fast and simple ranker, but results are far from optimal.\nconst longestPathRanker = longestPath;\nconst tightTreeRanker = g => {\n  longestPathWithLayer(g);\n  feasibleTree(g);\n};\nconst networkSimplexRanker = g => {\n  networkSimplex(g);\n};", "map": {"version": 3, "names": ["feasibleTreeWithLayer", "feasibleTree", "networkSimplex", "longestPath", "longestPathWith<PERSON>ayer", "rank", "g", "ranker", "networkSimplexRanker", "tightTreeRanker", "longestPathRanker"], "sources": ["../../../src/antv-dagre/rank/index.ts"], "sourcesContent": [null], "mappings": "AACA,SAASA,qBAAqB,IAAIC,YAAY,QAAQ,iBAAiB;AACvE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,EAAEC,oBAAoB,QAAQ,QAAQ;AAE1D;;;;;;;;;;;;;;;;;;;AAmBA,OAAO,MAAMC,IAAI,GAAGA,CAClBC,CAAS,EACTC,MAAyD,KACvD;EACF,QAAQA,MAAM;IACZ,KAAK,iBAAiB;MACpBC,oBAAoB,CAACF,CAAC,CAAC;MACvB;IACF,KAAK,YAAY;MACfG,eAAe,CAACH,CAAC,CAAC;MAClB;IACF,KAAK,cAAc;MACjBI,iBAAiB,CAACJ,CAAC,CAAC;MACpB;IACF;IACA;MACEG,eAAe,CAACH,CAAC,CAAC;;AAExB,CAAC;AAED;AACA,MAAMI,iBAAiB,GAAGP,WAAW;AAErC,MAAMM,eAAe,GAAIH,CAAS,IAAI;EACpCF,oBAAoB,CAACE,CAAC,CAAC;EACvBL,YAAY,CAACK,CAAC,CAAC;AACjB,CAAC;AAED,MAAME,oBAAoB,GAAIF,CAAS,IAAI;EACzCJ,cAAc,CAACI,CAAC,CAAC;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}