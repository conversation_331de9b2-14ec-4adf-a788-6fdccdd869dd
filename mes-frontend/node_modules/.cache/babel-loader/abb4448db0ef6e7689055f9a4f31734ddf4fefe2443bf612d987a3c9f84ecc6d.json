{"ast": null, "code": "\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar util = require(\"./util\");\nmodule.exports = {\n  run: run,\n  undo: undo\n};\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function (edge) {\n    normalizeEdge(g, edge);\n  });\n}\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n  if (wRank === vRank + 1) return;\n  g.removeEdge(e);\n  var dummy, attrs, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0,\n      height: 0,\n      edgeLabel: edgeLabel,\n      edgeObj: e,\n      rank: vRank\n    };\n    dummy = util.addDummyNode(g, \"edge\", attrs, \"_d\");\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = \"edge-label\";\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, {\n      weight: edgeLabel.weight\n    }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n  g.setEdge(v, w, {\n    weight: edgeLabel.weight\n  }, name);\n}\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function (v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({\n        x: node.x,\n        y: node.y\n      });\n      if (node.dummy === \"edge-label\") {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}", "map": {"version": 3, "names": ["_", "require", "util", "module", "exports", "run", "undo", "g", "graph", "dummy<PERSON><PERSON><PERSON>", "for<PERSON>ach", "edges", "edge", "normalizeEdge", "e", "v", "vRank", "node", "rank", "w", "wRank", "name", "edgeLabel", "labelRank", "removeEdge", "dummy", "attrs", "i", "points", "width", "height", "edgeObj", "addDummyNode", "labelpos", "setEdge", "weight", "push", "origLabel", "successors", "removeNode", "x", "y"], "sources": ["/root/mes-system/mes-frontend/node_modules/dagre/lib/normalize.js"], "sourcesContent": ["\"use strict\";\n\nvar _ = require(\"./lodash\");\nvar util = require(\"./util\");\n\nmodule.exports = {\n  run: run,\n  undo: undo\n};\n\n/*\n * Breaks any long edges in the graph into short segments that span 1 layer\n * each. This operation is undoable with the denormalize function.\n *\n * Pre-conditions:\n *\n *    1. The input graph is a DAG.\n *    2. Each node in the graph has a \"rank\" property.\n *\n * Post-condition:\n *\n *    1. All edges in the graph have a length of 1.\n *    2. Dummy nodes are added where edges have been split into segments.\n *    3. The graph is augmented with a \"dummyChains\" attribute which contains\n *       the first dummy in each chain of dummy nodes produced.\n */\nfunction run(g) {\n  g.graph().dummyChains = [];\n  _.forEach(g.edges(), function(edge) { normalizeEdge(g, edge); });\n}\n\nfunction normalizeEdge(g, e) {\n  var v = e.v;\n  var vRank = g.node(v).rank;\n  var w = e.w;\n  var wRank = g.node(w).rank;\n  var name = e.name;\n  var edgeLabel = g.edge(e);\n  var labelRank = edgeLabel.labelRank;\n\n  if (wRank === vRank + 1) return;\n\n  g.removeEdge(e);\n\n  var dummy, attrs, i;\n  for (i = 0, ++vRank; vRank < wRank; ++i, ++vRank) {\n    edgeLabel.points = [];\n    attrs = {\n      width: 0, height: 0,\n      edgeLabel: edgeLabel, edgeObj: e,\n      rank: vRank\n    };\n    dummy = util.addDummyNode(g, \"edge\", attrs, \"_d\");\n    if (vRank === labelRank) {\n      attrs.width = edgeLabel.width;\n      attrs.height = edgeLabel.height;\n      attrs.dummy = \"edge-label\";\n      attrs.labelpos = edgeLabel.labelpos;\n    }\n    g.setEdge(v, dummy, { weight: edgeLabel.weight }, name);\n    if (i === 0) {\n      g.graph().dummyChains.push(dummy);\n    }\n    v = dummy;\n  }\n\n  g.setEdge(v, w, { weight: edgeLabel.weight }, name);\n}\n\nfunction undo(g) {\n  _.forEach(g.graph().dummyChains, function(v) {\n    var node = g.node(v);\n    var origLabel = node.edgeLabel;\n    var w;\n    g.setEdge(node.edgeObj, origLabel);\n    while (node.dummy) {\n      w = g.successors(v)[0];\n      g.removeNode(v);\n      origLabel.points.push({ x: node.x, y: node.y });\n      if (node.dummy === \"edge-label\") {\n        origLabel.x = node.x;\n        origLabel.y = node.y;\n        origLabel.width = node.width;\n        origLabel.height = node.height;\n      }\n      v = w;\n      node = g.node(v);\n    }\n  });\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,CAAC,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC3B,IAAIC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;AAE5BE,MAAM,CAACC,OAAO,GAAG;EACfC,GAAG,EAAEA,GAAG;EACRC,IAAI,EAAEA;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,GAAGA,CAACE,CAAC,EAAE;EACdA,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,WAAW,GAAG,EAAE;EAC1BT,CAAC,CAACU,OAAO,CAACH,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,UAASC,IAAI,EAAE;IAAEC,aAAa,CAACN,CAAC,EAAEK,IAAI,CAAC;EAAE,CAAC,CAAC;AAClE;AAEA,SAASC,aAAaA,CAACN,CAAC,EAAEO,CAAC,EAAE;EAC3B,IAAIC,CAAC,GAAGD,CAAC,CAACC,CAAC;EACX,IAAIC,KAAK,GAAGT,CAAC,CAACU,IAAI,CAACF,CAAC,CAAC,CAACG,IAAI;EAC1B,IAAIC,CAAC,GAAGL,CAAC,CAACK,CAAC;EACX,IAAIC,KAAK,GAAGb,CAAC,CAACU,IAAI,CAACE,CAAC,CAAC,CAACD,IAAI;EAC1B,IAAIG,IAAI,GAAGP,CAAC,CAACO,IAAI;EACjB,IAAIC,SAAS,GAAGf,CAAC,CAACK,IAAI,CAACE,CAAC,CAAC;EACzB,IAAIS,SAAS,GAAGD,SAAS,CAACC,SAAS;EAEnC,IAAIH,KAAK,KAAKJ,KAAK,GAAG,CAAC,EAAE;EAEzBT,CAAC,CAACiB,UAAU,CAACV,CAAC,CAAC;EAEf,IAAIW,KAAK,EAAEC,KAAK,EAAEC,CAAC;EACnB,KAAKA,CAAC,GAAG,CAAC,EAAE,EAAEX,KAAK,EAAEA,KAAK,GAAGI,KAAK,EAAE,EAAEO,CAAC,EAAE,EAAEX,KAAK,EAAE;IAChDM,SAAS,CAACM,MAAM,GAAG,EAAE;IACrBF,KAAK,GAAG;MACNG,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MACnBR,SAAS,EAAEA,SAAS;MAAES,OAAO,EAAEjB,CAAC;MAChCI,IAAI,EAAEF;IACR,CAAC;IACDS,KAAK,GAAGvB,IAAI,CAAC8B,YAAY,CAACzB,CAAC,EAAE,MAAM,EAAEmB,KAAK,EAAE,IAAI,CAAC;IACjD,IAAIV,KAAK,KAAKO,SAAS,EAAE;MACvBG,KAAK,CAACG,KAAK,GAAGP,SAAS,CAACO,KAAK;MAC7BH,KAAK,CAACI,MAAM,GAAGR,SAAS,CAACQ,MAAM;MAC/BJ,KAAK,CAACD,KAAK,GAAG,YAAY;MAC1BC,KAAK,CAACO,QAAQ,GAAGX,SAAS,CAACW,QAAQ;IACrC;IACA1B,CAAC,CAAC2B,OAAO,CAACnB,CAAC,EAAEU,KAAK,EAAE;MAAEU,MAAM,EAAEb,SAAS,CAACa;IAAO,CAAC,EAAEd,IAAI,CAAC;IACvD,IAAIM,CAAC,KAAK,CAAC,EAAE;MACXpB,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,WAAW,CAAC2B,IAAI,CAACX,KAAK,CAAC;IACnC;IACAV,CAAC,GAAGU,KAAK;EACX;EAEAlB,CAAC,CAAC2B,OAAO,CAACnB,CAAC,EAAEI,CAAC,EAAE;IAAEgB,MAAM,EAAEb,SAAS,CAACa;EAAO,CAAC,EAAEd,IAAI,CAAC;AACrD;AAEA,SAASf,IAAIA,CAACC,CAAC,EAAE;EACfP,CAAC,CAACU,OAAO,CAACH,CAAC,CAACC,KAAK,CAAC,CAAC,CAACC,WAAW,EAAE,UAASM,CAAC,EAAE;IAC3C,IAAIE,IAAI,GAAGV,CAAC,CAACU,IAAI,CAACF,CAAC,CAAC;IACpB,IAAIsB,SAAS,GAAGpB,IAAI,CAACK,SAAS;IAC9B,IAAIH,CAAC;IACLZ,CAAC,CAAC2B,OAAO,CAACjB,IAAI,CAACc,OAAO,EAAEM,SAAS,CAAC;IAClC,OAAOpB,IAAI,CAACQ,KAAK,EAAE;MACjBN,CAAC,GAAGZ,CAAC,CAAC+B,UAAU,CAACvB,CAAC,CAAC,CAAC,CAAC,CAAC;MACtBR,CAAC,CAACgC,UAAU,CAACxB,CAAC,CAAC;MACfsB,SAAS,CAACT,MAAM,CAACQ,IAAI,CAAC;QAAEI,CAAC,EAAEvB,IAAI,CAACuB,CAAC;QAAEC,CAAC,EAAExB,IAAI,CAACwB;MAAE,CAAC,CAAC;MAC/C,IAAIxB,IAAI,CAACQ,KAAK,KAAK,YAAY,EAAE;QAC/BY,SAAS,CAACG,CAAC,GAAGvB,IAAI,CAACuB,CAAC;QACpBH,SAAS,CAACI,CAAC,GAAGxB,IAAI,CAACwB,CAAC;QACpBJ,SAAS,CAACR,KAAK,GAAGZ,IAAI,CAACY,KAAK;QAC5BQ,SAAS,CAACP,MAAM,GAAGb,IAAI,CAACa,MAAM;MAChC;MACAf,CAAC,GAAGI,CAAC;MACLF,IAAI,GAAGV,CAAC,CAACU,IAAI,CAACF,CAAC,CAAC;IAClB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}