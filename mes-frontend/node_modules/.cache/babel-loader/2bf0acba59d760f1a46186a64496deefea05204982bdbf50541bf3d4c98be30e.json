{"ast": null, "code": "export default function () {\n  var root = this,\n    links = [];\n  root.each(function (node) {\n    if (node !== root) {\n      // Don’t include the root’s parent, if any.\n      links.push({\n        source: node.parent,\n        target: node\n      });\n    }\n  });\n  return links;\n}", "map": {"version": 3, "names": ["root", "links", "each", "node", "push", "source", "parent", "target"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-hierarchy/src/hierarchy/links.js"], "sourcesContent": ["export default function() {\n  var root = this, links = [];\n  root.each(function(node) {\n    if (node !== root) { // Don’t include the root’s parent, if any.\n      links.push({source: node.parent, target: node});\n    }\n  });\n  return links;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,IAAIA,IAAI,GAAG,IAAI;IAAEC,KAAK,GAAG,EAAE;EAC3BD,IAAI,CAACE,IAAI,CAAC,UAASC,IAAI,EAAE;IACvB,IAAIA,IAAI,KAAKH,IAAI,EAAE;MAAE;MACnBC,KAAK,CAACG,IAAI,CAAC;QAACC,MAAM,EAAEF,IAAI,CAACG,MAAM;QAAEC,MAAM,EAAEJ;MAAI,CAAC,CAAC;IACjD;EACF,CAAC,CAAC;EACF,OAAOF,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}