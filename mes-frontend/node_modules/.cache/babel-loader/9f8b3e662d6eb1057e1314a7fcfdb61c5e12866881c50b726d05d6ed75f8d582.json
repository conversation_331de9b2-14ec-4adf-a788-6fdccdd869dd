{"ast": null, "code": "import { sum, max, min } from '@antv/vendor/d3-array';\nimport { justify } from './align';\nimport { constant } from './constant';\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\nfunction value(d) {\n  return d.value;\n}\nfunction defaultId(d) {\n  return d.index;\n}\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\nfunction defaultLinks(graph) {\n  return graph.links;\n}\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error('missing: ' + id);\n  return node;\n}\nfunction computeLinkBreadths({\n  nodes\n}) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\nexport function Sankey() {\n  let x0 = 0,\n    y0 = 0,\n    x1 = 1,\n    y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8,\n    py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let depth;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n  function sankey(arg) {\n    const graph = {\n      nodes: nodes(arg),\n      links: links(arg)\n    };\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n  sankey.update = function (graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n  sankey.nodeId = function (_) {\n    return arguments.length ? (id = typeof _ === 'function' ? _ : constant(_), sankey) : id;\n  };\n  sankey.nodeAlign = function (_) {\n    return arguments.length ? (align = typeof _ === 'function' ? _ : constant(_), sankey) : align;\n  };\n  sankey.nodeDepth = function (_) {\n    return arguments.length ? (depth = typeof _ === 'function' ? _ : _, sankey) : depth;\n  };\n  sankey.nodeSort = function (_) {\n    return arguments.length ? (sort = _, sankey) : sort;\n  };\n  sankey.nodeWidth = function (_) {\n    return arguments.length ? (dx = +_, sankey) : dx;\n  };\n  sankey.nodePadding = function (_) {\n    return arguments.length ? (dy = py = +_, sankey) : dy;\n  };\n  sankey.nodes = function (_) {\n    return arguments.length ? (nodes = typeof _ === 'function' ? _ : constant(_), sankey) : nodes;\n  };\n  sankey.links = function (_) {\n    return arguments.length ? (links = typeof _ === 'function' ? _ : constant(_), sankey) : links;\n  };\n  sankey.linkSort = function (_) {\n    return arguments.length ? (linkSort = _, sankey) : linkSort;\n  };\n  sankey.size = function (_) {\n    return arguments.length ? (x0 = y0 = 0, x1 = +_[0], y1 = +_[1], sankey) : [x1 - x0, y1 - y0];\n  };\n  sankey.extent = function (_) {\n    return arguments.length ? (x0 = +_[0][0], x1 = +_[1][0], y0 = +_[0][1], y1 = +_[1][1], sankey) : [[x0, y0], [x1, y1]];\n  };\n  sankey.iterations = function (_) {\n    return arguments.length ? (iterations = +_, sankey) : iterations;\n  };\n  function computeNodeLinks({\n    nodes,\n    links\n  }) {\n    nodes.forEach((node, idx) => {\n      node.index = idx;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    });\n    const nodeById = new Map(nodes.map(d => [id(d), d]));\n    links.forEach((link, idx) => {\n      link.index = idx;\n      let {\n        source,\n        target\n      } = link;\n      if (typeof source !== 'object') source = link.source = find(nodeById, source);\n      if (typeof target !== 'object') target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    });\n    if (linkSort != null) {\n      for (const {\n        sourceLinks,\n        targetLinks\n      } of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n  function computeNodeValues({\n    nodes\n  }) {\n    for (const node of nodes) {\n      node.value = node.fixedValue === undefined ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value)) : node.fixedValue;\n    }\n  }\n  function computeNodeDepths({\n    nodes\n  }) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      current.forEach(node => {\n        node.depth = x;\n        for (const {\n          target\n        } of node.sourceLinks) {\n          next.add(target);\n        }\n      });\n      if (++x > n) throw new Error('circular link');\n      current = next;\n      next = new Set();\n    }\n    // 如果配置了 depth，则设置自定义 depth\n    if (depth) {\n      const maxDepth = Math.max(max(nodes, d => d.depth) + 1, 0);\n      let node;\n      for (let i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.depth = depth.call(null, node, maxDepth);\n      }\n    }\n  }\n  function computeNodeHeights({\n    nodes\n  }) {\n    const n = nodes.length;\n    let current = new Set(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      current.forEach(node => {\n        node.height = x;\n        for (const {\n          source\n        } of node.targetLinks) {\n          next.add(source);\n        }\n      });\n      if (++x > n) throw new Error('circular link');\n      current = next;\n      next = new Set();\n    }\n  }\n  function computeNodeLayers({\n    nodes\n  }) {\n    const x = Math.max(max(nodes, d => d.depth) + 1, 0);\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x).fill(0).map(() => []);\n    for (const node of nodes) {\n      const i = Math.max(0, Math.min(x - 1, Math.floor(align.call(null, node, x))));\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);else columns[i] = [node];\n    }\n    if (sort) for (const column of columns) {\n      column.sort(sort);\n    }\n    return columns;\n  }\n  function initializeNodeBreadths(columns) {\n    const ky = min(columns, c => (y1 - y0 - (c.length - 1) * py) / sum(c, value));\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(dy, (y1 - y0) / (max(columns, c => c.length) - 1));\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const {\n          source,\n          value\n        } of target.targetLinks) {\n          const v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        const dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      if (column.length) resolveCollisions(column, beta);\n    }\n  }\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const {\n          target,\n          value\n        } of source.sourceLinks) {\n          const v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        const dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      if (column.length) resolveCollisions(column, beta);\n    }\n  }\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) node.y0 += dy, node.y1 += dy;\n      y = node.y1 + py;\n    }\n  }\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) node.y0 -= dy, node.y1 -= dy;\n      y = node.y0 - py;\n    }\n  }\n  function reorderNodeLinks({\n    sourceLinks,\n    targetLinks\n  }) {\n    if (linkSort === undefined) {\n      for (const {\n        source: {\n          sourceLinks\n        }\n      } of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {\n        target: {\n          targetLinks\n        }\n      } of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const {\n        sourceLinks,\n        targetLinks\n      } of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - (source.sourceLinks.length - 1) * py / 2;\n    for (const {\n      target: node,\n      width\n    } of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const {\n      source: node,\n      width\n    } of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - (target.targetLinks.length - 1) * py / 2;\n    for (const {\n      source: node,\n      width\n    } of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const {\n      target: node,\n      width\n    } of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n  return sankey;\n}", "map": {"version": 3, "names": ["sum", "max", "min", "justify", "constant", "ascendingSourceBreadth", "a", "b", "ascendingBreadth", "source", "index", "ascending<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "target", "y0", "value", "d", "defaultId", "defaultNodes", "graph", "nodes", "defaultLinks", "links", "find", "nodeById", "id", "node", "get", "Error", "computeLinkBreadths", "y1", "link", "sourceLinks", "width", "targetLinks", "<PERSON><PERSON>", "x0", "x1", "dx", "dy", "py", "align", "depth", "sort", "linkSort", "iterations", "sankey", "arg", "computeNodeLinks", "computeNodeValues", "computeNodeDepths", "computeNodeHeights", "computeNodeBreadths", "update", "nodeId", "_", "arguments", "length", "nodeAlign", "nodeDepth", "nodeSort", "nodeWidth", "nodePadding", "size", "extent", "for<PERSON>ach", "idx", "Map", "map", "push", "fixedValue", "undefined", "Math", "n", "current", "Set", "next", "x", "add", "max<PERSON><PERSON><PERSON>", "i", "call", "height", "computeNodeLayers", "kx", "columns", "Array", "fill", "floor", "layer", "column", "initializeNodeBreadths", "ky", "c", "y", "reorderLinks", "alpha", "pow", "beta", "relaxRightToLeft", "relaxLeftToRight", "w", "v", "targetTop", "reorderNodeLinks", "resolveCollisions", "sourceTop", "subject", "resolveCollisionsBottomToTop", "resolveCollisionsTopToBottom"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/g2/src/data/utils/d3-sankey/sankey.ts"], "sourcesContent": ["import { sum, max, min } from '@antv/vendor/d3-array';\nimport { justify } from './align';\nimport { constant } from './constant';\n\nfunction ascendingSourceBreadth(a, b) {\n  return ascendingBreadth(a.source, b.source) || a.index - b.index;\n}\n\nfunction ascendingTargetBreadth(a, b) {\n  return ascendingBreadth(a.target, b.target) || a.index - b.index;\n}\n\nfunction ascendingBreadth(a, b) {\n  return a.y0 - b.y0;\n}\n\nfunction value(d) {\n  return d.value;\n}\n\nfunction defaultId(d) {\n  return d.index;\n}\n\nfunction defaultNodes(graph) {\n  return graph.nodes;\n}\n\nfunction defaultLinks(graph) {\n  return graph.links;\n}\n\nfunction find(nodeById, id) {\n  const node = nodeById.get(id);\n  if (!node) throw new Error('missing: ' + id);\n  return node;\n}\n\nfunction computeLinkBreadths({ nodes }) {\n  for (const node of nodes) {\n    let y0 = node.y0;\n    let y1 = y0;\n    for (const link of node.sourceLinks) {\n      link.y0 = y0 + link.width / 2;\n      y0 += link.width;\n    }\n    for (const link of node.targetLinks) {\n      link.y1 = y1 + link.width / 2;\n      y1 += link.width;\n    }\n  }\n}\n\nexport function Sankey() {\n  let x0 = 0,\n    y0 = 0,\n    x1 = 1,\n    y1 = 1; // extent\n  let dx = 24; // nodeWidth\n  let dy = 8,\n    py; // nodePadding\n  let id = defaultId;\n  let align = justify;\n  let depth;\n  let sort;\n  let linkSort;\n  let nodes = defaultNodes;\n  let links = defaultLinks;\n  let iterations = 6;\n\n  function sankey(arg) {\n    const graph = {\n      nodes: nodes(arg),\n      links: links(arg),\n    };\n    computeNodeLinks(graph);\n    computeNodeValues(graph);\n    computeNodeDepths(graph);\n    computeNodeHeights(graph);\n    computeNodeBreadths(graph);\n    computeLinkBreadths(graph);\n    return graph;\n  }\n\n  sankey.update = function (graph) {\n    computeLinkBreadths(graph);\n    return graph;\n  };\n\n  sankey.nodeId = function (_) {\n    return arguments.length\n      ? ((id = typeof _ === 'function' ? _ : constant(_)), sankey)\n      : id;\n  };\n\n  sankey.nodeAlign = function (_) {\n    return arguments.length\n      ? ((align = typeof _ === 'function' ? _ : constant(_)), sankey)\n      : align;\n  };\n\n  sankey.nodeDepth = function (_) {\n    return arguments.length\n      ? ((depth = typeof _ === 'function' ? _ : _), sankey)\n      : depth;\n  };\n\n  sankey.nodeSort = function (_) {\n    return arguments.length ? ((sort = _), sankey) : sort;\n  };\n\n  sankey.nodeWidth = function (_) {\n    return arguments.length ? ((dx = +_), sankey) : dx;\n  };\n\n  sankey.nodePadding = function (_) {\n    return arguments.length ? ((dy = py = +_), sankey) : dy;\n  };\n\n  sankey.nodes = function (_) {\n    return arguments.length\n      ? ((nodes = typeof _ === 'function' ? _ : constant(_)), sankey)\n      : nodes;\n  };\n\n  sankey.links = function (_) {\n    return arguments.length\n      ? ((links = typeof _ === 'function' ? _ : constant(_)), sankey)\n      : links;\n  };\n\n  sankey.linkSort = function (_) {\n    return arguments.length ? ((linkSort = _), sankey) : linkSort;\n  };\n\n  sankey.size = function (_) {\n    return arguments.length\n      ? ((x0 = y0 = 0), (x1 = +_[0]), (y1 = +_[1]), sankey)\n      : [x1 - x0, y1 - y0];\n  };\n\n  sankey.extent = function (_) {\n    return arguments.length\n      ? ((x0 = +_[0][0]),\n        (x1 = +_[1][0]),\n        (y0 = +_[0][1]),\n        (y1 = +_[1][1]),\n        sankey)\n      : [\n          [x0, y0],\n          [x1, y1],\n        ];\n  };\n\n  sankey.iterations = function (_) {\n    return arguments.length ? ((iterations = +_), sankey) : iterations;\n  };\n\n  function computeNodeLinks({ nodes, links }) {\n    nodes.forEach((node, idx) => {\n      node.index = idx;\n      node.sourceLinks = [];\n      node.targetLinks = [];\n    });\n\n    const nodeById = new Map(nodes.map((d) => [id(d), d]));\n\n    links.forEach((link, idx) => {\n      link.index = idx;\n      let { source, target } = link;\n      if (typeof source !== 'object')\n        source = link.source = find(nodeById, source);\n      if (typeof target !== 'object')\n        target = link.target = find(nodeById, target);\n      source.sourceLinks.push(link);\n      target.targetLinks.push(link);\n    });\n\n    if (linkSort != null) {\n      for (const { sourceLinks, targetLinks } of nodes) {\n        sourceLinks.sort(linkSort);\n        targetLinks.sort(linkSort);\n      }\n    }\n  }\n\n  function computeNodeValues({ nodes }) {\n    for (const node of nodes) {\n      node.value =\n        node.fixedValue === undefined\n          ? Math.max(sum(node.sourceLinks, value), sum(node.targetLinks, value))\n          : node.fixedValue;\n    }\n  }\n\n  function computeNodeDepths({ nodes }) {\n    const n = nodes.length;\n    let current = new Set<any>(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      current.forEach((node) => {\n        node.depth = x;\n        for (const { target } of node.sourceLinks) {\n          next.add(target);\n        }\n      });\n      if (++x > n) throw new Error('circular link');\n      current = next;\n      next = new Set();\n    }\n\n    // 如果配置了 depth，则设置自定义 depth\n    if (depth) {\n      const maxDepth = Math.max(\n        max(nodes, (d: { depth: number }) => d.depth) + 1,\n        0,\n      );\n\n      let node;\n      for (let i = 0; i < nodes.length; i++) {\n        node = nodes[i];\n        node.depth = depth.call(null, node, maxDepth);\n      }\n    }\n  }\n\n  function computeNodeHeights({ nodes }) {\n    const n = nodes.length;\n    let current = new Set<any>(nodes);\n    let next = new Set();\n    let x = 0;\n    while (current.size) {\n      current.forEach((node) => {\n        node.height = x;\n        for (const { source } of node.targetLinks) {\n          next.add(source);\n        }\n      });\n      if (++x > n) throw new Error('circular link');\n      current = next;\n      next = new Set();\n    }\n  }\n\n  function computeNodeLayers({ nodes }) {\n    const x = Math.max(max(nodes, (d: { depth: number }) => d.depth) + 1, 0);\n    const kx = (x1 - x0 - dx) / (x - 1);\n    const columns = new Array(x).fill(0).map(() => []);\n    for (const node of nodes) {\n      const i = Math.max(\n        0,\n        Math.min(x - 1, Math.floor(align.call(null, node, x))),\n      );\n      node.layer = i;\n      node.x0 = x0 + i * kx;\n      node.x1 = node.x0 + dx;\n      if (columns[i]) columns[i].push(node);\n      else columns[i] = [node];\n    }\n    if (sort)\n      for (const column of columns) {\n        column.sort(sort);\n      }\n    return columns;\n  }\n\n  function initializeNodeBreadths(columns) {\n    const ky = min(\n      columns,\n      (c: any[]) => (y1 - y0 - (c.length - 1) * py) / sum(c, value),\n    ) as any as number;\n    for (const nodes of columns) {\n      let y = y0;\n      for (const node of nodes) {\n        node.y0 = y;\n        node.y1 = y + node.value * ky;\n        y = node.y1 + py;\n        for (const link of node.sourceLinks) {\n          link.width = link.value * ky;\n        }\n      }\n      y = (y1 - y + py) / (nodes.length + 1);\n      for (let i = 0; i < nodes.length; ++i) {\n        const node = nodes[i];\n        node.y0 += y * (i + 1);\n        node.y1 += y * (i + 1);\n      }\n      reorderLinks(nodes);\n    }\n  }\n\n  function computeNodeBreadths(graph) {\n    const columns = computeNodeLayers(graph);\n    py = Math.min(\n      dy,\n      (y1 - y0) / ((max(columns, (c: any[]) => c.length) as any as number) - 1),\n    );\n    initializeNodeBreadths(columns);\n    for (let i = 0; i < iterations; ++i) {\n      const alpha = Math.pow(0.99, i);\n      const beta = Math.max(1 - alpha, (i + 1) / iterations);\n      relaxRightToLeft(columns, alpha, beta);\n      relaxLeftToRight(columns, alpha, beta);\n    }\n  }\n\n  // Reposition each node based on its incoming (target) links.\n  function relaxLeftToRight(columns, alpha, beta) {\n    for (let i = 1, n = columns.length; i < n; ++i) {\n      const column = columns[i];\n      for (const target of column) {\n        let y = 0;\n        let w = 0;\n        for (const { source, value } of target.targetLinks) {\n          const v = value * (target.layer - source.layer);\n          y += targetTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        const dy = (y / w - target.y0) * alpha;\n        target.y0 += dy;\n        target.y1 += dy;\n        reorderNodeLinks(target);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      if (column.length) resolveCollisions(column, beta);\n    }\n  }\n\n  // Reposition each node based on its outgoing (source) links.\n  function relaxRightToLeft(columns, alpha, beta) {\n    for (let n = columns.length, i = n - 2; i >= 0; --i) {\n      const column = columns[i];\n      for (const source of column) {\n        let y = 0;\n        let w = 0;\n        for (const { target, value } of source.sourceLinks) {\n          const v = value * (target.layer - source.layer);\n          y += sourceTop(source, target) * v;\n          w += v;\n        }\n        if (!(w > 0)) continue;\n        const dy = (y / w - source.y0) * alpha;\n        source.y0 += dy;\n        source.y1 += dy;\n        reorderNodeLinks(source);\n      }\n      if (sort === undefined) column.sort(ascendingBreadth);\n      if (column.length) resolveCollisions(column, beta);\n    }\n  }\n\n  function resolveCollisions(nodes, alpha) {\n    const i = nodes.length >> 1;\n    const subject = nodes[i];\n    resolveCollisionsBottomToTop(nodes, subject.y0 - py, i - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, subject.y1 + py, i + 1, alpha);\n    resolveCollisionsBottomToTop(nodes, y1, nodes.length - 1, alpha);\n    resolveCollisionsTopToBottom(nodes, y0, 0, alpha);\n  }\n\n  // Push any overlapping nodes down.\n  function resolveCollisionsTopToBottom(nodes, y, i, alpha) {\n    for (; i < nodes.length; ++i) {\n      const node = nodes[i];\n      const dy = (y - node.y0) * alpha;\n      if (dy > 1e-6) (node.y0 += dy), (node.y1 += dy);\n      y = node.y1 + py;\n    }\n  }\n\n  // Push any overlapping nodes up.\n  function resolveCollisionsBottomToTop(nodes, y, i, alpha) {\n    for (; i >= 0; --i) {\n      const node = nodes[i];\n      const dy = (node.y1 - y) * alpha;\n      if (dy > 1e-6) (node.y0 -= dy), (node.y1 -= dy);\n      y = node.y0 - py;\n    }\n  }\n\n  function reorderNodeLinks({ sourceLinks, targetLinks }) {\n    if (linkSort === undefined) {\n      for (const {\n        source: { sourceLinks },\n      } of targetLinks) {\n        sourceLinks.sort(ascendingTargetBreadth);\n      }\n      for (const {\n        target: { targetLinks },\n      } of sourceLinks) {\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  function reorderLinks(nodes) {\n    if (linkSort === undefined) {\n      for (const { sourceLinks, targetLinks } of nodes) {\n        sourceLinks.sort(ascendingTargetBreadth);\n        targetLinks.sort(ascendingSourceBreadth);\n      }\n    }\n  }\n\n  // Returns the target.y0 that would produce an ideal link from source to target.\n  function targetTop(source, target) {\n    let y = source.y0 - ((source.sourceLinks.length - 1) * py) / 2;\n    for (const { target: node, width } of source.sourceLinks) {\n      if (node === target) break;\n      y += width + py;\n    }\n    for (const { source: node, width } of target.targetLinks) {\n      if (node === source) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  // Returns the source.y0 that would produce an ideal link from source to target.\n  function sourceTop(source, target) {\n    let y = target.y0 - ((target.targetLinks.length - 1) * py) / 2;\n    for (const { source: node, width } of target.targetLinks) {\n      if (node === source) break;\n      y += width + py;\n    }\n    for (const { target: node, width } of source.sourceLinks) {\n      if (node === target) break;\n      y -= width;\n    }\n    return y;\n  }\n\n  return sankey;\n}\n"], "mappings": "AAAA,SAASA,GAAG,EAAEC,GAAG,EAAEC,GAAG,QAAQ,uBAAuB;AACrD,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,QAAQ,QAAQ,YAAY;AAErC,SAASC,sBAAsBA,CAACC,CAAC,EAAEC,CAAC;EAClC,OAAOC,gBAAgB,CAACF,CAAC,CAACG,MAAM,EAAEF,CAAC,CAACE,MAAM,CAAC,IAAIH,CAAC,CAACI,KAAK,GAAGH,CAAC,CAACG,KAAK;AAClE;AAEA,SAASC,sBAAsBA,CAACL,CAAC,EAAEC,CAAC;EAClC,OAAOC,gBAAgB,CAACF,CAAC,CAACM,MAAM,EAAEL,CAAC,CAACK,MAAM,CAAC,IAAIN,CAAC,CAACI,KAAK,GAAGH,CAAC,CAACG,KAAK;AAClE;AAEA,SAASF,gBAAgBA,CAACF,CAAC,EAAEC,CAAC;EAC5B,OAAOD,CAAC,CAACO,EAAE,GAAGN,CAAC,CAACM,EAAE;AACpB;AAEA,SAASC,KAAKA,CAACC,CAAC;EACd,OAAOA,CAAC,CAACD,KAAK;AAChB;AAEA,SAASE,SAASA,CAACD,CAAC;EAClB,OAAOA,CAAC,CAACL,KAAK;AAChB;AAEA,SAASO,YAAYA,CAACC,KAAK;EACzB,OAAOA,KAAK,CAACC,KAAK;AACpB;AAEA,SAASC,YAAYA,CAACF,KAAK;EACzB,OAAOA,KAAK,CAACG,KAAK;AACpB;AAEA,SAASC,IAAIA,CAACC,QAAQ,EAAEC,EAAE;EACxB,MAAMC,IAAI,GAAGF,QAAQ,CAACG,GAAG,CAACF,EAAE,CAAC;EAC7B,IAAI,CAACC,IAAI,EAAE,MAAM,IAAIE,KAAK,CAAC,WAAW,GAAGH,EAAE,CAAC;EAC5C,OAAOC,IAAI;AACb;AAEA,SAASG,mBAAmBA,CAAC;EAAET;AAAK,CAAE;EACpC,KAAK,MAAMM,IAAI,IAAIN,KAAK,EAAE;IACxB,IAAIN,EAAE,GAAGY,IAAI,CAACZ,EAAE;IAChB,IAAIgB,EAAE,GAAGhB,EAAE;IACX,KAAK,MAAMiB,IAAI,IAAIL,IAAI,CAACM,WAAW,EAAE;MACnCD,IAAI,CAACjB,EAAE,GAAGA,EAAE,GAAGiB,IAAI,CAACE,KAAK,GAAG,CAAC;MAC7BnB,EAAE,IAAIiB,IAAI,CAACE,KAAK;;IAElB,KAAK,MAAMF,IAAI,IAAIL,IAAI,CAACQ,WAAW,EAAE;MACnCH,IAAI,CAACD,EAAE,GAAGA,EAAE,GAAGC,IAAI,CAACE,KAAK,GAAG,CAAC;MAC7BH,EAAE,IAAIC,IAAI,CAACE,KAAK;;;AAGtB;AAEA,OAAM,SAAUE,MAAMA,CAAA;EACpB,IAAIC,EAAE,GAAG,CAAC;IACRtB,EAAE,GAAG,CAAC;IACNuB,EAAE,GAAG,CAAC;IACNP,EAAE,GAAG,CAAC,CAAC,CAAC;EACV,IAAIQ,EAAE,GAAG,EAAE,CAAC,CAAC;EACb,IAAIC,EAAE,GAAG,CAAC;IACRC,EAAE,CAAC,CAAC;EACN,IAAIf,EAAE,GAAGR,SAAS;EAClB,IAAIwB,KAAK,GAAGrC,OAAO;EACnB,IAAIsC,KAAK;EACT,IAAIC,IAAI;EACR,IAAIC,QAAQ;EACZ,IAAIxB,KAAK,GAAGF,YAAY;EACxB,IAAII,KAAK,GAAGD,YAAY;EACxB,IAAIwB,UAAU,GAAG,CAAC;EAElB,SAASC,MAAMA,CAACC,GAAG;IACjB,MAAM5B,KAAK,GAAG;MACZC,KAAK,EAAEA,KAAK,CAAC2B,GAAG,CAAC;MACjBzB,KAAK,EAAEA,KAAK,CAACyB,GAAG;KACjB;IACDC,gBAAgB,CAAC7B,KAAK,CAAC;IACvB8B,iBAAiB,CAAC9B,KAAK,CAAC;IACxB+B,iBAAiB,CAAC/B,KAAK,CAAC;IACxBgC,kBAAkB,CAAChC,KAAK,CAAC;IACzBiC,mBAAmB,CAACjC,KAAK,CAAC;IAC1BU,mBAAmB,CAACV,KAAK,CAAC;IAC1B,OAAOA,KAAK;EACd;EAEA2B,MAAM,CAACO,MAAM,GAAG,UAAUlC,KAAK;IAC7BU,mBAAmB,CAACV,KAAK,CAAC;IAC1B,OAAOA,KAAK;EACd,CAAC;EAED2B,MAAM,CAACQ,MAAM,GAAG,UAAUC,CAAC;IACzB,OAAOC,SAAS,CAACC,MAAM,IACjBhC,EAAE,GAAG,OAAO8B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlD,QAAQ,CAACkD,CAAC,CAAC,EAAGT,MAAM,IACzDrB,EAAE;EACR,CAAC;EAEDqB,MAAM,CAACY,SAAS,GAAG,UAAUH,CAAC;IAC5B,OAAOC,SAAS,CAACC,MAAM,IACjBhB,KAAK,GAAG,OAAOc,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlD,QAAQ,CAACkD,CAAC,CAAC,EAAGT,MAAM,IAC5DL,KAAK;EACX,CAAC;EAEDK,MAAM,CAACa,SAAS,GAAG,UAAUJ,CAAC;IAC5B,OAAOC,SAAS,CAACC,MAAM,IACjBf,KAAK,GAAG,OAAOa,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGA,CAAC,EAAGT,MAAM,IAClDJ,KAAK;EACX,CAAC;EAEDI,MAAM,CAACc,QAAQ,GAAG,UAAUL,CAAC;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAKd,IAAI,GAAGY,CAAC,EAAGT,MAAM,IAAIH,IAAI;EACvD,CAAC;EAEDG,MAAM,CAACe,SAAS,GAAG,UAAUN,CAAC;IAC5B,OAAOC,SAAS,CAACC,MAAM,IAAKnB,EAAE,GAAG,CAACiB,CAAC,EAAGT,MAAM,IAAIR,EAAE;EACpD,CAAC;EAEDQ,MAAM,CAACgB,WAAW,GAAG,UAAUP,CAAC;IAC9B,OAAOC,SAAS,CAACC,MAAM,IAAKlB,EAAE,GAAGC,EAAE,GAAG,CAACe,CAAC,EAAGT,MAAM,IAAIP,EAAE;EACzD,CAAC;EAEDO,MAAM,CAAC1B,KAAK,GAAG,UAAUmC,CAAC;IACxB,OAAOC,SAAS,CAACC,MAAM,IACjBrC,KAAK,GAAG,OAAOmC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlD,QAAQ,CAACkD,CAAC,CAAC,EAAGT,MAAM,IAC5D1B,KAAK;EACX,CAAC;EAED0B,MAAM,CAACxB,KAAK,GAAG,UAAUiC,CAAC;IACxB,OAAOC,SAAS,CAACC,MAAM,IACjBnC,KAAK,GAAG,OAAOiC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlD,QAAQ,CAACkD,CAAC,CAAC,EAAGT,MAAM,IAC5DxB,KAAK;EACX,CAAC;EAEDwB,MAAM,CAACF,QAAQ,GAAG,UAAUW,CAAC;IAC3B,OAAOC,SAAS,CAACC,MAAM,IAAKb,QAAQ,GAAGW,CAAC,EAAGT,MAAM,IAAIF,QAAQ;EAC/D,CAAC;EAEDE,MAAM,CAACiB,IAAI,GAAG,UAAUR,CAAC;IACvB,OAAOC,SAAS,CAACC,MAAM,IACjBrB,EAAE,GAAGtB,EAAE,GAAG,CAAC,EAAIuB,EAAE,GAAG,CAACkB,CAAC,CAAC,CAAC,CAAC,EAAIzB,EAAE,GAAG,CAACyB,CAAC,CAAC,CAAC,CAAC,EAAGT,MAAM,IAClD,CAACT,EAAE,GAAGD,EAAE,EAAEN,EAAE,GAAGhB,EAAE,CAAC;EACxB,CAAC;EAEDgC,MAAM,CAACkB,MAAM,GAAG,UAAUT,CAAC;IACzB,OAAOC,SAAS,CAACC,MAAM,IACjBrB,EAAE,GAAG,CAACmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACdlB,EAAE,GAAG,CAACkB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACbzC,EAAE,GAAG,CAACyC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACbzB,EAAE,GAAG,CAACyB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACdT,MAAM,IACN,CACE,CAACV,EAAE,EAAEtB,EAAE,CAAC,EACR,CAACuB,EAAE,EAAEP,EAAE,CAAC,CACT;EACP,CAAC;EAEDgB,MAAM,CAACD,UAAU,GAAG,UAAUU,CAAC;IAC7B,OAAOC,SAAS,CAACC,MAAM,IAAKZ,UAAU,GAAG,CAACU,CAAC,EAAGT,MAAM,IAAID,UAAU;EACpE,CAAC;EAED,SAASG,gBAAgBA,CAAC;IAAE5B,KAAK;IAAEE;EAAK,CAAE;IACxCF,KAAK,CAAC6C,OAAO,CAAC,CAACvC,IAAI,EAAEwC,GAAG,KAAI;MAC1BxC,IAAI,CAACf,KAAK,GAAGuD,GAAG;MAChBxC,IAAI,CAACM,WAAW,GAAG,EAAE;MACrBN,IAAI,CAACQ,WAAW,GAAG,EAAE;IACvB,CAAC,CAAC;IAEF,MAAMV,QAAQ,GAAG,IAAI2C,GAAG,CAAC/C,KAAK,CAACgD,GAAG,CAAEpD,CAAC,IAAK,CAACS,EAAE,CAACT,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC;IAEtDM,KAAK,CAAC2C,OAAO,CAAC,CAAClC,IAAI,EAAEmC,GAAG,KAAI;MAC1BnC,IAAI,CAACpB,KAAK,GAAGuD,GAAG;MAChB,IAAI;QAAExD,MAAM;QAAEG;MAAM,CAAE,GAAGkB,IAAI;MAC7B,IAAI,OAAOrB,MAAM,KAAK,QAAQ,EAC5BA,MAAM,GAAGqB,IAAI,CAACrB,MAAM,GAAGa,IAAI,CAACC,QAAQ,EAAEd,MAAM,CAAC;MAC/C,IAAI,OAAOG,MAAM,KAAK,QAAQ,EAC5BA,MAAM,GAAGkB,IAAI,CAAClB,MAAM,GAAGU,IAAI,CAACC,QAAQ,EAAEX,MAAM,CAAC;MAC/CH,MAAM,CAACsB,WAAW,CAACqC,IAAI,CAACtC,IAAI,CAAC;MAC7BlB,MAAM,CAACqB,WAAW,CAACmC,IAAI,CAACtC,IAAI,CAAC;IAC/B,CAAC,CAAC;IAEF,IAAIa,QAAQ,IAAI,IAAI,EAAE;MACpB,KAAK,MAAM;QAAEZ,WAAW;QAAEE;MAAW,CAAE,IAAId,KAAK,EAAE;QAChDY,WAAW,CAACW,IAAI,CAACC,QAAQ,CAAC;QAC1BV,WAAW,CAACS,IAAI,CAACC,QAAQ,CAAC;;;EAGhC;EAEA,SAASK,iBAAiBA,CAAC;IAAE7B;EAAK,CAAE;IAClC,KAAK,MAAMM,IAAI,IAAIN,KAAK,EAAE;MACxBM,IAAI,CAACX,KAAK,GACRW,IAAI,CAAC4C,UAAU,KAAKC,SAAS,GACzBC,IAAI,CAACtE,GAAG,CAACD,GAAG,CAACyB,IAAI,CAACM,WAAW,EAAEjB,KAAK,CAAC,EAAEd,GAAG,CAACyB,IAAI,CAACQ,WAAW,EAAEnB,KAAK,CAAC,CAAC,GACpEW,IAAI,CAAC4C,UAAU;;EAEzB;EAEA,SAASpB,iBAAiBA,CAAC;IAAE9B;EAAK,CAAE;IAClC,MAAMqD,CAAC,GAAGrD,KAAK,CAACqC,MAAM;IACtB,IAAIiB,OAAO,GAAG,IAAIC,GAAG,CAAMvD,KAAK,CAAC;IACjC,IAAIwD,IAAI,GAAG,IAAID,GAAG,EAAE;IACpB,IAAIE,CAAC,GAAG,CAAC;IACT,OAAOH,OAAO,CAACX,IAAI,EAAE;MACnBW,OAAO,CAACT,OAAO,CAAEvC,IAAI,IAAI;QACvBA,IAAI,CAACgB,KAAK,GAAGmC,CAAC;QACd,KAAK,MAAM;UAAEhE;QAAM,CAAE,IAAIa,IAAI,CAACM,WAAW,EAAE;UACzC4C,IAAI,CAACE,GAAG,CAACjE,MAAM,CAAC;;MAEpB,CAAC,CAAC;MACF,IAAI,EAAEgE,CAAC,GAAGJ,CAAC,EAAE,MAAM,IAAI7C,KAAK,CAAC,eAAe,CAAC;MAC7C8C,OAAO,GAAGE,IAAI;MACdA,IAAI,GAAG,IAAID,GAAG,EAAE;;IAGlB;IACA,IAAIjC,KAAK,EAAE;MACT,MAAMqC,QAAQ,GAAGP,IAAI,CAACtE,GAAG,CACvBA,GAAG,CAACkB,KAAK,EAAGJ,CAAoB,IAAKA,CAAC,CAAC0B,KAAK,CAAC,GAAG,CAAC,EACjD,CAAC,CACF;MAED,IAAIhB,IAAI;MACR,KAAK,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,KAAK,CAACqC,MAAM,EAAEuB,CAAC,EAAE,EAAE;QACrCtD,IAAI,GAAGN,KAAK,CAAC4D,CAAC,CAAC;QACftD,IAAI,CAACgB,KAAK,GAAGA,KAAK,CAACuC,IAAI,CAAC,IAAI,EAAEvD,IAAI,EAAEqD,QAAQ,CAAC;;;EAGnD;EAEA,SAAS5B,kBAAkBA,CAAC;IAAE/B;EAAK,CAAE;IACnC,MAAMqD,CAAC,GAAGrD,KAAK,CAACqC,MAAM;IACtB,IAAIiB,OAAO,GAAG,IAAIC,GAAG,CAAMvD,KAAK,CAAC;IACjC,IAAIwD,IAAI,GAAG,IAAID,GAAG,EAAE;IACpB,IAAIE,CAAC,GAAG,CAAC;IACT,OAAOH,OAAO,CAACX,IAAI,EAAE;MACnBW,OAAO,CAACT,OAAO,CAAEvC,IAAI,IAAI;QACvBA,IAAI,CAACwD,MAAM,GAAGL,CAAC;QACf,KAAK,MAAM;UAAEnE;QAAM,CAAE,IAAIgB,IAAI,CAACQ,WAAW,EAAE;UACzC0C,IAAI,CAACE,GAAG,CAACpE,MAAM,CAAC;;MAEpB,CAAC,CAAC;MACF,IAAI,EAAEmE,CAAC,GAAGJ,CAAC,EAAE,MAAM,IAAI7C,KAAK,CAAC,eAAe,CAAC;MAC7C8C,OAAO,GAAGE,IAAI;MACdA,IAAI,GAAG,IAAID,GAAG,EAAE;;EAEpB;EAEA,SAASQ,iBAAiBA,CAAC;IAAE/D;EAAK,CAAE;IAClC,MAAMyD,CAAC,GAAGL,IAAI,CAACtE,GAAG,CAACA,GAAG,CAACkB,KAAK,EAAGJ,CAAoB,IAAKA,CAAC,CAAC0B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IACxE,MAAM0C,EAAE,GAAG,CAAC/C,EAAE,GAAGD,EAAE,GAAGE,EAAE,KAAKuC,CAAC,GAAG,CAAC,CAAC;IACnC,MAAMQ,OAAO,GAAG,IAAIC,KAAK,CAACT,CAAC,CAAC,CAACU,IAAI,CAAC,CAAC,CAAC,CAACnB,GAAG,CAAC,MAAM,EAAE,CAAC;IAClD,KAAK,MAAM1C,IAAI,IAAIN,KAAK,EAAE;MACxB,MAAM4D,CAAC,GAAGR,IAAI,CAACtE,GAAG,CAChB,CAAC,EACDsE,IAAI,CAACrE,GAAG,CAAC0E,CAAC,GAAG,CAAC,EAAEL,IAAI,CAACgB,KAAK,CAAC/C,KAAK,CAACwC,IAAI,CAAC,IAAI,EAAEvD,IAAI,EAAEmD,CAAC,CAAC,CAAC,CAAC,CACvD;MACDnD,IAAI,CAAC+D,KAAK,GAAGT,CAAC;MACdtD,IAAI,CAACU,EAAE,GAAGA,EAAE,GAAG4C,CAAC,GAAGI,EAAE;MACrB1D,IAAI,CAACW,EAAE,GAAGX,IAAI,CAACU,EAAE,GAAGE,EAAE;MACtB,IAAI+C,OAAO,CAACL,CAAC,CAAC,EAAEK,OAAO,CAACL,CAAC,CAAC,CAACX,IAAI,CAAC3C,IAAI,CAAC,CAAC,KACjC2D,OAAO,CAACL,CAAC,CAAC,GAAG,CAACtD,IAAI,CAAC;;IAE1B,IAAIiB,IAAI,EACN,KAAK,MAAM+C,MAAM,IAAIL,OAAO,EAAE;MAC5BK,MAAM,CAAC/C,IAAI,CAACA,IAAI,CAAC;;IAErB,OAAO0C,OAAO;EAChB;EAEA,SAASM,sBAAsBA,CAACN,OAAO;IACrC,MAAMO,EAAE,GAAGzF,GAAG,CACZkF,OAAO,EACNQ,CAAQ,IAAK,CAAC/D,EAAE,GAAGhB,EAAE,GAAG,CAAC+E,CAAC,CAACpC,MAAM,GAAG,CAAC,IAAIjB,EAAE,IAAIvC,GAAG,CAAC4F,CAAC,EAAE9E,KAAK,CAAC,CAC7C;IAClB,KAAK,MAAMK,KAAK,IAAIiE,OAAO,EAAE;MAC3B,IAAIS,CAAC,GAAGhF,EAAE;MACV,KAAK,MAAMY,IAAI,IAAIN,KAAK,EAAE;QACxBM,IAAI,CAACZ,EAAE,GAAGgF,CAAC;QACXpE,IAAI,CAACI,EAAE,GAAGgE,CAAC,GAAGpE,IAAI,CAACX,KAAK,GAAG6E,EAAE;QAC7BE,CAAC,GAAGpE,IAAI,CAACI,EAAE,GAAGU,EAAE;QAChB,KAAK,MAAMT,IAAI,IAAIL,IAAI,CAACM,WAAW,EAAE;UACnCD,IAAI,CAACE,KAAK,GAAGF,IAAI,CAAChB,KAAK,GAAG6E,EAAE;;;MAGhCE,CAAC,GAAG,CAAChE,EAAE,GAAGgE,CAAC,GAAGtD,EAAE,KAAKpB,KAAK,CAACqC,MAAM,GAAG,CAAC,CAAC;MACtC,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,KAAK,CAACqC,MAAM,EAAE,EAAEuB,CAAC,EAAE;QACrC,MAAMtD,IAAI,GAAGN,KAAK,CAAC4D,CAAC,CAAC;QACrBtD,IAAI,CAACZ,EAAE,IAAIgF,CAAC,IAAId,CAAC,GAAG,CAAC,CAAC;QACtBtD,IAAI,CAACI,EAAE,IAAIgE,CAAC,IAAId,CAAC,GAAG,CAAC,CAAC;;MAExBe,YAAY,CAAC3E,KAAK,CAAC;;EAEvB;EAEA,SAASgC,mBAAmBA,CAACjC,KAAK;IAChC,MAAMkE,OAAO,GAAGF,iBAAiB,CAAChE,KAAK,CAAC;IACxCqB,EAAE,GAAGgC,IAAI,CAACrE,GAAG,CACXoC,EAAE,EACF,CAACT,EAAE,GAAGhB,EAAE,KAAMZ,GAAG,CAACmF,OAAO,EAAGQ,CAAQ,IAAKA,CAAC,CAACpC,MAAM,CAAmB,GAAG,CAAC,CAAC,CAC1E;IACDkC,sBAAsB,CAACN,OAAO,CAAC;IAC/B,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnC,UAAU,EAAE,EAAEmC,CAAC,EAAE;MACnC,MAAMgB,KAAK,GAAGxB,IAAI,CAACyB,GAAG,CAAC,IAAI,EAAEjB,CAAC,CAAC;MAC/B,MAAMkB,IAAI,GAAG1B,IAAI,CAACtE,GAAG,CAAC,CAAC,GAAG8F,KAAK,EAAE,CAAChB,CAAC,GAAG,CAAC,IAAInC,UAAU,CAAC;MACtDsD,gBAAgB,CAACd,OAAO,EAAEW,KAAK,EAAEE,IAAI,CAAC;MACtCE,gBAAgB,CAACf,OAAO,EAAEW,KAAK,EAAEE,IAAI,CAAC;;EAE1C;EAEA;EACA,SAASE,gBAAgBA,CAACf,OAAO,EAAEW,KAAK,EAAEE,IAAI;IAC5C,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEP,CAAC,GAAGY,OAAO,CAAC5B,MAAM,EAAEuB,CAAC,GAAGP,CAAC,EAAE,EAAEO,CAAC,EAAE;MAC9C,MAAMU,MAAM,GAAGL,OAAO,CAACL,CAAC,CAAC;MACzB,KAAK,MAAMnE,MAAM,IAAI6E,MAAM,EAAE;QAC3B,IAAII,CAAC,GAAG,CAAC;QACT,IAAIO,CAAC,GAAG,CAAC;QACT,KAAK,MAAM;UAAE3F,MAAM;UAAEK;QAAK,CAAE,IAAIF,MAAM,CAACqB,WAAW,EAAE;UAClD,MAAMoE,CAAC,GAAGvF,KAAK,IAAIF,MAAM,CAAC4E,KAAK,GAAG/E,MAAM,CAAC+E,KAAK,CAAC;UAC/CK,CAAC,IAAIS,SAAS,CAAC7F,MAAM,EAAEG,MAAM,CAAC,GAAGyF,CAAC;UAClCD,CAAC,IAAIC,CAAC;;QAER,IAAI,EAAED,CAAC,GAAG,CAAC,CAAC,EAAE;QACd,MAAM9D,EAAE,GAAG,CAACuD,CAAC,GAAGO,CAAC,GAAGxF,MAAM,CAACC,EAAE,IAAIkF,KAAK;QACtCnF,MAAM,CAACC,EAAE,IAAIyB,EAAE;QACf1B,MAAM,CAACiB,EAAE,IAAIS,EAAE;QACfiE,gBAAgB,CAAC3F,MAAM,CAAC;;MAE1B,IAAI8B,IAAI,KAAK4B,SAAS,EAAEmB,MAAM,CAAC/C,IAAI,CAAClC,gBAAgB,CAAC;MACrD,IAAIiF,MAAM,CAACjC,MAAM,EAAEgD,iBAAiB,CAACf,MAAM,EAAEQ,IAAI,CAAC;;EAEtD;EAEA;EACA,SAASC,gBAAgBA,CAACd,OAAO,EAAEW,KAAK,EAAEE,IAAI;IAC5C,KAAK,IAAIzB,CAAC,GAAGY,OAAO,CAAC5B,MAAM,EAAEuB,CAAC,GAAGP,CAAC,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACnD,MAAMU,MAAM,GAAGL,OAAO,CAACL,CAAC,CAAC;MACzB,KAAK,MAAMtE,MAAM,IAAIgF,MAAM,EAAE;QAC3B,IAAII,CAAC,GAAG,CAAC;QACT,IAAIO,CAAC,GAAG,CAAC;QACT,KAAK,MAAM;UAAExF,MAAM;UAAEE;QAAK,CAAE,IAAIL,MAAM,CAACsB,WAAW,EAAE;UAClD,MAAMsE,CAAC,GAAGvF,KAAK,IAAIF,MAAM,CAAC4E,KAAK,GAAG/E,MAAM,CAAC+E,KAAK,CAAC;UAC/CK,CAAC,IAAIY,SAAS,CAAChG,MAAM,EAAEG,MAAM,CAAC,GAAGyF,CAAC;UAClCD,CAAC,IAAIC,CAAC;;QAER,IAAI,EAAED,CAAC,GAAG,CAAC,CAAC,EAAE;QACd,MAAM9D,EAAE,GAAG,CAACuD,CAAC,GAAGO,CAAC,GAAG3F,MAAM,CAACI,EAAE,IAAIkF,KAAK;QACtCtF,MAAM,CAACI,EAAE,IAAIyB,EAAE;QACf7B,MAAM,CAACoB,EAAE,IAAIS,EAAE;QACfiE,gBAAgB,CAAC9F,MAAM,CAAC;;MAE1B,IAAIiC,IAAI,KAAK4B,SAAS,EAAEmB,MAAM,CAAC/C,IAAI,CAAClC,gBAAgB,CAAC;MACrD,IAAIiF,MAAM,CAACjC,MAAM,EAAEgD,iBAAiB,CAACf,MAAM,EAAEQ,IAAI,CAAC;;EAEtD;EAEA,SAASO,iBAAiBA,CAACrF,KAAK,EAAE4E,KAAK;IACrC,MAAMhB,CAAC,GAAG5D,KAAK,CAACqC,MAAM,IAAI,CAAC;IAC3B,MAAMkD,OAAO,GAAGvF,KAAK,CAAC4D,CAAC,CAAC;IACxB4B,4BAA4B,CAACxF,KAAK,EAAEuF,OAAO,CAAC7F,EAAE,GAAG0B,EAAE,EAAEwC,CAAC,GAAG,CAAC,EAAEgB,KAAK,CAAC;IAClEa,4BAA4B,CAACzF,KAAK,EAAEuF,OAAO,CAAC7E,EAAE,GAAGU,EAAE,EAAEwC,CAAC,GAAG,CAAC,EAAEgB,KAAK,CAAC;IAClEY,4BAA4B,CAACxF,KAAK,EAAEU,EAAE,EAAEV,KAAK,CAACqC,MAAM,GAAG,CAAC,EAAEuC,KAAK,CAAC;IAChEa,4BAA4B,CAACzF,KAAK,EAAEN,EAAE,EAAE,CAAC,EAAEkF,KAAK,CAAC;EACnD;EAEA;EACA,SAASa,4BAA4BA,CAACzF,KAAK,EAAE0E,CAAC,EAAEd,CAAC,EAAEgB,KAAK;IACtD,OAAOhB,CAAC,GAAG5D,KAAK,CAACqC,MAAM,EAAE,EAAEuB,CAAC,EAAE;MAC5B,MAAMtD,IAAI,GAAGN,KAAK,CAAC4D,CAAC,CAAC;MACrB,MAAMzC,EAAE,GAAG,CAACuD,CAAC,GAAGpE,IAAI,CAACZ,EAAE,IAAIkF,KAAK;MAChC,IAAIzD,EAAE,GAAG,IAAI,EAAGb,IAAI,CAACZ,EAAE,IAAIyB,EAAE,EAAIb,IAAI,CAACI,EAAE,IAAIS,EAAG;MAC/CuD,CAAC,GAAGpE,IAAI,CAACI,EAAE,GAAGU,EAAE;;EAEpB;EAEA;EACA,SAASoE,4BAA4BA,CAACxF,KAAK,EAAE0E,CAAC,EAAEd,CAAC,EAAEgB,KAAK;IACtD,OAAOhB,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MAClB,MAAMtD,IAAI,GAAGN,KAAK,CAAC4D,CAAC,CAAC;MACrB,MAAMzC,EAAE,GAAG,CAACb,IAAI,CAACI,EAAE,GAAGgE,CAAC,IAAIE,KAAK;MAChC,IAAIzD,EAAE,GAAG,IAAI,EAAGb,IAAI,CAACZ,EAAE,IAAIyB,EAAE,EAAIb,IAAI,CAACI,EAAE,IAAIS,EAAG;MAC/CuD,CAAC,GAAGpE,IAAI,CAACZ,EAAE,GAAG0B,EAAE;;EAEpB;EAEA,SAASgE,gBAAgBA,CAAC;IAAExE,WAAW;IAAEE;EAAW,CAAE;IACpD,IAAIU,QAAQ,KAAK2B,SAAS,EAAE;MAC1B,KAAK,MAAM;QACT7D,MAAM,EAAE;UAAEsB;QAAW;MAAE,CACxB,IAAIE,WAAW,EAAE;QAChBF,WAAW,CAACW,IAAI,CAAC/B,sBAAsB,CAAC;;MAE1C,KAAK,MAAM;QACTC,MAAM,EAAE;UAAEqB;QAAW;MAAE,CACxB,IAAIF,WAAW,EAAE;QAChBE,WAAW,CAACS,IAAI,CAACrC,sBAAsB,CAAC;;;EAG9C;EAEA,SAASyF,YAAYA,CAAC3E,KAAK;IACzB,IAAIwB,QAAQ,KAAK2B,SAAS,EAAE;MAC1B,KAAK,MAAM;QAAEvC,WAAW;QAAEE;MAAW,CAAE,IAAId,KAAK,EAAE;QAChDY,WAAW,CAACW,IAAI,CAAC/B,sBAAsB,CAAC;QACxCsB,WAAW,CAACS,IAAI,CAACrC,sBAAsB,CAAC;;;EAG9C;EAEA;EACA,SAASiG,SAASA,CAAC7F,MAAM,EAAEG,MAAM;IAC/B,IAAIiF,CAAC,GAAGpF,MAAM,CAACI,EAAE,GAAI,CAACJ,MAAM,CAACsB,WAAW,CAACyB,MAAM,GAAG,CAAC,IAAIjB,EAAE,GAAI,CAAC;IAC9D,KAAK,MAAM;MAAE3B,MAAM,EAAEa,IAAI;MAAEO;IAAK,CAAE,IAAIvB,MAAM,CAACsB,WAAW,EAAE;MACxD,IAAIN,IAAI,KAAKb,MAAM,EAAE;MACrBiF,CAAC,IAAI7D,KAAK,GAAGO,EAAE;;IAEjB,KAAK,MAAM;MAAE9B,MAAM,EAAEgB,IAAI;MAAEO;IAAK,CAAE,IAAIpB,MAAM,CAACqB,WAAW,EAAE;MACxD,IAAIR,IAAI,KAAKhB,MAAM,EAAE;MACrBoF,CAAC,IAAI7D,KAAK;;IAEZ,OAAO6D,CAAC;EACV;EAEA;EACA,SAASY,SAASA,CAAChG,MAAM,EAAEG,MAAM;IAC/B,IAAIiF,CAAC,GAAGjF,MAAM,CAACC,EAAE,GAAI,CAACD,MAAM,CAACqB,WAAW,CAACuB,MAAM,GAAG,CAAC,IAAIjB,EAAE,GAAI,CAAC;IAC9D,KAAK,MAAM;MAAE9B,MAAM,EAAEgB,IAAI;MAAEO;IAAK,CAAE,IAAIpB,MAAM,CAACqB,WAAW,EAAE;MACxD,IAAIR,IAAI,KAAKhB,MAAM,EAAE;MACrBoF,CAAC,IAAI7D,KAAK,GAAGO,EAAE;;IAEjB,KAAK,MAAM;MAAE3B,MAAM,EAAEa,IAAI;MAAEO;IAAK,CAAE,IAAIvB,MAAM,CAACsB,WAAW,EAAE;MACxD,IAAIN,IAAI,KAAKb,MAAM,EAAE;MACrBiF,CAAC,IAAI7D,KAAK;;IAEZ,OAAO6D,CAAC;EACV;EAEA,OAAOhD,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}