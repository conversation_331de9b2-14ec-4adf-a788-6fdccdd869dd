{"ast": null, "code": "import Octant from \"./octant.js\";\nexport default function (callback) {\n  var octs = [],\n    next = [],\n    q;\n  if (this._root) octs.push(new Octant(this._root, this._x0, this._y0, this._z0, this._x1, this._y1, this._z1));\n  while (q = octs.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child,\n        x0 = q.x0,\n        y0 = q.y0,\n        z0 = q.z0,\n        x1 = q.x1,\n        y1 = q.y1,\n        z1 = q.z1,\n        xm = (x0 + x1) / 2,\n        ym = (y0 + y1) / 2,\n        zm = (z0 + z1) / 2;\n      if (child = node[0]) octs.push(new Octant(child, x0, y0, z0, xm, ym, zm));\n      if (child = node[1]) octs.push(new Octant(child, xm, y0, z0, x1, ym, zm));\n      if (child = node[2]) octs.push(new Octant(child, x0, ym, z0, xm, y1, zm));\n      if (child = node[3]) octs.push(new Octant(child, xm, ym, z0, x1, y1, zm));\n      if (child = node[4]) octs.push(new Octant(child, x0, y0, zm, xm, ym, z1));\n      if (child = node[5]) octs.push(new Octant(child, xm, y0, zm, x1, ym, z1));\n      if (child = node[6]) octs.push(new Octant(child, x0, ym, zm, xm, y1, z1));\n      if (child = node[7]) octs.push(new Octant(child, xm, ym, zm, x1, y1, z1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.z0, q.x1, q.y1, q.z1);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["Octant", "callback", "octs", "next", "q", "_root", "push", "_x0", "_y0", "_z0", "_x1", "_y1", "_z1", "pop", "node", "length", "child", "x0", "y0", "z0", "x1", "y1", "z1", "xm", "ym", "zm"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-octree/src/visitAfter.js"], "sourcesContent": ["import Octant from \"./octant.js\";\n\nexport default function(callback) {\n  var octs = [], next = [], q;\n  if (this._root) octs.push(new Octant(this._root, this._x0, this._y0, this._z0, this._x1, this._y1, this._z1));\n  while (q = octs.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child, x0 = q.x0, y0 = q.y0, z0 = q.z0, x1 = q.x1, y1 = q.y1, z1 = q.z1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2, zm = (z0 + z1) / 2;\n      if (child = node[0]) octs.push(new Octant(child, x0, y0, z0, xm, ym, zm));\n      if (child = node[1]) octs.push(new Octant(child, xm, y0, z0, x1, ym, zm));\n      if (child = node[2]) octs.push(new Octant(child, x0, ym, z0, xm, y1, zm));\n      if (child = node[3]) octs.push(new Octant(child, xm, ym, z0, x1, y1, zm));\n      if (child = node[4]) octs.push(new Octant(child, x0, y0, zm, xm, ym, z1));\n      if (child = node[5]) octs.push(new Octant(child, xm, y0, zm, x1, ym, z1));\n      if (child = node[6]) octs.push(new Octant(child, x0, ym, zm, xm, y1, z1));\n      if (child = node[7]) octs.push(new Octant(child, xm, ym, zm, x1, y1, z1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.z0, q.x1, q.y1, q.z1);\n  }\n  return this;\n}\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAEhC,eAAe,UAASC,QAAQ,EAAE;EAChC,IAAIC,IAAI,GAAG,EAAE;IAAEC,IAAI,GAAG,EAAE;IAAEC,CAAC;EAC3B,IAAI,IAAI,CAACC,KAAK,EAAEH,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAAC,IAAI,CAACK,KAAK,EAAE,IAAI,CAACE,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;EAC7G,OAAOR,CAAC,GAAGF,IAAI,CAACW,GAAG,CAAC,CAAC,EAAE;IACrB,IAAIC,IAAI,GAAGV,CAAC,CAACU,IAAI;IACjB,IAAIA,IAAI,CAACC,MAAM,EAAE;MACf,IAAIC,KAAK;QAAEC,EAAE,GAAGb,CAAC,CAACa,EAAE;QAAEC,EAAE,GAAGd,CAAC,CAACc,EAAE;QAAEC,EAAE,GAAGf,CAAC,CAACe,EAAE;QAAEC,EAAE,GAAGhB,CAAC,CAACgB,EAAE;QAAEC,EAAE,GAAGjB,CAAC,CAACiB,EAAE;QAAEC,EAAE,GAAGlB,CAAC,CAACkB,EAAE;QAAEC,EAAE,GAAG,CAACN,EAAE,GAAGG,EAAE,IAAI,CAAC;QAAEI,EAAE,GAAG,CAACN,EAAE,GAAGG,EAAE,IAAI,CAAC;QAAEI,EAAE,GAAG,CAACN,EAAE,GAAGG,EAAE,IAAI,CAAC;MACvI,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEI,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;MACzE,IAAIT,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEO,EAAE,EAAEL,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEI,EAAE,EAAEC,EAAE,CAAC,CAAC;MACzE,IAAIT,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEC,EAAE,EAAEO,EAAE,EAAEL,EAAE,EAAEI,EAAE,EAAEF,EAAE,EAAEI,EAAE,CAAC,CAAC;MACzE,IAAIT,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEO,EAAE,EAAEC,EAAE,EAAEL,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEI,EAAE,CAAC,CAAC;MACzE,IAAIT,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEO,EAAE,EAAEF,EAAE,EAAEC,EAAE,EAAEF,EAAE,CAAC,CAAC;MACzE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEO,EAAE,EAAEL,EAAE,EAAEO,EAAE,EAAEL,EAAE,EAAEI,EAAE,EAAEF,EAAE,CAAC,CAAC;MACzE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEC,EAAE,EAAEO,EAAE,EAAEC,EAAE,EAAEF,EAAE,EAAEF,EAAE,EAAEC,EAAE,CAAC,CAAC;MACzE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEZ,IAAI,CAACI,IAAI,CAAC,IAAIN,MAAM,CAACgB,KAAK,EAAEO,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEL,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC,CAAC;IAC3E;IACAnB,IAAI,CAACG,IAAI,CAACF,CAAC,CAAC;EACd;EACA,OAAOA,CAAC,GAAGD,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE;IACrBZ,QAAQ,CAACG,CAAC,CAACU,IAAI,EAAEV,CAAC,CAACa,EAAE,EAAEb,CAAC,CAACc,EAAE,EAAEd,CAAC,CAACe,EAAE,EAAEf,CAAC,CAACgB,EAAE,EAAEhB,CAAC,CAACiB,EAAE,EAAEjB,CAAC,CAACkB,EAAE,CAAC;EACtD;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}