{"ast": null, "code": "import constant from \"./constant.js\";\nexport default function (radius, x, y, z) {\n  var nodes,\n    nDim,\n    strength = constant(0.1),\n    strengths,\n    radiuses;\n  if (typeof radius !== \"function\") radius = constant(+radius);\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n  if (z == null) z = 0;\n  function force(alpha) {\n    for (var i = 0, n = nodes.length; i < n; ++i) {\n      var node = nodes[i],\n        dx = node.x - x || 1e-6,\n        dy = (node.y || 0) - y || 1e-6,\n        dz = (node.z || 0) - z || 1e-6,\n        r = Math.sqrt(dx * dx + dy * dy + dz * dz),\n        k = (radiuses[i] - r) * strengths[i] * alpha / r;\n      node.vx += dx * k;\n      if (nDim > 1) {\n        node.vy += dy * k;\n      }\n      if (nDim > 2) {\n        node.vz += dz * k;\n      }\n    }\n  }\n  function initialize() {\n    if (!nodes) return;\n    var i,\n      n = nodes.length;\n    strengths = new Array(n);\n    radiuses = new Array(n);\n    for (i = 0; i < n; ++i) {\n      radiuses[i] = +radius(nodes[i], i, nodes);\n      strengths[i] = isNaN(radiuses[i]) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n  force.initialize = function (initNodes, ...args) {\n    nodes = initNodes;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n  force.strength = function (_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n  force.radius = function (_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n  force.x = function (_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n  force.y = function (_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n  force.z = function (_) {\n    return arguments.length ? (z = +_, force) : z;\n  };\n  return force;\n}", "map": {"version": 3, "names": ["constant", "radius", "x", "y", "z", "nodes", "nDim", "strength", "strengths", "radiuses", "force", "alpha", "i", "n", "length", "node", "dx", "dy", "dz", "r", "Math", "sqrt", "k", "vx", "vy", "vz", "initialize", "Array", "isNaN", "initNodes", "args", "find", "arg", "includes", "_", "arguments"], "sources": ["/root/mes-system/mes-frontend/node_modules/d3-force-3d/src/radial.js"], "sourcesContent": ["import constant from \"./constant.js\";\n\nexport default function(radius, x, y, z) {\n  var nodes,\n      nDim,\n      strength = constant(0.1),\n      strengths,\n      radiuses;\n\n  if (typeof radius !== \"function\") radius = constant(+radius);\n  if (x == null) x = 0;\n  if (y == null) y = 0;\n  if (z == null) z = 0;\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length; i < n; ++i) {\n      var node = nodes[i],\n          dx = node.x - x || 1e-6,\n          dy = (node.y || 0) - y || 1e-6,\n          dz = (node.z || 0) - z || 1e-6,\n          r = Math.sqrt(dx * dx + dy * dy + dz * dz),\n          k = (radiuses[i] - r) * strengths[i] * alpha / r;\n      node.vx += dx * k;\n      if (nDim>1) { node.vy += dy * k; }\n      if (nDim>2) { node.vz += dz * k; }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    radiuses = new Array(n);\n    for (i = 0; i < n; ++i) {\n      radiuses[i] = +radius(nodes[i], i, nodes);\n      strengths[i] = isNaN(radiuses[i]) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(initNodes, ...args) {\n    nodes = initNodes;\n    nDim = args.find(arg => [1, 2, 3].includes(arg)) || 2;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = +_, force) : x;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = +_, force) : y;\n  };\n\n  force.z = function(_) {\n    return arguments.length ? (z = +_, force) : z;\n  };\n\n  return force;\n}\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AAEpC,eAAe,UAASC,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACvC,IAAIC,KAAK;IACLC,IAAI;IACJC,QAAQ,GAAGP,QAAQ,CAAC,GAAG,CAAC;IACxBQ,SAAS;IACTC,QAAQ;EAEZ,IAAI,OAAOR,MAAM,KAAK,UAAU,EAAEA,MAAM,GAAGD,QAAQ,CAAC,CAACC,MAAM,CAAC;EAC5D,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EACpB,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EACpB,IAAIC,CAAC,IAAI,IAAI,EAAEA,CAAC,GAAG,CAAC;EAEpB,SAASM,KAAKA,CAACC,KAAK,EAAE;IACpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGR,KAAK,CAACS,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC5C,IAAIG,IAAI,GAAGV,KAAK,CAACO,CAAC,CAAC;QACfI,EAAE,GAAGD,IAAI,CAACb,CAAC,GAAGA,CAAC,IAAI,IAAI;QACvBe,EAAE,GAAG,CAACF,IAAI,CAACZ,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,IAAI;QAC9Be,EAAE,GAAG,CAACH,IAAI,CAACX,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,IAAI;QAC9Be,CAAC,GAAGC,IAAI,CAACC,IAAI,CAACL,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;QAC1CI,CAAC,GAAG,CAACb,QAAQ,CAACG,CAAC,CAAC,GAAGO,CAAC,IAAIX,SAAS,CAACI,CAAC,CAAC,GAAGD,KAAK,GAAGQ,CAAC;MACpDJ,IAAI,CAACQ,EAAE,IAAIP,EAAE,GAAGM,CAAC;MACjB,IAAIhB,IAAI,GAAC,CAAC,EAAE;QAAES,IAAI,CAACS,EAAE,IAAIP,EAAE,GAAGK,CAAC;MAAE;MACjC,IAAIhB,IAAI,GAAC,CAAC,EAAE;QAAES,IAAI,CAACU,EAAE,IAAIP,EAAE,GAAGI,CAAC;MAAE;IACnC;EACF;EAEA,SAASI,UAAUA,CAAA,EAAG;IACpB,IAAI,CAACrB,KAAK,EAAE;IACZ,IAAIO,CAAC;MAAEC,CAAC,GAAGR,KAAK,CAACS,MAAM;IACvBN,SAAS,GAAG,IAAImB,KAAK,CAACd,CAAC,CAAC;IACxBJ,QAAQ,GAAG,IAAIkB,KAAK,CAACd,CAAC,CAAC;IACvB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MACtBH,QAAQ,CAACG,CAAC,CAAC,GAAG,CAACX,MAAM,CAACI,KAAK,CAACO,CAAC,CAAC,EAAEA,CAAC,EAAEP,KAAK,CAAC;MACzCG,SAAS,CAACI,CAAC,CAAC,GAAGgB,KAAK,CAACnB,QAAQ,CAACG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAACL,QAAQ,CAACF,KAAK,CAACO,CAAC,CAAC,EAAEA,CAAC,EAAEP,KAAK,CAAC;IACvE;EACF;EAEAK,KAAK,CAACgB,UAAU,GAAG,UAASG,SAAS,EAAE,GAAGC,IAAI,EAAE;IAC9CzB,KAAK,GAAGwB,SAAS;IACjBvB,IAAI,GAAGwB,IAAI,CAACC,IAAI,CAACC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAC,IAAI,CAAC;IACrDN,UAAU,CAAC,CAAC;EACd,CAAC;EAEDhB,KAAK,CAACH,QAAQ,GAAG,UAAS2B,CAAC,EAAE;IAC3B,OAAOC,SAAS,CAACrB,MAAM,IAAIP,QAAQ,GAAG,OAAO2B,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAER,UAAU,CAAC,CAAC,EAAEhB,KAAK,IAAIH,QAAQ;EACnH,CAAC;EAEDG,KAAK,CAACT,MAAM,GAAG,UAASiC,CAAC,EAAE;IACzB,OAAOC,SAAS,CAACrB,MAAM,IAAIb,MAAM,GAAG,OAAOiC,CAAC,KAAK,UAAU,GAAGA,CAAC,GAAGlC,QAAQ,CAAC,CAACkC,CAAC,CAAC,EAAER,UAAU,CAAC,CAAC,EAAEhB,KAAK,IAAIT,MAAM;EAC/G,CAAC;EAEDS,KAAK,CAACR,CAAC,GAAG,UAASgC,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACrB,MAAM,IAAIZ,CAAC,GAAG,CAACgC,CAAC,EAAExB,KAAK,IAAIR,CAAC;EAC/C,CAAC;EAEDQ,KAAK,CAACP,CAAC,GAAG,UAAS+B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACrB,MAAM,IAAIX,CAAC,GAAG,CAAC+B,CAAC,EAAExB,KAAK,IAAIP,CAAC;EAC/C,CAAC;EAEDO,KAAK,CAACN,CAAC,GAAG,UAAS8B,CAAC,EAAE;IACpB,OAAOC,SAAS,CAACrB,MAAM,IAAIV,CAAC,GAAG,CAAC8B,CAAC,EAAExB,KAAK,IAAIN,CAAC;EAC/C,CAAC;EAED,OAAOM,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}