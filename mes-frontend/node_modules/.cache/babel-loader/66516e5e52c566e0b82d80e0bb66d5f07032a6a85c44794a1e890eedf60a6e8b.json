{"ast": null, "code": "export { Component } from './component';\nexport * from './types';", "map": {"version": 3, "names": ["Component"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/core/index.ts"], "sourcesContent": ["export { Component } from './component';\nexport * from './types';\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AACvC,cAAc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}