{"ast": null, "code": "import { __read } from \"tslib\";\nimport { getItemsBBox } from '../utils';\nimport { BBox } from '../../bbox';\nexport var flex = function (container, children, config) {\n  var width = container.width,\n    height = container.height;\n  var _a = config.flexDirection,\n    flexDirection = _a === void 0 ? 'row' : _a,\n    _b = config.flexWrap,\n    flexWrap = _b === void 0 ? 'nowrap' : _b,\n    _c = config.justifyContent,\n    justifyContent = _c === void 0 ? 'flex-start' : _c,\n    _d = config.alignContent,\n    alignContent = _d === void 0 ? 'flex-start' : _d,\n    _e = config.alignItems,\n    alignItems = _e === void 0 ? 'flex-start' : _e;\n  var isHorizontalFlow = flexDirection === 'row'; // || flexDirection === 'row-reverse';\n  var isLeftToRightFlow = flexDirection === 'row' || flexDirection === 'column';\n  // implement default layout;\n  // flex direction\n  var direction = isHorizontalFlow ? isLeftToRightFlow ? [1, 0] : [-1, 0] : isLeftToRightFlow ? [0, 1] : [0, -1];\n  var _f = __read([0, 0], 2),\n    offsetX = _f[0],\n    offsetY = _f[1];\n  var itemsFromDirection = children.map(function (child) {\n    var _a;\n    var width = child.width,\n      height = child.height;\n    var _b = __read([offsetX, offsetY], 2),\n      x = _b[0],\n      y = _b[1];\n    _a = __read([offsetX + width * direction[0], offsetY + height * direction[1]], 2), offsetX = _a[0], offsetY = _a[1];\n    return new BBox(x, y, width, height);\n  });\n  // flex wrap\n  // todo\n  // justify content\n  // flex-start, flex-end, center\n  var itemsForJustifyContentBBox = getItemsBBox(itemsFromDirection);\n  var justifyContentOffset = {\n    'flex-start': 0,\n    'flex-end': isHorizontalFlow ? width - itemsForJustifyContentBBox.width : height - itemsForJustifyContentBBox.height,\n    center: isHorizontalFlow ? (width - itemsForJustifyContentBBox.width) / 2 : (height - itemsForJustifyContentBBox.height) / 2\n  };\n  var itemsFromJustifyContent = itemsFromDirection.map(function (item) {\n    var x = item.x,\n      y = item.y;\n    var itemBox = BBox.fromRect(item);\n    itemBox.x = isHorizontalFlow ? x + justifyContentOffset[justifyContent] : x;\n    itemBox.y = isHorizontalFlow ? y : y + justifyContentOffset[justifyContent];\n    return itemBox;\n  });\n  // align items\n  // flex-start, flex-end, center\n  var itemsForAlignItemsBBox = getItemsBBox(itemsFromJustifyContent);\n  var calcAlignItemsOffset = function (box) {\n    var _a = __read(isHorizontalFlow ? ['height', height] : ['width', width], 2),\n      key = _a[0],\n      size = _a[1];\n    switch (alignItems) {\n      case 'flex-start':\n        return 0;\n      case 'flex-end':\n        return size - box[key];\n      case 'center':\n        return size / 2 - box[key] / 2;\n      default:\n        return 0;\n    }\n  };\n  var itemsFromAlignItems = itemsFromJustifyContent.map(function (item) {\n    var x = item.x,\n      y = item.y;\n    var itemBox = BBox.fromRect(item);\n    itemBox.x = isHorizontalFlow ? x : x + calcAlignItemsOffset(itemBox);\n    itemBox.y = isHorizontalFlow ? y + calcAlignItemsOffset(itemBox) : y;\n    return itemBox;\n  });\n  var finalItems = itemsFromAlignItems.map(function (item) {\n    var _a, _b;\n    var itemBox = BBox.fromRect(item);\n    itemBox.x += (_a = container.x) !== null && _a !== void 0 ? _a : 0;\n    itemBox.y += (_b = container.y) !== null && _b !== void 0 ? _b : 0;\n    return itemBox;\n  });\n  return finalItems;\n};", "map": {"version": 3, "names": ["getItemsBBox", "BBox", "flex", "container", "children", "config", "width", "height", "_a", "flexDirection", "_b", "flexWrap", "_c", "justifyContent", "_d", "align<PERSON><PERSON><PERSON>", "_e", "alignItems", "isHorizontalFlow", "isLeftToRightFlow", "direction", "_f", "__read", "offsetX", "offsetY", "itemsFromDirection", "map", "child", "x", "y", "itemsForJustifyContentBBox", "justifyContentOffset", "center", "itemsFromJustifyContent", "item", "itemBox", "fromRect", "itemsForAlignItemsBBox", "calcAlignItemsOffset", "box", "key", "size", "itemsFromAlignItems", "finalItems"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/util/layout/flex/index.ts"], "sourcesContent": ["import type { LayoutExecuter } from '../types';\nimport { getItemsBBox } from '../utils';\nimport type { FlexLayoutConfig } from './types';\nimport { BBox } from '../../bbox';\n\nexport const flex: LayoutExecuter<FlexLayoutConfig> = function (container, children, config) {\n  const { width, height } = container;\n  const {\n    flexDirection = 'row',\n    flexWrap = 'nowrap',\n    justifyContent = 'flex-start',\n    alignContent = 'flex-start',\n    alignItems = 'flex-start',\n  } = config;\n\n  const isHorizontalFlow = flexDirection === 'row'; // || flexDirection === 'row-reverse';\n  const isLeftToRightFlow = flexDirection === 'row' || flexDirection === 'column';\n\n  // implement default layout;\n  // flex direction\n  const direction = isHorizontalFlow ? (isLeftToRightFlow ? [1, 0] : [-1, 0]) : isLeftToRightFlow ? [0, 1] : [0, -1];\n\n  let [offsetX, offsetY] = [0, 0];\n  const itemsFromDirection = children.map((child) => {\n    const { width, height } = child;\n    const [x, y] = [offsetX, offsetY];\n    [offsetX, offsetY] = [offsetX + width * direction[0], offsetY + height * direction[1]];\n    return new BBox(x, y, width, height);\n  });\n\n  // flex wrap\n  // todo\n\n  // justify content\n  // flex-start, flex-end, center\n  const itemsForJustifyContentBBox = getItemsBBox(itemsFromDirection);\n  const justifyContentOffset = {\n    'flex-start': 0,\n    'flex-end': isHorizontalFlow\n      ? width - itemsForJustifyContentBBox.width\n      : height - itemsForJustifyContentBBox.height,\n    center: isHorizontalFlow\n      ? (width - itemsForJustifyContentBBox.width) / 2\n      : (height - itemsForJustifyContentBBox.height) / 2,\n  };\n  const itemsFromJustifyContent = itemsFromDirection.map((item) => {\n    const { x, y } = item;\n    const itemBox = BBox.fromRect(item);\n    itemBox.x = isHorizontalFlow ? x + justifyContentOffset[justifyContent] : x;\n    itemBox.y = isHorizontalFlow ? y : y + justifyContentOffset[justifyContent];\n    return itemBox;\n  });\n\n  // align items\n  // flex-start, flex-end, center\n  const itemsForAlignItemsBBox = getItemsBBox(itemsFromJustifyContent);\n\n  const calcAlignItemsOffset = (box: DOMRect) => {\n    const [key, size] = isHorizontalFlow ? ['height', height] : ['width', width];\n\n    switch (alignItems) {\n      case 'flex-start':\n        return 0;\n      case 'flex-end':\n        return size - box[key as 'width' | 'height'];\n      case 'center':\n        return size / 2 - box[key as 'width' | 'height'] / 2;\n      default:\n        return 0;\n    }\n  };\n\n  const itemsFromAlignItems = itemsFromJustifyContent.map((item) => {\n    const { x, y } = item;\n    const itemBox = BBox.fromRect(item);\n    itemBox.x = isHorizontalFlow ? x : x + calcAlignItemsOffset(itemBox);\n    itemBox.y = isHorizontalFlow ? y + calcAlignItemsOffset(itemBox) : y;\n    return itemBox;\n  });\n\n  const finalItems = itemsFromAlignItems.map((item) => {\n    const itemBox = BBox.fromRect(item);\n    itemBox.x += container.x ?? 0;\n    itemBox.y += container.y ?? 0;\n    return itemBox;\n  });\n\n  return finalItems;\n};\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,UAAU;AAEvC,SAASC,IAAI,QAAQ,YAAY;AAEjC,OAAO,IAAMC,IAAI,GAAqC,SAAAA,CAAUC,SAAS,EAAEC,QAAQ,EAAEC,MAAM;EACjF,IAAAC,KAAK,GAAaH,SAAS,CAAAG,KAAtB;IAAEC,MAAM,GAAKJ,SAAS,CAAAI,MAAd;EAEnB,IAAAC,EAAA,GAKEH,MAAM,CAAAI,aALa;IAArBA,aAAa,GAAAD,EAAA,cAAG,KAAK,GAAAA,EAAA;IACrBE,EAAA,GAIEL,MAAM,CAAAM,QAJW;IAAnBA,QAAQ,GAAAD,EAAA,cAAG,QAAQ,GAAAA,EAAA;IACnBE,EAAA,GAGEP,MAAM,CAAAQ,cAHqB;IAA7BA,cAAc,GAAAD,EAAA,cAAG,YAAY,GAAAA,EAAA;IAC7BE,EAAA,GAEET,MAAM,CAAAU,YAFmB;IAA3BA,YAAY,GAAAD,EAAA,cAAG,YAAY,GAAAA,EAAA;IAC3BE,EAAA,GACEX,MAAM,CAAAY,UADiB;IAAzBA,UAAU,GAAAD,EAAA,cAAG,YAAY,GAAAA,EAAA;EAG3B,IAAME,gBAAgB,GAAGT,aAAa,KAAK,KAAK,CAAC,CAAC;EAClD,IAAMU,iBAAiB,GAAGV,aAAa,KAAK,KAAK,IAAIA,aAAa,KAAK,QAAQ;EAE/E;EACA;EACA,IAAMW,SAAS,GAAGF,gBAAgB,GAAIC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAIA,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EAE9G,IAAAE,EAAA,GAAAC,MAAA,CAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;IAA1BC,OAAO,GAAAF,EAAA;IAAEG,OAAO,GAAAH,EAAA,GAAU;EAC/B,IAAMI,kBAAkB,GAAGrB,QAAQ,CAACsB,GAAG,CAAC,UAACC,KAAK;;IACpC,IAAArB,KAAK,GAAaqB,KAAK,CAAArB,KAAlB;MAAEC,MAAM,GAAKoB,KAAK,CAAApB,MAAV;IACf,IAAAG,EAAA,GAAAY,MAAA,CAAS,CAACC,OAAO,EAAEC,OAAO,CAAC;MAA1BI,CAAC,GAAAlB,EAAA;MAAEmB,CAAC,GAAAnB,EAAA,GAAsB;IACjCF,EAAA,GAAAc,MAAA,CAAqB,CAACC,OAAO,GAAGjB,KAAK,GAAGc,SAAS,CAAC,CAAC,CAAC,EAAEI,OAAO,GAAGjB,MAAM,GAAGa,SAAS,CAAC,CAAC,CAAC,CAAC,MAArFG,OAAO,GAAAf,EAAA,KAAEgB,OAAO,GAAAhB,EAAA;IACjB,OAAO,IAAIP,IAAI,CAAC2B,CAAC,EAAEC,CAAC,EAAEvB,KAAK,EAAEC,MAAM,CAAC;EACtC,CAAC,CAAC;EAEF;EACA;EAEA;EACA;EACA,IAAMuB,0BAA0B,GAAG9B,YAAY,CAACyB,kBAAkB,CAAC;EACnE,IAAMM,oBAAoB,GAAG;IAC3B,YAAY,EAAE,CAAC;IACf,UAAU,EAAEb,gBAAgB,GACxBZ,KAAK,GAAGwB,0BAA0B,CAACxB,KAAK,GACxCC,MAAM,GAAGuB,0BAA0B,CAACvB,MAAM;IAC9CyB,MAAM,EAAEd,gBAAgB,GACpB,CAACZ,KAAK,GAAGwB,0BAA0B,CAACxB,KAAK,IAAI,CAAC,GAC9C,CAACC,MAAM,GAAGuB,0BAA0B,CAACvB,MAAM,IAAI;GACpD;EACD,IAAM0B,uBAAuB,GAAGR,kBAAkB,CAACC,GAAG,CAAC,UAACQ,IAAI;IAClD,IAAAN,CAAC,GAAQM,IAAI,CAAAN,CAAZ;MAAEC,CAAC,GAAKK,IAAI,CAAAL,CAAT;IACZ,IAAMM,OAAO,GAAGlC,IAAI,CAACmC,QAAQ,CAACF,IAAI,CAAC;IACnCC,OAAO,CAACP,CAAC,GAAGV,gBAAgB,GAAGU,CAAC,GAAGG,oBAAoB,CAAClB,cAAc,CAAC,GAAGe,CAAC;IAC3EO,OAAO,CAACN,CAAC,GAAGX,gBAAgB,GAAGW,CAAC,GAAGA,CAAC,GAAGE,oBAAoB,CAAClB,cAAc,CAAC;IAC3E,OAAOsB,OAAO;EAChB,CAAC,CAAC;EAEF;EACA;EACA,IAAME,sBAAsB,GAAGrC,YAAY,CAACiC,uBAAuB,CAAC;EAEpE,IAAMK,oBAAoB,GAAG,SAAAA,CAACC,GAAY;IAClC,IAAA/B,EAAA,GAAAc,MAAA,CAAcJ,gBAAgB,GAAG,CAAC,QAAQ,EAAEX,MAAM,CAAC,GAAG,CAAC,OAAO,EAAED,KAAK,CAAC;MAArEkC,GAAG,GAAAhC,EAAA;MAAEiC,IAAI,GAAAjC,EAAA,GAA4D;IAE5E,QAAQS,UAAU;MAChB,KAAK,YAAY;QACf,OAAO,CAAC;MACV,KAAK,UAAU;QACb,OAAOwB,IAAI,GAAGF,GAAG,CAACC,GAAyB,CAAC;MAC9C,KAAK,QAAQ;QACX,OAAOC,IAAI,GAAG,CAAC,GAAGF,GAAG,CAACC,GAAyB,CAAC,GAAG,CAAC;MACtD;QACE,OAAO,CAAC;IACZ;EACF,CAAC;EAED,IAAME,mBAAmB,GAAGT,uBAAuB,CAACP,GAAG,CAAC,UAACQ,IAAI;IACnD,IAAAN,CAAC,GAAQM,IAAI,CAAAN,CAAZ;MAAEC,CAAC,GAAKK,IAAI,CAAAL,CAAT;IACZ,IAAMM,OAAO,GAAGlC,IAAI,CAACmC,QAAQ,CAACF,IAAI,CAAC;IACnCC,OAAO,CAACP,CAAC,GAAGV,gBAAgB,GAAGU,CAAC,GAAGA,CAAC,GAAGU,oBAAoB,CAACH,OAAO,CAAC;IACpEA,OAAO,CAACN,CAAC,GAAGX,gBAAgB,GAAGW,CAAC,GAAGS,oBAAoB,CAACH,OAAO,CAAC,GAAGN,CAAC;IACpE,OAAOM,OAAO;EAChB,CAAC,CAAC;EAEF,IAAMQ,UAAU,GAAGD,mBAAmB,CAAChB,GAAG,CAAC,UAACQ,IAAI;;IAC9C,IAAMC,OAAO,GAAGlC,IAAI,CAACmC,QAAQ,CAACF,IAAI,CAAC;IACnCC,OAAO,CAACP,CAAC,IAAI,CAAApB,EAAA,GAAAL,SAAS,CAACyB,CAAC,cAAApB,EAAA,cAAAA,EAAA,GAAI,CAAC;IAC7B2B,OAAO,CAACN,CAAC,IAAI,CAAAnB,EAAA,GAAAP,SAAS,CAAC0B,CAAC,cAAAnB,EAAA,cAAAA,EAAA,GAAI,CAAC;IAC7B,OAAOyB,OAAO;EAChB,CAAC,CAAC;EAEF,OAAOQ,UAAU;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}