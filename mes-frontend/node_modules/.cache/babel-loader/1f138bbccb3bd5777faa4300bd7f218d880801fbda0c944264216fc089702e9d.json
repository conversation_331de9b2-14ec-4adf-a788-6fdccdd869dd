{"ast": null, "code": "import { __assign, __extends, __read } from \"tslib\";\nimport { Component } from '../../core';\nimport { Group, Rect } from '../../shapes';\nimport { parseSeriesAttr, renderExtDo, select, subStyleProps } from '../../util';\nimport { deepAssign } from '../../util/deep-assign';\nvar Option = /** @class */function (_super) {\n  __extends(Option, _super);\n  function Option(options) {\n    var _this = _super.call(this, deepAssign({}, Option.defaultOptions, options)) || this;\n    _this.hoverColor = '#f5f5f5';\n    _this.selectedColor = '#e6f7ff';\n    _this.background = _this.appendChild(new Rect({}));\n    _this.label = _this.background.appendChild(new Group({}));\n    return _this;\n  }\n  Object.defineProperty(Option.prototype, \"padding\", {\n    get: function () {\n      return parseSeriesAttr(this.style.padding);\n    },\n    enumerable: false,\n    configurable: true\n  });\n  Option.prototype.renderLabel = function () {\n    var _a = this.style,\n      label = _a.label,\n      value = _a.value;\n    var labelStyle = subStyleProps(this.attributes, 'label');\n    select(this.label).maybeAppend('.label', function () {\n      return renderExtDo(label);\n    }).attr('className', 'label').styles(labelStyle);\n    // bind data to label\n    this.label.attr('__data__', value);\n  };\n  Option.prototype.renderBackground = function () {\n    var labelBBox = this.label.getBBox();\n    var _a = __read(this.padding, 4),\n      top = _a[0],\n      right = _a[1],\n      bottom = _a[2],\n      left = _a[3];\n    var labelWidth = labelBBox.width,\n      labelHeight = labelBBox.height;\n    var backgroundWidth = labelWidth + left + right;\n    var backgroundHeight = labelHeight + top + bottom;\n    var backgroundStyle = subStyleProps(this.attributes, 'background');\n    var _b = this.style,\n      _c = _b.width,\n      styleWidth = _c === void 0 ? 0 : _c,\n      _d = _b.height,\n      styleHeight = _d === void 0 ? 0 : _d,\n      selected = _b.selected;\n    this.background.attr(__assign(__assign({}, backgroundStyle), {\n      width: Math.max(backgroundWidth, styleWidth),\n      height: Math.max(backgroundHeight, styleHeight),\n      fill: selected ? this.selectedColor : '#fff'\n    }));\n    // place label\n    this.label.attr({\n      transform: \"translate(\".concat(left, \", \").concat((backgroundHeight - labelHeight) / 2, \")\")\n    });\n  };\n  Option.prototype.render = function () {\n    this.renderLabel();\n    this.renderBackground();\n  };\n  Option.prototype.bindEvents = function () {\n    var _this = this;\n    this.addEventListener('pointerenter', function () {\n      if (_this.style.selected) return;\n      _this.background.attr('fill', _this.hoverColor);\n    });\n    this.addEventListener('pointerleave', function () {\n      if (_this.style.selected) return;\n      _this.background.attr('fill', _this.style.backgroundFill);\n    });\n    var item = this;\n    this.addEventListener('click', function () {\n      var _a = _this.style,\n        label = _a.label,\n        value = _a.value,\n        onClick = _a.onClick;\n      onClick === null || onClick === void 0 ? void 0 : onClick(value, {\n        label: label,\n        value: value\n      }, item);\n    });\n  };\n  Option.defaultOptions = {\n    style: {\n      value: '',\n      label: '',\n      cursor: 'pointer'\n    }\n  };\n  return Option;\n}(Component);\nexport { Option };", "map": {"version": 3, "names": ["Component", "Group", "Rect", "parseSeriesAttr", "renderExtDo", "select", "subStyleProps", "deepAssign", "Option", "_super", "__extends", "options", "_this", "call", "defaultOptions", "hoverColor", "selectedColor", "background", "append<PERSON><PERSON><PERSON>", "label", "Object", "defineProperty", "prototype", "get", "style", "padding", "renderLabel", "_a", "value", "labelStyle", "attributes", "maybe<PERSON><PERSON>nd", "attr", "styles", "renderBackground", "labelBBox", "getBBox", "__read", "top", "right", "bottom", "left", "labelWidth", "width", "labelHeight", "height", "backgroundWidth", "backgroundHeight", "backgroundStyle", "_b", "_c", "styleWidth", "_d", "styleHeight", "selected", "__assign", "Math", "max", "fill", "transform", "concat", "render", "bindEvents", "addEventListener", "backgroundFill", "item", "onClick", "cursor"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/component/src/ui/select/option.ts"], "sourcesContent": ["import { Component } from '../../core';\nimport { Group, Rect } from '../../shapes';\nimport { parseSeriesAttr, renderExtDo, select, subStyleProps } from '../../util';\nimport { deepAssign } from '../../util/deep-assign';\nimport type { OptionOptions, OptionStyleProps } from './types';\n\nexport class Option extends Component<OptionStyleProps> {\n  static defaultOptions: OptionOptions = {\n    style: {\n      value: '',\n      label: '',\n      cursor: 'pointer',\n    },\n  };\n\n  private hoverColor = '#f5f5f5';\n\n  private selectedColor = '#e6f7ff';\n\n  private background = this.appendChild(new Rect({}));\n\n  private label = this.background.appendChild(new Group({}));\n\n  private get padding() {\n    return parseSeriesAttr(this.style.padding);\n  }\n\n  private renderLabel() {\n    const { label, value } = this.style;\n    const labelStyle = subStyleProps(this.attributes, 'label');\n    select(this.label)\n      .maybeAppend('.label', () => renderExtDo(label))\n      .attr('className', 'label')\n      .styles(labelStyle);\n    // bind data to label\n    this.label.attr('__data__', value);\n  }\n\n  private renderBackground() {\n    const labelBBox = this.label.getBBox();\n    const [top, right, bottom, left] = this.padding;\n    const { width: labelWidth, height: labelHeight } = labelBBox;\n    const backgroundWidth = labelWidth + left + right;\n    const backgroundHeight = labelHeight + top + bottom;\n    const backgroundStyle = subStyleProps(this.attributes, 'background');\n    const { width: styleWidth = 0, height: styleHeight = 0, selected } = this.style;\n\n    this.background.attr({\n      ...backgroundStyle,\n      width: Math.max(backgroundWidth, styleWidth),\n      height: Math.max(backgroundHeight, styleHeight),\n      fill: selected ? this.selectedColor : '#fff',\n    });\n    // place label\n    this.label.attr({ transform: `translate(${left}, ${(backgroundHeight - labelHeight) / 2})` });\n  }\n\n  constructor(options: OptionOptions) {\n    super(deepAssign({}, Option.defaultOptions, options));\n  }\n\n  render() {\n    this.renderLabel();\n    this.renderBackground();\n  }\n\n  public bindEvents() {\n    this.addEventListener('pointerenter', () => {\n      if (this.style.selected) return;\n      this.background.attr('fill', this.hoverColor);\n    });\n    this.addEventListener('pointerleave', () => {\n      if (this.style.selected) return;\n      this.background.attr('fill', this.style.backgroundFill);\n    });\n\n    const item = this as unknown as typeof Option;\n    this.addEventListener('click', () => {\n      const { label, value, onClick } = this.style;\n      onClick?.(value, { label, value }, item);\n    });\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,SAASC,KAAK,EAAEC,IAAI,QAAQ,cAAc;AAC1C,SAASC,eAAe,EAAEC,WAAW,EAAEC,MAAM,EAAEC,aAAa,QAAQ,YAAY;AAChF,SAASC,UAAU,QAAQ,wBAAwB;AAGnD,IAAAC,MAAA,0BAAAC,MAAA;EAA4BC,SAAA,CAAAF,MAAA,EAAAC,MAAA;EAmD1B,SAAAD,OAAYG,OAAsB;IAChC,IAAAC,KAAA,GAAAH,MAAK,CAAAI,IAAA,OAACN,UAAU,CAAC,EAAE,EAAEC,MAAM,CAACM,cAAc,EAAEH,OAAO,CAAC,CAAC;IA3C/CC,KAAA,CAAAG,UAAU,GAAG,SAAS;IAEtBH,KAAA,CAAAI,aAAa,GAAG,SAAS;IAEzBJ,KAAA,CAAAK,UAAU,GAAGL,KAAI,CAACM,WAAW,CAAC,IAAIhB,IAAI,CAAC,EAAE,CAAC,CAAC;IAE3CU,KAAA,CAAAO,KAAK,GAAGP,KAAI,CAACK,UAAU,CAACC,WAAW,CAAC,IAAIjB,KAAK,CAAC,EAAE,CAAC,CAAC;;EAsC1D;EApCAmB,MAAA,CAAAC,cAAA,CAAYb,MAAA,CAAAc,SAAA,WAAO;SAAnB,SAAAC,CAAA;MACE,OAAOpB,eAAe,CAAC,IAAI,CAACqB,KAAK,CAACC,OAAO,CAAC;IAC5C,CAAC;;;;EAEOjB,MAAA,CAAAc,SAAA,CAAAI,WAAW,GAAnB;IACQ,IAAAC,EAAA,GAAmB,IAAI,CAACH,KAAK;MAA3BL,KAAK,GAAAQ,EAAA,CAAAR,KAAA;MAAES,KAAK,GAAAD,EAAA,CAAAC,KAAe;IACnC,IAAMC,UAAU,GAAGvB,aAAa,CAAC,IAAI,CAACwB,UAAU,EAAE,OAAO,CAAC;IAC1DzB,MAAM,CAAC,IAAI,CAACc,KAAK,CAAC,CACfY,WAAW,CAAC,QAAQ,EAAE;MAAM,OAAA3B,WAAW,CAACe,KAAK,CAAC;IAAlB,CAAkB,CAAC,CAC/Ca,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAC1BC,MAAM,CAACJ,UAAU,CAAC;IACrB;IACA,IAAI,CAACV,KAAK,CAACa,IAAI,CAAC,UAAU,EAAEJ,KAAK,CAAC;EACpC,CAAC;EAEOpB,MAAA,CAAAc,SAAA,CAAAY,gBAAgB,GAAxB;IACE,IAAMC,SAAS,GAAG,IAAI,CAAChB,KAAK,CAACiB,OAAO,EAAE;IAChC,IAAAT,EAAA,GAAAU,MAAA,CAA6B,IAAI,CAACZ,OAAO;MAAxCa,GAAG,GAAAX,EAAA;MAAEY,KAAK,GAAAZ,EAAA;MAAEa,MAAM,GAAAb,EAAA;MAAEc,IAAI,GAAAd,EAAA,GAAgB;IACvC,IAAOe,UAAU,GAA0BP,SAAS,CAAAQ,KAAnC;MAAUC,WAAW,GAAKT,SAAS,CAAAU,MAAd;IAC9C,IAAMC,eAAe,GAAGJ,UAAU,GAAGD,IAAI,GAAGF,KAAK;IACjD,IAAMQ,gBAAgB,GAAGH,WAAW,GAAGN,GAAG,GAAGE,MAAM;IACnD,IAAMQ,eAAe,GAAG1C,aAAa,CAAC,IAAI,CAACwB,UAAU,EAAE,YAAY,CAAC;IAC9D,IAAAmB,EAAA,GAA+D,IAAI,CAACzB,KAAK;MAAvE0B,EAAA,GAAAD,EAAA,CAAAN,KAAqB;MAAdQ,UAAU,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;MAAEE,EAAA,GAAAH,EAAA,CAAAJ,MAAuB;MAAfQ,WAAW,GAAAD,EAAA,cAAG,CAAC,GAAAA,EAAA;MAAEE,QAAQ,GAAAL,EAAA,CAAAK,QAAe;IAE/E,IAAI,CAACrC,UAAU,CAACe,IAAI,CAAAuB,QAAA,CAAAA,QAAA,KACfP,eAAe;MAClBL,KAAK,EAAEa,IAAI,CAACC,GAAG,CAACX,eAAe,EAAEK,UAAU,CAAC;MAC5CN,MAAM,EAAEW,IAAI,CAACC,GAAG,CAACV,gBAAgB,EAAEM,WAAW,CAAC;MAC/CK,IAAI,EAAEJ,QAAQ,GAAG,IAAI,CAACtC,aAAa,GAAG;IAAM,GAC5C;IACF;IACA,IAAI,CAACG,KAAK,CAACa,IAAI,CAAC;MAAE2B,SAAS,EAAE,aAAAC,MAAA,CAAanB,IAAI,QAAAmB,MAAA,CAAK,CAACb,gBAAgB,GAAGH,WAAW,IAAI,CAAC;IAAG,CAAE,CAAC;EAC/F,CAAC;EAMDpC,MAAA,CAAAc,SAAA,CAAAuC,MAAM,GAAN;IACE,IAAI,CAACnC,WAAW,EAAE;IAClB,IAAI,CAACQ,gBAAgB,EAAE;EACzB,CAAC;EAEM1B,MAAA,CAAAc,SAAA,CAAAwC,UAAU,GAAjB;IAAA,IAAAlD,KAAA;IACE,IAAI,CAACmD,gBAAgB,CAAC,cAAc,EAAE;MACpC,IAAInD,KAAI,CAACY,KAAK,CAAC8B,QAAQ,EAAE;MACzB1C,KAAI,CAACK,UAAU,CAACe,IAAI,CAAC,MAAM,EAAEpB,KAAI,CAACG,UAAU,CAAC;IAC/C,CAAC,CAAC;IACF,IAAI,CAACgD,gBAAgB,CAAC,cAAc,EAAE;MACpC,IAAInD,KAAI,CAACY,KAAK,CAAC8B,QAAQ,EAAE;MACzB1C,KAAI,CAACK,UAAU,CAACe,IAAI,CAAC,MAAM,EAAEpB,KAAI,CAACY,KAAK,CAACwC,cAAc,CAAC;IACzD,CAAC,CAAC;IAEF,IAAMC,IAAI,GAAG,IAAgC;IAC7C,IAAI,CAACF,gBAAgB,CAAC,OAAO,EAAE;MACvB,IAAApC,EAAA,GAA4Bf,KAAI,CAACY,KAAK;QAApCL,KAAK,GAAAQ,EAAA,CAAAR,KAAA;QAAES,KAAK,GAAAD,EAAA,CAAAC,KAAA;QAAEsC,OAAO,GAAAvC,EAAA,CAAAuC,OAAe;MAC5CA,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGtC,KAAK,EAAE;QAAET,KAAK,EAAAA,KAAA;QAAES,KAAK,EAAAA;MAAA,CAAE,EAAEqC,IAAI,CAAC;IAC1C,CAAC,CAAC;EACJ,CAAC;EA1EMzD,MAAA,CAAAM,cAAc,GAAkB;IACrCU,KAAK,EAAE;MACLI,KAAK,EAAE,EAAE;MACTT,KAAK,EAAE,EAAE;MACTgD,MAAM,EAAE;;GAEX;EAqEH,OAAA3D,MAAC;CAAA,CA5E2BR,SAAS;SAAxBQ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}