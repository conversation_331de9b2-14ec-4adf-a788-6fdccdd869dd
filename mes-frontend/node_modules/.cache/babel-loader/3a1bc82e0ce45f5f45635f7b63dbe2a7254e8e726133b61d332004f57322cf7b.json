{"ast": null, "code": "import isArray from './is-array';\nimport isFunction from './is-function';\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction groupBy(data, condition) {\n  if (!condition || !isArray(data)) {\n    return {};\n  }\n  var result = {};\n  // 兼容方法和 字符串的写法\n  var predicate = isFunction(condition) ? condition : function (item) {\n    return item[condition];\n  };\n  var key;\n  for (var i = 0; i < data.length; i++) {\n    var item = data[i];\n    key = predicate(item);\n    if (hasOwnProperty.call(result, key)) {\n      result[key].push(item);\n    } else {\n      result[key] = [item];\n    }\n  }\n  return result;\n}\nexport default groupBy;", "map": {"version": 3, "names": ["isArray", "isFunction", "hasOwnProperty", "Object", "prototype", "groupBy", "data", "condition", "result", "predicate", "item", "key", "i", "length", "call", "push"], "sources": ["/root/mes-system/mes-frontend/node_modules/@antv/algorithm/node_modules/@antv/util/src/group-by.ts"], "sourcesContent": ["import isArray from './is-array';\nimport isFunction from './is-function';\n\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\n\nexport interface ObjectType<T> {\n  [key: string]: T[];\n}\n\nfunction groupBy<T>(data: T[], condition: (item: T) => string): ObjectType<T>;\nfunction groupBy<T>(data: T[], condition: string): ObjectType<T>;\nfunction groupBy<T>(data: T[], condition: ((item: T) => string) | string): ObjectType<T> {\n  if (!condition || !isArray(data)) {\n    return {};\n  }\n  const result: ObjectType<T> = {};\n\n  // 兼容方法和 字符串的写法\n  const predicate = isFunction(condition) ? condition : (item) => item[condition];\n\n  let key: string;\n  for (let i = 0; i < data.length; i++) {\n    const item = data[i];\n    key = predicate(item);\n    if (hasOwnProperty.call(result, key)) {\n      result[key].push(item);\n    } else {\n      result[key] = [ item ];\n    }\n  }\n\n  return result;\n}\n\nexport default groupBy;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,YAAY;AAChC,OAAOC,UAAU,MAAM,eAAe;AAEtC,IAAMC,cAAc,GAAGC,MAAM,CAACC,SAAS,CAACF,cAAc;AAQtD,SAASG,OAAOA,CAAIC,IAAS,EAAEC,SAAyC;EACtE,IAAI,CAACA,SAAS,IAAI,CAACP,OAAO,CAACM,IAAI,CAAC,EAAE;IAChC,OAAO,EAAE;;EAEX,IAAME,MAAM,GAAkB,EAAE;EAEhC;EACA,IAAMC,SAAS,GAAGR,UAAU,CAACM,SAAS,CAAC,GAAGA,SAAS,GAAG,UAACG,IAAI;IAAK,OAAAA,IAAI,CAACH,SAAS,CAAC;EAAf,CAAe;EAE/E,IAAII,GAAW;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,IAAI,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;IACpC,IAAMF,IAAI,GAAGJ,IAAI,CAACM,CAAC,CAAC;IACpBD,GAAG,GAAGF,SAAS,CAACC,IAAI,CAAC;IACrB,IAAIR,cAAc,CAACY,IAAI,CAACN,MAAM,EAAEG,GAAG,CAAC,EAAE;MACpCH,MAAM,CAACG,GAAG,CAAC,CAACI,IAAI,CAACL,IAAI,CAAC;KACvB,MAAM;MACLF,MAAM,CAACG,GAAG,CAAC,GAAG,CAAED,IAAI,CAAE;;;EAI1B,OAAOF,MAAM;AACf;AAEA,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}