{"ast": null, "code": "var __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport { deepMix, pick, get } from \"@antv/util\";\nimport { partition, CHILD_NODE_COUNT } from \"../utils/hierarchy/partition\";\nexport const SUNBURST_TYPE = \"sunburst\";\nexport const SUNBURST_TYPE_FIELD = \"markType\";\nexport const SUNBURST_Y_FIELD = \"value\";\nexport const SUNBURST_PATH_FIELD = \"path\";\nexport const SUNBURST_ANCESTOR_FIELD = \"ancestor-node\";\n/**\n * Sunburst TransformData\n * @param options\n */\nexport function transformData(options) {\n  const {\n    data,\n    encode\n  } = options;\n  const {\n    color,\n    value\n  } = encode;\n  const type = \"partition\";\n  const nodes = partition(data, {\n    field: value,\n    // @ts-ignore\n    type: `hierarchy.${type}`,\n    as: [\"x\", \"y\"]\n  });\n  const result = [];\n  nodes.forEach(node => {\n    var _a, _b, _c, _d;\n    if (node.depth === 0) {\n      return null;\n    }\n    let path = node.data.name;\n    const pathList = [path];\n    let ancestorNode = Object.assign({}, node);\n    while (ancestorNode.depth > 1) {\n      path = `${(_a = ancestorNode.parent.data) === null || _a === void 0 ? void 0 : _a.name} / ${path}`;\n      pathList.unshift((_b = ancestorNode.parent.data) === null || _b === void 0 ? void 0 : _b.name);\n      ancestorNode = ancestorNode.parent;\n    }\n    const nodeInfo = Object.assign(Object.assign(Object.assign({}, pick(node.data, [value])), {\n      [SUNBURST_PATH_FIELD]: path,\n      [SUNBURST_ANCESTOR_FIELD]: ancestorNode.data.name\n    }), node);\n    if (color && color !== SUNBURST_ANCESTOR_FIELD) {\n      nodeInfo[color] = node.data[color] || ((_d = (_c = node.parent) === null || _c === void 0 ? void 0 : _c.data) === null || _d === void 0 ? void 0 : _d[color]);\n    }\n    result.push(nodeInfo);\n  });\n  return result.map(d => {\n    const x = d.x.slice(0, 2);\n    const y = [d.y[2], d.y[0]];\n    // 当出现 0 数值时，x 会出现相等的情况，导致渲染成的图形异常。\n    if (x[0] === x[1]) {\n      y[0] = y[1] = (d.y[2] + d.y[0]) / 2;\n    }\n    return Object.assign(Object.assign({}, d), {\n      x,\n      y,\n      fillOpacity: Math.pow(0.85, d.depth)\n    });\n  });\n}\nconst DEFAULT_OPTIONS = {\n  id: SUNBURST_TYPE,\n  encode: {\n    x: \"x\",\n    y: \"y\",\n    key: SUNBURST_PATH_FIELD,\n    color: SUNBURST_ANCESTOR_FIELD,\n    value: \"value\"\n  },\n  axis: {\n    x: false,\n    y: false\n  },\n  style: {\n    [SUNBURST_TYPE_FIELD]: SUNBURST_TYPE,\n    stroke: \"#fff\",\n    lineWidth: 0.5,\n    fillOpacity: \"fillOpacity\",\n    [CHILD_NODE_COUNT]: CHILD_NODE_COUNT,\n    depth: \"depth\"\n  },\n  state: {\n    active: {\n      zIndex: 2,\n      stroke: \"#000\"\n    },\n    inactive: {\n      zIndex: 1,\n      stroke: \"#fff\"\n    }\n  },\n  legend: false,\n  interaction: {\n    drillDown: true\n  },\n  coordinate: {\n    type: \"polar\",\n    innerRadius: 0.2\n  }\n};\nexport const Sunburst = options => {\n  const {\n      encode: encodeOption,\n      data = []\n    } = options,\n    resOptions = __rest(options, [\"encode\", \"data\"]);\n  const coordinate = Object.assign(Object.assign({}, resOptions.coordinate), {\n    // Reac Bug InnerRadius = 0.\n    innerRadius: Math.max(get(resOptions, [\"coordinate\", \"innerRadius\"], 0.2), 0.00001)\n  });\n  const encode = Object.assign(Object.assign({}, DEFAULT_OPTIONS.encode), encodeOption);\n  const {\n    value\n  } = encode;\n  const rectData = transformData({\n    encode,\n    data\n  });\n  return [deepMix({}, DEFAULT_OPTIONS, Object.assign(Object.assign({\n    type: \"rect\",\n    data: rectData,\n    encode,\n    tooltip: {\n      title: \"path\",\n      items: [d => {\n        return {\n          name: value,\n          value: d[value]\n        };\n      }]\n    }\n  }, resOptions), {\n    coordinate\n  }))];\n};\nSunburst.props = {};", "map": {"version": 3, "names": ["deepMix", "pick", "get", "partition", "CHILD_NODE_COUNT", "SUNBURST_TYPE", "SUNBURST_TYPE_FIELD", "SUNBURST_Y_FIELD", "SUNBURST_PATH_FIELD", "SUNBURST_ANCESTOR_FIELD", "transformData", "options", "data", "encode", "color", "value", "type", "nodes", "field", "as", "result", "for<PERSON>ach", "node", "depth", "path", "name", "pathList", "ancestorNode", "Object", "assign", "_a", "parent", "unshift", "_b", "nodeInfo", "_d", "_c", "push", "map", "d", "x", "slice", "y", "fillOpacity", "Math", "pow", "DEFAULT_OPTIONS", "id", "key", "axis", "style", "stroke", "lineWidth", "state", "active", "zIndex", "inactive", "legend", "interaction", "drillDown", "coordinate", "innerRadius", "Sunburst", "encodeOption", "resOptions", "__rest", "max", "rectData", "tooltip", "title", "items", "props"], "sources": ["mark/sunburst.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;AAAA,SAASA,OAAO,EAAEC,IAAI,EAAEC,GAAG,QAAQ,YAAY;AAC/C,SAASC,SAAS,EAAEC,gBAAgB,QAAQ,8BAA8B;AAa1E,OAAO,MAAMC,aAAa,GAAG,UAAU;AACvC,OAAO,MAAMC,mBAAmB,GAAG,UAAU;AAC7C,OAAO,MAAMC,gBAAgB,GAAG,OAAO;AACvC,OAAO,MAAMC,mBAAmB,GAAG,MAAM;AACzC,OAAO,MAAMC,uBAAuB,GAAG,eAAe;AAEtD;;;;AAIA,OAAM,SAAUC,aAAaA,CAACC,OAAiD;EAC7E,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAE,GAAGF,OAAO;EAChC,MAAM;IAAEG,KAAK;IAAEC;EAAK,CAAE,GAAGF,MAAM;EAE/B,MAAMG,IAAI,GAAG,WAAW;EAExB,MAAMC,KAAK,GAAGd,SAAS,CAACS,IAAI,EAAE;IAC5BM,KAAK,EAAEH,KAAK;IACZ;IACAC,IAAI,EAAE,aAAaA,IAAI,EAAE;IACzBG,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG;GACd,CAAC;EAEF,MAAMC,MAAM,GAAG,EAAE;EAEjBH,KAAK,CAACI,OAAO,CAAEC,IAAI,IAAI;;IACrB,IAAIA,IAAI,CAACC,KAAK,KAAK,CAAC,EAAE;MACpB,OAAO,IAAI;IACb;IAEA,IAAIC,IAAI,GAAGF,IAAI,CAACV,IAAI,CAACa,IAAI;IACzB,MAAMC,QAAQ,GAAG,CAACF,IAAI,CAAC;IACvB,IAAIG,YAAY,GAAAC,MAAA,CAAAC,MAAA,KAAQP,IAAI,CAAE;IAC9B,OAAOK,YAAY,CAACJ,KAAK,GAAG,CAAC,EAAE;MAC7BC,IAAI,GAAG,GAAG,CAAAM,EAAA,GAAAH,YAAY,CAACI,MAAM,CAACnB,IAAI,cAAAkB,EAAA,uBAAAA,EAAA,CAAEL,IAAI,MAAMD,IAAI,EAAE;MACpDE,QAAQ,CAACM,OAAO,CAAC,CAAAC,EAAA,GAAAN,YAAY,CAACI,MAAM,CAACnB,IAAI,cAAAqB,EAAA,uBAAAA,EAAA,CAAER,IAAI,CAAC;MAChDE,YAAY,GAAGA,YAAY,CAACI,MAAM;IACpC;IAEA,MAAMG,QAAQ,GAAAN,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACT5B,IAAI,CAACqB,IAAI,CAACV,IAAI,EAAE,CAACG,KAAK,CAAC,CAAC;MAC3B,CAACP,mBAAmB,GAAGgB,IAAI;MAC3B,CAACf,uBAAuB,GAAGkB,YAAY,CAACf,IAAI,CAACa;IAAI,IAC9CH,IAAI,CACR;IAED,IAAIR,KAAK,IAAIA,KAAK,KAAKL,uBAAuB,EAAE;MAC9CyB,QAAQ,CAACpB,KAAK,CAAC,GAAGQ,IAAI,CAACV,IAAI,CAACE,KAAK,CAAC,KAAI,CAAAqB,EAAA,IAAAC,EAAA,GAAAd,IAAI,CAACS,MAAM,cAAAK,EAAA,uBAAAA,EAAA,CAAExB,IAAI,cAAAuB,EAAA,uBAAAA,EAAA,CAAGrB,KAAK,CAAC;IAClE;IAEAM,MAAM,CAACiB,IAAI,CAACH,QAAQ,CAAC;EACvB,CAAC,CAAC;EAEF,OAAOd,MAAM,CAACkB,GAAG,CAAEC,CAAC,IAAI;IACtB,MAAMC,CAAC,GAAGD,CAAC,CAACC,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACzB,MAAMC,CAAC,GAAG,CAACH,CAAC,CAACG,CAAC,CAAC,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC;IAE1B;IACA,IAAIF,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,EAAE;MACjBE,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAG,CAACH,CAAC,CAACG,CAAC,CAAC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrC;IAEA,OAAAd,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACKU,CAAC;MACJC,CAAC;MACDE,CAAC;MACDC,WAAW,EAAEC,IAAA,CAAAC,GAAA,KAAI,EAAIN,CAAC,CAAChB,KAAK;IAAA;EAEhC,CAAC,CAAC;AACJ;AAEA,MAAMuB,eAAe,GAAG;EACtBC,EAAE,EAAE1C,aAAa;EACjBQ,MAAM,EAAE;IACN2B,CAAC,EAAE,GAAG;IACNE,CAAC,EAAE,GAAG;IACNM,GAAG,EAAExC,mBAAmB;IACxBM,KAAK,EAAEL,uBAAuB;IAC9BM,KAAK,EAAE;GACR;EACDkC,IAAI,EAAE;IAAET,CAAC,EAAE,KAAK;IAAEE,CAAC,EAAE;EAAK,CAAE;EAC5BQ,KAAK,EAAE;IACL,CAAC5C,mBAAmB,GAAGD,aAAa;IACpC8C,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,GAAG;IACdT,WAAW,EAAE,aAAa;IAC1B,CAACvC,gBAAgB,GAAGA,gBAAgB;IACpCmB,KAAK,EAAE;GACR;EACD8B,KAAK,EAAE;IACLC,MAAM,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEJ,MAAM,EAAE;IAAM,CAAE;IACrCK,QAAQ,EAAE;MAAED,MAAM,EAAE,CAAC;MAAEJ,MAAM,EAAE;IAAM;GACtC;EACDM,MAAM,EAAE,KAAK;EACbC,WAAW,EAAE;IAAEC,SAAS,EAAE;EAAI,CAAE;EAChCC,UAAU,EAAE;IACV5C,IAAI,EAAE,OAAO;IACb6C,WAAW,EAAE;;CAEhB;AAED,OAAO,MAAMC,QAAQ,GAAyBnD,OAAO,IAAI;EACvD,MAAM;MAAEE,MAAM,EAAEkD,YAAY;MAAEnD,IAAI,GAAG;IAAE,IAAoBD,OAAO;IAAtBqD,UAAU,GAAAC,MAAA,CAAKtD,OAAO,EAA5D,kBAAkD,CAAU;EAElE,MAAMiD,UAAU,GAAAhC,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KACXmC,UAAU,CAACJ,UAAU;IACxB;IACAC,WAAW,EAAEjB,IAAI,CAACsB,GAAG,CAAChE,GAAG,CAAC8D,UAAU,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE,GAAG,CAAC,EAAE,OAAO;EAAC,EACpF;EAED,MAAMnD,MAAM,GAAAe,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA,KAAQiB,eAAe,CAACjC,MAAM,GAAKkD,YAAY,CAAE;EAC7D,MAAM;IAAEhD;EAAK,CAAE,GAAGF,MAAM;EACxB,MAAMsD,QAAQ,GAAGzD,aAAa,CAAC;IAAEG,MAAM;IAAED;EAAI,CAAE,CAAC;EAEhD,OAAO,CACLZ,OAAO,CAAC,EAAE,EAAE8C,eAAe,EAAAlB,MAAA,CAAAC,MAAA,CAAAD,MAAA,CAAAC,MAAA;IACzBb,IAAI,EAAE,MAAM;IACZJ,IAAI,EAAEuD,QAAQ;IACdtD,MAAM;IACNuD,OAAO,EAAE;MACPC,KAAK,EAAE,MAAM;MACbC,KAAK,EAAE,CACJ/B,CAAC,IAAI;QACJ,OAAO;UACLd,IAAI,EAAEV,KAAK;UACXA,KAAK,EAAEwB,CAAC,CAACxB,KAAK;SACf;MACH,CAAC;;EAEJ,GACEiD,UAAU;IACbJ;EAAU,GACV,CACH;AACH,CAAC;AAEDE,QAAQ,CAACS,KAAK,GAAG,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}