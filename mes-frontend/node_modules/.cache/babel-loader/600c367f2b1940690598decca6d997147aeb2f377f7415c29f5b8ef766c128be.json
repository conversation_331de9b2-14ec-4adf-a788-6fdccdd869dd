{"ast": null, "code": "module.exports = function isArrayish(obj) {\n  if (!obj || typeof obj === 'string') {\n    return false;\n  }\n  return obj instanceof Array || Array.isArray(obj) || obj.length >= 0 && (obj.splice instanceof Function || Object.getOwnPropertyDescriptor(obj, obj.length - 1) && obj.constructor.name !== 'String');\n};", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "obj", "Array", "isArray", "length", "splice", "Function", "Object", "getOwnPropertyDescriptor", "constructor", "name"], "sources": ["/root/mes-system/mes-frontend/node_modules/simple-swizzle/node_modules/is-arrayish/index.js"], "sourcesContent": ["module.exports = function isArrayish(obj) {\n\tif (!obj || typeof obj === 'string') {\n\t\treturn false;\n\t}\n\n\treturn obj instanceof Array || Array.isArray(obj) ||\n\t\t(obj.length >= 0 && (obj.splice instanceof Function ||\n\t\t\t(Object.getOwnPropertyDescriptor(obj, (obj.length - 1)) && obj.constructor.name !== 'String')));\n};\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,GAAG,EAAE;EACzC,IAAI,CAACA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IACpC,OAAO,KAAK;EACb;EAEA,OAAOA,GAAG,YAAYC,KAAK,IAAIA,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,IAC/CA,GAAG,CAACG,MAAM,IAAI,CAAC,KAAKH,GAAG,CAACI,MAAM,YAAYC,QAAQ,IACjDC,MAAM,CAACC,wBAAwB,CAACP,GAAG,EAAGA,GAAG,CAACG,MAAM,GAAG,CAAE,CAAC,IAAIH,GAAG,CAACQ,WAAW,CAACC,IAAI,KAAK,QAAS,CAAE;AAClG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}