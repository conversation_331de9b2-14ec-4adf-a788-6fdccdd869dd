export default addLimitKeyword;
export type Ajv = import("ajv").default;
export type Code = import("ajv").Code;
export type Name = import("ajv").Name;
export type KeywordErrorDefinition = import("ajv").KeywordErrorDefinition;
/** @typedef {import("ajv").default} Ajv */
/** @typedef {import("ajv").Code} Code */
/** @typedef {import("ajv").Name} Name */
/** @typedef {import("ajv").KeywordErrorDefinition} KeywordErrorDefinition */
/**
 * @param {Ajv} ajv
 * @returns {Ajv}
 */
declare function addLimitKeyword(ajv: Ajv): Ajv;
