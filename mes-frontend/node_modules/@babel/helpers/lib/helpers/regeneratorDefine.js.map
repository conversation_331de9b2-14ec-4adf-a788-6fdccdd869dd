{"version": 3, "names": ["regeneratorDefine", "obj", "key", "value", "noFlags", "define", "Object", "defineProperty", "_", "exports", "default", "defineIteratorMethod", "method", "i", "arg", "_invoke", "enumerable", "configurable", "writable"], "sources": ["../../src/helpers/regeneratorDefine.ts"], "sourcesContent": ["/* @minVersion 7.27.0 */\n/* @mangleFns */\n/* @internal */\n\n// Also used to define Iterator Methods\n// Defining the .next, .throw, and .return methods of the Iterator interface in terms of a single ._invoke method.\nexport default function regeneratorDefine(\n  obj: any,\n  key?: PropertyKey,\n  value?: unknown,\n  noFlags?: true,\n) {\n  var define: typeof Object.defineProperty | 0 = Object.defineProperty;\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\", {});\n  } catch (_) {\n    define = 0;\n  }\n\n  // @ts-expect-error explicit function reassign\n  regeneratorDefine = function (\n    obj: any,\n    key?: PropertyKey,\n    value?: unknown,\n    noFlags?: true,\n  ) {\n    if (!key) {\n      function defineIteratorMethod(method: string, i: number) {\n        regeneratorDefine(obj, method, function (this: any, arg: any) {\n          return this._invoke(method, i, arg);\n        });\n      }\n      defineIteratorMethod(\"next\", 0);\n      defineIteratorMethod(\"throw\", 1);\n      defineIteratorMethod(\"return\", 2);\n    } else {\n      if (define) {\n        define(obj, key, {\n          value: value,\n          enumerable: !noFlags,\n          configurable: !noFlags,\n          writable: !noFlags,\n        });\n      } else {\n        obj[key] = value;\n      }\n    }\n  };\n  regeneratorDefine(obj, key, value, noFlags);\n}\n"], "mappings": ";;;;;;AAMe,SAASA,iBAAiBA,CACvCC,GAAQ,EACRC,GAAiB,EACjBC,KAAe,EACfC,OAAc,EACd;EACA,IAAIC,MAAwC,GAAGC,MAAM,CAACC,cAAc;EACpE,IAAI;IAEFF,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;EACpB,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVH,MAAM,GAAG,CAAC;EACZ;EAGAI,OAAA,CAAAC,OAAA,GAAAV,iBAAiB,GAAG,SAAAA,CAClBC,GAAQ,EACRC,GAAiB,EACjBC,KAAe,EACfC,OAAc,EACd;IACA,IAAI,CAACF,GAAG,EAAE;MACR,SAASS,oBAAoBA,CAACC,MAAc,EAAEC,CAAS,EAAE;QACvDb,iBAAiB,CAACC,GAAG,EAAEW,MAAM,EAAE,UAAqBE,GAAQ,EAAE;UAC5D,OAAO,IAAI,CAACC,OAAO,CAACH,MAAM,EAAEC,CAAC,EAAEC,GAAG,CAAC;QACrC,CAAC,CAAC;MACJ;MACAH,oBAAoB,CAAC,MAAM,EAAE,CAAC,CAAC;MAC/BA,oBAAoB,CAAC,OAAO,EAAE,CAAC,CAAC;MAChCA,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC;IACnC,CAAC,MAAM;MACL,IAAIN,MAAM,EAAE;QACVA,MAAM,CAACJ,GAAG,EAAEC,GAAG,EAAE;UACfC,KAAK,EAAEA,KAAK;UACZa,UAAU,EAAE,CAACZ,OAAO;UACpBa,YAAY,EAAE,CAACb,OAAO;UACtBc,QAAQ,EAAE,CAACd;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACLH,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;MAClB;IACF;EACF,CAAC;EACDH,iBAAiB,CAACC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,OAAO,CAAC;AAC7C", "ignoreList": []}