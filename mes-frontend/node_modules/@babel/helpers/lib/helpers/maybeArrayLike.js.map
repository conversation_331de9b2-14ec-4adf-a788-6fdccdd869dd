{"version": 3, "names": ["_arrayLikeToArray", "require", "_maybeArrayLike", "orElse", "arr", "i", "Array", "isArray", "length", "len", "arrayLikeToArray"], "sources": ["../../src/helpers/maybeArrayLike.ts"], "sourcesContent": ["/* @minVersion 7.9.0 */\n\nimport arrayLikeToArray from \"./arrayLikeToArray.ts\";\n\nexport default function _maybeArrayLike<T>(\n  orElse: (arr: any, i: number) => T[] | undefined,\n  arr: ArrayLike<T>,\n  i: number,\n) {\n  if (arr && !Array.isArray(arr) && typeof arr.length === \"number\") {\n    var len = arr.length;\n    return arrayLikeToArray<T>(arr, i !== void 0 && i < len ? i : len);\n  }\n  return orElse(arr, i);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAEe,SAASC,eAAeA,CACrCC,MAAgD,EAChDC,GAAiB,EACjBC,CAAS,EACT;EACA,IAAID,GAAG,IAAI,CAACE,KAAK,CAACC,OAAO,CAACH,GAAG,CAAC,IAAI,OAAOA,GAAG,CAACI,MAAM,KAAK,QAAQ,EAAE;IAChE,IAAIC,GAAG,GAAGL,GAAG,CAACI,MAAM;IACpB,OAAO,IAAAE,yBAAgB,EAAIN,GAAG,EAAEC,CAAC,KAAK,KAAK,CAAC,IAAIA,CAAC,GAAGI,GAAG,GAAGJ,CAAC,GAAGI,GAAG,CAAC;EACpE;EACA,OAAON,MAAM,CAACC,GAAG,EAAEC,CAAC,CAAC;AACvB", "ignoreList": []}