use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Machine {
    pub id: i32,
    pub machine_name: String,
    pub skill_group_id: i32,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct MachineWithSkillGroup {
    pub id: i32,
    pub machine_name: String,
    pub skill_group_id: i32,
    pub skill_group_name: String,
    pub status: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateMachineRequest {
    #[validate(length(min = 1, max = 255))]
    pub machine_name: String,
    pub skill_group_id: i32,
    pub status: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateMachineRequest {
    #[validate(length(min = 1, max = 255))]
    pub machine_name: Option<String>,
    pub skill_group_id: Option<i32>,
    pub status: Option<String>,
}

impl Machine {
    pub const STATUS_AVAILABLE: &'static str = "AVAILABLE";
    pub const STATUS_BUSY: &'static str = "BUSY";
    pub const STATUS_MAINTENANCE: &'static str = "MAINTENANCE";
    pub const STATUS_OFFLINE: &'static str = "OFFLINE";
}
