use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct SkillGroup {
    pub id: i32,
    pub group_name: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateSkillGroupRequest {
    #[validate(length(min = 1, max = 100))]
    pub group_name: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateSkillGroupRequest {
    #[validate(length(min = 1, max = 100))]
    pub group_name: String,
}

// 用户技能关联
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct UserSkill {
    pub user_id: i32,
    pub skill_group_id: i32,
}
