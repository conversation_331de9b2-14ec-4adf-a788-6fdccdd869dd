use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Project {
    pub id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateProjectRequest {
    #[validate(length(min = 1, max = 255))]
    pub project_name: String,
    #[validate(length(max = 255))]
    pub customer_name: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateProjectRequest {
    #[validate(length(min = 1, max = 255))]
    pub project_name: Option<String>,
    #[validate(length(max = 255))]
    pub customer_name: Option<String>,
}
