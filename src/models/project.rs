use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct Project {
    pub id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectWithBom {
    pub id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub created_at: DateTime<Utc>,
    pub bom_items: Vec<ProjectBomItem>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, FromRow)]
pub struct ProjectBom {
    pub id: i32,
    pub project_id: i32,
    pub part_id: i32,
    pub quantity: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectBomItem {
    pub id: i32,
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub quantity: i32,
    pub specifications: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateProjectRequest {
    #[validate(length(min = 1, max = 255))]
    pub project_name: String,
    #[validate(length(max = 255))]
    pub customer_name: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateProjectRequest {
    #[validate(length(min = 1, max = 255))]
    pub project_name: Option<String>,
    #[validate(length(max = 255))]
    pub customer_name: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct AddBomItemRequest {
    pub part_id: i32,
    #[validate(range(min = 1))]
    pub quantity: i32,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateBomItemRequest {
    #[validate(range(min = 1))]
    pub quantity: i32,
}
