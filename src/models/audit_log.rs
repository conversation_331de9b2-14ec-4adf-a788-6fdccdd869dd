use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};
use serde_json::Value;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct AuditLog {
    pub id: i32,
    pub user_id: i32,
    pub action_type: String,
    pub target_entity: String,
    pub target_id: i32,
    pub change_details: Option<Value>,
    pub action_time: DateTime<Utc>,
}

impl AuditLog {
    pub const ACTION_CREATE: &'static str = "CREATE";
    pub const ACTION_UPDATE: &'static str = "UPDATE";
    pub const ACTION_DELETE: &'static str = "DELETE";
    pub const ACTION_LOGIN: &'static str = "LOGIN";
    pub const ACTION_LOGOUT: &'static str = "LOGOUT";
}
