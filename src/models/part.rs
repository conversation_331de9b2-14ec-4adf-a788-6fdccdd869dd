use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Part {
    pub id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreatePartRequest {
    #[validate(length(min = 1, max = 255))]
    pub part_number: String,
    #[validate(length(max = 255))]
    pub part_name: Option<String>,
    #[validate(length(min = 1, max = 50))]
    pub version: String,
    pub specifications: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdatePartRequest {
    #[validate(length(min = 1, max = 255))]
    pub part_number: Option<String>,
    #[validate(length(max = 255))]
    pub part_name: Option<String>,
    #[validate(length(min = 1, max = 50))]
    pub version: Option<String>,
    pub specifications: Option<String>,
}
