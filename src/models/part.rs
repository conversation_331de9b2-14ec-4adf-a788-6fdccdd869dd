use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Part {
    pub id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PartWithRoutings {
    pub id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub version: String,
    pub specifications: Option<String>,
    pub routings: Vec<RoutingStep>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoutingStep {
    pub id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<rust_decimal::Decimal>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreatePartRequest {
    #[validate(length(min = 1, max = 255))]
    pub part_number: String,
    #[validate(length(max = 255))]
    pub part_name: Option<String>,
    #[validate(length(min = 1, max = 50))]
    pub version: String,
    pub specifications: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdatePartRequest {
    #[validate(length(min = 1, max = 255))]
    pub part_number: Option<String>,
    #[validate(length(max = 255))]
    pub part_name: Option<String>,
    #[validate(length(min = 1, max = 50))]
    pub version: Option<String>,
    pub specifications: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateRoutingStepRequest {
    pub step_number: i32,
    #[validate(length(min = 1, max = 255))]
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<rust_decimal::Decimal>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateRoutingStepRequest {
    pub step_number: Option<i32>,
    #[validate(length(min = 1, max = 255))]
    pub process_name: Option<String>,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<rust_decimal::Decimal>,
}
