use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc, NaiveDate};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct WorkOrder {
    pub id: i32,
    pub project_bom_id: i32,
    pub quantity: i32,
    pub status: String,
    pub due_date: Option<NaiveDate>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderWithDetails {
    pub id: i32,
    pub project_bom_id: i32,
    pub project_name: String,
    pub part_number: String,
    pub part_name: Option<String>,
    pub part_version: String,
    pub quantity: i32,
    pub status: String,
    pub due_date: Option<NaiveDate>,
    pub created_at: DateTime<Utc>,
    pub plan_tasks: Vec<WorkOrderPlanTask>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkOrderPlanTask {
    pub id: i32,
    pub routing_step_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub skill_group_name: String,
    pub planned_start: Option<DateTime<Utc>>,
    pub planned_end: Option<DateTime<Utc>>,
    pub status: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateWorkOrderRequest {
    pub project_bom_id: i32,
    #[validate(range(min = 1))]
    pub quantity: i32,
    pub due_date: Option<NaiveDate>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateWorkOrderRequest {
    #[validate(range(min = 1))]
    pub quantity: Option<i32>,
    pub status: Option<String>,
    pub due_date: Option<NaiveDate>,
}

#[derive(Debug, Deserialize)]
pub struct WorkOrderQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub status: Option<String>,
    pub project_id: Option<i32>,
    pub due_date_from: Option<NaiveDate>,
    pub due_date_to: Option<NaiveDate>,
}

impl WorkOrder {
    pub const STATUS_PENDING: &'static str = "PENDING";
    pub const STATUS_PLANNED: &'static str = "PLANNED";
    pub const STATUS_IN_PROGRESS: &'static str = "IN_PROGRESS";
    pub const STATUS_COMPLETED: &'static str = "COMPLETED";
    pub const STATUS_CANCELLED: &'static str = "CANCELLED";
}
