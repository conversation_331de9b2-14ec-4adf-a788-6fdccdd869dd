use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc, NaiveDate};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct WorkOrder {
    pub id: i32,
    pub project_bom_id: i32,
    pub quantity: i32,
    pub status: String,
    pub due_date: Option<NaiveDate>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateWorkOrderRequest {
    pub project_bom_id: i32,
    #[validate(range(min = 1))]
    pub quantity: i32,
    pub due_date: Option<NaiveDate>,
}
