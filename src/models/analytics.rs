use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, NaiveDate};
use validator::Validate;

// 生产效率分析
#[derive(Debug, Serialize, Deserialize)]
pub struct ProductionEfficiency {
    pub period: String,
    pub planned_hours: f64,
    pub actual_hours: f64,
    pub efficiency_rate: f64,
    pub completed_tasks: i32,
    pub total_tasks: i32,
    pub completion_rate: f64,
}

// 设备利用率分析
#[derive(Debug, Serialize, Deserialize)]
pub struct EquipmentUtilization {
    pub machine_id: i32,
    pub machine_name: String,
    pub skill_group: String,
    pub total_available_hours: f64,
    pub actual_working_hours: f64,
    pub utilization_rate: f64,
    pub maintenance_hours: f64,
    pub idle_hours: f64,
}

// 质量分析
#[derive(Debug, Serialize, Deserialize)]
pub struct QualityMetrics {
    pub period: String,
    pub total_parts: i32,
    pub passed_parts: i32,
    pub failed_parts: i32,
    pub rework_parts: i32,
    pub scrap_parts: i32,
    pub first_pass_yield: f64,
    pub overall_yield: f64,
}

// 人员效率分析
#[derive(Debug, Serialize, Deserialize)]
pub struct OperatorEfficiency {
    pub user_id: i32,
    pub username: String,
    pub full_name: Option<String>,
    pub skill_groups: Vec<String>,
    pub completed_tasks: i32,
    pub total_working_hours: f64,
    pub average_task_time: f64,
    pub efficiency_score: f64,
}

// 项目进度分析
#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectProgress {
    pub project_id: i32,
    pub project_name: String,
    pub customer_name: Option<String>,
    pub total_work_orders: i32,
    pub completed_work_orders: i32,
    pub in_progress_work_orders: i32,
    pub pending_work_orders: i32,
    pub overall_progress: f64,
    pub estimated_completion: Option<DateTime<Utc>>,
    pub is_on_schedule: bool,
}

// 成本分析
#[derive(Debug, Serialize, Deserialize)]
pub struct CostAnalysis {
    pub period: String,
    pub labor_cost: f64,
    pub material_cost: f64,
    pub overhead_cost: f64,
    pub total_cost: f64,
    pub cost_per_unit: f64,
    pub budget_variance: f64,
}

// KPI仪表板数据
#[derive(Debug, Serialize, Deserialize)]
pub struct KPIDashboard {
    pub period: String,
    pub production_efficiency: ProductionEfficiency,
    pub quality_metrics: QualityMetrics,
    pub equipment_utilization: Vec<EquipmentUtilization>,
    pub top_performers: Vec<OperatorEfficiency>,
    pub project_status: Vec<ProjectProgress>,
    pub cost_summary: CostAnalysis,
}

// 趋势分析数据
#[derive(Debug, Serialize, Deserialize)]
pub struct TrendData {
    pub date: NaiveDate,
    pub value: f64,
    pub target: Option<f64>,
    pub category: String,
}

// 报表查询参数
#[derive(Debug, Deserialize, Validate)]
pub struct ReportQuery {
    pub start_date: NaiveDate,
    pub end_date: NaiveDate,
    pub report_type: String,
    pub group_by: Option<String>, // "day", "week", "month"
    pub project_id: Option<i32>,
    pub skill_group_id: Option<i32>,
    pub user_id: Option<i32>,
}

// 实时监控数据
#[derive(Debug, Serialize, Deserialize)]
pub struct RealTimeMetrics {
    pub timestamp: DateTime<Utc>,
    pub active_tasks: i32,
    pub active_operators: i32,
    pub machines_in_use: i32,
    pub current_efficiency: f64,
    pub alerts: Vec<SystemAlert>,
}

// 系统告警
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemAlert {
    pub id: String,
    pub alert_type: String, // "DELAY", "QUALITY", "EQUIPMENT", "RESOURCE"
    pub severity: String,   // "LOW", "MEDIUM", "HIGH", "CRITICAL"
    pub message: String,
    pub entity_type: String,
    pub entity_id: i32,
    pub created_at: DateTime<Utc>,
    pub acknowledged: bool,
}

// 预测分析
#[derive(Debug, Serialize, Deserialize)]
pub struct PredictionData {
    pub metric_name: String,
    pub current_value: f64,
    pub predicted_value: f64,
    pub confidence_level: f64,
    pub prediction_date: DateTime<Utc>,
    pub factors: Vec<String>,
}

// 对比分析
#[derive(Debug, Serialize, Deserialize)]
pub struct ComparisonAnalysis {
    pub metric_name: String,
    pub current_period: f64,
    pub previous_period: f64,
    pub change_percentage: f64,
    pub trend: String, // "UP", "DOWN", "STABLE"
    pub is_improvement: bool,
}

impl ReportQuery {
    pub const REPORT_PRODUCTION: &'static str = "PRODUCTION";
    pub const REPORT_QUALITY: &'static str = "QUALITY";
    pub const REPORT_EQUIPMENT: &'static str = "EQUIPMENT";
    pub const REPORT_OPERATOR: &'static str = "OPERATOR";
    pub const REPORT_PROJECT: &'static str = "PROJECT";
    pub const REPORT_COST: &'static str = "COST";
}

impl SystemAlert {
    pub const TYPE_DELAY: &'static str = "DELAY";
    pub const TYPE_QUALITY: &'static str = "QUALITY";
    pub const TYPE_EQUIPMENT: &'static str = "EQUIPMENT";
    pub const TYPE_RESOURCE: &'static str = "RESOURCE";
    
    pub const SEVERITY_LOW: &'static str = "LOW";
    pub const SEVERITY_MEDIUM: &'static str = "MEDIUM";
    pub const SEVERITY_HIGH: &'static str = "HIGH";
    pub const SEVERITY_CRITICAL: &'static str = "CRITICAL";
}
