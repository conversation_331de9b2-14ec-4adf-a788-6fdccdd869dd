use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ExecutionLog {
    pub id: i32,
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    pub user_id: i32,
    pub event_type: String,
    pub event_time: DateTime<Utc>,
    pub notes: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateExecutionLogRequest {
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    #[validate(length(min = 1, max = 50))]
    pub event_type: String,
    pub notes: Option<String>,
}

impl ExecutionLog {
    pub const EVENT_START: &'static str = "START";
    pub const EVENT_PAUSE: &'static str = "PAUSE";
    pub const EVENT_RESUME: &'static str = "RESUME";
    pub const EVENT_COMPLETE: &'static str = "COMPLETE";
    pub const EVENT_CANCEL: &'static str = "CANCEL";
}
