use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc, NaiveDate};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct ExecutionLog {
    pub id: i32,
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    pub user_id: i32,
    pub event_type: String,
    pub event_time: DateTime<Utc>,
    pub notes: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ExecutionLogWithDetails {
    pub id: i32,
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    pub machine_name: Option<String>,
    pub user_id: i32,
    pub username: String,
    pub user_full_name: Option<String>,
    pub event_type: String,
    pub event_time: DateTime<Utc>,
    pub notes: Option<String>,
    pub project_name: String,
    pub part_number: String,
    pub part_name: Option<String>,
    pub process_name: String,
    pub step_number: i32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct WorkstationStatus {
    pub plan_task_id: i32,
    pub project_name: String,
    pub part_number: String,
    pub part_name: Option<String>,
    pub process_name: String,
    pub step_number: i32,
    pub work_order_quantity: i32,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub actual_start: Option<DateTime<Utc>>,
    pub status: String,
    pub assigned_machine: Option<String>,
    pub current_operator: Option<String>,
    pub progress_percentage: f32,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QRCodeData {
    pub task_id: i32,
    pub task_type: String, // "PLAN_TASK", "WORK_ORDER", "PART"
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateExecutionLogRequest {
    pub plan_task_id: i32,
    pub machine_id: Option<i32>,
    #[validate(length(min = 1, max = 50))]
    pub event_type: String,
    pub notes: Option<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct ScanReportRequest {
    #[validate(length(min = 1))]
    pub qr_code: String,
    #[validate(length(min = 1, max = 50))]
    pub event_type: String,
    pub machine_id: Option<i32>,
    pub notes: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct ExecutionLogQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub plan_task_id: Option<i32>,
    pub user_id: Option<i32>,
    pub event_type: Option<String>,
    pub date_from: Option<NaiveDate>,
    pub date_to: Option<NaiveDate>,
}

#[derive(Debug, Deserialize)]
pub struct TraceabilityQuery {
    pub part_number: Option<String>,
    pub work_order_id: Option<i32>,
    pub project_id: Option<i32>,
    pub date_from: Option<NaiveDate>,
    pub date_to: Option<NaiveDate>,
}

#[derive(Debug, Serialize)]
pub struct TraceabilityRecord {
    pub work_order_id: i32,
    pub project_name: String,
    pub part_number: String,
    pub part_name: Option<String>,
    pub quantity: i32,
    pub execution_history: Vec<ExecutionStep>,
}

#[derive(Debug, Serialize)]
pub struct ExecutionStep {
    pub step_number: i32,
    pub process_name: String,
    pub operator: String,
    pub machine: Option<String>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub duration_hours: Option<f32>,
    pub status: String,
    pub notes: Vec<String>,
}

impl ExecutionLog {
    pub const EVENT_START: &'static str = "START";
    pub const EVENT_PAUSE: &'static str = "PAUSE";
    pub const EVENT_RESUME: &'static str = "RESUME";
    pub const EVENT_COMPLETE: &'static str = "COMPLETE";
    pub const EVENT_CANCEL: &'static str = "CANCEL";
    pub const EVENT_QUALITY_CHECK: &'static str = "QUALITY_CHECK";
    pub const EVENT_REWORK: &'static str = "REWORK";
    pub const EVENT_SCRAP: &'static str = "SCRAP";
}
