use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: i32,
    pub username: String,
    pub password_hash: String,
    pub full_name: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: i32,
    pub username: String,
    pub full_name: Option<String>,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub roles: Vec<String>,
    pub skills: Vec<String>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateUserRequest {
    #[validate(length(min = 3, max = 100))]
    pub username: String,
    #[validate(length(min = 6))]
    pub password: String,
    #[validate(length(max = 100))]
    pub full_name: Option<String>,
    pub role_ids: Vec<i32>,
    pub skill_group_ids: Vec<i32>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateUserRequest {
    #[validate(length(min = 3, max = 100))]
    pub username: Option<String>,
    #[validate(length(min = 6))]
    pub password: Option<String>,
    #[validate(length(max = 100))]
    pub full_name: Option<String>,
    pub is_active: Option<bool>,
    pub role_ids: Option<Vec<i32>>,
    pub skill_group_ids: Option<Vec<i32>>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(length(min = 1))]
    pub username: String,
    #[validate(length(min = 1))]
    pub password: String,
}

#[derive(Debug, Serialize)]
pub struct LoginResponse {
    pub token: String,
    pub user: UserResponse,
}

impl User {
    pub fn to_response(&self, roles: Vec<String>, skills: Vec<String>) -> UserResponse {
        UserResponse {
            id: self.id,
            username: self.username.clone(),
            full_name: self.full_name.clone(),
            is_active: self.is_active,
            created_at: self.created_at,
            roles,
            skills,
        }
    }
}
