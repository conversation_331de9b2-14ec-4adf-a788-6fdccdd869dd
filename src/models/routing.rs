use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Routing {
    pub id: i32,
    pub part_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<rust_decimal::Decimal>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateRoutingRequest {
    pub part_id: i32,
    pub step_number: i32,
    #[validate(length(min = 1, max = 255))]
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<rust_decimal::Decimal>,
}
