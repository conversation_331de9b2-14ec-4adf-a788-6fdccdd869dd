use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;
use bigdecimal::BigDecimal;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Routing {
    pub id: i32,
    pub part_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<BigDecimal>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoutingWithPart {
    pub id: i32,
    pub part_id: i32,
    pub part_number: String,
    pub part_name: Option<String>,
    pub step_number: i32,
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<BigDecimal>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateRoutingRequest {
    pub part_id: i32,
    pub step_number: i32,
    #[validate(length(min = 1, max = 255))]
    pub process_name: String,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<BigDecimal>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateRoutingRequest {
    pub step_number: Option<i32>,
    #[validate(length(min = 1, max = 255))]
    pub process_name: Option<String>,
    pub work_instructions: Option<String>,
    pub standard_hours: Option<BigDecimal>,
}

#[derive(Debug, Deserialize)]
pub struct RoutingQuery {
    pub part_id: Option<i32>,
    pub process_name: Option<String>,
}
