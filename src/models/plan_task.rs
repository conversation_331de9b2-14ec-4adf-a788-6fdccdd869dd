use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc, NaiveDate};
use validator::Validate;

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PlanTask {
    pub id: i32,
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub status: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlanTaskWithDetails {
    pub id: i32,
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub step_number: i32,
    pub process_name: String,
    pub skill_group_id: i32,
    pub skill_group_name: String,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub status: String,
    pub project_name: String,
    pub part_number: String,
    pub part_name: Option<String>,
    pub work_order_quantity: i32,
    pub standard_hours: Option<rust_decimal::Decimal>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GanttTask {
    pub id: i32,
    pub name: String,
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
    pub progress: f32,
    pub dependencies: Vec<i32>,
    pub resource: String,
    pub project: String,
    pub part: String,
    pub process: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreatePlanTaskRequest {
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdatePlanTaskRequest {
    pub planned_start: Option<DateTime<Utc>>,
    pub planned_end: Option<DateTime<Utc>>,
    pub status: Option<String>,
    pub skill_group_id: Option<i32>,
}

#[derive(Debug, Deserialize)]
pub struct PlanTaskQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub status: Option<String>,
    pub skill_group_id: Option<i32>,
    pub date_from: Option<NaiveDate>,
    pub date_to: Option<NaiveDate>,
    pub work_order_id: Option<i32>,
}

#[derive(Debug, Deserialize, Validate)]
pub struct ScheduleRequest {
    pub work_order_ids: Vec<i32>,
    pub start_date: NaiveDate,
    pub priority_mode: Option<String>, // "due_date", "fifo", "shortest_first"
}

#[derive(Debug, Serialize)]
pub struct ScheduleResult {
    pub success: bool,
    pub message: String,
    pub scheduled_tasks: Vec<PlanTaskWithDetails>,
    pub conflicts: Vec<ScheduleConflict>,
}

#[derive(Debug, Serialize)]
pub struct ScheduleConflict {
    pub task_id: i32,
    pub conflict_type: String,
    pub description: String,
    pub suggested_start: Option<DateTime<Utc>>,
}

impl PlanTask {
    pub const STATUS_PLANNED: &'static str = "PLANNED";
    pub const STATUS_ASSIGNED: &'static str = "ASSIGNED";
    pub const STATUS_IN_PROGRESS: &'static str = "IN_PROGRESS";
    pub const STATUS_COMPLETED: &'static str = "COMPLETED";
    pub const STATUS_CANCELLED: &'static str = "CANCELLED";
}
