use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use chrono::{DateTime, Utc};
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct PlanTask {
    pub id: i32,
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
    pub status: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreatePlanTaskRequest {
    pub work_order_id: i32,
    pub routing_step_id: i32,
    pub skill_group_id: i32,
    pub planned_start: DateTime<Utc>,
    pub planned_end: DateTime<Utc>,
}

impl PlanTask {
    pub const STATUS_PLANNED: &'static str = "PLANNED";
    pub const STATUS_ASSIGNED: &'static str = "ASSIGNED";
    pub const STATUS_IN_PROGRESS: &'static str = "IN_PROGRESS";
    pub const STATUS_COMPLETED: &'static str = "COMPLETED";
    pub const STATUS_CANCELLED: &'static str = "CANCELLED";
}
