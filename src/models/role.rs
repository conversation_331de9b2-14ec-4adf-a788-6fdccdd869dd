use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use validator::Validate;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: i32,
    pub role_name: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct CreateRoleRequest {
    #[validate(length(min = 1, max = 50))]
    pub role_name: String,
}

#[derive(Debug, Deserialize, Validate)]
pub struct UpdateRoleRequest {
    #[validate(length(min = 1, max = 50))]
    pub role_name: String,
}

// 用户角色关联
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow)]
pub struct UserRole {
    pub user_id: i32,
    pub role_id: i32,
}

impl Role {
    pub const ADMIN: &'static str = "Admin";
    pub const PROCESS_ENGINEER: &'static str = "Process Engineer";
    pub const PLANNER: &'static str = "Planner";
    pub const OPERATOR: &'static str = "Operator";
    pub const QUALITY_INSPECTOR: &'static str = "Quality Inspector";
}
