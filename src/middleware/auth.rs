use axum::{
    extract::{Request, State},
    http::{header::AUTHORIZATION, StatusCode},
    middleware::Next,
    response::Response,
};
use jsonwebtoken::{decode, DecodingKey, Validation, Algorithm};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;

use crate::{database::Database, error::AppError};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,  // user_id
    pub username: String,
    pub roles: Vec<String>,
    pub skills: Vec<String>,
    pub exp: usize,
}

#[derive(Debug, Clone)]
pub struct CurrentUser {
    pub id: i32,
    pub username: String,
    pub roles: Vec<String>,
    pub skills: Vec<String>,
}

impl CurrentUser {
    pub fn has_role(&self, role: &str) -> bool {
        self.roles.contains(&role.to_string())
    }

    pub fn has_any_role(&self, roles: &[&str]) -> bool {
        roles.iter().any(|role| self.has_role(role))
    }

    pub fn has_skill(&self, skill: &str) -> bool {
        self.skills.contains(&skill.to_string())
    }

    pub fn is_admin(&self) -> bool {
        self.has_role("Admin")
    }
}

pub async fn auth_middleware(
    State(database): State<Database>,
    mut request: Request,
    next: Next,
) -> Result<Response, AppError> {
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
        .and_then(|header| header.strip_prefix("Bearer "));

    let token = auth_header.ok_or_else(|| {
        AppError::Authentication("Missing or invalid authorization header".to_string())
    })?;

    let claims = validate_token(token)?;
    
    // 验证用户是否仍然活跃
    let user_active = sqlx::query_scalar!(
        "SELECT is_active FROM users WHERE id = $1",
        claims.sub.parse::<i32>().map_err(|_| {
            AppError::Authentication("Invalid user ID in token".to_string())
        })?
    )
    .fetch_optional(&database.pool)
    .await?
    .unwrap_or(false);

    if !user_active {
        return Err(AppError::Authentication("User account is inactive".to_string()));
    }

    let current_user = CurrentUser {
        id: claims.sub.parse().unwrap(),
        username: claims.username,
        roles: claims.roles,
        skills: claims.skills,
    };

    request.extensions_mut().insert(current_user);
    Ok(next.run(request).await)
}

pub fn validate_token(token: &str) -> Result<Claims, AppError> {
    let secret = std::env::var("JWT_SECRET")
        .unwrap_or_else(|_| "your-secret-key-change-in-production".to_string());
    
    let validation = Validation::new(Algorithm::HS256);
    
    decode::<Claims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &validation,
    )
    .map(|data| data.claims)
    .map_err(|_| AppError::Authentication("Invalid token".to_string()))
}

pub fn create_token(user_id: i32, username: &str, roles: Vec<String>, skills: Vec<String>) -> Result<String, AppError> {
    let secret = std::env::var("JWT_SECRET")
        .unwrap_or_else(|_| "your-secret-key-change-in-production".to_string());
    
    let exp = chrono::Utc::now()
        .checked_add_signed(chrono::Duration::hours(24))
        .expect("valid timestamp")
        .timestamp() as usize;

    let claims = Claims {
        sub: user_id.to_string(),
        username: username.to_string(),
        roles,
        skills,
        exp,
    };

    jsonwebtoken::encode(
        &jsonwebtoken::Header::default(),
        &claims,
        &jsonwebtoken::EncodingKey::from_secret(secret.as_ref()),
    )
    .map_err(|_| AppError::Internal("Failed to create token".to_string()))
}

// 权限检查宏
#[macro_export]
macro_rules! require_role {
    ($user:expr, $role:expr) => {
        if !$user.has_role($role) {
            return Err(AppError::Authorization(format!("Required role: {}", $role)));
        }
    };
}

#[macro_export]
macro_rules! require_any_role {
    ($user:expr, $($role:expr),+) => {
        if !$user.has_any_role(&[$($role),+]) {
            return Err(AppError::Authorization("Insufficient permissions".to_string()));
        }
    };
}
