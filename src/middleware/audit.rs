use axum::{
    extract::{Request, State},
    middleware::Next,
    response::Response,
};
use serde_json::Value;

use crate::{database::Database, middleware::auth::CurrentUser};

pub async fn audit_middleware(
    State(database): State<Database>,
    request: Request,
    next: Next,
) -> Response {
    let method = request.method().clone();
    let uri = request.uri().clone();
    
    // 获取当前用户信息（如果已认证）
    let user_id = request
        .extensions()
        .get::<CurrentUser>()
        .map(|user| user.id);

    let response = next.run(request).await;

    // 只记录修改操作的审计日志
    if matches!(method.as_str(), "POST" | "PUT" | "PATCH" | "DELETE") {
        if let Some(user_id) = user_id {
            let action_type = match method.as_str() {
                "POST" => "CREATE",
                "PUT" | "PATCH" => "UPDATE", 
                "DELETE" => "DELETE",
                _ => "UNKNOWN",
            };

            // 从URI路径推断目标实体
            let target_entity = extract_entity_from_path(uri.path());
            
            // 记录审计日志（忽略错误，不影响主要业务流程）
            let _ = sqlx::query!(
                r#"
                INSERT INTO audit_logs (user_id, action_type, target_entity, target_id, action_time)
                VALUES ($1, $2, $3, $4, NOW())
                "#,
                user_id,
                action_type,
                target_entity,
                0i32, // 暂时使用0，后续可以从响应中提取实际ID
            )
            .execute(&database.pool)
            .await;
        }
    }

    response
}

fn extract_entity_from_path(path: &str) -> String {
    // 从API路径中提取实体名称
    // 例如: /api/users/123 -> "users"
    let parts: Vec<&str> = path.split('/').collect();
    if parts.len() >= 3 && parts[1] == "api" {
        parts[2].to_string()
    } else {
        "unknown".to_string()
    }
}

// 审计日志记录函数
pub async fn log_audit(
    database: &Database,
    user_id: i32,
    action_type: &str,
    target_entity: &str,
    target_id: i32,
    change_details: Option<Value>,
) -> Result<(), sqlx::Error> {
    sqlx::query!(
        r#"
        INSERT INTO audit_logs (user_id, action_type, target_entity, target_id, change_details, action_time)
        VALUES ($1, $2, $3, $4, $5, NOW())
        "#,
        user_id,
        action_type,
        target_entity,
        target_id,
        change_details,
    )
    .execute(&database.pool)
    .await?;

    Ok(())
}
