use axum::{
    middleware as axum_middleware,
    routing::{get, post, put, delete},
    Router,
    <PERSON><PERSON>,
    response::<PERSON><PERSON> as Response<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing_subscriber;

mod config;
mod database;
mod models;
mod handlers;
mod middleware;
mod error;

use config::Config;
use database::Database;
use middleware::{auth::auth_middleware, audit::audit_middleware};

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    message: String,
}

async fn health_check() -> ResponseJson<HealthResponse> {
    ResponseJson(HealthResponse {
        status: "ok".to_string(),
        message: "MES System is running".to_string(),
    })
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::init();

    // Load configuration
    let config = Config::from_env()?;
    
    // Initialize database
    let database = Database::new(&config.database_url).await?;
    
    // Run migrations
    database.migrate().await?;

    // Build our application with routes
    let app = Router::new()
        .route("/health", get(health_check))
        // Public routes (no authentication required)
        .route("/api/auth/login", post(handlers::auth::login))
        // Protected routes (authentication required)
        .nest("/api", protected_routes(database.clone()))
        .layer(CorsLayer::permissive())
        .layer(axum_middleware::from_fn_with_state(
            database.clone(),
            audit_middleware,
        ))
        .with_state(database);

    // Run the server
    let addr = SocketAddr::from(([0, 0, 0, 0], config.port));
    tracing::info!("Server listening on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

fn protected_routes(database: Database) -> Router<Database> {
    Router::new()
        // Auth routes
        .route("/auth/me", get(handlers::auth::me))
        .route("/auth/change-password", post(handlers::auth::change_password))
        // User management routes
        .route("/users", get(handlers::user::list_users))
        .route("/users", post(handlers::user::create_user))
        .route("/users/:id", get(handlers::user::get_user))
        // Placeholder routes for future implementation
        .route("/projects", get(handlers::project::list_projects))
        .route("/parts", get(handlers::part::list_parts))
        .route("/work-orders", get(handlers::work_order::list_work_orders))
        .route("/routings", get(handlers::routing::list_routings))
        .route("/plan-tasks", get(handlers::plan_task::list_plan_tasks))
        .route("/execution-logs", get(handlers::execution::list_execution_logs))
        // Apply authentication middleware to all protected routes
        .layer(axum_middleware::from_fn_with_state(
            database,
            auth_middleware,
        ))
}
