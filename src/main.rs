use axum::{
    middleware as axum_middleware,
    routing::{get, post, put, delete},
    Router,
    <PERSON><PERSON>,
    response::<PERSON><PERSON> as Response<PERSON><PERSON>,
};
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;
use tower_http::cors::CorsLayer;
use tracing_subscriber;

mod config;
mod database;
mod models;
mod handlers;
mod middleware;
mod error;

use config::Config;
use database::Database;
use middleware::{auth::auth_middleware, audit::audit_middleware};

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    message: String,
}

async fn health_check() -> ResponseJson<HealthResponse> {
    ResponseJson(HealthResponse {
        status: "ok".to_string(),
        message: "MES System is running".to_string(),
    })
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::init();

    // Load configuration
    let config = Config::from_env()?;
    
    // Initialize database
    let database = Database::new(&config.database_url).await?;
    
    // Run migrations
    database.migrate().await?;

    // Build our application with routes
    let app = Router::new()
        .route("/health", get(health_check))
        // Public routes (no authentication required)
        .route("/api/auth/login", post(handlers::auth::login))
        // Protected routes (authentication required)
        .nest("/api", protected_routes(database.clone()))
        .layer(CorsLayer::permissive())
        .layer(axum_middleware::from_fn_with_state(
            database.clone(),
            audit_middleware,
        ))
        .with_state(database);

    // Run the server
    let addr = SocketAddr::from(([0, 0, 0, 0], config.port));
    tracing::info!("Server listening on {}", addr);
    
    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}

fn protected_routes(database: Database) -> Router<Database> {
    Router::new()
        // Auth routes
        .route("/auth/me", get(handlers::auth::me))
        .route("/auth/change-password", post(handlers::auth::change_password))
        // User management routes
        .route("/users", get(handlers::user::list_users))
        .route("/users", post(handlers::user::create_user))
        .route("/users/:id", get(handlers::user::get_user))
        // Project management routes
        .route("/projects", get(handlers::project::list_projects))
        .route("/projects", post(handlers::project::create_project))
        .route("/projects/:id", get(handlers::project::get_project))
        .route("/projects/:id", put(handlers::project::update_project))
        .route("/projects/:id", delete(handlers::project::delete_project))
        .route("/projects/:id/bom", post(handlers::project::add_bom_item))
        .route("/projects/:project_id/bom/:bom_id", put(handlers::project::update_bom_item))
        .route("/projects/:project_id/bom/:bom_id", delete(handlers::project::remove_bom_item))
        // Parts management routes
        .route("/parts", get(handlers::part::list_parts))
        .route("/parts", post(handlers::part::create_part))
        .route("/parts/:id", get(handlers::part::get_part))
        .route("/parts/:id", put(handlers::part::update_part))
        .route("/parts/:id", delete(handlers::part::delete_part))
        // Routing management routes
        .route("/routings", get(handlers::routing::list_routings))
        .route("/routings", post(handlers::routing::create_routing))
        .route("/routings/:id", get(handlers::routing::get_routing))
        .route("/routings/:id", put(handlers::routing::update_routing))
        .route("/routings/:id", delete(handlers::routing::delete_routing))
        // Work order management routes
        .route("/work-orders", get(handlers::work_order::list_work_orders))
        .route("/work-orders", post(handlers::work_order::create_work_order))
        .route("/work-orders/:id", get(handlers::work_order::get_work_order))
        .route("/work-orders/:id", put(handlers::work_order::update_work_order))
        .route("/work-orders/:id", delete(handlers::work_order::delete_work_order))
        // Plan task management routes
        .route("/plan-tasks", get(handlers::plan_task::list_plan_tasks))
        .route("/plan-tasks", post(handlers::plan_task::create_plan_task))
        .route("/plan-tasks/:id", put(handlers::plan_task::update_plan_task))
        .route("/plan-tasks/:id", delete(handlers::plan_task::delete_plan_task))
        .route("/plan-tasks/gantt", get(handlers::plan_task::get_gantt_data))
        .route("/plan-tasks/schedule", post(handlers::plan_task::auto_schedule))
        .route("/plan-tasks/resource-utilization", get(handlers::plan_task::get_resource_utilization))
        // Execution and workshop management routes
        .route("/execution-logs", get(handlers::execution::list_execution_logs))
        .route("/execution-logs", post(handlers::execution::create_execution_log))
        .route("/execution/workstation-status", get(handlers::execution::get_workstation_status))
        .route("/execution/my-tasks", get(handlers::execution::get_my_tasks))
        .route("/execution/summary", get(handlers::execution::get_execution_summary))
        .route("/execution/qr-code/:task_id", get(handlers::execution::generate_qr_code))
        .route("/execution/scan-report", post(handlers::execution::scan_report))
        .route("/execution/traceability", get(handlers::execution::get_traceability))
        // Apply authentication middleware to all protected routes
        .layer(axum_middleware::from_fn_with_state(
            database,
            auth_middleware,
        ))
}
