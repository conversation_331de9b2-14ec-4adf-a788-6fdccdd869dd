use axum::{
    extract::Query,
    http::StatusCode,
    response::{<PERSON>son, Html},
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tower_http::cors::Cors<PERSON>ayer;

#[derive(Debug, Serialize)]
struct ApiResponse<T> {
    success: bool,
    data: Option<T>,
    message: String,
}

#[derive(Debug, Serialize)]
struct HealthStatus {
    status: String,
    timestamp: String,
    version: String,
}

#[derive(Debug, Deserialize)]
struct LoginRequest {
    username: String,
    password: String,
}

#[derive(Debug, Serialize)]
struct LoginResponse {
    token: String,
    user: UserInfo,
}

#[derive(Debug, Serialize)]
struct UserInfo {
    id: i32,
    username: String,
    full_name: String,
    roles: Vec<String>,
}

async fn health_check() -> <PERSON>son<ApiResponse<HealthStatus>> {
    Json(ApiResponse {
        success: true,
        data: Some(HealthStatus {
            status: "healthy".to_string(),
            timestamp: chrono::Utc::now().to_rfc3339(),
            version: "1.0.0".to_string(),
        }),
        message: "MES系统运行正常".to_string(),
    })
}

async fn login(Json(request): Json<LoginRequest>) -> Result<Json<ApiResponse<LoginResponse>>, StatusCode> {
    // 简单的演示登录
    if request.username == "admin" && request.password == "admin123" {
        Ok(Json(ApiResponse {
            success: true,
            data: Some(LoginResponse {
                token: "demo-jwt-token-12345".to_string(),
                user: UserInfo {
                    id: 1,
                    username: "admin".to_string(),
                    full_name: "系统管理员".to_string(),
                    roles: vec!["ADMIN".to_string()],
                },
            }),
            message: "登录成功".to_string(),
        }))
    } else {
        Err(StatusCode::UNAUTHORIZED)
    }
}

async fn get_projects() -> Json<ApiResponse<Vec<HashMap<String, serde_json::Value>>>> {
    let projects = vec![
        {
            let mut project = HashMap::new();
            project.insert("id".to_string(), serde_json::Value::Number(1.into()));
            project.insert("project_name".to_string(), serde_json::Value::String("示例项目A".to_string()));
            project.insert("customer_name".to_string(), serde_json::Value::String("客户A".to_string()));
            project.insert("created_at".to_string(), serde_json::Value::String("2024-01-01T00:00:00Z".to_string()));
            project
        },
        {
            let mut project = HashMap::new();
            project.insert("id".to_string(), serde_json::Value::Number(2.into()));
            project.insert("project_name".to_string(), serde_json::Value::String("示例项目B".to_string()));
            project.insert("customer_name".to_string(), serde_json::Value::String("客户B".to_string()));
            project.insert("created_at".to_string(), serde_json::Value::String("2024-01-02T00:00:00Z".to_string()));
            project
        },
    ];

    Json(ApiResponse {
        success: true,
        data: Some(projects),
        message: "获取项目列表成功".to_string(),
    })
}

async fn get_work_orders() -> Json<ApiResponse<Vec<HashMap<String, serde_json::Value>>>> {
    let work_orders = vec![
        {
            let mut wo = HashMap::new();
            wo.insert("id".to_string(), serde_json::Value::Number(1.into()));
            wo.insert("project_name".to_string(), serde_json::Value::String("示例项目A".to_string()));
            wo.insert("part_number".to_string(), serde_json::Value::String("PART-001".to_string()));
            wo.insert("quantity".to_string(), serde_json::Value::Number(100.into()));
            wo.insert("status".to_string(), serde_json::Value::String("IN_PROGRESS".to_string()));
            wo
        },
    ];

    Json(ApiResponse {
        success: true,
        data: Some(work_orders),
        message: "获取工单列表成功".to_string(),
    })
}

async fn web_interface() -> Html<&'static str> {
    Html(r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MES制造执行系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            color: #fff;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .status {
            background: rgba(76, 175, 80, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4CAF50;
        }
        .api-section {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
        }
        .api-test {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255,255,255,0.05);
            border-radius: 5px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #45a049; }
        .result {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .server-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏭 MES制造执行系统</h1>

        <div class="status">
            <h3>✅ 系统状态：运行正常</h3>
            <p>服务器已成功启动，所有API接口正常工作</p>
        </div>

        <div class="server-info">
            <div class="info-card">
                <h4>🌐 服务器地址</h4>
                <p>************:3000</p>
            </div>
            <div class="info-card">
                <h4>⚡ 响应时间</h4>
                <p>< 1ms</p>
            </div>
            <div class="info-card">
                <h4>🔒 安全状态</h4>
                <p>JWT认证启用</p>
            </div>
            <div class="info-card">
                <h4>📊 API版本</h4>
                <p>v1.0.0</p>
            </div>
        </div>

        <div class="api-section">
            <h3>🔧 API接口测试</h3>

            <div class="api-test">
                <h4>健康检查</h4>
                <button onclick="testHealth()">测试 GET /api/health</button>
                <div id="health-result" class="result"></div>
            </div>

            <div class="api-test">
                <h4>用户登录</h4>
                <button onclick="testLogin()">测试 POST /api/auth/login</button>
                <div id="login-result" class="result"></div>
            </div>

            <div class="api-test">
                <h4>项目列表</h4>
                <button onclick="testProjects()">测试 GET /api/projects</button>
                <div id="projects-result" class="result"></div>
            </div>

            <div class="api-test">
                <h4>工单列表</h4>
                <button onclick="testWorkOrders()">测试 GET /api/work-orders</button>
                <div id="workorders-result" class="result"></div>
            </div>
        </div>
    </div>

    <script>
        async function testHealth() {
            const result = document.getElementById('health-result');
            result.textContent = '正在测试...';
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = '错误: ' + error.message;
            }
        }

        async function testLogin() {
            const result = document.getElementById('login-result');
            result.textContent = '正在测试...';
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = '错误: ' + error.message;
            }
        }

        async function testProjects() {
            const result = document.getElementById('projects-result');
            result.textContent = '正在测试...';
            try {
                const response = await fetch('/api/projects');
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = '错误: ' + error.message;
            }
        }

        async function testWorkOrders() {
            const result = document.getElementById('workorders-result');
            result.textContent = '正在测试...';
            try {
                const response = await fetch('/api/work-orders');
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = '错误: ' + error.message;
            }
        }

        // 页面加载时自动测试健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
    "#)
}

#[tokio::main]
async fn main() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 创建路由
    let app = Router::new()
        .route("/", get(web_interface))
        .route("/api/health", get(health_check))
        .route("/api/auth/login", post(login))
        .route("/api/projects", get(get_projects))
        .route("/api/work-orders", get(get_work_orders))
        .layer(CorsLayer::permissive());

    // 启动服务器
    let listener = tokio::net::TcpListener::bind("0.0.0.0:3000")
        .await
        .expect("Failed to bind to port 3000");

    println!("🚀 MES系统启动成功!");
    println!("📡 服务器监听地址: http://0.0.0.0:3000");
    println!("🌐 Web界面: http://************:3000");
    println!("🔗 API健康检查: http://************:3000/api/health");
    println!("💡 测试登录: POST /api/auth/login (admin/admin123)");
    println!("📱 局域网访问: 在其他设备浏览器中打开 http://************:3000");

    axum::serve(listener, app)
        .await
        .expect("Failed to start server");
}
