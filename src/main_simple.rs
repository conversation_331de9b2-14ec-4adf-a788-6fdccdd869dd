use axum::{
    extract::Query,
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tower_http::cors::CorsLayer;

#[derive(Debug, Serialize)]
struct ApiResponse<T> {
    success: bool,
    data: Option<T>,
    message: String,
}

#[derive(Debug, Serialize)]
struct HealthStatus {
    status: String,
    timestamp: String,
    version: String,
}

#[derive(Debug, Deserialize)]
struct LoginRequest {
    username: String,
    password: String,
}

#[derive(Debug, Serialize)]
struct LoginResponse {
    token: String,
    user: UserInfo,
}

#[derive(Debug, Serialize)]
struct UserInfo {
    id: i32,
    username: String,
    full_name: String,
    roles: Vec<String>,
}

async fn health_check() -> Json<ApiResponse<HealthStatus>> {
    Json(ApiResponse {
        success: true,
        data: Some(HealthStatus {
            status: "healthy".to_string(),
            timestamp: chrono::Utc::now().to_rfc3339(),
            version: "1.0.0".to_string(),
        }),
        message: "MES系统运行正常".to_string(),
    })
}

async fn login(Json(request): Json<LoginRequest>) -> Result<Json<ApiResponse<LoginResponse>>, StatusCode> {
    // 简单的演示登录
    if request.username == "admin" && request.password == "admin123" {
        Ok(Json(ApiResponse {
            success: true,
            data: Some(LoginResponse {
                token: "demo-jwt-token-12345".to_string(),
                user: UserInfo {
                    id: 1,
                    username: "admin".to_string(),
                    full_name: "系统管理员".to_string(),
                    roles: vec!["ADMIN".to_string()],
                },
            }),
            message: "登录成功".to_string(),
        }))
    } else {
        Err(StatusCode::UNAUTHORIZED)
    }
}

async fn get_projects() -> Json<ApiResponse<Vec<HashMap<String, serde_json::Value>>>> {
    let projects = vec![
        {
            let mut project = HashMap::new();
            project.insert("id".to_string(), serde_json::Value::Number(1.into()));
            project.insert("project_name".to_string(), serde_json::Value::String("示例项目A".to_string()));
            project.insert("customer_name".to_string(), serde_json::Value::String("客户A".to_string()));
            project.insert("created_at".to_string(), serde_json::Value::String("2024-01-01T00:00:00Z".to_string()));
            project
        },
        {
            let mut project = HashMap::new();
            project.insert("id".to_string(), serde_json::Value::Number(2.into()));
            project.insert("project_name".to_string(), serde_json::Value::String("示例项目B".to_string()));
            project.insert("customer_name".to_string(), serde_json::Value::String("客户B".to_string()));
            project.insert("created_at".to_string(), serde_json::Value::String("2024-01-02T00:00:00Z".to_string()));
            project
        },
    ];

    Json(ApiResponse {
        success: true,
        data: Some(projects),
        message: "获取项目列表成功".to_string(),
    })
}

async fn get_work_orders() -> Json<ApiResponse<Vec<HashMap<String, serde_json::Value>>>> {
    let work_orders = vec![
        {
            let mut wo = HashMap::new();
            wo.insert("id".to_string(), serde_json::Value::Number(1.into()));
            wo.insert("project_name".to_string(), serde_json::Value::String("示例项目A".to_string()));
            wo.insert("part_number".to_string(), serde_json::Value::String("PART-001".to_string()));
            wo.insert("quantity".to_string(), serde_json::Value::Number(100.into()));
            wo.insert("status".to_string(), serde_json::Value::String("IN_PROGRESS".to_string()));
            wo
        },
    ];

    Json(ApiResponse {
        success: true,
        data: Some(work_orders),
        message: "获取工单列表成功".to_string(),
    })
}

#[tokio::main]
async fn main() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    // 创建路由
    let app = Router::new()
        .route("/api/health", get(health_check))
        .route("/api/auth/login", post(login))
        .route("/api/projects", get(get_projects))
        .route("/api/work-orders", get(get_work_orders))
        .layer(CorsLayer::permissive());

    // 启动服务器
    let listener = tokio::net::TcpListener::bind("0.0.0.0:3000")
        .await
        .expect("Failed to bind to port 3000");

    println!("🚀 MES系统启动成功!");
    println!("📡 服务器监听地址: http://0.0.0.0:3000");
    println!("🔗 API文档: http://0.0.0.0:3000/api/health");
    println!("💡 测试登录: POST /api/auth/login (admin/admin123)");

    axum::serve(listener, app)
        .await
        .expect("Failed to start server");
}
