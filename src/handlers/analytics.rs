use axum::{
    extract::{Query, State},
    response::Json,
    Extension,
};
use validator::Validate;
use chrono::{DateTime, Utc, NaiveDate};

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::{
        role::Role,
        analytics::{
            ProductionEfficiency, EquipmentUtilization, QualityMetrics,
            OperatorEfficiency, ProjectProgress, CostAnalysis, KPIDashboard,
            TrendData, ReportQuery, RealTimeMetrics, SystemAlert,
            PredictionData, ComparisonAnalysis
        },
    },
    require_any_role,
};

pub async fn get_kpi_dashboard(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<ReportQuery>,
) -> Result<Json<KPIDashboard>> {
    // 管理员和计划员可以查看KPI仪表板
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    query.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    let period = format!("{} to {}", query.start_date, query.end_date);

    // 生产效率分析
    let production_efficiency = get_production_efficiency(&database, &query).await?;
    
    // 质量指标
    let quality_metrics = get_quality_metrics(&database, &query).await?;
    
    // 设备利用率
    let equipment_utilization = get_equipment_utilization(&database, &query).await?;
    
    // 优秀操作员
    let top_performers = get_top_performers(&database, &query).await?;
    
    // 项目状态
    let project_status = get_project_progress(&database, &query).await?;
    
    // 成本摘要
    let cost_summary = get_cost_analysis(&database, &query).await?;

    let dashboard = KPIDashboard {
        period,
        production_efficiency,
        quality_metrics,
        equipment_utilization,
        top_performers,
        project_status,
        cost_summary,
    };

    Ok(Json(dashboard))
}

async fn get_production_efficiency(database: &Database, query: &ReportQuery) -> Result<ProductionEfficiency> {
    let efficiency_data = sqlx::query!(
        r#"
        SELECT 
            COALESCE(SUM(EXTRACT(EPOCH FROM (pt.planned_end - pt.planned_start)) / 3600), 0) as planned_hours,
            COALESCE(SUM(
                CASE 
                    WHEN el_start.event_time IS NOT NULL AND el_end.event_time IS NOT NULL 
                    THEN EXTRACT(EPOCH FROM (el_end.event_time - el_start.event_time)) / 3600
                    ELSE 0
                END
            ), 0) as actual_hours,
            COUNT(CASE WHEN pt.status = 'COMPLETED' THEN 1 END) as completed_tasks,
            COUNT(*) as total_tasks
        FROM plan_tasks pt
        LEFT JOIN (
            SELECT DISTINCT ON (plan_task_id) plan_task_id, event_time
            FROM execution_logs 
            WHERE event_type = 'START'
            ORDER BY plan_task_id, event_time
        ) el_start ON pt.id = el_start.plan_task_id
        LEFT JOIN (
            SELECT DISTINCT ON (plan_task_id) plan_task_id, event_time
            FROM execution_logs 
            WHERE event_type = 'COMPLETE'
            ORDER BY plan_task_id, event_time DESC
        ) el_end ON pt.id = el_end.plan_task_id
        WHERE pt.planned_start::date BETWEEN $1 AND $2
        "#,
        query.start_date,
        query.end_date
    )
    .fetch_one(&database.pool)
    .await?;

    let planned_hours = efficiency_data.planned_hours.unwrap_or(0.0);
    let actual_hours = efficiency_data.actual_hours.unwrap_or(0.0);
    let completed_tasks = efficiency_data.completed_tasks.unwrap_or(0) as i32;
    let total_tasks = efficiency_data.total_tasks.unwrap_or(0) as i32;

    let efficiency_rate = if planned_hours > 0.0 {
        (planned_hours / actual_hours.max(0.1)) * 100.0
    } else {
        0.0
    };

    let completion_rate = if total_tasks > 0 {
        (completed_tasks as f64 / total_tasks as f64) * 100.0
    } else {
        0.0
    };

    Ok(ProductionEfficiency {
        period: format!("{} to {}", query.start_date, query.end_date),
        planned_hours,
        actual_hours,
        efficiency_rate,
        completed_tasks,
        total_tasks,
        completion_rate,
    })
}

async fn get_quality_metrics(database: &Database, query: &ReportQuery) -> Result<QualityMetrics> {
    let quality_data = sqlx::query!(
        r#"
        SELECT 
            COUNT(*) as total_parts,
            COUNT(CASE WHEN el.event_type = 'COMPLETE' AND el.notes NOT ILIKE '%返工%' AND el.notes NOT ILIKE '%报废%' THEN 1 END) as passed_parts,
            COUNT(CASE WHEN el.event_type = 'REWORK' OR el.notes ILIKE '%返工%' THEN 1 END) as rework_parts,
            COUNT(CASE WHEN el.event_type = 'SCRAP' OR el.notes ILIKE '%报废%' THEN 1 END) as scrap_parts
        FROM execution_logs el
        JOIN plan_tasks pt ON el.plan_task_id = pt.id
        WHERE el.event_time::date BETWEEN $1 AND $2
        AND el.event_type IN ('COMPLETE', 'QUALITY_CHECK', 'REWORK', 'SCRAP')
        "#,
        query.start_date,
        query.end_date
    )
    .fetch_one(&database.pool)
    .await?;

    let total_parts = quality_data.total_parts.unwrap_or(0) as i32;
    let passed_parts = quality_data.passed_parts.unwrap_or(0) as i32;
    let rework_parts = quality_data.rework_parts.unwrap_or(0) as i32;
    let scrap_parts = quality_data.scrap_parts.unwrap_or(0) as i32;
    let failed_parts = rework_parts + scrap_parts;

    let first_pass_yield = if total_parts > 0 {
        (passed_parts as f64 / total_parts as f64) * 100.0
    } else {
        0.0
    };

    let overall_yield = if total_parts > 0 {
        ((passed_parts + rework_parts) as f64 / total_parts as f64) * 100.0
    } else {
        0.0
    };

    Ok(QualityMetrics {
        period: format!("{} to {}", query.start_date, query.end_date),
        total_parts,
        passed_parts,
        failed_parts,
        rework_parts,
        scrap_parts,
        first_pass_yield,
        overall_yield,
    })
}

async fn get_equipment_utilization(database: &Database, query: &ReportQuery) -> Result<Vec<EquipmentUtilization>> {
    let equipment_data = sqlx::query!(
        r#"
        SELECT 
            m.id as machine_id,
            m.machine_name,
            sg.group_name as skill_group,
            COALESCE(SUM(
                CASE 
                    WHEN el_start.event_time IS NOT NULL AND el_end.event_time IS NOT NULL 
                    THEN EXTRACT(EPOCH FROM (el_end.event_time - el_start.event_time)) / 3600
                    ELSE 0
                END
            ), 0) as actual_working_hours
        FROM machines m
        JOIN skill_groups sg ON m.skill_group_id = sg.id
        LEFT JOIN execution_logs el_start ON m.id = el_start.machine_id AND el_start.event_type = 'START'
        LEFT JOIN execution_logs el_end ON m.id = el_end.machine_id AND el_end.event_type = 'COMPLETE'
            AND el_end.plan_task_id = el_start.plan_task_id
        WHERE (el_start.event_time IS NULL OR el_start.event_time::date BETWEEN $1 AND $2)
        GROUP BY m.id, m.machine_name, sg.group_name
        ORDER BY actual_working_hours DESC
        "#,
        query.start_date,
        query.end_date
    )
    .fetch_all(&database.pool)
    .await?;

    let mut utilization_list = Vec::new();
    let days_in_period = (query.end_date - query.start_date).num_days() + 1;
    let total_available_hours = (days_in_period as f64) * 8.0; // 假设每天8小时工作

    for equipment in equipment_data {
        let actual_working_hours = equipment.actual_working_hours.unwrap_or(0.0);
        let utilization_rate = if total_available_hours > 0.0 {
            (actual_working_hours / total_available_hours) * 100.0
        } else {
            0.0
        };

        utilization_list.push(EquipmentUtilization {
            machine_id: equipment.machine_id,
            machine_name: equipment.machine_name,
            skill_group: equipment.skill_group,
            total_available_hours,
            actual_working_hours,
            utilization_rate,
            maintenance_hours: 0.0, // 可以从维护记录中计算
            idle_hours: total_available_hours - actual_working_hours,
        });
    }

    Ok(utilization_list)
}

async fn get_top_performers(database: &Database, query: &ReportQuery) -> Result<Vec<OperatorEfficiency>> {
    let operator_data = sqlx::query!(
        r#"
        SELECT
            u.id as user_id,
            u.username,
            u.full_name,
            COUNT(CASE WHEN pt.status = 'COMPLETED' THEN 1 END) as completed_tasks,
            COALESCE(SUM(
                CASE
                    WHEN el_start.event_time IS NOT NULL AND el_end.event_time IS NOT NULL
                    THEN EXTRACT(EPOCH FROM (el_end.event_time - el_start.event_time)) / 3600
                    ELSE 0
                END
            ), 0) as total_working_hours,
            ARRAY_AGG(DISTINCT sg.group_name) as skill_groups
        FROM users u
        JOIN execution_logs el ON u.id = el.user_id
        JOIN plan_tasks pt ON el.plan_task_id = pt.id
        LEFT JOIN user_skills us ON u.id = us.user_id
        LEFT JOIN skill_groups sg ON us.skill_group_id = sg.id
        LEFT JOIN execution_logs el_start ON pt.id = el_start.plan_task_id AND el_start.event_type = 'START'
        LEFT JOIN execution_logs el_end ON pt.id = el_end.plan_task_id AND el_end.event_type = 'COMPLETE'
        WHERE el.event_time::date BETWEEN $1 AND $2
        GROUP BY u.id, u.username, u.full_name
        HAVING COUNT(CASE WHEN pt.status = 'COMPLETED' THEN 1 END) > 0
        ORDER BY completed_tasks DESC, total_working_hours ASC
        LIMIT 10
        "#,
        query.start_date,
        query.end_date
    )
    .fetch_all(&database.pool)
    .await?;

    let mut performers = Vec::new();
    for operator in operator_data {
        let completed_tasks = operator.completed_tasks.unwrap_or(0) as i32;
        let total_working_hours = operator.total_working_hours.unwrap_or(0.0);
        let average_task_time = if completed_tasks > 0 {
            total_working_hours / completed_tasks as f64
        } else {
            0.0
        };

        // 简单的效率评分：完成任务数 / 平均任务时间
        let efficiency_score = if average_task_time > 0.0 {
            completed_tasks as f64 / average_task_time
        } else {
            0.0
        };

        performers.push(OperatorEfficiency {
            user_id: operator.user_id,
            username: operator.username,
            full_name: operator.full_name,
            skill_groups: operator.skill_groups.unwrap_or_default(),
            completed_tasks,
            total_working_hours,
            average_task_time,
            efficiency_score,
        });
    }

    Ok(performers)
}

async fn get_project_progress(database: &Database, query: &ReportQuery) -> Result<Vec<ProjectProgress>> {
    let project_data = sqlx::query!(
        r#"
        SELECT
            p.id as project_id,
            p.project_name,
            p.customer_name,
            COUNT(wo.id) as total_work_orders,
            COUNT(CASE WHEN wo.status = 'COMPLETED' THEN 1 END) as completed_work_orders,
            COUNT(CASE WHEN wo.status = 'IN_PROGRESS' THEN 1 END) as in_progress_work_orders,
            COUNT(CASE WHEN wo.status IN ('PENDING', 'PLANNED') THEN 1 END) as pending_work_orders
        FROM projects p
        LEFT JOIN project_boms pb ON p.id = pb.project_id
        LEFT JOIN work_orders wo ON pb.id = wo.project_bom_id
        WHERE p.created_at::date <= $2
        GROUP BY p.id, p.project_name, p.customer_name
        ORDER BY p.created_at DESC
        "#,
        query.end_date
    )
    .fetch_all(&database.pool)
    .await?;

    let mut progress_list = Vec::new();
    for project in project_data {
        let total_work_orders = project.total_work_orders.unwrap_or(0) as i32;
        let completed_work_orders = project.completed_work_orders.unwrap_or(0) as i32;
        let in_progress_work_orders = project.in_progress_work_orders.unwrap_or(0) as i32;
        let pending_work_orders = project.pending_work_orders.unwrap_or(0) as i32;

        let overall_progress = if total_work_orders > 0 {
            (completed_work_orders as f64 / total_work_orders as f64) * 100.0
        } else {
            0.0
        };

        progress_list.push(ProjectProgress {
            project_id: project.project_id,
            project_name: project.project_name,
            customer_name: project.customer_name,
            total_work_orders,
            completed_work_orders,
            in_progress_work_orders,
            pending_work_orders,
            overall_progress,
            estimated_completion: None, // 可以基于当前进度和历史数据计算
            is_on_schedule: overall_progress >= 50.0, // 简单的判断逻辑
        });
    }

    Ok(progress_list)
}

async fn get_cost_analysis(database: &Database, query: &ReportQuery) -> Result<CostAnalysis> {
    // 这里是一个简化的成本分析，实际应用中需要更复杂的成本计算
    let cost_data = sqlx::query!(
        r#"
        SELECT
            COUNT(DISTINCT el.user_id) as active_operators,
            COALESCE(SUM(
                CASE
                    WHEN el_start.event_time IS NOT NULL AND el_end.event_time IS NOT NULL
                    THEN EXTRACT(EPOCH FROM (el_end.event_time - el_start.event_time)) / 3600
                    ELSE 0
                END
            ), 0) as total_labor_hours,
            COUNT(CASE WHEN pt.status = 'COMPLETED' THEN 1 END) as completed_tasks
        FROM execution_logs el
        JOIN plan_tasks pt ON el.plan_task_id = pt.id
        LEFT JOIN execution_logs el_start ON pt.id = el_start.plan_task_id AND el_start.event_type = 'START'
        LEFT JOIN execution_logs el_end ON pt.id = el_end.plan_task_id AND el_end.event_type = 'COMPLETE'
        WHERE el.event_time::date BETWEEN $1 AND $2
        "#,
        query.start_date,
        query.end_date
    )
    .fetch_one(&database.pool)
    .await?;

    let total_labor_hours = cost_data.total_labor_hours.unwrap_or(0.0);
    let completed_tasks = cost_data.completed_tasks.unwrap_or(0) as i32;

    // 假设的成本参数
    let hourly_rate = 50.0; // 每小时人工成本
    let material_cost_per_task = 100.0; // 每个任务的材料成本
    let overhead_rate = 0.3; // 30%的间接费用

    let labor_cost = total_labor_hours * hourly_rate;
    let material_cost = completed_tasks as f64 * material_cost_per_task;
    let overhead_cost = (labor_cost + material_cost) * overhead_rate;
    let total_cost = labor_cost + material_cost + overhead_cost;

    let cost_per_unit = if completed_tasks > 0 {
        total_cost / completed_tasks as f64
    } else {
        0.0
    };

    Ok(CostAnalysis {
        period: format!("{} to {}", query.start_date, query.end_date),
        labor_cost,
        material_cost,
        overhead_cost,
        total_cost,
        cost_per_unit,
        budget_variance: 0.0, // 需要预算数据来计算
    })
}

pub async fn get_trend_analysis(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<ReportQuery>,
) -> Result<Json<Vec<TrendData>>> {
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    query.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    let group_by = query.group_by.as_deref().unwrap_or("day");

    let date_format = match group_by {
        "week" => "YYYY-\"W\"WW",
        "month" => "YYYY-MM",
        _ => "YYYY-MM-DD",
    };

    let trend_data = sqlx::query!(
        r#"
        SELECT
            TO_CHAR(pt.planned_start, $3) as period,
            COUNT(CASE WHEN pt.status = 'COMPLETED' THEN 1 END) as completed_tasks,
            COUNT(*) as total_tasks,
            COALESCE(AVG(
                CASE
                    WHEN el_start.event_time IS NOT NULL AND el_end.event_time IS NOT NULL
                    THEN EXTRACT(EPOCH FROM (el_end.event_time - el_start.event_time)) / 3600
                    ELSE NULL
                END
            ), 0) as avg_task_duration
        FROM plan_tasks pt
        LEFT JOIN execution_logs el_start ON pt.id = el_start.plan_task_id AND el_start.event_type = 'START'
        LEFT JOIN execution_logs el_end ON pt.id = el_end.plan_task_id AND el_end.event_type = 'COMPLETE'
        WHERE pt.planned_start::date BETWEEN $1 AND $2
        GROUP BY TO_CHAR(pt.planned_start, $3)
        ORDER BY period
        "#,
        query.start_date,
        query.end_date,
        date_format
    )
    .fetch_all(&database.pool)
    .await?;

    let mut trends = Vec::new();
    for data in trend_data {
        let period_str = data.period.unwrap_or_default();
        let completed_tasks = data.completed_tasks.unwrap_or(0) as f64;
        let total_tasks = data.total_tasks.unwrap_or(0) as f64;
        let avg_duration = data.avg_task_duration.unwrap_or(0.0);

        // 完成率趋势
        if total_tasks > 0.0 {
            trends.push(TrendData {
                date: query.start_date, // 简化处理，实际应该解析period_str
                value: (completed_tasks / total_tasks) * 100.0,
                target: Some(85.0), // 目标完成率85%
                category: "completion_rate".to_string(),
            });
        }

        // 平均任务时长趋势
        trends.push(TrendData {
            date: query.start_date,
            value: avg_duration,
            target: Some(6.0), // 目标平均6小时完成
            category: "avg_duration".to_string(),
        });
    }

    Ok(Json(trends))
}

pub async fn get_real_time_metrics(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<RealTimeMetrics>> {
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    let metrics_data = sqlx::query!(
        r#"
        SELECT
            COUNT(CASE WHEN pt.status = 'IN_PROGRESS' THEN 1 END) as active_tasks,
            COUNT(DISTINCT el.user_id) as active_operators,
            COUNT(DISTINCT el.machine_id) as machines_in_use
        FROM plan_tasks pt
        LEFT JOIN execution_logs el ON pt.id = el.plan_task_id
            AND el.event_time >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
        WHERE pt.status IN ('IN_PROGRESS', 'ASSIGNED')
        "#
    )
    .fetch_one(&database.pool)
    .await?;

    let active_tasks = metrics_data.active_tasks.unwrap_or(0) as i32;
    let active_operators = metrics_data.active_operators.unwrap_or(0) as i32;
    let machines_in_use = metrics_data.machines_in_use.unwrap_or(0) as i32;

    // 简单的效率计算
    let current_efficiency = if active_operators > 0 {
        (active_tasks as f64 / active_operators as f64) * 100.0
    } else {
        0.0
    };

    // 生成一些示例告警
    let mut alerts = Vec::new();

    // 检查延期任务
    let overdue_tasks = sqlx::query_scalar!(
        r#"
        SELECT COUNT(*)
        FROM plan_tasks pt
        WHERE pt.status IN ('PLANNED', 'ASSIGNED', 'IN_PROGRESS')
        AND pt.planned_end < CURRENT_TIMESTAMP
        "#
    )
    .fetch_one(&database.pool)
    .await?;

    if let Some(overdue_count) = overdue_tasks {
        if overdue_count > 0 {
            alerts.push(SystemAlert {
                id: format!("DELAY_{}", Utc::now().timestamp()),
                alert_type: SystemAlert::TYPE_DELAY.to_string(),
                severity: if overdue_count > 5 {
                    SystemAlert::SEVERITY_HIGH.to_string()
                } else {
                    SystemAlert::SEVERITY_MEDIUM.to_string()
                },
                message: format!("有 {} 个任务已延期", overdue_count),
                entity_type: "TASK".to_string(),
                entity_id: 0,
                created_at: Utc::now(),
                acknowledged: false,
            });
        }
    }

    // 检查设备利用率
    if machines_in_use < 3 {
        alerts.push(SystemAlert {
            id: format!("RESOURCE_{}", Utc::now().timestamp()),
            alert_type: SystemAlert::TYPE_RESOURCE.to_string(),
            severity: SystemAlert::SEVERITY_LOW.to_string(),
            message: "设备利用率较低，建议优化调度".to_string(),
            entity_type: "EQUIPMENT".to_string(),
            entity_id: 0,
            created_at: Utc::now(),
            acknowledged: false,
        });
    }

    Ok(Json(RealTimeMetrics {
        timestamp: Utc::now(),
        active_tasks,
        active_operators,
        machines_in_use,
        current_efficiency,
        alerts,
    }))
}

pub async fn get_comparison_analysis(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<ReportQuery>,
) -> Result<Json<Vec<ComparisonAnalysis>>> {
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    query.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 计算前一个周期的日期范围
    let period_days = (query.end_date - query.start_date).num_days();
    let prev_end_date = query.start_date - chrono::Duration::days(1);
    let prev_start_date = prev_end_date - chrono::Duration::days(period_days);

    // 当前周期数据
    let current_efficiency = get_production_efficiency(&database, &query).await?;

    // 前一周期数据
    let prev_query = ReportQuery {
        start_date: prev_start_date.date_naive(),
        end_date: prev_end_date.date_naive(),
        report_type: query.report_type.clone(),
        group_by: query.group_by.clone(),
        project_id: query.project_id,
        skill_group_id: query.skill_group_id,
        user_id: query.user_id,
    };
    let prev_efficiency = get_production_efficiency(&database, &prev_query).await?;

    let mut comparisons = Vec::new();

    // 效率对比
    let efficiency_change = current_efficiency.efficiency_rate - prev_efficiency.efficiency_rate;
    comparisons.push(ComparisonAnalysis {
        metric_name: "生产效率".to_string(),
        current_period: current_efficiency.efficiency_rate,
        previous_period: prev_efficiency.efficiency_rate,
        change_percentage: if prev_efficiency.efficiency_rate > 0.0 {
            (efficiency_change / prev_efficiency.efficiency_rate) * 100.0
        } else {
            0.0
        },
        trend: if efficiency_change > 0.0 { "UP".to_string() }
               else if efficiency_change < 0.0 { "DOWN".to_string() }
               else { "STABLE".to_string() },
        is_improvement: efficiency_change > 0.0,
    });

    // 完成率对比
    let completion_change = current_efficiency.completion_rate - prev_efficiency.completion_rate;
    comparisons.push(ComparisonAnalysis {
        metric_name: "任务完成率".to_string(),
        current_period: current_efficiency.completion_rate,
        previous_period: prev_efficiency.completion_rate,
        change_percentage: if prev_efficiency.completion_rate > 0.0 {
            (completion_change / prev_efficiency.completion_rate) * 100.0
        } else {
            0.0
        },
        trend: if completion_change > 0.0 { "UP".to_string() }
               else if completion_change < 0.0 { "DOWN".to_string() }
               else { "STABLE".to_string() },
        is_improvement: completion_change > 0.0,
    });

    Ok(Json(comparisons))
}
