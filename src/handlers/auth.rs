use axum::{
    extract::State,
    http::StatusCode,
    response::<PERSON><PERSON>,
    Extension,
};
use bcrypt::{hash, verify, DEFAULT_COST};
use validator::Validate;

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::{create_token, CurrentUser},
    models::user::{LoginRequest, LoginResponse, UserResponse},
};

pub async fn login(
    State(database): State<Database>,
    <PERSON><PERSON>(request): <PERSON><PERSON><LoginRequest>,
) -> Result<Json<LoginResponse>> {
    // 验证请求数据
    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 查找用户
    let user = sqlx::query!(
        "SELECT id, username, password_hash, full_name, is_active, created_at FROM users WHERE username = $1",
        request.username
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::Authentication("Invalid username or password".to_string()))?;

    // 检查用户是否激活
    if !user.is_active {
        return Err(AppError::Authentication("Account is inactive".to_string()));
    }

    // 验证密码
    if !verify(&request.password, &user.password_hash)
        .map_err(|_| AppError::Internal("Password verification failed".to_string()))?
    {
        return Err(AppError::Authentication("Invalid username or password".to_string()));
    }

    // 获取用户角色
    let roles = sqlx::query_scalar!(
        r#"
        SELECT r.role_name
        FROM roles r
        JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = $1
        "#,
        user.id
    )
    .fetch_all(&database.pool)
    .await?;

    // 获取用户技能
    let skills = sqlx::query_scalar!(
        r#"
        SELECT sg.group_name
        FROM skill_groups sg
        JOIN user_skills us ON sg.id = us.skill_group_id
        WHERE us.user_id = $1
        "#,
        user.id
    )
    .fetch_all(&database.pool)
    .await?;

    // 创建JWT令牌
    let token = create_token(user.id, &user.username, roles.clone(), skills.clone())?;

    // 构建响应
    let user_response = UserResponse {
        id: user.id,
        username: user.username,
        full_name: user.full_name,
        is_active: user.is_active,
        created_at: user.created_at,
        roles,
        skills,
    };

    Ok(Json(LoginResponse {
        token,
        user: user_response,
    }))
}

pub async fn me(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
) -> Result<Json<UserResponse>> {
    // 获取最新的用户信息
    let user = sqlx::query!(
        "SELECT id, username, full_name, is_active, created_at FROM users WHERE id = $1",
        current_user.id
    )
    .fetch_one(&database.pool)
    .await?;

    let user_response = UserResponse {
        id: user.id,
        username: user.username,
        full_name: user.full_name,
        is_active: user.is_active,
        created_at: user.created_at,
        roles: current_user.roles,
        skills: current_user.skills,
    };

    Ok(Json(user_response))
}

pub async fn change_password(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<ChangePasswordRequest>,
) -> Result<StatusCode> {
    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 获取当前密码哈希
    let current_hash: String = sqlx::query_scalar!(
        "SELECT password_hash FROM users WHERE id = $1",
        current_user.id
    )
    .fetch_one(&database.pool)
    .await?;

    // 验证当前密码
    if !verify(&request.current_password, &current_hash)
        .map_err(|_| AppError::Internal("Password verification failed".to_string()))?
    {
        return Err(AppError::Authentication("Current password is incorrect".to_string()));
    }

    // 哈希新密码
    let new_hash = hash(&request.new_password, DEFAULT_COST)
        .map_err(|_| AppError::Internal("Password hashing failed".to_string()))?;

    // 更新密码
    sqlx::query!(
        "UPDATE users SET password_hash = $1 WHERE id = $2",
        new_hash,
        current_user.id
    )
    .execute(&database.pool)
    .await?;

    Ok(StatusCode::OK)
}

#[derive(serde::Deserialize, validator::Validate)]
pub struct ChangePasswordRequest {
    #[validate(length(min = 1))]
    pub current_password: String,
    #[validate(length(min = 6))]
    pub new_password: String,
}
