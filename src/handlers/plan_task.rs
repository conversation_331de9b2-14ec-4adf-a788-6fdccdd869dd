use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::Json,
    Extension,
};
use validator::Validate;
use chrono::{DateTime, Utc, Duration};

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::{
        role::Role,
        plan_task::{
            PlanTask, PlanTaskWithDetails, GanttTask,
            CreatePlanTaskRequest, UpdatePlanTaskRequest, PlanTaskQuery,
            ScheduleRequest, ScheduleResult, ScheduleConflict
        },
    },
    require_any_role,
};

pub async fn list_plan_tasks(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<PlanTaskQuery>,
) -> Result<Json<Vec<PlanTaskWithDetails>>> {
    // 计划员、工艺员和管理员可以查看计划任务
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    let limit = query.limit.unwrap_or(50).min(100) as i64;
    let offset = (query.page.unwrap_or(1) - 1) * (limit as u32) as i64;

    let plan_tasks = sqlx::query_as!(
        PlanTaskWithDetails,
        r#"
        SELECT
            pt.id,
            pt.work_order_id,
            pt.routing_step_id,
            r.step_number,
            r.process_name,
            pt.skill_group_id,
            sg.group_name as skill_group_name,
            pt.planned_start,
            pt.planned_end,
            pt.status,
            p.project_name,
            parts.part_number,
            parts.part_name,
            wo.quantity as work_order_quantity,
            r.standard_hours
        FROM plan_tasks pt
        JOIN routings r ON pt.routing_step_id = r.id
        JOIN skill_groups sg ON pt.skill_group_id = sg.id
        JOIN work_orders wo ON pt.work_order_id = wo.id
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts ON pb.part_id = parts.id
        ORDER BY pt.planned_start
        LIMIT $1 OFFSET $2
        "#,
        limit,
        offset
    )
    .fetch_all(&database.pool)
    .await?;

    Ok(Json(plan_tasks))
}

pub async fn get_gantt_data(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<PlanTaskQuery>,
) -> Result<Json<Vec<GanttTask>>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    let plan_tasks = sqlx::query!(
        r#"
        SELECT
            pt.id,
            pt.planned_start,
            pt.planned_end,
            pt.status,
            r.step_number,
            r.process_name,
            sg.group_name as skill_group_name,
            p.project_name,
            parts.part_number,
            parts.part_name
        FROM plan_tasks pt
        JOIN routings r ON pt.routing_step_id = r.id
        JOIN skill_groups sg ON pt.skill_group_id = sg.id
        JOIN work_orders wo ON pt.work_order_id = wo.id
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts ON pb.part_id = parts.id
        WHERE pt.status != 'CANCELLED'
        ORDER BY pt.planned_start
        "#
    )
    .fetch_all(&database.pool)
    .await?;

    let mut gantt_tasks = Vec::new();
    for task in plan_tasks {
        let progress = match task.status.as_str() {
            "COMPLETED" => 100.0,
            "IN_PROGRESS" => 50.0,
            "ASSIGNED" => 25.0,
            _ => 0.0,
        };

        let task_name = format!(
            "{} - {} (步骤{})",
            task.part_number,
            task.process_name,
            task.step_number
        );

        gantt_tasks.push(GanttTask {
            id: task.id,
            name: task_name,
            start: task.planned_start,
            end: task.planned_end,
            progress,
            dependencies: Vec::new(), // 后续可以实现依赖关系
            resource: task.skill_group_name,
            project: task.project_name,
            part: format!("{} {}", task.part_number, task.part_name.unwrap_or_default()),
            process: task.process_name,
        });
    }

    Ok(Json(gantt_tasks))
}

pub async fn create_plan_task(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<CreatePlanTaskRequest>,
) -> Result<Json<PlanTask>> {
    // 只有计划员和管理员可以创建计划任务
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查工单是否存在
    sqlx::query_scalar!(
        "SELECT id FROM work_orders WHERE id = $1",
        request.work_order_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Work order not found".to_string()))?;

    // 检查工艺步骤是否存在
    sqlx::query_scalar!(
        "SELECT id FROM routings WHERE id = $1",
        request.routing_step_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Routing step not found".to_string()))?;

    // 检查技能组是否存在
    sqlx::query_scalar!(
        "SELECT id FROM skill_groups WHERE id = $1",
        request.skill_group_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Skill group not found".to_string()))?;

    let plan_task = sqlx::query_as!(
        PlanTask,
        r#"
        INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING id, work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status
        "#,
        request.work_order_id,
        request.routing_step_id,
        request.skill_group_id,
        request.planned_start,
        request.planned_end,
        PlanTask::STATUS_PLANNED
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(plan_task))
}

pub async fn auto_schedule(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<ScheduleRequest>,
) -> Result<Json<ScheduleResult>> {
    // 只有计划员和管理员可以执行自动调度
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    let mut scheduled_tasks = Vec::new();
    let mut conflicts = Vec::new();
    let mut current_time = request.start_date.and_hms_opt(8, 0, 0).unwrap().and_utc();

    // 开始事务
    let mut tx = database.pool.begin().await?;

    for work_order_id in &request.work_order_ids {
        // 获取工单的所有工艺步骤
        let routing_steps = sqlx::query!(
            r#"
            SELECT
                r.id as routing_id,
                r.step_number,
                r.process_name,
                r.standard_hours,
                sg.id as skill_group_id,
                sg.group_name as skill_group_name
            FROM work_orders wo
            JOIN project_boms pb ON wo.project_bom_id = pb.id
            JOIN parts p ON pb.part_id = p.id
            JOIN routings r ON p.id = r.part_id
            JOIN skill_groups sg ON sg.group_name = (
                CASE
                    WHEN r.process_name ILIKE '%CNC%' OR r.process_name ILIKE '%数控%' THEN 'CNC Machining'
                    WHEN r.process_name ILIKE '%手工%' OR r.process_name ILIKE '%磨削%' THEN 'Manual Machining'
                    WHEN r.process_name ILIKE '%装配%' THEN 'Assembly'
                    WHEN r.process_name ILIKE '%检验%' OR r.process_name ILIKE '%测量%' THEN 'Quality Control'
                    ELSE 'Manual Machining'
                END
            )
            WHERE wo.id = $1
            ORDER BY r.step_number
            "#,
            work_order_id
        )
        .fetch_all(&mut *tx)
        .await?;

        let mut step_start_time = current_time;

        for step in routing_steps {
            let standard_hours = step.standard_hours.unwrap_or_else(|| rust_decimal::Decimal::new(8, 0));
            let duration_hours = standard_hours.to_f64().unwrap_or(8.0);
            let step_end_time = step_start_time + Duration::hours(duration_hours as i64);

            // 检查资源冲突
            let conflicting_tasks = sqlx::query_scalar!(
                r#"
                SELECT COUNT(*)
                FROM plan_tasks pt
                WHERE pt.skill_group_id = $1
                AND pt.status NOT IN ('COMPLETED', 'CANCELLED')
                AND (
                    (pt.planned_start <= $2 AND pt.planned_end > $2) OR
                    (pt.planned_start < $3 AND pt.planned_end >= $3) OR
                    (pt.planned_start >= $2 AND pt.planned_end <= $3)
                )
                "#,
                step.skill_group_id,
                step_start_time,
                step_end_time
            )
            .fetch_one(&mut *tx)
            .await?;

            if conflicting_tasks.unwrap_or(0) > 0 {
                // 有冲突，尝试找到下一个可用时间
                let next_available = sqlx::query_scalar!(
                    r#"
                    SELECT COALESCE(MAX(planned_end), $1)
                    FROM plan_tasks pt
                    WHERE pt.skill_group_id = $2
                    AND pt.status NOT IN ('COMPLETED', 'CANCELLED')
                    AND pt.planned_end > $1
                    "#,
                    step_start_time,
                    step.skill_group_id
                )
                .fetch_one(&mut *tx)
                .await?;

                if let Some(next_time) = next_available {
                    step_start_time = next_time;
                    conflicts.push(ScheduleConflict {
                        task_id: 0, // 临时ID
                        conflict_type: "RESOURCE_CONFLICT".to_string(),
                        description: format!("Resource {} is busy, rescheduled to {}", step.skill_group_name, next_time),
                        suggested_start: Some(next_time),
                    });
                }
            }

            // 创建计划任务
            let plan_task_id = sqlx::query_scalar!(
                r#"
                INSERT INTO plan_tasks (work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status)
                VALUES ($1, $2, $3, $4, $5, $6)
                RETURNING id
                "#,
                work_order_id,
                step.routing_id,
                step.skill_group_id,
                step_start_time,
                step_end_time,
                PlanTask::STATUS_PLANNED
            )
            .execute(&mut *tx)
            .await?;

            // 准备下一个步骤的开始时间
            step_start_time = step_end_time;
        }

        // 更新工单状态为已计划
        sqlx::query!(
            "UPDATE work_orders SET status = $1 WHERE id = $2",
            "PLANNED",
            work_order_id
        )
        .execute(&mut *tx)
        .await?;
    }

    // 提交事务
    tx.commit().await?;

    // 获取调度后的任务详情
    let scheduled_task_details = sqlx::query_as!(
        PlanTaskWithDetails,
        r#"
        SELECT
            pt.id,
            pt.work_order_id,
            pt.routing_step_id,
            r.step_number,
            r.process_name,
            pt.skill_group_id,
            sg.group_name as skill_group_name,
            pt.planned_start,
            pt.planned_end,
            pt.status,
            p.project_name,
            parts.part_number,
            parts.part_name,
            wo.quantity as work_order_quantity,
            r.standard_hours
        FROM plan_tasks pt
        JOIN routings r ON pt.routing_step_id = r.id
        JOIN skill_groups sg ON pt.skill_group_id = sg.id
        JOIN work_orders wo ON pt.work_order_id = wo.id
        JOIN project_boms pb ON wo.project_bom_id = pb.id
        JOIN projects p ON pb.project_id = p.id
        JOIN parts ON pb.part_id = parts.id
        WHERE pt.work_order_id = ANY($1)
        ORDER BY pt.planned_start
        "#,
        &request.work_order_ids
    )
    .fetch_all(&database.pool)
    .await?;

    Ok(Json(ScheduleResult {
        success: true,
        message: format!("Successfully scheduled {} work orders", request.work_order_ids.len()),
        scheduled_tasks: scheduled_task_details,
        conflicts,
    }))
}

pub async fn update_plan_task(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(task_id): Path<i32>,
    Json(request): Json<UpdatePlanTaskRequest>,
) -> Result<Json<PlanTask>> {
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查任务是否存在
    sqlx::query_scalar!(
        "SELECT id FROM plan_tasks WHERE id = $1",
        task_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Plan task not found".to_string()))?;

    let plan_task = sqlx::query_as!(
        PlanTask,
        r#"
        UPDATE plan_tasks
        SET
            planned_start = COALESCE($2, planned_start),
            planned_end = COALESCE($3, planned_end),
            status = COALESCE($4, status),
            skill_group_id = COALESCE($5, skill_group_id)
        WHERE id = $1
        RETURNING id, work_order_id, routing_step_id, skill_group_id, planned_start, planned_end, status
        "#,
        task_id,
        request.planned_start,
        request.planned_end,
        request.status,
        request.skill_group_id
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(plan_task))
}

pub async fn delete_plan_task(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(task_id): Path<i32>,
) -> Result<StatusCode> {
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    // 检查是否有执行记录
    let execution_count = sqlx::query_scalar!(
        "SELECT COUNT(*) FROM execution_logs WHERE plan_task_id = $1",
        task_id
    )
    .fetch_one(&database.pool)
    .await?;

    if execution_count.unwrap_or(0) > 0 {
        return Err(AppError::Validation("Cannot delete plan task: it has execution records".to_string()));
    }

    let deleted_rows = sqlx::query!(
        "DELETE FROM plan_tasks WHERE id = $1",
        task_id
    )
    .execute(&database.pool)
    .await?
    .rows_affected();

    if deleted_rows == 0 {
        return Err(AppError::NotFound("Plan task not found".to_string()));
    }

    Ok(StatusCode::NO_CONTENT)
}

pub async fn get_resource_utilization(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<PlanTaskQuery>,
) -> Result<Json<serde_json::Value>> {
    require_any_role!(current_user, Role::ADMIN, Role::PLANNER);

    let utilization_data = sqlx::query!(
        r#"
        SELECT
            sg.group_name,
            COUNT(pt.id) as task_count,
            SUM(EXTRACT(EPOCH FROM (pt.planned_end - pt.planned_start)) / 3600) as total_hours,
            AVG(EXTRACT(EPOCH FROM (pt.planned_end - pt.planned_start)) / 3600) as avg_hours_per_task
        FROM skill_groups sg
        LEFT JOIN plan_tasks pt ON sg.id = pt.skill_group_id
            AND pt.status NOT IN ('COMPLETED', 'CANCELLED')
        GROUP BY sg.id, sg.group_name
        ORDER BY sg.group_name
        "#
    )
    .fetch_all(&database.pool)
    .await?;

    let result = serde_json::json!({
        "resource_utilization": utilization_data.into_iter().map(|row| {
            serde_json::json!({
                "skill_group": row.group_name,
                "task_count": row.task_count.unwrap_or(0),
                "total_hours": row.total_hours.unwrap_or(0.0),
                "avg_hours_per_task": row.avg_hours_per_task.unwrap_or(0.0)
            })
        }).collect::<Vec<_>>()
    });

    Ok(Json(result))
}
