use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
    Extension,
};
use serde::Deserialize;
use validator::Validate;

use crate::{
    database::Database,
    error::{AppError, Result},
    middleware::auth::CurrentUser,
    models::{
        role::Role,
        part::{
            Part, PartWithRoutings, RoutingStep,
            CreatePartRequest, UpdatePartRequest, CreateRoutingStepRequest, UpdateRoutingStepRequest
        },
    },
    require_any_role,
};

#[derive(Deserialize)]
pub struct ListPartsQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    pub search: Option<String>,
    pub part_number: Option<String>,
}

pub async fn list_parts(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Query(query): Query<ListPartsQuery>,
) -> Result<Json<Vec<Part>>> {
    // 工艺员、计划员和管理员可以查看零件列表
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    let limit = query.limit.unwrap_or(50).min(100) as i64;
    let offset = (query.page.unwrap_or(1) - 1) * (limit as u32) as i64;

    let parts = if let Some(search) = query.search {
        sqlx::query_as!(
            Part,
            r#"
            SELECT id, part_number, part_name, version, specifications
            FROM parts
            WHERE part_number ILIKE $1 OR part_name ILIKE $1
            ORDER BY part_number, version
            LIMIT $2 OFFSET $3
            "#,
            format!("%{}%", search),
            limit,
            offset
        )
        .fetch_all(&database.pool)
        .await?
    } else if let Some(part_number) = query.part_number {
        sqlx::query_as!(
            Part,
            r#"
            SELECT id, part_number, part_name, version, specifications
            FROM parts
            WHERE part_number = $1
            ORDER BY version
            LIMIT $2 OFFSET $3
            "#,
            part_number,
            limit,
            offset
        )
        .fetch_all(&database.pool)
        .await?
    } else {
        sqlx::query_as!(
            Part,
            r#"
            SELECT id, part_number, part_name, version, specifications
            FROM parts
            ORDER BY part_number, version
            LIMIT $1 OFFSET $2
            "#,
            limit,
            offset
        )
        .fetch_all(&database.pool)
        .await?
    };

    Ok(Json(parts))
}

pub async fn get_part(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(part_id): Path<i32>,
) -> Result<Json<PartWithRoutings>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER, Role::PLANNER);

    // 获取零件基本信息
    let part = sqlx::query_as!(
        Part,
        "SELECT id, part_number, part_name, version, specifications FROM parts WHERE id = $1",
        part_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Part not found".to_string()))?;

    // 获取零件的工艺路线
    let routings = sqlx::query_as!(
        RoutingStep,
        r#"
        SELECT id, step_number, process_name, work_instructions, standard_hours
        FROM routings
        WHERE part_id = $1
        ORDER BY step_number
        "#,
        part_id
    )
    .fetch_all(&database.pool)
    .await?;

    let part_with_routings = PartWithRoutings {
        id: part.id,
        part_number: part.part_number,
        part_name: part.part_name,
        version: part.version,
        specifications: part.specifications,
        routings,
    };

    Ok(Json(part_with_routings))
}

pub async fn create_part(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Json(request): Json<CreatePartRequest>,
) -> Result<Json<Part>> {
    // 只有工艺员和管理员可以创建零件
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查零件号和版本是否已存在
    let existing_part = sqlx::query_scalar!(
        "SELECT id FROM parts WHERE part_number = $1 AND version = $2",
        request.part_number,
        request.version
    )
    .fetch_optional(&database.pool)
    .await?;

    if existing_part.is_some() {
        return Err(AppError::Validation("Part number and version combination already exists".to_string()));
    }

    let part = sqlx::query_as!(
        Part,
        r#"
        INSERT INTO parts (part_number, part_name, version, specifications)
        VALUES ($1, $2, $3, $4)
        RETURNING id, part_number, part_name, version, specifications
        "#,
        request.part_number,
        request.part_name,
        request.version,
        request.specifications
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(part))
}

pub async fn update_part(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(part_id): Path<i32>,
    Json(request): Json<UpdatePartRequest>,
) -> Result<Json<Part>> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    request.validate()
        .map_err(|e| AppError::Validation(format!("Validation error: {}", e)))?;

    // 检查零件是否存在
    sqlx::query_scalar!(
        "SELECT id FROM parts WHERE id = $1",
        part_id
    )
    .fetch_optional(&database.pool)
    .await?
    .ok_or_else(|| AppError::NotFound("Part not found".to_string()))?;

    // 如果要更新零件号和版本，检查是否会产生冲突
    if let (Some(part_number), Some(version)) = (&request.part_number, &request.version) {
        let existing_part = sqlx::query_scalar!(
            "SELECT id FROM parts WHERE part_number = $1 AND version = $2 AND id != $3",
            part_number,
            version,
            part_id
        )
        .fetch_optional(&database.pool)
        .await?;

        if existing_part.is_some() {
            return Err(AppError::Validation("Part number and version combination already exists".to_string()));
        }
    }

    let part = sqlx::query_as!(
        Part,
        r#"
        UPDATE parts
        SET
            part_number = COALESCE($2, part_number),
            part_name = COALESCE($3, part_name),
            version = COALESCE($4, version),
            specifications = COALESCE($5, specifications)
        WHERE id = $1
        RETURNING id, part_number, part_name, version, specifications
        "#,
        part_id,
        request.part_number,
        request.part_name,
        request.version,
        request.specifications
    )
    .fetch_one(&database.pool)
    .await?;

    Ok(Json(part))
}

pub async fn delete_part(
    Extension(current_user): Extension<CurrentUser>,
    State(database): State<Database>,
    Path(part_id): Path<i32>,
) -> Result<StatusCode> {
    require_any_role!(current_user, Role::ADMIN, Role::PROCESS_ENGINEER);

    // 检查零件是否被项目BOM引用
    let bom_references = sqlx::query_scalar!(
        "SELECT COUNT(*) FROM project_boms WHERE part_id = $1",
        part_id
    )
    .fetch_one(&database.pool)
    .await?;

    if bom_references.unwrap_or(0) > 0 {
        return Err(AppError::Validation("Cannot delete part: it is referenced by project BOMs".to_string()));
    }

    let deleted_rows = sqlx::query!(
        "DELETE FROM parts WHERE id = $1",
        part_id
    )
    .execute(&database.pool)
    .await?
    .rows_affected();

    if deleted_rows == 0 {
        return Err(AppError::NotFound("Part not found".to_string()));
    }

    Ok(StatusCode::NO_CONTENT)
}
