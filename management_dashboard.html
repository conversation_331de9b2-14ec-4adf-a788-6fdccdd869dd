<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理看板 - MES系统</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
        }
        
        .dashboard {
            padding: 30px;
            max-width: 1600px;
            margin: 0 auto;
        }
        
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .kpi-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .kpi-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
        }
        
        .kpi-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }
        
        .kpi-number {
            font-size: 3.5em;
            font-weight: bold;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #3498db, #2ecc71);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .kpi-label {
            font-size: 1.3em;
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        
        .kpi-trend {
            font-size: 1em;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
        }
        
        .trend-up {
            background: #d5f4e6;
            color: #27ae60;
        }
        
        .trend-down {
            background: #fadbd8;
            color: #e74c3c;
        }
        
        .trend-stable {
            background: #fef9e7;
            color: #f39c12;
        }
        
        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .chart-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
        }
        
        .alerts-section {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .alerts-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .alert-item {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .alert-high {
            background: #fadbd8;
            border-color: #e74c3c;
        }
        
        .alert-medium {
            background: #fef9e7;
            border-color: #f39c12;
        }
        
        .alert-low {
            background: #d5f4e6;
            border-color: #27ae60;
        }
        
        .alert-message {
            flex: 1;
            color: #2c3e50;
        }
        
        .alert-time {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        .refresh-controls {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
        }
        
        .control-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
            transition: all 0.3s ease;
        }
        
        .control-btn:hover {
            background: #2980b9;
            transform: scale(1.05);
        }
        
        .control-btn.auto-refresh {
            background: #27ae60;
        }
        
        .control-btn.auto-refresh:hover {
            background: #229954;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: white;
            font-size: 1.2em;
        }
        
        .error {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 20px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .kpi-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 管理看板</h1>
        <div class="subtitle">数据驱动决策 · 智能制造管理</div>
    </div>
    
    <div class="dashboard">
        <div id="loading" class="loading">
            正在加载管理数据...
        </div>
        
        <div id="error" class="error" style="display: none;">
            数据加载失败，请检查网络连接或稍后重试
        </div>
        
        <div id="content" style="display: none;">
            <!-- KPI指标卡片 -->
            <div class="kpi-grid" id="kpiGrid">
                <!-- KPI卡片将通过JavaScript动态生成 -->
            </div>
            
            <!-- 图表区域 -->
            <div class="charts-grid">
                <div class="chart-card">
                    <div class="chart-title">生产效率趋势</div>
                    <div class="chart-container">
                        <canvas id="efficiencyChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="chart-title">设备利用率</div>
                    <div class="chart-container">
                        <canvas id="utilizationChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="chart-title">质量指标</div>
                    <div class="chart-container">
                        <canvas id="qualityChart"></canvas>
                    </div>
                </div>
                
                <div class="chart-card">
                    <div class="chart-title">项目进度</div>
                    <div class="chart-container">
                        <canvas id="projectChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 告警区域 -->
            <div class="alerts-section">
                <div class="alerts-title">
                    🚨 系统告警
                </div>
                <div id="alertsList">
                    <!-- 告警项目将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>
    
    <div class="refresh-controls">
        <button class="control-btn" onclick="loadDashboardData()">
            🔄 手动刷新
        </button>
        <button class="control-btn auto-refresh" id="autoRefreshBtn" onclick="toggleAutoRefresh()">
            ⏰ 自动刷新: 开
        </button>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:3000/api';
        let authToken = '';
        let autoRefreshInterval = null;
        let charts = {};
        
        // 登录获取token
        async function login() {
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                if (data.token) {
                    authToken = data.token;
                    return true;
                }
            } catch (error) {
                console.error('登录失败:', error);
            }
            return false;
        }
        
        async function loadDashboardData() {
            const loading = document.getElementById('loading');
            const error = document.getElementById('error');
            const content = document.getElementById('content');
            
            loading.style.display = 'block';
            error.style.display = 'none';
            content.style.display = 'none';
            
            try {
                if (!authToken && !(await login())) {
                    throw new Error('认证失败');
                }
                
                // 设置查询参数（最近30天）
                const endDate = new Date().toISOString().split('T')[0];
                const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
                const queryParams = `start_date=${startDate}&end_date=${endDate}&report_type=PRODUCTION`;
                
                // 并行加载数据
                const [kpiResponse, realTimeResponse] = await Promise.all([
                    fetch(`${API_BASE}/analytics/kpi-dashboard?${queryParams}`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    }),
                    fetch(`${API_BASE}/analytics/real-time`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    })
                ]);
                
                const kpiData = await kpiResponse.json();
                const realTimeData = await realTimeResponse.json();
                
                renderKPIs(kpiData);
                renderCharts(kpiData);
                renderAlerts(realTimeData.alerts);
                
                loading.style.display = 'none';
                content.style.display = 'block';
                
            } catch (err) {
                console.error('加载数据失败:', err);
                loading.style.display = 'none';
                error.style.display = 'block';
            }
        }
        
        function renderKPIs(data) {
            const kpiGrid = document.getElementById('kpiGrid');
            
            const kpis = [
                {
                    label: '生产效率',
                    value: data.production_efficiency.efficiency_rate.toFixed(1) + '%',
                    trend: data.production_efficiency.efficiency_rate > 80 ? 'up' : 'down'
                },
                {
                    label: '任务完成率',
                    value: data.production_efficiency.completion_rate.toFixed(1) + '%',
                    trend: data.production_efficiency.completion_rate > 85 ? 'up' : 'down'
                },
                {
                    label: '质量合格率',
                    value: data.quality_metrics.first_pass_yield.toFixed(1) + '%',
                    trend: data.quality_metrics.first_pass_yield > 95 ? 'up' : 'down'
                },
                {
                    label: '设备利用率',
                    value: data.equipment_utilization.length > 0 ? 
                           (data.equipment_utilization.reduce((sum, eq) => sum + eq.utilization_rate, 0) / data.equipment_utilization.length).toFixed(1) + '%' : '0%',
                    trend: 'stable'
                }
            ];
            
            kpiGrid.innerHTML = kpis.map(kpi => `
                <div class="kpi-card">
                    <div class="kpi-number">${kpi.value}</div>
                    <div class="kpi-label">${kpi.label}</div>
                    <div class="kpi-trend trend-${kpi.trend}">
                        ${kpi.trend === 'up' ? '↗ 良好' : kpi.trend === 'down' ? '↘ 需改进' : '→ 稳定'}
                    </div>
                </div>
            `).join('');
        }
        
        function renderCharts(data) {
            // 销毁现有图表
            Object.values(charts).forEach(chart => chart.destroy());
            charts = {};
            
            // 生产效率图表
            const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');
            charts.efficiency = new Chart(efficiencyCtx, {
                type: 'line',
                data: {
                    labels: ['第1周', '第2周', '第3周', '第4周'],
                    datasets: [{
                        label: '效率率',
                        data: [75, 82, 78, data.production_efficiency.efficiency_rate],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            
            // 设备利用率图表
            const utilizationCtx = document.getElementById('utilizationChart').getContext('2d');
            charts.utilization = new Chart(utilizationCtx, {
                type: 'bar',
                data: {
                    labels: data.equipment_utilization.map(eq => eq.machine_name),
                    datasets: [{
                        label: '利用率 (%)',
                        data: data.equipment_utilization.map(eq => eq.utilization_rate),
                        backgroundColor: ['#3498db', '#2ecc71', '#f39c12', '#e74c3c', '#9b59b6']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            
            // 质量指标图表
            const qualityCtx = document.getElementById('qualityChart').getContext('2d');
            charts.quality = new Chart(qualityCtx, {
                type: 'doughnut',
                data: {
                    labels: ['合格', '返工', '报废'],
                    datasets: [{
                        data: [
                            data.quality_metrics.passed_parts,
                            data.quality_metrics.rework_parts,
                            data.quality_metrics.scrap_parts
                        ],
                        backgroundColor: ['#2ecc71', '#f39c12', '#e74c3c']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // 项目进度图表
            const projectCtx = document.getElementById('projectChart').getContext('2d');
            charts.project = new Chart(projectCtx, {
                type: 'horizontalBar',
                data: {
                    labels: data.project_status.map(p => p.project_name),
                    datasets: [{
                        label: '完成进度 (%)',
                        data: data.project_status.map(p => p.overall_progress),
                        backgroundColor: '#2ecc71'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
        
        function renderAlerts(alerts) {
            const alertsList = document.getElementById('alertsList');
            
            if (alerts.length === 0) {
                alertsList.innerHTML = '<div style="text-align: center; color: #27ae60; padding: 20px;">✅ 暂无告警</div>';
                return;
            }
            
            alertsList.innerHTML = alerts.map(alert => `
                <div class="alert-item alert-${alert.severity.toLowerCase()}">
                    <div class="alert-message">
                        <strong>${alert.alert_type}:</strong> ${alert.message}
                    </div>
                    <div class="alert-time">
                        ${new Date(alert.created_at).toLocaleString()}
                    </div>
                </div>
            `).join('');
        }
        
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                btn.textContent = '⏰ 自动刷新: 关';
                btn.style.background = '#e74c3c';
            } else {
                autoRefreshInterval = setInterval(loadDashboardData, 60000); // 每分钟刷新
                btn.textContent = '⏰ 自动刷新: 开';
                btn.style.background = '#27ae60';
            }
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadDashboardData();
            // 默认开启自动刷新
            toggleAutoRefresh();
        });
    </script>
</body>
</html>
