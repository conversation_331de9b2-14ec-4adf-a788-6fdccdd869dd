# 模具车间制造执行系统 (MES)

一个为机械加工与模具制造车间设计的轻量级、高效率的制造执行系统。

## 技术栈

- **后端**: Rust + Axum + SQLx
- **数据库**: PostgreSQL
- **前端**: Vue.js + Quasar (待开发)
- **容器化**: Docker + Docker Compose

## 功能特性

- 用户权限管理
- 项目与BOM管理
- 零件与工艺管理
- 生产计划与调度
- 车间执行与报工
- 质量追溯
- 数据看板

## 快速开始

### 环境要求

- Rust 1.70+
- Docker & Docker Compose
- Node.js 18+ (前端开发)

### 启动开发环境

1. 克隆项目
```bash
git clone <repository-url>
cd mes-system
```

2. 启动数据库
```bash
docker-compose up -d postgres
```

3. 设置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件中的配置
```

4. 运行数据库迁移
```bash
cargo install sqlx-cli
sqlx migrate run
```

5. 启动后端服务
```bash
cargo run
```

服务将在 http://localhost:3000 启动

### API 文档

- 健康检查: `GET /health`

## 开发计划

- [x] 阶段0: 环境准备
- [ ] 阶段1: 核心骨架与权限管理
- [ ] 阶段2: 工艺与项目定义
- [ ] 阶段3: 计划与调度
- [ ] 阶段4: 车间执行与追溯
- [ ] 阶段5: 看板报表与优化

## 项目结构

```
mes-system/
├── src/
│   ├── main.rs          # 应用入口
│   ├── config.rs        # 配置管理
│   ├── database.rs      # 数据库连接
│   ├── error.rs         # 错误处理
│   ├── models/          # 数据模型
│   ├── handlers/        # API处理器
│   └── middleware/      # 中间件
├── migrations/          # 数据库迁移
├── docker-compose.yml   # Docker配置
└── Cargo.toml          # Rust依赖
```

## 许可证

MIT License
